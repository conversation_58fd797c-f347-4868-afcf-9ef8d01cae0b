{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces\n        const predictions = await model.estimateFaces(tensor, false);\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sophisticated face detection criteria\n          if (analysis.skinRatio > 0.25 && analysis.hasVariation && analysis.brightness > 0.2 && analysis.brightness < 0.8) {\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions`);\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n      console.log(`[EchoCameraWeb] 🎨 Applying strong blur to face region...`);\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      console.log(`[EchoCameraWeb] 🔲 Applying heavy pixelation with size: ${pixelSize}px`);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      console.log(`[EchoCameraWeb] 🌫️ Applying additional blur passes...`);\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n      console.log(`[EchoCameraWeb] ✅ BLUR COMPLETE: Applied to (${clampedX}, ${clampedY}) ${clampedWidth}x${clampedHeight}`);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          await loadTensorFlowFaceDetection();\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 817,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 818,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 816,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 827,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 828,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 837,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 836,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 826,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 825,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 850,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 874,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 871,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 870,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 883,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 902,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 909,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 916,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 926,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 925,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 884,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 939,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 942,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 940,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 946,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 947,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 945,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 938,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 952,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 951,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 937,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 936,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 959,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 957,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 978,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 980,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 983,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 964,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 849,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1000,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1007,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1006,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1014,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1021,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 997,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 996,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 991,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1035,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1041,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1047,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1043,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1033,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1032,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1027,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 847,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1637, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 68, 132, 66], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [179, 43, 188, 12, "analysis"], [179, 51, 188, 20], [179, 52, 188, 21, "hasVariation"], [179, 64, 188, 33], [179, 68, 189, 12, "analysis"], [179, 76, 189, 20], [179, 77, 189, 21, "brightness"], [179, 87, 189, 31], [179, 90, 189, 34], [179, 93, 189, 37], [179, 97, 190, 12, "analysis"], [179, 105, 190, 20], [179, 106, 190, 21, "brightness"], [179, 116, 190, 31], [179, 119, 190, 34], [179, 122, 190, 37], [179, 124, 190, 39], [180, 12, 192, 10, "faces"], [180, 17, 192, 15], [180, 18, 192, 16, "push"], [180, 22, 192, 20], [180, 23, 192, 21], [181, 14, 193, 12, "boundingBox"], [181, 25, 193, 23], [181, 27, 193, 25], [182, 16, 194, 14, "xCenter"], [182, 23, 194, 21], [182, 25, 194, 23], [182, 26, 194, 24, "x"], [182, 27, 194, 25], [182, 30, 194, 28, "blockSize"], [182, 39, 194, 37], [182, 42, 194, 40], [182, 43, 194, 41], [182, 47, 194, 45, "img"], [182, 50, 194, 48], [182, 51, 194, 49, "width"], [182, 56, 194, 54], [183, 16, 195, 14, "yCenter"], [183, 23, 195, 21], [183, 25, 195, 23], [183, 26, 195, 24, "y"], [183, 27, 195, 25], [183, 30, 195, 28, "blockSize"], [183, 39, 195, 37], [183, 42, 195, 40], [183, 43, 195, 41], [183, 47, 195, 45, "img"], [183, 50, 195, 48], [183, 51, 195, 49, "height"], [183, 57, 195, 55], [184, 16, 196, 14, "width"], [184, 21, 196, 19], [184, 23, 196, 22, "blockSize"], [184, 32, 196, 31], [184, 35, 196, 34], [184, 38, 196, 37], [184, 41, 196, 41, "img"], [184, 44, 196, 44], [184, 45, 196, 45, "width"], [184, 50, 196, 50], [185, 16, 197, 14, "height"], [185, 22, 197, 20], [185, 24, 197, 23, "blockSize"], [185, 33, 197, 32], [185, 36, 197, 35], [185, 39, 197, 38], [185, 42, 197, 42, "img"], [185, 45, 197, 45], [185, 46, 197, 46, "height"], [186, 14, 198, 12], [186, 15, 198, 13], [187, 14, 199, 12, "confidence"], [187, 24, 199, 22], [187, 26, 199, 24, "analysis"], [187, 34, 199, 32], [187, 35, 199, 33, "skinRatio"], [187, 44, 199, 42], [187, 47, 199, 45, "analysis"], [187, 55, 199, 53], [187, 56, 199, 54, "variation"], [188, 12, 200, 10], [188, 13, 200, 11], [188, 14, 200, 12], [189, 12, 202, 10, "console"], [189, 19, 202, 17], [189, 20, 202, 18, "log"], [189, 23, 202, 21], [189, 24, 202, 22], [189, 71, 202, 69, "Math"], [189, 75, 202, 73], [189, 76, 202, 74, "round"], [189, 81, 202, 79], [189, 82, 202, 80, "x"], [189, 83, 202, 81], [189, 84, 202, 82], [189, 89, 202, 87, "Math"], [189, 93, 202, 91], [189, 94, 202, 92, "round"], [189, 99, 202, 97], [189, 100, 202, 98, "y"], [189, 101, 202, 99], [189, 102, 202, 100], [189, 115, 202, 113], [189, 116, 202, 114, "analysis"], [189, 124, 202, 122], [189, 125, 202, 123, "skinRatio"], [189, 134, 202, 132], [189, 137, 202, 135], [189, 140, 202, 138], [189, 142, 202, 140, "toFixed"], [189, 149, 202, 147], [189, 150, 202, 148], [189, 151, 202, 149], [189, 152, 202, 150], [189, 169, 202, 167, "analysis"], [189, 177, 202, 175], [189, 178, 202, 176, "variation"], [189, 187, 202, 185], [189, 188, 202, 186, "toFixed"], [189, 195, 202, 193], [189, 196, 202, 194], [189, 197, 202, 195], [189, 198, 202, 196], [189, 215, 202, 213, "analysis"], [189, 223, 202, 221], [189, 224, 202, 222, "brightness"], [189, 234, 202, 232], [189, 235, 202, 233, "toFixed"], [189, 242, 202, 240], [189, 243, 202, 241], [189, 244, 202, 242], [189, 245, 202, 243], [189, 247, 202, 245], [189, 248, 202, 246], [190, 10, 203, 8], [191, 8, 204, 6], [192, 6, 205, 4], [194, 6, 207, 4], [195, 6, 208, 4, "faces"], [195, 11, 208, 9], [195, 12, 208, 10, "sort"], [195, 16, 208, 14], [195, 17, 208, 15], [195, 18, 208, 16, "a"], [195, 19, 208, 17], [195, 21, 208, 19, "b"], [195, 22, 208, 20], [195, 27, 208, 25], [195, 28, 208, 26, "b"], [195, 29, 208, 27], [195, 30, 208, 28, "confidence"], [195, 40, 208, 38], [195, 44, 208, 42], [195, 45, 208, 43], [195, 50, 208, 48, "a"], [195, 51, 208, 49], [195, 52, 208, 50, "confidence"], [195, 62, 208, 60], [195, 66, 208, 64], [195, 67, 208, 65], [195, 68, 208, 66], [195, 69, 208, 67], [196, 6, 209, 4], [196, 12, 209, 10, "mergedFaces"], [196, 23, 209, 21], [196, 26, 209, 24, "mergeFaceDetections"], [196, 45, 209, 43], [196, 46, 209, 44, "faces"], [196, 51, 209, 49], [196, 52, 209, 50], [197, 6, 211, 4, "console"], [197, 13, 211, 11], [197, 14, 211, 12, "log"], [197, 17, 211, 15], [197, 18, 211, 16], [197, 61, 211, 59, "faces"], [197, 66, 211, 64], [197, 67, 211, 65, "length"], [197, 73, 211, 71], [197, 90, 211, 88, "mergedFaces"], [197, 101, 211, 99], [197, 102, 211, 100, "length"], [197, 108, 211, 106], [197, 123, 211, 121], [197, 124, 211, 122], [198, 6, 212, 4], [198, 13, 212, 11, "mergedFaces"], [198, 24, 212, 22], [198, 25, 212, 23, "slice"], [198, 30, 212, 28], [198, 31, 212, 29], [198, 32, 212, 30], [198, 34, 212, 32], [198, 35, 212, 33], [198, 36, 212, 34], [198, 37, 212, 35], [198, 38, 212, 36], [199, 4, 213, 2], [199, 5, 213, 3], [200, 4, 215, 2], [200, 10, 215, 8, "analyzeRegionForFace"], [200, 30, 215, 28], [200, 33, 215, 31, "analyzeRegionForFace"], [200, 34, 215, 32, "data"], [200, 38, 215, 55], [200, 40, 215, 57, "startX"], [200, 46, 215, 71], [200, 48, 215, 73, "startY"], [200, 54, 215, 87], [200, 56, 215, 89, "size"], [200, 60, 215, 101], [200, 62, 215, 103, "imageWidth"], [200, 72, 215, 121], [200, 74, 215, 123, "imageHeight"], [200, 85, 215, 142], [200, 90, 215, 147], [201, 6, 216, 4], [201, 10, 216, 8, "skinPixels"], [201, 20, 216, 18], [201, 23, 216, 21], [201, 24, 216, 22], [202, 6, 217, 4], [202, 10, 217, 8, "totalPixels"], [202, 21, 217, 19], [202, 24, 217, 22], [202, 25, 217, 23], [203, 6, 218, 4], [203, 10, 218, 8, "totalBrightness"], [203, 25, 218, 23], [203, 28, 218, 26], [203, 29, 218, 27], [204, 6, 219, 4], [204, 10, 219, 8, "colorVariations"], [204, 25, 219, 23], [204, 28, 219, 26], [204, 29, 219, 27], [205, 6, 220, 4], [205, 10, 220, 8, "prevR"], [205, 15, 220, 13], [205, 18, 220, 16], [205, 19, 220, 17], [206, 8, 220, 19, "prevG"], [206, 13, 220, 24], [206, 16, 220, 27], [206, 17, 220, 28], [207, 8, 220, 30, "prevB"], [207, 13, 220, 35], [207, 16, 220, 38], [207, 17, 220, 39], [208, 6, 222, 4], [208, 11, 222, 9], [208, 15, 222, 13, "y"], [208, 16, 222, 14], [208, 19, 222, 17, "startY"], [208, 25, 222, 23], [208, 27, 222, 25, "y"], [208, 28, 222, 26], [208, 31, 222, 29, "startY"], [208, 37, 222, 35], [208, 40, 222, 38, "size"], [208, 44, 222, 42], [208, 48, 222, 46, "y"], [208, 49, 222, 47], [208, 52, 222, 50, "imageHeight"], [208, 63, 222, 61], [208, 65, 222, 63, "y"], [208, 66, 222, 64], [208, 68, 222, 66], [208, 70, 222, 68], [209, 8, 223, 6], [209, 13, 223, 11], [209, 17, 223, 15, "x"], [209, 18, 223, 16], [209, 21, 223, 19, "startX"], [209, 27, 223, 25], [209, 29, 223, 27, "x"], [209, 30, 223, 28], [209, 33, 223, 31, "startX"], [209, 39, 223, 37], [209, 42, 223, 40, "size"], [209, 46, 223, 44], [209, 50, 223, 48, "x"], [209, 51, 223, 49], [209, 54, 223, 52, "imageWidth"], [209, 64, 223, 62], [209, 66, 223, 64, "x"], [209, 67, 223, 65], [209, 69, 223, 67], [209, 71, 223, 69], [210, 10, 224, 8], [210, 16, 224, 14, "index"], [210, 21, 224, 19], [210, 24, 224, 22], [210, 25, 224, 23, "y"], [210, 26, 224, 24], [210, 29, 224, 27, "imageWidth"], [210, 39, 224, 37], [210, 42, 224, 40, "x"], [210, 43, 224, 41], [210, 47, 224, 45], [210, 48, 224, 46], [211, 10, 225, 8], [211, 16, 225, 14, "r"], [211, 17, 225, 15], [211, 20, 225, 18, "data"], [211, 24, 225, 22], [211, 25, 225, 23, "index"], [211, 30, 225, 28], [211, 31, 225, 29], [212, 10, 226, 8], [212, 16, 226, 14, "g"], [212, 17, 226, 15], [212, 20, 226, 18, "data"], [212, 24, 226, 22], [212, 25, 226, 23, "index"], [212, 30, 226, 28], [212, 33, 226, 31], [212, 34, 226, 32], [212, 35, 226, 33], [213, 10, 227, 8], [213, 16, 227, 14, "b"], [213, 17, 227, 15], [213, 20, 227, 18, "data"], [213, 24, 227, 22], [213, 25, 227, 23, "index"], [213, 30, 227, 28], [213, 33, 227, 31], [213, 34, 227, 32], [213, 35, 227, 33], [215, 10, 229, 8], [216, 10, 230, 8], [216, 14, 230, 12, "isSkinTone"], [216, 24, 230, 22], [216, 25, 230, 23, "r"], [216, 26, 230, 24], [216, 28, 230, 26, "g"], [216, 29, 230, 27], [216, 31, 230, 29, "b"], [216, 32, 230, 30], [216, 33, 230, 31], [216, 35, 230, 33], [217, 12, 231, 10, "skinPixels"], [217, 22, 231, 20], [217, 24, 231, 22], [218, 10, 232, 8], [220, 10, 234, 8], [221, 10, 235, 8], [221, 16, 235, 14, "brightness"], [221, 26, 235, 24], [221, 29, 235, 27], [221, 30, 235, 28, "r"], [221, 31, 235, 29], [221, 34, 235, 32, "g"], [221, 35, 235, 33], [221, 38, 235, 36, "b"], [221, 39, 235, 37], [221, 44, 235, 42], [221, 45, 235, 43], [221, 48, 235, 46], [221, 51, 235, 49], [221, 52, 235, 50], [222, 10, 236, 8, "totalBrightness"], [222, 25, 236, 23], [222, 29, 236, 27, "brightness"], [222, 39, 236, 37], [224, 10, 238, 8], [225, 10, 239, 8], [225, 14, 239, 12, "totalPixels"], [225, 25, 239, 23], [225, 28, 239, 26], [225, 29, 239, 27], [225, 31, 239, 29], [226, 12, 240, 10], [226, 18, 240, 16, "colorDiff"], [226, 27, 240, 25], [226, 30, 240, 28, "Math"], [226, 34, 240, 32], [226, 35, 240, 33, "abs"], [226, 38, 240, 36], [226, 39, 240, 37, "r"], [226, 40, 240, 38], [226, 43, 240, 41, "prevR"], [226, 48, 240, 46], [226, 49, 240, 47], [226, 52, 240, 50, "Math"], [226, 56, 240, 54], [226, 57, 240, 55, "abs"], [226, 60, 240, 58], [226, 61, 240, 59, "g"], [226, 62, 240, 60], [226, 65, 240, 63, "prevG"], [226, 70, 240, 68], [226, 71, 240, 69], [226, 74, 240, 72, "Math"], [226, 78, 240, 76], [226, 79, 240, 77, "abs"], [226, 82, 240, 80], [226, 83, 240, 81, "b"], [226, 84, 240, 82], [226, 87, 240, 85, "prevB"], [226, 92, 240, 90], [226, 93, 240, 91], [227, 12, 241, 10], [227, 16, 241, 14, "colorDiff"], [227, 25, 241, 23], [227, 28, 241, 26], [227, 30, 241, 28], [227, 32, 241, 30], [228, 14, 241, 32], [229, 14, 242, 12, "colorVariations"], [229, 29, 242, 27], [229, 31, 242, 29], [230, 12, 243, 10], [231, 10, 244, 8], [232, 10, 246, 8, "prevR"], [232, 15, 246, 13], [232, 18, 246, 16, "r"], [232, 19, 246, 17], [233, 10, 246, 19, "prevG"], [233, 15, 246, 24], [233, 18, 246, 27, "g"], [233, 19, 246, 28], [234, 10, 246, 30, "prevB"], [234, 15, 246, 35], [234, 18, 246, 38, "b"], [234, 19, 246, 39], [235, 10, 247, 8, "totalPixels"], [235, 21, 247, 19], [235, 23, 247, 21], [236, 8, 248, 6], [237, 6, 249, 4], [238, 6, 251, 4], [238, 13, 251, 11], [239, 8, 252, 6, "skinRatio"], [239, 17, 252, 15], [239, 19, 252, 17, "skinPixels"], [239, 29, 252, 27], [239, 32, 252, 30, "totalPixels"], [239, 43, 252, 41], [240, 8, 253, 6, "brightness"], [240, 18, 253, 16], [240, 20, 253, 18, "totalBrightness"], [240, 35, 253, 33], [240, 38, 253, 36, "totalPixels"], [240, 49, 253, 47], [241, 8, 254, 6, "variation"], [241, 17, 254, 15], [241, 19, 254, 17, "colorVariations"], [241, 34, 254, 32], [241, 37, 254, 35, "totalPixels"], [241, 48, 254, 46], [242, 8, 255, 6, "hasVariation"], [242, 20, 255, 18], [242, 22, 255, 20, "colorVariations"], [242, 37, 255, 35], [242, 40, 255, 38, "totalPixels"], [242, 51, 255, 49], [242, 54, 255, 52], [242, 57, 255, 55], [242, 58, 255, 56], [243, 6, 256, 4], [243, 7, 256, 5], [244, 4, 257, 2], [244, 5, 257, 3], [245, 4, 259, 2], [245, 10, 259, 8, "isSkinTone"], [245, 20, 259, 18], [245, 23, 259, 21, "isSkinTone"], [245, 24, 259, 22, "r"], [245, 25, 259, 31], [245, 27, 259, 33, "g"], [245, 28, 259, 42], [245, 30, 259, 44, "b"], [245, 31, 259, 53], [245, 36, 259, 58], [246, 6, 260, 4], [247, 6, 261, 4], [247, 13, 262, 6, "r"], [247, 14, 262, 7], [247, 17, 262, 10], [247, 19, 262, 12], [247, 23, 262, 16, "g"], [247, 24, 262, 17], [247, 27, 262, 20], [247, 29, 262, 22], [247, 33, 262, 26, "b"], [247, 34, 262, 27], [247, 37, 262, 30], [247, 39, 262, 32], [247, 43, 263, 6, "r"], [247, 44, 263, 7], [247, 47, 263, 10, "g"], [247, 48, 263, 11], [247, 52, 263, 15, "r"], [247, 53, 263, 16], [247, 56, 263, 19, "b"], [247, 57, 263, 20], [247, 61, 264, 6, "Math"], [247, 65, 264, 10], [247, 66, 264, 11, "abs"], [247, 69, 264, 14], [247, 70, 264, 15, "r"], [247, 71, 264, 16], [247, 74, 264, 19, "g"], [247, 75, 264, 20], [247, 76, 264, 21], [247, 79, 264, 24], [247, 81, 264, 26], [247, 85, 265, 6, "Math"], [247, 89, 265, 10], [247, 90, 265, 11, "max"], [247, 93, 265, 14], [247, 94, 265, 15, "r"], [247, 95, 265, 16], [247, 97, 265, 18, "g"], [247, 98, 265, 19], [247, 100, 265, 21, "b"], [247, 101, 265, 22], [247, 102, 265, 23], [247, 105, 265, 26, "Math"], [247, 109, 265, 30], [247, 110, 265, 31, "min"], [247, 113, 265, 34], [247, 114, 265, 35, "r"], [247, 115, 265, 36], [247, 117, 265, 38, "g"], [247, 118, 265, 39], [247, 120, 265, 41, "b"], [247, 121, 265, 42], [247, 122, 265, 43], [247, 125, 265, 46], [247, 127, 265, 48], [248, 4, 267, 2], [248, 5, 267, 3], [249, 4, 269, 2], [249, 10, 269, 8, "mergeFaceDetections"], [249, 29, 269, 27], [249, 32, 269, 31, "faces"], [249, 37, 269, 43], [249, 41, 269, 48], [250, 6, 270, 4], [250, 10, 270, 8, "faces"], [250, 15, 270, 13], [250, 16, 270, 14, "length"], [250, 22, 270, 20], [250, 26, 270, 24], [250, 27, 270, 25], [250, 29, 270, 27], [250, 36, 270, 34, "faces"], [250, 41, 270, 39], [251, 6, 272, 4], [251, 12, 272, 10, "merged"], [251, 18, 272, 16], [251, 21, 272, 19], [251, 23, 272, 21], [252, 6, 273, 4], [252, 12, 273, 10, "used"], [252, 16, 273, 14], [252, 19, 273, 17], [252, 23, 273, 21, "Set"], [252, 26, 273, 24], [252, 27, 273, 25], [252, 28, 273, 26], [253, 6, 275, 4], [253, 11, 275, 9], [253, 15, 275, 13, "i"], [253, 16, 275, 14], [253, 19, 275, 17], [253, 20, 275, 18], [253, 22, 275, 20, "i"], [253, 23, 275, 21], [253, 26, 275, 24, "faces"], [253, 31, 275, 29], [253, 32, 275, 30, "length"], [253, 38, 275, 36], [253, 40, 275, 38, "i"], [253, 41, 275, 39], [253, 43, 275, 41], [253, 45, 275, 43], [254, 8, 276, 6], [254, 12, 276, 10, "used"], [254, 16, 276, 14], [254, 17, 276, 15, "has"], [254, 20, 276, 18], [254, 21, 276, 19, "i"], [254, 22, 276, 20], [254, 23, 276, 21], [254, 25, 276, 23], [255, 8, 278, 6], [255, 12, 278, 10, "currentFace"], [255, 23, 278, 21], [255, 26, 278, 24, "faces"], [255, 31, 278, 29], [255, 32, 278, 30, "i"], [255, 33, 278, 31], [255, 34, 278, 32], [256, 8, 279, 6, "used"], [256, 12, 279, 10], [256, 13, 279, 11, "add"], [256, 16, 279, 14], [256, 17, 279, 15, "i"], [256, 18, 279, 16], [256, 19, 279, 17], [258, 8, 281, 6], [259, 8, 282, 6], [259, 13, 282, 11], [259, 17, 282, 15, "j"], [259, 18, 282, 16], [259, 21, 282, 19, "i"], [259, 22, 282, 20], [259, 25, 282, 23], [259, 26, 282, 24], [259, 28, 282, 26, "j"], [259, 29, 282, 27], [259, 32, 282, 30, "faces"], [259, 37, 282, 35], [259, 38, 282, 36, "length"], [259, 44, 282, 42], [259, 46, 282, 44, "j"], [259, 47, 282, 45], [259, 49, 282, 47], [259, 51, 282, 49], [260, 10, 283, 8], [260, 14, 283, 12, "used"], [260, 18, 283, 16], [260, 19, 283, 17, "has"], [260, 22, 283, 20], [260, 23, 283, 21, "j"], [260, 24, 283, 22], [260, 25, 283, 23], [260, 27, 283, 25], [261, 10, 285, 8], [261, 16, 285, 14, "overlap"], [261, 23, 285, 21], [261, 26, 285, 24, "calculateOverlap"], [261, 42, 285, 40], [261, 43, 285, 41, "currentFace"], [261, 54, 285, 52], [261, 55, 285, 53, "boundingBox"], [261, 66, 285, 64], [261, 68, 285, 66, "faces"], [261, 73, 285, 71], [261, 74, 285, 72, "j"], [261, 75, 285, 73], [261, 76, 285, 74], [261, 77, 285, 75, "boundingBox"], [261, 88, 285, 86], [261, 89, 285, 87], [262, 10, 286, 8], [262, 14, 286, 12, "overlap"], [262, 21, 286, 19], [262, 24, 286, 22], [262, 27, 286, 25], [262, 29, 286, 27], [263, 12, 286, 29], [264, 12, 287, 10], [265, 12, 288, 10, "currentFace"], [265, 23, 288, 21], [265, 26, 288, 24, "mergeTwoFaces"], [265, 39, 288, 37], [265, 40, 288, 38, "currentFace"], [265, 51, 288, 49], [265, 53, 288, 51, "faces"], [265, 58, 288, 56], [265, 59, 288, 57, "j"], [265, 60, 288, 58], [265, 61, 288, 59], [265, 62, 288, 60], [266, 12, 289, 10, "used"], [266, 16, 289, 14], [266, 17, 289, 15, "add"], [266, 20, 289, 18], [266, 21, 289, 19, "j"], [266, 22, 289, 20], [266, 23, 289, 21], [267, 10, 290, 8], [268, 8, 291, 6], [269, 8, 293, 6, "merged"], [269, 14, 293, 12], [269, 15, 293, 13, "push"], [269, 19, 293, 17], [269, 20, 293, 18, "currentFace"], [269, 31, 293, 29], [269, 32, 293, 30], [270, 6, 294, 4], [271, 6, 296, 4], [271, 13, 296, 11, "merged"], [271, 19, 296, 17], [272, 4, 297, 2], [272, 5, 297, 3], [273, 4, 299, 2], [273, 10, 299, 8, "calculateOverlap"], [273, 26, 299, 24], [273, 29, 299, 27, "calculateOverlap"], [273, 30, 299, 28, "box1"], [273, 34, 299, 37], [273, 36, 299, 39, "box2"], [273, 40, 299, 48], [273, 45, 299, 53], [274, 6, 300, 4], [274, 12, 300, 10, "x1"], [274, 14, 300, 12], [274, 17, 300, 15, "Math"], [274, 21, 300, 19], [274, 22, 300, 20, "max"], [274, 25, 300, 23], [274, 26, 300, 24, "box1"], [274, 30, 300, 28], [274, 31, 300, 29, "xCenter"], [274, 38, 300, 36], [274, 41, 300, 39, "box1"], [274, 45, 300, 43], [274, 46, 300, 44, "width"], [274, 51, 300, 49], [274, 54, 300, 50], [274, 55, 300, 51], [274, 57, 300, 53, "box2"], [274, 61, 300, 57], [274, 62, 300, 58, "xCenter"], [274, 69, 300, 65], [274, 72, 300, 68, "box2"], [274, 76, 300, 72], [274, 77, 300, 73, "width"], [274, 82, 300, 78], [274, 85, 300, 79], [274, 86, 300, 80], [274, 87, 300, 81], [275, 6, 301, 4], [275, 12, 301, 10, "y1"], [275, 14, 301, 12], [275, 17, 301, 15, "Math"], [275, 21, 301, 19], [275, 22, 301, 20, "max"], [275, 25, 301, 23], [275, 26, 301, 24, "box1"], [275, 30, 301, 28], [275, 31, 301, 29, "yCenter"], [275, 38, 301, 36], [275, 41, 301, 39, "box1"], [275, 45, 301, 43], [275, 46, 301, 44, "height"], [275, 52, 301, 50], [275, 55, 301, 51], [275, 56, 301, 52], [275, 58, 301, 54, "box2"], [275, 62, 301, 58], [275, 63, 301, 59, "yCenter"], [275, 70, 301, 66], [275, 73, 301, 69, "box2"], [275, 77, 301, 73], [275, 78, 301, 74, "height"], [275, 84, 301, 80], [275, 87, 301, 81], [275, 88, 301, 82], [275, 89, 301, 83], [276, 6, 302, 4], [276, 12, 302, 10, "x2"], [276, 14, 302, 12], [276, 17, 302, 15, "Math"], [276, 21, 302, 19], [276, 22, 302, 20, "min"], [276, 25, 302, 23], [276, 26, 302, 24, "box1"], [276, 30, 302, 28], [276, 31, 302, 29, "xCenter"], [276, 38, 302, 36], [276, 41, 302, 39, "box1"], [276, 45, 302, 43], [276, 46, 302, 44, "width"], [276, 51, 302, 49], [276, 54, 302, 50], [276, 55, 302, 51], [276, 57, 302, 53, "box2"], [276, 61, 302, 57], [276, 62, 302, 58, "xCenter"], [276, 69, 302, 65], [276, 72, 302, 68, "box2"], [276, 76, 302, 72], [276, 77, 302, 73, "width"], [276, 82, 302, 78], [276, 85, 302, 79], [276, 86, 302, 80], [276, 87, 302, 81], [277, 6, 303, 4], [277, 12, 303, 10, "y2"], [277, 14, 303, 12], [277, 17, 303, 15, "Math"], [277, 21, 303, 19], [277, 22, 303, 20, "min"], [277, 25, 303, 23], [277, 26, 303, 24, "box1"], [277, 30, 303, 28], [277, 31, 303, 29, "yCenter"], [277, 38, 303, 36], [277, 41, 303, 39, "box1"], [277, 45, 303, 43], [277, 46, 303, 44, "height"], [277, 52, 303, 50], [277, 55, 303, 51], [277, 56, 303, 52], [277, 58, 303, 54, "box2"], [277, 62, 303, 58], [277, 63, 303, 59, "yCenter"], [277, 70, 303, 66], [277, 73, 303, 69, "box2"], [277, 77, 303, 73], [277, 78, 303, 74, "height"], [277, 84, 303, 80], [277, 87, 303, 81], [277, 88, 303, 82], [277, 89, 303, 83], [278, 6, 305, 4], [278, 10, 305, 8, "x2"], [278, 12, 305, 10], [278, 16, 305, 14, "x1"], [278, 18, 305, 16], [278, 22, 305, 20, "y2"], [278, 24, 305, 22], [278, 28, 305, 26, "y1"], [278, 30, 305, 28], [278, 32, 305, 30], [278, 39, 305, 37], [278, 40, 305, 38], [279, 6, 307, 4], [279, 12, 307, 10, "overlapArea"], [279, 23, 307, 21], [279, 26, 307, 24], [279, 27, 307, 25, "x2"], [279, 29, 307, 27], [279, 32, 307, 30, "x1"], [279, 34, 307, 32], [279, 39, 307, 37, "y2"], [279, 41, 307, 39], [279, 44, 307, 42, "y1"], [279, 46, 307, 44], [279, 47, 307, 45], [280, 6, 308, 4], [280, 12, 308, 10, "box1Area"], [280, 20, 308, 18], [280, 23, 308, 21, "box1"], [280, 27, 308, 25], [280, 28, 308, 26, "width"], [280, 33, 308, 31], [280, 36, 308, 34, "box1"], [280, 40, 308, 38], [280, 41, 308, 39, "height"], [280, 47, 308, 45], [281, 6, 309, 4], [281, 12, 309, 10, "box2Area"], [281, 20, 309, 18], [281, 23, 309, 21, "box2"], [281, 27, 309, 25], [281, 28, 309, 26, "width"], [281, 33, 309, 31], [281, 36, 309, 34, "box2"], [281, 40, 309, 38], [281, 41, 309, 39, "height"], [281, 47, 309, 45], [282, 6, 311, 4], [282, 13, 311, 11, "overlapArea"], [282, 24, 311, 22], [282, 27, 311, 25, "Math"], [282, 31, 311, 29], [282, 32, 311, 30, "min"], [282, 35, 311, 33], [282, 36, 311, 34, "box1Area"], [282, 44, 311, 42], [282, 46, 311, 44, "box2Area"], [282, 54, 311, 52], [282, 55, 311, 53], [283, 4, 312, 2], [283, 5, 312, 3], [284, 4, 314, 2], [284, 10, 314, 8, "mergeTwoFaces"], [284, 23, 314, 21], [284, 26, 314, 24, "mergeTwoFaces"], [284, 27, 314, 25, "face1"], [284, 32, 314, 35], [284, 34, 314, 37, "face2"], [284, 39, 314, 47], [284, 44, 314, 52], [285, 6, 315, 4], [285, 12, 315, 10, "box1"], [285, 16, 315, 14], [285, 19, 315, 17, "face1"], [285, 24, 315, 22], [285, 25, 315, 23, "boundingBox"], [285, 36, 315, 34], [286, 6, 316, 4], [286, 12, 316, 10, "box2"], [286, 16, 316, 14], [286, 19, 316, 17, "face2"], [286, 24, 316, 22], [286, 25, 316, 23, "boundingBox"], [286, 36, 316, 34], [287, 6, 318, 4], [287, 12, 318, 10, "left"], [287, 16, 318, 14], [287, 19, 318, 17, "Math"], [287, 23, 318, 21], [287, 24, 318, 22, "min"], [287, 27, 318, 25], [287, 28, 318, 26, "box1"], [287, 32, 318, 30], [287, 33, 318, 31, "xCenter"], [287, 40, 318, 38], [287, 43, 318, 41, "box1"], [287, 47, 318, 45], [287, 48, 318, 46, "width"], [287, 53, 318, 51], [287, 56, 318, 52], [287, 57, 318, 53], [287, 59, 318, 55, "box2"], [287, 63, 318, 59], [287, 64, 318, 60, "xCenter"], [287, 71, 318, 67], [287, 74, 318, 70, "box2"], [287, 78, 318, 74], [287, 79, 318, 75, "width"], [287, 84, 318, 80], [287, 87, 318, 81], [287, 88, 318, 82], [287, 89, 318, 83], [288, 6, 319, 4], [288, 12, 319, 10, "right"], [288, 17, 319, 15], [288, 20, 319, 18, "Math"], [288, 24, 319, 22], [288, 25, 319, 23, "max"], [288, 28, 319, 26], [288, 29, 319, 27, "box1"], [288, 33, 319, 31], [288, 34, 319, 32, "xCenter"], [288, 41, 319, 39], [288, 44, 319, 42, "box1"], [288, 48, 319, 46], [288, 49, 319, 47, "width"], [288, 54, 319, 52], [288, 57, 319, 53], [288, 58, 319, 54], [288, 60, 319, 56, "box2"], [288, 64, 319, 60], [288, 65, 319, 61, "xCenter"], [288, 72, 319, 68], [288, 75, 319, 71, "box2"], [288, 79, 319, 75], [288, 80, 319, 76, "width"], [288, 85, 319, 81], [288, 88, 319, 82], [288, 89, 319, 83], [288, 90, 319, 84], [289, 6, 320, 4], [289, 12, 320, 10, "top"], [289, 15, 320, 13], [289, 18, 320, 16, "Math"], [289, 22, 320, 20], [289, 23, 320, 21, "min"], [289, 26, 320, 24], [289, 27, 320, 25, "box1"], [289, 31, 320, 29], [289, 32, 320, 30, "yCenter"], [289, 39, 320, 37], [289, 42, 320, 40, "box1"], [289, 46, 320, 44], [289, 47, 320, 45, "height"], [289, 53, 320, 51], [289, 56, 320, 52], [289, 57, 320, 53], [289, 59, 320, 55, "box2"], [289, 63, 320, 59], [289, 64, 320, 60, "yCenter"], [289, 71, 320, 67], [289, 74, 320, 70, "box2"], [289, 78, 320, 74], [289, 79, 320, 75, "height"], [289, 85, 320, 81], [289, 88, 320, 82], [289, 89, 320, 83], [289, 90, 320, 84], [290, 6, 321, 4], [290, 12, 321, 10, "bottom"], [290, 18, 321, 16], [290, 21, 321, 19, "Math"], [290, 25, 321, 23], [290, 26, 321, 24, "max"], [290, 29, 321, 27], [290, 30, 321, 28, "box1"], [290, 34, 321, 32], [290, 35, 321, 33, "yCenter"], [290, 42, 321, 40], [290, 45, 321, 43, "box1"], [290, 49, 321, 47], [290, 50, 321, 48, "height"], [290, 56, 321, 54], [290, 59, 321, 55], [290, 60, 321, 56], [290, 62, 321, 58, "box2"], [290, 66, 321, 62], [290, 67, 321, 63, "yCenter"], [290, 74, 321, 70], [290, 77, 321, 73, "box2"], [290, 81, 321, 77], [290, 82, 321, 78, "height"], [290, 88, 321, 84], [290, 91, 321, 85], [290, 92, 321, 86], [290, 93, 321, 87], [291, 6, 323, 4], [291, 13, 323, 11], [292, 8, 324, 6, "boundingBox"], [292, 19, 324, 17], [292, 21, 324, 19], [293, 10, 325, 8, "xCenter"], [293, 17, 325, 15], [293, 19, 325, 17], [293, 20, 325, 18, "left"], [293, 24, 325, 22], [293, 27, 325, 25, "right"], [293, 32, 325, 30], [293, 36, 325, 34], [293, 37, 325, 35], [294, 10, 326, 8, "yCenter"], [294, 17, 326, 15], [294, 19, 326, 17], [294, 20, 326, 18, "top"], [294, 23, 326, 21], [294, 26, 326, 24, "bottom"], [294, 32, 326, 30], [294, 36, 326, 34], [294, 37, 326, 35], [295, 10, 327, 8, "width"], [295, 15, 327, 13], [295, 17, 327, 15, "right"], [295, 22, 327, 20], [295, 25, 327, 23, "left"], [295, 29, 327, 27], [296, 10, 328, 8, "height"], [296, 16, 328, 14], [296, 18, 328, 16, "bottom"], [296, 24, 328, 22], [296, 27, 328, 25, "top"], [297, 8, 329, 6], [298, 6, 330, 4], [298, 7, 330, 5], [299, 4, 331, 2], [299, 5, 331, 3], [301, 4, 333, 2], [302, 4, 334, 2], [302, 10, 334, 8, "applyStrongBlur"], [302, 25, 334, 23], [302, 28, 334, 26, "applyStrongBlur"], [302, 29, 334, 27, "ctx"], [302, 32, 334, 56], [302, 34, 334, 58, "x"], [302, 35, 334, 67], [302, 37, 334, 69, "y"], [302, 38, 334, 78], [302, 40, 334, 80, "width"], [302, 45, 334, 93], [302, 47, 334, 95, "height"], [302, 53, 334, 109], [302, 58, 334, 114], [303, 6, 335, 4], [304, 6, 336, 4], [304, 12, 336, 10, "canvasWidth"], [304, 23, 336, 21], [304, 26, 336, 24, "ctx"], [304, 29, 336, 27], [304, 30, 336, 28, "canvas"], [304, 36, 336, 34], [304, 37, 336, 35, "width"], [304, 42, 336, 40], [305, 6, 337, 4], [305, 12, 337, 10, "canvasHeight"], [305, 24, 337, 22], [305, 27, 337, 25, "ctx"], [305, 30, 337, 28], [305, 31, 337, 29, "canvas"], [305, 37, 337, 35], [305, 38, 337, 36, "height"], [305, 44, 337, 42], [306, 6, 339, 4], [306, 12, 339, 10, "clampedX"], [306, 20, 339, 18], [306, 23, 339, 21, "Math"], [306, 27, 339, 25], [306, 28, 339, 26, "max"], [306, 31, 339, 29], [306, 32, 339, 30], [306, 33, 339, 31], [306, 35, 339, 33, "Math"], [306, 39, 339, 37], [306, 40, 339, 38, "min"], [306, 43, 339, 41], [306, 44, 339, 42, "Math"], [306, 48, 339, 46], [306, 49, 339, 47, "floor"], [306, 54, 339, 52], [306, 55, 339, 53, "x"], [306, 56, 339, 54], [306, 57, 339, 55], [306, 59, 339, 57, "canvasWidth"], [306, 70, 339, 68], [306, 73, 339, 71], [306, 74, 339, 72], [306, 75, 339, 73], [306, 76, 339, 74], [307, 6, 340, 4], [307, 12, 340, 10, "clampedY"], [307, 20, 340, 18], [307, 23, 340, 21, "Math"], [307, 27, 340, 25], [307, 28, 340, 26, "max"], [307, 31, 340, 29], [307, 32, 340, 30], [307, 33, 340, 31], [307, 35, 340, 33, "Math"], [307, 39, 340, 37], [307, 40, 340, 38, "min"], [307, 43, 340, 41], [307, 44, 340, 42, "Math"], [307, 48, 340, 46], [307, 49, 340, 47, "floor"], [307, 54, 340, 52], [307, 55, 340, 53, "y"], [307, 56, 340, 54], [307, 57, 340, 55], [307, 59, 340, 57, "canvasHeight"], [307, 71, 340, 69], [307, 74, 340, 72], [307, 75, 340, 73], [307, 76, 340, 74], [307, 77, 340, 75], [308, 6, 341, 4], [308, 12, 341, 10, "<PERSON><PERSON><PERSON><PERSON>"], [308, 24, 341, 22], [308, 27, 341, 25, "Math"], [308, 31, 341, 29], [308, 32, 341, 30, "min"], [308, 35, 341, 33], [308, 36, 341, 34, "Math"], [308, 40, 341, 38], [308, 41, 341, 39, "floor"], [308, 46, 341, 44], [308, 47, 341, 45, "width"], [308, 52, 341, 50], [308, 53, 341, 51], [308, 55, 341, 53, "canvasWidth"], [308, 66, 341, 64], [308, 69, 341, 67, "clampedX"], [308, 77, 341, 75], [308, 78, 341, 76], [309, 6, 342, 4], [309, 12, 342, 10, "clampedHeight"], [309, 25, 342, 23], [309, 28, 342, 26, "Math"], [309, 32, 342, 30], [309, 33, 342, 31, "min"], [309, 36, 342, 34], [309, 37, 342, 35, "Math"], [309, 41, 342, 39], [309, 42, 342, 40, "floor"], [309, 47, 342, 45], [309, 48, 342, 46, "height"], [309, 54, 342, 52], [309, 55, 342, 53], [309, 57, 342, 55, "canvasHeight"], [309, 69, 342, 67], [309, 72, 342, 70, "clampedY"], [309, 80, 342, 78], [309, 81, 342, 79], [310, 6, 344, 4], [310, 10, 344, 8, "<PERSON><PERSON><PERSON><PERSON>"], [310, 22, 344, 20], [310, 26, 344, 24], [310, 27, 344, 25], [310, 31, 344, 29, "clampedHeight"], [310, 44, 344, 42], [310, 48, 344, 46], [310, 49, 344, 47], [310, 51, 344, 49], [311, 8, 345, 6, "console"], [311, 15, 345, 13], [311, 16, 345, 14, "warn"], [311, 20, 345, 18], [311, 21, 345, 19], [311, 72, 345, 70], [311, 73, 345, 71], [312, 8, 346, 6], [313, 6, 347, 4], [315, 6, 349, 4], [316, 6, 350, 4], [316, 12, 350, 10, "imageData"], [316, 21, 350, 19], [316, 24, 350, 22, "ctx"], [316, 27, 350, 25], [316, 28, 350, 26, "getImageData"], [316, 40, 350, 38], [316, 41, 350, 39, "clampedX"], [316, 49, 350, 47], [316, 51, 350, 49, "clampedY"], [316, 59, 350, 57], [316, 61, 350, 59, "<PERSON><PERSON><PERSON><PERSON>"], [316, 73, 350, 71], [316, 75, 350, 73, "clampedHeight"], [316, 88, 350, 86], [316, 89, 350, 87], [317, 6, 351, 4], [317, 12, 351, 10, "data"], [317, 16, 351, 14], [317, 19, 351, 17, "imageData"], [317, 28, 351, 26], [317, 29, 351, 27, "data"], [317, 33, 351, 31], [318, 6, 353, 4, "console"], [318, 13, 353, 11], [318, 14, 353, 12, "log"], [318, 17, 353, 15], [318, 18, 353, 16], [318, 77, 353, 75], [318, 78, 353, 76], [320, 6, 355, 4], [321, 6, 356, 4], [321, 12, 356, 10, "pixelSize"], [321, 21, 356, 19], [321, 24, 356, 22, "Math"], [321, 28, 356, 26], [321, 29, 356, 27, "max"], [321, 32, 356, 30], [321, 33, 356, 31], [321, 35, 356, 33], [321, 37, 356, 35, "Math"], [321, 41, 356, 39], [321, 42, 356, 40, "min"], [321, 45, 356, 43], [321, 46, 356, 44, "<PERSON><PERSON><PERSON><PERSON>"], [321, 58, 356, 56], [321, 60, 356, 58, "clampedHeight"], [321, 73, 356, 71], [321, 74, 356, 72], [321, 77, 356, 75], [321, 78, 356, 76], [321, 79, 356, 77], [322, 6, 357, 4, "console"], [322, 13, 357, 11], [322, 14, 357, 12, "log"], [322, 17, 357, 15], [322, 18, 357, 16], [322, 77, 357, 75, "pixelSize"], [322, 86, 357, 84], [322, 90, 357, 88], [322, 91, 357, 89], [323, 6, 359, 4], [323, 11, 359, 9], [323, 15, 359, 13, "py"], [323, 17, 359, 15], [323, 20, 359, 18], [323, 21, 359, 19], [323, 23, 359, 21, "py"], [323, 25, 359, 23], [323, 28, 359, 26, "clampedHeight"], [323, 41, 359, 39], [323, 43, 359, 41, "py"], [323, 45, 359, 43], [323, 49, 359, 47, "pixelSize"], [323, 58, 359, 56], [323, 60, 359, 58], [324, 8, 360, 6], [324, 13, 360, 11], [324, 17, 360, 15, "px"], [324, 19, 360, 17], [324, 22, 360, 20], [324, 23, 360, 21], [324, 25, 360, 23, "px"], [324, 27, 360, 25], [324, 30, 360, 28, "<PERSON><PERSON><PERSON><PERSON>"], [324, 42, 360, 40], [324, 44, 360, 42, "px"], [324, 46, 360, 44], [324, 50, 360, 48, "pixelSize"], [324, 59, 360, 57], [324, 61, 360, 59], [325, 10, 361, 8], [326, 10, 362, 8], [326, 14, 362, 12, "r"], [326, 15, 362, 13], [326, 18, 362, 16], [326, 19, 362, 17], [327, 12, 362, 19, "g"], [327, 13, 362, 20], [327, 16, 362, 23], [327, 17, 362, 24], [328, 12, 362, 26, "b"], [328, 13, 362, 27], [328, 16, 362, 30], [328, 17, 362, 31], [329, 12, 362, 33, "count"], [329, 17, 362, 38], [329, 20, 362, 41], [329, 21, 362, 42], [330, 10, 364, 8], [330, 15, 364, 13], [330, 19, 364, 17, "dy"], [330, 21, 364, 19], [330, 24, 364, 22], [330, 25, 364, 23], [330, 27, 364, 25, "dy"], [330, 29, 364, 27], [330, 32, 364, 30, "pixelSize"], [330, 41, 364, 39], [330, 45, 364, 43, "py"], [330, 47, 364, 45], [330, 50, 364, 48, "dy"], [330, 52, 364, 50], [330, 55, 364, 53, "clampedHeight"], [330, 68, 364, 66], [330, 70, 364, 68, "dy"], [330, 72, 364, 70], [330, 74, 364, 72], [330, 76, 364, 74], [331, 12, 365, 10], [331, 17, 365, 15], [331, 21, 365, 19, "dx"], [331, 23, 365, 21], [331, 26, 365, 24], [331, 27, 365, 25], [331, 29, 365, 27, "dx"], [331, 31, 365, 29], [331, 34, 365, 32, "pixelSize"], [331, 43, 365, 41], [331, 47, 365, 45, "px"], [331, 49, 365, 47], [331, 52, 365, 50, "dx"], [331, 54, 365, 52], [331, 57, 365, 55, "<PERSON><PERSON><PERSON><PERSON>"], [331, 69, 365, 67], [331, 71, 365, 69, "dx"], [331, 73, 365, 71], [331, 75, 365, 73], [331, 77, 365, 75], [332, 14, 366, 12], [332, 20, 366, 18, "index"], [332, 25, 366, 23], [332, 28, 366, 26], [332, 29, 366, 27], [332, 30, 366, 28, "py"], [332, 32, 366, 30], [332, 35, 366, 33, "dy"], [332, 37, 366, 35], [332, 41, 366, 39, "<PERSON><PERSON><PERSON><PERSON>"], [332, 53, 366, 51], [332, 57, 366, 55, "px"], [332, 59, 366, 57], [332, 62, 366, 60, "dx"], [332, 64, 366, 62], [332, 65, 366, 63], [332, 69, 366, 67], [332, 70, 366, 68], [333, 14, 367, 12, "r"], [333, 15, 367, 13], [333, 19, 367, 17, "data"], [333, 23, 367, 21], [333, 24, 367, 22, "index"], [333, 29, 367, 27], [333, 30, 367, 28], [334, 14, 368, 12, "g"], [334, 15, 368, 13], [334, 19, 368, 17, "data"], [334, 23, 368, 21], [334, 24, 368, 22, "index"], [334, 29, 368, 27], [334, 32, 368, 30], [334, 33, 368, 31], [334, 34, 368, 32], [335, 14, 369, 12, "b"], [335, 15, 369, 13], [335, 19, 369, 17, "data"], [335, 23, 369, 21], [335, 24, 369, 22, "index"], [335, 29, 369, 27], [335, 32, 369, 30], [335, 33, 369, 31], [335, 34, 369, 32], [336, 14, 370, 12, "count"], [336, 19, 370, 17], [336, 21, 370, 19], [337, 12, 371, 10], [338, 10, 372, 8], [339, 10, 374, 8], [339, 14, 374, 12, "count"], [339, 19, 374, 17], [339, 22, 374, 20], [339, 23, 374, 21], [339, 25, 374, 23], [340, 12, 375, 10, "r"], [340, 13, 375, 11], [340, 16, 375, 14, "Math"], [340, 20, 375, 18], [340, 21, 375, 19, "floor"], [340, 26, 375, 24], [340, 27, 375, 25, "r"], [340, 28, 375, 26], [340, 31, 375, 29, "count"], [340, 36, 375, 34], [340, 37, 375, 35], [341, 12, 376, 10, "g"], [341, 13, 376, 11], [341, 16, 376, 14, "Math"], [341, 20, 376, 18], [341, 21, 376, 19, "floor"], [341, 26, 376, 24], [341, 27, 376, 25, "g"], [341, 28, 376, 26], [341, 31, 376, 29, "count"], [341, 36, 376, 34], [341, 37, 376, 35], [342, 12, 377, 10, "b"], [342, 13, 377, 11], [342, 16, 377, 14, "Math"], [342, 20, 377, 18], [342, 21, 377, 19, "floor"], [342, 26, 377, 24], [342, 27, 377, 25, "b"], [342, 28, 377, 26], [342, 31, 377, 29, "count"], [342, 36, 377, 34], [342, 37, 377, 35], [344, 12, 379, 10], [345, 12, 380, 10], [345, 17, 380, 15], [345, 21, 380, 19, "dy"], [345, 23, 380, 21], [345, 26, 380, 24], [345, 27, 380, 25], [345, 29, 380, 27, "dy"], [345, 31, 380, 29], [345, 34, 380, 32, "pixelSize"], [345, 43, 380, 41], [345, 47, 380, 45, "py"], [345, 49, 380, 47], [345, 52, 380, 50, "dy"], [345, 54, 380, 52], [345, 57, 380, 55, "clampedHeight"], [345, 70, 380, 68], [345, 72, 380, 70, "dy"], [345, 74, 380, 72], [345, 76, 380, 74], [345, 78, 380, 76], [346, 14, 381, 12], [346, 19, 381, 17], [346, 23, 381, 21, "dx"], [346, 25, 381, 23], [346, 28, 381, 26], [346, 29, 381, 27], [346, 31, 381, 29, "dx"], [346, 33, 381, 31], [346, 36, 381, 34, "pixelSize"], [346, 45, 381, 43], [346, 49, 381, 47, "px"], [346, 51, 381, 49], [346, 54, 381, 52, "dx"], [346, 56, 381, 54], [346, 59, 381, 57, "<PERSON><PERSON><PERSON><PERSON>"], [346, 71, 381, 69], [346, 73, 381, 71, "dx"], [346, 75, 381, 73], [346, 77, 381, 75], [346, 79, 381, 77], [347, 16, 382, 14], [347, 22, 382, 20, "index"], [347, 27, 382, 25], [347, 30, 382, 28], [347, 31, 382, 29], [347, 32, 382, 30, "py"], [347, 34, 382, 32], [347, 37, 382, 35, "dy"], [347, 39, 382, 37], [347, 43, 382, 41, "<PERSON><PERSON><PERSON><PERSON>"], [347, 55, 382, 53], [347, 59, 382, 57, "px"], [347, 61, 382, 59], [347, 64, 382, 62, "dx"], [347, 66, 382, 64], [347, 67, 382, 65], [347, 71, 382, 69], [347, 72, 382, 70], [348, 16, 383, 14, "data"], [348, 20, 383, 18], [348, 21, 383, 19, "index"], [348, 26, 383, 24], [348, 27, 383, 25], [348, 30, 383, 28, "r"], [348, 31, 383, 29], [349, 16, 384, 14, "data"], [349, 20, 384, 18], [349, 21, 384, 19, "index"], [349, 26, 384, 24], [349, 29, 384, 27], [349, 30, 384, 28], [349, 31, 384, 29], [349, 34, 384, 32, "g"], [349, 35, 384, 33], [350, 16, 385, 14, "data"], [350, 20, 385, 18], [350, 21, 385, 19, "index"], [350, 26, 385, 24], [350, 29, 385, 27], [350, 30, 385, 28], [350, 31, 385, 29], [350, 34, 385, 32, "b"], [350, 35, 385, 33], [351, 16, 386, 14], [352, 14, 387, 12], [353, 12, 388, 10], [354, 10, 389, 8], [355, 8, 390, 6], [356, 6, 391, 4], [358, 6, 393, 4], [359, 6, 394, 4, "console"], [359, 13, 394, 11], [359, 14, 394, 12, "log"], [359, 17, 394, 15], [359, 18, 394, 16], [359, 74, 394, 72], [359, 75, 394, 73], [360, 6, 395, 4], [360, 11, 395, 9], [360, 15, 395, 13, "i"], [360, 16, 395, 14], [360, 19, 395, 17], [360, 20, 395, 18], [360, 22, 395, 20, "i"], [360, 23, 395, 21], [360, 26, 395, 24], [360, 27, 395, 25], [360, 29, 395, 27, "i"], [360, 30, 395, 28], [360, 32, 395, 30], [360, 34, 395, 32], [361, 8, 396, 6, "applySimpleBlur"], [361, 23, 396, 21], [361, 24, 396, 22, "data"], [361, 28, 396, 26], [361, 30, 396, 28, "<PERSON><PERSON><PERSON><PERSON>"], [361, 42, 396, 40], [361, 44, 396, 42, "clampedHeight"], [361, 57, 396, 55], [361, 58, 396, 56], [362, 6, 397, 4], [364, 6, 399, 4], [365, 6, 400, 4, "ctx"], [365, 9, 400, 7], [365, 10, 400, 8, "putImageData"], [365, 22, 400, 20], [365, 23, 400, 21, "imageData"], [365, 32, 400, 30], [365, 34, 400, 32, "clampedX"], [365, 42, 400, 40], [365, 44, 400, 42, "clampedY"], [365, 52, 400, 50], [365, 53, 400, 51], [367, 6, 402, 4], [368, 6, 403, 4, "ctx"], [368, 9, 403, 7], [368, 10, 403, 8, "fillStyle"], [368, 19, 403, 17], [368, 22, 403, 20], [368, 48, 403, 46], [369, 6, 404, 4, "ctx"], [369, 9, 404, 7], [369, 10, 404, 8, "fillRect"], [369, 18, 404, 16], [369, 19, 404, 17, "clampedX"], [369, 27, 404, 25], [369, 29, 404, 27, "clampedY"], [369, 37, 404, 35], [369, 39, 404, 37, "<PERSON><PERSON><PERSON><PERSON>"], [369, 51, 404, 49], [369, 53, 404, 51, "clampedHeight"], [369, 66, 404, 64], [369, 67, 404, 65], [370, 6, 406, 4, "console"], [370, 13, 406, 11], [370, 14, 406, 12, "log"], [370, 17, 406, 15], [370, 18, 406, 16], [370, 66, 406, 64, "clampedX"], [370, 74, 406, 72], [370, 79, 406, 77, "clampedY"], [370, 87, 406, 85], [370, 92, 406, 90, "<PERSON><PERSON><PERSON><PERSON>"], [370, 104, 406, 102], [370, 108, 406, 106, "clampedHeight"], [370, 121, 406, 119], [370, 123, 406, 121], [370, 124, 406, 122], [371, 4, 407, 2], [371, 5, 407, 3], [372, 4, 409, 2], [372, 10, 409, 8, "applySimpleBlur"], [372, 25, 409, 23], [372, 28, 409, 26, "applySimpleBlur"], [372, 29, 409, 27, "data"], [372, 33, 409, 50], [372, 35, 409, 52, "width"], [372, 40, 409, 65], [372, 42, 409, 67, "height"], [372, 48, 409, 81], [372, 53, 409, 86], [373, 6, 410, 4], [373, 12, 410, 10, "original"], [373, 20, 410, 18], [373, 23, 410, 21], [373, 27, 410, 25, "Uint8ClampedArray"], [373, 44, 410, 42], [373, 45, 410, 43, "data"], [373, 49, 410, 47], [373, 50, 410, 48], [374, 6, 412, 4], [374, 11, 412, 9], [374, 15, 412, 13, "y"], [374, 16, 412, 14], [374, 19, 412, 17], [374, 20, 412, 18], [374, 22, 412, 20, "y"], [374, 23, 412, 21], [374, 26, 412, 24, "height"], [374, 32, 412, 30], [374, 35, 412, 33], [374, 36, 412, 34], [374, 38, 412, 36, "y"], [374, 39, 412, 37], [374, 41, 412, 39], [374, 43, 412, 41], [375, 8, 413, 6], [375, 13, 413, 11], [375, 17, 413, 15, "x"], [375, 18, 413, 16], [375, 21, 413, 19], [375, 22, 413, 20], [375, 24, 413, 22, "x"], [375, 25, 413, 23], [375, 28, 413, 26, "width"], [375, 33, 413, 31], [375, 36, 413, 34], [375, 37, 413, 35], [375, 39, 413, 37, "x"], [375, 40, 413, 38], [375, 42, 413, 40], [375, 44, 413, 42], [376, 10, 414, 8], [376, 16, 414, 14, "index"], [376, 21, 414, 19], [376, 24, 414, 22], [376, 25, 414, 23, "y"], [376, 26, 414, 24], [376, 29, 414, 27, "width"], [376, 34, 414, 32], [376, 37, 414, 35, "x"], [376, 38, 414, 36], [376, 42, 414, 40], [376, 43, 414, 41], [378, 10, 416, 8], [379, 10, 417, 8], [379, 14, 417, 12, "r"], [379, 15, 417, 13], [379, 18, 417, 16], [379, 19, 417, 17], [380, 12, 417, 19, "g"], [380, 13, 417, 20], [380, 16, 417, 23], [380, 17, 417, 24], [381, 12, 417, 26, "b"], [381, 13, 417, 27], [381, 16, 417, 30], [381, 17, 417, 31], [382, 10, 418, 8], [382, 15, 418, 13], [382, 19, 418, 17, "dy"], [382, 21, 418, 19], [382, 24, 418, 22], [382, 25, 418, 23], [382, 26, 418, 24], [382, 28, 418, 26, "dy"], [382, 30, 418, 28], [382, 34, 418, 32], [382, 35, 418, 33], [382, 37, 418, 35, "dy"], [382, 39, 418, 37], [382, 41, 418, 39], [382, 43, 418, 41], [383, 12, 419, 10], [383, 17, 419, 15], [383, 21, 419, 19, "dx"], [383, 23, 419, 21], [383, 26, 419, 24], [383, 27, 419, 25], [383, 28, 419, 26], [383, 30, 419, 28, "dx"], [383, 32, 419, 30], [383, 36, 419, 34], [383, 37, 419, 35], [383, 39, 419, 37, "dx"], [383, 41, 419, 39], [383, 43, 419, 41], [383, 45, 419, 43], [384, 14, 420, 12], [384, 20, 420, 18, "neighborIndex"], [384, 33, 420, 31], [384, 36, 420, 34], [384, 37, 420, 35], [384, 38, 420, 36, "y"], [384, 39, 420, 37], [384, 42, 420, 40, "dy"], [384, 44, 420, 42], [384, 48, 420, 46, "width"], [384, 53, 420, 51], [384, 57, 420, 55, "x"], [384, 58, 420, 56], [384, 61, 420, 59, "dx"], [384, 63, 420, 61], [384, 64, 420, 62], [384, 68, 420, 66], [384, 69, 420, 67], [385, 14, 421, 12, "r"], [385, 15, 421, 13], [385, 19, 421, 17, "original"], [385, 27, 421, 25], [385, 28, 421, 26, "neighborIndex"], [385, 41, 421, 39], [385, 42, 421, 40], [386, 14, 422, 12, "g"], [386, 15, 422, 13], [386, 19, 422, 17, "original"], [386, 27, 422, 25], [386, 28, 422, 26, "neighborIndex"], [386, 41, 422, 39], [386, 44, 422, 42], [386, 45, 422, 43], [386, 46, 422, 44], [387, 14, 423, 12, "b"], [387, 15, 423, 13], [387, 19, 423, 17, "original"], [387, 27, 423, 25], [387, 28, 423, 26, "neighborIndex"], [387, 41, 423, 39], [387, 44, 423, 42], [387, 45, 423, 43], [387, 46, 423, 44], [388, 12, 424, 10], [389, 10, 425, 8], [390, 10, 427, 8, "data"], [390, 14, 427, 12], [390, 15, 427, 13, "index"], [390, 20, 427, 18], [390, 21, 427, 19], [390, 24, 427, 22, "r"], [390, 25, 427, 23], [390, 28, 427, 26], [390, 29, 427, 27], [391, 10, 428, 8, "data"], [391, 14, 428, 12], [391, 15, 428, 13, "index"], [391, 20, 428, 18], [391, 23, 428, 21], [391, 24, 428, 22], [391, 25, 428, 23], [391, 28, 428, 26, "g"], [391, 29, 428, 27], [391, 32, 428, 30], [391, 33, 428, 31], [392, 10, 429, 8, "data"], [392, 14, 429, 12], [392, 15, 429, 13, "index"], [392, 20, 429, 18], [392, 23, 429, 21], [392, 24, 429, 22], [392, 25, 429, 23], [392, 28, 429, 26, "b"], [392, 29, 429, 27], [392, 32, 429, 30], [392, 33, 429, 31], [393, 8, 430, 6], [394, 6, 431, 4], [395, 4, 432, 2], [395, 5, 432, 3], [396, 4, 434, 2], [396, 10, 434, 8, "applyFallbackFaceBlur"], [396, 31, 434, 29], [396, 34, 434, 32, "applyFallbackFaceBlur"], [396, 35, 434, 33, "ctx"], [396, 38, 434, 62], [396, 40, 434, 64, "imgWidth"], [396, 48, 434, 80], [396, 50, 434, 82, "imgHeight"], [396, 59, 434, 99], [396, 64, 434, 104], [397, 6, 435, 4, "console"], [397, 13, 435, 11], [397, 14, 435, 12, "log"], [397, 17, 435, 15], [397, 18, 435, 16], [397, 90, 435, 88], [397, 91, 435, 89], [399, 6, 437, 4], [400, 6, 438, 4], [400, 12, 438, 10, "areas"], [400, 17, 438, 15], [400, 20, 438, 18], [401, 6, 439, 6], [402, 6, 440, 6], [403, 8, 440, 8, "x"], [403, 9, 440, 9], [403, 11, 440, 11, "imgWidth"], [403, 19, 440, 19], [403, 22, 440, 22], [403, 26, 440, 26], [404, 8, 440, 28, "y"], [404, 9, 440, 29], [404, 11, 440, 31, "imgHeight"], [404, 20, 440, 40], [404, 23, 440, 43], [404, 27, 440, 47], [405, 8, 440, 49, "w"], [405, 9, 440, 50], [405, 11, 440, 52, "imgWidth"], [405, 19, 440, 60], [405, 22, 440, 63], [405, 25, 440, 66], [406, 8, 440, 68, "h"], [406, 9, 440, 69], [406, 11, 440, 71, "imgHeight"], [406, 20, 440, 80], [406, 23, 440, 83], [407, 6, 440, 87], [407, 7, 440, 88], [408, 6, 441, 6], [409, 6, 442, 6], [410, 8, 442, 8, "x"], [410, 9, 442, 9], [410, 11, 442, 11, "imgWidth"], [410, 19, 442, 19], [410, 22, 442, 22], [410, 25, 442, 25], [411, 8, 442, 27, "y"], [411, 9, 442, 28], [411, 11, 442, 30, "imgHeight"], [411, 20, 442, 39], [411, 23, 442, 42], [411, 26, 442, 45], [412, 8, 442, 47, "w"], [412, 9, 442, 48], [412, 11, 442, 50, "imgWidth"], [412, 19, 442, 58], [412, 22, 442, 61], [412, 26, 442, 65], [413, 8, 442, 67, "h"], [413, 9, 442, 68], [413, 11, 442, 70, "imgHeight"], [413, 20, 442, 79], [413, 23, 442, 82], [414, 6, 442, 86], [414, 7, 442, 87], [415, 6, 443, 6], [416, 6, 444, 6], [417, 8, 444, 8, "x"], [417, 9, 444, 9], [417, 11, 444, 11, "imgWidth"], [417, 19, 444, 19], [417, 22, 444, 22], [417, 26, 444, 26], [418, 8, 444, 28, "y"], [418, 9, 444, 29], [418, 11, 444, 31, "imgHeight"], [418, 20, 444, 40], [418, 23, 444, 43], [418, 26, 444, 46], [419, 8, 444, 48, "w"], [419, 9, 444, 49], [419, 11, 444, 51, "imgWidth"], [419, 19, 444, 59], [419, 22, 444, 62], [419, 26, 444, 66], [420, 8, 444, 68, "h"], [420, 9, 444, 69], [420, 11, 444, 71, "imgHeight"], [420, 20, 444, 80], [420, 23, 444, 83], [421, 6, 444, 87], [421, 7, 444, 88], [421, 8, 445, 5], [422, 6, 447, 4, "areas"], [422, 11, 447, 9], [422, 12, 447, 10, "for<PERSON>ach"], [422, 19, 447, 17], [422, 20, 447, 18], [422, 21, 447, 19, "area"], [422, 25, 447, 23], [422, 27, 447, 25, "index"], [422, 32, 447, 30], [422, 37, 447, 35], [423, 8, 448, 6, "console"], [423, 15, 448, 13], [423, 16, 448, 14, "log"], [423, 19, 448, 17], [423, 20, 448, 18], [423, 65, 448, 63, "index"], [423, 70, 448, 68], [423, 73, 448, 71], [423, 74, 448, 72], [423, 77, 448, 75], [423, 79, 448, 77, "area"], [423, 83, 448, 81], [423, 84, 448, 82], [424, 8, 449, 6, "applyStrongBlur"], [424, 23, 449, 21], [424, 24, 449, 22, "ctx"], [424, 27, 449, 25], [424, 29, 449, 27, "area"], [424, 33, 449, 31], [424, 34, 449, 32, "x"], [424, 35, 449, 33], [424, 37, 449, 35, "area"], [424, 41, 449, 39], [424, 42, 449, 40, "y"], [424, 43, 449, 41], [424, 45, 449, 43, "area"], [424, 49, 449, 47], [424, 50, 449, 48, "w"], [424, 51, 449, 49], [424, 53, 449, 51, "area"], [424, 57, 449, 55], [424, 58, 449, 56, "h"], [424, 59, 449, 57], [424, 60, 449, 58], [425, 6, 450, 4], [425, 7, 450, 5], [425, 8, 450, 6], [426, 4, 451, 2], [426, 5, 451, 3], [428, 4, 453, 2], [429, 4, 454, 2], [429, 10, 454, 8, "capturePhoto"], [429, 22, 454, 20], [429, 25, 454, 23], [429, 29, 454, 23, "useCallback"], [429, 47, 454, 34], [429, 49, 454, 35], [429, 61, 454, 47], [430, 6, 455, 4], [431, 6, 456, 4], [431, 12, 456, 10, "isDev"], [431, 17, 456, 15], [431, 20, 456, 18, "process"], [431, 27, 456, 25], [431, 28, 456, 26, "env"], [431, 31, 456, 29], [431, 32, 456, 30, "NODE_ENV"], [431, 40, 456, 38], [431, 45, 456, 43], [431, 58, 456, 56], [431, 62, 456, 60, "__DEV__"], [431, 69, 456, 67], [432, 6, 458, 4], [432, 10, 458, 8], [432, 11, 458, 9, "cameraRef"], [432, 20, 458, 18], [432, 21, 458, 19, "current"], [432, 28, 458, 26], [432, 32, 458, 30], [432, 33, 458, 31, "isDev"], [432, 38, 458, 36], [432, 40, 458, 38], [433, 8, 459, 6, "<PERSON><PERSON>"], [433, 22, 459, 11], [433, 23, 459, 12, "alert"], [433, 28, 459, 17], [433, 29, 459, 18], [433, 36, 459, 25], [433, 38, 459, 27], [433, 56, 459, 45], [433, 57, 459, 46], [434, 8, 460, 6], [435, 6, 461, 4], [436, 6, 462, 4], [436, 10, 462, 8], [437, 8, 463, 6, "setProcessingState"], [437, 26, 463, 24], [437, 27, 463, 25], [437, 38, 463, 36], [437, 39, 463, 37], [438, 8, 464, 6, "setProcessingProgress"], [438, 29, 464, 27], [438, 30, 464, 28], [438, 32, 464, 30], [438, 33, 464, 31], [439, 8, 465, 6], [440, 8, 466, 6], [441, 8, 467, 6], [442, 8, 468, 6], [442, 14, 468, 12], [442, 18, 468, 16, "Promise"], [442, 25, 468, 23], [442, 26, 468, 24, "resolve"], [442, 33, 468, 31], [442, 37, 468, 35, "setTimeout"], [442, 47, 468, 45], [442, 48, 468, 46, "resolve"], [442, 55, 468, 53], [442, 57, 468, 55], [442, 59, 468, 57], [442, 60, 468, 58], [442, 61, 468, 59], [443, 8, 469, 6], [444, 8, 470, 6], [444, 12, 470, 10, "photo"], [444, 17, 470, 15], [445, 8, 472, 6], [445, 12, 472, 10], [446, 10, 473, 8, "photo"], [446, 15, 473, 13], [446, 18, 473, 16], [446, 24, 473, 22, "cameraRef"], [446, 33, 473, 31], [446, 34, 473, 32, "current"], [446, 41, 473, 39], [446, 42, 473, 40, "takePictureAsync"], [446, 58, 473, 56], [446, 59, 473, 57], [447, 12, 474, 10, "quality"], [447, 19, 474, 17], [447, 21, 474, 19], [447, 24, 474, 22], [448, 12, 475, 10, "base64"], [448, 18, 475, 16], [448, 20, 475, 18], [448, 25, 475, 23], [449, 12, 476, 10, "skipProcessing"], [449, 26, 476, 24], [449, 28, 476, 26], [449, 32, 476, 30], [449, 33, 476, 32], [450, 10, 477, 8], [450, 11, 477, 9], [450, 12, 477, 10], [451, 8, 478, 6], [451, 9, 478, 7], [451, 10, 478, 8], [451, 17, 478, 15, "cameraError"], [451, 28, 478, 26], [451, 30, 478, 28], [452, 10, 479, 8, "console"], [452, 17, 479, 15], [452, 18, 479, 16, "log"], [452, 21, 479, 19], [452, 22, 479, 20], [452, 82, 479, 80], [452, 84, 479, 82, "cameraError"], [452, 95, 479, 93], [452, 96, 479, 94], [453, 10, 480, 8], [454, 10, 481, 8], [454, 14, 481, 12, "isDev"], [454, 19, 481, 17], [454, 21, 481, 19], [455, 12, 482, 10, "photo"], [455, 17, 482, 15], [455, 20, 482, 18], [456, 14, 483, 12, "uri"], [456, 17, 483, 15], [456, 19, 483, 17], [457, 12, 484, 10], [457, 13, 484, 11], [458, 10, 485, 8], [458, 11, 485, 9], [458, 17, 485, 15], [459, 12, 486, 10], [459, 18, 486, 16, "cameraError"], [459, 29, 486, 27], [460, 10, 487, 8], [461, 8, 488, 6], [462, 8, 489, 6], [462, 12, 489, 10], [462, 13, 489, 11, "photo"], [462, 18, 489, 16], [462, 20, 489, 18], [463, 10, 490, 8], [463, 16, 490, 14], [463, 20, 490, 18, "Error"], [463, 25, 490, 23], [463, 26, 490, 24], [463, 51, 490, 49], [463, 52, 490, 50], [464, 8, 491, 6], [465, 8, 492, 6, "console"], [465, 15, 492, 13], [465, 16, 492, 14, "log"], [465, 19, 492, 17], [465, 20, 492, 18], [465, 56, 492, 54], [465, 58, 492, 56, "photo"], [465, 63, 492, 61], [465, 64, 492, 62, "uri"], [465, 67, 492, 65], [465, 68, 492, 66], [466, 8, 493, 6, "setCapturedPhoto"], [466, 24, 493, 22], [466, 25, 493, 23, "photo"], [466, 30, 493, 28], [466, 31, 493, 29, "uri"], [466, 34, 493, 32], [466, 35, 493, 33], [467, 8, 494, 6, "setProcessingProgress"], [467, 29, 494, 27], [467, 30, 494, 28], [467, 32, 494, 30], [467, 33, 494, 31], [468, 8, 495, 6], [469, 8, 496, 6, "console"], [469, 15, 496, 13], [469, 16, 496, 14, "log"], [469, 19, 496, 17], [469, 20, 496, 18], [469, 73, 496, 71], [469, 74, 496, 72], [470, 8, 497, 6], [470, 14, 497, 12, "processImageWithFaceBlur"], [470, 38, 497, 36], [470, 39, 497, 37, "photo"], [470, 44, 497, 42], [470, 45, 497, 43, "uri"], [470, 48, 497, 46], [470, 49, 497, 47], [471, 8, 498, 6, "console"], [471, 15, 498, 13], [471, 16, 498, 14, "log"], [471, 19, 498, 17], [471, 20, 498, 18], [471, 71, 498, 69], [471, 72, 498, 70], [472, 6, 499, 4], [472, 7, 499, 5], [472, 8, 499, 6], [472, 15, 499, 13, "error"], [472, 20, 499, 18], [472, 22, 499, 20], [473, 8, 500, 6, "console"], [473, 15, 500, 13], [473, 16, 500, 14, "error"], [473, 21, 500, 19], [473, 22, 500, 20], [473, 54, 500, 52], [473, 56, 500, 54, "error"], [473, 61, 500, 59], [473, 62, 500, 60], [474, 8, 501, 6, "setErrorMessage"], [474, 23, 501, 21], [474, 24, 501, 22], [474, 68, 501, 66], [474, 69, 501, 67], [475, 8, 502, 6, "setProcessingState"], [475, 26, 502, 24], [475, 27, 502, 25], [475, 34, 502, 32], [475, 35, 502, 33], [476, 6, 503, 4], [477, 4, 504, 2], [477, 5, 504, 3], [477, 7, 504, 5], [477, 9, 504, 7], [477, 10, 504, 8], [478, 4, 505, 2], [479, 4, 506, 2], [479, 10, 506, 8, "processImageWithFaceBlur"], [479, 34, 506, 32], [479, 37, 506, 35], [479, 43, 506, 42, "photoUri"], [479, 51, 506, 58], [479, 55, 506, 63], [480, 6, 507, 4], [480, 10, 507, 8], [481, 8, 508, 6, "console"], [481, 15, 508, 13], [481, 16, 508, 14, "log"], [481, 19, 508, 17], [481, 20, 508, 18], [481, 84, 508, 82], [481, 85, 508, 83], [482, 8, 509, 6, "setProcessingState"], [482, 26, 509, 24], [482, 27, 509, 25], [482, 39, 509, 37], [482, 40, 509, 38], [483, 8, 510, 6, "setProcessingProgress"], [483, 29, 510, 27], [483, 30, 510, 28], [483, 32, 510, 30], [483, 33, 510, 31], [485, 8, 512, 6], [486, 8, 513, 6], [486, 14, 513, 12, "canvas"], [486, 20, 513, 18], [486, 23, 513, 21, "document"], [486, 31, 513, 29], [486, 32, 513, 30, "createElement"], [486, 45, 513, 43], [486, 46, 513, 44], [486, 54, 513, 52], [486, 55, 513, 53], [487, 8, 514, 6], [487, 14, 514, 12, "ctx"], [487, 17, 514, 15], [487, 20, 514, 18, "canvas"], [487, 26, 514, 24], [487, 27, 514, 25, "getContext"], [487, 37, 514, 35], [487, 38, 514, 36], [487, 42, 514, 40], [487, 43, 514, 41], [488, 8, 515, 6], [488, 12, 515, 10], [488, 13, 515, 11, "ctx"], [488, 16, 515, 14], [488, 18, 515, 16], [488, 24, 515, 22], [488, 28, 515, 26, "Error"], [488, 33, 515, 31], [488, 34, 515, 32], [488, 64, 515, 62], [488, 65, 515, 63], [490, 8, 517, 6], [491, 8, 518, 6], [491, 14, 518, 12, "img"], [491, 17, 518, 15], [491, 20, 518, 18], [491, 24, 518, 22, "Image"], [491, 29, 518, 27], [491, 30, 518, 28], [491, 31, 518, 29], [492, 8, 519, 6], [492, 14, 519, 12], [492, 18, 519, 16, "Promise"], [492, 25, 519, 23], [492, 26, 519, 24], [492, 27, 519, 25, "resolve"], [492, 34, 519, 32], [492, 36, 519, 34, "reject"], [492, 42, 519, 40], [492, 47, 519, 45], [493, 10, 520, 8, "img"], [493, 13, 520, 11], [493, 14, 520, 12, "onload"], [493, 20, 520, 18], [493, 23, 520, 21, "resolve"], [493, 30, 520, 28], [494, 10, 521, 8, "img"], [494, 13, 521, 11], [494, 14, 521, 12, "onerror"], [494, 21, 521, 19], [494, 24, 521, 22, "reject"], [494, 30, 521, 28], [495, 10, 522, 8, "img"], [495, 13, 522, 11], [495, 14, 522, 12, "src"], [495, 17, 522, 15], [495, 20, 522, 18, "photoUri"], [495, 28, 522, 26], [496, 8, 523, 6], [496, 9, 523, 7], [496, 10, 523, 8], [498, 8, 525, 6], [499, 8, 526, 6, "canvas"], [499, 14, 526, 12], [499, 15, 526, 13, "width"], [499, 20, 526, 18], [499, 23, 526, 21, "img"], [499, 26, 526, 24], [499, 27, 526, 25, "width"], [499, 32, 526, 30], [500, 8, 527, 6, "canvas"], [500, 14, 527, 12], [500, 15, 527, 13, "height"], [500, 21, 527, 19], [500, 24, 527, 22, "img"], [500, 27, 527, 25], [500, 28, 527, 26, "height"], [500, 34, 527, 32], [501, 8, 528, 6, "console"], [501, 15, 528, 13], [501, 16, 528, 14, "log"], [501, 19, 528, 17], [501, 20, 528, 18], [501, 54, 528, 52], [501, 56, 528, 54], [502, 10, 528, 56, "width"], [502, 15, 528, 61], [502, 17, 528, 63, "img"], [502, 20, 528, 66], [502, 21, 528, 67, "width"], [502, 26, 528, 72], [503, 10, 528, 74, "height"], [503, 16, 528, 80], [503, 18, 528, 82, "img"], [503, 21, 528, 85], [503, 22, 528, 86, "height"], [504, 8, 528, 93], [504, 9, 528, 94], [504, 10, 528, 95], [506, 8, 530, 6], [507, 8, 531, 6, "ctx"], [507, 11, 531, 9], [507, 12, 531, 10, "drawImage"], [507, 21, 531, 19], [507, 22, 531, 20, "img"], [507, 25, 531, 23], [507, 27, 531, 25], [507, 28, 531, 26], [507, 30, 531, 28], [507, 31, 531, 29], [507, 32, 531, 30], [508, 8, 532, 6, "console"], [508, 15, 532, 13], [508, 16, 532, 14, "log"], [508, 19, 532, 17], [508, 20, 532, 18], [508, 72, 532, 70], [508, 73, 532, 71], [509, 8, 534, 6, "setProcessingProgress"], [509, 29, 534, 27], [509, 30, 534, 28], [509, 32, 534, 30], [509, 33, 534, 31], [511, 8, 536, 6], [512, 8, 537, 6], [512, 12, 537, 10, "detectedFaces"], [512, 25, 537, 23], [512, 28, 537, 26], [512, 30, 537, 28], [513, 8, 539, 6, "console"], [513, 15, 539, 13], [513, 16, 539, 14, "log"], [513, 19, 539, 17], [513, 20, 539, 18], [513, 81, 539, 79], [513, 82, 539, 80], [515, 8, 541, 6], [516, 8, 542, 6], [516, 12, 542, 10], [517, 10, 543, 8], [517, 16, 543, 14, "loadTensorFlowFaceDetection"], [517, 43, 543, 41], [517, 44, 543, 42], [517, 45, 543, 43], [518, 10, 544, 8, "detectedFaces"], [518, 23, 544, 21], [518, 26, 544, 24], [518, 32, 544, 30, "detectFacesWithTensorFlow"], [518, 57, 544, 55], [518, 58, 544, 56, "img"], [518, 61, 544, 59], [518, 62, 544, 60], [519, 10, 545, 8, "console"], [519, 17, 545, 15], [519, 18, 545, 16, "log"], [519, 21, 545, 19], [519, 22, 545, 20], [519, 70, 545, 68, "detectedFaces"], [519, 83, 545, 81], [519, 84, 545, 82, "length"], [519, 90, 545, 88], [519, 98, 545, 96], [519, 99, 545, 97], [520, 8, 546, 6], [520, 9, 546, 7], [520, 10, 546, 8], [520, 17, 546, 15, "tensorFlowError"], [520, 32, 546, 30], [520, 34, 546, 32], [521, 10, 547, 8, "console"], [521, 17, 547, 15], [521, 18, 547, 16, "warn"], [521, 22, 547, 20], [521, 23, 547, 21], [521, 61, 547, 59], [521, 63, 547, 61, "tensorFlowError"], [521, 78, 547, 76], [521, 79, 547, 77], [523, 10, 549, 8], [524, 10, 550, 8, "console"], [524, 17, 550, 15], [524, 18, 550, 16, "log"], [524, 21, 550, 19], [524, 22, 550, 20], [524, 86, 550, 84], [524, 87, 550, 85], [525, 10, 551, 8, "detectedFaces"], [525, 23, 551, 21], [525, 26, 551, 24, "detectFacesHeuristic"], [525, 46, 551, 44], [525, 47, 551, 45, "img"], [525, 50, 551, 48], [525, 52, 551, 50, "ctx"], [525, 55, 551, 53], [525, 56, 551, 54], [526, 10, 552, 8, "console"], [526, 17, 552, 15], [526, 18, 552, 16, "log"], [526, 21, 552, 19], [526, 22, 552, 20], [526, 70, 552, 68, "detectedFaces"], [526, 83, 552, 81], [526, 84, 552, 82, "length"], [526, 90, 552, 88], [526, 98, 552, 96], [526, 99, 552, 97], [527, 8, 553, 6], [528, 8, 555, 6, "console"], [528, 15, 555, 13], [528, 16, 555, 14, "log"], [528, 19, 555, 17], [528, 20, 555, 18], [528, 72, 555, 70, "detectedFaces"], [528, 85, 555, 83], [528, 86, 555, 84, "length"], [528, 92, 555, 90], [528, 100, 555, 98], [528, 101, 555, 99], [529, 8, 556, 6], [529, 12, 556, 10, "detectedFaces"], [529, 25, 556, 23], [529, 26, 556, 24, "length"], [529, 32, 556, 30], [529, 35, 556, 33], [529, 36, 556, 34], [529, 38, 556, 36], [530, 10, 557, 8, "console"], [530, 17, 557, 15], [530, 18, 557, 16, "log"], [530, 21, 557, 19], [530, 22, 557, 20], [530, 66, 557, 64], [530, 68, 557, 66, "detectedFaces"], [530, 81, 557, 79], [530, 82, 557, 80, "map"], [530, 85, 557, 83], [530, 86, 557, 84], [530, 87, 557, 85, "face"], [530, 91, 557, 89], [530, 93, 557, 91, "i"], [530, 94, 557, 92], [530, 100, 557, 98], [531, 12, 558, 10, "faceNumber"], [531, 22, 558, 20], [531, 24, 558, 22, "i"], [531, 25, 558, 23], [531, 28, 558, 26], [531, 29, 558, 27], [532, 12, 559, 10, "centerX"], [532, 19, 559, 17], [532, 21, 559, 19, "face"], [532, 25, 559, 23], [532, 26, 559, 24, "boundingBox"], [532, 37, 559, 35], [532, 38, 559, 36, "xCenter"], [532, 45, 559, 43], [533, 12, 560, 10, "centerY"], [533, 19, 560, 17], [533, 21, 560, 19, "face"], [533, 25, 560, 23], [533, 26, 560, 24, "boundingBox"], [533, 37, 560, 35], [533, 38, 560, 36, "yCenter"], [533, 45, 560, 43], [534, 12, 561, 10, "width"], [534, 17, 561, 15], [534, 19, 561, 17, "face"], [534, 23, 561, 21], [534, 24, 561, 22, "boundingBox"], [534, 35, 561, 33], [534, 36, 561, 34, "width"], [534, 41, 561, 39], [535, 12, 562, 10, "height"], [535, 18, 562, 16], [535, 20, 562, 18, "face"], [535, 24, 562, 22], [535, 25, 562, 23, "boundingBox"], [535, 36, 562, 34], [535, 37, 562, 35, "height"], [536, 10, 563, 8], [536, 11, 563, 9], [536, 12, 563, 10], [536, 13, 563, 11], [536, 14, 563, 12], [537, 8, 564, 6], [537, 9, 564, 7], [537, 15, 564, 13], [538, 10, 565, 8, "console"], [538, 17, 565, 15], [538, 18, 565, 16, "log"], [538, 21, 565, 19], [538, 22, 565, 20], [538, 91, 565, 89], [538, 92, 565, 90], [539, 8, 566, 6], [540, 8, 568, 6, "setProcessingProgress"], [540, 29, 568, 27], [540, 30, 568, 28], [540, 32, 568, 30], [540, 33, 568, 31], [542, 8, 570, 6], [543, 8, 571, 6], [543, 12, 571, 10, "detectedFaces"], [543, 25, 571, 23], [543, 26, 571, 24, "length"], [543, 32, 571, 30], [543, 35, 571, 33], [543, 36, 571, 34], [543, 38, 571, 36], [544, 10, 572, 8, "console"], [544, 17, 572, 15], [544, 18, 572, 16, "log"], [544, 21, 572, 19], [544, 22, 572, 20], [544, 61, 572, 59, "detectedFaces"], [544, 74, 572, 72], [544, 75, 572, 73, "length"], [544, 81, 572, 79], [544, 101, 572, 99], [544, 102, 572, 100], [545, 10, 574, 8, "detectedFaces"], [545, 23, 574, 21], [545, 24, 574, 22, "for<PERSON>ach"], [545, 31, 574, 29], [545, 32, 574, 30], [545, 33, 574, 31, "detection"], [545, 42, 574, 40], [545, 44, 574, 42, "index"], [545, 49, 574, 47], [545, 54, 574, 52], [546, 12, 575, 10], [546, 18, 575, 16, "bbox"], [546, 22, 575, 20], [546, 25, 575, 23, "detection"], [546, 34, 575, 32], [546, 35, 575, 33, "boundingBox"], [546, 46, 575, 44], [548, 12, 577, 10], [549, 12, 578, 10], [549, 18, 578, 16, "faceX"], [549, 23, 578, 21], [549, 26, 578, 24, "bbox"], [549, 30, 578, 28], [549, 31, 578, 29, "xCenter"], [549, 38, 578, 36], [549, 41, 578, 39, "img"], [549, 44, 578, 42], [549, 45, 578, 43, "width"], [549, 50, 578, 48], [549, 53, 578, 52, "bbox"], [549, 57, 578, 56], [549, 58, 578, 57, "width"], [549, 63, 578, 62], [549, 66, 578, 65, "img"], [549, 69, 578, 68], [549, 70, 578, 69, "width"], [549, 75, 578, 74], [549, 78, 578, 78], [549, 79, 578, 79], [550, 12, 579, 10], [550, 18, 579, 16, "faceY"], [550, 23, 579, 21], [550, 26, 579, 24, "bbox"], [550, 30, 579, 28], [550, 31, 579, 29, "yCenter"], [550, 38, 579, 36], [550, 41, 579, 39, "img"], [550, 44, 579, 42], [550, 45, 579, 43, "height"], [550, 51, 579, 49], [550, 54, 579, 53, "bbox"], [550, 58, 579, 57], [550, 59, 579, 58, "height"], [550, 65, 579, 64], [550, 68, 579, 67, "img"], [550, 71, 579, 70], [550, 72, 579, 71, "height"], [550, 78, 579, 77], [550, 81, 579, 81], [550, 82, 579, 82], [551, 12, 580, 10], [551, 18, 580, 16, "faceWidth"], [551, 27, 580, 25], [551, 30, 580, 28, "bbox"], [551, 34, 580, 32], [551, 35, 580, 33, "width"], [551, 40, 580, 38], [551, 43, 580, 41, "img"], [551, 46, 580, 44], [551, 47, 580, 45, "width"], [551, 52, 580, 50], [552, 12, 581, 10], [552, 18, 581, 16, "faceHeight"], [552, 28, 581, 26], [552, 31, 581, 29, "bbox"], [552, 35, 581, 33], [552, 36, 581, 34, "height"], [552, 42, 581, 40], [552, 45, 581, 43, "img"], [552, 48, 581, 46], [552, 49, 581, 47, "height"], [552, 55, 581, 53], [554, 12, 583, 10], [555, 12, 584, 10], [555, 18, 584, 16, "padding"], [555, 25, 584, 23], [555, 28, 584, 26], [555, 31, 584, 29], [556, 12, 585, 10], [556, 18, 585, 16, "paddedX"], [556, 25, 585, 23], [556, 28, 585, 26, "Math"], [556, 32, 585, 30], [556, 33, 585, 31, "max"], [556, 36, 585, 34], [556, 37, 585, 35], [556, 38, 585, 36], [556, 40, 585, 38, "faceX"], [556, 45, 585, 43], [556, 48, 585, 46, "faceWidth"], [556, 57, 585, 55], [556, 60, 585, 58, "padding"], [556, 67, 585, 65], [556, 68, 585, 66], [557, 12, 586, 10], [557, 18, 586, 16, "paddedY"], [557, 25, 586, 23], [557, 28, 586, 26, "Math"], [557, 32, 586, 30], [557, 33, 586, 31, "max"], [557, 36, 586, 34], [557, 37, 586, 35], [557, 38, 586, 36], [557, 40, 586, 38, "faceY"], [557, 45, 586, 43], [557, 48, 586, 46, "faceHeight"], [557, 58, 586, 56], [557, 61, 586, 59, "padding"], [557, 68, 586, 66], [557, 69, 586, 67], [558, 12, 587, 10], [558, 18, 587, 16, "<PERSON><PERSON><PERSON><PERSON>"], [558, 29, 587, 27], [558, 32, 587, 30, "Math"], [558, 36, 587, 34], [558, 37, 587, 35, "min"], [558, 40, 587, 38], [558, 41, 587, 39, "img"], [558, 44, 587, 42], [558, 45, 587, 43, "width"], [558, 50, 587, 48], [558, 53, 587, 51, "paddedX"], [558, 60, 587, 58], [558, 62, 587, 60, "faceWidth"], [558, 71, 587, 69], [558, 75, 587, 73], [558, 76, 587, 74], [558, 79, 587, 77], [558, 80, 587, 78], [558, 83, 587, 81, "padding"], [558, 90, 587, 88], [558, 91, 587, 89], [558, 92, 587, 90], [559, 12, 588, 10], [559, 18, 588, 16, "paddedHeight"], [559, 30, 588, 28], [559, 33, 588, 31, "Math"], [559, 37, 588, 35], [559, 38, 588, 36, "min"], [559, 41, 588, 39], [559, 42, 588, 40, "img"], [559, 45, 588, 43], [559, 46, 588, 44, "height"], [559, 52, 588, 50], [559, 55, 588, 53, "paddedY"], [559, 62, 588, 60], [559, 64, 588, 62, "faceHeight"], [559, 74, 588, 72], [559, 78, 588, 76], [559, 79, 588, 77], [559, 82, 588, 80], [559, 83, 588, 81], [559, 86, 588, 84, "padding"], [559, 93, 588, 91], [559, 94, 588, 92], [559, 95, 588, 93], [560, 12, 590, 10, "console"], [560, 19, 590, 17], [560, 20, 590, 18, "log"], [560, 23, 590, 21], [560, 24, 590, 22], [560, 60, 590, 58, "index"], [560, 65, 590, 63], [560, 68, 590, 66], [560, 69, 590, 67], [560, 72, 590, 70], [560, 74, 590, 72], [561, 14, 591, 12, "original"], [561, 22, 591, 20], [561, 24, 591, 22], [562, 16, 591, 24, "x"], [562, 17, 591, 25], [562, 19, 591, 27, "Math"], [562, 23, 591, 31], [562, 24, 591, 32, "round"], [562, 29, 591, 37], [562, 30, 591, 38, "faceX"], [562, 35, 591, 43], [562, 36, 591, 44], [563, 16, 591, 46, "y"], [563, 17, 591, 47], [563, 19, 591, 49, "Math"], [563, 23, 591, 53], [563, 24, 591, 54, "round"], [563, 29, 591, 59], [563, 30, 591, 60, "faceY"], [563, 35, 591, 65], [563, 36, 591, 66], [564, 16, 591, 68, "w"], [564, 17, 591, 69], [564, 19, 591, 71, "Math"], [564, 23, 591, 75], [564, 24, 591, 76, "round"], [564, 29, 591, 81], [564, 30, 591, 82, "faceWidth"], [564, 39, 591, 91], [564, 40, 591, 92], [565, 16, 591, 94, "h"], [565, 17, 591, 95], [565, 19, 591, 97, "Math"], [565, 23, 591, 101], [565, 24, 591, 102, "round"], [565, 29, 591, 107], [565, 30, 591, 108, "faceHeight"], [565, 40, 591, 118], [566, 14, 591, 120], [566, 15, 591, 121], [567, 14, 592, 12, "padded"], [567, 20, 592, 18], [567, 22, 592, 20], [568, 16, 592, 22, "x"], [568, 17, 592, 23], [568, 19, 592, 25, "Math"], [568, 23, 592, 29], [568, 24, 592, 30, "round"], [568, 29, 592, 35], [568, 30, 592, 36, "paddedX"], [568, 37, 592, 43], [568, 38, 592, 44], [569, 16, 592, 46, "y"], [569, 17, 592, 47], [569, 19, 592, 49, "Math"], [569, 23, 592, 53], [569, 24, 592, 54, "round"], [569, 29, 592, 59], [569, 30, 592, 60, "paddedY"], [569, 37, 592, 67], [569, 38, 592, 68], [570, 16, 592, 70, "w"], [570, 17, 592, 71], [570, 19, 592, 73, "Math"], [570, 23, 592, 77], [570, 24, 592, 78, "round"], [570, 29, 592, 83], [570, 30, 592, 84, "<PERSON><PERSON><PERSON><PERSON>"], [570, 41, 592, 95], [570, 42, 592, 96], [571, 16, 592, 98, "h"], [571, 17, 592, 99], [571, 19, 592, 101, "Math"], [571, 23, 592, 105], [571, 24, 592, 106, "round"], [571, 29, 592, 111], [571, 30, 592, 112, "paddedHeight"], [571, 42, 592, 124], [572, 14, 592, 126], [573, 12, 593, 10], [573, 13, 593, 11], [573, 14, 593, 12], [575, 12, 595, 10], [576, 12, 596, 10, "console"], [576, 19, 596, 17], [576, 20, 596, 18, "log"], [576, 23, 596, 21], [576, 24, 596, 22], [576, 70, 596, 68], [576, 72, 596, 70], [577, 14, 597, 12, "width"], [577, 19, 597, 17], [577, 21, 597, 19, "canvas"], [577, 27, 597, 25], [577, 28, 597, 26, "width"], [577, 33, 597, 31], [578, 14, 598, 12, "height"], [578, 20, 598, 18], [578, 22, 598, 20, "canvas"], [578, 28, 598, 26], [578, 29, 598, 27, "height"], [578, 35, 598, 33], [579, 14, 599, 12, "contextValid"], [579, 26, 599, 24], [579, 28, 599, 26], [579, 29, 599, 27], [579, 30, 599, 28, "ctx"], [580, 12, 600, 10], [580, 13, 600, 11], [580, 14, 600, 12], [582, 12, 602, 10], [583, 12, 603, 10, "applyStrongBlur"], [583, 27, 603, 25], [583, 28, 603, 26, "ctx"], [583, 31, 603, 29], [583, 33, 603, 31, "paddedX"], [583, 40, 603, 38], [583, 42, 603, 40, "paddedY"], [583, 49, 603, 47], [583, 51, 603, 49, "<PERSON><PERSON><PERSON><PERSON>"], [583, 62, 603, 60], [583, 64, 603, 62, "paddedHeight"], [583, 76, 603, 74], [583, 77, 603, 75], [585, 12, 605, 10], [586, 12, 606, 10, "console"], [586, 19, 606, 17], [586, 20, 606, 18, "log"], [586, 23, 606, 21], [586, 24, 606, 22], [586, 102, 606, 100], [586, 103, 606, 101], [588, 12, 608, 10], [589, 12, 609, 10], [589, 18, 609, 16, "testImageData"], [589, 31, 609, 29], [589, 34, 609, 32, "ctx"], [589, 37, 609, 35], [589, 38, 609, 36, "getImageData"], [589, 50, 609, 48], [589, 51, 609, 49, "paddedX"], [589, 58, 609, 56], [589, 61, 609, 59], [589, 63, 609, 61], [589, 65, 609, 63, "paddedY"], [589, 72, 609, 70], [589, 75, 609, 73], [589, 77, 609, 75], [589, 79, 609, 77], [589, 81, 609, 79], [589, 83, 609, 81], [589, 85, 609, 83], [589, 86, 609, 84], [590, 12, 610, 10, "console"], [590, 19, 610, 17], [590, 20, 610, 18, "log"], [590, 23, 610, 21], [590, 24, 610, 22], [590, 70, 610, 68], [590, 72, 610, 70], [591, 14, 611, 12, "firstPixel"], [591, 24, 611, 22], [591, 26, 611, 24], [591, 27, 611, 25, "testImageData"], [591, 40, 611, 38], [591, 41, 611, 39, "data"], [591, 45, 611, 43], [591, 46, 611, 44], [591, 47, 611, 45], [591, 48, 611, 46], [591, 50, 611, 48, "testImageData"], [591, 63, 611, 61], [591, 64, 611, 62, "data"], [591, 68, 611, 66], [591, 69, 611, 67], [591, 70, 611, 68], [591, 71, 611, 69], [591, 73, 611, 71, "testImageData"], [591, 86, 611, 84], [591, 87, 611, 85, "data"], [591, 91, 611, 89], [591, 92, 611, 90], [591, 93, 611, 91], [591, 94, 611, 92], [591, 95, 611, 93], [592, 14, 612, 12, "secondPixel"], [592, 25, 612, 23], [592, 27, 612, 25], [592, 28, 612, 26, "testImageData"], [592, 41, 612, 39], [592, 42, 612, 40, "data"], [592, 46, 612, 44], [592, 47, 612, 45], [592, 48, 612, 46], [592, 49, 612, 47], [592, 51, 612, 49, "testImageData"], [592, 64, 612, 62], [592, 65, 612, 63, "data"], [592, 69, 612, 67], [592, 70, 612, 68], [592, 71, 612, 69], [592, 72, 612, 70], [592, 74, 612, 72, "testImageData"], [592, 87, 612, 85], [592, 88, 612, 86, "data"], [592, 92, 612, 90], [592, 93, 612, 91], [592, 94, 612, 92], [592, 95, 612, 93], [593, 12, 613, 10], [593, 13, 613, 11], [593, 14, 613, 12], [594, 12, 615, 10, "console"], [594, 19, 615, 17], [594, 20, 615, 18, "log"], [594, 23, 615, 21], [594, 24, 615, 22], [594, 50, 615, 48, "index"], [594, 55, 615, 53], [594, 58, 615, 56], [594, 59, 615, 57], [594, 79, 615, 77], [594, 80, 615, 78], [595, 10, 616, 8], [595, 11, 616, 9], [595, 12, 616, 10], [596, 10, 618, 8, "console"], [596, 17, 618, 15], [596, 18, 618, 16, "log"], [596, 21, 618, 19], [596, 22, 618, 20], [596, 48, 618, 46, "detectedFaces"], [596, 61, 618, 59], [596, 62, 618, 60, "length"], [596, 68, 618, 66], [596, 104, 618, 102], [596, 105, 618, 103], [597, 8, 619, 6], [597, 9, 619, 7], [597, 15, 619, 13], [598, 10, 620, 8, "console"], [598, 17, 620, 15], [598, 18, 620, 16, "log"], [598, 21, 620, 19], [598, 22, 620, 20], [598, 109, 620, 107], [598, 110, 620, 108], [599, 10, 621, 8], [600, 10, 622, 8, "applyFallbackFaceBlur"], [600, 31, 622, 29], [600, 32, 622, 30, "ctx"], [600, 35, 622, 33], [600, 37, 622, 35, "img"], [600, 40, 622, 38], [600, 41, 622, 39, "width"], [600, 46, 622, 44], [600, 48, 622, 46, "img"], [600, 51, 622, 49], [600, 52, 622, 50, "height"], [600, 58, 622, 56], [600, 59, 622, 57], [601, 8, 623, 6], [602, 8, 625, 6, "setProcessingProgress"], [602, 29, 625, 27], [602, 30, 625, 28], [602, 32, 625, 30], [602, 33, 625, 31], [604, 8, 627, 6], [605, 8, 628, 6, "console"], [605, 15, 628, 13], [605, 16, 628, 14, "log"], [605, 19, 628, 17], [605, 20, 628, 18], [605, 85, 628, 83], [605, 86, 628, 84], [606, 8, 629, 6], [606, 14, 629, 12, "blurredImageBlob"], [606, 30, 629, 28], [606, 33, 629, 31], [606, 39, 629, 37], [606, 43, 629, 41, "Promise"], [606, 50, 629, 48], [606, 51, 629, 56, "resolve"], [606, 58, 629, 63], [606, 62, 629, 68], [607, 10, 630, 8, "canvas"], [607, 16, 630, 14], [607, 17, 630, 15, "toBlob"], [607, 23, 630, 21], [607, 24, 630, 23, "blob"], [607, 28, 630, 27], [607, 32, 630, 32, "resolve"], [607, 39, 630, 39], [607, 40, 630, 40, "blob"], [607, 44, 630, 45], [607, 45, 630, 46], [607, 47, 630, 48], [607, 59, 630, 60], [607, 61, 630, 62], [607, 64, 630, 65], [607, 65, 630, 66], [608, 8, 631, 6], [608, 9, 631, 7], [608, 10, 631, 8], [609, 8, 633, 6], [609, 14, 633, 12, "blurredImageUrl"], [609, 29, 633, 27], [609, 32, 633, 30, "URL"], [609, 35, 633, 33], [609, 36, 633, 34, "createObjectURL"], [609, 51, 633, 49], [609, 52, 633, 50, "blurredImageBlob"], [609, 68, 633, 66], [609, 69, 633, 67], [610, 8, 634, 6, "console"], [610, 15, 634, 13], [610, 16, 634, 14, "log"], [610, 19, 634, 17], [610, 20, 634, 18], [610, 66, 634, 64], [610, 68, 634, 66, "blurredImageUrl"], [610, 83, 634, 81], [610, 84, 634, 82, "substring"], [610, 93, 634, 91], [610, 94, 634, 92], [610, 95, 634, 93], [610, 97, 634, 95], [610, 99, 634, 97], [610, 100, 634, 98], [610, 103, 634, 101], [610, 108, 634, 106], [610, 109, 634, 107], [611, 8, 636, 6, "setProcessingProgress"], [611, 29, 636, 27], [611, 30, 636, 28], [611, 33, 636, 31], [611, 34, 636, 32], [613, 8, 638, 6], [614, 8, 639, 6], [614, 14, 639, 12, "completeProcessing"], [614, 32, 639, 30], [614, 33, 639, 31, "blurredImageUrl"], [614, 48, 639, 46], [614, 49, 639, 47], [615, 6, 641, 4], [615, 7, 641, 5], [615, 8, 641, 6], [615, 15, 641, 13, "error"], [615, 20, 641, 18], [615, 22, 641, 20], [616, 8, 642, 6, "console"], [616, 15, 642, 13], [616, 16, 642, 14, "error"], [616, 21, 642, 19], [616, 22, 642, 20], [616, 57, 642, 55], [616, 59, 642, 57, "error"], [616, 64, 642, 62], [616, 65, 642, 63], [617, 8, 643, 6, "setErrorMessage"], [617, 23, 643, 21], [617, 24, 643, 22], [617, 50, 643, 48], [617, 51, 643, 49], [618, 8, 644, 6, "setProcessingState"], [618, 26, 644, 24], [618, 27, 644, 25], [618, 34, 644, 32], [618, 35, 644, 33], [619, 6, 645, 4], [620, 4, 646, 2], [620, 5, 646, 3], [622, 4, 648, 2], [623, 4, 649, 2], [623, 10, 649, 8, "completeProcessing"], [623, 28, 649, 26], [623, 31, 649, 29], [623, 37, 649, 36, "blurredImageUrl"], [623, 52, 649, 59], [623, 56, 649, 64], [624, 6, 650, 4], [624, 10, 650, 8], [625, 8, 651, 6, "setProcessingState"], [625, 26, 651, 24], [625, 27, 651, 25], [625, 37, 651, 35], [625, 38, 651, 36], [627, 8, 653, 6], [628, 8, 654, 6], [628, 14, 654, 12, "timestamp"], [628, 23, 654, 21], [628, 26, 654, 24, "Date"], [628, 30, 654, 28], [628, 31, 654, 29, "now"], [628, 34, 654, 32], [628, 35, 654, 33], [628, 36, 654, 34], [629, 8, 655, 6], [629, 14, 655, 12, "result"], [629, 20, 655, 18], [629, 23, 655, 21], [630, 10, 656, 8, "imageUrl"], [630, 18, 656, 16], [630, 20, 656, 18, "blurredImageUrl"], [630, 35, 656, 33], [631, 10, 657, 8, "localUri"], [631, 18, 657, 16], [631, 20, 657, 18, "blurredImageUrl"], [631, 35, 657, 33], [632, 10, 658, 8, "challengeCode"], [632, 23, 658, 21], [632, 25, 658, 23, "challengeCode"], [632, 38, 658, 36], [632, 42, 658, 40], [632, 44, 658, 42], [633, 10, 659, 8, "timestamp"], [633, 19, 659, 17], [634, 10, 660, 8, "jobId"], [634, 15, 660, 13], [634, 17, 660, 15], [634, 27, 660, 25, "timestamp"], [634, 36, 660, 34], [634, 38, 660, 36], [635, 10, 661, 8, "status"], [635, 16, 661, 14], [635, 18, 661, 16], [636, 8, 662, 6], [636, 9, 662, 7], [637, 8, 664, 6, "console"], [637, 15, 664, 13], [637, 16, 664, 14, "log"], [637, 19, 664, 17], [637, 20, 664, 18], [637, 100, 664, 98], [637, 102, 664, 100], [638, 10, 665, 8, "imageUrl"], [638, 18, 665, 16], [638, 20, 665, 18, "blurredImageUrl"], [638, 35, 665, 33], [638, 36, 665, 34, "substring"], [638, 45, 665, 43], [638, 46, 665, 44], [638, 47, 665, 45], [638, 49, 665, 47], [638, 51, 665, 49], [638, 52, 665, 50], [638, 55, 665, 53], [638, 60, 665, 58], [639, 10, 666, 8, "timestamp"], [639, 19, 666, 17], [640, 10, 667, 8, "jobId"], [640, 15, 667, 13], [640, 17, 667, 15, "result"], [640, 23, 667, 21], [640, 24, 667, 22, "jobId"], [641, 8, 668, 6], [641, 9, 668, 7], [641, 10, 668, 8], [643, 8, 670, 6], [644, 8, 671, 6, "onComplete"], [644, 18, 671, 16], [644, 19, 671, 17, "result"], [644, 25, 671, 23], [644, 26, 671, 24], [645, 6, 673, 4], [645, 7, 673, 5], [645, 8, 673, 6], [645, 15, 673, 13, "error"], [645, 20, 673, 18], [645, 22, 673, 20], [646, 8, 674, 6, "console"], [646, 15, 674, 13], [646, 16, 674, 14, "error"], [646, 21, 674, 19], [646, 22, 674, 20], [646, 57, 674, 55], [646, 59, 674, 57, "error"], [646, 64, 674, 62], [646, 65, 674, 63], [647, 8, 675, 6, "setErrorMessage"], [647, 23, 675, 21], [647, 24, 675, 22], [647, 56, 675, 54], [647, 57, 675, 55], [648, 8, 676, 6, "setProcessingState"], [648, 26, 676, 24], [648, 27, 676, 25], [648, 34, 676, 32], [648, 35, 676, 33], [649, 6, 677, 4], [650, 4, 678, 2], [650, 5, 678, 3], [652, 4, 680, 2], [653, 4, 681, 2], [653, 10, 681, 8, "triggerServerProcessing"], [653, 33, 681, 31], [653, 36, 681, 34], [653, 42, 681, 34, "triggerServerProcessing"], [653, 43, 681, 41, "privateImageUrl"], [653, 58, 681, 64], [653, 60, 681, 66, "timestamp"], [653, 69, 681, 83], [653, 74, 681, 88], [654, 6, 682, 4], [654, 10, 682, 8], [655, 8, 683, 6, "console"], [655, 15, 683, 13], [655, 16, 683, 14, "log"], [655, 19, 683, 17], [655, 20, 683, 18], [655, 74, 683, 72], [655, 76, 683, 74, "privateImageUrl"], [655, 91, 683, 89], [655, 92, 683, 90], [656, 8, 684, 6, "setProcessingState"], [656, 26, 684, 24], [656, 27, 684, 25], [656, 39, 684, 37], [656, 40, 684, 38], [657, 8, 685, 6, "setProcessingProgress"], [657, 29, 685, 27], [657, 30, 685, 28], [657, 32, 685, 30], [657, 33, 685, 31], [658, 8, 687, 6], [658, 14, 687, 12, "requestBody"], [658, 25, 687, 23], [658, 28, 687, 26], [659, 10, 688, 8, "imageUrl"], [659, 18, 688, 16], [659, 20, 688, 18, "privateImageUrl"], [659, 35, 688, 33], [660, 10, 689, 8, "userId"], [660, 16, 689, 14], [661, 10, 690, 8, "requestId"], [661, 19, 690, 17], [662, 10, 691, 8, "timestamp"], [662, 19, 691, 17], [663, 10, 692, 8, "platform"], [663, 18, 692, 16], [663, 20, 692, 18], [664, 8, 693, 6], [664, 9, 693, 7], [665, 8, 695, 6, "console"], [665, 15, 695, 13], [665, 16, 695, 14, "log"], [665, 19, 695, 17], [665, 20, 695, 18], [665, 65, 695, 63], [665, 67, 695, 65, "requestBody"], [665, 78, 695, 76], [665, 79, 695, 77], [667, 8, 697, 6], [668, 8, 698, 6], [668, 14, 698, 12, "response"], [668, 22, 698, 20], [668, 25, 698, 23], [668, 31, 698, 29, "fetch"], [668, 36, 698, 34], [668, 37, 698, 35], [668, 40, 698, 38, "API_BASE_URL"], [668, 52, 698, 50], [668, 72, 698, 70], [668, 74, 698, 72], [669, 10, 699, 8, "method"], [669, 16, 699, 14], [669, 18, 699, 16], [669, 24, 699, 22], [670, 10, 700, 8, "headers"], [670, 17, 700, 15], [670, 19, 700, 17], [671, 12, 701, 10], [671, 26, 701, 24], [671, 28, 701, 26], [671, 46, 701, 44], [672, 12, 702, 10], [672, 27, 702, 25], [672, 29, 702, 27], [672, 39, 702, 37], [672, 45, 702, 43, "getAuthToken"], [672, 57, 702, 55], [672, 58, 702, 56], [672, 59, 702, 57], [673, 10, 703, 8], [673, 11, 703, 9], [674, 10, 704, 8, "body"], [674, 14, 704, 12], [674, 16, 704, 14, "JSON"], [674, 20, 704, 18], [674, 21, 704, 19, "stringify"], [674, 30, 704, 28], [674, 31, 704, 29, "requestBody"], [674, 42, 704, 40], [675, 8, 705, 6], [675, 9, 705, 7], [675, 10, 705, 8], [676, 8, 707, 6], [676, 12, 707, 10], [676, 13, 707, 11, "response"], [676, 21, 707, 19], [676, 22, 707, 20, "ok"], [676, 24, 707, 22], [676, 26, 707, 24], [677, 10, 708, 8], [677, 16, 708, 14, "errorText"], [677, 25, 708, 23], [677, 28, 708, 26], [677, 34, 708, 32, "response"], [677, 42, 708, 40], [677, 43, 708, 41, "text"], [677, 47, 708, 45], [677, 48, 708, 46], [677, 49, 708, 47], [678, 10, 709, 8, "console"], [678, 17, 709, 15], [678, 18, 709, 16, "error"], [678, 23, 709, 21], [678, 24, 709, 22], [678, 68, 709, 66], [678, 70, 709, 68, "response"], [678, 78, 709, 76], [678, 79, 709, 77, "status"], [678, 85, 709, 83], [678, 87, 709, 85, "errorText"], [678, 96, 709, 94], [678, 97, 709, 95], [679, 10, 710, 8], [679, 16, 710, 14], [679, 20, 710, 18, "Error"], [679, 25, 710, 23], [679, 26, 710, 24], [679, 48, 710, 46, "response"], [679, 56, 710, 54], [679, 57, 710, 55, "status"], [679, 63, 710, 61], [679, 67, 710, 65, "response"], [679, 75, 710, 73], [679, 76, 710, 74, "statusText"], [679, 86, 710, 84], [679, 88, 710, 86], [679, 89, 710, 87], [680, 8, 711, 6], [681, 8, 713, 6], [681, 14, 713, 12, "result"], [681, 20, 713, 18], [681, 23, 713, 21], [681, 29, 713, 27, "response"], [681, 37, 713, 35], [681, 38, 713, 36, "json"], [681, 42, 713, 40], [681, 43, 713, 41], [681, 44, 713, 42], [682, 8, 714, 6, "console"], [682, 15, 714, 13], [682, 16, 714, 14, "log"], [682, 19, 714, 17], [682, 20, 714, 18], [682, 68, 714, 66], [682, 70, 714, 68, "result"], [682, 76, 714, 74], [682, 77, 714, 75], [683, 8, 716, 6], [683, 12, 716, 10], [683, 13, 716, 11, "result"], [683, 19, 716, 17], [683, 20, 716, 18, "jobId"], [683, 25, 716, 23], [683, 27, 716, 25], [684, 10, 717, 8], [684, 16, 717, 14], [684, 20, 717, 18, "Error"], [684, 25, 717, 23], [684, 26, 717, 24], [684, 70, 717, 68], [684, 71, 717, 69], [685, 8, 718, 6], [687, 8, 720, 6], [688, 8, 721, 6], [688, 14, 721, 12, "pollForCompletion"], [688, 31, 721, 29], [688, 32, 721, 30, "result"], [688, 38, 721, 36], [688, 39, 721, 37, "jobId"], [688, 44, 721, 42], [688, 46, 721, 44, "timestamp"], [688, 55, 721, 53], [688, 56, 721, 54], [689, 6, 722, 4], [689, 7, 722, 5], [689, 8, 722, 6], [689, 15, 722, 13, "error"], [689, 20, 722, 18], [689, 22, 722, 20], [690, 8, 723, 6, "console"], [690, 15, 723, 13], [690, 16, 723, 14, "error"], [690, 21, 723, 19], [690, 22, 723, 20], [690, 57, 723, 55], [690, 59, 723, 57, "error"], [690, 64, 723, 62], [690, 65, 723, 63], [691, 8, 724, 6, "setErrorMessage"], [691, 23, 724, 21], [691, 24, 724, 22], [691, 52, 724, 50, "error"], [691, 57, 724, 55], [691, 58, 724, 56, "message"], [691, 65, 724, 63], [691, 67, 724, 65], [691, 68, 724, 66], [692, 8, 725, 6, "setProcessingState"], [692, 26, 725, 24], [692, 27, 725, 25], [692, 34, 725, 32], [692, 35, 725, 33], [693, 6, 726, 4], [694, 4, 727, 2], [694, 5, 727, 3], [695, 4, 728, 2], [696, 4, 729, 2], [696, 10, 729, 8, "pollForCompletion"], [696, 27, 729, 25], [696, 30, 729, 28], [696, 36, 729, 28, "pollForCompletion"], [696, 37, 729, 35, "jobId"], [696, 42, 729, 48], [696, 44, 729, 50, "timestamp"], [696, 53, 729, 67], [696, 55, 729, 69, "attempts"], [696, 63, 729, 77], [696, 66, 729, 80], [696, 67, 729, 81], [696, 72, 729, 86], [697, 6, 730, 4], [697, 12, 730, 10, "MAX_ATTEMPTS"], [697, 24, 730, 22], [697, 27, 730, 25], [697, 29, 730, 27], [697, 30, 730, 28], [697, 31, 730, 29], [698, 6, 731, 4], [698, 12, 731, 10, "POLL_INTERVAL"], [698, 25, 731, 23], [698, 28, 731, 26], [698, 32, 731, 30], [698, 33, 731, 31], [698, 34, 731, 32], [700, 6, 733, 4, "console"], [700, 13, 733, 11], [700, 14, 733, 12, "log"], [700, 17, 733, 15], [700, 18, 733, 16], [700, 53, 733, 51, "attempts"], [700, 61, 733, 59], [700, 64, 733, 62], [700, 65, 733, 63], [700, 69, 733, 67, "MAX_ATTEMPTS"], [700, 81, 733, 79], [700, 93, 733, 91, "jobId"], [700, 98, 733, 96], [700, 100, 733, 98], [700, 101, 733, 99], [701, 6, 735, 4], [701, 10, 735, 8, "attempts"], [701, 18, 735, 16], [701, 22, 735, 20, "MAX_ATTEMPTS"], [701, 34, 735, 32], [701, 36, 735, 34], [702, 8, 736, 6, "console"], [702, 15, 736, 13], [702, 16, 736, 14, "error"], [702, 21, 736, 19], [702, 22, 736, 20], [702, 75, 736, 73], [702, 76, 736, 74], [703, 8, 737, 6, "setErrorMessage"], [703, 23, 737, 21], [703, 24, 737, 22], [703, 63, 737, 61], [703, 64, 737, 62], [704, 8, 738, 6, "setProcessingState"], [704, 26, 738, 24], [704, 27, 738, 25], [704, 34, 738, 32], [704, 35, 738, 33], [705, 8, 739, 6], [706, 6, 740, 4], [707, 6, 742, 4], [707, 10, 742, 8], [708, 8, 743, 6], [708, 14, 743, 12, "response"], [708, 22, 743, 20], [708, 25, 743, 23], [708, 31, 743, 29, "fetch"], [708, 36, 743, 34], [708, 37, 743, 35], [708, 40, 743, 38, "API_BASE_URL"], [708, 52, 743, 50], [708, 75, 743, 73, "jobId"], [708, 80, 743, 78], [708, 82, 743, 80], [708, 84, 743, 82], [709, 10, 744, 8, "headers"], [709, 17, 744, 15], [709, 19, 744, 17], [710, 12, 745, 10], [710, 27, 745, 25], [710, 29, 745, 27], [710, 39, 745, 37], [710, 45, 745, 43, "getAuthToken"], [710, 57, 745, 55], [710, 58, 745, 56], [710, 59, 745, 57], [711, 10, 746, 8], [712, 8, 747, 6], [712, 9, 747, 7], [712, 10, 747, 8], [713, 8, 749, 6], [713, 12, 749, 10], [713, 13, 749, 11, "response"], [713, 21, 749, 19], [713, 22, 749, 20, "ok"], [713, 24, 749, 22], [713, 26, 749, 24], [714, 10, 750, 8], [714, 16, 750, 14], [714, 20, 750, 18, "Error"], [714, 25, 750, 23], [714, 26, 750, 24], [714, 34, 750, 32, "response"], [714, 42, 750, 40], [714, 43, 750, 41, "status"], [714, 49, 750, 47], [714, 54, 750, 52, "response"], [714, 62, 750, 60], [714, 63, 750, 61, "statusText"], [714, 73, 750, 71], [714, 75, 750, 73], [714, 76, 750, 74], [715, 8, 751, 6], [716, 8, 753, 6], [716, 14, 753, 12, "status"], [716, 20, 753, 18], [716, 23, 753, 21], [716, 29, 753, 27, "response"], [716, 37, 753, 35], [716, 38, 753, 36, "json"], [716, 42, 753, 40], [716, 43, 753, 41], [716, 44, 753, 42], [717, 8, 754, 6, "console"], [717, 15, 754, 13], [717, 16, 754, 14, "log"], [717, 19, 754, 17], [717, 20, 754, 18], [717, 54, 754, 52], [717, 56, 754, 54, "status"], [717, 62, 754, 60], [717, 63, 754, 61], [718, 8, 756, 6], [718, 12, 756, 10, "status"], [718, 18, 756, 16], [718, 19, 756, 17, "status"], [718, 25, 756, 23], [718, 30, 756, 28], [718, 41, 756, 39], [718, 43, 756, 41], [719, 10, 757, 8, "console"], [719, 17, 757, 15], [719, 18, 757, 16, "log"], [719, 21, 757, 19], [719, 22, 757, 20], [719, 73, 757, 71], [719, 74, 757, 72], [720, 10, 758, 8, "setProcessingProgress"], [720, 31, 758, 29], [720, 32, 758, 30], [720, 35, 758, 33], [720, 36, 758, 34], [721, 10, 759, 8, "setProcessingState"], [721, 28, 759, 26], [721, 29, 759, 27], [721, 40, 759, 38], [721, 41, 759, 39], [722, 10, 760, 8], [723, 10, 761, 8], [723, 16, 761, 14, "result"], [723, 22, 761, 20], [723, 25, 761, 23], [724, 12, 762, 10, "imageUrl"], [724, 20, 762, 18], [724, 22, 762, 20, "status"], [724, 28, 762, 26], [724, 29, 762, 27, "publicUrl"], [724, 38, 762, 36], [725, 12, 762, 38], [726, 12, 763, 10, "localUri"], [726, 20, 763, 18], [726, 22, 763, 20, "capturedPhoto"], [726, 35, 763, 33], [726, 39, 763, 37, "status"], [726, 45, 763, 43], [726, 46, 763, 44, "publicUrl"], [726, 55, 763, 53], [727, 12, 763, 55], [728, 12, 764, 10, "challengeCode"], [728, 25, 764, 23], [728, 27, 764, 25, "challengeCode"], [728, 40, 764, 38], [728, 44, 764, 42], [728, 46, 764, 44], [729, 12, 765, 10, "timestamp"], [729, 21, 765, 19], [730, 12, 766, 10, "processingStatus"], [730, 28, 766, 26], [730, 30, 766, 28], [731, 10, 767, 8], [731, 11, 767, 9], [732, 10, 768, 8, "console"], [732, 17, 768, 15], [732, 18, 768, 16, "log"], [732, 21, 768, 19], [732, 22, 768, 20], [732, 57, 768, 55], [732, 59, 768, 57, "result"], [732, 65, 768, 63], [732, 66, 768, 64], [733, 10, 769, 8, "onComplete"], [733, 20, 769, 18], [733, 21, 769, 19, "result"], [733, 27, 769, 25], [733, 28, 769, 26], [734, 10, 770, 8], [735, 8, 771, 6], [735, 9, 771, 7], [735, 15, 771, 13], [735, 19, 771, 17, "status"], [735, 25, 771, 23], [735, 26, 771, 24, "status"], [735, 32, 771, 30], [735, 37, 771, 35], [735, 45, 771, 43], [735, 47, 771, 45], [736, 10, 772, 8, "console"], [736, 17, 772, 15], [736, 18, 772, 16, "error"], [736, 23, 772, 21], [736, 24, 772, 22], [736, 60, 772, 58], [736, 62, 772, 60, "status"], [736, 68, 772, 66], [736, 69, 772, 67, "error"], [736, 74, 772, 72], [736, 75, 772, 73], [737, 10, 773, 8], [737, 16, 773, 14], [737, 20, 773, 18, "Error"], [737, 25, 773, 23], [737, 26, 773, 24, "status"], [737, 32, 773, 30], [737, 33, 773, 31, "error"], [737, 38, 773, 36], [737, 42, 773, 40], [737, 61, 773, 59], [737, 62, 773, 60], [738, 8, 774, 6], [738, 9, 774, 7], [738, 15, 774, 13], [739, 10, 775, 8], [740, 10, 776, 8], [740, 16, 776, 14, "progressValue"], [740, 29, 776, 27], [740, 32, 776, 30], [740, 34, 776, 32], [740, 37, 776, 36, "attempts"], [740, 45, 776, 44], [740, 48, 776, 47, "MAX_ATTEMPTS"], [740, 60, 776, 59], [740, 63, 776, 63], [740, 65, 776, 65], [741, 10, 777, 8, "console"], [741, 17, 777, 15], [741, 18, 777, 16, "log"], [741, 21, 777, 19], [741, 22, 777, 20], [741, 71, 777, 69, "progressValue"], [741, 84, 777, 82], [741, 87, 777, 85], [741, 88, 777, 86], [742, 10, 778, 8, "setProcessingProgress"], [742, 31, 778, 29], [742, 32, 778, 30, "progressValue"], [742, 45, 778, 43], [742, 46, 778, 44], [743, 10, 780, 8, "setTimeout"], [743, 20, 780, 18], [743, 21, 780, 19], [743, 27, 780, 25], [744, 12, 781, 10, "pollForCompletion"], [744, 29, 781, 27], [744, 30, 781, 28, "jobId"], [744, 35, 781, 33], [744, 37, 781, 35, "timestamp"], [744, 46, 781, 44], [744, 48, 781, 46, "attempts"], [744, 56, 781, 54], [744, 59, 781, 57], [744, 60, 781, 58], [744, 61, 781, 59], [745, 10, 782, 8], [745, 11, 782, 9], [745, 13, 782, 11, "POLL_INTERVAL"], [745, 26, 782, 24], [745, 27, 782, 25], [746, 8, 783, 6], [747, 6, 784, 4], [747, 7, 784, 5], [747, 8, 784, 6], [747, 15, 784, 13, "error"], [747, 20, 784, 18], [747, 22, 784, 20], [748, 8, 785, 6, "console"], [748, 15, 785, 13], [748, 16, 785, 14, "error"], [748, 21, 785, 19], [748, 22, 785, 20], [748, 54, 785, 52], [748, 56, 785, 54, "error"], [748, 61, 785, 59], [748, 62, 785, 60], [749, 8, 786, 6, "setErrorMessage"], [749, 23, 786, 21], [749, 24, 786, 22], [749, 62, 786, 60, "error"], [749, 67, 786, 65], [749, 68, 786, 66, "message"], [749, 75, 786, 73], [749, 77, 786, 75], [749, 78, 786, 76], [750, 8, 787, 6, "setProcessingState"], [750, 26, 787, 24], [750, 27, 787, 25], [750, 34, 787, 32], [750, 35, 787, 33], [751, 6, 788, 4], [752, 4, 789, 2], [752, 5, 789, 3], [753, 4, 790, 2], [754, 4, 791, 2], [754, 10, 791, 8, "getAuthToken"], [754, 22, 791, 20], [754, 25, 791, 23], [754, 31, 791, 23, "getAuthToken"], [754, 32, 791, 23], [754, 37, 791, 52], [755, 6, 792, 4], [756, 6, 793, 4], [757, 6, 794, 4], [757, 13, 794, 11], [757, 30, 794, 28], [758, 4, 795, 2], [758, 5, 795, 3], [760, 4, 797, 2], [761, 4, 798, 2], [761, 10, 798, 8, "retryCapture"], [761, 22, 798, 20], [761, 25, 798, 23], [761, 29, 798, 23, "useCallback"], [761, 47, 798, 34], [761, 49, 798, 35], [761, 55, 798, 41], [762, 6, 799, 4, "console"], [762, 13, 799, 11], [762, 14, 799, 12, "log"], [762, 17, 799, 15], [762, 18, 799, 16], [762, 55, 799, 53], [762, 56, 799, 54], [763, 6, 800, 4, "setProcessingState"], [763, 24, 800, 22], [763, 25, 800, 23], [763, 31, 800, 29], [763, 32, 800, 30], [764, 6, 801, 4, "setErrorMessage"], [764, 21, 801, 19], [764, 22, 801, 20], [764, 24, 801, 22], [764, 25, 801, 23], [765, 6, 802, 4, "setCapturedPhoto"], [765, 22, 802, 20], [765, 23, 802, 21], [765, 25, 802, 23], [765, 26, 802, 24], [766, 6, 803, 4, "setProcessingProgress"], [766, 27, 803, 25], [766, 28, 803, 26], [766, 29, 803, 27], [766, 30, 803, 28], [767, 4, 804, 2], [767, 5, 804, 3], [767, 7, 804, 5], [767, 9, 804, 7], [767, 10, 804, 8], [768, 4, 805, 2], [769, 4, 806, 2], [769, 8, 806, 2, "useEffect"], [769, 24, 806, 11], [769, 26, 806, 12], [769, 32, 806, 18], [770, 6, 807, 4, "console"], [770, 13, 807, 11], [770, 14, 807, 12, "log"], [770, 17, 807, 15], [770, 18, 807, 16], [770, 53, 807, 51], [770, 55, 807, 53, "permission"], [770, 65, 807, 63], [770, 66, 807, 64], [771, 6, 808, 4], [771, 10, 808, 8, "permission"], [771, 20, 808, 18], [771, 22, 808, 20], [772, 8, 809, 6, "console"], [772, 15, 809, 13], [772, 16, 809, 14, "log"], [772, 19, 809, 17], [772, 20, 809, 18], [772, 57, 809, 55], [772, 59, 809, 57, "permission"], [772, 69, 809, 67], [772, 70, 809, 68, "granted"], [772, 77, 809, 75], [772, 78, 809, 76], [773, 6, 810, 4], [774, 4, 811, 2], [774, 5, 811, 3], [774, 7, 811, 5], [774, 8, 811, 6, "permission"], [774, 18, 811, 16], [774, 19, 811, 17], [774, 20, 811, 18], [775, 4, 812, 2], [776, 4, 813, 2], [776, 8, 813, 6], [776, 9, 813, 7, "permission"], [776, 19, 813, 17], [776, 21, 813, 19], [777, 6, 814, 4, "console"], [777, 13, 814, 11], [777, 14, 814, 12, "log"], [777, 17, 814, 15], [777, 18, 814, 16], [777, 67, 814, 65], [777, 68, 814, 66], [778, 6, 815, 4], [778, 26, 816, 6], [778, 30, 816, 6, "_jsxDevRuntime"], [778, 44, 816, 6], [778, 45, 816, 6, "jsxDEV"], [778, 51, 816, 6], [778, 53, 816, 7, "_View"], [778, 58, 816, 7], [778, 59, 816, 7, "default"], [778, 66, 816, 11], [779, 8, 816, 12, "style"], [779, 13, 816, 17], [779, 15, 816, 19, "styles"], [779, 21, 816, 25], [779, 22, 816, 26, "container"], [779, 31, 816, 36], [780, 8, 816, 36, "children"], [780, 16, 816, 36], [780, 32, 817, 8], [780, 36, 817, 8, "_jsxDevRuntime"], [780, 50, 817, 8], [780, 51, 817, 8, "jsxDEV"], [780, 57, 817, 8], [780, 59, 817, 9, "_ActivityIndicator"], [780, 77, 817, 9], [780, 78, 817, 9, "default"], [780, 85, 817, 26], [781, 10, 817, 27, "size"], [781, 14, 817, 31], [781, 16, 817, 32], [781, 23, 817, 39], [782, 10, 817, 40, "color"], [782, 15, 817, 45], [782, 17, 817, 46], [783, 8, 817, 55], [784, 10, 817, 55, "fileName"], [784, 18, 817, 55], [784, 20, 817, 55, "_jsxFileName"], [784, 32, 817, 55], [785, 10, 817, 55, "lineNumber"], [785, 20, 817, 55], [786, 10, 817, 55, "columnNumber"], [786, 22, 817, 55], [787, 8, 817, 55], [787, 15, 817, 57], [787, 16, 817, 58], [787, 31, 818, 8], [787, 35, 818, 8, "_jsxDevRuntime"], [787, 49, 818, 8], [787, 50, 818, 8, "jsxDEV"], [787, 56, 818, 8], [787, 58, 818, 9, "_Text"], [787, 63, 818, 9], [787, 64, 818, 9, "default"], [787, 71, 818, 13], [788, 10, 818, 14, "style"], [788, 15, 818, 19], [788, 17, 818, 21, "styles"], [788, 23, 818, 27], [788, 24, 818, 28, "loadingText"], [788, 35, 818, 40], [789, 10, 818, 40, "children"], [789, 18, 818, 40], [789, 20, 818, 41], [790, 8, 818, 58], [791, 10, 818, 58, "fileName"], [791, 18, 818, 58], [791, 20, 818, 58, "_jsxFileName"], [791, 32, 818, 58], [792, 10, 818, 58, "lineNumber"], [792, 20, 818, 58], [793, 10, 818, 58, "columnNumber"], [793, 22, 818, 58], [794, 8, 818, 58], [794, 15, 818, 64], [794, 16, 818, 65], [795, 6, 818, 65], [796, 8, 818, 65, "fileName"], [796, 16, 818, 65], [796, 18, 818, 65, "_jsxFileName"], [796, 30, 818, 65], [797, 8, 818, 65, "lineNumber"], [797, 18, 818, 65], [798, 8, 818, 65, "columnNumber"], [798, 20, 818, 65], [799, 6, 818, 65], [799, 13, 819, 12], [799, 14, 819, 13], [800, 4, 821, 2], [801, 4, 822, 2], [801, 8, 822, 6], [801, 9, 822, 7, "permission"], [801, 19, 822, 17], [801, 20, 822, 18, "granted"], [801, 27, 822, 25], [801, 29, 822, 27], [802, 6, 823, 4, "console"], [802, 13, 823, 11], [802, 14, 823, 12, "log"], [802, 17, 823, 15], [802, 18, 823, 16], [802, 93, 823, 91], [802, 94, 823, 92], [803, 6, 824, 4], [803, 26, 825, 6], [803, 30, 825, 6, "_jsxDevRuntime"], [803, 44, 825, 6], [803, 45, 825, 6, "jsxDEV"], [803, 51, 825, 6], [803, 53, 825, 7, "_View"], [803, 58, 825, 7], [803, 59, 825, 7, "default"], [803, 66, 825, 11], [804, 8, 825, 12, "style"], [804, 13, 825, 17], [804, 15, 825, 19, "styles"], [804, 21, 825, 25], [804, 22, 825, 26, "container"], [804, 31, 825, 36], [805, 8, 825, 36, "children"], [805, 16, 825, 36], [805, 31, 826, 8], [805, 35, 826, 8, "_jsxDevRuntime"], [805, 49, 826, 8], [805, 50, 826, 8, "jsxDEV"], [805, 56, 826, 8], [805, 58, 826, 9, "_View"], [805, 63, 826, 9], [805, 64, 826, 9, "default"], [805, 71, 826, 13], [806, 10, 826, 14, "style"], [806, 15, 826, 19], [806, 17, 826, 21, "styles"], [806, 23, 826, 27], [806, 24, 826, 28, "permissionContent"], [806, 41, 826, 46], [807, 10, 826, 46, "children"], [807, 18, 826, 46], [807, 34, 827, 10], [807, 38, 827, 10, "_jsxDevRuntime"], [807, 52, 827, 10], [807, 53, 827, 10, "jsxDEV"], [807, 59, 827, 10], [807, 61, 827, 11, "_lucideReactNative"], [807, 79, 827, 11], [807, 80, 827, 11, "Camera"], [807, 86, 827, 21], [808, 12, 827, 22, "size"], [808, 16, 827, 26], [808, 18, 827, 28], [808, 20, 827, 31], [809, 12, 827, 32, "color"], [809, 17, 827, 37], [809, 19, 827, 38], [810, 10, 827, 47], [811, 12, 827, 47, "fileName"], [811, 20, 827, 47], [811, 22, 827, 47, "_jsxFileName"], [811, 34, 827, 47], [812, 12, 827, 47, "lineNumber"], [812, 22, 827, 47], [813, 12, 827, 47, "columnNumber"], [813, 24, 827, 47], [814, 10, 827, 47], [814, 17, 827, 49], [814, 18, 827, 50], [814, 33, 828, 10], [814, 37, 828, 10, "_jsxDevRuntime"], [814, 51, 828, 10], [814, 52, 828, 10, "jsxDEV"], [814, 58, 828, 10], [814, 60, 828, 11, "_Text"], [814, 65, 828, 11], [814, 66, 828, 11, "default"], [814, 73, 828, 15], [815, 12, 828, 16, "style"], [815, 17, 828, 21], [815, 19, 828, 23, "styles"], [815, 25, 828, 29], [815, 26, 828, 30, "permissionTitle"], [815, 41, 828, 46], [816, 12, 828, 46, "children"], [816, 20, 828, 46], [816, 22, 828, 47], [817, 10, 828, 73], [818, 12, 828, 73, "fileName"], [818, 20, 828, 73], [818, 22, 828, 73, "_jsxFileName"], [818, 34, 828, 73], [819, 12, 828, 73, "lineNumber"], [819, 22, 828, 73], [820, 12, 828, 73, "columnNumber"], [820, 24, 828, 73], [821, 10, 828, 73], [821, 17, 828, 79], [821, 18, 828, 80], [821, 33, 829, 10], [821, 37, 829, 10, "_jsxDevRuntime"], [821, 51, 829, 10], [821, 52, 829, 10, "jsxDEV"], [821, 58, 829, 10], [821, 60, 829, 11, "_Text"], [821, 65, 829, 11], [821, 66, 829, 11, "default"], [821, 73, 829, 15], [822, 12, 829, 16, "style"], [822, 17, 829, 21], [822, 19, 829, 23, "styles"], [822, 25, 829, 29], [822, 26, 829, 30, "permissionDescription"], [822, 47, 829, 52], [823, 12, 829, 52, "children"], [823, 20, 829, 52], [823, 22, 829, 53], [824, 10, 832, 10], [825, 12, 832, 10, "fileName"], [825, 20, 832, 10], [825, 22, 832, 10, "_jsxFileName"], [825, 34, 832, 10], [826, 12, 832, 10, "lineNumber"], [826, 22, 832, 10], [827, 12, 832, 10, "columnNumber"], [827, 24, 832, 10], [828, 10, 832, 10], [828, 17, 832, 16], [828, 18, 832, 17], [828, 33, 833, 10], [828, 37, 833, 10, "_jsxDevRuntime"], [828, 51, 833, 10], [828, 52, 833, 10, "jsxDEV"], [828, 58, 833, 10], [828, 60, 833, 11, "_TouchableOpacity"], [828, 77, 833, 11], [828, 78, 833, 11, "default"], [828, 85, 833, 27], [829, 12, 833, 28, "onPress"], [829, 19, 833, 35], [829, 21, 833, 37, "requestPermission"], [829, 38, 833, 55], [830, 12, 833, 56, "style"], [830, 17, 833, 61], [830, 19, 833, 63, "styles"], [830, 25, 833, 69], [830, 26, 833, 70, "primaryButton"], [830, 39, 833, 84], [831, 12, 833, 84, "children"], [831, 20, 833, 84], [831, 35, 834, 12], [831, 39, 834, 12, "_jsxDevRuntime"], [831, 53, 834, 12], [831, 54, 834, 12, "jsxDEV"], [831, 60, 834, 12], [831, 62, 834, 13, "_Text"], [831, 67, 834, 13], [831, 68, 834, 13, "default"], [831, 75, 834, 17], [832, 14, 834, 18, "style"], [832, 19, 834, 23], [832, 21, 834, 25, "styles"], [832, 27, 834, 31], [832, 28, 834, 32, "primaryButtonText"], [832, 45, 834, 50], [833, 14, 834, 50, "children"], [833, 22, 834, 50], [833, 24, 834, 51], [834, 12, 834, 67], [835, 14, 834, 67, "fileName"], [835, 22, 834, 67], [835, 24, 834, 67, "_jsxFileName"], [835, 36, 834, 67], [836, 14, 834, 67, "lineNumber"], [836, 24, 834, 67], [837, 14, 834, 67, "columnNumber"], [837, 26, 834, 67], [838, 12, 834, 67], [838, 19, 834, 73], [839, 10, 834, 74], [840, 12, 834, 74, "fileName"], [840, 20, 834, 74], [840, 22, 834, 74, "_jsxFileName"], [840, 34, 834, 74], [841, 12, 834, 74, "lineNumber"], [841, 22, 834, 74], [842, 12, 834, 74, "columnNumber"], [842, 24, 834, 74], [843, 10, 834, 74], [843, 17, 835, 28], [843, 18, 835, 29], [843, 33, 836, 10], [843, 37, 836, 10, "_jsxDevRuntime"], [843, 51, 836, 10], [843, 52, 836, 10, "jsxDEV"], [843, 58, 836, 10], [843, 60, 836, 11, "_TouchableOpacity"], [843, 77, 836, 11], [843, 78, 836, 11, "default"], [843, 85, 836, 27], [844, 12, 836, 28, "onPress"], [844, 19, 836, 35], [844, 21, 836, 37, "onCancel"], [844, 29, 836, 46], [845, 12, 836, 47, "style"], [845, 17, 836, 52], [845, 19, 836, 54, "styles"], [845, 25, 836, 60], [845, 26, 836, 61, "secondaryButton"], [845, 41, 836, 77], [846, 12, 836, 77, "children"], [846, 20, 836, 77], [846, 35, 837, 12], [846, 39, 837, 12, "_jsxDevRuntime"], [846, 53, 837, 12], [846, 54, 837, 12, "jsxDEV"], [846, 60, 837, 12], [846, 62, 837, 13, "_Text"], [846, 67, 837, 13], [846, 68, 837, 13, "default"], [846, 75, 837, 17], [847, 14, 837, 18, "style"], [847, 19, 837, 23], [847, 21, 837, 25, "styles"], [847, 27, 837, 31], [847, 28, 837, 32, "secondaryButtonText"], [847, 47, 837, 52], [848, 14, 837, 52, "children"], [848, 22, 837, 52], [848, 24, 837, 53], [849, 12, 837, 59], [850, 14, 837, 59, "fileName"], [850, 22, 837, 59], [850, 24, 837, 59, "_jsxFileName"], [850, 36, 837, 59], [851, 14, 837, 59, "lineNumber"], [851, 24, 837, 59], [852, 14, 837, 59, "columnNumber"], [852, 26, 837, 59], [853, 12, 837, 59], [853, 19, 837, 65], [854, 10, 837, 66], [855, 12, 837, 66, "fileName"], [855, 20, 837, 66], [855, 22, 837, 66, "_jsxFileName"], [855, 34, 837, 66], [856, 12, 837, 66, "lineNumber"], [856, 22, 837, 66], [857, 12, 837, 66, "columnNumber"], [857, 24, 837, 66], [858, 10, 837, 66], [858, 17, 838, 28], [858, 18, 838, 29], [859, 8, 838, 29], [860, 10, 838, 29, "fileName"], [860, 18, 838, 29], [860, 20, 838, 29, "_jsxFileName"], [860, 32, 838, 29], [861, 10, 838, 29, "lineNumber"], [861, 20, 838, 29], [862, 10, 838, 29, "columnNumber"], [862, 22, 838, 29], [863, 8, 838, 29], [863, 15, 839, 14], [864, 6, 839, 15], [865, 8, 839, 15, "fileName"], [865, 16, 839, 15], [865, 18, 839, 15, "_jsxFileName"], [865, 30, 839, 15], [866, 8, 839, 15, "lineNumber"], [866, 18, 839, 15], [867, 8, 839, 15, "columnNumber"], [867, 20, 839, 15], [868, 6, 839, 15], [868, 13, 840, 12], [868, 14, 840, 13], [869, 4, 842, 2], [870, 4, 843, 2], [871, 4, 844, 2, "console"], [871, 11, 844, 9], [871, 12, 844, 10, "log"], [871, 15, 844, 13], [871, 16, 844, 14], [871, 55, 844, 53], [871, 56, 844, 54], [872, 4, 846, 2], [872, 24, 847, 4], [872, 28, 847, 4, "_jsxDevRuntime"], [872, 42, 847, 4], [872, 43, 847, 4, "jsxDEV"], [872, 49, 847, 4], [872, 51, 847, 5, "_View"], [872, 56, 847, 5], [872, 57, 847, 5, "default"], [872, 64, 847, 9], [873, 6, 847, 10, "style"], [873, 11, 847, 15], [873, 13, 847, 17, "styles"], [873, 19, 847, 23], [873, 20, 847, 24, "container"], [873, 29, 847, 34], [874, 6, 847, 34, "children"], [874, 14, 847, 34], [874, 30, 849, 6], [874, 34, 849, 6, "_jsxDevRuntime"], [874, 48, 849, 6], [874, 49, 849, 6, "jsxDEV"], [874, 55, 849, 6], [874, 57, 849, 7, "_View"], [874, 62, 849, 7], [874, 63, 849, 7, "default"], [874, 70, 849, 11], [875, 8, 849, 12, "style"], [875, 13, 849, 17], [875, 15, 849, 19, "styles"], [875, 21, 849, 25], [875, 22, 849, 26, "cameraContainer"], [875, 37, 849, 42], [876, 8, 849, 43, "id"], [876, 10, 849, 45], [876, 12, 849, 46], [876, 29, 849, 63], [877, 8, 849, 63, "children"], [877, 16, 849, 63], [877, 32, 850, 8], [877, 36, 850, 8, "_jsxDevRuntime"], [877, 50, 850, 8], [877, 51, 850, 8, "jsxDEV"], [877, 57, 850, 8], [877, 59, 850, 9, "_expoCamera"], [877, 70, 850, 9], [877, 71, 850, 9, "CameraView"], [877, 81, 850, 19], [878, 10, 851, 10, "ref"], [878, 13, 851, 13], [878, 15, 851, 15, "cameraRef"], [878, 24, 851, 25], [879, 10, 852, 10, "style"], [879, 15, 852, 15], [879, 17, 852, 17], [879, 18, 852, 18, "styles"], [879, 24, 852, 24], [879, 25, 852, 25, "camera"], [879, 31, 852, 31], [879, 33, 852, 33], [880, 12, 852, 35, "backgroundColor"], [880, 27, 852, 50], [880, 29, 852, 52], [881, 10, 852, 62], [881, 11, 852, 63], [881, 12, 852, 65], [882, 10, 853, 10, "facing"], [882, 16, 853, 16], [882, 18, 853, 17], [882, 24, 853, 23], [883, 10, 854, 10, "onLayout"], [883, 18, 854, 18], [883, 20, 854, 21, "e"], [883, 21, 854, 22], [883, 25, 854, 27], [884, 12, 855, 12, "console"], [884, 19, 855, 19], [884, 20, 855, 20, "log"], [884, 23, 855, 23], [884, 24, 855, 24], [884, 56, 855, 56], [884, 58, 855, 58, "e"], [884, 59, 855, 59], [884, 60, 855, 60, "nativeEvent"], [884, 71, 855, 71], [884, 72, 855, 72, "layout"], [884, 78, 855, 78], [884, 79, 855, 79], [885, 12, 856, 12, "setViewSize"], [885, 23, 856, 23], [885, 24, 856, 24], [886, 14, 856, 26, "width"], [886, 19, 856, 31], [886, 21, 856, 33, "e"], [886, 22, 856, 34], [886, 23, 856, 35, "nativeEvent"], [886, 34, 856, 46], [886, 35, 856, 47, "layout"], [886, 41, 856, 53], [886, 42, 856, 54, "width"], [886, 47, 856, 59], [887, 14, 856, 61, "height"], [887, 20, 856, 67], [887, 22, 856, 69, "e"], [887, 23, 856, 70], [887, 24, 856, 71, "nativeEvent"], [887, 35, 856, 82], [887, 36, 856, 83, "layout"], [887, 42, 856, 89], [887, 43, 856, 90, "height"], [888, 12, 856, 97], [888, 13, 856, 98], [888, 14, 856, 99], [889, 10, 857, 10], [889, 11, 857, 12], [890, 10, 858, 10, "onCameraReady"], [890, 23, 858, 23], [890, 25, 858, 25, "onCameraReady"], [890, 26, 858, 25], [890, 31, 858, 31], [891, 12, 859, 12, "console"], [891, 19, 859, 19], [891, 20, 859, 20, "log"], [891, 23, 859, 23], [891, 24, 859, 24], [891, 55, 859, 55], [891, 56, 859, 56], [892, 12, 860, 12, "setIsCameraReady"], [892, 28, 860, 28], [892, 29, 860, 29], [892, 33, 860, 33], [892, 34, 860, 34], [892, 35, 860, 35], [892, 36, 860, 36], [893, 10, 861, 10], [893, 11, 861, 12], [894, 10, 862, 10, "onMountError"], [894, 22, 862, 22], [894, 24, 862, 25, "error"], [894, 29, 862, 30], [894, 33, 862, 35], [895, 12, 863, 12, "console"], [895, 19, 863, 19], [895, 20, 863, 20, "error"], [895, 25, 863, 25], [895, 26, 863, 26], [895, 63, 863, 63], [895, 65, 863, 65, "error"], [895, 70, 863, 70], [895, 71, 863, 71], [896, 12, 864, 12, "setErrorMessage"], [896, 27, 864, 27], [896, 28, 864, 28], [896, 57, 864, 57], [896, 58, 864, 58], [897, 12, 865, 12, "setProcessingState"], [897, 30, 865, 30], [897, 31, 865, 31], [897, 38, 865, 38], [897, 39, 865, 39], [898, 10, 866, 10], [899, 8, 866, 12], [900, 10, 866, 12, "fileName"], [900, 18, 866, 12], [900, 20, 866, 12, "_jsxFileName"], [900, 32, 866, 12], [901, 10, 866, 12, "lineNumber"], [901, 20, 866, 12], [902, 10, 866, 12, "columnNumber"], [902, 22, 866, 12], [903, 8, 866, 12], [903, 15, 867, 9], [903, 16, 867, 10], [903, 18, 869, 9], [903, 19, 869, 10, "isCameraReady"], [903, 32, 869, 23], [903, 49, 870, 10], [903, 53, 870, 10, "_jsxDevRuntime"], [903, 67, 870, 10], [903, 68, 870, 10, "jsxDEV"], [903, 74, 870, 10], [903, 76, 870, 11, "_View"], [903, 81, 870, 11], [903, 82, 870, 11, "default"], [903, 89, 870, 15], [904, 10, 870, 16, "style"], [904, 15, 870, 21], [904, 17, 870, 23], [904, 18, 870, 24, "StyleSheet"], [904, 37, 870, 34], [904, 38, 870, 35, "absoluteFill"], [904, 50, 870, 47], [904, 52, 870, 49], [905, 12, 870, 51, "backgroundColor"], [905, 27, 870, 66], [905, 29, 870, 68], [905, 49, 870, 88], [906, 12, 870, 90, "justifyContent"], [906, 26, 870, 104], [906, 28, 870, 106], [906, 36, 870, 114], [907, 12, 870, 116, "alignItems"], [907, 22, 870, 126], [907, 24, 870, 128], [907, 32, 870, 136], [908, 12, 870, 138, "zIndex"], [908, 18, 870, 144], [908, 20, 870, 146], [909, 10, 870, 151], [909, 11, 870, 152], [909, 12, 870, 154], [910, 10, 870, 154, "children"], [910, 18, 870, 154], [910, 33, 871, 12], [910, 37, 871, 12, "_jsxDevRuntime"], [910, 51, 871, 12], [910, 52, 871, 12, "jsxDEV"], [910, 58, 871, 12], [910, 60, 871, 13, "_View"], [910, 65, 871, 13], [910, 66, 871, 13, "default"], [910, 73, 871, 17], [911, 12, 871, 18, "style"], [911, 17, 871, 23], [911, 19, 871, 25], [912, 14, 871, 27, "backgroundColor"], [912, 29, 871, 42], [912, 31, 871, 44], [912, 51, 871, 64], [913, 14, 871, 66, "padding"], [913, 21, 871, 73], [913, 23, 871, 75], [913, 25, 871, 77], [914, 14, 871, 79, "borderRadius"], [914, 26, 871, 91], [914, 28, 871, 93], [914, 30, 871, 95], [915, 14, 871, 97, "alignItems"], [915, 24, 871, 107], [915, 26, 871, 109], [916, 12, 871, 118], [916, 13, 871, 120], [917, 12, 871, 120, "children"], [917, 20, 871, 120], [917, 36, 872, 14], [917, 40, 872, 14, "_jsxDevRuntime"], [917, 54, 872, 14], [917, 55, 872, 14, "jsxDEV"], [917, 61, 872, 14], [917, 63, 872, 15, "_ActivityIndicator"], [917, 81, 872, 15], [917, 82, 872, 15, "default"], [917, 89, 872, 32], [918, 14, 872, 33, "size"], [918, 18, 872, 37], [918, 20, 872, 38], [918, 27, 872, 45], [919, 14, 872, 46, "color"], [919, 19, 872, 51], [919, 21, 872, 52], [919, 30, 872, 61], [920, 14, 872, 62, "style"], [920, 19, 872, 67], [920, 21, 872, 69], [921, 16, 872, 71, "marginBottom"], [921, 28, 872, 83], [921, 30, 872, 85], [922, 14, 872, 88], [923, 12, 872, 90], [924, 14, 872, 90, "fileName"], [924, 22, 872, 90], [924, 24, 872, 90, "_jsxFileName"], [924, 36, 872, 90], [925, 14, 872, 90, "lineNumber"], [925, 24, 872, 90], [926, 14, 872, 90, "columnNumber"], [926, 26, 872, 90], [927, 12, 872, 90], [927, 19, 872, 92], [927, 20, 872, 93], [927, 35, 873, 14], [927, 39, 873, 14, "_jsxDevRuntime"], [927, 53, 873, 14], [927, 54, 873, 14, "jsxDEV"], [927, 60, 873, 14], [927, 62, 873, 15, "_Text"], [927, 67, 873, 15], [927, 68, 873, 15, "default"], [927, 75, 873, 19], [928, 14, 873, 20, "style"], [928, 19, 873, 25], [928, 21, 873, 27], [929, 16, 873, 29, "color"], [929, 21, 873, 34], [929, 23, 873, 36], [929, 29, 873, 42], [930, 16, 873, 44, "fontSize"], [930, 24, 873, 52], [930, 26, 873, 54], [930, 28, 873, 56], [931, 16, 873, 58, "fontWeight"], [931, 26, 873, 68], [931, 28, 873, 70], [932, 14, 873, 76], [932, 15, 873, 78], [933, 14, 873, 78, "children"], [933, 22, 873, 78], [933, 24, 873, 79], [934, 12, 873, 101], [935, 14, 873, 101, "fileName"], [935, 22, 873, 101], [935, 24, 873, 101, "_jsxFileName"], [935, 36, 873, 101], [936, 14, 873, 101, "lineNumber"], [936, 24, 873, 101], [937, 14, 873, 101, "columnNumber"], [937, 26, 873, 101], [938, 12, 873, 101], [938, 19, 873, 107], [938, 20, 873, 108], [938, 35, 874, 14], [938, 39, 874, 14, "_jsxDevRuntime"], [938, 53, 874, 14], [938, 54, 874, 14, "jsxDEV"], [938, 60, 874, 14], [938, 62, 874, 15, "_Text"], [938, 67, 874, 15], [938, 68, 874, 15, "default"], [938, 75, 874, 19], [939, 14, 874, 20, "style"], [939, 19, 874, 25], [939, 21, 874, 27], [940, 16, 874, 29, "color"], [940, 21, 874, 34], [940, 23, 874, 36], [940, 32, 874, 45], [941, 16, 874, 47, "fontSize"], [941, 24, 874, 55], [941, 26, 874, 57], [941, 28, 874, 59], [942, 16, 874, 61, "marginTop"], [942, 25, 874, 70], [942, 27, 874, 72], [943, 14, 874, 74], [943, 15, 874, 76], [944, 14, 874, 76, "children"], [944, 22, 874, 76], [944, 24, 874, 77], [945, 12, 874, 88], [946, 14, 874, 88, "fileName"], [946, 22, 874, 88], [946, 24, 874, 88, "_jsxFileName"], [946, 36, 874, 88], [947, 14, 874, 88, "lineNumber"], [947, 24, 874, 88], [948, 14, 874, 88, "columnNumber"], [948, 26, 874, 88], [949, 12, 874, 88], [949, 19, 874, 94], [949, 20, 874, 95], [950, 10, 874, 95], [951, 12, 874, 95, "fileName"], [951, 20, 874, 95], [951, 22, 874, 95, "_jsxFileName"], [951, 34, 874, 95], [952, 12, 874, 95, "lineNumber"], [952, 22, 874, 95], [953, 12, 874, 95, "columnNumber"], [953, 24, 874, 95], [954, 10, 874, 95], [954, 17, 875, 18], [955, 8, 875, 19], [956, 10, 875, 19, "fileName"], [956, 18, 875, 19], [956, 20, 875, 19, "_jsxFileName"], [956, 32, 875, 19], [957, 10, 875, 19, "lineNumber"], [957, 20, 875, 19], [958, 10, 875, 19, "columnNumber"], [958, 22, 875, 19], [959, 8, 875, 19], [959, 15, 876, 16], [959, 16, 877, 9], [959, 18, 880, 9, "isCameraReady"], [959, 31, 880, 22], [959, 35, 880, 26, "previewBlurEnabled"], [959, 53, 880, 44], [959, 57, 880, 48, "viewSize"], [959, 65, 880, 56], [959, 66, 880, 57, "width"], [959, 71, 880, 62], [959, 74, 880, 65], [959, 75, 880, 66], [959, 92, 881, 10], [959, 96, 881, 10, "_jsxDevRuntime"], [959, 110, 881, 10], [959, 111, 881, 10, "jsxDEV"], [959, 117, 881, 10], [959, 119, 881, 10, "_jsxDevRuntime"], [959, 133, 881, 10], [959, 134, 881, 10, "Fragment"], [959, 142, 881, 10], [960, 10, 881, 10, "children"], [960, 18, 881, 10], [960, 34, 883, 12], [960, 38, 883, 12, "_jsxDevRuntime"], [960, 52, 883, 12], [960, 53, 883, 12, "jsxDEV"], [960, 59, 883, 12], [960, 61, 883, 13, "_LiveFaceCanvas"], [960, 76, 883, 13], [960, 77, 883, 13, "default"], [960, 84, 883, 27], [961, 12, 883, 28, "containerId"], [961, 23, 883, 39], [961, 25, 883, 40], [961, 42, 883, 57], [962, 12, 883, 58, "width"], [962, 17, 883, 63], [962, 19, 883, 65, "viewSize"], [962, 27, 883, 73], [962, 28, 883, 74, "width"], [962, 33, 883, 80], [963, 12, 883, 81, "height"], [963, 18, 883, 87], [963, 20, 883, 89, "viewSize"], [963, 28, 883, 97], [963, 29, 883, 98, "height"], [964, 10, 883, 105], [965, 12, 883, 105, "fileName"], [965, 20, 883, 105], [965, 22, 883, 105, "_jsxFileName"], [965, 34, 883, 105], [966, 12, 883, 105, "lineNumber"], [966, 22, 883, 105], [967, 12, 883, 105, "columnNumber"], [967, 24, 883, 105], [968, 10, 883, 105], [968, 17, 883, 107], [968, 18, 883, 108], [968, 33, 884, 12], [968, 37, 884, 12, "_jsxDevRuntime"], [968, 51, 884, 12], [968, 52, 884, 12, "jsxDEV"], [968, 58, 884, 12], [968, 60, 884, 13, "_View"], [968, 65, 884, 13], [968, 66, 884, 13, "default"], [968, 73, 884, 17], [969, 12, 884, 18, "style"], [969, 17, 884, 23], [969, 19, 884, 25], [969, 20, 884, 26, "StyleSheet"], [969, 39, 884, 36], [969, 40, 884, 37, "absoluteFill"], [969, 52, 884, 49], [969, 54, 884, 51], [970, 14, 884, 53, "pointerEvents"], [970, 27, 884, 66], [970, 29, 884, 68], [971, 12, 884, 75], [971, 13, 884, 76], [971, 14, 884, 78], [972, 12, 884, 78, "children"], [972, 20, 884, 78], [972, 36, 886, 12], [972, 40, 886, 12, "_jsxDevRuntime"], [972, 54, 886, 12], [972, 55, 886, 12, "jsxDEV"], [972, 61, 886, 12], [972, 63, 886, 13, "_expoBlur"], [972, 72, 886, 13], [972, 73, 886, 13, "BlurView"], [972, 81, 886, 21], [973, 14, 886, 22, "intensity"], [973, 23, 886, 31], [973, 25, 886, 33], [973, 27, 886, 36], [974, 14, 886, 37, "tint"], [974, 18, 886, 41], [974, 20, 886, 42], [974, 26, 886, 48], [975, 14, 886, 49, "style"], [975, 19, 886, 54], [975, 21, 886, 56], [975, 22, 886, 57, "styles"], [975, 28, 886, 63], [975, 29, 886, 64, "blurZone"], [975, 37, 886, 72], [975, 39, 886, 74], [976, 16, 887, 14, "left"], [976, 20, 887, 18], [976, 22, 887, 20], [976, 23, 887, 21], [977, 16, 888, 14, "top"], [977, 19, 888, 17], [977, 21, 888, 19, "viewSize"], [977, 29, 888, 27], [977, 30, 888, 28, "height"], [977, 36, 888, 34], [977, 39, 888, 37], [977, 42, 888, 40], [978, 16, 889, 14, "width"], [978, 21, 889, 19], [978, 23, 889, 21, "viewSize"], [978, 31, 889, 29], [978, 32, 889, 30, "width"], [978, 37, 889, 35], [979, 16, 890, 14, "height"], [979, 22, 890, 20], [979, 24, 890, 22, "viewSize"], [979, 32, 890, 30], [979, 33, 890, 31, "height"], [979, 39, 890, 37], [979, 42, 890, 40], [979, 46, 890, 44], [980, 16, 891, 14, "borderRadius"], [980, 28, 891, 26], [980, 30, 891, 28], [981, 14, 892, 12], [981, 15, 892, 13], [982, 12, 892, 15], [983, 14, 892, 15, "fileName"], [983, 22, 892, 15], [983, 24, 892, 15, "_jsxFileName"], [983, 36, 892, 15], [984, 14, 892, 15, "lineNumber"], [984, 24, 892, 15], [985, 14, 892, 15, "columnNumber"], [985, 26, 892, 15], [986, 12, 892, 15], [986, 19, 892, 17], [986, 20, 892, 18], [986, 35, 894, 12], [986, 39, 894, 12, "_jsxDevRuntime"], [986, 53, 894, 12], [986, 54, 894, 12, "jsxDEV"], [986, 60, 894, 12], [986, 62, 894, 13, "_expoBlur"], [986, 71, 894, 13], [986, 72, 894, 13, "BlurView"], [986, 80, 894, 21], [987, 14, 894, 22, "intensity"], [987, 23, 894, 31], [987, 25, 894, 33], [987, 27, 894, 36], [988, 14, 894, 37, "tint"], [988, 18, 894, 41], [988, 20, 894, 42], [988, 26, 894, 48], [989, 14, 894, 49, "style"], [989, 19, 894, 54], [989, 21, 894, 56], [989, 22, 894, 57, "styles"], [989, 28, 894, 63], [989, 29, 894, 64, "blurZone"], [989, 37, 894, 72], [989, 39, 894, 74], [990, 16, 895, 14, "left"], [990, 20, 895, 18], [990, 22, 895, 20], [990, 23, 895, 21], [991, 16, 896, 14, "top"], [991, 19, 896, 17], [991, 21, 896, 19], [991, 22, 896, 20], [992, 16, 897, 14, "width"], [992, 21, 897, 19], [992, 23, 897, 21, "viewSize"], [992, 31, 897, 29], [992, 32, 897, 30, "width"], [992, 37, 897, 35], [993, 16, 898, 14, "height"], [993, 22, 898, 20], [993, 24, 898, 22, "viewSize"], [993, 32, 898, 30], [993, 33, 898, 31, "height"], [993, 39, 898, 37], [993, 42, 898, 40], [993, 45, 898, 43], [994, 16, 899, 14, "borderRadius"], [994, 28, 899, 26], [994, 30, 899, 28], [995, 14, 900, 12], [995, 15, 900, 13], [996, 12, 900, 15], [997, 14, 900, 15, "fileName"], [997, 22, 900, 15], [997, 24, 900, 15, "_jsxFileName"], [997, 36, 900, 15], [998, 14, 900, 15, "lineNumber"], [998, 24, 900, 15], [999, 14, 900, 15, "columnNumber"], [999, 26, 900, 15], [1000, 12, 900, 15], [1000, 19, 900, 17], [1000, 20, 900, 18], [1000, 35, 902, 12], [1000, 39, 902, 12, "_jsxDevRuntime"], [1000, 53, 902, 12], [1000, 54, 902, 12, "jsxDEV"], [1000, 60, 902, 12], [1000, 62, 902, 13, "_expoBlur"], [1000, 71, 902, 13], [1000, 72, 902, 13, "BlurView"], [1000, 80, 902, 21], [1001, 14, 902, 22, "intensity"], [1001, 23, 902, 31], [1001, 25, 902, 33], [1001, 27, 902, 36], [1002, 14, 902, 37, "tint"], [1002, 18, 902, 41], [1002, 20, 902, 42], [1002, 26, 902, 48], [1003, 14, 902, 49, "style"], [1003, 19, 902, 54], [1003, 21, 902, 56], [1003, 22, 902, 57, "styles"], [1003, 28, 902, 63], [1003, 29, 902, 64, "blurZone"], [1003, 37, 902, 72], [1003, 39, 902, 74], [1004, 16, 903, 14, "left"], [1004, 20, 903, 18], [1004, 22, 903, 20, "viewSize"], [1004, 30, 903, 28], [1004, 31, 903, 29, "width"], [1004, 36, 903, 34], [1004, 39, 903, 37], [1004, 42, 903, 40], [1004, 45, 903, 44, "viewSize"], [1004, 53, 903, 52], [1004, 54, 903, 53, "width"], [1004, 59, 903, 58], [1004, 62, 903, 61], [1004, 66, 903, 66], [1005, 16, 904, 14, "top"], [1005, 19, 904, 17], [1005, 21, 904, 19, "viewSize"], [1005, 29, 904, 27], [1005, 30, 904, 28, "height"], [1005, 36, 904, 34], [1005, 39, 904, 37], [1005, 43, 904, 41], [1005, 46, 904, 45, "viewSize"], [1005, 54, 904, 53], [1005, 55, 904, 54, "width"], [1005, 60, 904, 59], [1005, 63, 904, 62], [1005, 67, 904, 67], [1006, 16, 905, 14, "width"], [1006, 21, 905, 19], [1006, 23, 905, 21, "viewSize"], [1006, 31, 905, 29], [1006, 32, 905, 30, "width"], [1006, 37, 905, 35], [1006, 40, 905, 38], [1006, 43, 905, 41], [1007, 16, 906, 14, "height"], [1007, 22, 906, 20], [1007, 24, 906, 22, "viewSize"], [1007, 32, 906, 30], [1007, 33, 906, 31, "width"], [1007, 38, 906, 36], [1007, 41, 906, 39], [1007, 44, 906, 42], [1008, 16, 907, 14, "borderRadius"], [1008, 28, 907, 26], [1008, 30, 907, 29, "viewSize"], [1008, 38, 907, 37], [1008, 39, 907, 38, "width"], [1008, 44, 907, 43], [1008, 47, 907, 46], [1008, 50, 907, 49], [1008, 53, 907, 53], [1009, 14, 908, 12], [1009, 15, 908, 13], [1010, 12, 908, 15], [1011, 14, 908, 15, "fileName"], [1011, 22, 908, 15], [1011, 24, 908, 15, "_jsxFileName"], [1011, 36, 908, 15], [1012, 14, 908, 15, "lineNumber"], [1012, 24, 908, 15], [1013, 14, 908, 15, "columnNumber"], [1013, 26, 908, 15], [1014, 12, 908, 15], [1014, 19, 908, 17], [1014, 20, 908, 18], [1014, 35, 909, 12], [1014, 39, 909, 12, "_jsxDevRuntime"], [1014, 53, 909, 12], [1014, 54, 909, 12, "jsxDEV"], [1014, 60, 909, 12], [1014, 62, 909, 13, "_expoBlur"], [1014, 71, 909, 13], [1014, 72, 909, 13, "BlurView"], [1014, 80, 909, 21], [1015, 14, 909, 22, "intensity"], [1015, 23, 909, 31], [1015, 25, 909, 33], [1015, 27, 909, 36], [1016, 14, 909, 37, "tint"], [1016, 18, 909, 41], [1016, 20, 909, 42], [1016, 26, 909, 48], [1017, 14, 909, 49, "style"], [1017, 19, 909, 54], [1017, 21, 909, 56], [1017, 22, 909, 57, "styles"], [1017, 28, 909, 63], [1017, 29, 909, 64, "blurZone"], [1017, 37, 909, 72], [1017, 39, 909, 74], [1018, 16, 910, 14, "left"], [1018, 20, 910, 18], [1018, 22, 910, 20, "viewSize"], [1018, 30, 910, 28], [1018, 31, 910, 29, "width"], [1018, 36, 910, 34], [1018, 39, 910, 37], [1018, 42, 910, 40], [1018, 45, 910, 44, "viewSize"], [1018, 53, 910, 52], [1018, 54, 910, 53, "width"], [1018, 59, 910, 58], [1018, 62, 910, 61], [1018, 66, 910, 66], [1019, 16, 911, 14, "top"], [1019, 19, 911, 17], [1019, 21, 911, 19, "viewSize"], [1019, 29, 911, 27], [1019, 30, 911, 28, "height"], [1019, 36, 911, 34], [1019, 39, 911, 37], [1019, 42, 911, 40], [1019, 45, 911, 44, "viewSize"], [1019, 53, 911, 52], [1019, 54, 911, 53, "width"], [1019, 59, 911, 58], [1019, 62, 911, 61], [1019, 66, 911, 66], [1020, 16, 912, 14, "width"], [1020, 21, 912, 19], [1020, 23, 912, 21, "viewSize"], [1020, 31, 912, 29], [1020, 32, 912, 30, "width"], [1020, 37, 912, 35], [1020, 40, 912, 38], [1020, 43, 912, 41], [1021, 16, 913, 14, "height"], [1021, 22, 913, 20], [1021, 24, 913, 22, "viewSize"], [1021, 32, 913, 30], [1021, 33, 913, 31, "width"], [1021, 38, 913, 36], [1021, 41, 913, 39], [1021, 44, 913, 42], [1022, 16, 914, 14, "borderRadius"], [1022, 28, 914, 26], [1022, 30, 914, 29, "viewSize"], [1022, 38, 914, 37], [1022, 39, 914, 38, "width"], [1022, 44, 914, 43], [1022, 47, 914, 46], [1022, 50, 914, 49], [1022, 53, 914, 53], [1023, 14, 915, 12], [1023, 15, 915, 13], [1024, 12, 915, 15], [1025, 14, 915, 15, "fileName"], [1025, 22, 915, 15], [1025, 24, 915, 15, "_jsxFileName"], [1025, 36, 915, 15], [1026, 14, 915, 15, "lineNumber"], [1026, 24, 915, 15], [1027, 14, 915, 15, "columnNumber"], [1027, 26, 915, 15], [1028, 12, 915, 15], [1028, 19, 915, 17], [1028, 20, 915, 18], [1028, 35, 916, 12], [1028, 39, 916, 12, "_jsxDevRuntime"], [1028, 53, 916, 12], [1028, 54, 916, 12, "jsxDEV"], [1028, 60, 916, 12], [1028, 62, 916, 13, "_expoBlur"], [1028, 71, 916, 13], [1028, 72, 916, 13, "BlurView"], [1028, 80, 916, 21], [1029, 14, 916, 22, "intensity"], [1029, 23, 916, 31], [1029, 25, 916, 33], [1029, 27, 916, 36], [1030, 14, 916, 37, "tint"], [1030, 18, 916, 41], [1030, 20, 916, 42], [1030, 26, 916, 48], [1031, 14, 916, 49, "style"], [1031, 19, 916, 54], [1031, 21, 916, 56], [1031, 22, 916, 57, "styles"], [1031, 28, 916, 63], [1031, 29, 916, 64, "blurZone"], [1031, 37, 916, 72], [1031, 39, 916, 74], [1032, 16, 917, 14, "left"], [1032, 20, 917, 18], [1032, 22, 917, 20, "viewSize"], [1032, 30, 917, 28], [1032, 31, 917, 29, "width"], [1032, 36, 917, 34], [1032, 39, 917, 37], [1032, 42, 917, 40], [1032, 45, 917, 44, "viewSize"], [1032, 53, 917, 52], [1032, 54, 917, 53, "width"], [1032, 59, 917, 58], [1032, 62, 917, 61], [1032, 66, 917, 66], [1033, 16, 918, 14, "top"], [1033, 19, 918, 17], [1033, 21, 918, 19, "viewSize"], [1033, 29, 918, 27], [1033, 30, 918, 28, "height"], [1033, 36, 918, 34], [1033, 39, 918, 37], [1033, 42, 918, 40], [1033, 45, 918, 44, "viewSize"], [1033, 53, 918, 52], [1033, 54, 918, 53, "width"], [1033, 59, 918, 58], [1033, 62, 918, 61], [1033, 66, 918, 66], [1034, 16, 919, 14, "width"], [1034, 21, 919, 19], [1034, 23, 919, 21, "viewSize"], [1034, 31, 919, 29], [1034, 32, 919, 30, "width"], [1034, 37, 919, 35], [1034, 40, 919, 38], [1034, 43, 919, 41], [1035, 16, 920, 14, "height"], [1035, 22, 920, 20], [1035, 24, 920, 22, "viewSize"], [1035, 32, 920, 30], [1035, 33, 920, 31, "width"], [1035, 38, 920, 36], [1035, 41, 920, 39], [1035, 44, 920, 42], [1036, 16, 921, 14, "borderRadius"], [1036, 28, 921, 26], [1036, 30, 921, 29, "viewSize"], [1036, 38, 921, 37], [1036, 39, 921, 38, "width"], [1036, 44, 921, 43], [1036, 47, 921, 46], [1036, 50, 921, 49], [1036, 53, 921, 53], [1037, 14, 922, 12], [1037, 15, 922, 13], [1038, 12, 922, 15], [1039, 14, 922, 15, "fileName"], [1039, 22, 922, 15], [1039, 24, 922, 15, "_jsxFileName"], [1039, 36, 922, 15], [1040, 14, 922, 15, "lineNumber"], [1040, 24, 922, 15], [1041, 14, 922, 15, "columnNumber"], [1041, 26, 922, 15], [1042, 12, 922, 15], [1042, 19, 922, 17], [1042, 20, 922, 18], [1042, 22, 924, 13, "__DEV__"], [1042, 29, 924, 20], [1042, 46, 925, 14], [1042, 50, 925, 14, "_jsxDevRuntime"], [1042, 64, 925, 14], [1042, 65, 925, 14, "jsxDEV"], [1042, 71, 925, 14], [1042, 73, 925, 15, "_View"], [1042, 78, 925, 15], [1042, 79, 925, 15, "default"], [1042, 86, 925, 19], [1043, 14, 925, 20, "style"], [1043, 19, 925, 25], [1043, 21, 925, 27, "styles"], [1043, 27, 925, 33], [1043, 28, 925, 34, "previewChip"], [1043, 39, 925, 46], [1044, 14, 925, 46, "children"], [1044, 22, 925, 46], [1044, 37, 926, 16], [1044, 41, 926, 16, "_jsxDevRuntime"], [1044, 55, 926, 16], [1044, 56, 926, 16, "jsxDEV"], [1044, 62, 926, 16], [1044, 64, 926, 17, "_Text"], [1044, 69, 926, 17], [1044, 70, 926, 17, "default"], [1044, 77, 926, 21], [1045, 16, 926, 22, "style"], [1045, 21, 926, 27], [1045, 23, 926, 29, "styles"], [1045, 29, 926, 35], [1045, 30, 926, 36, "previewChipText"], [1045, 45, 926, 52], [1046, 16, 926, 52, "children"], [1046, 24, 926, 52], [1046, 26, 926, 53], [1047, 14, 926, 73], [1048, 16, 926, 73, "fileName"], [1048, 24, 926, 73], [1048, 26, 926, 73, "_jsxFileName"], [1048, 38, 926, 73], [1049, 16, 926, 73, "lineNumber"], [1049, 26, 926, 73], [1050, 16, 926, 73, "columnNumber"], [1050, 28, 926, 73], [1051, 14, 926, 73], [1051, 21, 926, 79], [1052, 12, 926, 80], [1053, 14, 926, 80, "fileName"], [1053, 22, 926, 80], [1053, 24, 926, 80, "_jsxFileName"], [1053, 36, 926, 80], [1054, 14, 926, 80, "lineNumber"], [1054, 24, 926, 80], [1055, 14, 926, 80, "columnNumber"], [1055, 26, 926, 80], [1056, 12, 926, 80], [1056, 19, 927, 20], [1056, 20, 928, 13], [1057, 10, 928, 13], [1058, 12, 928, 13, "fileName"], [1058, 20, 928, 13], [1058, 22, 928, 13, "_jsxFileName"], [1058, 34, 928, 13], [1059, 12, 928, 13, "lineNumber"], [1059, 22, 928, 13], [1060, 12, 928, 13, "columnNumber"], [1060, 24, 928, 13], [1061, 10, 928, 13], [1061, 17, 929, 18], [1061, 18, 929, 19], [1062, 8, 929, 19], [1062, 23, 930, 12], [1062, 24, 931, 9], [1062, 26, 933, 9, "isCameraReady"], [1062, 39, 933, 22], [1062, 56, 934, 10], [1062, 60, 934, 10, "_jsxDevRuntime"], [1062, 74, 934, 10], [1062, 75, 934, 10, "jsxDEV"], [1062, 81, 934, 10], [1062, 83, 934, 10, "_jsxDevRuntime"], [1062, 97, 934, 10], [1062, 98, 934, 10, "Fragment"], [1062, 106, 934, 10], [1063, 10, 934, 10, "children"], [1063, 18, 934, 10], [1063, 34, 936, 12], [1063, 38, 936, 12, "_jsxDevRuntime"], [1063, 52, 936, 12], [1063, 53, 936, 12, "jsxDEV"], [1063, 59, 936, 12], [1063, 61, 936, 13, "_View"], [1063, 66, 936, 13], [1063, 67, 936, 13, "default"], [1063, 74, 936, 17], [1064, 12, 936, 18, "style"], [1064, 17, 936, 23], [1064, 19, 936, 25, "styles"], [1064, 25, 936, 31], [1064, 26, 936, 32, "headerOverlay"], [1064, 39, 936, 46], [1065, 12, 936, 46, "children"], [1065, 20, 936, 46], [1065, 35, 937, 14], [1065, 39, 937, 14, "_jsxDevRuntime"], [1065, 53, 937, 14], [1065, 54, 937, 14, "jsxDEV"], [1065, 60, 937, 14], [1065, 62, 937, 15, "_View"], [1065, 67, 937, 15], [1065, 68, 937, 15, "default"], [1065, 75, 937, 19], [1066, 14, 937, 20, "style"], [1066, 19, 937, 25], [1066, 21, 937, 27, "styles"], [1066, 27, 937, 33], [1066, 28, 937, 34, "headerContent"], [1066, 41, 937, 48], [1067, 14, 937, 48, "children"], [1067, 22, 937, 48], [1067, 38, 938, 16], [1067, 42, 938, 16, "_jsxDevRuntime"], [1067, 56, 938, 16], [1067, 57, 938, 16, "jsxDEV"], [1067, 63, 938, 16], [1067, 65, 938, 17, "_View"], [1067, 70, 938, 17], [1067, 71, 938, 17, "default"], [1067, 78, 938, 21], [1068, 16, 938, 22, "style"], [1068, 21, 938, 27], [1068, 23, 938, 29, "styles"], [1068, 29, 938, 35], [1068, 30, 938, 36, "headerLeft"], [1068, 40, 938, 47], [1069, 16, 938, 47, "children"], [1069, 24, 938, 47], [1069, 40, 939, 18], [1069, 44, 939, 18, "_jsxDevRuntime"], [1069, 58, 939, 18], [1069, 59, 939, 18, "jsxDEV"], [1069, 65, 939, 18], [1069, 67, 939, 19, "_Text"], [1069, 72, 939, 19], [1069, 73, 939, 19, "default"], [1069, 80, 939, 23], [1070, 18, 939, 24, "style"], [1070, 23, 939, 29], [1070, 25, 939, 31, "styles"], [1070, 31, 939, 37], [1070, 32, 939, 38, "headerTitle"], [1070, 43, 939, 50], [1071, 18, 939, 50, "children"], [1071, 26, 939, 50], [1071, 28, 939, 51], [1072, 16, 939, 62], [1073, 18, 939, 62, "fileName"], [1073, 26, 939, 62], [1073, 28, 939, 62, "_jsxFileName"], [1073, 40, 939, 62], [1074, 18, 939, 62, "lineNumber"], [1074, 28, 939, 62], [1075, 18, 939, 62, "columnNumber"], [1075, 30, 939, 62], [1076, 16, 939, 62], [1076, 23, 939, 68], [1076, 24, 939, 69], [1076, 39, 940, 18], [1076, 43, 940, 18, "_jsxDevRuntime"], [1076, 57, 940, 18], [1076, 58, 940, 18, "jsxDEV"], [1076, 64, 940, 18], [1076, 66, 940, 19, "_View"], [1076, 71, 940, 19], [1076, 72, 940, 19, "default"], [1076, 79, 940, 23], [1077, 18, 940, 24, "style"], [1077, 23, 940, 29], [1077, 25, 940, 31, "styles"], [1077, 31, 940, 37], [1077, 32, 940, 38, "subtitleRow"], [1077, 43, 940, 50], [1078, 18, 940, 50, "children"], [1078, 26, 940, 50], [1078, 42, 941, 20], [1078, 46, 941, 20, "_jsxDevRuntime"], [1078, 60, 941, 20], [1078, 61, 941, 20, "jsxDEV"], [1078, 67, 941, 20], [1078, 69, 941, 21, "_Text"], [1078, 74, 941, 21], [1078, 75, 941, 21, "default"], [1078, 82, 941, 25], [1079, 20, 941, 26, "style"], [1079, 25, 941, 31], [1079, 27, 941, 33, "styles"], [1079, 33, 941, 39], [1079, 34, 941, 40, "webIcon"], [1079, 41, 941, 48], [1080, 20, 941, 48, "children"], [1080, 28, 941, 48], [1080, 30, 941, 49], [1081, 18, 941, 51], [1082, 20, 941, 51, "fileName"], [1082, 28, 941, 51], [1082, 30, 941, 51, "_jsxFileName"], [1082, 42, 941, 51], [1083, 20, 941, 51, "lineNumber"], [1083, 30, 941, 51], [1084, 20, 941, 51, "columnNumber"], [1084, 32, 941, 51], [1085, 18, 941, 51], [1085, 25, 941, 57], [1085, 26, 941, 58], [1085, 41, 942, 20], [1085, 45, 942, 20, "_jsxDevRuntime"], [1085, 59, 942, 20], [1085, 60, 942, 20, "jsxDEV"], [1085, 66, 942, 20], [1085, 68, 942, 21, "_Text"], [1085, 73, 942, 21], [1085, 74, 942, 21, "default"], [1085, 81, 942, 25], [1086, 20, 942, 26, "style"], [1086, 25, 942, 31], [1086, 27, 942, 33, "styles"], [1086, 33, 942, 39], [1086, 34, 942, 40, "headerSubtitle"], [1086, 48, 942, 55], [1087, 20, 942, 55, "children"], [1087, 28, 942, 55], [1087, 30, 942, 56], [1088, 18, 942, 71], [1089, 20, 942, 71, "fileName"], [1089, 28, 942, 71], [1089, 30, 942, 71, "_jsxFileName"], [1089, 42, 942, 71], [1090, 20, 942, 71, "lineNumber"], [1090, 30, 942, 71], [1091, 20, 942, 71, "columnNumber"], [1091, 32, 942, 71], [1092, 18, 942, 71], [1092, 25, 942, 77], [1092, 26, 942, 78], [1093, 16, 942, 78], [1094, 18, 942, 78, "fileName"], [1094, 26, 942, 78], [1094, 28, 942, 78, "_jsxFileName"], [1094, 40, 942, 78], [1095, 18, 942, 78, "lineNumber"], [1095, 28, 942, 78], [1096, 18, 942, 78, "columnNumber"], [1096, 30, 942, 78], [1097, 16, 942, 78], [1097, 23, 943, 24], [1097, 24, 943, 25], [1097, 26, 944, 19, "challengeCode"], [1097, 39, 944, 32], [1097, 56, 945, 20], [1097, 60, 945, 20, "_jsxDevRuntime"], [1097, 74, 945, 20], [1097, 75, 945, 20, "jsxDEV"], [1097, 81, 945, 20], [1097, 83, 945, 21, "_View"], [1097, 88, 945, 21], [1097, 89, 945, 21, "default"], [1097, 96, 945, 25], [1098, 18, 945, 26, "style"], [1098, 23, 945, 31], [1098, 25, 945, 33, "styles"], [1098, 31, 945, 39], [1098, 32, 945, 40, "challengeRow"], [1098, 44, 945, 53], [1099, 18, 945, 53, "children"], [1099, 26, 945, 53], [1099, 42, 946, 22], [1099, 46, 946, 22, "_jsxDevRuntime"], [1099, 60, 946, 22], [1099, 61, 946, 22, "jsxDEV"], [1099, 67, 946, 22], [1099, 69, 946, 23, "_lucideReactNative"], [1099, 87, 946, 23], [1099, 88, 946, 23, "Shield"], [1099, 94, 946, 29], [1100, 20, 946, 30, "size"], [1100, 24, 946, 34], [1100, 26, 946, 36], [1100, 28, 946, 39], [1101, 20, 946, 40, "color"], [1101, 25, 946, 45], [1101, 27, 946, 46], [1102, 18, 946, 52], [1103, 20, 946, 52, "fileName"], [1103, 28, 946, 52], [1103, 30, 946, 52, "_jsxFileName"], [1103, 42, 946, 52], [1104, 20, 946, 52, "lineNumber"], [1104, 30, 946, 52], [1105, 20, 946, 52, "columnNumber"], [1105, 32, 946, 52], [1106, 18, 946, 52], [1106, 25, 946, 54], [1106, 26, 946, 55], [1106, 41, 947, 22], [1106, 45, 947, 22, "_jsxDevRuntime"], [1106, 59, 947, 22], [1106, 60, 947, 22, "jsxDEV"], [1106, 66, 947, 22], [1106, 68, 947, 23, "_Text"], [1106, 73, 947, 23], [1106, 74, 947, 23, "default"], [1106, 81, 947, 27], [1107, 20, 947, 28, "style"], [1107, 25, 947, 33], [1107, 27, 947, 35, "styles"], [1107, 33, 947, 41], [1107, 34, 947, 42, "challengeCode"], [1107, 47, 947, 56], [1108, 20, 947, 56, "children"], [1108, 28, 947, 56], [1108, 30, 947, 58, "challengeCode"], [1109, 18, 947, 71], [1110, 20, 947, 71, "fileName"], [1110, 28, 947, 71], [1110, 30, 947, 71, "_jsxFileName"], [1110, 42, 947, 71], [1111, 20, 947, 71, "lineNumber"], [1111, 30, 947, 71], [1112, 20, 947, 71, "columnNumber"], [1112, 32, 947, 71], [1113, 18, 947, 71], [1113, 25, 947, 78], [1113, 26, 947, 79], [1114, 16, 947, 79], [1115, 18, 947, 79, "fileName"], [1115, 26, 947, 79], [1115, 28, 947, 79, "_jsxFileName"], [1115, 40, 947, 79], [1116, 18, 947, 79, "lineNumber"], [1116, 28, 947, 79], [1117, 18, 947, 79, "columnNumber"], [1117, 30, 947, 79], [1118, 16, 947, 79], [1118, 23, 948, 26], [1118, 24, 949, 19], [1119, 14, 949, 19], [1120, 16, 949, 19, "fileName"], [1120, 24, 949, 19], [1120, 26, 949, 19, "_jsxFileName"], [1120, 38, 949, 19], [1121, 16, 949, 19, "lineNumber"], [1121, 26, 949, 19], [1122, 16, 949, 19, "columnNumber"], [1122, 28, 949, 19], [1123, 14, 949, 19], [1123, 21, 950, 22], [1123, 22, 950, 23], [1123, 37, 951, 16], [1123, 41, 951, 16, "_jsxDevRuntime"], [1123, 55, 951, 16], [1123, 56, 951, 16, "jsxDEV"], [1123, 62, 951, 16], [1123, 64, 951, 17, "_TouchableOpacity"], [1123, 81, 951, 17], [1123, 82, 951, 17, "default"], [1123, 89, 951, 33], [1124, 16, 951, 34, "onPress"], [1124, 23, 951, 41], [1124, 25, 951, 43, "onCancel"], [1124, 33, 951, 52], [1125, 16, 951, 53, "style"], [1125, 21, 951, 58], [1125, 23, 951, 60, "styles"], [1125, 29, 951, 66], [1125, 30, 951, 67, "closeButton"], [1125, 41, 951, 79], [1126, 16, 951, 79, "children"], [1126, 24, 951, 79], [1126, 39, 952, 18], [1126, 43, 952, 18, "_jsxDevRuntime"], [1126, 57, 952, 18], [1126, 58, 952, 18, "jsxDEV"], [1126, 64, 952, 18], [1126, 66, 952, 19, "_lucideReactNative"], [1126, 84, 952, 19], [1126, 85, 952, 19, "X"], [1126, 86, 952, 20], [1127, 18, 952, 21, "size"], [1127, 22, 952, 25], [1127, 24, 952, 27], [1127, 26, 952, 30], [1128, 18, 952, 31, "color"], [1128, 23, 952, 36], [1128, 25, 952, 37], [1129, 16, 952, 43], [1130, 18, 952, 43, "fileName"], [1130, 26, 952, 43], [1130, 28, 952, 43, "_jsxFileName"], [1130, 40, 952, 43], [1131, 18, 952, 43, "lineNumber"], [1131, 28, 952, 43], [1132, 18, 952, 43, "columnNumber"], [1132, 30, 952, 43], [1133, 16, 952, 43], [1133, 23, 952, 45], [1134, 14, 952, 46], [1135, 16, 952, 46, "fileName"], [1135, 24, 952, 46], [1135, 26, 952, 46, "_jsxFileName"], [1135, 38, 952, 46], [1136, 16, 952, 46, "lineNumber"], [1136, 26, 952, 46], [1137, 16, 952, 46, "columnNumber"], [1137, 28, 952, 46], [1138, 14, 952, 46], [1138, 21, 953, 34], [1138, 22, 953, 35], [1139, 12, 953, 35], [1140, 14, 953, 35, "fileName"], [1140, 22, 953, 35], [1140, 24, 953, 35, "_jsxFileName"], [1140, 36, 953, 35], [1141, 14, 953, 35, "lineNumber"], [1141, 24, 953, 35], [1142, 14, 953, 35, "columnNumber"], [1142, 26, 953, 35], [1143, 12, 953, 35], [1143, 19, 954, 20], [1144, 10, 954, 21], [1145, 12, 954, 21, "fileName"], [1145, 20, 954, 21], [1145, 22, 954, 21, "_jsxFileName"], [1145, 34, 954, 21], [1146, 12, 954, 21, "lineNumber"], [1146, 22, 954, 21], [1147, 12, 954, 21, "columnNumber"], [1147, 24, 954, 21], [1148, 10, 954, 21], [1148, 17, 955, 18], [1148, 18, 955, 19], [1148, 33, 957, 12], [1148, 37, 957, 12, "_jsxDevRuntime"], [1148, 51, 957, 12], [1148, 52, 957, 12, "jsxDEV"], [1148, 58, 957, 12], [1148, 60, 957, 13, "_View"], [1148, 65, 957, 13], [1148, 66, 957, 13, "default"], [1148, 73, 957, 17], [1149, 12, 957, 18, "style"], [1149, 17, 957, 23], [1149, 19, 957, 25, "styles"], [1149, 25, 957, 31], [1149, 26, 957, 32, "privacyNotice"], [1149, 39, 957, 46], [1150, 12, 957, 46, "children"], [1150, 20, 957, 46], [1150, 36, 958, 14], [1150, 40, 958, 14, "_jsxDevRuntime"], [1150, 54, 958, 14], [1150, 55, 958, 14, "jsxDEV"], [1150, 61, 958, 14], [1150, 63, 958, 15, "_lucideReactNative"], [1150, 81, 958, 15], [1150, 82, 958, 15, "Shield"], [1150, 88, 958, 21], [1151, 14, 958, 22, "size"], [1151, 18, 958, 26], [1151, 20, 958, 28], [1151, 22, 958, 31], [1152, 14, 958, 32, "color"], [1152, 19, 958, 37], [1152, 21, 958, 38], [1153, 12, 958, 47], [1154, 14, 958, 47, "fileName"], [1154, 22, 958, 47], [1154, 24, 958, 47, "_jsxFileName"], [1154, 36, 958, 47], [1155, 14, 958, 47, "lineNumber"], [1155, 24, 958, 47], [1156, 14, 958, 47, "columnNumber"], [1156, 26, 958, 47], [1157, 12, 958, 47], [1157, 19, 958, 49], [1157, 20, 958, 50], [1157, 35, 959, 14], [1157, 39, 959, 14, "_jsxDevRuntime"], [1157, 53, 959, 14], [1157, 54, 959, 14, "jsxDEV"], [1157, 60, 959, 14], [1157, 62, 959, 15, "_Text"], [1157, 67, 959, 15], [1157, 68, 959, 15, "default"], [1157, 75, 959, 19], [1158, 14, 959, 20, "style"], [1158, 19, 959, 25], [1158, 21, 959, 27, "styles"], [1158, 27, 959, 33], [1158, 28, 959, 34, "privacyText"], [1158, 39, 959, 46], [1159, 14, 959, 46, "children"], [1159, 22, 959, 46], [1159, 24, 959, 47], [1160, 12, 961, 14], [1161, 14, 961, 14, "fileName"], [1161, 22, 961, 14], [1161, 24, 961, 14, "_jsxFileName"], [1161, 36, 961, 14], [1162, 14, 961, 14, "lineNumber"], [1162, 24, 961, 14], [1163, 14, 961, 14, "columnNumber"], [1163, 26, 961, 14], [1164, 12, 961, 14], [1164, 19, 961, 20], [1164, 20, 961, 21], [1165, 10, 961, 21], [1166, 12, 961, 21, "fileName"], [1166, 20, 961, 21], [1166, 22, 961, 21, "_jsxFileName"], [1166, 34, 961, 21], [1167, 12, 961, 21, "lineNumber"], [1167, 22, 961, 21], [1168, 12, 961, 21, "columnNumber"], [1168, 24, 961, 21], [1169, 10, 961, 21], [1169, 17, 962, 18], [1169, 18, 962, 19], [1169, 33, 964, 12], [1169, 37, 964, 12, "_jsxDevRuntime"], [1169, 51, 964, 12], [1169, 52, 964, 12, "jsxDEV"], [1169, 58, 964, 12], [1169, 60, 964, 13, "_View"], [1169, 65, 964, 13], [1169, 66, 964, 13, "default"], [1169, 73, 964, 17], [1170, 12, 964, 18, "style"], [1170, 17, 964, 23], [1170, 19, 964, 25, "styles"], [1170, 25, 964, 31], [1170, 26, 964, 32, "footer<PERSON><PERSON><PERSON>"], [1170, 39, 964, 46], [1171, 12, 964, 46, "children"], [1171, 20, 964, 46], [1171, 36, 965, 14], [1171, 40, 965, 14, "_jsxDevRuntime"], [1171, 54, 965, 14], [1171, 55, 965, 14, "jsxDEV"], [1171, 61, 965, 14], [1171, 63, 965, 15, "_Text"], [1171, 68, 965, 15], [1171, 69, 965, 15, "default"], [1171, 76, 965, 19], [1172, 14, 965, 20, "style"], [1172, 19, 965, 25], [1172, 21, 965, 27, "styles"], [1172, 27, 965, 33], [1172, 28, 965, 34, "instruction"], [1172, 39, 965, 46], [1173, 14, 965, 46, "children"], [1173, 22, 965, 46], [1173, 24, 965, 47], [1174, 12, 967, 14], [1175, 14, 967, 14, "fileName"], [1175, 22, 967, 14], [1175, 24, 967, 14, "_jsxFileName"], [1175, 36, 967, 14], [1176, 14, 967, 14, "lineNumber"], [1176, 24, 967, 14], [1177, 14, 967, 14, "columnNumber"], [1177, 26, 967, 14], [1178, 12, 967, 14], [1178, 19, 967, 20], [1178, 20, 967, 21], [1178, 35, 969, 14], [1178, 39, 969, 14, "_jsxDevRuntime"], [1178, 53, 969, 14], [1178, 54, 969, 14, "jsxDEV"], [1178, 60, 969, 14], [1178, 62, 969, 15, "_TouchableOpacity"], [1178, 79, 969, 15], [1178, 80, 969, 15, "default"], [1178, 87, 969, 31], [1179, 14, 970, 16, "onPress"], [1179, 21, 970, 23], [1179, 23, 970, 25, "capturePhoto"], [1179, 35, 970, 38], [1180, 14, 971, 16, "disabled"], [1180, 22, 971, 24], [1180, 24, 971, 26, "processingState"], [1180, 39, 971, 41], [1180, 44, 971, 46], [1180, 50, 971, 52], [1180, 54, 971, 56], [1180, 55, 971, 57, "isCameraReady"], [1180, 68, 971, 71], [1181, 14, 972, 16, "style"], [1181, 19, 972, 21], [1181, 21, 972, 23], [1181, 22, 973, 18, "styles"], [1181, 28, 973, 24], [1181, 29, 973, 25, "shutterButton"], [1181, 42, 973, 38], [1181, 44, 974, 18, "processingState"], [1181, 59, 974, 33], [1181, 64, 974, 38], [1181, 70, 974, 44], [1181, 74, 974, 48, "styles"], [1181, 80, 974, 54], [1181, 81, 974, 55, "shutterButtonDisabled"], [1181, 102, 974, 76], [1181, 103, 975, 18], [1182, 14, 975, 18, "children"], [1182, 22, 975, 18], [1182, 24, 977, 17, "processingState"], [1182, 39, 977, 32], [1182, 44, 977, 37], [1182, 50, 977, 43], [1182, 66, 978, 18], [1182, 70, 978, 18, "_jsxDevRuntime"], [1182, 84, 978, 18], [1182, 85, 978, 18, "jsxDEV"], [1182, 91, 978, 18], [1182, 93, 978, 19, "_View"], [1182, 98, 978, 19], [1182, 99, 978, 19, "default"], [1182, 106, 978, 23], [1183, 16, 978, 24, "style"], [1183, 21, 978, 29], [1183, 23, 978, 31, "styles"], [1183, 29, 978, 37], [1183, 30, 978, 38, "shutterInner"], [1184, 14, 978, 51], [1185, 16, 978, 51, "fileName"], [1185, 24, 978, 51], [1185, 26, 978, 51, "_jsxFileName"], [1185, 38, 978, 51], [1186, 16, 978, 51, "lineNumber"], [1186, 26, 978, 51], [1187, 16, 978, 51, "columnNumber"], [1187, 28, 978, 51], [1188, 14, 978, 51], [1188, 21, 978, 53], [1188, 22, 978, 54], [1188, 38, 980, 18], [1188, 42, 980, 18, "_jsxDevRuntime"], [1188, 56, 980, 18], [1188, 57, 980, 18, "jsxDEV"], [1188, 63, 980, 18], [1188, 65, 980, 19, "_ActivityIndicator"], [1188, 83, 980, 19], [1188, 84, 980, 19, "default"], [1188, 91, 980, 36], [1189, 16, 980, 37, "size"], [1189, 20, 980, 41], [1189, 22, 980, 42], [1189, 29, 980, 49], [1190, 16, 980, 50, "color"], [1190, 21, 980, 55], [1190, 23, 980, 56], [1191, 14, 980, 65], [1192, 16, 980, 65, "fileName"], [1192, 24, 980, 65], [1192, 26, 980, 65, "_jsxFileName"], [1192, 38, 980, 65], [1193, 16, 980, 65, "lineNumber"], [1193, 26, 980, 65], [1194, 16, 980, 65, "columnNumber"], [1194, 28, 980, 65], [1195, 14, 980, 65], [1195, 21, 980, 67], [1196, 12, 981, 17], [1197, 14, 981, 17, "fileName"], [1197, 22, 981, 17], [1197, 24, 981, 17, "_jsxFileName"], [1197, 36, 981, 17], [1198, 14, 981, 17, "lineNumber"], [1198, 24, 981, 17], [1199, 14, 981, 17, "columnNumber"], [1199, 26, 981, 17], [1200, 12, 981, 17], [1200, 19, 982, 32], [1200, 20, 982, 33], [1200, 35, 983, 14], [1200, 39, 983, 14, "_jsxDevRuntime"], [1200, 53, 983, 14], [1200, 54, 983, 14, "jsxDEV"], [1200, 60, 983, 14], [1200, 62, 983, 15, "_Text"], [1200, 67, 983, 15], [1200, 68, 983, 15, "default"], [1200, 75, 983, 19], [1201, 14, 983, 20, "style"], [1201, 19, 983, 25], [1201, 21, 983, 27, "styles"], [1201, 27, 983, 33], [1201, 28, 983, 34, "privacyNote"], [1201, 39, 983, 46], [1202, 14, 983, 46, "children"], [1202, 22, 983, 46], [1202, 24, 983, 47], [1203, 12, 985, 14], [1204, 14, 985, 14, "fileName"], [1204, 22, 985, 14], [1204, 24, 985, 14, "_jsxFileName"], [1204, 36, 985, 14], [1205, 14, 985, 14, "lineNumber"], [1205, 24, 985, 14], [1206, 14, 985, 14, "columnNumber"], [1206, 26, 985, 14], [1207, 12, 985, 14], [1207, 19, 985, 20], [1207, 20, 985, 21], [1208, 10, 985, 21], [1209, 12, 985, 21, "fileName"], [1209, 20, 985, 21], [1209, 22, 985, 21, "_jsxFileName"], [1209, 34, 985, 21], [1210, 12, 985, 21, "lineNumber"], [1210, 22, 985, 21], [1211, 12, 985, 21, "columnNumber"], [1211, 24, 985, 21], [1212, 10, 985, 21], [1212, 17, 986, 18], [1212, 18, 986, 19], [1213, 8, 986, 19], [1213, 23, 987, 12], [1213, 24, 988, 9], [1214, 6, 988, 9], [1215, 8, 988, 9, "fileName"], [1215, 16, 988, 9], [1215, 18, 988, 9, "_jsxFileName"], [1215, 30, 988, 9], [1216, 8, 988, 9, "lineNumber"], [1216, 18, 988, 9], [1217, 8, 988, 9, "columnNumber"], [1217, 20, 988, 9], [1218, 6, 988, 9], [1218, 13, 989, 12], [1218, 14, 989, 13], [1218, 29, 991, 6], [1218, 33, 991, 6, "_jsxDevRuntime"], [1218, 47, 991, 6], [1218, 48, 991, 6, "jsxDEV"], [1218, 54, 991, 6], [1218, 56, 991, 7, "_Modal"], [1218, 62, 991, 7], [1218, 63, 991, 7, "default"], [1218, 70, 991, 12], [1219, 8, 992, 8, "visible"], [1219, 15, 992, 15], [1219, 17, 992, 17, "processingState"], [1219, 32, 992, 32], [1219, 37, 992, 37], [1219, 43, 992, 43], [1219, 47, 992, 47, "processingState"], [1219, 62, 992, 62], [1219, 67, 992, 67], [1219, 74, 992, 75], [1220, 8, 993, 8, "transparent"], [1220, 19, 993, 19], [1221, 8, 994, 8, "animationType"], [1221, 21, 994, 21], [1221, 23, 994, 22], [1221, 29, 994, 28], [1222, 8, 994, 28, "children"], [1222, 16, 994, 28], [1222, 31, 996, 8], [1222, 35, 996, 8, "_jsxDevRuntime"], [1222, 49, 996, 8], [1222, 50, 996, 8, "jsxDEV"], [1222, 56, 996, 8], [1222, 58, 996, 9, "_View"], [1222, 63, 996, 9], [1222, 64, 996, 9, "default"], [1222, 71, 996, 13], [1223, 10, 996, 14, "style"], [1223, 15, 996, 19], [1223, 17, 996, 21, "styles"], [1223, 23, 996, 27], [1223, 24, 996, 28, "processingModal"], [1223, 39, 996, 44], [1224, 10, 996, 44, "children"], [1224, 18, 996, 44], [1224, 33, 997, 10], [1224, 37, 997, 10, "_jsxDevRuntime"], [1224, 51, 997, 10], [1224, 52, 997, 10, "jsxDEV"], [1224, 58, 997, 10], [1224, 60, 997, 11, "_View"], [1224, 65, 997, 11], [1224, 66, 997, 11, "default"], [1224, 73, 997, 15], [1225, 12, 997, 16, "style"], [1225, 17, 997, 21], [1225, 19, 997, 23, "styles"], [1225, 25, 997, 29], [1225, 26, 997, 30, "processingContent"], [1225, 43, 997, 48], [1226, 12, 997, 48, "children"], [1226, 20, 997, 48], [1226, 36, 998, 12], [1226, 40, 998, 12, "_jsxDevRuntime"], [1226, 54, 998, 12], [1226, 55, 998, 12, "jsxDEV"], [1226, 61, 998, 12], [1226, 63, 998, 13, "_ActivityIndicator"], [1226, 81, 998, 13], [1226, 82, 998, 13, "default"], [1226, 89, 998, 30], [1227, 14, 998, 31, "size"], [1227, 18, 998, 35], [1227, 20, 998, 36], [1227, 27, 998, 43], [1228, 14, 998, 44, "color"], [1228, 19, 998, 49], [1228, 21, 998, 50], [1229, 12, 998, 59], [1230, 14, 998, 59, "fileName"], [1230, 22, 998, 59], [1230, 24, 998, 59, "_jsxFileName"], [1230, 36, 998, 59], [1231, 14, 998, 59, "lineNumber"], [1231, 24, 998, 59], [1232, 14, 998, 59, "columnNumber"], [1232, 26, 998, 59], [1233, 12, 998, 59], [1233, 19, 998, 61], [1233, 20, 998, 62], [1233, 35, 1000, 12], [1233, 39, 1000, 12, "_jsxDevRuntime"], [1233, 53, 1000, 12], [1233, 54, 1000, 12, "jsxDEV"], [1233, 60, 1000, 12], [1233, 62, 1000, 13, "_Text"], [1233, 67, 1000, 13], [1233, 68, 1000, 13, "default"], [1233, 75, 1000, 17], [1234, 14, 1000, 18, "style"], [1234, 19, 1000, 23], [1234, 21, 1000, 25, "styles"], [1234, 27, 1000, 31], [1234, 28, 1000, 32, "processingTitle"], [1234, 43, 1000, 48], [1235, 14, 1000, 48, "children"], [1235, 22, 1000, 48], [1235, 25, 1001, 15, "processingState"], [1235, 40, 1001, 30], [1235, 45, 1001, 35], [1235, 56, 1001, 46], [1235, 60, 1001, 50], [1235, 80, 1001, 70], [1235, 82, 1002, 15, "processingState"], [1235, 97, 1002, 30], [1235, 102, 1002, 35], [1235, 113, 1002, 46], [1235, 117, 1002, 50], [1235, 146, 1002, 79], [1235, 148, 1003, 15, "processingState"], [1235, 163, 1003, 30], [1235, 168, 1003, 35], [1235, 180, 1003, 47], [1235, 184, 1003, 51], [1235, 216, 1003, 83], [1235, 218, 1004, 15, "processingState"], [1235, 233, 1004, 30], [1235, 238, 1004, 35], [1235, 249, 1004, 46], [1235, 253, 1004, 50], [1235, 275, 1004, 72], [1236, 12, 1004, 72], [1237, 14, 1004, 72, "fileName"], [1237, 22, 1004, 72], [1237, 24, 1004, 72, "_jsxFileName"], [1237, 36, 1004, 72], [1238, 14, 1004, 72, "lineNumber"], [1238, 24, 1004, 72], [1239, 14, 1004, 72, "columnNumber"], [1239, 26, 1004, 72], [1240, 12, 1004, 72], [1240, 19, 1005, 18], [1240, 20, 1005, 19], [1240, 35, 1006, 12], [1240, 39, 1006, 12, "_jsxDevRuntime"], [1240, 53, 1006, 12], [1240, 54, 1006, 12, "jsxDEV"], [1240, 60, 1006, 12], [1240, 62, 1006, 13, "_View"], [1240, 67, 1006, 13], [1240, 68, 1006, 13, "default"], [1240, 75, 1006, 17], [1241, 14, 1006, 18, "style"], [1241, 19, 1006, 23], [1241, 21, 1006, 25, "styles"], [1241, 27, 1006, 31], [1241, 28, 1006, 32, "progressBar"], [1241, 39, 1006, 44], [1242, 14, 1006, 44, "children"], [1242, 22, 1006, 44], [1242, 37, 1007, 14], [1242, 41, 1007, 14, "_jsxDevRuntime"], [1242, 55, 1007, 14], [1242, 56, 1007, 14, "jsxDEV"], [1242, 62, 1007, 14], [1242, 64, 1007, 15, "_View"], [1242, 69, 1007, 15], [1242, 70, 1007, 15, "default"], [1242, 77, 1007, 19], [1243, 16, 1008, 16, "style"], [1243, 21, 1008, 21], [1243, 23, 1008, 23], [1243, 24, 1009, 18, "styles"], [1243, 30, 1009, 24], [1243, 31, 1009, 25, "progressFill"], [1243, 43, 1009, 37], [1243, 45, 1010, 18], [1244, 18, 1010, 20, "width"], [1244, 23, 1010, 25], [1244, 25, 1010, 27], [1244, 28, 1010, 30, "processingProgress"], [1244, 46, 1010, 48], [1245, 16, 1010, 52], [1245, 17, 1010, 53], [1246, 14, 1011, 18], [1247, 16, 1011, 18, "fileName"], [1247, 24, 1011, 18], [1247, 26, 1011, 18, "_jsxFileName"], [1247, 38, 1011, 18], [1248, 16, 1011, 18, "lineNumber"], [1248, 26, 1011, 18], [1249, 16, 1011, 18, "columnNumber"], [1249, 28, 1011, 18], [1250, 14, 1011, 18], [1250, 21, 1012, 15], [1251, 12, 1012, 16], [1252, 14, 1012, 16, "fileName"], [1252, 22, 1012, 16], [1252, 24, 1012, 16, "_jsxFileName"], [1252, 36, 1012, 16], [1253, 14, 1012, 16, "lineNumber"], [1253, 24, 1012, 16], [1254, 14, 1012, 16, "columnNumber"], [1254, 26, 1012, 16], [1255, 12, 1012, 16], [1255, 19, 1013, 18], [1255, 20, 1013, 19], [1255, 35, 1014, 12], [1255, 39, 1014, 12, "_jsxDevRuntime"], [1255, 53, 1014, 12], [1255, 54, 1014, 12, "jsxDEV"], [1255, 60, 1014, 12], [1255, 62, 1014, 13, "_Text"], [1255, 67, 1014, 13], [1255, 68, 1014, 13, "default"], [1255, 75, 1014, 17], [1256, 14, 1014, 18, "style"], [1256, 19, 1014, 23], [1256, 21, 1014, 25, "styles"], [1256, 27, 1014, 31], [1256, 28, 1014, 32, "processingDescription"], [1256, 49, 1014, 54], [1257, 14, 1014, 54, "children"], [1257, 22, 1014, 54], [1257, 25, 1015, 15, "processingState"], [1257, 40, 1015, 30], [1257, 45, 1015, 35], [1257, 56, 1015, 46], [1257, 60, 1015, 50], [1257, 89, 1015, 79], [1257, 91, 1016, 15, "processingState"], [1257, 106, 1016, 30], [1257, 111, 1016, 35], [1257, 122, 1016, 46], [1257, 126, 1016, 50], [1257, 164, 1016, 88], [1257, 166, 1017, 15, "processingState"], [1257, 181, 1017, 30], [1257, 186, 1017, 35], [1257, 198, 1017, 47], [1257, 202, 1017, 51], [1257, 247, 1017, 96], [1257, 249, 1018, 15, "processingState"], [1257, 264, 1018, 30], [1257, 269, 1018, 35], [1257, 280, 1018, 46], [1257, 284, 1018, 50], [1257, 325, 1018, 91], [1258, 12, 1018, 91], [1259, 14, 1018, 91, "fileName"], [1259, 22, 1018, 91], [1259, 24, 1018, 91, "_jsxFileName"], [1259, 36, 1018, 91], [1260, 14, 1018, 91, "lineNumber"], [1260, 24, 1018, 91], [1261, 14, 1018, 91, "columnNumber"], [1261, 26, 1018, 91], [1262, 12, 1018, 91], [1262, 19, 1019, 18], [1262, 20, 1019, 19], [1262, 22, 1020, 13, "processingState"], [1262, 37, 1020, 28], [1262, 42, 1020, 33], [1262, 53, 1020, 44], [1262, 70, 1021, 14], [1262, 74, 1021, 14, "_jsxDevRuntime"], [1262, 88, 1021, 14], [1262, 89, 1021, 14, "jsxDEV"], [1262, 95, 1021, 14], [1262, 97, 1021, 15, "_lucideReactNative"], [1262, 115, 1021, 15], [1262, 116, 1021, 15, "CheckCircle"], [1262, 127, 1021, 26], [1263, 14, 1021, 27, "size"], [1263, 18, 1021, 31], [1263, 20, 1021, 33], [1263, 22, 1021, 36], [1264, 14, 1021, 37, "color"], [1264, 19, 1021, 42], [1264, 21, 1021, 43], [1264, 30, 1021, 52], [1265, 14, 1021, 53, "style"], [1265, 19, 1021, 58], [1265, 21, 1021, 60, "styles"], [1265, 27, 1021, 66], [1265, 28, 1021, 67, "successIcon"], [1266, 12, 1021, 79], [1267, 14, 1021, 79, "fileName"], [1267, 22, 1021, 79], [1267, 24, 1021, 79, "_jsxFileName"], [1267, 36, 1021, 79], [1268, 14, 1021, 79, "lineNumber"], [1268, 24, 1021, 79], [1269, 14, 1021, 79, "columnNumber"], [1269, 26, 1021, 79], [1270, 12, 1021, 79], [1270, 19, 1021, 81], [1270, 20, 1022, 13], [1271, 10, 1022, 13], [1272, 12, 1022, 13, "fileName"], [1272, 20, 1022, 13], [1272, 22, 1022, 13, "_jsxFileName"], [1272, 34, 1022, 13], [1273, 12, 1022, 13, "lineNumber"], [1273, 22, 1022, 13], [1274, 12, 1022, 13, "columnNumber"], [1274, 24, 1022, 13], [1275, 10, 1022, 13], [1275, 17, 1023, 16], [1276, 8, 1023, 17], [1277, 10, 1023, 17, "fileName"], [1277, 18, 1023, 17], [1277, 20, 1023, 17, "_jsxFileName"], [1277, 32, 1023, 17], [1278, 10, 1023, 17, "lineNumber"], [1278, 20, 1023, 17], [1279, 10, 1023, 17, "columnNumber"], [1279, 22, 1023, 17], [1280, 8, 1023, 17], [1280, 15, 1024, 14], [1281, 6, 1024, 15], [1282, 8, 1024, 15, "fileName"], [1282, 16, 1024, 15], [1282, 18, 1024, 15, "_jsxFileName"], [1282, 30, 1024, 15], [1283, 8, 1024, 15, "lineNumber"], [1283, 18, 1024, 15], [1284, 8, 1024, 15, "columnNumber"], [1284, 20, 1024, 15], [1285, 6, 1024, 15], [1285, 13, 1025, 13], [1285, 14, 1025, 14], [1285, 29, 1027, 6], [1285, 33, 1027, 6, "_jsxDevRuntime"], [1285, 47, 1027, 6], [1285, 48, 1027, 6, "jsxDEV"], [1285, 54, 1027, 6], [1285, 56, 1027, 7, "_Modal"], [1285, 62, 1027, 7], [1285, 63, 1027, 7, "default"], [1285, 70, 1027, 12], [1286, 8, 1028, 8, "visible"], [1286, 15, 1028, 15], [1286, 17, 1028, 17, "processingState"], [1286, 32, 1028, 32], [1286, 37, 1028, 37], [1286, 44, 1028, 45], [1287, 8, 1029, 8, "transparent"], [1287, 19, 1029, 19], [1288, 8, 1030, 8, "animationType"], [1288, 21, 1030, 21], [1288, 23, 1030, 22], [1288, 29, 1030, 28], [1289, 8, 1030, 28, "children"], [1289, 16, 1030, 28], [1289, 31, 1032, 8], [1289, 35, 1032, 8, "_jsxDevRuntime"], [1289, 49, 1032, 8], [1289, 50, 1032, 8, "jsxDEV"], [1289, 56, 1032, 8], [1289, 58, 1032, 9, "_View"], [1289, 63, 1032, 9], [1289, 64, 1032, 9, "default"], [1289, 71, 1032, 13], [1290, 10, 1032, 14, "style"], [1290, 15, 1032, 19], [1290, 17, 1032, 21, "styles"], [1290, 23, 1032, 27], [1290, 24, 1032, 28, "processingModal"], [1290, 39, 1032, 44], [1291, 10, 1032, 44, "children"], [1291, 18, 1032, 44], [1291, 33, 1033, 10], [1291, 37, 1033, 10, "_jsxDevRuntime"], [1291, 51, 1033, 10], [1291, 52, 1033, 10, "jsxDEV"], [1291, 58, 1033, 10], [1291, 60, 1033, 11, "_View"], [1291, 65, 1033, 11], [1291, 66, 1033, 11, "default"], [1291, 73, 1033, 15], [1292, 12, 1033, 16, "style"], [1292, 17, 1033, 21], [1292, 19, 1033, 23, "styles"], [1292, 25, 1033, 29], [1292, 26, 1033, 30, "errorContent"], [1292, 38, 1033, 43], [1293, 12, 1033, 43, "children"], [1293, 20, 1033, 43], [1293, 36, 1034, 12], [1293, 40, 1034, 12, "_jsxDevRuntime"], [1293, 54, 1034, 12], [1293, 55, 1034, 12, "jsxDEV"], [1293, 61, 1034, 12], [1293, 63, 1034, 13, "_lucideReactNative"], [1293, 81, 1034, 13], [1293, 82, 1034, 13, "X"], [1293, 83, 1034, 14], [1294, 14, 1034, 15, "size"], [1294, 18, 1034, 19], [1294, 20, 1034, 21], [1294, 22, 1034, 24], [1295, 14, 1034, 25, "color"], [1295, 19, 1034, 30], [1295, 21, 1034, 31], [1296, 12, 1034, 40], [1297, 14, 1034, 40, "fileName"], [1297, 22, 1034, 40], [1297, 24, 1034, 40, "_jsxFileName"], [1297, 36, 1034, 40], [1298, 14, 1034, 40, "lineNumber"], [1298, 24, 1034, 40], [1299, 14, 1034, 40, "columnNumber"], [1299, 26, 1034, 40], [1300, 12, 1034, 40], [1300, 19, 1034, 42], [1300, 20, 1034, 43], [1300, 35, 1035, 12], [1300, 39, 1035, 12, "_jsxDevRuntime"], [1300, 53, 1035, 12], [1300, 54, 1035, 12, "jsxDEV"], [1300, 60, 1035, 12], [1300, 62, 1035, 13, "_Text"], [1300, 67, 1035, 13], [1300, 68, 1035, 13, "default"], [1300, 75, 1035, 17], [1301, 14, 1035, 18, "style"], [1301, 19, 1035, 23], [1301, 21, 1035, 25, "styles"], [1301, 27, 1035, 31], [1301, 28, 1035, 32, "errorTitle"], [1301, 38, 1035, 43], [1302, 14, 1035, 43, "children"], [1302, 22, 1035, 43], [1302, 24, 1035, 44], [1303, 12, 1035, 61], [1304, 14, 1035, 61, "fileName"], [1304, 22, 1035, 61], [1304, 24, 1035, 61, "_jsxFileName"], [1304, 36, 1035, 61], [1305, 14, 1035, 61, "lineNumber"], [1305, 24, 1035, 61], [1306, 14, 1035, 61, "columnNumber"], [1306, 26, 1035, 61], [1307, 12, 1035, 61], [1307, 19, 1035, 67], [1307, 20, 1035, 68], [1307, 35, 1036, 12], [1307, 39, 1036, 12, "_jsxDevRuntime"], [1307, 53, 1036, 12], [1307, 54, 1036, 12, "jsxDEV"], [1307, 60, 1036, 12], [1307, 62, 1036, 13, "_Text"], [1307, 67, 1036, 13], [1307, 68, 1036, 13, "default"], [1307, 75, 1036, 17], [1308, 14, 1036, 18, "style"], [1308, 19, 1036, 23], [1308, 21, 1036, 25, "styles"], [1308, 27, 1036, 31], [1308, 28, 1036, 32, "errorMessage"], [1308, 40, 1036, 45], [1309, 14, 1036, 45, "children"], [1309, 22, 1036, 45], [1309, 24, 1036, 47, "errorMessage"], [1310, 12, 1036, 59], [1311, 14, 1036, 59, "fileName"], [1311, 22, 1036, 59], [1311, 24, 1036, 59, "_jsxFileName"], [1311, 36, 1036, 59], [1312, 14, 1036, 59, "lineNumber"], [1312, 24, 1036, 59], [1313, 14, 1036, 59, "columnNumber"], [1313, 26, 1036, 59], [1314, 12, 1036, 59], [1314, 19, 1036, 66], [1314, 20, 1036, 67], [1314, 35, 1037, 12], [1314, 39, 1037, 12, "_jsxDevRuntime"], [1314, 53, 1037, 12], [1314, 54, 1037, 12, "jsxDEV"], [1314, 60, 1037, 12], [1314, 62, 1037, 13, "_TouchableOpacity"], [1314, 79, 1037, 13], [1314, 80, 1037, 13, "default"], [1314, 87, 1037, 29], [1315, 14, 1038, 14, "onPress"], [1315, 21, 1038, 21], [1315, 23, 1038, 23, "retryCapture"], [1315, 35, 1038, 36], [1316, 14, 1039, 14, "style"], [1316, 19, 1039, 19], [1316, 21, 1039, 21, "styles"], [1316, 27, 1039, 27], [1316, 28, 1039, 28, "primaryButton"], [1316, 41, 1039, 42], [1317, 14, 1039, 42, "children"], [1317, 22, 1039, 42], [1317, 37, 1041, 14], [1317, 41, 1041, 14, "_jsxDevRuntime"], [1317, 55, 1041, 14], [1317, 56, 1041, 14, "jsxDEV"], [1317, 62, 1041, 14], [1317, 64, 1041, 15, "_Text"], [1317, 69, 1041, 15], [1317, 70, 1041, 15, "default"], [1317, 77, 1041, 19], [1318, 16, 1041, 20, "style"], [1318, 21, 1041, 25], [1318, 23, 1041, 27, "styles"], [1318, 29, 1041, 33], [1318, 30, 1041, 34, "primaryButtonText"], [1318, 47, 1041, 52], [1319, 16, 1041, 52, "children"], [1319, 24, 1041, 52], [1319, 26, 1041, 53], [1320, 14, 1041, 62], [1321, 16, 1041, 62, "fileName"], [1321, 24, 1041, 62], [1321, 26, 1041, 62, "_jsxFileName"], [1321, 38, 1041, 62], [1322, 16, 1041, 62, "lineNumber"], [1322, 26, 1041, 62], [1323, 16, 1041, 62, "columnNumber"], [1323, 28, 1041, 62], [1324, 14, 1041, 62], [1324, 21, 1041, 68], [1325, 12, 1041, 69], [1326, 14, 1041, 69, "fileName"], [1326, 22, 1041, 69], [1326, 24, 1041, 69, "_jsxFileName"], [1326, 36, 1041, 69], [1327, 14, 1041, 69, "lineNumber"], [1327, 24, 1041, 69], [1328, 14, 1041, 69, "columnNumber"], [1328, 26, 1041, 69], [1329, 12, 1041, 69], [1329, 19, 1042, 30], [1329, 20, 1042, 31], [1329, 35, 1043, 12], [1329, 39, 1043, 12, "_jsxDevRuntime"], [1329, 53, 1043, 12], [1329, 54, 1043, 12, "jsxDEV"], [1329, 60, 1043, 12], [1329, 62, 1043, 13, "_TouchableOpacity"], [1329, 79, 1043, 13], [1329, 80, 1043, 13, "default"], [1329, 87, 1043, 29], [1330, 14, 1044, 14, "onPress"], [1330, 21, 1044, 21], [1330, 23, 1044, 23, "onCancel"], [1330, 31, 1044, 32], [1331, 14, 1045, 14, "style"], [1331, 19, 1045, 19], [1331, 21, 1045, 21, "styles"], [1331, 27, 1045, 27], [1331, 28, 1045, 28, "secondaryButton"], [1331, 43, 1045, 44], [1332, 14, 1045, 44, "children"], [1332, 22, 1045, 44], [1332, 37, 1047, 14], [1332, 41, 1047, 14, "_jsxDevRuntime"], [1332, 55, 1047, 14], [1332, 56, 1047, 14, "jsxDEV"], [1332, 62, 1047, 14], [1332, 64, 1047, 15, "_Text"], [1332, 69, 1047, 15], [1332, 70, 1047, 15, "default"], [1332, 77, 1047, 19], [1333, 16, 1047, 20, "style"], [1333, 21, 1047, 25], [1333, 23, 1047, 27, "styles"], [1333, 29, 1047, 33], [1333, 30, 1047, 34, "secondaryButtonText"], [1333, 49, 1047, 54], [1334, 16, 1047, 54, "children"], [1334, 24, 1047, 54], [1334, 26, 1047, 55], [1335, 14, 1047, 61], [1336, 16, 1047, 61, "fileName"], [1336, 24, 1047, 61], [1336, 26, 1047, 61, "_jsxFileName"], [1336, 38, 1047, 61], [1337, 16, 1047, 61, "lineNumber"], [1337, 26, 1047, 61], [1338, 16, 1047, 61, "columnNumber"], [1338, 28, 1047, 61], [1339, 14, 1047, 61], [1339, 21, 1047, 67], [1340, 12, 1047, 68], [1341, 14, 1047, 68, "fileName"], [1341, 22, 1047, 68], [1341, 24, 1047, 68, "_jsxFileName"], [1341, 36, 1047, 68], [1342, 14, 1047, 68, "lineNumber"], [1342, 24, 1047, 68], [1343, 14, 1047, 68, "columnNumber"], [1343, 26, 1047, 68], [1344, 12, 1047, 68], [1344, 19, 1048, 30], [1344, 20, 1048, 31], [1345, 10, 1048, 31], [1346, 12, 1048, 31, "fileName"], [1346, 20, 1048, 31], [1346, 22, 1048, 31, "_jsxFileName"], [1346, 34, 1048, 31], [1347, 12, 1048, 31, "lineNumber"], [1347, 22, 1048, 31], [1348, 12, 1048, 31, "columnNumber"], [1348, 24, 1048, 31], [1349, 10, 1048, 31], [1349, 17, 1049, 16], [1350, 8, 1049, 17], [1351, 10, 1049, 17, "fileName"], [1351, 18, 1049, 17], [1351, 20, 1049, 17, "_jsxFileName"], [1351, 32, 1049, 17], [1352, 10, 1049, 17, "lineNumber"], [1352, 20, 1049, 17], [1353, 10, 1049, 17, "columnNumber"], [1353, 22, 1049, 17], [1354, 8, 1049, 17], [1354, 15, 1050, 14], [1355, 6, 1050, 15], [1356, 8, 1050, 15, "fileName"], [1356, 16, 1050, 15], [1356, 18, 1050, 15, "_jsxFileName"], [1356, 30, 1050, 15], [1357, 8, 1050, 15, "lineNumber"], [1357, 18, 1050, 15], [1358, 8, 1050, 15, "columnNumber"], [1358, 20, 1050, 15], [1359, 6, 1050, 15], [1359, 13, 1051, 13], [1359, 14, 1051, 14], [1360, 4, 1051, 14], [1361, 6, 1051, 14, "fileName"], [1361, 14, 1051, 14], [1361, 16, 1051, 14, "_jsxFileName"], [1361, 28, 1051, 14], [1362, 6, 1051, 14, "lineNumber"], [1362, 16, 1051, 14], [1363, 6, 1051, 14, "columnNumber"], [1363, 18, 1051, 14], [1364, 4, 1051, 14], [1364, 11, 1052, 10], [1364, 12, 1052, 11], [1365, 2, 1054, 0], [1366, 2, 1054, 1, "_s"], [1366, 4, 1054, 1], [1366, 5, 51, 24, "EchoCameraWeb"], [1366, 18, 51, 37], [1367, 4, 51, 37], [1367, 12, 58, 42, "useCameraPermissions"], [1367, 44, 58, 62], [1367, 46, 72, 19, "useUpload"], [1367, 64, 72, 28], [1368, 2, 72, 28], [1369, 2, 72, 28, "_c"], [1369, 4, 72, 28], [1369, 7, 51, 24, "EchoCameraWeb"], [1369, 20, 51, 37], [1370, 2, 1055, 0], [1370, 8, 1055, 6, "styles"], [1370, 14, 1055, 12], [1370, 17, 1055, 15, "StyleSheet"], [1370, 36, 1055, 25], [1370, 37, 1055, 26, "create"], [1370, 43, 1055, 32], [1370, 44, 1055, 33], [1371, 4, 1056, 2, "container"], [1371, 13, 1056, 11], [1371, 15, 1056, 13], [1372, 6, 1057, 4, "flex"], [1372, 10, 1057, 8], [1372, 12, 1057, 10], [1372, 13, 1057, 11], [1373, 6, 1058, 4, "backgroundColor"], [1373, 21, 1058, 19], [1373, 23, 1058, 21], [1374, 4, 1059, 2], [1374, 5, 1059, 3], [1375, 4, 1060, 2, "cameraContainer"], [1375, 19, 1060, 17], [1375, 21, 1060, 19], [1376, 6, 1061, 4, "flex"], [1376, 10, 1061, 8], [1376, 12, 1061, 10], [1376, 13, 1061, 11], [1377, 6, 1062, 4, "max<PERSON><PERSON><PERSON>"], [1377, 14, 1062, 12], [1377, 16, 1062, 14], [1377, 19, 1062, 17], [1378, 6, 1063, 4, "alignSelf"], [1378, 15, 1063, 13], [1378, 17, 1063, 15], [1378, 25, 1063, 23], [1379, 6, 1064, 4, "width"], [1379, 11, 1064, 9], [1379, 13, 1064, 11], [1380, 4, 1065, 2], [1380, 5, 1065, 3], [1381, 4, 1066, 2, "camera"], [1381, 10, 1066, 8], [1381, 12, 1066, 10], [1382, 6, 1067, 4, "flex"], [1382, 10, 1067, 8], [1382, 12, 1067, 10], [1383, 4, 1068, 2], [1383, 5, 1068, 3], [1384, 4, 1069, 2, "headerOverlay"], [1384, 17, 1069, 15], [1384, 19, 1069, 17], [1385, 6, 1070, 4, "position"], [1385, 14, 1070, 12], [1385, 16, 1070, 14], [1385, 26, 1070, 24], [1386, 6, 1071, 4, "top"], [1386, 9, 1071, 7], [1386, 11, 1071, 9], [1386, 12, 1071, 10], [1387, 6, 1072, 4, "left"], [1387, 10, 1072, 8], [1387, 12, 1072, 10], [1387, 13, 1072, 11], [1388, 6, 1073, 4, "right"], [1388, 11, 1073, 9], [1388, 13, 1073, 11], [1388, 14, 1073, 12], [1389, 6, 1074, 4, "backgroundColor"], [1389, 21, 1074, 19], [1389, 23, 1074, 21], [1389, 36, 1074, 34], [1390, 6, 1075, 4, "paddingTop"], [1390, 16, 1075, 14], [1390, 18, 1075, 16], [1390, 20, 1075, 18], [1391, 6, 1076, 4, "paddingHorizontal"], [1391, 23, 1076, 21], [1391, 25, 1076, 23], [1391, 27, 1076, 25], [1392, 6, 1077, 4, "paddingBottom"], [1392, 19, 1077, 17], [1392, 21, 1077, 19], [1393, 4, 1078, 2], [1393, 5, 1078, 3], [1394, 4, 1079, 2, "headerContent"], [1394, 17, 1079, 15], [1394, 19, 1079, 17], [1395, 6, 1080, 4, "flexDirection"], [1395, 19, 1080, 17], [1395, 21, 1080, 19], [1395, 26, 1080, 24], [1396, 6, 1081, 4, "justifyContent"], [1396, 20, 1081, 18], [1396, 22, 1081, 20], [1396, 37, 1081, 35], [1397, 6, 1082, 4, "alignItems"], [1397, 16, 1082, 14], [1397, 18, 1082, 16], [1398, 4, 1083, 2], [1398, 5, 1083, 3], [1399, 4, 1084, 2, "headerLeft"], [1399, 14, 1084, 12], [1399, 16, 1084, 14], [1400, 6, 1085, 4, "flex"], [1400, 10, 1085, 8], [1400, 12, 1085, 10], [1401, 4, 1086, 2], [1401, 5, 1086, 3], [1402, 4, 1087, 2, "headerTitle"], [1402, 15, 1087, 13], [1402, 17, 1087, 15], [1403, 6, 1088, 4, "fontSize"], [1403, 14, 1088, 12], [1403, 16, 1088, 14], [1403, 18, 1088, 16], [1404, 6, 1089, 4, "fontWeight"], [1404, 16, 1089, 14], [1404, 18, 1089, 16], [1404, 23, 1089, 21], [1405, 6, 1090, 4, "color"], [1405, 11, 1090, 9], [1405, 13, 1090, 11], [1405, 19, 1090, 17], [1406, 6, 1091, 4, "marginBottom"], [1406, 18, 1091, 16], [1406, 20, 1091, 18], [1407, 4, 1092, 2], [1407, 5, 1092, 3], [1408, 4, 1093, 2, "subtitleRow"], [1408, 15, 1093, 13], [1408, 17, 1093, 15], [1409, 6, 1094, 4, "flexDirection"], [1409, 19, 1094, 17], [1409, 21, 1094, 19], [1409, 26, 1094, 24], [1410, 6, 1095, 4, "alignItems"], [1410, 16, 1095, 14], [1410, 18, 1095, 16], [1410, 26, 1095, 24], [1411, 6, 1096, 4, "marginBottom"], [1411, 18, 1096, 16], [1411, 20, 1096, 18], [1412, 4, 1097, 2], [1412, 5, 1097, 3], [1413, 4, 1098, 2, "webIcon"], [1413, 11, 1098, 9], [1413, 13, 1098, 11], [1414, 6, 1099, 4, "fontSize"], [1414, 14, 1099, 12], [1414, 16, 1099, 14], [1414, 18, 1099, 16], [1415, 6, 1100, 4, "marginRight"], [1415, 17, 1100, 15], [1415, 19, 1100, 17], [1416, 4, 1101, 2], [1416, 5, 1101, 3], [1417, 4, 1102, 2, "headerSubtitle"], [1417, 18, 1102, 16], [1417, 20, 1102, 18], [1418, 6, 1103, 4, "fontSize"], [1418, 14, 1103, 12], [1418, 16, 1103, 14], [1418, 18, 1103, 16], [1419, 6, 1104, 4, "color"], [1419, 11, 1104, 9], [1419, 13, 1104, 11], [1419, 19, 1104, 17], [1420, 6, 1105, 4, "opacity"], [1420, 13, 1105, 11], [1420, 15, 1105, 13], [1421, 4, 1106, 2], [1421, 5, 1106, 3], [1422, 4, 1107, 2, "challengeRow"], [1422, 16, 1107, 14], [1422, 18, 1107, 16], [1423, 6, 1108, 4, "flexDirection"], [1423, 19, 1108, 17], [1423, 21, 1108, 19], [1423, 26, 1108, 24], [1424, 6, 1109, 4, "alignItems"], [1424, 16, 1109, 14], [1424, 18, 1109, 16], [1425, 4, 1110, 2], [1425, 5, 1110, 3], [1426, 4, 1111, 2, "challengeCode"], [1426, 17, 1111, 15], [1426, 19, 1111, 17], [1427, 6, 1112, 4, "fontSize"], [1427, 14, 1112, 12], [1427, 16, 1112, 14], [1427, 18, 1112, 16], [1428, 6, 1113, 4, "color"], [1428, 11, 1113, 9], [1428, 13, 1113, 11], [1428, 19, 1113, 17], [1429, 6, 1114, 4, "marginLeft"], [1429, 16, 1114, 14], [1429, 18, 1114, 16], [1429, 19, 1114, 17], [1430, 6, 1115, 4, "fontFamily"], [1430, 16, 1115, 14], [1430, 18, 1115, 16], [1431, 4, 1116, 2], [1431, 5, 1116, 3], [1432, 4, 1117, 2, "closeButton"], [1432, 15, 1117, 13], [1432, 17, 1117, 15], [1433, 6, 1118, 4, "padding"], [1433, 13, 1118, 11], [1433, 15, 1118, 13], [1434, 4, 1119, 2], [1434, 5, 1119, 3], [1435, 4, 1120, 2, "privacyNotice"], [1435, 17, 1120, 15], [1435, 19, 1120, 17], [1436, 6, 1121, 4, "position"], [1436, 14, 1121, 12], [1436, 16, 1121, 14], [1436, 26, 1121, 24], [1437, 6, 1122, 4, "top"], [1437, 9, 1122, 7], [1437, 11, 1122, 9], [1437, 14, 1122, 12], [1438, 6, 1123, 4, "left"], [1438, 10, 1123, 8], [1438, 12, 1123, 10], [1438, 14, 1123, 12], [1439, 6, 1124, 4, "right"], [1439, 11, 1124, 9], [1439, 13, 1124, 11], [1439, 15, 1124, 13], [1440, 6, 1125, 4, "backgroundColor"], [1440, 21, 1125, 19], [1440, 23, 1125, 21], [1440, 48, 1125, 46], [1441, 6, 1126, 4, "borderRadius"], [1441, 18, 1126, 16], [1441, 20, 1126, 18], [1441, 21, 1126, 19], [1442, 6, 1127, 4, "padding"], [1442, 13, 1127, 11], [1442, 15, 1127, 13], [1442, 17, 1127, 15], [1443, 6, 1128, 4, "flexDirection"], [1443, 19, 1128, 17], [1443, 21, 1128, 19], [1443, 26, 1128, 24], [1444, 6, 1129, 4, "alignItems"], [1444, 16, 1129, 14], [1444, 18, 1129, 16], [1445, 4, 1130, 2], [1445, 5, 1130, 3], [1446, 4, 1131, 2, "privacyText"], [1446, 15, 1131, 13], [1446, 17, 1131, 15], [1447, 6, 1132, 4, "color"], [1447, 11, 1132, 9], [1447, 13, 1132, 11], [1447, 19, 1132, 17], [1448, 6, 1133, 4, "fontSize"], [1448, 14, 1133, 12], [1448, 16, 1133, 14], [1448, 18, 1133, 16], [1449, 6, 1134, 4, "marginLeft"], [1449, 16, 1134, 14], [1449, 18, 1134, 16], [1449, 19, 1134, 17], [1450, 6, 1135, 4, "flex"], [1450, 10, 1135, 8], [1450, 12, 1135, 10], [1451, 4, 1136, 2], [1451, 5, 1136, 3], [1452, 4, 1137, 2, "footer<PERSON><PERSON><PERSON>"], [1452, 17, 1137, 15], [1452, 19, 1137, 17], [1453, 6, 1138, 4, "position"], [1453, 14, 1138, 12], [1453, 16, 1138, 14], [1453, 26, 1138, 24], [1454, 6, 1139, 4, "bottom"], [1454, 12, 1139, 10], [1454, 14, 1139, 12], [1454, 15, 1139, 13], [1455, 6, 1140, 4, "left"], [1455, 10, 1140, 8], [1455, 12, 1140, 10], [1455, 13, 1140, 11], [1456, 6, 1141, 4, "right"], [1456, 11, 1141, 9], [1456, 13, 1141, 11], [1456, 14, 1141, 12], [1457, 6, 1142, 4, "backgroundColor"], [1457, 21, 1142, 19], [1457, 23, 1142, 21], [1457, 36, 1142, 34], [1458, 6, 1143, 4, "paddingBottom"], [1458, 19, 1143, 17], [1458, 21, 1143, 19], [1458, 23, 1143, 21], [1459, 6, 1144, 4, "paddingTop"], [1459, 16, 1144, 14], [1459, 18, 1144, 16], [1459, 20, 1144, 18], [1460, 6, 1145, 4, "alignItems"], [1460, 16, 1145, 14], [1460, 18, 1145, 16], [1461, 4, 1146, 2], [1461, 5, 1146, 3], [1462, 4, 1147, 2, "instruction"], [1462, 15, 1147, 13], [1462, 17, 1147, 15], [1463, 6, 1148, 4, "fontSize"], [1463, 14, 1148, 12], [1463, 16, 1148, 14], [1463, 18, 1148, 16], [1464, 6, 1149, 4, "color"], [1464, 11, 1149, 9], [1464, 13, 1149, 11], [1464, 19, 1149, 17], [1465, 6, 1150, 4, "marginBottom"], [1465, 18, 1150, 16], [1465, 20, 1150, 18], [1466, 4, 1151, 2], [1466, 5, 1151, 3], [1467, 4, 1152, 2, "shutterButton"], [1467, 17, 1152, 15], [1467, 19, 1152, 17], [1468, 6, 1153, 4, "width"], [1468, 11, 1153, 9], [1468, 13, 1153, 11], [1468, 15, 1153, 13], [1469, 6, 1154, 4, "height"], [1469, 12, 1154, 10], [1469, 14, 1154, 12], [1469, 16, 1154, 14], [1470, 6, 1155, 4, "borderRadius"], [1470, 18, 1155, 16], [1470, 20, 1155, 18], [1470, 22, 1155, 20], [1471, 6, 1156, 4, "backgroundColor"], [1471, 21, 1156, 19], [1471, 23, 1156, 21], [1471, 29, 1156, 27], [1472, 6, 1157, 4, "justifyContent"], [1472, 20, 1157, 18], [1472, 22, 1157, 20], [1472, 30, 1157, 28], [1473, 6, 1158, 4, "alignItems"], [1473, 16, 1158, 14], [1473, 18, 1158, 16], [1473, 26, 1158, 24], [1474, 6, 1159, 4, "marginBottom"], [1474, 18, 1159, 16], [1474, 20, 1159, 18], [1474, 22, 1159, 20], [1475, 6, 1160, 4], [1475, 9, 1160, 7, "Platform"], [1475, 26, 1160, 15], [1475, 27, 1160, 16, "select"], [1475, 33, 1160, 22], [1475, 34, 1160, 23], [1476, 8, 1161, 6, "ios"], [1476, 11, 1161, 9], [1476, 13, 1161, 11], [1477, 10, 1162, 8, "shadowColor"], [1477, 21, 1162, 19], [1477, 23, 1162, 21], [1477, 32, 1162, 30], [1478, 10, 1163, 8, "shadowOffset"], [1478, 22, 1163, 20], [1478, 24, 1163, 22], [1479, 12, 1163, 24, "width"], [1479, 17, 1163, 29], [1479, 19, 1163, 31], [1479, 20, 1163, 32], [1480, 12, 1163, 34, "height"], [1480, 18, 1163, 40], [1480, 20, 1163, 42], [1481, 10, 1163, 44], [1481, 11, 1163, 45], [1482, 10, 1164, 8, "shadowOpacity"], [1482, 23, 1164, 21], [1482, 25, 1164, 23], [1482, 28, 1164, 26], [1483, 10, 1165, 8, "shadowRadius"], [1483, 22, 1165, 20], [1483, 24, 1165, 22], [1484, 8, 1166, 6], [1484, 9, 1166, 7], [1485, 8, 1167, 6, "android"], [1485, 15, 1167, 13], [1485, 17, 1167, 15], [1486, 10, 1168, 8, "elevation"], [1486, 19, 1168, 17], [1486, 21, 1168, 19], [1487, 8, 1169, 6], [1487, 9, 1169, 7], [1488, 8, 1170, 6, "web"], [1488, 11, 1170, 9], [1488, 13, 1170, 11], [1489, 10, 1171, 8, "boxShadow"], [1489, 19, 1171, 17], [1489, 21, 1171, 19], [1490, 8, 1172, 6], [1491, 6, 1173, 4], [1491, 7, 1173, 5], [1492, 4, 1174, 2], [1492, 5, 1174, 3], [1493, 4, 1175, 2, "shutterButtonDisabled"], [1493, 25, 1175, 23], [1493, 27, 1175, 25], [1494, 6, 1176, 4, "opacity"], [1494, 13, 1176, 11], [1494, 15, 1176, 13], [1495, 4, 1177, 2], [1495, 5, 1177, 3], [1496, 4, 1178, 2, "shutterInner"], [1496, 16, 1178, 14], [1496, 18, 1178, 16], [1497, 6, 1179, 4, "width"], [1497, 11, 1179, 9], [1497, 13, 1179, 11], [1497, 15, 1179, 13], [1498, 6, 1180, 4, "height"], [1498, 12, 1180, 10], [1498, 14, 1180, 12], [1498, 16, 1180, 14], [1499, 6, 1181, 4, "borderRadius"], [1499, 18, 1181, 16], [1499, 20, 1181, 18], [1499, 22, 1181, 20], [1500, 6, 1182, 4, "backgroundColor"], [1500, 21, 1182, 19], [1500, 23, 1182, 21], [1500, 29, 1182, 27], [1501, 6, 1183, 4, "borderWidth"], [1501, 17, 1183, 15], [1501, 19, 1183, 17], [1501, 20, 1183, 18], [1502, 6, 1184, 4, "borderColor"], [1502, 17, 1184, 15], [1502, 19, 1184, 17], [1503, 4, 1185, 2], [1503, 5, 1185, 3], [1504, 4, 1186, 2, "privacyNote"], [1504, 15, 1186, 13], [1504, 17, 1186, 15], [1505, 6, 1187, 4, "fontSize"], [1505, 14, 1187, 12], [1505, 16, 1187, 14], [1505, 18, 1187, 16], [1506, 6, 1188, 4, "color"], [1506, 11, 1188, 9], [1506, 13, 1188, 11], [1507, 4, 1189, 2], [1507, 5, 1189, 3], [1508, 4, 1190, 2, "processingModal"], [1508, 19, 1190, 17], [1508, 21, 1190, 19], [1509, 6, 1191, 4, "flex"], [1509, 10, 1191, 8], [1509, 12, 1191, 10], [1509, 13, 1191, 11], [1510, 6, 1192, 4, "backgroundColor"], [1510, 21, 1192, 19], [1510, 23, 1192, 21], [1510, 43, 1192, 41], [1511, 6, 1193, 4, "justifyContent"], [1511, 20, 1193, 18], [1511, 22, 1193, 20], [1511, 30, 1193, 28], [1512, 6, 1194, 4, "alignItems"], [1512, 16, 1194, 14], [1512, 18, 1194, 16], [1513, 4, 1195, 2], [1513, 5, 1195, 3], [1514, 4, 1196, 2, "processingContent"], [1514, 21, 1196, 19], [1514, 23, 1196, 21], [1515, 6, 1197, 4, "backgroundColor"], [1515, 21, 1197, 19], [1515, 23, 1197, 21], [1515, 29, 1197, 27], [1516, 6, 1198, 4, "borderRadius"], [1516, 18, 1198, 16], [1516, 20, 1198, 18], [1516, 22, 1198, 20], [1517, 6, 1199, 4, "padding"], [1517, 13, 1199, 11], [1517, 15, 1199, 13], [1517, 17, 1199, 15], [1518, 6, 1200, 4, "width"], [1518, 11, 1200, 9], [1518, 13, 1200, 11], [1518, 18, 1200, 16], [1519, 6, 1201, 4, "max<PERSON><PERSON><PERSON>"], [1519, 14, 1201, 12], [1519, 16, 1201, 14], [1519, 19, 1201, 17], [1520, 6, 1202, 4, "alignItems"], [1520, 16, 1202, 14], [1520, 18, 1202, 16], [1521, 4, 1203, 2], [1521, 5, 1203, 3], [1522, 4, 1204, 2, "processingTitle"], [1522, 19, 1204, 17], [1522, 21, 1204, 19], [1523, 6, 1205, 4, "fontSize"], [1523, 14, 1205, 12], [1523, 16, 1205, 14], [1523, 18, 1205, 16], [1524, 6, 1206, 4, "fontWeight"], [1524, 16, 1206, 14], [1524, 18, 1206, 16], [1524, 23, 1206, 21], [1525, 6, 1207, 4, "color"], [1525, 11, 1207, 9], [1525, 13, 1207, 11], [1525, 22, 1207, 20], [1526, 6, 1208, 4, "marginTop"], [1526, 15, 1208, 13], [1526, 17, 1208, 15], [1526, 19, 1208, 17], [1527, 6, 1209, 4, "marginBottom"], [1527, 18, 1209, 16], [1527, 20, 1209, 18], [1528, 4, 1210, 2], [1528, 5, 1210, 3], [1529, 4, 1211, 2, "progressBar"], [1529, 15, 1211, 13], [1529, 17, 1211, 15], [1530, 6, 1212, 4, "width"], [1530, 11, 1212, 9], [1530, 13, 1212, 11], [1530, 19, 1212, 17], [1531, 6, 1213, 4, "height"], [1531, 12, 1213, 10], [1531, 14, 1213, 12], [1531, 15, 1213, 13], [1532, 6, 1214, 4, "backgroundColor"], [1532, 21, 1214, 19], [1532, 23, 1214, 21], [1532, 32, 1214, 30], [1533, 6, 1215, 4, "borderRadius"], [1533, 18, 1215, 16], [1533, 20, 1215, 18], [1533, 21, 1215, 19], [1534, 6, 1216, 4, "overflow"], [1534, 14, 1216, 12], [1534, 16, 1216, 14], [1534, 24, 1216, 22], [1535, 6, 1217, 4, "marginBottom"], [1535, 18, 1217, 16], [1535, 20, 1217, 18], [1536, 4, 1218, 2], [1536, 5, 1218, 3], [1537, 4, 1219, 2, "progressFill"], [1537, 16, 1219, 14], [1537, 18, 1219, 16], [1538, 6, 1220, 4, "height"], [1538, 12, 1220, 10], [1538, 14, 1220, 12], [1538, 20, 1220, 18], [1539, 6, 1221, 4, "backgroundColor"], [1539, 21, 1221, 19], [1539, 23, 1221, 21], [1539, 32, 1221, 30], [1540, 6, 1222, 4, "borderRadius"], [1540, 18, 1222, 16], [1540, 20, 1222, 18], [1541, 4, 1223, 2], [1541, 5, 1223, 3], [1542, 4, 1224, 2, "processingDescription"], [1542, 25, 1224, 23], [1542, 27, 1224, 25], [1543, 6, 1225, 4, "fontSize"], [1543, 14, 1225, 12], [1543, 16, 1225, 14], [1543, 18, 1225, 16], [1544, 6, 1226, 4, "color"], [1544, 11, 1226, 9], [1544, 13, 1226, 11], [1544, 22, 1226, 20], [1545, 6, 1227, 4, "textAlign"], [1545, 15, 1227, 13], [1545, 17, 1227, 15], [1546, 4, 1228, 2], [1546, 5, 1228, 3], [1547, 4, 1229, 2, "successIcon"], [1547, 15, 1229, 13], [1547, 17, 1229, 15], [1548, 6, 1230, 4, "marginTop"], [1548, 15, 1230, 13], [1548, 17, 1230, 15], [1549, 4, 1231, 2], [1549, 5, 1231, 3], [1550, 4, 1232, 2, "errorContent"], [1550, 16, 1232, 14], [1550, 18, 1232, 16], [1551, 6, 1233, 4, "backgroundColor"], [1551, 21, 1233, 19], [1551, 23, 1233, 21], [1551, 29, 1233, 27], [1552, 6, 1234, 4, "borderRadius"], [1552, 18, 1234, 16], [1552, 20, 1234, 18], [1552, 22, 1234, 20], [1553, 6, 1235, 4, "padding"], [1553, 13, 1235, 11], [1553, 15, 1235, 13], [1553, 17, 1235, 15], [1554, 6, 1236, 4, "width"], [1554, 11, 1236, 9], [1554, 13, 1236, 11], [1554, 18, 1236, 16], [1555, 6, 1237, 4, "max<PERSON><PERSON><PERSON>"], [1555, 14, 1237, 12], [1555, 16, 1237, 14], [1555, 19, 1237, 17], [1556, 6, 1238, 4, "alignItems"], [1556, 16, 1238, 14], [1556, 18, 1238, 16], [1557, 4, 1239, 2], [1557, 5, 1239, 3], [1558, 4, 1240, 2, "errorTitle"], [1558, 14, 1240, 12], [1558, 16, 1240, 14], [1559, 6, 1241, 4, "fontSize"], [1559, 14, 1241, 12], [1559, 16, 1241, 14], [1559, 18, 1241, 16], [1560, 6, 1242, 4, "fontWeight"], [1560, 16, 1242, 14], [1560, 18, 1242, 16], [1560, 23, 1242, 21], [1561, 6, 1243, 4, "color"], [1561, 11, 1243, 9], [1561, 13, 1243, 11], [1561, 22, 1243, 20], [1562, 6, 1244, 4, "marginTop"], [1562, 15, 1244, 13], [1562, 17, 1244, 15], [1562, 19, 1244, 17], [1563, 6, 1245, 4, "marginBottom"], [1563, 18, 1245, 16], [1563, 20, 1245, 18], [1564, 4, 1246, 2], [1564, 5, 1246, 3], [1565, 4, 1247, 2, "errorMessage"], [1565, 16, 1247, 14], [1565, 18, 1247, 16], [1566, 6, 1248, 4, "fontSize"], [1566, 14, 1248, 12], [1566, 16, 1248, 14], [1566, 18, 1248, 16], [1567, 6, 1249, 4, "color"], [1567, 11, 1249, 9], [1567, 13, 1249, 11], [1567, 22, 1249, 20], [1568, 6, 1250, 4, "textAlign"], [1568, 15, 1250, 13], [1568, 17, 1250, 15], [1568, 25, 1250, 23], [1569, 6, 1251, 4, "marginBottom"], [1569, 18, 1251, 16], [1569, 20, 1251, 18], [1570, 4, 1252, 2], [1570, 5, 1252, 3], [1571, 4, 1253, 2, "primaryButton"], [1571, 17, 1253, 15], [1571, 19, 1253, 17], [1572, 6, 1254, 4, "backgroundColor"], [1572, 21, 1254, 19], [1572, 23, 1254, 21], [1572, 32, 1254, 30], [1573, 6, 1255, 4, "paddingHorizontal"], [1573, 23, 1255, 21], [1573, 25, 1255, 23], [1573, 27, 1255, 25], [1574, 6, 1256, 4, "paddingVertical"], [1574, 21, 1256, 19], [1574, 23, 1256, 21], [1574, 25, 1256, 23], [1575, 6, 1257, 4, "borderRadius"], [1575, 18, 1257, 16], [1575, 20, 1257, 18], [1575, 21, 1257, 19], [1576, 6, 1258, 4, "marginTop"], [1576, 15, 1258, 13], [1576, 17, 1258, 15], [1577, 4, 1259, 2], [1577, 5, 1259, 3], [1578, 4, 1260, 2, "primaryButtonText"], [1578, 21, 1260, 19], [1578, 23, 1260, 21], [1579, 6, 1261, 4, "color"], [1579, 11, 1261, 9], [1579, 13, 1261, 11], [1579, 19, 1261, 17], [1580, 6, 1262, 4, "fontSize"], [1580, 14, 1262, 12], [1580, 16, 1262, 14], [1580, 18, 1262, 16], [1581, 6, 1263, 4, "fontWeight"], [1581, 16, 1263, 14], [1581, 18, 1263, 16], [1582, 4, 1264, 2], [1582, 5, 1264, 3], [1583, 4, 1265, 2, "secondaryButton"], [1583, 19, 1265, 17], [1583, 21, 1265, 19], [1584, 6, 1266, 4, "paddingHorizontal"], [1584, 23, 1266, 21], [1584, 25, 1266, 23], [1584, 27, 1266, 25], [1585, 6, 1267, 4, "paddingVertical"], [1585, 21, 1267, 19], [1585, 23, 1267, 21], [1585, 25, 1267, 23], [1586, 6, 1268, 4, "marginTop"], [1586, 15, 1268, 13], [1586, 17, 1268, 15], [1587, 4, 1269, 2], [1587, 5, 1269, 3], [1588, 4, 1270, 2, "secondaryButtonText"], [1588, 23, 1270, 21], [1588, 25, 1270, 23], [1589, 6, 1271, 4, "color"], [1589, 11, 1271, 9], [1589, 13, 1271, 11], [1589, 22, 1271, 20], [1590, 6, 1272, 4, "fontSize"], [1590, 14, 1272, 12], [1590, 16, 1272, 14], [1591, 4, 1273, 2], [1591, 5, 1273, 3], [1592, 4, 1274, 2, "permissionContent"], [1592, 21, 1274, 19], [1592, 23, 1274, 21], [1593, 6, 1275, 4, "flex"], [1593, 10, 1275, 8], [1593, 12, 1275, 10], [1593, 13, 1275, 11], [1594, 6, 1276, 4, "justifyContent"], [1594, 20, 1276, 18], [1594, 22, 1276, 20], [1594, 30, 1276, 28], [1595, 6, 1277, 4, "alignItems"], [1595, 16, 1277, 14], [1595, 18, 1277, 16], [1595, 26, 1277, 24], [1596, 6, 1278, 4, "padding"], [1596, 13, 1278, 11], [1596, 15, 1278, 13], [1597, 4, 1279, 2], [1597, 5, 1279, 3], [1598, 4, 1280, 2, "permissionTitle"], [1598, 19, 1280, 17], [1598, 21, 1280, 19], [1599, 6, 1281, 4, "fontSize"], [1599, 14, 1281, 12], [1599, 16, 1281, 14], [1599, 18, 1281, 16], [1600, 6, 1282, 4, "fontWeight"], [1600, 16, 1282, 14], [1600, 18, 1282, 16], [1600, 23, 1282, 21], [1601, 6, 1283, 4, "color"], [1601, 11, 1283, 9], [1601, 13, 1283, 11], [1601, 22, 1283, 20], [1602, 6, 1284, 4, "marginTop"], [1602, 15, 1284, 13], [1602, 17, 1284, 15], [1602, 19, 1284, 17], [1603, 6, 1285, 4, "marginBottom"], [1603, 18, 1285, 16], [1603, 20, 1285, 18], [1604, 4, 1286, 2], [1604, 5, 1286, 3], [1605, 4, 1287, 2, "permissionDescription"], [1605, 25, 1287, 23], [1605, 27, 1287, 25], [1606, 6, 1288, 4, "fontSize"], [1606, 14, 1288, 12], [1606, 16, 1288, 14], [1606, 18, 1288, 16], [1607, 6, 1289, 4, "color"], [1607, 11, 1289, 9], [1607, 13, 1289, 11], [1607, 22, 1289, 20], [1608, 6, 1290, 4, "textAlign"], [1608, 15, 1290, 13], [1608, 17, 1290, 15], [1608, 25, 1290, 23], [1609, 6, 1291, 4, "marginBottom"], [1609, 18, 1291, 16], [1609, 20, 1291, 18], [1610, 4, 1292, 2], [1610, 5, 1292, 3], [1611, 4, 1293, 2, "loadingText"], [1611, 15, 1293, 13], [1611, 17, 1293, 15], [1612, 6, 1294, 4, "color"], [1612, 11, 1294, 9], [1612, 13, 1294, 11], [1612, 22, 1294, 20], [1613, 6, 1295, 4, "marginTop"], [1613, 15, 1295, 13], [1613, 17, 1295, 15], [1614, 4, 1296, 2], [1614, 5, 1296, 3], [1615, 4, 1297, 2], [1616, 4, 1298, 2, "blurZone"], [1616, 12, 1298, 10], [1616, 14, 1298, 12], [1617, 6, 1299, 4, "position"], [1617, 14, 1299, 12], [1617, 16, 1299, 14], [1617, 26, 1299, 24], [1618, 6, 1300, 4, "overflow"], [1618, 14, 1300, 12], [1618, 16, 1300, 14], [1619, 4, 1301, 2], [1619, 5, 1301, 3], [1620, 4, 1302, 2, "previewChip"], [1620, 15, 1302, 13], [1620, 17, 1302, 15], [1621, 6, 1303, 4, "position"], [1621, 14, 1303, 12], [1621, 16, 1303, 14], [1621, 26, 1303, 24], [1622, 6, 1304, 4, "top"], [1622, 9, 1304, 7], [1622, 11, 1304, 9], [1622, 12, 1304, 10], [1623, 6, 1305, 4, "right"], [1623, 11, 1305, 9], [1623, 13, 1305, 11], [1623, 14, 1305, 12], [1624, 6, 1306, 4, "backgroundColor"], [1624, 21, 1306, 19], [1624, 23, 1306, 21], [1624, 40, 1306, 38], [1625, 6, 1307, 4, "paddingHorizontal"], [1625, 23, 1307, 21], [1625, 25, 1307, 23], [1625, 27, 1307, 25], [1626, 6, 1308, 4, "paddingVertical"], [1626, 21, 1308, 19], [1626, 23, 1308, 21], [1626, 24, 1308, 22], [1627, 6, 1309, 4, "borderRadius"], [1627, 18, 1309, 16], [1627, 20, 1309, 18], [1628, 4, 1310, 2], [1628, 5, 1310, 3], [1629, 4, 1311, 2, "previewChipText"], [1629, 19, 1311, 17], [1629, 21, 1311, 19], [1630, 6, 1312, 4, "color"], [1630, 11, 1312, 9], [1630, 13, 1312, 11], [1630, 19, 1312, 17], [1631, 6, 1313, 4, "fontSize"], [1631, 14, 1313, 12], [1631, 16, 1313, 14], [1631, 18, 1313, 16], [1632, 6, 1314, 4, "fontWeight"], [1632, 16, 1314, 14], [1632, 18, 1314, 16], [1633, 4, 1315, 2], [1634, 2, 1316, 0], [1634, 3, 1316, 1], [1634, 4, 1316, 2], [1635, 2, 1316, 3], [1635, 6, 1316, 3, "_c"], [1635, 8, 1316, 3], [1636, 2, 1316, 3, "$RefreshReg$"], [1636, 14, 1316, 3], [1636, 15, 1316, 3, "_c"], [1636, 17, 1316, 3], [1637, 0, 1316, 3], [1637, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;+BSE;GT0C;qBUE;GVQ;8BWE;GX4B;2BYE;GZa;wBaE;GbiB;0BcG;GdyE;0BeE;GfuB;gCgBE;kBCa;KDG;GhBC;mCkBG;wBdc,kCc;GlBoC;mCmBE;wBfa;OeI;oFCkC;UDM;8BEW;SF0C;uDfa;sBkBC,wBlB;OeC;GnBe;6BuBG;GvB6B;kCwBG;GxB8C;4ByBE;mBCmD;SDE;GzBO;uB2BE;G3BI;mC4BG;G5BM;YCE;GDK;oB6B2C;W7BG;yB8BC;W9BG;wB+BC;W/BI;CD4L"}}, "type": "js/module"}]}