{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.processCircle = exports.isCircleScalarDef = void 0;\n  const _worklet_7967137287623_init_data = {\n    code: \"function CircleJs1(def){return def.cx!==undefined;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\dom\\\\nodes\\\\datatypes\\\\Circle.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"CircleJs1\\\",\\\"def\\\",\\\"cx\\\",\\\"undefined\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/dom/nodes/datatypes/Circle.js\\\"],\\\"mappings\\\":\\\"AAAiC,SAAAA,SAAOA,CAAAC,GAAA,EAKtC,MAAO,CAAAA,GAAG,CAACC,EAAE,GAAKC,SAAS,CAC7B\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const isCircleScalarDef = exports.isCircleScalarDef = function () {\n    const _e = [new global.Error(), 1, -27];\n    const CircleJs1 = function (def) {\n      // We have an issue to check property existence on JSI backed instances\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return def.cx !== undefined;\n    };\n    CircleJs1.__closure = {};\n    CircleJs1.__workletHash = 7967137287623;\n    CircleJs1.__initData = _worklet_7967137287623_init_data;\n    CircleJs1.__stackDetails = _e;\n    return CircleJs1;\n  }();\n  const _worklet_9334466683399_init_data = {\n    code: \"function CircleJs2(def){const{isCircleScalarDef}=this.__closure;var _def$c;if(isCircleScalarDef(def)){return{c:{x:def.cx,y:def.cy},r:def.r};}return{...def,c:(_def$c=def.c)!==null&&_def$c!==void 0?_def$c:{x:0,y:0}};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\dom\\\\nodes\\\\datatypes\\\\Circle.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"CircleJs2\\\",\\\"def\\\",\\\"isCircleScalarDef\\\",\\\"__closure\\\",\\\"_def$c\\\",\\\"c\\\",\\\"x\\\",\\\"cx\\\",\\\"y\\\",\\\"cy\\\",\\\"r\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/dom/nodes/datatypes/Circle.js\\\"],\\\"mappings\\\":\\\"AAO6B,SAAAA,SAAOA,CAAAC,GAAA,QAAAC,iBAAA,OAAAC,SAAA,CAGlC,GAAI,CAAAC,MAAM,CACV,GAAIF,iBAAiB,CAACD,GAAG,CAAC,CAAE,CAC1B,MAAO,CACLI,CAAC,CAAE,CACDC,CAAC,CAAEL,GAAG,CAACM,EAAE,CACTC,CAAC,CAAEP,GAAG,CAACQ,EACT,CAAC,CACDC,CAAC,CAAET,GAAG,CAACS,CACT,CAAC,CACH,CACA,MAAO,CACL,GAAGT,GAAG,CACNI,CAAC,CAAE,CAACD,MAAM,CAAGH,GAAG,CAACI,CAAC,IAAM,IAAI,EAAID,MAAM,GAAK,IAAK,EAAC,CAAGA,MAAM,CAAG,CAC3DE,CAAC,CAAE,CAAC,CACJE,CAAC,CAAE,CACL,CACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const processCircle = exports.processCircle = function () {\n    const _e = [new global.Error(), -2, -27];\n    const CircleJs2 = function (def) {\n      var _def$c;\n      if (isCircleScalarDef(def)) {\n        return {\n          c: {\n            x: def.cx,\n            y: def.cy\n          },\n          r: def.r\n        };\n      }\n      return {\n        ...def,\n        c: (_def$c = def.c) !== null && _def$c !== void 0 ? _def$c : {\n          x: 0,\n          y: 0\n        }\n      };\n    };\n    CircleJs2.__closure = {\n      isCircleScalarDef\n    };\n    CircleJs2.__workletHash = 9334466683399;\n    CircleJs2.__initData = _worklet_9334466683399_init_data;\n    CircleJs2.__stackDetails = _e;\n    return CircleJs2;\n  }();\n});", "lineCount": 60, "map": [[12, 2, 1, 7], [12, 8, 1, 13, "isCircleScalarDef"], [12, 25, 1, 30], [12, 28, 1, 30, "exports"], [12, 35, 1, 30], [12, 36, 1, 30, "isCircleScalarDef"], [12, 53, 1, 30], [12, 56, 1, 33], [13, 4, 1, 33], [13, 10, 1, 33, "_e"], [13, 12, 1, 33], [13, 20, 1, 33, "global"], [13, 26, 1, 33], [13, 27, 1, 33, "Error"], [13, 32, 1, 33], [14, 4, 1, 33], [14, 10, 1, 33, "CircleJs1"], [14, 19, 1, 33], [14, 31, 1, 33, "CircleJs1"], [14, 32, 1, 33, "def"], [14, 35, 1, 36], [14, 37, 1, 40], [15, 6, 4, 2], [16, 6, 5, 2], [17, 6, 6, 2], [17, 13, 6, 9, "def"], [17, 16, 6, 12], [17, 17, 6, 13, "cx"], [17, 19, 6, 15], [17, 24, 6, 20, "undefined"], [17, 33, 6, 29], [18, 4, 7, 0], [18, 5, 7, 1], [19, 4, 7, 1, "CircleJs1"], [19, 13, 7, 1], [19, 14, 7, 1, "__closure"], [19, 23, 7, 1], [20, 4, 7, 1, "CircleJs1"], [20, 13, 7, 1], [20, 14, 7, 1, "__workletHash"], [20, 27, 7, 1], [21, 4, 7, 1, "CircleJs1"], [21, 13, 7, 1], [21, 14, 7, 1, "__initData"], [21, 24, 7, 1], [21, 27, 7, 1, "_worklet_7967137287623_init_data"], [21, 59, 7, 1], [22, 4, 7, 1, "CircleJs1"], [22, 13, 7, 1], [22, 14, 7, 1, "__stackDetails"], [22, 28, 7, 1], [22, 31, 7, 1, "_e"], [22, 33, 7, 1], [23, 4, 7, 1], [23, 11, 7, 1, "CircleJs1"], [23, 20, 7, 1], [24, 2, 7, 1], [24, 3, 1, 33], [24, 5, 7, 1], [25, 2, 7, 2], [25, 8, 7, 2, "_worklet_9334466683399_init_data"], [25, 40, 7, 2], [26, 4, 7, 2, "code"], [26, 8, 7, 2], [27, 4, 7, 2, "location"], [27, 12, 7, 2], [28, 4, 7, 2, "sourceMap"], [28, 13, 7, 2], [29, 4, 7, 2, "version"], [29, 11, 7, 2], [30, 2, 7, 2], [31, 2, 8, 7], [31, 8, 8, 13, "processCircle"], [31, 21, 8, 26], [31, 24, 8, 26, "exports"], [31, 31, 8, 26], [31, 32, 8, 26, "processCircle"], [31, 45, 8, 26], [31, 48, 8, 29], [32, 4, 8, 29], [32, 10, 8, 29, "_e"], [32, 12, 8, 29], [32, 20, 8, 29, "global"], [32, 26, 8, 29], [32, 27, 8, 29, "Error"], [32, 32, 8, 29], [33, 4, 8, 29], [33, 10, 8, 29, "CircleJs2"], [33, 19, 8, 29], [33, 31, 8, 29, "CircleJs2"], [33, 32, 8, 29, "def"], [33, 35, 8, 32], [33, 37, 8, 36], [34, 6, 11, 2], [34, 10, 11, 6, "_def$c"], [34, 16, 11, 12], [35, 6, 12, 2], [35, 10, 12, 6, "isCircleScalarDef"], [35, 27, 12, 23], [35, 28, 12, 24, "def"], [35, 31, 12, 27], [35, 32, 12, 28], [35, 34, 12, 30], [36, 8, 13, 4], [36, 15, 13, 11], [37, 10, 14, 6, "c"], [37, 11, 14, 7], [37, 13, 14, 9], [38, 12, 15, 8, "x"], [38, 13, 15, 9], [38, 15, 15, 11, "def"], [38, 18, 15, 14], [38, 19, 15, 15, "cx"], [38, 21, 15, 17], [39, 12, 16, 8, "y"], [39, 13, 16, 9], [39, 15, 16, 11, "def"], [39, 18, 16, 14], [39, 19, 16, 15, "cy"], [40, 10, 17, 6], [40, 11, 17, 7], [41, 10, 18, 6, "r"], [41, 11, 18, 7], [41, 13, 18, 9, "def"], [41, 16, 18, 12], [41, 17, 18, 13, "r"], [42, 8, 19, 4], [42, 9, 19, 5], [43, 6, 20, 2], [44, 6, 21, 2], [44, 13, 21, 9], [45, 8, 22, 4], [45, 11, 22, 7, "def"], [45, 14, 22, 10], [46, 8, 23, 4, "c"], [46, 9, 23, 5], [46, 11, 23, 7], [46, 12, 23, 8, "_def$c"], [46, 18, 23, 14], [46, 21, 23, 17, "def"], [46, 24, 23, 20], [46, 25, 23, 21, "c"], [46, 26, 23, 22], [46, 32, 23, 28], [46, 36, 23, 32], [46, 40, 23, 36, "_def$c"], [46, 46, 23, 42], [46, 51, 23, 47], [46, 56, 23, 52], [46, 57, 23, 53], [46, 60, 23, 56, "_def$c"], [46, 66, 23, 62], [46, 69, 23, 65], [47, 10, 24, 6, "x"], [47, 11, 24, 7], [47, 13, 24, 9], [47, 14, 24, 10], [48, 10, 25, 6, "y"], [48, 11, 25, 7], [48, 13, 25, 9], [49, 8, 26, 4], [50, 6, 27, 2], [50, 7, 27, 3], [51, 4, 28, 0], [51, 5, 28, 1], [52, 4, 28, 1, "CircleJs2"], [52, 13, 28, 1], [52, 14, 28, 1, "__closure"], [52, 23, 28, 1], [53, 6, 28, 1, "isCircleScalarDef"], [54, 4, 28, 1], [55, 4, 28, 1, "CircleJs2"], [55, 13, 28, 1], [55, 14, 28, 1, "__workletHash"], [55, 27, 28, 1], [56, 4, 28, 1, "CircleJs2"], [56, 13, 28, 1], [56, 14, 28, 1, "__initData"], [56, 24, 28, 1], [56, 27, 28, 1, "_worklet_9334466683399_init_data"], [56, 59, 28, 1], [57, 4, 28, 1, "CircleJs2"], [57, 13, 28, 1], [57, 14, 28, 1, "__stackDetails"], [57, 28, 28, 1], [57, 31, 28, 1, "_e"], [57, 33, 28, 1], [58, 4, 28, 1], [58, 11, 28, 1, "CircleJs2"], [58, 20, 28, 1], [59, 2, 28, 1], [59, 3, 8, 29], [59, 5, 28, 1], [60, 0, 28, 2], [60, 3]], "functionMap": {"names": ["<global>", "isCircleScalarDef", "processCircle"], "mappings": "AAA,iCC;CDM;6BEC;CFoB"}}, "type": "js/module"}]}