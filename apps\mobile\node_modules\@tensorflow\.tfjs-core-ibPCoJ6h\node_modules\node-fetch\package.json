{"name": "node-fetch", "version": "2.1.2", "description": "A light-weight module that brings window.fetch to node.js", "main": "lib/index.js", "browser": "./browser.js", "module": "lib/index.es.js", "files": ["lib/index.js", "lib/index.es.js", "browser.js"], "engines": {"node": "4.x || >=6.0.0"}, "scripts": {"build": "cross-env BABEL_ENV=rollup rollup -c", "prepare": "npm run build", "test": "cross-env BABEL_ENV=test mocha --compilers js:babel-register test/test.js", "report": "cross-env BABEL_ENV=coverage nyc --reporter lcov --reporter text mocha -R spec test/test.js", "coverage": "cross-env BABEL_ENV=coverage nyc --reporter json --reporter text mocha -R spec test/test.js && codecov -f coverage/coverage-final.json"}, "repository": {"type": "git", "url": "https://github.com/bitinn/node-fetch.git"}, "keywords": ["fetch", "http", "promise"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/bitinn/node-fetch/issues"}, "homepage": "https://github.com/bitinn/node-fetch", "devDependencies": {"babel-core": "^6.26.0", "babel-plugin-istanbul": "^4.1.5", "babel-preset-env": "^1.6.1", "babel-register": "^6.16.3", "chai": "^3.5.0", "chai-as-promised": "^7.1.1", "chai-iterator": "^1.1.1", "chai-string": "^1.3.0", "codecov": "^3.0.0", "cross-env": "^5.1.3", "form-data": "^2.3.1", "mocha": "^5.0.0", "nyc": "^11.4.1", "parted": "^0.1.1", "promise": "^8.0.1", "resumer": "0.0.0", "rollup": "^0.55.1", "rollup-plugin-babel": "^3.0.3", "string-to-arraybuffer": "^1.0.0", "url-search-params": "^0.10.0", "whatwg-url": "^5.0.0"}, "dependencies": {}}