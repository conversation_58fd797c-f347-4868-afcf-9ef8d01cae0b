{"dependencies": [{"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 54, "index": 54}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  Object.defineProperty(exports, \"PermissionStatus\", {\n    enumerable: true,\n    get: function () {\n      return _expoModulesCore.PermissionStatus;\n    }\n  });\n  var _expoModulesCore = require(_dependencyMap[0], \"expo-modules-core\");\n});", "lineCount": 12, "map": [[11, 2, 1, 0], [11, 6, 1, 0, "_expoModulesCore"], [11, 22, 1, 0], [11, 25, 1, 0, "require"], [11, 32, 1, 0], [11, 33, 1, 0, "_dependencyMap"], [11, 47, 1, 0], [12, 0, 1, 54], [12, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}