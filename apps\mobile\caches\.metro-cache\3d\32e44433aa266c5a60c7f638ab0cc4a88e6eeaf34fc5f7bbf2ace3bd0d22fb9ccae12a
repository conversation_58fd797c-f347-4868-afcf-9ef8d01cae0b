{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 39, "index": 39}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkMatrix", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 40}, "end": {"line": 2, "column": 44, "index": 84}}], "key": "aOVfjZgmz4R2ci39pV6HZujK8og=", "exportNames": ["*"]}}, {"name": "./JsiSkPoint", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 85}, "end": {"line": 3, "column": 42, "index": 127}}], "key": "t00LaVO/wJ2FJvJQ0krGRNiisCQ=", "exportNames": ["*"]}}, {"name": "./JsiSkShader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 128}, "end": {"line": 4, "column": 44, "index": 172}}], "key": "qmH0e2X2qhdhK7DOZEHi4mFPbz8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkShaderFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkMatrix = require(_dependencyMap[1], \"./JsiSkMatrix\");\n  var _JsiSkPoint = require(_dependencyMap[2], \"./JsiSkPoint\");\n  var _JsiSkShader = require(_dependencyMap[3], \"./JsiSkShader\");\n  class JsiSkShaderFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    MakeLinearGradient(start, end, colors, pos, mode, localMatrix, flags) {\n      return new _JsiSkShader.JsiSkShader(this.CanvasKit, this.CanvasKit.Shader.MakeLinearGradient(_JsiSkPoint.JsiSkPoint.fromValue(start), _JsiSkPoint.JsiSkPoint.fromValue(end), colors, pos, (0, _Host.getEnum)(this.CanvasKit, \"TileMode\", mode), localMatrix === undefined ? undefined : _JsiSkMatrix.JsiSkMatrix.fromValue(localMatrix), flags));\n    }\n    MakeRadialGradient(center, radius, colors, pos, mode, localMatrix, flags) {\n      return new _JsiSkShader.JsiSkShader(this.CanvasKit, this.CanvasKit.Shader.MakeRadialGradient(_JsiSkPoint.JsiSkPoint.fromValue(center), radius, colors, pos, (0, _Host.getEnum)(this.CanvasKit, \"TileMode\", mode), localMatrix === undefined ? undefined : _JsiSkMatrix.JsiSkMatrix.fromValue(localMatrix), flags));\n    }\n    MakeTwoPointConicalGradient(start, startRadius, end, endRadius, colors, pos, mode, localMatrix, flags) {\n      return new _JsiSkShader.JsiSkShader(this.CanvasKit, this.CanvasKit.Shader.MakeTwoPointConicalGradient(_JsiSkPoint.JsiSkPoint.fromValue(start), startRadius, _JsiSkPoint.JsiSkPoint.fromValue(end), endRadius, colors, pos, (0, _Host.getEnum)(this.CanvasKit, \"TileMode\", mode), localMatrix === undefined ? undefined : _JsiSkMatrix.JsiSkMatrix.fromValue(localMatrix), flags));\n    }\n    MakeSweepGradient(cx, cy, colors, pos, mode, localMatrix, flags, startAngleInDegrees, endAngleInDegrees) {\n      return new _JsiSkShader.JsiSkShader(this.CanvasKit, this.CanvasKit.Shader.MakeSweepGradient(cx, cy, colors, pos, (0, _Host.getEnum)(this.CanvasKit, \"TileMode\", mode), localMatrix === undefined || localMatrix === null ? undefined : _JsiSkMatrix.JsiSkMatrix.fromValue(localMatrix), flags, startAngleInDegrees, endAngleInDegrees));\n    }\n    MakeTurbulence(baseFreqX, baseFreqY, octaves, seed, tileW, tileH) {\n      return new _JsiSkShader.JsiSkShader(this.CanvasKit, this.CanvasKit.Shader.MakeTurbulence(baseFreqX, baseFreqY, octaves, seed, tileW, tileH));\n    }\n    MakeFractalNoise(baseFreqX, baseFreqY, octaves, seed, tileW, tileH) {\n      return new _JsiSkShader.JsiSkShader(this.CanvasKit, this.CanvasKit.Shader.MakeFractalNoise(baseFreqX, baseFreqY, octaves, seed, tileW, tileH));\n    }\n    MakeBlend(mode, one, two) {\n      return new _JsiSkShader.JsiSkShader(this.CanvasKit, this.CanvasKit.Shader.MakeBlend((0, _Host.getEnum)(this.CanvasKit, \"BlendMode\", mode), _JsiSkShader.JsiSkShader.fromValue(one), _JsiSkShader.JsiSkShader.fromValue(two)));\n    }\n    MakeColor(color) {\n      return new _JsiSkShader.JsiSkShader(this.CanvasKit, this.CanvasKit.Shader.MakeColor(color, this.CanvasKit.ColorSpace.SRGB));\n    }\n  }\n  exports.JsiSkShaderFactory = JsiSkShaderFactory;\n});", "lineCount": 40, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkMatrix"], [7, 18, 2, 0], [7, 21, 2, 0, "require"], [7, 28, 2, 0], [7, 29, 2, 0, "_dependencyMap"], [7, 43, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_JsiSkPoint"], [8, 17, 3, 0], [8, 20, 3, 0, "require"], [8, 27, 3, 0], [8, 28, 3, 0, "_dependencyMap"], [8, 42, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_JsiSkShader"], [9, 18, 4, 0], [9, 21, 4, 0, "require"], [9, 28, 4, 0], [9, 29, 4, 0, "_dependencyMap"], [9, 43, 4, 0], [10, 2, 5, 7], [10, 8, 5, 13, "JsiSkShaderFactory"], [10, 26, 5, 31], [10, 35, 5, 40, "Host"], [10, 45, 5, 44], [10, 46, 5, 45], [11, 4, 6, 2, "constructor"], [11, 15, 6, 13, "constructor"], [11, 16, 6, 14, "CanvasKit"], [11, 25, 6, 23], [11, 27, 6, 25], [12, 6, 7, 4], [12, 11, 7, 9], [12, 12, 7, 10, "CanvasKit"], [12, 21, 7, 19], [12, 22, 7, 20], [13, 4, 8, 2], [14, 4, 9, 2, "MakeLinearGradient"], [14, 22, 9, 20, "MakeLinearGradient"], [14, 23, 9, 21, "start"], [14, 28, 9, 26], [14, 30, 9, 28, "end"], [14, 33, 9, 31], [14, 35, 9, 33, "colors"], [14, 41, 9, 39], [14, 43, 9, 41, "pos"], [14, 46, 9, 44], [14, 48, 9, 46, "mode"], [14, 52, 9, 50], [14, 54, 9, 52, "localMatrix"], [14, 65, 9, 63], [14, 67, 9, 65, "flags"], [14, 72, 9, 70], [14, 74, 9, 72], [15, 6, 10, 4], [15, 13, 10, 11], [15, 17, 10, 15, "JsiSkShader"], [15, 41, 10, 26], [15, 42, 10, 27], [15, 46, 10, 31], [15, 47, 10, 32, "CanvasKit"], [15, 56, 10, 41], [15, 58, 10, 43], [15, 62, 10, 47], [15, 63, 10, 48, "CanvasKit"], [15, 72, 10, 57], [15, 73, 10, 58, "Shader"], [15, 79, 10, 64], [15, 80, 10, 65, "MakeLinearGradient"], [15, 98, 10, 83], [15, 99, 10, 84, "JsiSkPoint"], [15, 121, 10, 94], [15, 122, 10, 95, "fromValue"], [15, 131, 10, 104], [15, 132, 10, 105, "start"], [15, 137, 10, 110], [15, 138, 10, 111], [15, 140, 10, 113, "JsiSkPoint"], [15, 162, 10, 123], [15, 163, 10, 124, "fromValue"], [15, 172, 10, 133], [15, 173, 10, 134, "end"], [15, 176, 10, 137], [15, 177, 10, 138], [15, 179, 10, 140, "colors"], [15, 185, 10, 146], [15, 187, 10, 148, "pos"], [15, 190, 10, 151], [15, 192, 10, 153], [15, 196, 10, 153, "getEnum"], [15, 209, 10, 160], [15, 211, 10, 161], [15, 215, 10, 165], [15, 216, 10, 166, "CanvasKit"], [15, 225, 10, 175], [15, 227, 10, 177], [15, 237, 10, 187], [15, 239, 10, 189, "mode"], [15, 243, 10, 193], [15, 244, 10, 194], [15, 246, 10, 196, "localMatrix"], [15, 257, 10, 207], [15, 262, 10, 212, "undefined"], [15, 271, 10, 221], [15, 274, 10, 224, "undefined"], [15, 283, 10, 233], [15, 286, 10, 236, "JsiSkMatrix"], [15, 310, 10, 247], [15, 311, 10, 248, "fromValue"], [15, 320, 10, 257], [15, 321, 10, 258, "localMatrix"], [15, 332, 10, 269], [15, 333, 10, 270], [15, 335, 10, 272, "flags"], [15, 340, 10, 277], [15, 341, 10, 278], [15, 342, 10, 279], [16, 4, 11, 2], [17, 4, 12, 2, "MakeRadialGradient"], [17, 22, 12, 20, "MakeRadialGradient"], [17, 23, 12, 21, "center"], [17, 29, 12, 27], [17, 31, 12, 29, "radius"], [17, 37, 12, 35], [17, 39, 12, 37, "colors"], [17, 45, 12, 43], [17, 47, 12, 45, "pos"], [17, 50, 12, 48], [17, 52, 12, 50, "mode"], [17, 56, 12, 54], [17, 58, 12, 56, "localMatrix"], [17, 69, 12, 67], [17, 71, 12, 69, "flags"], [17, 76, 12, 74], [17, 78, 12, 76], [18, 6, 13, 4], [18, 13, 13, 11], [18, 17, 13, 15, "JsiSkShader"], [18, 41, 13, 26], [18, 42, 13, 27], [18, 46, 13, 31], [18, 47, 13, 32, "CanvasKit"], [18, 56, 13, 41], [18, 58, 13, 43], [18, 62, 13, 47], [18, 63, 13, 48, "CanvasKit"], [18, 72, 13, 57], [18, 73, 13, 58, "Shader"], [18, 79, 13, 64], [18, 80, 13, 65, "MakeRadialGradient"], [18, 98, 13, 83], [18, 99, 13, 84, "JsiSkPoint"], [18, 121, 13, 94], [18, 122, 13, 95, "fromValue"], [18, 131, 13, 104], [18, 132, 13, 105, "center"], [18, 138, 13, 111], [18, 139, 13, 112], [18, 141, 13, 114, "radius"], [18, 147, 13, 120], [18, 149, 13, 122, "colors"], [18, 155, 13, 128], [18, 157, 13, 130, "pos"], [18, 160, 13, 133], [18, 162, 13, 135], [18, 166, 13, 135, "getEnum"], [18, 179, 13, 142], [18, 181, 13, 143], [18, 185, 13, 147], [18, 186, 13, 148, "CanvasKit"], [18, 195, 13, 157], [18, 197, 13, 159], [18, 207, 13, 169], [18, 209, 13, 171, "mode"], [18, 213, 13, 175], [18, 214, 13, 176], [18, 216, 13, 178, "localMatrix"], [18, 227, 13, 189], [18, 232, 13, 194, "undefined"], [18, 241, 13, 203], [18, 244, 13, 206, "undefined"], [18, 253, 13, 215], [18, 256, 13, 218, "JsiSkMatrix"], [18, 280, 13, 229], [18, 281, 13, 230, "fromValue"], [18, 290, 13, 239], [18, 291, 13, 240, "localMatrix"], [18, 302, 13, 251], [18, 303, 13, 252], [18, 305, 13, 254, "flags"], [18, 310, 13, 259], [18, 311, 13, 260], [18, 312, 13, 261], [19, 4, 14, 2], [20, 4, 15, 2, "MakeTwoPointConicalGradient"], [20, 31, 15, 29, "MakeTwoPointConicalGradient"], [20, 32, 15, 30, "start"], [20, 37, 15, 35], [20, 39, 15, 37, "startRadius"], [20, 50, 15, 48], [20, 52, 15, 50, "end"], [20, 55, 15, 53], [20, 57, 15, 55, "endRadius"], [20, 66, 15, 64], [20, 68, 15, 66, "colors"], [20, 74, 15, 72], [20, 76, 15, 74, "pos"], [20, 79, 15, 77], [20, 81, 15, 79, "mode"], [20, 85, 15, 83], [20, 87, 15, 85, "localMatrix"], [20, 98, 15, 96], [20, 100, 15, 98, "flags"], [20, 105, 15, 103], [20, 107, 15, 105], [21, 6, 16, 4], [21, 13, 16, 11], [21, 17, 16, 15, "JsiSkShader"], [21, 41, 16, 26], [21, 42, 16, 27], [21, 46, 16, 31], [21, 47, 16, 32, "CanvasKit"], [21, 56, 16, 41], [21, 58, 16, 43], [21, 62, 16, 47], [21, 63, 16, 48, "CanvasKit"], [21, 72, 16, 57], [21, 73, 16, 58, "Shader"], [21, 79, 16, 64], [21, 80, 16, 65, "MakeTwoPointConicalGradient"], [21, 107, 16, 92], [21, 108, 16, 93, "JsiSkPoint"], [21, 130, 16, 103], [21, 131, 16, 104, "fromValue"], [21, 140, 16, 113], [21, 141, 16, 114, "start"], [21, 146, 16, 119], [21, 147, 16, 120], [21, 149, 16, 122, "startRadius"], [21, 160, 16, 133], [21, 162, 16, 135, "JsiSkPoint"], [21, 184, 16, 145], [21, 185, 16, 146, "fromValue"], [21, 194, 16, 155], [21, 195, 16, 156, "end"], [21, 198, 16, 159], [21, 199, 16, 160], [21, 201, 16, 162, "endRadius"], [21, 210, 16, 171], [21, 212, 16, 173, "colors"], [21, 218, 16, 179], [21, 220, 16, 181, "pos"], [21, 223, 16, 184], [21, 225, 16, 186], [21, 229, 16, 186, "getEnum"], [21, 242, 16, 193], [21, 244, 16, 194], [21, 248, 16, 198], [21, 249, 16, 199, "CanvasKit"], [21, 258, 16, 208], [21, 260, 16, 210], [21, 270, 16, 220], [21, 272, 16, 222, "mode"], [21, 276, 16, 226], [21, 277, 16, 227], [21, 279, 16, 229, "localMatrix"], [21, 290, 16, 240], [21, 295, 16, 245, "undefined"], [21, 304, 16, 254], [21, 307, 16, 257, "undefined"], [21, 316, 16, 266], [21, 319, 16, 269, "JsiSkMatrix"], [21, 343, 16, 280], [21, 344, 16, 281, "fromValue"], [21, 353, 16, 290], [21, 354, 16, 291, "localMatrix"], [21, 365, 16, 302], [21, 366, 16, 303], [21, 368, 16, 305, "flags"], [21, 373, 16, 310], [21, 374, 16, 311], [21, 375, 16, 312], [22, 4, 17, 2], [23, 4, 18, 2, "MakeSweepGradient"], [23, 21, 18, 19, "MakeSweepGradient"], [23, 22, 18, 20, "cx"], [23, 24, 18, 22], [23, 26, 18, 24, "cy"], [23, 28, 18, 26], [23, 30, 18, 28, "colors"], [23, 36, 18, 34], [23, 38, 18, 36, "pos"], [23, 41, 18, 39], [23, 43, 18, 41, "mode"], [23, 47, 18, 45], [23, 49, 18, 47, "localMatrix"], [23, 60, 18, 58], [23, 62, 18, 60, "flags"], [23, 67, 18, 65], [23, 69, 18, 67, "startAngleInDegrees"], [23, 88, 18, 86], [23, 90, 18, 88, "endAngleInDegrees"], [23, 107, 18, 105], [23, 109, 18, 107], [24, 6, 19, 4], [24, 13, 19, 11], [24, 17, 19, 15, "JsiSkShader"], [24, 41, 19, 26], [24, 42, 19, 27], [24, 46, 19, 31], [24, 47, 19, 32, "CanvasKit"], [24, 56, 19, 41], [24, 58, 19, 43], [24, 62, 19, 47], [24, 63, 19, 48, "CanvasKit"], [24, 72, 19, 57], [24, 73, 19, 58, "Shader"], [24, 79, 19, 64], [24, 80, 19, 65, "MakeSweepGradient"], [24, 97, 19, 82], [24, 98, 19, 83, "cx"], [24, 100, 19, 85], [24, 102, 19, 87, "cy"], [24, 104, 19, 89], [24, 106, 19, 91, "colors"], [24, 112, 19, 97], [24, 114, 19, 99, "pos"], [24, 117, 19, 102], [24, 119, 19, 104], [24, 123, 19, 104, "getEnum"], [24, 136, 19, 111], [24, 138, 19, 112], [24, 142, 19, 116], [24, 143, 19, 117, "CanvasKit"], [24, 152, 19, 126], [24, 154, 19, 128], [24, 164, 19, 138], [24, 166, 19, 140, "mode"], [24, 170, 19, 144], [24, 171, 19, 145], [24, 173, 19, 147, "localMatrix"], [24, 184, 19, 158], [24, 189, 19, 163, "undefined"], [24, 198, 19, 172], [24, 202, 19, 176, "localMatrix"], [24, 213, 19, 187], [24, 218, 19, 192], [24, 222, 19, 196], [24, 225, 19, 199, "undefined"], [24, 234, 19, 208], [24, 237, 19, 211, "JsiSkMatrix"], [24, 261, 19, 222], [24, 262, 19, 223, "fromValue"], [24, 271, 19, 232], [24, 272, 19, 233, "localMatrix"], [24, 283, 19, 244], [24, 284, 19, 245], [24, 286, 19, 247, "flags"], [24, 291, 19, 252], [24, 293, 19, 254, "startAngleInDegrees"], [24, 312, 19, 273], [24, 314, 19, 275, "endAngleInDegrees"], [24, 331, 19, 292], [24, 332, 19, 293], [24, 333, 19, 294], [25, 4, 20, 2], [26, 4, 21, 2, "MakeTurbulence"], [26, 18, 21, 16, "MakeTurbulence"], [26, 19, 21, 17, "baseFreqX"], [26, 28, 21, 26], [26, 30, 21, 28, "baseFreqY"], [26, 39, 21, 37], [26, 41, 21, 39, "octaves"], [26, 48, 21, 46], [26, 50, 21, 48, "seed"], [26, 54, 21, 52], [26, 56, 21, 54, "tileW"], [26, 61, 21, 59], [26, 63, 21, 61, "tileH"], [26, 68, 21, 66], [26, 70, 21, 68], [27, 6, 22, 4], [27, 13, 22, 11], [27, 17, 22, 15, "JsiSkShader"], [27, 41, 22, 26], [27, 42, 22, 27], [27, 46, 22, 31], [27, 47, 22, 32, "CanvasKit"], [27, 56, 22, 41], [27, 58, 22, 43], [27, 62, 22, 47], [27, 63, 22, 48, "CanvasKit"], [27, 72, 22, 57], [27, 73, 22, 58, "Shader"], [27, 79, 22, 64], [27, 80, 22, 65, "MakeTurbulence"], [27, 94, 22, 79], [27, 95, 22, 80, "baseFreqX"], [27, 104, 22, 89], [27, 106, 22, 91, "baseFreqY"], [27, 115, 22, 100], [27, 117, 22, 102, "octaves"], [27, 124, 22, 109], [27, 126, 22, 111, "seed"], [27, 130, 22, 115], [27, 132, 22, 117, "tileW"], [27, 137, 22, 122], [27, 139, 22, 124, "tileH"], [27, 144, 22, 129], [27, 145, 22, 130], [27, 146, 22, 131], [28, 4, 23, 2], [29, 4, 24, 2, "MakeFractalNoise"], [29, 20, 24, 18, "MakeFractalNoise"], [29, 21, 24, 19, "baseFreqX"], [29, 30, 24, 28], [29, 32, 24, 30, "baseFreqY"], [29, 41, 24, 39], [29, 43, 24, 41, "octaves"], [29, 50, 24, 48], [29, 52, 24, 50, "seed"], [29, 56, 24, 54], [29, 58, 24, 56, "tileW"], [29, 63, 24, 61], [29, 65, 24, 63, "tileH"], [29, 70, 24, 68], [29, 72, 24, 70], [30, 6, 25, 4], [30, 13, 25, 11], [30, 17, 25, 15, "JsiSkShader"], [30, 41, 25, 26], [30, 42, 25, 27], [30, 46, 25, 31], [30, 47, 25, 32, "CanvasKit"], [30, 56, 25, 41], [30, 58, 25, 43], [30, 62, 25, 47], [30, 63, 25, 48, "CanvasKit"], [30, 72, 25, 57], [30, 73, 25, 58, "Shader"], [30, 79, 25, 64], [30, 80, 25, 65, "MakeFractalNoise"], [30, 96, 25, 81], [30, 97, 25, 82, "baseFreqX"], [30, 106, 25, 91], [30, 108, 25, 93, "baseFreqY"], [30, 117, 25, 102], [30, 119, 25, 104, "octaves"], [30, 126, 25, 111], [30, 128, 25, 113, "seed"], [30, 132, 25, 117], [30, 134, 25, 119, "tileW"], [30, 139, 25, 124], [30, 141, 25, 126, "tileH"], [30, 146, 25, 131], [30, 147, 25, 132], [30, 148, 25, 133], [31, 4, 26, 2], [32, 4, 27, 2, "MakeBlend"], [32, 13, 27, 11, "MakeBlend"], [32, 14, 27, 12, "mode"], [32, 18, 27, 16], [32, 20, 27, 18, "one"], [32, 23, 27, 21], [32, 25, 27, 23, "two"], [32, 28, 27, 26], [32, 30, 27, 28], [33, 6, 28, 4], [33, 13, 28, 11], [33, 17, 28, 15, "JsiSkShader"], [33, 41, 28, 26], [33, 42, 28, 27], [33, 46, 28, 31], [33, 47, 28, 32, "CanvasKit"], [33, 56, 28, 41], [33, 58, 28, 43], [33, 62, 28, 47], [33, 63, 28, 48, "CanvasKit"], [33, 72, 28, 57], [33, 73, 28, 58, "Shader"], [33, 79, 28, 64], [33, 80, 28, 65, "MakeBlend"], [33, 89, 28, 74], [33, 90, 28, 75], [33, 94, 28, 75, "getEnum"], [33, 107, 28, 82], [33, 109, 28, 83], [33, 113, 28, 87], [33, 114, 28, 88, "CanvasKit"], [33, 123, 28, 97], [33, 125, 28, 99], [33, 136, 28, 110], [33, 138, 28, 112, "mode"], [33, 142, 28, 116], [33, 143, 28, 117], [33, 145, 28, 119, "JsiSkShader"], [33, 169, 28, 130], [33, 170, 28, 131, "fromValue"], [33, 179, 28, 140], [33, 180, 28, 141, "one"], [33, 183, 28, 144], [33, 184, 28, 145], [33, 186, 28, 147, "JsiSkShader"], [33, 210, 28, 158], [33, 211, 28, 159, "fromValue"], [33, 220, 28, 168], [33, 221, 28, 169, "two"], [33, 224, 28, 172], [33, 225, 28, 173], [33, 226, 28, 174], [33, 227, 28, 175], [34, 4, 29, 2], [35, 4, 30, 2, "MakeColor"], [35, 13, 30, 11, "MakeColor"], [35, 14, 30, 12, "color"], [35, 19, 30, 17], [35, 21, 30, 19], [36, 6, 31, 4], [36, 13, 31, 11], [36, 17, 31, 15, "JsiSkShader"], [36, 41, 31, 26], [36, 42, 31, 27], [36, 46, 31, 31], [36, 47, 31, 32, "CanvasKit"], [36, 56, 31, 41], [36, 58, 31, 43], [36, 62, 31, 47], [36, 63, 31, 48, "CanvasKit"], [36, 72, 31, 57], [36, 73, 31, 58, "Shader"], [36, 79, 31, 64], [36, 80, 31, 65, "MakeColor"], [36, 89, 31, 74], [36, 90, 31, 75, "color"], [36, 95, 31, 80], [36, 97, 31, 82], [36, 101, 31, 86], [36, 102, 31, 87, "CanvasKit"], [36, 111, 31, 96], [36, 112, 31, 97, "ColorSpace"], [36, 122, 31, 107], [36, 123, 31, 108, "SRGB"], [36, 127, 31, 112], [36, 128, 31, 113], [36, 129, 31, 114], [37, 4, 32, 2], [38, 2, 33, 0], [39, 2, 33, 1, "exports"], [39, 9, 33, 1], [39, 10, 33, 1, "JsiSkShaderFactory"], [39, 28, 33, 1], [39, 31, 33, 1, "JsiSkShaderFactory"], [39, 49, 33, 1], [40, 0, 33, 1], [40, 3]], "functionMap": {"names": ["<global>", "JsiSkShaderFactory", "constructor", "MakeLinearGradient", "MakeRadialGradient", "MakeTwoPointConicalGradient", "MakeSweepGradient", "MakeTurbulence", "MakeFractalNoise", "MakeBlend", "MakeColor"], "mappings": "AAA;OCI;ECC;GDE;EEC;GFE;EGC;GHE;EIC;GJE;EKC;GLE;EMC;GNE;EOC;GPE;EQC;GRE;ESC;GTE;CDC"}}, "type": "js/module"}]}