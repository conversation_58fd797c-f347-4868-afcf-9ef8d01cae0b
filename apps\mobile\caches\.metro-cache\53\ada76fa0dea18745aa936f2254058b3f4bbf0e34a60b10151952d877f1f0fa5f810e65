{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 30, "index": 30}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkPicture", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 31}, "end": {"line": 2, "column": 46, "index": 77}}], "key": "/AN6zwESuLHK8aJTwDD/mo1rd2w=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkPictureFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkPicture = require(_dependencyMap[1], \"./JsiSkPicture\");\n  class JsiSkPictureFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    MakePicture(bytes) {\n      const pic = this.CanvasKit.MakePicture(bytes);\n      if (pic === null) {\n        return null;\n      }\n      return new _JsiSkPicture.JsiSkPicture(this.CanvasKit, pic);\n    }\n  }\n  exports.JsiSkPictureFactory = JsiSkPictureFactory;\n});", "lineCount": 21, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkPicture"], [7, 19, 2, 0], [7, 22, 2, 0, "require"], [7, 29, 2, 0], [7, 30, 2, 0, "_dependencyMap"], [7, 44, 2, 0], [8, 2, 3, 7], [8, 8, 3, 13, "JsiSkPictureFactory"], [8, 27, 3, 32], [8, 36, 3, 41, "Host"], [8, 46, 3, 45], [8, 47, 3, 46], [9, 4, 4, 2, "constructor"], [9, 15, 4, 13, "constructor"], [9, 16, 4, 14, "CanvasKit"], [9, 25, 4, 23], [9, 27, 4, 25], [10, 6, 5, 4], [10, 11, 5, 9], [10, 12, 5, 10, "CanvasKit"], [10, 21, 5, 19], [10, 22, 5, 20], [11, 4, 6, 2], [12, 4, 7, 2, "MakePicture"], [12, 15, 7, 13, "MakePicture"], [12, 16, 7, 14, "bytes"], [12, 21, 7, 19], [12, 23, 7, 21], [13, 6, 8, 4], [13, 12, 8, 10, "pic"], [13, 15, 8, 13], [13, 18, 8, 16], [13, 22, 8, 20], [13, 23, 8, 21, "CanvasKit"], [13, 32, 8, 30], [13, 33, 8, 31, "MakePicture"], [13, 44, 8, 42], [13, 45, 8, 43, "bytes"], [13, 50, 8, 48], [13, 51, 8, 49], [14, 6, 9, 4], [14, 10, 9, 8, "pic"], [14, 13, 9, 11], [14, 18, 9, 16], [14, 22, 9, 20], [14, 24, 9, 22], [15, 8, 10, 6], [15, 15, 10, 13], [15, 19, 10, 17], [16, 6, 11, 4], [17, 6, 12, 4], [17, 13, 12, 11], [17, 17, 12, 15, "JsiSkPicture"], [17, 43, 12, 27], [17, 44, 12, 28], [17, 48, 12, 32], [17, 49, 12, 33, "CanvasKit"], [17, 58, 12, 42], [17, 60, 12, 44, "pic"], [17, 63, 12, 47], [17, 64, 12, 48], [18, 4, 13, 2], [19, 2, 14, 0], [20, 2, 14, 1, "exports"], [20, 9, 14, 1], [20, 10, 14, 1, "JsiSkPictureFactory"], [20, 29, 14, 1], [20, 32, 14, 1, "JsiSkPictureFactory"], [20, 51, 14, 1], [21, 0, 14, 1], [21, 3]], "functionMap": {"names": ["<global>", "JsiSkPictureFactory", "constructor", "MakePicture"], "mappings": "AAA;OCE;ECC;GDE;EEC;GFM;CDC"}}, "type": "js/module"}]}