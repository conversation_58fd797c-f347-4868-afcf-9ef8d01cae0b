{"dependencies": [{"name": "../../ImageManipulator.types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 68, "index": 68}}], "key": "i0qcO3JYlnCXP2vHzq06nHKN3o8=", "exportNames": ["*"]}}, {"name": "../utils.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 69}, "end": {"line": 2, "column": 42, "index": 111}}], "key": "V6Cd8v/K/LtdxP2wf1WD5HCNhqM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _ImageManipulator = require(_dependencyMap[0], \"../../ImageManipulator.types\");\n  var _utils = require(_dependencyMap[1], \"../utils.web\");\n  var _default = (canvas, flip) => {\n    const xFlip = flip === _ImageManipulator.FlipType.Horizontal;\n    const yFlip = flip === _ImageManipulator.FlipType.Vertical;\n    const result = document.createElement('canvas');\n    result.width = canvas.width;\n    result.height = canvas.height;\n    const context = (0, _utils.getContext)(result);\n\n    // Set the origin to the center of the image\n    context.translate(canvas.width / 2, canvas.height / 2);\n\n    // Flip/flop the canvas\n    const xScale = xFlip ? -1 : 1;\n    const yScale = yFlip ? -1 : 1;\n    context.scale(xScale, yScale);\n\n    // Draw the image\n    context.drawImage(canvas, -canvas.width / 2, -canvas.height / 2, canvas.width, canvas.height);\n    return result;\n  };\n  exports.default = _default;\n});", "lineCount": 29, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_ImageManipulator"], [6, 23, 1, 0], [6, 26, 1, 0, "require"], [6, 33, 1, 0], [6, 34, 1, 0, "_dependencyMap"], [6, 48, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_utils"], [7, 12, 2, 0], [7, 15, 2, 0, "require"], [7, 22, 2, 0], [7, 23, 2, 0, "_dependencyMap"], [7, 37, 2, 0], [8, 2, 2, 42], [8, 6, 2, 42, "_default"], [8, 14, 2, 42], [8, 17, 4, 15, "_default"], [8, 18, 4, 16, "canvas"], [8, 24, 4, 41], [8, 26, 4, 43, "flip"], [8, 30, 4, 67], [8, 35, 4, 72], [9, 4, 5, 2], [9, 10, 5, 8, "xFlip"], [9, 15, 5, 13], [9, 18, 5, 16, "flip"], [9, 22, 5, 20], [9, 27, 5, 25, "FlipType"], [9, 53, 5, 33], [9, 54, 5, 34, "Horizontal"], [9, 64, 5, 44], [10, 4, 6, 2], [10, 10, 6, 8, "yFlip"], [10, 15, 6, 13], [10, 18, 6, 16, "flip"], [10, 22, 6, 20], [10, 27, 6, 25, "FlipType"], [10, 53, 6, 33], [10, 54, 6, 34, "Vertical"], [10, 62, 6, 42], [11, 4, 8, 2], [11, 10, 8, 8, "result"], [11, 16, 8, 14], [11, 19, 8, 17, "document"], [11, 27, 8, 25], [11, 28, 8, 26, "createElement"], [11, 41, 8, 39], [11, 42, 8, 40], [11, 50, 8, 48], [11, 51, 8, 49], [12, 4, 9, 2, "result"], [12, 10, 9, 8], [12, 11, 9, 9, "width"], [12, 16, 9, 14], [12, 19, 9, 17, "canvas"], [12, 25, 9, 23], [12, 26, 9, 24, "width"], [12, 31, 9, 29], [13, 4, 10, 2, "result"], [13, 10, 10, 8], [13, 11, 10, 9, "height"], [13, 17, 10, 15], [13, 20, 10, 18, "canvas"], [13, 26, 10, 24], [13, 27, 10, 25, "height"], [13, 33, 10, 31], [14, 4, 12, 2], [14, 10, 12, 8, "context"], [14, 17, 12, 15], [14, 20, 12, 18], [14, 24, 12, 18, "getContext"], [14, 41, 12, 28], [14, 43, 12, 29, "result"], [14, 49, 12, 35], [14, 50, 12, 36], [16, 4, 14, 2], [17, 4, 15, 2, "context"], [17, 11, 15, 9], [17, 12, 15, 10, "translate"], [17, 21, 15, 19], [17, 22, 15, 20, "canvas"], [17, 28, 15, 26], [17, 29, 15, 27, "width"], [17, 34, 15, 32], [17, 37, 15, 35], [17, 38, 15, 36], [17, 40, 15, 38, "canvas"], [17, 46, 15, 44], [17, 47, 15, 45, "height"], [17, 53, 15, 51], [17, 56, 15, 54], [17, 57, 15, 55], [17, 58, 15, 56], [19, 4, 17, 2], [20, 4, 18, 2], [20, 10, 18, 8, "xScale"], [20, 16, 18, 14], [20, 19, 18, 17, "xFlip"], [20, 24, 18, 22], [20, 27, 18, 25], [20, 28, 18, 26], [20, 29, 18, 27], [20, 32, 18, 30], [20, 33, 18, 31], [21, 4, 19, 2], [21, 10, 19, 8, "yScale"], [21, 16, 19, 14], [21, 19, 19, 17, "yFlip"], [21, 24, 19, 22], [21, 27, 19, 25], [21, 28, 19, 26], [21, 29, 19, 27], [21, 32, 19, 30], [21, 33, 19, 31], [22, 4, 20, 2, "context"], [22, 11, 20, 9], [22, 12, 20, 10, "scale"], [22, 17, 20, 15], [22, 18, 20, 16, "xScale"], [22, 24, 20, 22], [22, 26, 20, 24, "yScale"], [22, 32, 20, 30], [22, 33, 20, 31], [24, 4, 22, 2], [25, 4, 23, 2, "context"], [25, 11, 23, 9], [25, 12, 23, 10, "drawImage"], [25, 21, 23, 19], [25, 22, 23, 20, "canvas"], [25, 28, 23, 26], [25, 30, 23, 28], [25, 31, 23, 29, "canvas"], [25, 37, 23, 35], [25, 38, 23, 36, "width"], [25, 43, 23, 41], [25, 46, 23, 44], [25, 47, 23, 45], [25, 49, 23, 47], [25, 50, 23, 48, "canvas"], [25, 56, 23, 54], [25, 57, 23, 55, "height"], [25, 63, 23, 61], [25, 66, 23, 64], [25, 67, 23, 65], [25, 69, 23, 67, "canvas"], [25, 75, 23, 73], [25, 76, 23, 74, "width"], [25, 81, 23, 79], [25, 83, 23, 81, "canvas"], [25, 89, 23, 87], [25, 90, 23, 88, "height"], [25, 96, 23, 94], [25, 97, 23, 95], [26, 4, 25, 2], [26, 11, 25, 9, "result"], [26, 17, 25, 15], [27, 2, 26, 0], [27, 3, 26, 1], [28, 2, 26, 1, "exports"], [28, 9, 26, 1], [28, 10, 26, 1, "default"], [28, 17, 26, 1], [28, 20, 26, 1, "_default"], [28, 28, 26, 1], [29, 0, 26, 1], [29, 3]], "functionMap": {"names": ["<global>", "default"], "mappings": "AAA;eCG;CDsB"}}, "type": "js/module"}]}