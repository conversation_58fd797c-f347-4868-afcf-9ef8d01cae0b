{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 30, "index": 30}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkFontMgr", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 31}, "end": {"line": 2, "column": 46, "index": 77}}], "key": "BxhiMIxQxmn291Df5HgBnnOupgg=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkFontMgrFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkFontMgr = require(_dependencyMap[1], \"./JsiSkFontMgr\");\n  class JsiSkFontMgrFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    System() {\n      const fontMgr = this.CanvasKit.TypefaceFontProvider.Make();\n      if (!fontMgr) {\n        throw new Error(\"Couldn't create system font manager\");\n      }\n      return new _JsiSkFontMgr.JsiSkFontMgr(this.CanvasKit, fontMgr);\n    }\n  }\n  exports.JsiSkFontMgrFactory = JsiSkFontMgrFactory;\n});", "lineCount": 21, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkFontMgr"], [7, 19, 2, 0], [7, 22, 2, 0, "require"], [7, 29, 2, 0], [7, 30, 2, 0, "_dependencyMap"], [7, 44, 2, 0], [8, 2, 3, 7], [8, 8, 3, 13, "JsiSkFontMgrFactory"], [8, 27, 3, 32], [8, 36, 3, 41, "Host"], [8, 46, 3, 45], [8, 47, 3, 46], [9, 4, 4, 2, "constructor"], [9, 15, 4, 13, "constructor"], [9, 16, 4, 14, "CanvasKit"], [9, 25, 4, 23], [9, 27, 4, 25], [10, 6, 5, 4], [10, 11, 5, 9], [10, 12, 5, 10, "CanvasKit"], [10, 21, 5, 19], [10, 22, 5, 20], [11, 4, 6, 2], [12, 4, 7, 2, "System"], [12, 10, 7, 8, "System"], [12, 11, 7, 8], [12, 13, 7, 11], [13, 6, 8, 4], [13, 12, 8, 10, "fontMgr"], [13, 19, 8, 17], [13, 22, 8, 20], [13, 26, 8, 24], [13, 27, 8, 25, "CanvasKit"], [13, 36, 8, 34], [13, 37, 8, 35, "TypefaceFontProvider"], [13, 57, 8, 55], [13, 58, 8, 56, "Make"], [13, 62, 8, 60], [13, 63, 8, 61], [13, 64, 8, 62], [14, 6, 9, 4], [14, 10, 9, 8], [14, 11, 9, 9, "fontMgr"], [14, 18, 9, 16], [14, 20, 9, 18], [15, 8, 10, 6], [15, 14, 10, 12], [15, 18, 10, 16, "Error"], [15, 23, 10, 21], [15, 24, 10, 22], [15, 61, 10, 59], [15, 62, 10, 60], [16, 6, 11, 4], [17, 6, 12, 4], [17, 13, 12, 11], [17, 17, 12, 15, "JsiSkFontMgr"], [17, 43, 12, 27], [17, 44, 12, 28], [17, 48, 12, 32], [17, 49, 12, 33, "CanvasKit"], [17, 58, 12, 42], [17, 60, 12, 44, "fontMgr"], [17, 67, 12, 51], [17, 68, 12, 52], [18, 4, 13, 2], [19, 2, 14, 0], [20, 2, 14, 1, "exports"], [20, 9, 14, 1], [20, 10, 14, 1, "JsiSkFontMgrFactory"], [20, 29, 14, 1], [20, 32, 14, 1, "JsiSkFontMgrFactory"], [20, 51, 14, 1], [21, 0, 14, 1], [21, 3]], "functionMap": {"names": ["<global>", "JsiSkFontMgrFactory", "constructor", "System"], "mappings": "AAA;OCE;ECC;GDE;EEC;GFM;CDC"}}, "type": "js/module"}]}