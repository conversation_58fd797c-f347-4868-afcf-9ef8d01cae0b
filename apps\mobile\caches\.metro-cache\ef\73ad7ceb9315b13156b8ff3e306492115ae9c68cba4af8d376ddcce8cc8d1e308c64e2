{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  /**\n   * @license React\n   * react-reconciler-constants.development.js\n   *\n   * Copyright (c) Meta Platforms, Inc. and affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  \"use strict\";\n\n  \"production\" !== process.env.NODE_ENV && (exports.ConcurrentRoot = 1, exports.ContinuousEventPriority = 8, exports.DefaultEventPriority = 32, exports.DiscreteEventPriority = 2, exports.IdleEventPriority = 268435456, exports.LegacyRoot = 0, exports.NoEventPriority = 0);\n});", "lineCount": 15, "map": [[2, 2, 1, 0], [3, 0, 2, 0], [4, 0, 3, 0], [5, 0, 4, 0], [6, 0, 5, 0], [7, 0, 6, 0], [8, 0, 7, 0], [9, 0, 8, 0], [10, 0, 9, 0], [12, 2, 11, 0], [12, 14, 11, 12], [14, 2, 12, 0], [14, 14, 12, 12], [14, 19, 12, 17, "process"], [14, 26, 12, 24], [14, 27, 12, 25, "env"], [14, 30, 12, 28], [14, 31, 12, 29, "NODE_ENV"], [14, 39, 12, 37], [14, 44, 13, 4, "exports"], [14, 51, 13, 11], [14, 52, 13, 12, "ConcurrentRoot"], [14, 66, 13, 26], [14, 69, 13, 29], [14, 70, 13, 30], [14, 72, 14, 3, "exports"], [14, 79, 14, 10], [14, 80, 14, 11, "ContinuousEventPriority"], [14, 103, 14, 34], [14, 106, 14, 37], [14, 107, 14, 38], [14, 109, 15, 3, "exports"], [14, 116, 15, 10], [14, 117, 15, 11, "DefaultEventPriority"], [14, 137, 15, 31], [14, 140, 15, 34], [14, 142, 15, 36], [14, 144, 16, 3, "exports"], [14, 151, 16, 10], [14, 152, 16, 11, "DiscreteEventPriority"], [14, 173, 16, 32], [14, 176, 16, 35], [14, 177, 16, 36], [14, 179, 17, 3, "exports"], [14, 186, 17, 10], [14, 187, 17, 11, "IdleEventPriority"], [14, 204, 17, 28], [14, 207, 17, 31], [14, 216, 17, 40], [14, 218, 18, 3, "exports"], [14, 225, 18, 10], [14, 226, 18, 11, "LegacyRoot"], [14, 236, 18, 21], [14, 239, 18, 24], [14, 240, 18, 25], [14, 242, 19, 3, "exports"], [14, 249, 19, 10], [14, 250, 19, 11, "NoEventPriority"], [14, 265, 19, 26], [14, 268, 19, 29], [14, 269, 19, 31], [14, 270, 19, 32], [15, 0, 19, 33], [15, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}