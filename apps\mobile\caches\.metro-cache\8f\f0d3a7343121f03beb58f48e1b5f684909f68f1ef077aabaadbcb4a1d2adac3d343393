{"dependencies": [{"name": "../types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 67, "index": 67}}], "key": "SiqkZ9nARqNkdXfcIWbBgsKp5Yo=", "exportNames": ["*"]}}, {"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 68}, "end": {"line": 2, "column": 67, "index": 135}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkImage", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 136}, "end": {"line": 3, "column": 42, "index": 178}}], "key": "5HhJKBZpkVLC59VDnsBNlnwB3DE=", "exportNames": ["*"]}}, {"name": "./JsiSkData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 179}, "end": {"line": 4, "column": 40, "index": 219}}], "key": "c+biP0KCYKcLc6xq5NFUJB5wGKM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkImageFactory = void 0;\n  var _types = require(_dependencyMap[0], \"../types\");\n  var _Host = require(_dependencyMap[1], \"./Host\");\n  var _JsiSkImage = require(_dependencyMap[2], \"./JsiSkImage\");\n  var _JsiSkData = require(_dependencyMap[3], \"./JsiSkData\");\n  class JsiSkImageFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    MakeImageFromViewTag(viewTag) {\n      const view = viewTag;\n      // TODO: Implement screenshot from view in React JS\n      console.log(view);\n      return Promise.resolve(null);\n    }\n    MakeImageFromNativeBuffer(buffer, surface, image) {\n      if (!(0, _types.isNativeBufferWeb)(buffer)) {\n        throw new Error(\"Invalid NativeBuffer\");\n      }\n      if (!surface) {\n        let img;\n        if (buffer instanceof HTMLImageElement || buffer instanceof HTMLVideoElement || buffer instanceof ImageBitmap) {\n          img = this.CanvasKit.MakeLazyImageFromTextureSource(buffer);\n        } else if (buffer instanceof _types.CanvasKitWebGLBuffer) {\n          img = buffer.toImage();\n        } else {\n          img = this.CanvasKit.MakeImageFromCanvasImageSource(buffer);\n        }\n        return new _JsiSkImage.JsiSkImage(this.CanvasKit, img);\n      } else if (!image) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const img = surface.makeImageFromTextureSource(buffer);\n        return new _JsiSkImage.JsiSkImage(this.CanvasKit, img);\n      } else {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const img = surface.updateTextureFromSource(image, buffer);\n        return new _JsiSkImage.JsiSkImage(this.CanvasKit, img);\n      }\n    }\n    MakeImageFromEncoded(encoded) {\n      const image = this.CanvasKit.MakeImageFromEncoded(_JsiSkData.JsiSkData.fromValue(encoded));\n      if (image === null) {\n        return null;\n      }\n      return new _JsiSkImage.JsiSkImage(this.CanvasKit, image);\n    }\n    MakeImageFromNativeTextureUnstable() {\n      return (0, _Host.throwNotImplementedOnRNWeb)();\n    }\n    MakeImage(info, data, bytesPerRow) {\n      // see toSkImageInfo() from canvaskit\n      const image = this.CanvasKit.MakeImage({\n        alphaType: (0, _Host.getEnum)(this.CanvasKit, \"AlphaType\", info.alphaType),\n        colorSpace: this.CanvasKit.ColorSpace.SRGB,\n        colorType: (0, _Host.getEnum)(this.CanvasKit, \"ColorType\", info.colorType),\n        height: info.height,\n        width: info.width\n      }, _JsiSkData.JsiSkData.fromValue(data), bytesPerRow);\n      if (image === null) {\n        return null;\n      }\n      return new _JsiSkImage.JsiSkImage(this.CanvasKit, image);\n    }\n  }\n  exports.JsiSkImageFactory = JsiSkImageFactory;\n});", "lineCount": 70, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_types"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_Host"], [7, 11, 2, 0], [7, 14, 2, 0, "require"], [7, 21, 2, 0], [7, 22, 2, 0, "_dependencyMap"], [7, 36, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_JsiSkImage"], [8, 17, 3, 0], [8, 20, 3, 0, "require"], [8, 27, 3, 0], [8, 28, 3, 0, "_dependencyMap"], [8, 42, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_JsiSkData"], [9, 16, 4, 0], [9, 19, 4, 0, "require"], [9, 26, 4, 0], [9, 27, 4, 0, "_dependencyMap"], [9, 41, 4, 0], [10, 2, 5, 7], [10, 8, 5, 13, "JsiSkImageFactory"], [10, 25, 5, 30], [10, 34, 5, 39, "Host"], [10, 44, 5, 43], [10, 45, 5, 44], [11, 4, 6, 2, "constructor"], [11, 15, 6, 13, "constructor"], [11, 16, 6, 14, "CanvasKit"], [11, 25, 6, 23], [11, 27, 6, 25], [12, 6, 7, 4], [12, 11, 7, 9], [12, 12, 7, 10, "CanvasKit"], [12, 21, 7, 19], [12, 22, 7, 20], [13, 4, 8, 2], [14, 4, 9, 2, "MakeImageFromViewTag"], [14, 24, 9, 22, "MakeImageFromViewTag"], [14, 25, 9, 23, "viewTag"], [14, 32, 9, 30], [14, 34, 9, 32], [15, 6, 10, 4], [15, 12, 10, 10, "view"], [15, 16, 10, 14], [15, 19, 10, 17, "viewTag"], [15, 26, 10, 24], [16, 6, 11, 4], [17, 6, 12, 4, "console"], [17, 13, 12, 11], [17, 14, 12, 12, "log"], [17, 17, 12, 15], [17, 18, 12, 16, "view"], [17, 22, 12, 20], [17, 23, 12, 21], [18, 6, 13, 4], [18, 13, 13, 11, "Promise"], [18, 20, 13, 18], [18, 21, 13, 19, "resolve"], [18, 28, 13, 26], [18, 29, 13, 27], [18, 33, 13, 31], [18, 34, 13, 32], [19, 4, 14, 2], [20, 4, 15, 2, "MakeImageFromNativeBuffer"], [20, 29, 15, 27, "MakeImageFromNativeBuffer"], [20, 30, 15, 28, "buffer"], [20, 36, 15, 34], [20, 38, 15, 36, "surface"], [20, 45, 15, 43], [20, 47, 15, 45, "image"], [20, 52, 15, 50], [20, 54, 15, 52], [21, 6, 16, 4], [21, 10, 16, 8], [21, 11, 16, 9], [21, 15, 16, 9, "isNativeBufferWeb"], [21, 39, 16, 26], [21, 41, 16, 27, "buffer"], [21, 47, 16, 33], [21, 48, 16, 34], [21, 50, 16, 36], [22, 8, 17, 6], [22, 14, 17, 12], [22, 18, 17, 16, "Error"], [22, 23, 17, 21], [22, 24, 17, 22], [22, 46, 17, 44], [22, 47, 17, 45], [23, 6, 18, 4], [24, 6, 19, 4], [24, 10, 19, 8], [24, 11, 19, 9, "surface"], [24, 18, 19, 16], [24, 20, 19, 18], [25, 8, 20, 6], [25, 12, 20, 10, "img"], [25, 15, 20, 13], [26, 8, 21, 6], [26, 12, 21, 10, "buffer"], [26, 18, 21, 16], [26, 30, 21, 28, "HTMLImageElement"], [26, 46, 21, 44], [26, 50, 21, 48, "buffer"], [26, 56, 21, 54], [26, 68, 21, 66, "HTMLVideoElement"], [26, 84, 21, 82], [26, 88, 21, 86, "buffer"], [26, 94, 21, 92], [26, 106, 21, 104, "ImageBitmap"], [26, 117, 21, 115], [26, 119, 21, 117], [27, 10, 22, 8, "img"], [27, 13, 22, 11], [27, 16, 22, 14], [27, 20, 22, 18], [27, 21, 22, 19, "CanvasKit"], [27, 30, 22, 28], [27, 31, 22, 29, "MakeLazyImageFromTextureSource"], [27, 61, 22, 59], [27, 62, 22, 60, "buffer"], [27, 68, 22, 66], [27, 69, 22, 67], [28, 8, 23, 6], [28, 9, 23, 7], [28, 15, 23, 13], [28, 19, 23, 17, "buffer"], [28, 25, 23, 23], [28, 37, 23, 35, "CanvasKitWebGLBuffer"], [28, 64, 23, 55], [28, 66, 23, 57], [29, 10, 24, 8, "img"], [29, 13, 24, 11], [29, 16, 24, 14, "buffer"], [29, 22, 24, 20], [29, 23, 24, 21, "toImage"], [29, 30, 24, 28], [29, 31, 24, 29], [29, 32, 24, 30], [30, 8, 25, 6], [30, 9, 25, 7], [30, 15, 25, 13], [31, 10, 26, 8, "img"], [31, 13, 26, 11], [31, 16, 26, 14], [31, 20, 26, 18], [31, 21, 26, 19, "CanvasKit"], [31, 30, 26, 28], [31, 31, 26, 29, "MakeImageFromCanvasImageSource"], [31, 61, 26, 59], [31, 62, 26, 60, "buffer"], [31, 68, 26, 66], [31, 69, 26, 67], [32, 8, 27, 6], [33, 8, 28, 6], [33, 15, 28, 13], [33, 19, 28, 17, "JsiSkImage"], [33, 41, 28, 27], [33, 42, 28, 28], [33, 46, 28, 32], [33, 47, 28, 33, "CanvasKit"], [33, 56, 28, 42], [33, 58, 28, 44, "img"], [33, 61, 28, 47], [33, 62, 28, 48], [34, 6, 29, 4], [34, 7, 29, 5], [34, 13, 29, 11], [34, 17, 29, 15], [34, 18, 29, 16, "image"], [34, 23, 29, 21], [34, 25, 29, 23], [35, 8, 30, 6], [36, 8, 31, 6], [36, 14, 31, 12, "img"], [36, 17, 31, 15], [36, 20, 31, 18, "surface"], [36, 27, 31, 25], [36, 28, 31, 26, "makeImageFromTextureSource"], [36, 54, 31, 52], [36, 55, 31, 53, "buffer"], [36, 61, 31, 59], [36, 62, 31, 60], [37, 8, 32, 6], [37, 15, 32, 13], [37, 19, 32, 17, "JsiSkImage"], [37, 41, 32, 27], [37, 42, 32, 28], [37, 46, 32, 32], [37, 47, 32, 33, "CanvasKit"], [37, 56, 32, 42], [37, 58, 32, 44, "img"], [37, 61, 32, 47], [37, 62, 32, 48], [38, 6, 33, 4], [38, 7, 33, 5], [38, 13, 33, 11], [39, 8, 34, 6], [40, 8, 35, 6], [40, 14, 35, 12, "img"], [40, 17, 35, 15], [40, 20, 35, 18, "surface"], [40, 27, 35, 25], [40, 28, 35, 26, "updateTextureFromSource"], [40, 51, 35, 49], [40, 52, 35, 50, "image"], [40, 57, 35, 55], [40, 59, 35, 57, "buffer"], [40, 65, 35, 63], [40, 66, 35, 64], [41, 8, 36, 6], [41, 15, 36, 13], [41, 19, 36, 17, "JsiSkImage"], [41, 41, 36, 27], [41, 42, 36, 28], [41, 46, 36, 32], [41, 47, 36, 33, "CanvasKit"], [41, 56, 36, 42], [41, 58, 36, 44, "img"], [41, 61, 36, 47], [41, 62, 36, 48], [42, 6, 37, 4], [43, 4, 38, 2], [44, 4, 39, 2, "MakeImageFromEncoded"], [44, 24, 39, 22, "MakeImageFromEncoded"], [44, 25, 39, 23, "encoded"], [44, 32, 39, 30], [44, 34, 39, 32], [45, 6, 40, 4], [45, 12, 40, 10, "image"], [45, 17, 40, 15], [45, 20, 40, 18], [45, 24, 40, 22], [45, 25, 40, 23, "CanvasKit"], [45, 34, 40, 32], [45, 35, 40, 33, "MakeImageFromEncoded"], [45, 55, 40, 53], [45, 56, 40, 54, "JsiSkData"], [45, 76, 40, 63], [45, 77, 40, 64, "fromValue"], [45, 86, 40, 73], [45, 87, 40, 74, "encoded"], [45, 94, 40, 81], [45, 95, 40, 82], [45, 96, 40, 83], [46, 6, 41, 4], [46, 10, 41, 8, "image"], [46, 15, 41, 13], [46, 20, 41, 18], [46, 24, 41, 22], [46, 26, 41, 24], [47, 8, 42, 6], [47, 15, 42, 13], [47, 19, 42, 17], [48, 6, 43, 4], [49, 6, 44, 4], [49, 13, 44, 11], [49, 17, 44, 15, "JsiSkImage"], [49, 39, 44, 25], [49, 40, 44, 26], [49, 44, 44, 30], [49, 45, 44, 31, "CanvasKit"], [49, 54, 44, 40], [49, 56, 44, 42, "image"], [49, 61, 44, 47], [49, 62, 44, 48], [50, 4, 45, 2], [51, 4, 46, 2, "MakeImageFromNativeTextureUnstable"], [51, 38, 46, 36, "MakeImageFromNativeTextureUnstable"], [51, 39, 46, 36], [51, 41, 46, 39], [52, 6, 47, 4], [52, 13, 47, 11], [52, 17, 47, 11, "throwNotImplementedOnRNWeb"], [52, 49, 47, 37], [52, 51, 47, 38], [52, 52, 47, 39], [53, 4, 48, 2], [54, 4, 49, 2, "MakeImage"], [54, 13, 49, 11, "MakeImage"], [54, 14, 49, 12, "info"], [54, 18, 49, 16], [54, 20, 49, 18, "data"], [54, 24, 49, 22], [54, 26, 49, 24, "bytesPerRow"], [54, 37, 49, 35], [54, 39, 49, 37], [55, 6, 50, 4], [56, 6, 51, 4], [56, 12, 51, 10, "image"], [56, 17, 51, 15], [56, 20, 51, 18], [56, 24, 51, 22], [56, 25, 51, 23, "CanvasKit"], [56, 34, 51, 32], [56, 35, 51, 33, "MakeImage"], [56, 44, 51, 42], [56, 45, 51, 43], [57, 8, 52, 6, "alphaType"], [57, 17, 52, 15], [57, 19, 52, 17], [57, 23, 52, 17, "getEnum"], [57, 36, 52, 24], [57, 38, 52, 25], [57, 42, 52, 29], [57, 43, 52, 30, "CanvasKit"], [57, 52, 52, 39], [57, 54, 52, 41], [57, 65, 52, 52], [57, 67, 52, 54, "info"], [57, 71, 52, 58], [57, 72, 52, 59, "alphaType"], [57, 81, 52, 68], [57, 82, 52, 69], [58, 8, 53, 6, "colorSpace"], [58, 18, 53, 16], [58, 20, 53, 18], [58, 24, 53, 22], [58, 25, 53, 23, "CanvasKit"], [58, 34, 53, 32], [58, 35, 53, 33, "ColorSpace"], [58, 45, 53, 43], [58, 46, 53, 44, "SRGB"], [58, 50, 53, 48], [59, 8, 54, 6, "colorType"], [59, 17, 54, 15], [59, 19, 54, 17], [59, 23, 54, 17, "getEnum"], [59, 36, 54, 24], [59, 38, 54, 25], [59, 42, 54, 29], [59, 43, 54, 30, "CanvasKit"], [59, 52, 54, 39], [59, 54, 54, 41], [59, 65, 54, 52], [59, 67, 54, 54, "info"], [59, 71, 54, 58], [59, 72, 54, 59, "colorType"], [59, 81, 54, 68], [59, 82, 54, 69], [60, 8, 55, 6, "height"], [60, 14, 55, 12], [60, 16, 55, 14, "info"], [60, 20, 55, 18], [60, 21, 55, 19, "height"], [60, 27, 55, 25], [61, 8, 56, 6, "width"], [61, 13, 56, 11], [61, 15, 56, 13, "info"], [61, 19, 56, 17], [61, 20, 56, 18, "width"], [62, 6, 57, 4], [62, 7, 57, 5], [62, 9, 57, 7, "JsiSkData"], [62, 29, 57, 16], [62, 30, 57, 17, "fromValue"], [62, 39, 57, 26], [62, 40, 57, 27, "data"], [62, 44, 57, 31], [62, 45, 57, 32], [62, 47, 57, 34, "bytesPerRow"], [62, 58, 57, 45], [62, 59, 57, 46], [63, 6, 58, 4], [63, 10, 58, 8, "image"], [63, 15, 58, 13], [63, 20, 58, 18], [63, 24, 58, 22], [63, 26, 58, 24], [64, 8, 59, 6], [64, 15, 59, 13], [64, 19, 59, 17], [65, 6, 60, 4], [66, 6, 61, 4], [66, 13, 61, 11], [66, 17, 61, 15, "JsiSkImage"], [66, 39, 61, 25], [66, 40, 61, 26], [66, 44, 61, 30], [66, 45, 61, 31, "CanvasKit"], [66, 54, 61, 40], [66, 56, 61, 42, "image"], [66, 61, 61, 47], [66, 62, 61, 48], [67, 4, 62, 2], [68, 2, 63, 0], [69, 2, 63, 1, "exports"], [69, 9, 63, 1], [69, 10, 63, 1, "JsiSkImageFactory"], [69, 27, 63, 1], [69, 30, 63, 1, "JsiSkImageFactory"], [69, 47, 63, 1], [70, 0, 63, 1], [70, 3]], "functionMap": {"names": ["<global>", "JsiSkImageFactory", "constructor", "MakeImageFromViewTag", "MakeImageFromNativeBuffer", "MakeImageFromEncoded", "MakeImageFromNativeTextureUnstable", "MakeImage"], "mappings": "AAA;OCI;ECC;GDE;EEC;GFK;EGC;GHuB;EIC;GJM;EKC;GLE;EMC;GNa;CDC"}}, "type": "js/module"}]}