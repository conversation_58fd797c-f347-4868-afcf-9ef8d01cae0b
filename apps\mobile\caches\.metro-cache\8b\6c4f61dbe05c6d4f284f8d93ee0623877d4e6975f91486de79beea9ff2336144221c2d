{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 36, "index": 36}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkParagraph = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  class JsiSkParagraph extends _Host.HostObject {\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"Paragraph\");\n    }\n    getMinIntrinsicWidth() {\n      return this.ref.getMinIntrinsicWidth();\n    }\n    getMaxIntrinsicWidth() {\n      return this.ref.getMaxIntrinsicWidth();\n    }\n    getLongestLine() {\n      return this.ref.getLongestLine();\n    }\n    layout(width) {\n      this.ref.layout(width);\n    }\n    paint(canvas, x, y) {\n      canvas.ref.drawParagraph(this.ref, x, y);\n    }\n    getHeight() {\n      return this.ref.getHeight();\n    }\n    getMaxWidth() {\n      return this.ref.getMaxWidth();\n    }\n    getGlyphPositionAtCoordinate(x, y) {\n      return this.ref.getGlyphPositionAtCoordinate(x, y).pos;\n    }\n    getRectsForPlaceholders() {\n      return this.ref.getRectsForPlaceholders().map(({\n        rect,\n        dir\n      }) => ({\n        rect: {\n          x: rect.at(0),\n          y: rect.at(1),\n          width: rect.at(2),\n          height: rect.at(3)\n        },\n        direction: dir.value\n      }));\n    }\n    getRectsForRange(start, end) {\n      return this.ref.getRectsForRange(start, end, {\n        value: 0\n      } /** kTight */, {\n        value: 0\n      } /** kTight */).map(({\n        rect\n      }) => ({\n        x: rect[0],\n        y: rect[1],\n        width: rect[2],\n        height: rect[3]\n      }));\n    }\n    getLineMetrics() {\n      return this.ref.getLineMetrics().map((r, index) => ({\n        x: r.left,\n        y: index * r.height,\n        width: r.width,\n        height: r.height\n      }));\n    }\n    dispose() {\n      this.ref.delete();\n    }\n  }\n  exports.JsiSkParagraph = JsiSkParagraph;\n});", "lineCount": 76, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 7], [7, 8, 2, 13, "JsiSkParagraph"], [7, 22, 2, 27], [7, 31, 2, 36, "HostObject"], [7, 47, 2, 46], [7, 48, 2, 47], [8, 4, 3, 2, "constructor"], [8, 15, 3, 13, "constructor"], [8, 16, 3, 14, "CanvasKit"], [8, 25, 3, 23], [8, 27, 3, 25, "ref"], [8, 30, 3, 28], [8, 32, 3, 30], [9, 6, 4, 4], [9, 11, 4, 9], [9, 12, 4, 10, "CanvasKit"], [9, 21, 4, 19], [9, 23, 4, 21, "ref"], [9, 26, 4, 24], [9, 28, 4, 26], [9, 39, 4, 37], [9, 40, 4, 38], [10, 4, 5, 2], [11, 4, 6, 2, "getMinIntrinsicWidth"], [11, 24, 6, 22, "getMinIntrinsicWidth"], [11, 25, 6, 22], [11, 27, 6, 25], [12, 6, 7, 4], [12, 13, 7, 11], [12, 17, 7, 15], [12, 18, 7, 16, "ref"], [12, 21, 7, 19], [12, 22, 7, 20, "getMinIntrinsicWidth"], [12, 42, 7, 40], [12, 43, 7, 41], [12, 44, 7, 42], [13, 4, 8, 2], [14, 4, 9, 2, "getMaxIntrinsicWidth"], [14, 24, 9, 22, "getMaxIntrinsicWidth"], [14, 25, 9, 22], [14, 27, 9, 25], [15, 6, 10, 4], [15, 13, 10, 11], [15, 17, 10, 15], [15, 18, 10, 16, "ref"], [15, 21, 10, 19], [15, 22, 10, 20, "getMaxIntrinsicWidth"], [15, 42, 10, 40], [15, 43, 10, 41], [15, 44, 10, 42], [16, 4, 11, 2], [17, 4, 12, 2, "getLongestLine"], [17, 18, 12, 16, "getLongestLine"], [17, 19, 12, 16], [17, 21, 12, 19], [18, 6, 13, 4], [18, 13, 13, 11], [18, 17, 13, 15], [18, 18, 13, 16, "ref"], [18, 21, 13, 19], [18, 22, 13, 20, "getLongestLine"], [18, 36, 13, 34], [18, 37, 13, 35], [18, 38, 13, 36], [19, 4, 14, 2], [20, 4, 15, 2, "layout"], [20, 10, 15, 8, "layout"], [20, 11, 15, 9, "width"], [20, 16, 15, 14], [20, 18, 15, 16], [21, 6, 16, 4], [21, 10, 16, 8], [21, 11, 16, 9, "ref"], [21, 14, 16, 12], [21, 15, 16, 13, "layout"], [21, 21, 16, 19], [21, 22, 16, 20, "width"], [21, 27, 16, 25], [21, 28, 16, 26], [22, 4, 17, 2], [23, 4, 18, 2, "paint"], [23, 9, 18, 7, "paint"], [23, 10, 18, 8, "canvas"], [23, 16, 18, 14], [23, 18, 18, 16, "x"], [23, 19, 18, 17], [23, 21, 18, 19, "y"], [23, 22, 18, 20], [23, 24, 18, 22], [24, 6, 19, 4, "canvas"], [24, 12, 19, 10], [24, 13, 19, 11, "ref"], [24, 16, 19, 14], [24, 17, 19, 15, "drawParagraph"], [24, 30, 19, 28], [24, 31, 19, 29], [24, 35, 19, 33], [24, 36, 19, 34, "ref"], [24, 39, 19, 37], [24, 41, 19, 39, "x"], [24, 42, 19, 40], [24, 44, 19, 42, "y"], [24, 45, 19, 43], [24, 46, 19, 44], [25, 4, 20, 2], [26, 4, 21, 2, "getHeight"], [26, 13, 21, 11, "getHeight"], [26, 14, 21, 11], [26, 16, 21, 14], [27, 6, 22, 4], [27, 13, 22, 11], [27, 17, 22, 15], [27, 18, 22, 16, "ref"], [27, 21, 22, 19], [27, 22, 22, 20, "getHeight"], [27, 31, 22, 29], [27, 32, 22, 30], [27, 33, 22, 31], [28, 4, 23, 2], [29, 4, 24, 2, "getMaxWidth"], [29, 15, 24, 13, "getMaxWidth"], [29, 16, 24, 13], [29, 18, 24, 16], [30, 6, 25, 4], [30, 13, 25, 11], [30, 17, 25, 15], [30, 18, 25, 16, "ref"], [30, 21, 25, 19], [30, 22, 25, 20, "getMaxWidth"], [30, 33, 25, 31], [30, 34, 25, 32], [30, 35, 25, 33], [31, 4, 26, 2], [32, 4, 27, 2, "getGlyphPositionAtCoordinate"], [32, 32, 27, 30, "getGlyphPositionAtCoordinate"], [32, 33, 27, 31, "x"], [32, 34, 27, 32], [32, 36, 27, 34, "y"], [32, 37, 27, 35], [32, 39, 27, 37], [33, 6, 28, 4], [33, 13, 28, 11], [33, 17, 28, 15], [33, 18, 28, 16, "ref"], [33, 21, 28, 19], [33, 22, 28, 20, "getGlyphPositionAtCoordinate"], [33, 50, 28, 48], [33, 51, 28, 49, "x"], [33, 52, 28, 50], [33, 54, 28, 52, "y"], [33, 55, 28, 53], [33, 56, 28, 54], [33, 57, 28, 55, "pos"], [33, 60, 28, 58], [34, 4, 29, 2], [35, 4, 30, 2, "getRectsForPlaceholders"], [35, 27, 30, 25, "getRectsForPlaceholders"], [35, 28, 30, 25], [35, 30, 30, 28], [36, 6, 31, 4], [36, 13, 31, 11], [36, 17, 31, 15], [36, 18, 31, 16, "ref"], [36, 21, 31, 19], [36, 22, 31, 20, "getRectsForPlaceholders"], [36, 45, 31, 43], [36, 46, 31, 44], [36, 47, 31, 45], [36, 48, 31, 46, "map"], [36, 51, 31, 49], [36, 52, 31, 50], [36, 53, 31, 51], [37, 8, 32, 6, "rect"], [37, 12, 32, 10], [38, 8, 33, 6, "dir"], [39, 6, 34, 4], [39, 7, 34, 5], [39, 13, 34, 11], [40, 8, 35, 6, "rect"], [40, 12, 35, 10], [40, 14, 35, 12], [41, 10, 36, 8, "x"], [41, 11, 36, 9], [41, 13, 36, 11, "rect"], [41, 17, 36, 15], [41, 18, 36, 16, "at"], [41, 20, 36, 18], [41, 21, 36, 19], [41, 22, 36, 20], [41, 23, 36, 21], [42, 10, 37, 8, "y"], [42, 11, 37, 9], [42, 13, 37, 11, "rect"], [42, 17, 37, 15], [42, 18, 37, 16, "at"], [42, 20, 37, 18], [42, 21, 37, 19], [42, 22, 37, 20], [42, 23, 37, 21], [43, 10, 38, 8, "width"], [43, 15, 38, 13], [43, 17, 38, 15, "rect"], [43, 21, 38, 19], [43, 22, 38, 20, "at"], [43, 24, 38, 22], [43, 25, 38, 23], [43, 26, 38, 24], [43, 27, 38, 25], [44, 10, 39, 8, "height"], [44, 16, 39, 14], [44, 18, 39, 16, "rect"], [44, 22, 39, 20], [44, 23, 39, 21, "at"], [44, 25, 39, 23], [44, 26, 39, 24], [44, 27, 39, 25], [45, 8, 40, 6], [45, 9, 40, 7], [46, 8, 41, 6, "direction"], [46, 17, 41, 15], [46, 19, 41, 17, "dir"], [46, 22, 41, 20], [46, 23, 41, 21, "value"], [47, 6, 42, 4], [47, 7, 42, 5], [47, 8, 42, 6], [47, 9, 42, 7], [48, 4, 43, 2], [49, 4, 44, 2, "getRectsForRange"], [49, 20, 44, 18, "getRectsForRange"], [49, 21, 44, 19, "start"], [49, 26, 44, 24], [49, 28, 44, 26, "end"], [49, 31, 44, 29], [49, 33, 44, 31], [50, 6, 45, 4], [50, 13, 45, 11], [50, 17, 45, 15], [50, 18, 45, 16, "ref"], [50, 21, 45, 19], [50, 22, 45, 20, "getRectsForRange"], [50, 38, 45, 36], [50, 39, 45, 37, "start"], [50, 44, 45, 42], [50, 46, 45, 44, "end"], [50, 49, 45, 47], [50, 51, 45, 49], [51, 8, 46, 6, "value"], [51, 13, 46, 11], [51, 15, 46, 13], [52, 6, 47, 4], [52, 7, 47, 5], [52, 8, 47, 6], [52, 23, 47, 21], [53, 8, 48, 6, "value"], [53, 13, 48, 11], [53, 15, 48, 13], [54, 6, 49, 4], [54, 7, 49, 5], [54, 8, 49, 6], [54, 21, 49, 19], [54, 22, 49, 20], [54, 23, 49, 21, "map"], [54, 26, 49, 24], [54, 27, 49, 25], [54, 28, 49, 26], [55, 8, 50, 6, "rect"], [56, 6, 51, 4], [56, 7, 51, 5], [56, 13, 51, 11], [57, 8, 52, 6, "x"], [57, 9, 52, 7], [57, 11, 52, 9, "rect"], [57, 15, 52, 13], [57, 16, 52, 14], [57, 17, 52, 15], [57, 18, 52, 16], [58, 8, 53, 6, "y"], [58, 9, 53, 7], [58, 11, 53, 9, "rect"], [58, 15, 53, 13], [58, 16, 53, 14], [58, 17, 53, 15], [58, 18, 53, 16], [59, 8, 54, 6, "width"], [59, 13, 54, 11], [59, 15, 54, 13, "rect"], [59, 19, 54, 17], [59, 20, 54, 18], [59, 21, 54, 19], [59, 22, 54, 20], [60, 8, 55, 6, "height"], [60, 14, 55, 12], [60, 16, 55, 14, "rect"], [60, 20, 55, 18], [60, 21, 55, 19], [60, 22, 55, 20], [61, 6, 56, 4], [61, 7, 56, 5], [61, 8, 56, 6], [61, 9, 56, 7], [62, 4, 57, 2], [63, 4, 58, 2, "getLineMetrics"], [63, 18, 58, 16, "getLineMetrics"], [63, 19, 58, 16], [63, 21, 58, 19], [64, 6, 59, 4], [64, 13, 59, 11], [64, 17, 59, 15], [64, 18, 59, 16, "ref"], [64, 21, 59, 19], [64, 22, 59, 20, "getLineMetrics"], [64, 36, 59, 34], [64, 37, 59, 35], [64, 38, 59, 36], [64, 39, 59, 37, "map"], [64, 42, 59, 40], [64, 43, 59, 41], [64, 44, 59, 42, "r"], [64, 45, 59, 43], [64, 47, 59, 45, "index"], [64, 52, 59, 50], [64, 58, 59, 56], [65, 8, 60, 6, "x"], [65, 9, 60, 7], [65, 11, 60, 9, "r"], [65, 12, 60, 10], [65, 13, 60, 11, "left"], [65, 17, 60, 15], [66, 8, 61, 6, "y"], [66, 9, 61, 7], [66, 11, 61, 9, "index"], [66, 16, 61, 14], [66, 19, 61, 17, "r"], [66, 20, 61, 18], [66, 21, 61, 19, "height"], [66, 27, 61, 25], [67, 8, 62, 6, "width"], [67, 13, 62, 11], [67, 15, 62, 13, "r"], [67, 16, 62, 14], [67, 17, 62, 15, "width"], [67, 22, 62, 20], [68, 8, 63, 6, "height"], [68, 14, 63, 12], [68, 16, 63, 14, "r"], [68, 17, 63, 15], [68, 18, 63, 16, "height"], [69, 6, 64, 4], [69, 7, 64, 5], [69, 8, 64, 6], [69, 9, 64, 7], [70, 4, 65, 2], [71, 4, 66, 2, "dispose"], [71, 11, 66, 9, "dispose"], [71, 12, 66, 9], [71, 14, 66, 12], [72, 6, 67, 4], [72, 10, 67, 8], [72, 11, 67, 9, "ref"], [72, 14, 67, 12], [72, 15, 67, 13, "delete"], [72, 21, 67, 19], [72, 22, 67, 20], [72, 23, 67, 21], [73, 4, 68, 2], [74, 2, 69, 0], [75, 2, 69, 1, "exports"], [75, 9, 69, 1], [75, 10, 69, 1, "JsiSkParagraph"], [75, 24, 69, 1], [75, 27, 69, 1, "JsiSkParagraph"], [75, 41, 69, 1], [76, 0, 69, 1], [76, 3]], "functionMap": {"names": ["<global>", "JsiSkParagraph", "constructor", "getMinIntrinsicWidth", "getMaxIntrinsicWidth", "getLongestLine", "layout", "paint", "getHeight", "getMaxWidth", "getGlyphPositionAtCoordinate", "getRectsForPlaceholders", "ref.getRectsForPlaceholders.map$argument_0", "getRectsForRange", "ref.getRectsForRange.map$argument_0", "getLineMetrics", "ref.getLineMetrics.map$argument_0", "dispose"], "mappings": "AAA;OCC;ECC;GDE;EEC;GFE;EGC;GHE;EIC;GJE;EKC;GLE;EMC;GNE;EOC;GPE;EQC;GRE;ESC;GTE;EUC;kDCC;MDW;GVC;EYC;yBCK;MDO;GZC;EcC;yCCC;MDK;GdC;EgBC;GhBE;CDC"}}, "type": "js/module"}]}