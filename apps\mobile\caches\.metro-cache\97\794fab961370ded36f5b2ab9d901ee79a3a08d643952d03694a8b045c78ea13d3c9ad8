{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // Real face detection and blurring using browser APIs and CDN libraries\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        setProcessingProgress(60);\n\n        // Try multiple face detection approaches\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting face detection on image:', {\n          width: img.width,\n          height: img.height,\n          src: photoUri.substring(0, 50) + '...'\n        });\n\n        // Method 1: Try browser's native Face Detection API\n        try {\n          if ('FaceDetector' in window) {\n            console.log('[EchoCameraWeb] ✅ Browser Face Detection API available, attempting detection...');\n            const faceDetector = new window.FaceDetector({\n              maxDetectedFaces: 10,\n              fastMode: false\n            });\n            const browserDetections = await faceDetector.detect(img);\n            detectedFaces = browserDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.boundingBox.x + detection.boundingBox.width / 2) / img.width,\n                yCenter: (detection.boundingBox.y + detection.boundingBox.height / 2) / img.height,\n                width: detection.boundingBox.width / img.width,\n                height: detection.boundingBox.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] ✅ Browser Face Detection API found ${detectedFaces.length} faces`);\n          } else {\n            console.log('[EchoCameraWeb] ❌ Browser Face Detection API not available in this browser');\n            throw new Error('Browser Face Detection API not available');\n          }\n        } catch (browserError) {\n          console.warn('[EchoCameraWeb] ❌ Browser face detection failed, trying face-api.js from CDN:', browserError);\n\n          // Method 2: Try loading face-api.js from CDN\n          try {\n            // Load face-api.js from CDN if not already loaded\n            if (!window.faceapi) {\n              await new Promise((resolve, reject) => {\n                const script = document.createElement('script');\n                script.src = 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js';\n                script.onload = resolve;\n                script.onerror = reject;\n                document.head.appendChild(script);\n              });\n            }\n            const faceapi = window.faceapi;\n\n            // Load models from CDN\n            await Promise.all([faceapi.nets.tinyFaceDetector.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'), faceapi.nets.faceLandmark68Net.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights')]);\n\n            // Detect faces\n            const faceDetections = await faceapi.detectAllFaces(img, new faceapi.TinyFaceDetectorOptions());\n            detectedFaces = faceDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.box.x + detection.box.width / 2) / img.width,\n                yCenter: (detection.box.y + detection.box.height / 2) / img.height,\n                width: detection.box.width / img.width,\n                height: detection.box.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] ✅ face-api.js found ${detectedFaces.length} faces`);\n          } catch (faceApiError) {\n            console.warn('[EchoCameraWeb] ❌ face-api.js also failed:', faceApiError);\n\n            // Method 3: Fallback - Mock face detection for testing (assumes center face)\n            console.log('[EchoCameraWeb] 🧪 Using fallback mock face detection for testing...');\n            detectedFaces = [{\n              boundingBox: {\n                xCenter: 0.5,\n                // Center of image\n                yCenter: 0.4,\n                // Slightly above center (typical face position)\n                width: 0.3,\n                // 30% of image width\n                height: 0.4 // 40% of image height\n              }\n            }];\n            console.log(`[EchoCameraWeb] 🧪 Mock detection created 1 face at center of image`);\n          }\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // Apply blurring to each detected face\n        if (detectedFaces.length > 0) {\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add some padding around the face\n            const padding = 0.2; // 20% padding\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎨 Blurring face ${index + 1} at (${Math.round(paddedX)}, ${Math.round(paddedY)}) size ${Math.round(paddedWidth)}x${Math.round(paddedHeight)}`);\n\n            // Get the face region image data\n            const faceImageData = ctx.getImageData(paddedX, paddedY, paddedWidth, paddedHeight);\n            const data = faceImageData.data;\n            console.log(`[EchoCameraWeb] 📊 Face region data: ${data.length} bytes, ${paddedWidth}x${paddedHeight} pixels`);\n\n            // Apply pixelation blur effect\n            const pixelSize = Math.max(12, Math.min(paddedWidth, paddedHeight) / 15); // Increased pixel size for more visible effect\n            console.log(`[EchoCameraWeb] 🔲 Using pixel size: ${pixelSize}px for blurring`);\n            for (let y = 0; y < paddedHeight; y += pixelSize) {\n              for (let x = 0; x < paddedWidth; x += pixelSize) {\n                // Get the color of the top-left pixel in this block\n                const pixelIndex = (y * paddedWidth + x) * 4;\n                const r = data[pixelIndex];\n                const g = data[pixelIndex + 1];\n                const b = data[pixelIndex + 2];\n                const a = data[pixelIndex + 3];\n\n                // Apply this color to the entire block\n                for (let dy = 0; dy < pixelSize && y + dy < paddedHeight; dy++) {\n                  for (let dx = 0; dx < pixelSize && x + dx < paddedWidth; dx++) {\n                    const blockPixelIndex = ((y + dy) * paddedWidth + (x + dx)) * 4;\n                    data[blockPixelIndex] = r;\n                    data[blockPixelIndex + 1] = g;\n                    data[blockPixelIndex + 2] = b;\n                    data[blockPixelIndex + 3] = a;\n                  }\n                }\n              }\n            }\n\n            // Put the blurred face region back on the canvas\n            ctx.putImageData(faceImageData, paddedX, paddedY);\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} blurring applied successfully`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 621,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 645,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 651,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 652,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 643,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 554,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 703,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 696,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 739,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 732,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1352, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [89, 4, 89, 2], [90, 4, 90, 2], [90, 10, 90, 8, "capturePhoto"], [90, 22, 90, 20], [90, 25, 90, 23], [90, 29, 90, 23, "useCallback"], [90, 47, 90, 34], [90, 49, 90, 35], [90, 61, 90, 47], [91, 6, 91, 4], [92, 6, 92, 4], [92, 12, 92, 10, "isDev"], [92, 17, 92, 15], [92, 20, 92, 18, "process"], [92, 27, 92, 25], [92, 28, 92, 26, "env"], [92, 31, 92, 29], [92, 32, 92, 30, "NODE_ENV"], [92, 40, 92, 38], [92, 45, 92, 43], [92, 58, 92, 56], [92, 62, 92, 60, "__DEV__"], [92, 69, 92, 67], [93, 6, 94, 4], [93, 10, 94, 8], [93, 11, 94, 9, "cameraRef"], [93, 20, 94, 18], [93, 21, 94, 19, "current"], [93, 28, 94, 26], [93, 32, 94, 30], [93, 33, 94, 31, "isDev"], [93, 38, 94, 36], [93, 40, 94, 38], [94, 8, 95, 6, "<PERSON><PERSON>"], [94, 22, 95, 11], [94, 23, 95, 12, "alert"], [94, 28, 95, 17], [94, 29, 95, 18], [94, 36, 95, 25], [94, 38, 95, 27], [94, 56, 95, 45], [94, 57, 95, 46], [95, 8, 96, 6], [96, 6, 97, 4], [97, 6, 98, 4], [97, 10, 98, 8], [98, 8, 99, 6, "setProcessingState"], [98, 26, 99, 24], [98, 27, 99, 25], [98, 38, 99, 36], [98, 39, 99, 37], [99, 8, 100, 6, "setProcessingProgress"], [99, 29, 100, 27], [99, 30, 100, 28], [99, 32, 100, 30], [99, 33, 100, 31], [100, 8, 101, 6], [101, 8, 102, 6], [102, 8, 103, 6], [103, 8, 104, 6], [103, 14, 104, 12], [103, 18, 104, 16, "Promise"], [103, 25, 104, 23], [103, 26, 104, 24, "resolve"], [103, 33, 104, 31], [103, 37, 104, 35, "setTimeout"], [103, 47, 104, 45], [103, 48, 104, 46, "resolve"], [103, 55, 104, 53], [103, 57, 104, 55], [103, 59, 104, 57], [103, 60, 104, 58], [103, 61, 104, 59], [104, 8, 105, 6], [105, 8, 106, 6], [105, 12, 106, 10, "photo"], [105, 17, 106, 15], [106, 8, 108, 6], [106, 12, 108, 10], [107, 10, 109, 8, "photo"], [107, 15, 109, 13], [107, 18, 109, 16], [107, 24, 109, 22, "cameraRef"], [107, 33, 109, 31], [107, 34, 109, 32, "current"], [107, 41, 109, 39], [107, 42, 109, 40, "takePictureAsync"], [107, 58, 109, 56], [107, 59, 109, 57], [108, 12, 110, 10, "quality"], [108, 19, 110, 17], [108, 21, 110, 19], [108, 24, 110, 22], [109, 12, 111, 10, "base64"], [109, 18, 111, 16], [109, 20, 111, 18], [109, 25, 111, 23], [110, 12, 112, 10, "skipProcessing"], [110, 26, 112, 24], [110, 28, 112, 26], [110, 32, 112, 30], [110, 33, 112, 32], [111, 10, 113, 8], [111, 11, 113, 9], [111, 12, 113, 10], [112, 8, 114, 6], [112, 9, 114, 7], [112, 10, 114, 8], [112, 17, 114, 15, "cameraError"], [112, 28, 114, 26], [112, 30, 114, 28], [113, 10, 115, 8, "console"], [113, 17, 115, 15], [113, 18, 115, 16, "log"], [113, 21, 115, 19], [113, 22, 115, 20], [113, 82, 115, 80], [113, 84, 115, 82, "cameraError"], [113, 95, 115, 93], [113, 96, 115, 94], [114, 10, 116, 8], [115, 10, 117, 8], [115, 14, 117, 12, "isDev"], [115, 19, 117, 17], [115, 21, 117, 19], [116, 12, 118, 10, "photo"], [116, 17, 118, 15], [116, 20, 118, 18], [117, 14, 119, 12, "uri"], [117, 17, 119, 15], [117, 19, 119, 17], [118, 12, 120, 10], [118, 13, 120, 11], [119, 10, 121, 8], [119, 11, 121, 9], [119, 17, 121, 15], [120, 12, 122, 10], [120, 18, 122, 16, "cameraError"], [120, 29, 122, 27], [121, 10, 123, 8], [122, 8, 124, 6], [123, 8, 125, 6], [123, 12, 125, 10], [123, 13, 125, 11, "photo"], [123, 18, 125, 16], [123, 20, 125, 18], [124, 10, 126, 8], [124, 16, 126, 14], [124, 20, 126, 18, "Error"], [124, 25, 126, 23], [124, 26, 126, 24], [124, 51, 126, 49], [124, 52, 126, 50], [125, 8, 127, 6], [126, 8, 128, 6, "console"], [126, 15, 128, 13], [126, 16, 128, 14, "log"], [126, 19, 128, 17], [126, 20, 128, 18], [126, 56, 128, 54], [126, 58, 128, 56, "photo"], [126, 63, 128, 61], [126, 64, 128, 62, "uri"], [126, 67, 128, 65], [126, 68, 128, 66], [127, 8, 129, 6, "setCapturedPhoto"], [127, 24, 129, 22], [127, 25, 129, 23, "photo"], [127, 30, 129, 28], [127, 31, 129, 29, "uri"], [127, 34, 129, 32], [127, 35, 129, 33], [128, 8, 130, 6, "setProcessingProgress"], [128, 29, 130, 27], [128, 30, 130, 28], [128, 32, 130, 30], [128, 33, 130, 31], [129, 8, 131, 6], [130, 8, 132, 6, "console"], [130, 15, 132, 13], [130, 16, 132, 14, "log"], [130, 19, 132, 17], [130, 20, 132, 18], [130, 73, 132, 71], [130, 74, 132, 72], [131, 8, 133, 6], [131, 14, 133, 12, "processImageWithFaceBlur"], [131, 38, 133, 36], [131, 39, 133, 37, "photo"], [131, 44, 133, 42], [131, 45, 133, 43, "uri"], [131, 48, 133, 46], [131, 49, 133, 47], [132, 8, 134, 6, "console"], [132, 15, 134, 13], [132, 16, 134, 14, "log"], [132, 19, 134, 17], [132, 20, 134, 18], [132, 71, 134, 69], [132, 72, 134, 70], [133, 6, 135, 4], [133, 7, 135, 5], [133, 8, 135, 6], [133, 15, 135, 13, "error"], [133, 20, 135, 18], [133, 22, 135, 20], [134, 8, 136, 6, "console"], [134, 15, 136, 13], [134, 16, 136, 14, "error"], [134, 21, 136, 19], [134, 22, 136, 20], [134, 54, 136, 52], [134, 56, 136, 54, "error"], [134, 61, 136, 59], [134, 62, 136, 60], [135, 8, 137, 6, "setErrorMessage"], [135, 23, 137, 21], [135, 24, 137, 22], [135, 68, 137, 66], [135, 69, 137, 67], [136, 8, 138, 6, "setProcessingState"], [136, 26, 138, 24], [136, 27, 138, 25], [136, 34, 138, 32], [136, 35, 138, 33], [137, 6, 139, 4], [138, 4, 140, 2], [138, 5, 140, 3], [138, 7, 140, 5], [138, 9, 140, 7], [138, 10, 140, 8], [139, 4, 141, 2], [140, 4, 142, 2], [140, 10, 142, 8, "processImageWithFaceBlur"], [140, 34, 142, 32], [140, 37, 142, 35], [140, 43, 142, 42, "photoUri"], [140, 51, 142, 58], [140, 55, 142, 63], [141, 6, 143, 4], [141, 10, 143, 8], [142, 8, 144, 6, "setProcessingState"], [142, 26, 144, 24], [142, 27, 144, 25], [142, 39, 144, 37], [142, 40, 144, 38], [143, 8, 145, 6, "setProcessingProgress"], [143, 29, 145, 27], [143, 30, 145, 28], [143, 32, 145, 30], [143, 33, 145, 31], [145, 8, 147, 6], [146, 8, 148, 6], [146, 14, 148, 12, "canvas"], [146, 20, 148, 18], [146, 23, 148, 21, "document"], [146, 31, 148, 29], [146, 32, 148, 30, "createElement"], [146, 45, 148, 43], [146, 46, 148, 44], [146, 54, 148, 52], [146, 55, 148, 53], [147, 8, 149, 6], [147, 14, 149, 12, "ctx"], [147, 17, 149, 15], [147, 20, 149, 18, "canvas"], [147, 26, 149, 24], [147, 27, 149, 25, "getContext"], [147, 37, 149, 35], [147, 38, 149, 36], [147, 42, 149, 40], [147, 43, 149, 41], [148, 8, 150, 6], [148, 12, 150, 10], [148, 13, 150, 11, "ctx"], [148, 16, 150, 14], [148, 18, 150, 16], [148, 24, 150, 22], [148, 28, 150, 26, "Error"], [148, 33, 150, 31], [148, 34, 150, 32], [148, 64, 150, 62], [148, 65, 150, 63], [150, 8, 152, 6], [151, 8, 153, 6], [151, 14, 153, 12, "img"], [151, 17, 153, 15], [151, 20, 153, 18], [151, 24, 153, 22, "Image"], [151, 29, 153, 27], [151, 30, 153, 28], [151, 31, 153, 29], [152, 8, 154, 6], [152, 14, 154, 12], [152, 18, 154, 16, "Promise"], [152, 25, 154, 23], [152, 26, 154, 24], [152, 27, 154, 25, "resolve"], [152, 34, 154, 32], [152, 36, 154, 34, "reject"], [152, 42, 154, 40], [152, 47, 154, 45], [153, 10, 155, 8, "img"], [153, 13, 155, 11], [153, 14, 155, 12, "onload"], [153, 20, 155, 18], [153, 23, 155, 21, "resolve"], [153, 30, 155, 28], [154, 10, 156, 8, "img"], [154, 13, 156, 11], [154, 14, 156, 12, "onerror"], [154, 21, 156, 19], [154, 24, 156, 22, "reject"], [154, 30, 156, 28], [155, 10, 157, 8, "img"], [155, 13, 157, 11], [155, 14, 157, 12, "src"], [155, 17, 157, 15], [155, 20, 157, 18, "photoUri"], [155, 28, 157, 26], [156, 8, 158, 6], [156, 9, 158, 7], [156, 10, 158, 8], [158, 8, 160, 6], [159, 8, 161, 6, "canvas"], [159, 14, 161, 12], [159, 15, 161, 13, "width"], [159, 20, 161, 18], [159, 23, 161, 21, "img"], [159, 26, 161, 24], [159, 27, 161, 25, "width"], [159, 32, 161, 30], [160, 8, 162, 6, "canvas"], [160, 14, 162, 12], [160, 15, 162, 13, "height"], [160, 21, 162, 19], [160, 24, 162, 22, "img"], [160, 27, 162, 25], [160, 28, 162, 26, "height"], [160, 34, 162, 32], [162, 8, 164, 6], [163, 8, 165, 6, "ctx"], [163, 11, 165, 9], [163, 12, 165, 10, "drawImage"], [163, 21, 165, 19], [163, 22, 165, 20, "img"], [163, 25, 165, 23], [163, 27, 165, 25], [163, 28, 165, 26], [163, 30, 165, 28], [163, 31, 165, 29], [163, 32, 165, 30], [164, 8, 167, 6, "setProcessingProgress"], [164, 29, 167, 27], [164, 30, 167, 28], [164, 32, 167, 30], [164, 33, 167, 31], [166, 8, 169, 6], [167, 8, 170, 6], [167, 12, 170, 10, "detectedFaces"], [167, 25, 170, 23], [167, 28, 170, 26], [167, 30, 170, 28], [168, 8, 172, 6, "console"], [168, 15, 172, 13], [168, 16, 172, 14, "log"], [168, 19, 172, 17], [168, 20, 172, 18], [168, 74, 172, 72], [168, 76, 172, 74], [169, 10, 173, 8, "width"], [169, 15, 173, 13], [169, 17, 173, 15, "img"], [169, 20, 173, 18], [169, 21, 173, 19, "width"], [169, 26, 173, 24], [170, 10, 174, 8, "height"], [170, 16, 174, 14], [170, 18, 174, 16, "img"], [170, 21, 174, 19], [170, 22, 174, 20, "height"], [170, 28, 174, 26], [171, 10, 175, 8, "src"], [171, 13, 175, 11], [171, 15, 175, 13, "photoUri"], [171, 23, 175, 21], [171, 24, 175, 22, "substring"], [171, 33, 175, 31], [171, 34, 175, 32], [171, 35, 175, 33], [171, 37, 175, 35], [171, 39, 175, 37], [171, 40, 175, 38], [171, 43, 175, 41], [172, 8, 176, 6], [172, 9, 176, 7], [172, 10, 176, 8], [174, 8, 178, 6], [175, 8, 179, 6], [175, 12, 179, 10], [176, 10, 180, 8], [176, 14, 180, 12], [176, 28, 180, 26], [176, 32, 180, 30, "window"], [176, 38, 180, 36], [176, 40, 180, 38], [177, 12, 181, 10, "console"], [177, 19, 181, 17], [177, 20, 181, 18, "log"], [177, 23, 181, 21], [177, 24, 181, 22], [177, 105, 181, 103], [177, 106, 181, 104], [178, 12, 182, 10], [178, 18, 182, 16, "faceDetector"], [178, 30, 182, 28], [178, 33, 182, 31], [178, 37, 182, 36, "window"], [178, 43, 182, 42], [178, 44, 182, 51, "FaceDetector"], [178, 56, 182, 63], [178, 57, 182, 64], [179, 14, 183, 12, "maxDetectedFaces"], [179, 30, 183, 28], [179, 32, 183, 30], [179, 34, 183, 32], [180, 14, 184, 12, "fastMode"], [180, 22, 184, 20], [180, 24, 184, 22], [181, 12, 185, 10], [181, 13, 185, 11], [181, 14, 185, 12], [182, 12, 187, 10], [182, 18, 187, 16, "browserDetections"], [182, 35, 187, 33], [182, 38, 187, 36], [182, 44, 187, 42, "faceDetector"], [182, 56, 187, 54], [182, 57, 187, 55, "detect"], [182, 63, 187, 61], [182, 64, 187, 62, "img"], [182, 67, 187, 65], [182, 68, 187, 66], [183, 12, 188, 10, "detectedFaces"], [183, 25, 188, 23], [183, 28, 188, 26, "browserDetections"], [183, 45, 188, 43], [183, 46, 188, 44, "map"], [183, 49, 188, 47], [183, 50, 188, 49, "detection"], [183, 59, 188, 63], [183, 64, 188, 69], [184, 14, 189, 12, "boundingBox"], [184, 25, 189, 23], [184, 27, 189, 25], [185, 16, 190, 14, "xCenter"], [185, 23, 190, 21], [185, 25, 190, 23], [185, 26, 190, 24, "detection"], [185, 35, 190, 33], [185, 36, 190, 34, "boundingBox"], [185, 47, 190, 45], [185, 48, 190, 46, "x"], [185, 49, 190, 47], [185, 52, 190, 50, "detection"], [185, 61, 190, 59], [185, 62, 190, 60, "boundingBox"], [185, 73, 190, 71], [185, 74, 190, 72, "width"], [185, 79, 190, 77], [185, 82, 190, 80], [185, 83, 190, 81], [185, 87, 190, 85, "img"], [185, 90, 190, 88], [185, 91, 190, 89, "width"], [185, 96, 190, 94], [186, 16, 191, 14, "yCenter"], [186, 23, 191, 21], [186, 25, 191, 23], [186, 26, 191, 24, "detection"], [186, 35, 191, 33], [186, 36, 191, 34, "boundingBox"], [186, 47, 191, 45], [186, 48, 191, 46, "y"], [186, 49, 191, 47], [186, 52, 191, 50, "detection"], [186, 61, 191, 59], [186, 62, 191, 60, "boundingBox"], [186, 73, 191, 71], [186, 74, 191, 72, "height"], [186, 80, 191, 78], [186, 83, 191, 81], [186, 84, 191, 82], [186, 88, 191, 86, "img"], [186, 91, 191, 89], [186, 92, 191, 90, "height"], [186, 98, 191, 96], [187, 16, 192, 14, "width"], [187, 21, 192, 19], [187, 23, 192, 21, "detection"], [187, 32, 192, 30], [187, 33, 192, 31, "boundingBox"], [187, 44, 192, 42], [187, 45, 192, 43, "width"], [187, 50, 192, 48], [187, 53, 192, 51, "img"], [187, 56, 192, 54], [187, 57, 192, 55, "width"], [187, 62, 192, 60], [188, 16, 193, 14, "height"], [188, 22, 193, 20], [188, 24, 193, 22, "detection"], [188, 33, 193, 31], [188, 34, 193, 32, "boundingBox"], [188, 45, 193, 43], [188, 46, 193, 44, "height"], [188, 52, 193, 50], [188, 55, 193, 53, "img"], [188, 58, 193, 56], [188, 59, 193, 57, "height"], [189, 14, 194, 12], [190, 12, 195, 10], [190, 13, 195, 11], [190, 14, 195, 12], [190, 15, 195, 13], [191, 12, 196, 10, "console"], [191, 19, 196, 17], [191, 20, 196, 18, "log"], [191, 23, 196, 21], [191, 24, 196, 22], [191, 78, 196, 76, "detectedFaces"], [191, 91, 196, 89], [191, 92, 196, 90, "length"], [191, 98, 196, 96], [191, 106, 196, 104], [191, 107, 196, 105], [192, 10, 197, 8], [192, 11, 197, 9], [192, 17, 197, 15], [193, 12, 198, 10, "console"], [193, 19, 198, 17], [193, 20, 198, 18, "log"], [193, 23, 198, 21], [193, 24, 198, 22], [193, 100, 198, 98], [193, 101, 198, 99], [194, 12, 199, 10], [194, 18, 199, 16], [194, 22, 199, 20, "Error"], [194, 27, 199, 25], [194, 28, 199, 26], [194, 70, 199, 68], [194, 71, 199, 69], [195, 10, 200, 8], [196, 8, 201, 6], [196, 9, 201, 7], [196, 10, 201, 8], [196, 17, 201, 15, "browserError"], [196, 29, 201, 27], [196, 31, 201, 29], [197, 10, 202, 8, "console"], [197, 17, 202, 15], [197, 18, 202, 16, "warn"], [197, 22, 202, 20], [197, 23, 202, 21], [197, 102, 202, 100], [197, 104, 202, 102, "browserError"], [197, 116, 202, 114], [197, 117, 202, 115], [199, 10, 204, 8], [200, 10, 205, 8], [200, 14, 205, 12], [201, 12, 206, 10], [202, 12, 207, 10], [202, 16, 207, 14], [202, 17, 207, 16, "window"], [202, 23, 207, 22], [202, 24, 207, 31, "<PERSON>ap<PERSON>"], [202, 31, 207, 38], [202, 33, 207, 40], [203, 14, 208, 12], [203, 20, 208, 18], [203, 24, 208, 22, "Promise"], [203, 31, 208, 29], [203, 32, 208, 30], [203, 33, 208, 31, "resolve"], [203, 40, 208, 38], [203, 42, 208, 40, "reject"], [203, 48, 208, 46], [203, 53, 208, 51], [204, 16, 209, 14], [204, 22, 209, 20, "script"], [204, 28, 209, 26], [204, 31, 209, 29, "document"], [204, 39, 209, 37], [204, 40, 209, 38, "createElement"], [204, 53, 209, 51], [204, 54, 209, 52], [204, 62, 209, 60], [204, 63, 209, 61], [205, 16, 210, 14, "script"], [205, 22, 210, 20], [205, 23, 210, 21, "src"], [205, 26, 210, 24], [205, 29, 210, 27], [205, 99, 210, 97], [206, 16, 211, 14, "script"], [206, 22, 211, 20], [206, 23, 211, 21, "onload"], [206, 29, 211, 27], [206, 32, 211, 30, "resolve"], [206, 39, 211, 37], [207, 16, 212, 14, "script"], [207, 22, 212, 20], [207, 23, 212, 21, "onerror"], [207, 30, 212, 28], [207, 33, 212, 31, "reject"], [207, 39, 212, 37], [208, 16, 213, 14, "document"], [208, 24, 213, 22], [208, 25, 213, 23, "head"], [208, 29, 213, 27], [208, 30, 213, 28, "append<PERSON><PERSON><PERSON>"], [208, 41, 213, 39], [208, 42, 213, 40, "script"], [208, 48, 213, 46], [208, 49, 213, 47], [209, 14, 214, 12], [209, 15, 214, 13], [209, 16, 214, 14], [210, 12, 215, 10], [211, 12, 217, 10], [211, 18, 217, 16, "<PERSON>ap<PERSON>"], [211, 25, 217, 23], [211, 28, 217, 27, "window"], [211, 34, 217, 33], [211, 35, 217, 42, "<PERSON>ap<PERSON>"], [211, 42, 217, 49], [213, 12, 219, 10], [214, 12, 220, 10], [214, 18, 220, 16, "Promise"], [214, 25, 220, 23], [214, 26, 220, 24, "all"], [214, 29, 220, 27], [214, 30, 220, 28], [214, 31, 221, 12, "<PERSON>ap<PERSON>"], [214, 38, 221, 19], [214, 39, 221, 20, "nets"], [214, 43, 221, 24], [214, 44, 221, 25, "tinyFaceDetector"], [214, 60, 221, 41], [214, 61, 221, 42, "loadFromUri"], [214, 72, 221, 53], [214, 73, 221, 54], [214, 130, 221, 111], [214, 131, 221, 112], [214, 133, 222, 12, "<PERSON>ap<PERSON>"], [214, 140, 222, 19], [214, 141, 222, 20, "nets"], [214, 145, 222, 24], [214, 146, 222, 25, "faceLandmark68Net"], [214, 163, 222, 42], [214, 164, 222, 43, "loadFromUri"], [214, 175, 222, 54], [214, 176, 222, 55], [214, 233, 222, 112], [214, 234, 222, 113], [214, 235, 223, 11], [214, 236, 223, 12], [216, 12, 225, 10], [217, 12, 226, 10], [217, 18, 226, 16, "faceDetections"], [217, 32, 226, 30], [217, 35, 226, 33], [217, 41, 226, 39, "<PERSON>ap<PERSON>"], [217, 48, 226, 46], [217, 49, 226, 47, "detectAllFaces"], [217, 63, 226, 61], [217, 64, 226, 62, "img"], [217, 67, 226, 65], [217, 69, 226, 67], [217, 73, 226, 71, "<PERSON>ap<PERSON>"], [217, 80, 226, 78], [217, 81, 226, 79, "TinyFaceDetectorOptions"], [217, 104, 226, 102], [217, 105, 226, 103], [217, 106, 226, 104], [217, 107, 226, 105], [218, 12, 228, 10, "detectedFaces"], [218, 25, 228, 23], [218, 28, 228, 26, "faceDetections"], [218, 42, 228, 40], [218, 43, 228, 41, "map"], [218, 46, 228, 44], [218, 47, 228, 46, "detection"], [218, 56, 228, 60], [218, 61, 228, 66], [219, 14, 229, 12, "boundingBox"], [219, 25, 229, 23], [219, 27, 229, 25], [220, 16, 230, 14, "xCenter"], [220, 23, 230, 21], [220, 25, 230, 23], [220, 26, 230, 24, "detection"], [220, 35, 230, 33], [220, 36, 230, 34, "box"], [220, 39, 230, 37], [220, 40, 230, 38, "x"], [220, 41, 230, 39], [220, 44, 230, 42, "detection"], [220, 53, 230, 51], [220, 54, 230, 52, "box"], [220, 57, 230, 55], [220, 58, 230, 56, "width"], [220, 63, 230, 61], [220, 66, 230, 64], [220, 67, 230, 65], [220, 71, 230, 69, "img"], [220, 74, 230, 72], [220, 75, 230, 73, "width"], [220, 80, 230, 78], [221, 16, 231, 14, "yCenter"], [221, 23, 231, 21], [221, 25, 231, 23], [221, 26, 231, 24, "detection"], [221, 35, 231, 33], [221, 36, 231, 34, "box"], [221, 39, 231, 37], [221, 40, 231, 38, "y"], [221, 41, 231, 39], [221, 44, 231, 42, "detection"], [221, 53, 231, 51], [221, 54, 231, 52, "box"], [221, 57, 231, 55], [221, 58, 231, 56, "height"], [221, 64, 231, 62], [221, 67, 231, 65], [221, 68, 231, 66], [221, 72, 231, 70, "img"], [221, 75, 231, 73], [221, 76, 231, 74, "height"], [221, 82, 231, 80], [222, 16, 232, 14, "width"], [222, 21, 232, 19], [222, 23, 232, 21, "detection"], [222, 32, 232, 30], [222, 33, 232, 31, "box"], [222, 36, 232, 34], [222, 37, 232, 35, "width"], [222, 42, 232, 40], [222, 45, 232, 43, "img"], [222, 48, 232, 46], [222, 49, 232, 47, "width"], [222, 54, 232, 52], [223, 16, 233, 14, "height"], [223, 22, 233, 20], [223, 24, 233, 22, "detection"], [223, 33, 233, 31], [223, 34, 233, 32, "box"], [223, 37, 233, 35], [223, 38, 233, 36, "height"], [223, 44, 233, 42], [223, 47, 233, 45, "img"], [223, 50, 233, 48], [223, 51, 233, 49, "height"], [224, 14, 234, 12], [225, 12, 235, 10], [225, 13, 235, 11], [225, 14, 235, 12], [225, 15, 235, 13], [226, 12, 237, 10, "console"], [226, 19, 237, 17], [226, 20, 237, 18, "log"], [226, 23, 237, 21], [226, 24, 237, 22], [226, 63, 237, 61, "detectedFaces"], [226, 76, 237, 74], [226, 77, 237, 75, "length"], [226, 83, 237, 81], [226, 91, 237, 89], [226, 92, 237, 90], [227, 10, 238, 8], [227, 11, 238, 9], [227, 12, 238, 10], [227, 19, 238, 17, "faceApiError"], [227, 31, 238, 29], [227, 33, 238, 31], [228, 12, 239, 10, "console"], [228, 19, 239, 17], [228, 20, 239, 18, "warn"], [228, 24, 239, 22], [228, 25, 239, 23], [228, 69, 239, 67], [228, 71, 239, 69, "faceApiError"], [228, 83, 239, 81], [228, 84, 239, 82], [230, 12, 241, 10], [231, 12, 242, 10, "console"], [231, 19, 242, 17], [231, 20, 242, 18, "log"], [231, 23, 242, 21], [231, 24, 242, 22], [231, 94, 242, 92], [231, 95, 242, 93], [232, 12, 243, 10, "detectedFaces"], [232, 25, 243, 23], [232, 28, 243, 26], [232, 29, 243, 27], [233, 14, 244, 12, "boundingBox"], [233, 25, 244, 23], [233, 27, 244, 25], [234, 16, 245, 14, "xCenter"], [234, 23, 245, 21], [234, 25, 245, 23], [234, 28, 245, 26], [235, 16, 245, 29], [236, 16, 246, 14, "yCenter"], [236, 23, 246, 21], [236, 25, 246, 23], [236, 28, 246, 26], [237, 16, 246, 29], [238, 16, 247, 14, "width"], [238, 21, 247, 19], [238, 23, 247, 21], [238, 26, 247, 24], [239, 16, 247, 29], [240, 16, 248, 14, "height"], [240, 22, 248, 20], [240, 24, 248, 22], [240, 27, 248, 25], [240, 28, 248, 29], [241, 14, 249, 12], [242, 12, 250, 10], [242, 13, 250, 11], [242, 14, 250, 12], [243, 12, 251, 10, "console"], [243, 19, 251, 17], [243, 20, 251, 18, "log"], [243, 23, 251, 21], [243, 24, 251, 22], [243, 93, 251, 91], [243, 94, 251, 92], [244, 10, 252, 8], [245, 8, 253, 6], [246, 8, 255, 6, "console"], [246, 15, 255, 13], [246, 16, 255, 14, "log"], [246, 19, 255, 17], [246, 20, 255, 18], [246, 72, 255, 70, "detectedFaces"], [246, 85, 255, 83], [246, 86, 255, 84, "length"], [246, 92, 255, 90], [246, 100, 255, 98], [246, 101, 255, 99], [247, 8, 256, 6], [247, 12, 256, 10, "detectedFaces"], [247, 25, 256, 23], [247, 26, 256, 24, "length"], [247, 32, 256, 30], [247, 35, 256, 33], [247, 36, 256, 34], [247, 38, 256, 36], [248, 10, 257, 8, "console"], [248, 17, 257, 15], [248, 18, 257, 16, "log"], [248, 21, 257, 19], [248, 22, 257, 20], [248, 66, 257, 64], [248, 68, 257, 66, "detectedFaces"], [248, 81, 257, 79], [248, 82, 257, 80, "map"], [248, 85, 257, 83], [248, 86, 257, 84], [248, 87, 257, 85, "face"], [248, 91, 257, 89], [248, 93, 257, 91, "i"], [248, 94, 257, 92], [248, 100, 257, 98], [249, 12, 258, 10, "faceNumber"], [249, 22, 258, 20], [249, 24, 258, 22, "i"], [249, 25, 258, 23], [249, 28, 258, 26], [249, 29, 258, 27], [250, 12, 259, 10, "centerX"], [250, 19, 259, 17], [250, 21, 259, 19, "face"], [250, 25, 259, 23], [250, 26, 259, 24, "boundingBox"], [250, 37, 259, 35], [250, 38, 259, 36, "xCenter"], [250, 45, 259, 43], [251, 12, 260, 10, "centerY"], [251, 19, 260, 17], [251, 21, 260, 19, "face"], [251, 25, 260, 23], [251, 26, 260, 24, "boundingBox"], [251, 37, 260, 35], [251, 38, 260, 36, "yCenter"], [251, 45, 260, 43], [252, 12, 261, 10, "width"], [252, 17, 261, 15], [252, 19, 261, 17, "face"], [252, 23, 261, 21], [252, 24, 261, 22, "boundingBox"], [252, 35, 261, 33], [252, 36, 261, 34, "width"], [252, 41, 261, 39], [253, 12, 262, 10, "height"], [253, 18, 262, 16], [253, 20, 262, 18, "face"], [253, 24, 262, 22], [253, 25, 262, 23, "boundingBox"], [253, 36, 262, 34], [253, 37, 262, 35, "height"], [254, 10, 263, 8], [254, 11, 263, 9], [254, 12, 263, 10], [254, 13, 263, 11], [254, 14, 263, 12], [255, 8, 264, 6], [255, 9, 264, 7], [255, 15, 264, 13], [256, 10, 265, 8, "console"], [256, 17, 265, 15], [256, 18, 265, 16, "log"], [256, 21, 265, 19], [256, 22, 265, 20], [256, 91, 265, 89], [256, 92, 265, 90], [257, 8, 266, 6], [258, 8, 268, 6, "setProcessingProgress"], [258, 29, 268, 27], [258, 30, 268, 28], [258, 32, 268, 30], [258, 33, 268, 31], [260, 8, 270, 6], [261, 8, 271, 6], [261, 12, 271, 10, "detectedFaces"], [261, 25, 271, 23], [261, 26, 271, 24, "length"], [261, 32, 271, 30], [261, 35, 271, 33], [261, 36, 271, 34], [261, 38, 271, 36], [262, 10, 272, 8, "detectedFaces"], [262, 23, 272, 21], [262, 24, 272, 22, "for<PERSON>ach"], [262, 31, 272, 29], [262, 32, 272, 30], [262, 33, 272, 31, "detection"], [262, 42, 272, 40], [262, 44, 272, 42, "index"], [262, 49, 272, 47], [262, 54, 272, 52], [263, 12, 273, 10], [263, 18, 273, 16, "bbox"], [263, 22, 273, 20], [263, 25, 273, 23, "detection"], [263, 34, 273, 32], [263, 35, 273, 33, "boundingBox"], [263, 46, 273, 44], [265, 12, 275, 10], [266, 12, 276, 10], [266, 18, 276, 16, "faceX"], [266, 23, 276, 21], [266, 26, 276, 24, "bbox"], [266, 30, 276, 28], [266, 31, 276, 29, "xCenter"], [266, 38, 276, 36], [266, 41, 276, 39, "img"], [266, 44, 276, 42], [266, 45, 276, 43, "width"], [266, 50, 276, 48], [266, 53, 276, 52, "bbox"], [266, 57, 276, 56], [266, 58, 276, 57, "width"], [266, 63, 276, 62], [266, 66, 276, 65, "img"], [266, 69, 276, 68], [266, 70, 276, 69, "width"], [266, 75, 276, 74], [266, 78, 276, 78], [266, 79, 276, 79], [267, 12, 277, 10], [267, 18, 277, 16, "faceY"], [267, 23, 277, 21], [267, 26, 277, 24, "bbox"], [267, 30, 277, 28], [267, 31, 277, 29, "yCenter"], [267, 38, 277, 36], [267, 41, 277, 39, "img"], [267, 44, 277, 42], [267, 45, 277, 43, "height"], [267, 51, 277, 49], [267, 54, 277, 53, "bbox"], [267, 58, 277, 57], [267, 59, 277, 58, "height"], [267, 65, 277, 64], [267, 68, 277, 67, "img"], [267, 71, 277, 70], [267, 72, 277, 71, "height"], [267, 78, 277, 77], [267, 81, 277, 81], [267, 82, 277, 82], [268, 12, 278, 10], [268, 18, 278, 16, "faceWidth"], [268, 27, 278, 25], [268, 30, 278, 28, "bbox"], [268, 34, 278, 32], [268, 35, 278, 33, "width"], [268, 40, 278, 38], [268, 43, 278, 41, "img"], [268, 46, 278, 44], [268, 47, 278, 45, "width"], [268, 52, 278, 50], [269, 12, 279, 10], [269, 18, 279, 16, "faceHeight"], [269, 28, 279, 26], [269, 31, 279, 29, "bbox"], [269, 35, 279, 33], [269, 36, 279, 34, "height"], [269, 42, 279, 40], [269, 45, 279, 43, "img"], [269, 48, 279, 46], [269, 49, 279, 47, "height"], [269, 55, 279, 53], [271, 12, 281, 10], [272, 12, 282, 10], [272, 18, 282, 16, "padding"], [272, 25, 282, 23], [272, 28, 282, 26], [272, 31, 282, 29], [272, 32, 282, 30], [272, 33, 282, 31], [273, 12, 283, 10], [273, 18, 283, 16, "paddedX"], [273, 25, 283, 23], [273, 28, 283, 26, "Math"], [273, 32, 283, 30], [273, 33, 283, 31, "max"], [273, 36, 283, 34], [273, 37, 283, 35], [273, 38, 283, 36], [273, 40, 283, 38, "faceX"], [273, 45, 283, 43], [273, 48, 283, 46, "faceWidth"], [273, 57, 283, 55], [273, 60, 283, 58, "padding"], [273, 67, 283, 65], [273, 68, 283, 66], [274, 12, 284, 10], [274, 18, 284, 16, "paddedY"], [274, 25, 284, 23], [274, 28, 284, 26, "Math"], [274, 32, 284, 30], [274, 33, 284, 31, "max"], [274, 36, 284, 34], [274, 37, 284, 35], [274, 38, 284, 36], [274, 40, 284, 38, "faceY"], [274, 45, 284, 43], [274, 48, 284, 46, "faceHeight"], [274, 58, 284, 56], [274, 61, 284, 59, "padding"], [274, 68, 284, 66], [274, 69, 284, 67], [275, 12, 285, 10], [275, 18, 285, 16, "<PERSON><PERSON><PERSON><PERSON>"], [275, 29, 285, 27], [275, 32, 285, 30, "Math"], [275, 36, 285, 34], [275, 37, 285, 35, "min"], [275, 40, 285, 38], [275, 41, 285, 39, "img"], [275, 44, 285, 42], [275, 45, 285, 43, "width"], [275, 50, 285, 48], [275, 53, 285, 51, "paddedX"], [275, 60, 285, 58], [275, 62, 285, 60, "faceWidth"], [275, 71, 285, 69], [275, 75, 285, 73], [275, 76, 285, 74], [275, 79, 285, 77], [275, 80, 285, 78], [275, 83, 285, 81, "padding"], [275, 90, 285, 88], [275, 91, 285, 89], [275, 92, 285, 90], [276, 12, 286, 10], [276, 18, 286, 16, "paddedHeight"], [276, 30, 286, 28], [276, 33, 286, 31, "Math"], [276, 37, 286, 35], [276, 38, 286, 36, "min"], [276, 41, 286, 39], [276, 42, 286, 40, "img"], [276, 45, 286, 43], [276, 46, 286, 44, "height"], [276, 52, 286, 50], [276, 55, 286, 53, "paddedY"], [276, 62, 286, 60], [276, 64, 286, 62, "faceHeight"], [276, 74, 286, 72], [276, 78, 286, 76], [276, 79, 286, 77], [276, 82, 286, 80], [276, 83, 286, 81], [276, 86, 286, 84, "padding"], [276, 93, 286, 91], [276, 94, 286, 92], [276, 95, 286, 93], [277, 12, 288, 10, "console"], [277, 19, 288, 17], [277, 20, 288, 18, "log"], [277, 23, 288, 21], [277, 24, 288, 22], [277, 60, 288, 58, "index"], [277, 65, 288, 63], [277, 68, 288, 66], [277, 69, 288, 67], [277, 77, 288, 75, "Math"], [277, 81, 288, 79], [277, 82, 288, 80, "round"], [277, 87, 288, 85], [277, 88, 288, 86, "paddedX"], [277, 95, 288, 93], [277, 96, 288, 94], [277, 101, 288, 99, "Math"], [277, 105, 288, 103], [277, 106, 288, 104, "round"], [277, 111, 288, 109], [277, 112, 288, 110, "paddedY"], [277, 119, 288, 117], [277, 120, 288, 118], [277, 130, 288, 128, "Math"], [277, 134, 288, 132], [277, 135, 288, 133, "round"], [277, 140, 288, 138], [277, 141, 288, 139, "<PERSON><PERSON><PERSON><PERSON>"], [277, 152, 288, 150], [277, 153, 288, 151], [277, 157, 288, 155, "Math"], [277, 161, 288, 159], [277, 162, 288, 160, "round"], [277, 167, 288, 165], [277, 168, 288, 166, "paddedHeight"], [277, 180, 288, 178], [277, 181, 288, 179], [277, 183, 288, 181], [277, 184, 288, 182], [279, 12, 290, 10], [280, 12, 291, 10], [280, 18, 291, 16, "faceImageData"], [280, 31, 291, 29], [280, 34, 291, 32, "ctx"], [280, 37, 291, 35], [280, 38, 291, 36, "getImageData"], [280, 50, 291, 48], [280, 51, 291, 49, "paddedX"], [280, 58, 291, 56], [280, 60, 291, 58, "paddedY"], [280, 67, 291, 65], [280, 69, 291, 67, "<PERSON><PERSON><PERSON><PERSON>"], [280, 80, 291, 78], [280, 82, 291, 80, "paddedHeight"], [280, 94, 291, 92], [280, 95, 291, 93], [281, 12, 292, 10], [281, 18, 292, 16, "data"], [281, 22, 292, 20], [281, 25, 292, 23, "faceImageData"], [281, 38, 292, 36], [281, 39, 292, 37, "data"], [281, 43, 292, 41], [282, 12, 294, 10, "console"], [282, 19, 294, 17], [282, 20, 294, 18, "log"], [282, 23, 294, 21], [282, 24, 294, 22], [282, 64, 294, 62, "data"], [282, 68, 294, 66], [282, 69, 294, 67, "length"], [282, 75, 294, 73], [282, 86, 294, 84, "<PERSON><PERSON><PERSON><PERSON>"], [282, 97, 294, 95], [282, 101, 294, 99, "paddedHeight"], [282, 113, 294, 111], [282, 122, 294, 120], [282, 123, 294, 121], [284, 12, 296, 10], [285, 12, 297, 10], [285, 18, 297, 16, "pixelSize"], [285, 27, 297, 25], [285, 30, 297, 28, "Math"], [285, 34, 297, 32], [285, 35, 297, 33, "max"], [285, 38, 297, 36], [285, 39, 297, 37], [285, 41, 297, 39], [285, 43, 297, 41, "Math"], [285, 47, 297, 45], [285, 48, 297, 46, "min"], [285, 51, 297, 49], [285, 52, 297, 50, "<PERSON><PERSON><PERSON><PERSON>"], [285, 63, 297, 61], [285, 65, 297, 63, "paddedHeight"], [285, 77, 297, 75], [285, 78, 297, 76], [285, 81, 297, 79], [285, 83, 297, 81], [285, 84, 297, 82], [285, 85, 297, 83], [285, 86, 297, 84], [286, 12, 298, 10, "console"], [286, 19, 298, 17], [286, 20, 298, 18, "log"], [286, 23, 298, 21], [286, 24, 298, 22], [286, 64, 298, 62, "pixelSize"], [286, 73, 298, 71], [286, 90, 298, 88], [286, 91, 298, 89], [287, 12, 299, 10], [287, 17, 299, 15], [287, 21, 299, 19, "y"], [287, 22, 299, 20], [287, 25, 299, 23], [287, 26, 299, 24], [287, 28, 299, 26, "y"], [287, 29, 299, 27], [287, 32, 299, 30, "paddedHeight"], [287, 44, 299, 42], [287, 46, 299, 44, "y"], [287, 47, 299, 45], [287, 51, 299, 49, "pixelSize"], [287, 60, 299, 58], [287, 62, 299, 60], [288, 14, 300, 12], [288, 19, 300, 17], [288, 23, 300, 21, "x"], [288, 24, 300, 22], [288, 27, 300, 25], [288, 28, 300, 26], [288, 30, 300, 28, "x"], [288, 31, 300, 29], [288, 34, 300, 32, "<PERSON><PERSON><PERSON><PERSON>"], [288, 45, 300, 43], [288, 47, 300, 45, "x"], [288, 48, 300, 46], [288, 52, 300, 50, "pixelSize"], [288, 61, 300, 59], [288, 63, 300, 61], [289, 16, 301, 14], [290, 16, 302, 14], [290, 22, 302, 20, "pixelIndex"], [290, 32, 302, 30], [290, 35, 302, 33], [290, 36, 302, 34, "y"], [290, 37, 302, 35], [290, 40, 302, 38, "<PERSON><PERSON><PERSON><PERSON>"], [290, 51, 302, 49], [290, 54, 302, 52, "x"], [290, 55, 302, 53], [290, 59, 302, 57], [290, 60, 302, 58], [291, 16, 303, 14], [291, 22, 303, 20, "r"], [291, 23, 303, 21], [291, 26, 303, 24, "data"], [291, 30, 303, 28], [291, 31, 303, 29, "pixelIndex"], [291, 41, 303, 39], [291, 42, 303, 40], [292, 16, 304, 14], [292, 22, 304, 20, "g"], [292, 23, 304, 21], [292, 26, 304, 24, "data"], [292, 30, 304, 28], [292, 31, 304, 29, "pixelIndex"], [292, 41, 304, 39], [292, 44, 304, 42], [292, 45, 304, 43], [292, 46, 304, 44], [293, 16, 305, 14], [293, 22, 305, 20, "b"], [293, 23, 305, 21], [293, 26, 305, 24, "data"], [293, 30, 305, 28], [293, 31, 305, 29, "pixelIndex"], [293, 41, 305, 39], [293, 44, 305, 42], [293, 45, 305, 43], [293, 46, 305, 44], [294, 16, 306, 14], [294, 22, 306, 20, "a"], [294, 23, 306, 21], [294, 26, 306, 24, "data"], [294, 30, 306, 28], [294, 31, 306, 29, "pixelIndex"], [294, 41, 306, 39], [294, 44, 306, 42], [294, 45, 306, 43], [294, 46, 306, 44], [296, 16, 308, 14], [297, 16, 309, 14], [297, 21, 309, 19], [297, 25, 309, 23, "dy"], [297, 27, 309, 25], [297, 30, 309, 28], [297, 31, 309, 29], [297, 33, 309, 31, "dy"], [297, 35, 309, 33], [297, 38, 309, 36, "pixelSize"], [297, 47, 309, 45], [297, 51, 309, 49, "y"], [297, 52, 309, 50], [297, 55, 309, 53, "dy"], [297, 57, 309, 55], [297, 60, 309, 58, "paddedHeight"], [297, 72, 309, 70], [297, 74, 309, 72, "dy"], [297, 76, 309, 74], [297, 78, 309, 76], [297, 80, 309, 78], [298, 18, 310, 16], [298, 23, 310, 21], [298, 27, 310, 25, "dx"], [298, 29, 310, 27], [298, 32, 310, 30], [298, 33, 310, 31], [298, 35, 310, 33, "dx"], [298, 37, 310, 35], [298, 40, 310, 38, "pixelSize"], [298, 49, 310, 47], [298, 53, 310, 51, "x"], [298, 54, 310, 52], [298, 57, 310, 55, "dx"], [298, 59, 310, 57], [298, 62, 310, 60, "<PERSON><PERSON><PERSON><PERSON>"], [298, 73, 310, 71], [298, 75, 310, 73, "dx"], [298, 77, 310, 75], [298, 79, 310, 77], [298, 81, 310, 79], [299, 20, 311, 18], [299, 26, 311, 24, "blockPixelIndex"], [299, 41, 311, 39], [299, 44, 311, 42], [299, 45, 311, 43], [299, 46, 311, 44, "y"], [299, 47, 311, 45], [299, 50, 311, 48, "dy"], [299, 52, 311, 50], [299, 56, 311, 54, "<PERSON><PERSON><PERSON><PERSON>"], [299, 67, 311, 65], [299, 71, 311, 69, "x"], [299, 72, 311, 70], [299, 75, 311, 73, "dx"], [299, 77, 311, 75], [299, 78, 311, 76], [299, 82, 311, 80], [299, 83, 311, 81], [300, 20, 312, 18, "data"], [300, 24, 312, 22], [300, 25, 312, 23, "blockPixelIndex"], [300, 40, 312, 38], [300, 41, 312, 39], [300, 44, 312, 42, "r"], [300, 45, 312, 43], [301, 20, 313, 18, "data"], [301, 24, 313, 22], [301, 25, 313, 23, "blockPixelIndex"], [301, 40, 313, 38], [301, 43, 313, 41], [301, 44, 313, 42], [301, 45, 313, 43], [301, 48, 313, 46, "g"], [301, 49, 313, 47], [302, 20, 314, 18, "data"], [302, 24, 314, 22], [302, 25, 314, 23, "blockPixelIndex"], [302, 40, 314, 38], [302, 43, 314, 41], [302, 44, 314, 42], [302, 45, 314, 43], [302, 48, 314, 46, "b"], [302, 49, 314, 47], [303, 20, 315, 18, "data"], [303, 24, 315, 22], [303, 25, 315, 23, "blockPixelIndex"], [303, 40, 315, 38], [303, 43, 315, 41], [303, 44, 315, 42], [303, 45, 315, 43], [303, 48, 315, 46, "a"], [303, 49, 315, 47], [304, 18, 316, 16], [305, 16, 317, 14], [306, 14, 318, 12], [307, 12, 319, 10], [309, 12, 321, 10], [310, 12, 322, 10, "ctx"], [310, 15, 322, 13], [310, 16, 322, 14, "putImageData"], [310, 28, 322, 26], [310, 29, 322, 27, "faceImageData"], [310, 42, 322, 40], [310, 44, 322, 42, "paddedX"], [310, 51, 322, 49], [310, 53, 322, 51, "paddedY"], [310, 60, 322, 58], [310, 61, 322, 59], [311, 12, 323, 10, "console"], [311, 19, 323, 17], [311, 20, 323, 18, "log"], [311, 23, 323, 21], [311, 24, 323, 22], [311, 50, 323, 48, "index"], [311, 55, 323, 53], [311, 58, 323, 56], [311, 59, 323, 57], [311, 91, 323, 89], [311, 92, 323, 90], [312, 10, 324, 8], [312, 11, 324, 9], [312, 12, 324, 10], [313, 10, 325, 8, "console"], [313, 17, 325, 15], [313, 18, 325, 16, "log"], [313, 21, 325, 19], [313, 22, 325, 20], [313, 48, 325, 46, "detectedFaces"], [313, 61, 325, 59], [313, 62, 325, 60, "length"], [313, 68, 325, 66], [313, 95, 325, 93], [313, 96, 325, 94], [314, 8, 326, 6], [314, 9, 326, 7], [314, 15, 326, 13], [315, 10, 327, 8, "console"], [315, 17, 327, 15], [315, 18, 327, 16, "log"], [315, 21, 327, 19], [315, 22, 327, 20], [315, 91, 327, 89], [315, 92, 327, 90], [316, 8, 328, 6], [317, 8, 330, 6, "setProcessingProgress"], [317, 29, 330, 27], [317, 30, 330, 28], [317, 32, 330, 30], [317, 33, 330, 31], [319, 8, 332, 6], [320, 8, 333, 6, "console"], [320, 15, 333, 13], [320, 16, 333, 14, "log"], [320, 19, 333, 17], [320, 20, 333, 18], [320, 85, 333, 83], [320, 86, 333, 84], [321, 8, 334, 6], [321, 14, 334, 12, "blurredImageBlob"], [321, 30, 334, 28], [321, 33, 334, 31], [321, 39, 334, 37], [321, 43, 334, 41, "Promise"], [321, 50, 334, 48], [321, 51, 334, 56, "resolve"], [321, 58, 334, 63], [321, 62, 334, 68], [322, 10, 335, 8, "canvas"], [322, 16, 335, 14], [322, 17, 335, 15, "toBlob"], [322, 23, 335, 21], [322, 24, 335, 23, "blob"], [322, 28, 335, 27], [322, 32, 335, 32, "resolve"], [322, 39, 335, 39], [322, 40, 335, 40, "blob"], [322, 44, 335, 45], [322, 45, 335, 46], [322, 47, 335, 48], [322, 59, 335, 60], [322, 61, 335, 62], [322, 64, 335, 65], [322, 65, 335, 66], [323, 8, 336, 6], [323, 9, 336, 7], [323, 10, 336, 8], [324, 8, 338, 6], [324, 14, 338, 12, "blurredImageUrl"], [324, 29, 338, 27], [324, 32, 338, 30, "URL"], [324, 35, 338, 33], [324, 36, 338, 34, "createObjectURL"], [324, 51, 338, 49], [324, 52, 338, 50, "blurredImageBlob"], [324, 68, 338, 66], [324, 69, 338, 67], [325, 8, 339, 6, "console"], [325, 15, 339, 13], [325, 16, 339, 14, "log"], [325, 19, 339, 17], [325, 20, 339, 18], [325, 66, 339, 64], [325, 68, 339, 66, "blurredImageUrl"], [325, 83, 339, 81], [325, 84, 339, 82, "substring"], [325, 93, 339, 91], [325, 94, 339, 92], [325, 95, 339, 93], [325, 97, 339, 95], [325, 99, 339, 97], [325, 100, 339, 98], [325, 103, 339, 101], [325, 108, 339, 106], [325, 109, 339, 107], [326, 8, 341, 6, "setProcessingProgress"], [326, 29, 341, 27], [326, 30, 341, 28], [326, 33, 341, 31], [326, 34, 341, 32], [328, 8, 343, 6], [329, 8, 344, 6], [329, 14, 344, 12, "completeProcessing"], [329, 32, 344, 30], [329, 33, 344, 31, "blurredImageUrl"], [329, 48, 344, 46], [329, 49, 344, 47], [330, 6, 346, 4], [330, 7, 346, 5], [330, 8, 346, 6], [330, 15, 346, 13, "error"], [330, 20, 346, 18], [330, 22, 346, 20], [331, 8, 347, 6, "console"], [331, 15, 347, 13], [331, 16, 347, 14, "error"], [331, 21, 347, 19], [331, 22, 347, 20], [331, 57, 347, 55], [331, 59, 347, 57, "error"], [331, 64, 347, 62], [331, 65, 347, 63], [332, 8, 348, 6, "setErrorMessage"], [332, 23, 348, 21], [332, 24, 348, 22], [332, 50, 348, 48], [332, 51, 348, 49], [333, 8, 349, 6, "setProcessingState"], [333, 26, 349, 24], [333, 27, 349, 25], [333, 34, 349, 32], [333, 35, 349, 33], [334, 6, 350, 4], [335, 4, 351, 2], [335, 5, 351, 3], [337, 4, 353, 2], [338, 4, 354, 2], [338, 10, 354, 8, "completeProcessing"], [338, 28, 354, 26], [338, 31, 354, 29], [338, 37, 354, 36, "blurredImageUrl"], [338, 52, 354, 59], [338, 56, 354, 64], [339, 6, 355, 4], [339, 10, 355, 8], [340, 8, 356, 6, "setProcessingState"], [340, 26, 356, 24], [340, 27, 356, 25], [340, 37, 356, 35], [340, 38, 356, 36], [342, 8, 358, 6], [343, 8, 359, 6], [343, 14, 359, 12, "timestamp"], [343, 23, 359, 21], [343, 26, 359, 24, "Date"], [343, 30, 359, 28], [343, 31, 359, 29, "now"], [343, 34, 359, 32], [343, 35, 359, 33], [343, 36, 359, 34], [344, 8, 360, 6], [344, 14, 360, 12, "result"], [344, 20, 360, 18], [344, 23, 360, 21], [345, 10, 361, 8, "imageUrl"], [345, 18, 361, 16], [345, 20, 361, 18, "blurredImageUrl"], [345, 35, 361, 33], [346, 10, 362, 8, "localUri"], [346, 18, 362, 16], [346, 20, 362, 18, "blurredImageUrl"], [346, 35, 362, 33], [347, 10, 363, 8, "challengeCode"], [347, 23, 363, 21], [347, 25, 363, 23, "challengeCode"], [347, 38, 363, 36], [347, 42, 363, 40], [347, 44, 363, 42], [348, 10, 364, 8, "timestamp"], [348, 19, 364, 17], [349, 10, 365, 8, "jobId"], [349, 15, 365, 13], [349, 17, 365, 15], [349, 27, 365, 25, "timestamp"], [349, 36, 365, 34], [349, 38, 365, 36], [350, 10, 366, 8, "status"], [350, 16, 366, 14], [350, 18, 366, 16], [351, 8, 367, 6], [351, 9, 367, 7], [352, 8, 369, 6, "console"], [352, 15, 369, 13], [352, 16, 369, 14, "log"], [352, 19, 369, 17], [352, 20, 369, 18], [352, 100, 369, 98], [352, 102, 369, 100], [353, 10, 370, 8, "imageUrl"], [353, 18, 370, 16], [353, 20, 370, 18, "blurredImageUrl"], [353, 35, 370, 33], [353, 36, 370, 34, "substring"], [353, 45, 370, 43], [353, 46, 370, 44], [353, 47, 370, 45], [353, 49, 370, 47], [353, 51, 370, 49], [353, 52, 370, 50], [353, 55, 370, 53], [353, 60, 370, 58], [354, 10, 371, 8, "timestamp"], [354, 19, 371, 17], [355, 10, 372, 8, "jobId"], [355, 15, 372, 13], [355, 17, 372, 15, "result"], [355, 23, 372, 21], [355, 24, 372, 22, "jobId"], [356, 8, 373, 6], [356, 9, 373, 7], [356, 10, 373, 8], [358, 8, 375, 6], [359, 8, 376, 6, "onComplete"], [359, 18, 376, 16], [359, 19, 376, 17, "result"], [359, 25, 376, 23], [359, 26, 376, 24], [360, 6, 378, 4], [360, 7, 378, 5], [360, 8, 378, 6], [360, 15, 378, 13, "error"], [360, 20, 378, 18], [360, 22, 378, 20], [361, 8, 379, 6, "console"], [361, 15, 379, 13], [361, 16, 379, 14, "error"], [361, 21, 379, 19], [361, 22, 379, 20], [361, 57, 379, 55], [361, 59, 379, 57, "error"], [361, 64, 379, 62], [361, 65, 379, 63], [362, 8, 380, 6, "setErrorMessage"], [362, 23, 380, 21], [362, 24, 380, 22], [362, 56, 380, 54], [362, 57, 380, 55], [363, 8, 381, 6, "setProcessingState"], [363, 26, 381, 24], [363, 27, 381, 25], [363, 34, 381, 32], [363, 35, 381, 33], [364, 6, 382, 4], [365, 4, 383, 2], [365, 5, 383, 3], [367, 4, 385, 2], [368, 4, 386, 2], [368, 10, 386, 8, "triggerServerProcessing"], [368, 33, 386, 31], [368, 36, 386, 34], [368, 42, 386, 34, "triggerServerProcessing"], [368, 43, 386, 41, "privateImageUrl"], [368, 58, 386, 64], [368, 60, 386, 66, "timestamp"], [368, 69, 386, 83], [368, 74, 386, 88], [369, 6, 387, 4], [369, 10, 387, 8], [370, 8, 388, 6, "console"], [370, 15, 388, 13], [370, 16, 388, 14, "log"], [370, 19, 388, 17], [370, 20, 388, 18], [370, 74, 388, 72], [370, 76, 388, 74, "privateImageUrl"], [370, 91, 388, 89], [370, 92, 388, 90], [371, 8, 389, 6, "setProcessingState"], [371, 26, 389, 24], [371, 27, 389, 25], [371, 39, 389, 37], [371, 40, 389, 38], [372, 8, 390, 6, "setProcessingProgress"], [372, 29, 390, 27], [372, 30, 390, 28], [372, 32, 390, 30], [372, 33, 390, 31], [373, 8, 392, 6], [373, 14, 392, 12, "requestBody"], [373, 25, 392, 23], [373, 28, 392, 26], [374, 10, 393, 8, "imageUrl"], [374, 18, 393, 16], [374, 20, 393, 18, "privateImageUrl"], [374, 35, 393, 33], [375, 10, 394, 8, "userId"], [375, 16, 394, 14], [376, 10, 395, 8, "requestId"], [376, 19, 395, 17], [377, 10, 396, 8, "timestamp"], [377, 19, 396, 17], [378, 10, 397, 8, "platform"], [378, 18, 397, 16], [378, 20, 397, 18], [379, 8, 398, 6], [379, 9, 398, 7], [380, 8, 400, 6, "console"], [380, 15, 400, 13], [380, 16, 400, 14, "log"], [380, 19, 400, 17], [380, 20, 400, 18], [380, 65, 400, 63], [380, 67, 400, 65, "requestBody"], [380, 78, 400, 76], [380, 79, 400, 77], [382, 8, 402, 6], [383, 8, 403, 6], [383, 14, 403, 12, "response"], [383, 22, 403, 20], [383, 25, 403, 23], [383, 31, 403, 29, "fetch"], [383, 36, 403, 34], [383, 37, 403, 35], [383, 40, 403, 38, "API_BASE_URL"], [383, 52, 403, 50], [383, 72, 403, 70], [383, 74, 403, 72], [384, 10, 404, 8, "method"], [384, 16, 404, 14], [384, 18, 404, 16], [384, 24, 404, 22], [385, 10, 405, 8, "headers"], [385, 17, 405, 15], [385, 19, 405, 17], [386, 12, 406, 10], [386, 26, 406, 24], [386, 28, 406, 26], [386, 46, 406, 44], [387, 12, 407, 10], [387, 27, 407, 25], [387, 29, 407, 27], [387, 39, 407, 37], [387, 45, 407, 43, "getAuthToken"], [387, 57, 407, 55], [387, 58, 407, 56], [387, 59, 407, 57], [388, 10, 408, 8], [388, 11, 408, 9], [389, 10, 409, 8, "body"], [389, 14, 409, 12], [389, 16, 409, 14, "JSON"], [389, 20, 409, 18], [389, 21, 409, 19, "stringify"], [389, 30, 409, 28], [389, 31, 409, 29, "requestBody"], [389, 42, 409, 40], [390, 8, 410, 6], [390, 9, 410, 7], [390, 10, 410, 8], [391, 8, 412, 6], [391, 12, 412, 10], [391, 13, 412, 11, "response"], [391, 21, 412, 19], [391, 22, 412, 20, "ok"], [391, 24, 412, 22], [391, 26, 412, 24], [392, 10, 413, 8], [392, 16, 413, 14, "errorText"], [392, 25, 413, 23], [392, 28, 413, 26], [392, 34, 413, 32, "response"], [392, 42, 413, 40], [392, 43, 413, 41, "text"], [392, 47, 413, 45], [392, 48, 413, 46], [392, 49, 413, 47], [393, 10, 414, 8, "console"], [393, 17, 414, 15], [393, 18, 414, 16, "error"], [393, 23, 414, 21], [393, 24, 414, 22], [393, 68, 414, 66], [393, 70, 414, 68, "response"], [393, 78, 414, 76], [393, 79, 414, 77, "status"], [393, 85, 414, 83], [393, 87, 414, 85, "errorText"], [393, 96, 414, 94], [393, 97, 414, 95], [394, 10, 415, 8], [394, 16, 415, 14], [394, 20, 415, 18, "Error"], [394, 25, 415, 23], [394, 26, 415, 24], [394, 48, 415, 46, "response"], [394, 56, 415, 54], [394, 57, 415, 55, "status"], [394, 63, 415, 61], [394, 67, 415, 65, "response"], [394, 75, 415, 73], [394, 76, 415, 74, "statusText"], [394, 86, 415, 84], [394, 88, 415, 86], [394, 89, 415, 87], [395, 8, 416, 6], [396, 8, 418, 6], [396, 14, 418, 12, "result"], [396, 20, 418, 18], [396, 23, 418, 21], [396, 29, 418, 27, "response"], [396, 37, 418, 35], [396, 38, 418, 36, "json"], [396, 42, 418, 40], [396, 43, 418, 41], [396, 44, 418, 42], [397, 8, 419, 6, "console"], [397, 15, 419, 13], [397, 16, 419, 14, "log"], [397, 19, 419, 17], [397, 20, 419, 18], [397, 68, 419, 66], [397, 70, 419, 68, "result"], [397, 76, 419, 74], [397, 77, 419, 75], [398, 8, 421, 6], [398, 12, 421, 10], [398, 13, 421, 11, "result"], [398, 19, 421, 17], [398, 20, 421, 18, "jobId"], [398, 25, 421, 23], [398, 27, 421, 25], [399, 10, 422, 8], [399, 16, 422, 14], [399, 20, 422, 18, "Error"], [399, 25, 422, 23], [399, 26, 422, 24], [399, 70, 422, 68], [399, 71, 422, 69], [400, 8, 423, 6], [402, 8, 425, 6], [403, 8, 426, 6], [403, 14, 426, 12, "pollForCompletion"], [403, 31, 426, 29], [403, 32, 426, 30, "result"], [403, 38, 426, 36], [403, 39, 426, 37, "jobId"], [403, 44, 426, 42], [403, 46, 426, 44, "timestamp"], [403, 55, 426, 53], [403, 56, 426, 54], [404, 6, 427, 4], [404, 7, 427, 5], [404, 8, 427, 6], [404, 15, 427, 13, "error"], [404, 20, 427, 18], [404, 22, 427, 20], [405, 8, 428, 6, "console"], [405, 15, 428, 13], [405, 16, 428, 14, "error"], [405, 21, 428, 19], [405, 22, 428, 20], [405, 57, 428, 55], [405, 59, 428, 57, "error"], [405, 64, 428, 62], [405, 65, 428, 63], [406, 8, 429, 6, "setErrorMessage"], [406, 23, 429, 21], [406, 24, 429, 22], [406, 52, 429, 50, "error"], [406, 57, 429, 55], [406, 58, 429, 56, "message"], [406, 65, 429, 63], [406, 67, 429, 65], [406, 68, 429, 66], [407, 8, 430, 6, "setProcessingState"], [407, 26, 430, 24], [407, 27, 430, 25], [407, 34, 430, 32], [407, 35, 430, 33], [408, 6, 431, 4], [409, 4, 432, 2], [409, 5, 432, 3], [410, 4, 433, 2], [411, 4, 434, 2], [411, 10, 434, 8, "pollForCompletion"], [411, 27, 434, 25], [411, 30, 434, 28], [411, 36, 434, 28, "pollForCompletion"], [411, 37, 434, 35, "jobId"], [411, 42, 434, 48], [411, 44, 434, 50, "timestamp"], [411, 53, 434, 67], [411, 55, 434, 69, "attempts"], [411, 63, 434, 77], [411, 66, 434, 80], [411, 67, 434, 81], [411, 72, 434, 86], [412, 6, 435, 4], [412, 12, 435, 10, "MAX_ATTEMPTS"], [412, 24, 435, 22], [412, 27, 435, 25], [412, 29, 435, 27], [412, 30, 435, 28], [412, 31, 435, 29], [413, 6, 436, 4], [413, 12, 436, 10, "POLL_INTERVAL"], [413, 25, 436, 23], [413, 28, 436, 26], [413, 32, 436, 30], [413, 33, 436, 31], [413, 34, 436, 32], [415, 6, 438, 4, "console"], [415, 13, 438, 11], [415, 14, 438, 12, "log"], [415, 17, 438, 15], [415, 18, 438, 16], [415, 53, 438, 51, "attempts"], [415, 61, 438, 59], [415, 64, 438, 62], [415, 65, 438, 63], [415, 69, 438, 67, "MAX_ATTEMPTS"], [415, 81, 438, 79], [415, 93, 438, 91, "jobId"], [415, 98, 438, 96], [415, 100, 438, 98], [415, 101, 438, 99], [416, 6, 440, 4], [416, 10, 440, 8, "attempts"], [416, 18, 440, 16], [416, 22, 440, 20, "MAX_ATTEMPTS"], [416, 34, 440, 32], [416, 36, 440, 34], [417, 8, 441, 6, "console"], [417, 15, 441, 13], [417, 16, 441, 14, "error"], [417, 21, 441, 19], [417, 22, 441, 20], [417, 75, 441, 73], [417, 76, 441, 74], [418, 8, 442, 6, "setErrorMessage"], [418, 23, 442, 21], [418, 24, 442, 22], [418, 63, 442, 61], [418, 64, 442, 62], [419, 8, 443, 6, "setProcessingState"], [419, 26, 443, 24], [419, 27, 443, 25], [419, 34, 443, 32], [419, 35, 443, 33], [420, 8, 444, 6], [421, 6, 445, 4], [422, 6, 447, 4], [422, 10, 447, 8], [423, 8, 448, 6], [423, 14, 448, 12, "response"], [423, 22, 448, 20], [423, 25, 448, 23], [423, 31, 448, 29, "fetch"], [423, 36, 448, 34], [423, 37, 448, 35], [423, 40, 448, 38, "API_BASE_URL"], [423, 52, 448, 50], [423, 75, 448, 73, "jobId"], [423, 80, 448, 78], [423, 82, 448, 80], [423, 84, 448, 82], [424, 10, 449, 8, "headers"], [424, 17, 449, 15], [424, 19, 449, 17], [425, 12, 450, 10], [425, 27, 450, 25], [425, 29, 450, 27], [425, 39, 450, 37], [425, 45, 450, 43, "getAuthToken"], [425, 57, 450, 55], [425, 58, 450, 56], [425, 59, 450, 57], [426, 10, 451, 8], [427, 8, 452, 6], [427, 9, 452, 7], [427, 10, 452, 8], [428, 8, 454, 6], [428, 12, 454, 10], [428, 13, 454, 11, "response"], [428, 21, 454, 19], [428, 22, 454, 20, "ok"], [428, 24, 454, 22], [428, 26, 454, 24], [429, 10, 455, 8], [429, 16, 455, 14], [429, 20, 455, 18, "Error"], [429, 25, 455, 23], [429, 26, 455, 24], [429, 34, 455, 32, "response"], [429, 42, 455, 40], [429, 43, 455, 41, "status"], [429, 49, 455, 47], [429, 54, 455, 52, "response"], [429, 62, 455, 60], [429, 63, 455, 61, "statusText"], [429, 73, 455, 71], [429, 75, 455, 73], [429, 76, 455, 74], [430, 8, 456, 6], [431, 8, 458, 6], [431, 14, 458, 12, "status"], [431, 20, 458, 18], [431, 23, 458, 21], [431, 29, 458, 27, "response"], [431, 37, 458, 35], [431, 38, 458, 36, "json"], [431, 42, 458, 40], [431, 43, 458, 41], [431, 44, 458, 42], [432, 8, 459, 6, "console"], [432, 15, 459, 13], [432, 16, 459, 14, "log"], [432, 19, 459, 17], [432, 20, 459, 18], [432, 54, 459, 52], [432, 56, 459, 54, "status"], [432, 62, 459, 60], [432, 63, 459, 61], [433, 8, 461, 6], [433, 12, 461, 10, "status"], [433, 18, 461, 16], [433, 19, 461, 17, "status"], [433, 25, 461, 23], [433, 30, 461, 28], [433, 41, 461, 39], [433, 43, 461, 41], [434, 10, 462, 8, "console"], [434, 17, 462, 15], [434, 18, 462, 16, "log"], [434, 21, 462, 19], [434, 22, 462, 20], [434, 73, 462, 71], [434, 74, 462, 72], [435, 10, 463, 8, "setProcessingProgress"], [435, 31, 463, 29], [435, 32, 463, 30], [435, 35, 463, 33], [435, 36, 463, 34], [436, 10, 464, 8, "setProcessingState"], [436, 28, 464, 26], [436, 29, 464, 27], [436, 40, 464, 38], [436, 41, 464, 39], [437, 10, 465, 8], [438, 10, 466, 8], [438, 16, 466, 14, "result"], [438, 22, 466, 20], [438, 25, 466, 23], [439, 12, 467, 10, "imageUrl"], [439, 20, 467, 18], [439, 22, 467, 20, "status"], [439, 28, 467, 26], [439, 29, 467, 27, "publicUrl"], [439, 38, 467, 36], [440, 12, 467, 38], [441, 12, 468, 10, "localUri"], [441, 20, 468, 18], [441, 22, 468, 20, "capturedPhoto"], [441, 35, 468, 33], [441, 39, 468, 37, "status"], [441, 45, 468, 43], [441, 46, 468, 44, "publicUrl"], [441, 55, 468, 53], [442, 12, 468, 55], [443, 12, 469, 10, "challengeCode"], [443, 25, 469, 23], [443, 27, 469, 25, "challengeCode"], [443, 40, 469, 38], [443, 44, 469, 42], [443, 46, 469, 44], [444, 12, 470, 10, "timestamp"], [444, 21, 470, 19], [445, 12, 471, 10, "processingStatus"], [445, 28, 471, 26], [445, 30, 471, 28], [446, 10, 472, 8], [446, 11, 472, 9], [447, 10, 473, 8, "console"], [447, 17, 473, 15], [447, 18, 473, 16, "log"], [447, 21, 473, 19], [447, 22, 473, 20], [447, 57, 473, 55], [447, 59, 473, 57, "result"], [447, 65, 473, 63], [447, 66, 473, 64], [448, 10, 474, 8, "onComplete"], [448, 20, 474, 18], [448, 21, 474, 19, "result"], [448, 27, 474, 25], [448, 28, 474, 26], [449, 10, 475, 8], [450, 8, 476, 6], [450, 9, 476, 7], [450, 15, 476, 13], [450, 19, 476, 17, "status"], [450, 25, 476, 23], [450, 26, 476, 24, "status"], [450, 32, 476, 30], [450, 37, 476, 35], [450, 45, 476, 43], [450, 47, 476, 45], [451, 10, 477, 8, "console"], [451, 17, 477, 15], [451, 18, 477, 16, "error"], [451, 23, 477, 21], [451, 24, 477, 22], [451, 60, 477, 58], [451, 62, 477, 60, "status"], [451, 68, 477, 66], [451, 69, 477, 67, "error"], [451, 74, 477, 72], [451, 75, 477, 73], [452, 10, 478, 8], [452, 16, 478, 14], [452, 20, 478, 18, "Error"], [452, 25, 478, 23], [452, 26, 478, 24, "status"], [452, 32, 478, 30], [452, 33, 478, 31, "error"], [452, 38, 478, 36], [452, 42, 478, 40], [452, 61, 478, 59], [452, 62, 478, 60], [453, 8, 479, 6], [453, 9, 479, 7], [453, 15, 479, 13], [454, 10, 480, 8], [455, 10, 481, 8], [455, 16, 481, 14, "progressValue"], [455, 29, 481, 27], [455, 32, 481, 30], [455, 34, 481, 32], [455, 37, 481, 36, "attempts"], [455, 45, 481, 44], [455, 48, 481, 47, "MAX_ATTEMPTS"], [455, 60, 481, 59], [455, 63, 481, 63], [455, 65, 481, 65], [456, 10, 482, 8, "console"], [456, 17, 482, 15], [456, 18, 482, 16, "log"], [456, 21, 482, 19], [456, 22, 482, 20], [456, 71, 482, 69, "progressValue"], [456, 84, 482, 82], [456, 87, 482, 85], [456, 88, 482, 86], [457, 10, 483, 8, "setProcessingProgress"], [457, 31, 483, 29], [457, 32, 483, 30, "progressValue"], [457, 45, 483, 43], [457, 46, 483, 44], [458, 10, 485, 8, "setTimeout"], [458, 20, 485, 18], [458, 21, 485, 19], [458, 27, 485, 25], [459, 12, 486, 10, "pollForCompletion"], [459, 29, 486, 27], [459, 30, 486, 28, "jobId"], [459, 35, 486, 33], [459, 37, 486, 35, "timestamp"], [459, 46, 486, 44], [459, 48, 486, 46, "attempts"], [459, 56, 486, 54], [459, 59, 486, 57], [459, 60, 486, 58], [459, 61, 486, 59], [460, 10, 487, 8], [460, 11, 487, 9], [460, 13, 487, 11, "POLL_INTERVAL"], [460, 26, 487, 24], [460, 27, 487, 25], [461, 8, 488, 6], [462, 6, 489, 4], [462, 7, 489, 5], [462, 8, 489, 6], [462, 15, 489, 13, "error"], [462, 20, 489, 18], [462, 22, 489, 20], [463, 8, 490, 6, "console"], [463, 15, 490, 13], [463, 16, 490, 14, "error"], [463, 21, 490, 19], [463, 22, 490, 20], [463, 54, 490, 52], [463, 56, 490, 54, "error"], [463, 61, 490, 59], [463, 62, 490, 60], [464, 8, 491, 6, "setErrorMessage"], [464, 23, 491, 21], [464, 24, 491, 22], [464, 62, 491, 60, "error"], [464, 67, 491, 65], [464, 68, 491, 66, "message"], [464, 75, 491, 73], [464, 77, 491, 75], [464, 78, 491, 76], [465, 8, 492, 6, "setProcessingState"], [465, 26, 492, 24], [465, 27, 492, 25], [465, 34, 492, 32], [465, 35, 492, 33], [466, 6, 493, 4], [467, 4, 494, 2], [467, 5, 494, 3], [468, 4, 495, 2], [469, 4, 496, 2], [469, 10, 496, 8, "getAuthToken"], [469, 22, 496, 20], [469, 25, 496, 23], [469, 31, 496, 23, "getAuthToken"], [469, 32, 496, 23], [469, 37, 496, 52], [470, 6, 497, 4], [471, 6, 498, 4], [472, 6, 499, 4], [472, 13, 499, 11], [472, 30, 499, 28], [473, 4, 500, 2], [473, 5, 500, 3], [475, 4, 502, 2], [476, 4, 503, 2], [476, 10, 503, 8, "retryCapture"], [476, 22, 503, 20], [476, 25, 503, 23], [476, 29, 503, 23, "useCallback"], [476, 47, 503, 34], [476, 49, 503, 35], [476, 55, 503, 41], [477, 6, 504, 4, "console"], [477, 13, 504, 11], [477, 14, 504, 12, "log"], [477, 17, 504, 15], [477, 18, 504, 16], [477, 55, 504, 53], [477, 56, 504, 54], [478, 6, 505, 4, "setProcessingState"], [478, 24, 505, 22], [478, 25, 505, 23], [478, 31, 505, 29], [478, 32, 505, 30], [479, 6, 506, 4, "setErrorMessage"], [479, 21, 506, 19], [479, 22, 506, 20], [479, 24, 506, 22], [479, 25, 506, 23], [480, 6, 507, 4, "setCapturedPhoto"], [480, 22, 507, 20], [480, 23, 507, 21], [480, 25, 507, 23], [480, 26, 507, 24], [481, 6, 508, 4, "setProcessingProgress"], [481, 27, 508, 25], [481, 28, 508, 26], [481, 29, 508, 27], [481, 30, 508, 28], [482, 4, 509, 2], [482, 5, 509, 3], [482, 7, 509, 5], [482, 9, 509, 7], [482, 10, 509, 8], [483, 4, 510, 2], [484, 4, 511, 2], [484, 8, 511, 2, "useEffect"], [484, 24, 511, 11], [484, 26, 511, 12], [484, 32, 511, 18], [485, 6, 512, 4, "console"], [485, 13, 512, 11], [485, 14, 512, 12, "log"], [485, 17, 512, 15], [485, 18, 512, 16], [485, 53, 512, 51], [485, 55, 512, 53, "permission"], [485, 65, 512, 63], [485, 66, 512, 64], [486, 6, 513, 4], [486, 10, 513, 8, "permission"], [486, 20, 513, 18], [486, 22, 513, 20], [487, 8, 514, 6, "console"], [487, 15, 514, 13], [487, 16, 514, 14, "log"], [487, 19, 514, 17], [487, 20, 514, 18], [487, 57, 514, 55], [487, 59, 514, 57, "permission"], [487, 69, 514, 67], [487, 70, 514, 68, "granted"], [487, 77, 514, 75], [487, 78, 514, 76], [488, 6, 515, 4], [489, 4, 516, 2], [489, 5, 516, 3], [489, 7, 516, 5], [489, 8, 516, 6, "permission"], [489, 18, 516, 16], [489, 19, 516, 17], [489, 20, 516, 18], [490, 4, 517, 2], [491, 4, 518, 2], [491, 8, 518, 6], [491, 9, 518, 7, "permission"], [491, 19, 518, 17], [491, 21, 518, 19], [492, 6, 519, 4, "console"], [492, 13, 519, 11], [492, 14, 519, 12, "log"], [492, 17, 519, 15], [492, 18, 519, 16], [492, 67, 519, 65], [492, 68, 519, 66], [493, 6, 520, 4], [493, 26, 521, 6], [493, 30, 521, 6, "_jsxDevRuntime"], [493, 44, 521, 6], [493, 45, 521, 6, "jsxDEV"], [493, 51, 521, 6], [493, 53, 521, 7, "_View"], [493, 58, 521, 7], [493, 59, 521, 7, "default"], [493, 66, 521, 11], [494, 8, 521, 12, "style"], [494, 13, 521, 17], [494, 15, 521, 19, "styles"], [494, 21, 521, 25], [494, 22, 521, 26, "container"], [494, 31, 521, 36], [495, 8, 521, 36, "children"], [495, 16, 521, 36], [495, 32, 522, 8], [495, 36, 522, 8, "_jsxDevRuntime"], [495, 50, 522, 8], [495, 51, 522, 8, "jsxDEV"], [495, 57, 522, 8], [495, 59, 522, 9, "_ActivityIndicator"], [495, 77, 522, 9], [495, 78, 522, 9, "default"], [495, 85, 522, 26], [496, 10, 522, 27, "size"], [496, 14, 522, 31], [496, 16, 522, 32], [496, 23, 522, 39], [497, 10, 522, 40, "color"], [497, 15, 522, 45], [497, 17, 522, 46], [498, 8, 522, 55], [499, 10, 522, 55, "fileName"], [499, 18, 522, 55], [499, 20, 522, 55, "_jsxFileName"], [499, 32, 522, 55], [500, 10, 522, 55, "lineNumber"], [500, 20, 522, 55], [501, 10, 522, 55, "columnNumber"], [501, 22, 522, 55], [502, 8, 522, 55], [502, 15, 522, 57], [502, 16, 522, 58], [502, 31, 523, 8], [502, 35, 523, 8, "_jsxDevRuntime"], [502, 49, 523, 8], [502, 50, 523, 8, "jsxDEV"], [502, 56, 523, 8], [502, 58, 523, 9, "_Text"], [502, 63, 523, 9], [502, 64, 523, 9, "default"], [502, 71, 523, 13], [503, 10, 523, 14, "style"], [503, 15, 523, 19], [503, 17, 523, 21, "styles"], [503, 23, 523, 27], [503, 24, 523, 28, "loadingText"], [503, 35, 523, 40], [504, 10, 523, 40, "children"], [504, 18, 523, 40], [504, 20, 523, 41], [505, 8, 523, 58], [506, 10, 523, 58, "fileName"], [506, 18, 523, 58], [506, 20, 523, 58, "_jsxFileName"], [506, 32, 523, 58], [507, 10, 523, 58, "lineNumber"], [507, 20, 523, 58], [508, 10, 523, 58, "columnNumber"], [508, 22, 523, 58], [509, 8, 523, 58], [509, 15, 523, 64], [509, 16, 523, 65], [510, 6, 523, 65], [511, 8, 523, 65, "fileName"], [511, 16, 523, 65], [511, 18, 523, 65, "_jsxFileName"], [511, 30, 523, 65], [512, 8, 523, 65, "lineNumber"], [512, 18, 523, 65], [513, 8, 523, 65, "columnNumber"], [513, 20, 523, 65], [514, 6, 523, 65], [514, 13, 524, 12], [514, 14, 524, 13], [515, 4, 526, 2], [516, 4, 527, 2], [516, 8, 527, 6], [516, 9, 527, 7, "permission"], [516, 19, 527, 17], [516, 20, 527, 18, "granted"], [516, 27, 527, 25], [516, 29, 527, 27], [517, 6, 528, 4, "console"], [517, 13, 528, 11], [517, 14, 528, 12, "log"], [517, 17, 528, 15], [517, 18, 528, 16], [517, 93, 528, 91], [517, 94, 528, 92], [518, 6, 529, 4], [518, 26, 530, 6], [518, 30, 530, 6, "_jsxDevRuntime"], [518, 44, 530, 6], [518, 45, 530, 6, "jsxDEV"], [518, 51, 530, 6], [518, 53, 530, 7, "_View"], [518, 58, 530, 7], [518, 59, 530, 7, "default"], [518, 66, 530, 11], [519, 8, 530, 12, "style"], [519, 13, 530, 17], [519, 15, 530, 19, "styles"], [519, 21, 530, 25], [519, 22, 530, 26, "container"], [519, 31, 530, 36], [520, 8, 530, 36, "children"], [520, 16, 530, 36], [520, 31, 531, 8], [520, 35, 531, 8, "_jsxDevRuntime"], [520, 49, 531, 8], [520, 50, 531, 8, "jsxDEV"], [520, 56, 531, 8], [520, 58, 531, 9, "_View"], [520, 63, 531, 9], [520, 64, 531, 9, "default"], [520, 71, 531, 13], [521, 10, 531, 14, "style"], [521, 15, 531, 19], [521, 17, 531, 21, "styles"], [521, 23, 531, 27], [521, 24, 531, 28, "permissionContent"], [521, 41, 531, 46], [522, 10, 531, 46, "children"], [522, 18, 531, 46], [522, 34, 532, 10], [522, 38, 532, 10, "_jsxDevRuntime"], [522, 52, 532, 10], [522, 53, 532, 10, "jsxDEV"], [522, 59, 532, 10], [522, 61, 532, 11, "_lucideReactNative"], [522, 79, 532, 11], [522, 80, 532, 11, "Camera"], [522, 86, 532, 21], [523, 12, 532, 22, "size"], [523, 16, 532, 26], [523, 18, 532, 28], [523, 20, 532, 31], [524, 12, 532, 32, "color"], [524, 17, 532, 37], [524, 19, 532, 38], [525, 10, 532, 47], [526, 12, 532, 47, "fileName"], [526, 20, 532, 47], [526, 22, 532, 47, "_jsxFileName"], [526, 34, 532, 47], [527, 12, 532, 47, "lineNumber"], [527, 22, 532, 47], [528, 12, 532, 47, "columnNumber"], [528, 24, 532, 47], [529, 10, 532, 47], [529, 17, 532, 49], [529, 18, 532, 50], [529, 33, 533, 10], [529, 37, 533, 10, "_jsxDevRuntime"], [529, 51, 533, 10], [529, 52, 533, 10, "jsxDEV"], [529, 58, 533, 10], [529, 60, 533, 11, "_Text"], [529, 65, 533, 11], [529, 66, 533, 11, "default"], [529, 73, 533, 15], [530, 12, 533, 16, "style"], [530, 17, 533, 21], [530, 19, 533, 23, "styles"], [530, 25, 533, 29], [530, 26, 533, 30, "permissionTitle"], [530, 41, 533, 46], [531, 12, 533, 46, "children"], [531, 20, 533, 46], [531, 22, 533, 47], [532, 10, 533, 73], [533, 12, 533, 73, "fileName"], [533, 20, 533, 73], [533, 22, 533, 73, "_jsxFileName"], [533, 34, 533, 73], [534, 12, 533, 73, "lineNumber"], [534, 22, 533, 73], [535, 12, 533, 73, "columnNumber"], [535, 24, 533, 73], [536, 10, 533, 73], [536, 17, 533, 79], [536, 18, 533, 80], [536, 33, 534, 10], [536, 37, 534, 10, "_jsxDevRuntime"], [536, 51, 534, 10], [536, 52, 534, 10, "jsxDEV"], [536, 58, 534, 10], [536, 60, 534, 11, "_Text"], [536, 65, 534, 11], [536, 66, 534, 11, "default"], [536, 73, 534, 15], [537, 12, 534, 16, "style"], [537, 17, 534, 21], [537, 19, 534, 23, "styles"], [537, 25, 534, 29], [537, 26, 534, 30, "permissionDescription"], [537, 47, 534, 52], [538, 12, 534, 52, "children"], [538, 20, 534, 52], [538, 22, 534, 53], [539, 10, 537, 10], [540, 12, 537, 10, "fileName"], [540, 20, 537, 10], [540, 22, 537, 10, "_jsxFileName"], [540, 34, 537, 10], [541, 12, 537, 10, "lineNumber"], [541, 22, 537, 10], [542, 12, 537, 10, "columnNumber"], [542, 24, 537, 10], [543, 10, 537, 10], [543, 17, 537, 16], [543, 18, 537, 17], [543, 33, 538, 10], [543, 37, 538, 10, "_jsxDevRuntime"], [543, 51, 538, 10], [543, 52, 538, 10, "jsxDEV"], [543, 58, 538, 10], [543, 60, 538, 11, "_TouchableOpacity"], [543, 77, 538, 11], [543, 78, 538, 11, "default"], [543, 85, 538, 27], [544, 12, 538, 28, "onPress"], [544, 19, 538, 35], [544, 21, 538, 37, "requestPermission"], [544, 38, 538, 55], [545, 12, 538, 56, "style"], [545, 17, 538, 61], [545, 19, 538, 63, "styles"], [545, 25, 538, 69], [545, 26, 538, 70, "primaryButton"], [545, 39, 538, 84], [546, 12, 538, 84, "children"], [546, 20, 538, 84], [546, 35, 539, 12], [546, 39, 539, 12, "_jsxDevRuntime"], [546, 53, 539, 12], [546, 54, 539, 12, "jsxDEV"], [546, 60, 539, 12], [546, 62, 539, 13, "_Text"], [546, 67, 539, 13], [546, 68, 539, 13, "default"], [546, 75, 539, 17], [547, 14, 539, 18, "style"], [547, 19, 539, 23], [547, 21, 539, 25, "styles"], [547, 27, 539, 31], [547, 28, 539, 32, "primaryButtonText"], [547, 45, 539, 50], [548, 14, 539, 50, "children"], [548, 22, 539, 50], [548, 24, 539, 51], [549, 12, 539, 67], [550, 14, 539, 67, "fileName"], [550, 22, 539, 67], [550, 24, 539, 67, "_jsxFileName"], [550, 36, 539, 67], [551, 14, 539, 67, "lineNumber"], [551, 24, 539, 67], [552, 14, 539, 67, "columnNumber"], [552, 26, 539, 67], [553, 12, 539, 67], [553, 19, 539, 73], [554, 10, 539, 74], [555, 12, 539, 74, "fileName"], [555, 20, 539, 74], [555, 22, 539, 74, "_jsxFileName"], [555, 34, 539, 74], [556, 12, 539, 74, "lineNumber"], [556, 22, 539, 74], [557, 12, 539, 74, "columnNumber"], [557, 24, 539, 74], [558, 10, 539, 74], [558, 17, 540, 28], [558, 18, 540, 29], [558, 33, 541, 10], [558, 37, 541, 10, "_jsxDevRuntime"], [558, 51, 541, 10], [558, 52, 541, 10, "jsxDEV"], [558, 58, 541, 10], [558, 60, 541, 11, "_TouchableOpacity"], [558, 77, 541, 11], [558, 78, 541, 11, "default"], [558, 85, 541, 27], [559, 12, 541, 28, "onPress"], [559, 19, 541, 35], [559, 21, 541, 37, "onCancel"], [559, 29, 541, 46], [560, 12, 541, 47, "style"], [560, 17, 541, 52], [560, 19, 541, 54, "styles"], [560, 25, 541, 60], [560, 26, 541, 61, "secondaryButton"], [560, 41, 541, 77], [561, 12, 541, 77, "children"], [561, 20, 541, 77], [561, 35, 542, 12], [561, 39, 542, 12, "_jsxDevRuntime"], [561, 53, 542, 12], [561, 54, 542, 12, "jsxDEV"], [561, 60, 542, 12], [561, 62, 542, 13, "_Text"], [561, 67, 542, 13], [561, 68, 542, 13, "default"], [561, 75, 542, 17], [562, 14, 542, 18, "style"], [562, 19, 542, 23], [562, 21, 542, 25, "styles"], [562, 27, 542, 31], [562, 28, 542, 32, "secondaryButtonText"], [562, 47, 542, 52], [563, 14, 542, 52, "children"], [563, 22, 542, 52], [563, 24, 542, 53], [564, 12, 542, 59], [565, 14, 542, 59, "fileName"], [565, 22, 542, 59], [565, 24, 542, 59, "_jsxFileName"], [565, 36, 542, 59], [566, 14, 542, 59, "lineNumber"], [566, 24, 542, 59], [567, 14, 542, 59, "columnNumber"], [567, 26, 542, 59], [568, 12, 542, 59], [568, 19, 542, 65], [569, 10, 542, 66], [570, 12, 542, 66, "fileName"], [570, 20, 542, 66], [570, 22, 542, 66, "_jsxFileName"], [570, 34, 542, 66], [571, 12, 542, 66, "lineNumber"], [571, 22, 542, 66], [572, 12, 542, 66, "columnNumber"], [572, 24, 542, 66], [573, 10, 542, 66], [573, 17, 543, 28], [573, 18, 543, 29], [574, 8, 543, 29], [575, 10, 543, 29, "fileName"], [575, 18, 543, 29], [575, 20, 543, 29, "_jsxFileName"], [575, 32, 543, 29], [576, 10, 543, 29, "lineNumber"], [576, 20, 543, 29], [577, 10, 543, 29, "columnNumber"], [577, 22, 543, 29], [578, 8, 543, 29], [578, 15, 544, 14], [579, 6, 544, 15], [580, 8, 544, 15, "fileName"], [580, 16, 544, 15], [580, 18, 544, 15, "_jsxFileName"], [580, 30, 544, 15], [581, 8, 544, 15, "lineNumber"], [581, 18, 544, 15], [582, 8, 544, 15, "columnNumber"], [582, 20, 544, 15], [583, 6, 544, 15], [583, 13, 545, 12], [583, 14, 545, 13], [584, 4, 547, 2], [585, 4, 548, 2], [586, 4, 549, 2, "console"], [586, 11, 549, 9], [586, 12, 549, 10, "log"], [586, 15, 549, 13], [586, 16, 549, 14], [586, 55, 549, 53], [586, 56, 549, 54], [587, 4, 551, 2], [587, 24, 552, 4], [587, 28, 552, 4, "_jsxDevRuntime"], [587, 42, 552, 4], [587, 43, 552, 4, "jsxDEV"], [587, 49, 552, 4], [587, 51, 552, 5, "_View"], [587, 56, 552, 5], [587, 57, 552, 5, "default"], [587, 64, 552, 9], [588, 6, 552, 10, "style"], [588, 11, 552, 15], [588, 13, 552, 17, "styles"], [588, 19, 552, 23], [588, 20, 552, 24, "container"], [588, 29, 552, 34], [589, 6, 552, 34, "children"], [589, 14, 552, 34], [589, 30, 554, 6], [589, 34, 554, 6, "_jsxDevRuntime"], [589, 48, 554, 6], [589, 49, 554, 6, "jsxDEV"], [589, 55, 554, 6], [589, 57, 554, 7, "_View"], [589, 62, 554, 7], [589, 63, 554, 7, "default"], [589, 70, 554, 11], [590, 8, 554, 12, "style"], [590, 13, 554, 17], [590, 15, 554, 19, "styles"], [590, 21, 554, 25], [590, 22, 554, 26, "cameraContainer"], [590, 37, 554, 42], [591, 8, 554, 43, "id"], [591, 10, 554, 45], [591, 12, 554, 46], [591, 29, 554, 63], [592, 8, 554, 63, "children"], [592, 16, 554, 63], [592, 32, 555, 8], [592, 36, 555, 8, "_jsxDevRuntime"], [592, 50, 555, 8], [592, 51, 555, 8, "jsxDEV"], [592, 57, 555, 8], [592, 59, 555, 9, "_expoCamera"], [592, 70, 555, 9], [592, 71, 555, 9, "CameraView"], [592, 81, 555, 19], [593, 10, 556, 10, "ref"], [593, 13, 556, 13], [593, 15, 556, 15, "cameraRef"], [593, 24, 556, 25], [594, 10, 557, 10, "style"], [594, 15, 557, 15], [594, 17, 557, 17], [594, 18, 557, 18, "styles"], [594, 24, 557, 24], [594, 25, 557, 25, "camera"], [594, 31, 557, 31], [594, 33, 557, 33], [595, 12, 557, 35, "backgroundColor"], [595, 27, 557, 50], [595, 29, 557, 52], [596, 10, 557, 62], [596, 11, 557, 63], [596, 12, 557, 65], [597, 10, 558, 10, "facing"], [597, 16, 558, 16], [597, 18, 558, 17], [597, 24, 558, 23], [598, 10, 559, 10, "onLayout"], [598, 18, 559, 18], [598, 20, 559, 21, "e"], [598, 21, 559, 22], [598, 25, 559, 27], [599, 12, 560, 12, "console"], [599, 19, 560, 19], [599, 20, 560, 20, "log"], [599, 23, 560, 23], [599, 24, 560, 24], [599, 56, 560, 56], [599, 58, 560, 58, "e"], [599, 59, 560, 59], [599, 60, 560, 60, "nativeEvent"], [599, 71, 560, 71], [599, 72, 560, 72, "layout"], [599, 78, 560, 78], [599, 79, 560, 79], [600, 12, 561, 12, "setViewSize"], [600, 23, 561, 23], [600, 24, 561, 24], [601, 14, 561, 26, "width"], [601, 19, 561, 31], [601, 21, 561, 33, "e"], [601, 22, 561, 34], [601, 23, 561, 35, "nativeEvent"], [601, 34, 561, 46], [601, 35, 561, 47, "layout"], [601, 41, 561, 53], [601, 42, 561, 54, "width"], [601, 47, 561, 59], [602, 14, 561, 61, "height"], [602, 20, 561, 67], [602, 22, 561, 69, "e"], [602, 23, 561, 70], [602, 24, 561, 71, "nativeEvent"], [602, 35, 561, 82], [602, 36, 561, 83, "layout"], [602, 42, 561, 89], [602, 43, 561, 90, "height"], [603, 12, 561, 97], [603, 13, 561, 98], [603, 14, 561, 99], [604, 10, 562, 10], [604, 11, 562, 12], [605, 10, 563, 10, "onCameraReady"], [605, 23, 563, 23], [605, 25, 563, 25, "onCameraReady"], [605, 26, 563, 25], [605, 31, 563, 31], [606, 12, 564, 12, "console"], [606, 19, 564, 19], [606, 20, 564, 20, "log"], [606, 23, 564, 23], [606, 24, 564, 24], [606, 55, 564, 55], [606, 56, 564, 56], [607, 12, 565, 12, "setIsCameraReady"], [607, 28, 565, 28], [607, 29, 565, 29], [607, 33, 565, 33], [607, 34, 565, 34], [607, 35, 565, 35], [607, 36, 565, 36], [608, 10, 566, 10], [608, 11, 566, 12], [609, 10, 567, 10, "onMountError"], [609, 22, 567, 22], [609, 24, 567, 25, "error"], [609, 29, 567, 30], [609, 33, 567, 35], [610, 12, 568, 12, "console"], [610, 19, 568, 19], [610, 20, 568, 20, "error"], [610, 25, 568, 25], [610, 26, 568, 26], [610, 63, 568, 63], [610, 65, 568, 65, "error"], [610, 70, 568, 70], [610, 71, 568, 71], [611, 12, 569, 12, "setErrorMessage"], [611, 27, 569, 27], [611, 28, 569, 28], [611, 57, 569, 57], [611, 58, 569, 58], [612, 12, 570, 12, "setProcessingState"], [612, 30, 570, 30], [612, 31, 570, 31], [612, 38, 570, 38], [612, 39, 570, 39], [613, 10, 571, 10], [614, 8, 571, 12], [615, 10, 571, 12, "fileName"], [615, 18, 571, 12], [615, 20, 571, 12, "_jsxFileName"], [615, 32, 571, 12], [616, 10, 571, 12, "lineNumber"], [616, 20, 571, 12], [617, 10, 571, 12, "columnNumber"], [617, 22, 571, 12], [618, 8, 571, 12], [618, 15, 572, 9], [618, 16, 572, 10], [618, 18, 574, 9], [618, 19, 574, 10, "isCameraReady"], [618, 32, 574, 23], [618, 49, 575, 10], [618, 53, 575, 10, "_jsxDevRuntime"], [618, 67, 575, 10], [618, 68, 575, 10, "jsxDEV"], [618, 74, 575, 10], [618, 76, 575, 11, "_View"], [618, 81, 575, 11], [618, 82, 575, 11, "default"], [618, 89, 575, 15], [619, 10, 575, 16, "style"], [619, 15, 575, 21], [619, 17, 575, 23], [619, 18, 575, 24, "StyleSheet"], [619, 37, 575, 34], [619, 38, 575, 35, "absoluteFill"], [619, 50, 575, 47], [619, 52, 575, 49], [620, 12, 575, 51, "backgroundColor"], [620, 27, 575, 66], [620, 29, 575, 68], [620, 49, 575, 88], [621, 12, 575, 90, "justifyContent"], [621, 26, 575, 104], [621, 28, 575, 106], [621, 36, 575, 114], [622, 12, 575, 116, "alignItems"], [622, 22, 575, 126], [622, 24, 575, 128], [622, 32, 575, 136], [623, 12, 575, 138, "zIndex"], [623, 18, 575, 144], [623, 20, 575, 146], [624, 10, 575, 151], [624, 11, 575, 152], [624, 12, 575, 154], [625, 10, 575, 154, "children"], [625, 18, 575, 154], [625, 33, 576, 12], [625, 37, 576, 12, "_jsxDevRuntime"], [625, 51, 576, 12], [625, 52, 576, 12, "jsxDEV"], [625, 58, 576, 12], [625, 60, 576, 13, "_View"], [625, 65, 576, 13], [625, 66, 576, 13, "default"], [625, 73, 576, 17], [626, 12, 576, 18, "style"], [626, 17, 576, 23], [626, 19, 576, 25], [627, 14, 576, 27, "backgroundColor"], [627, 29, 576, 42], [627, 31, 576, 44], [627, 51, 576, 64], [628, 14, 576, 66, "padding"], [628, 21, 576, 73], [628, 23, 576, 75], [628, 25, 576, 77], [629, 14, 576, 79, "borderRadius"], [629, 26, 576, 91], [629, 28, 576, 93], [629, 30, 576, 95], [630, 14, 576, 97, "alignItems"], [630, 24, 576, 107], [630, 26, 576, 109], [631, 12, 576, 118], [631, 13, 576, 120], [632, 12, 576, 120, "children"], [632, 20, 576, 120], [632, 36, 577, 14], [632, 40, 577, 14, "_jsxDevRuntime"], [632, 54, 577, 14], [632, 55, 577, 14, "jsxDEV"], [632, 61, 577, 14], [632, 63, 577, 15, "_ActivityIndicator"], [632, 81, 577, 15], [632, 82, 577, 15, "default"], [632, 89, 577, 32], [633, 14, 577, 33, "size"], [633, 18, 577, 37], [633, 20, 577, 38], [633, 27, 577, 45], [634, 14, 577, 46, "color"], [634, 19, 577, 51], [634, 21, 577, 52], [634, 30, 577, 61], [635, 14, 577, 62, "style"], [635, 19, 577, 67], [635, 21, 577, 69], [636, 16, 577, 71, "marginBottom"], [636, 28, 577, 83], [636, 30, 577, 85], [637, 14, 577, 88], [638, 12, 577, 90], [639, 14, 577, 90, "fileName"], [639, 22, 577, 90], [639, 24, 577, 90, "_jsxFileName"], [639, 36, 577, 90], [640, 14, 577, 90, "lineNumber"], [640, 24, 577, 90], [641, 14, 577, 90, "columnNumber"], [641, 26, 577, 90], [642, 12, 577, 90], [642, 19, 577, 92], [642, 20, 577, 93], [642, 35, 578, 14], [642, 39, 578, 14, "_jsxDevRuntime"], [642, 53, 578, 14], [642, 54, 578, 14, "jsxDEV"], [642, 60, 578, 14], [642, 62, 578, 15, "_Text"], [642, 67, 578, 15], [642, 68, 578, 15, "default"], [642, 75, 578, 19], [643, 14, 578, 20, "style"], [643, 19, 578, 25], [643, 21, 578, 27], [644, 16, 578, 29, "color"], [644, 21, 578, 34], [644, 23, 578, 36], [644, 29, 578, 42], [645, 16, 578, 44, "fontSize"], [645, 24, 578, 52], [645, 26, 578, 54], [645, 28, 578, 56], [646, 16, 578, 58, "fontWeight"], [646, 26, 578, 68], [646, 28, 578, 70], [647, 14, 578, 76], [647, 15, 578, 78], [648, 14, 578, 78, "children"], [648, 22, 578, 78], [648, 24, 578, 79], [649, 12, 578, 101], [650, 14, 578, 101, "fileName"], [650, 22, 578, 101], [650, 24, 578, 101, "_jsxFileName"], [650, 36, 578, 101], [651, 14, 578, 101, "lineNumber"], [651, 24, 578, 101], [652, 14, 578, 101, "columnNumber"], [652, 26, 578, 101], [653, 12, 578, 101], [653, 19, 578, 107], [653, 20, 578, 108], [653, 35, 579, 14], [653, 39, 579, 14, "_jsxDevRuntime"], [653, 53, 579, 14], [653, 54, 579, 14, "jsxDEV"], [653, 60, 579, 14], [653, 62, 579, 15, "_Text"], [653, 67, 579, 15], [653, 68, 579, 15, "default"], [653, 75, 579, 19], [654, 14, 579, 20, "style"], [654, 19, 579, 25], [654, 21, 579, 27], [655, 16, 579, 29, "color"], [655, 21, 579, 34], [655, 23, 579, 36], [655, 32, 579, 45], [656, 16, 579, 47, "fontSize"], [656, 24, 579, 55], [656, 26, 579, 57], [656, 28, 579, 59], [657, 16, 579, 61, "marginTop"], [657, 25, 579, 70], [657, 27, 579, 72], [658, 14, 579, 74], [658, 15, 579, 76], [659, 14, 579, 76, "children"], [659, 22, 579, 76], [659, 24, 579, 77], [660, 12, 579, 88], [661, 14, 579, 88, "fileName"], [661, 22, 579, 88], [661, 24, 579, 88, "_jsxFileName"], [661, 36, 579, 88], [662, 14, 579, 88, "lineNumber"], [662, 24, 579, 88], [663, 14, 579, 88, "columnNumber"], [663, 26, 579, 88], [664, 12, 579, 88], [664, 19, 579, 94], [664, 20, 579, 95], [665, 10, 579, 95], [666, 12, 579, 95, "fileName"], [666, 20, 579, 95], [666, 22, 579, 95, "_jsxFileName"], [666, 34, 579, 95], [667, 12, 579, 95, "lineNumber"], [667, 22, 579, 95], [668, 12, 579, 95, "columnNumber"], [668, 24, 579, 95], [669, 10, 579, 95], [669, 17, 580, 18], [670, 8, 580, 19], [671, 10, 580, 19, "fileName"], [671, 18, 580, 19], [671, 20, 580, 19, "_jsxFileName"], [671, 32, 580, 19], [672, 10, 580, 19, "lineNumber"], [672, 20, 580, 19], [673, 10, 580, 19, "columnNumber"], [673, 22, 580, 19], [674, 8, 580, 19], [674, 15, 581, 16], [674, 16, 582, 9], [674, 18, 585, 9, "isCameraReady"], [674, 31, 585, 22], [674, 35, 585, 26, "previewBlurEnabled"], [674, 53, 585, 44], [674, 57, 585, 48, "viewSize"], [674, 65, 585, 56], [674, 66, 585, 57, "width"], [674, 71, 585, 62], [674, 74, 585, 65], [674, 75, 585, 66], [674, 92, 586, 10], [674, 96, 586, 10, "_jsxDevRuntime"], [674, 110, 586, 10], [674, 111, 586, 10, "jsxDEV"], [674, 117, 586, 10], [674, 119, 586, 10, "_jsxDevRuntime"], [674, 133, 586, 10], [674, 134, 586, 10, "Fragment"], [674, 142, 586, 10], [675, 10, 586, 10, "children"], [675, 18, 586, 10], [675, 34, 588, 12], [675, 38, 588, 12, "_jsxDevRuntime"], [675, 52, 588, 12], [675, 53, 588, 12, "jsxDEV"], [675, 59, 588, 12], [675, 61, 588, 13, "_LiveFaceCanvas"], [675, 76, 588, 13], [675, 77, 588, 13, "default"], [675, 84, 588, 27], [676, 12, 588, 28, "containerId"], [676, 23, 588, 39], [676, 25, 588, 40], [676, 42, 588, 57], [677, 12, 588, 58, "width"], [677, 17, 588, 63], [677, 19, 588, 65, "viewSize"], [677, 27, 588, 73], [677, 28, 588, 74, "width"], [677, 33, 588, 80], [678, 12, 588, 81, "height"], [678, 18, 588, 87], [678, 20, 588, 89, "viewSize"], [678, 28, 588, 97], [678, 29, 588, 98, "height"], [679, 10, 588, 105], [680, 12, 588, 105, "fileName"], [680, 20, 588, 105], [680, 22, 588, 105, "_jsxFileName"], [680, 34, 588, 105], [681, 12, 588, 105, "lineNumber"], [681, 22, 588, 105], [682, 12, 588, 105, "columnNumber"], [682, 24, 588, 105], [683, 10, 588, 105], [683, 17, 588, 107], [683, 18, 588, 108], [683, 33, 589, 12], [683, 37, 589, 12, "_jsxDevRuntime"], [683, 51, 589, 12], [683, 52, 589, 12, "jsxDEV"], [683, 58, 589, 12], [683, 60, 589, 13, "_View"], [683, 65, 589, 13], [683, 66, 589, 13, "default"], [683, 73, 589, 17], [684, 12, 589, 18, "style"], [684, 17, 589, 23], [684, 19, 589, 25], [684, 20, 589, 26, "StyleSheet"], [684, 39, 589, 36], [684, 40, 589, 37, "absoluteFill"], [684, 52, 589, 49], [684, 54, 589, 51], [685, 14, 589, 53, "pointerEvents"], [685, 27, 589, 66], [685, 29, 589, 68], [686, 12, 589, 75], [686, 13, 589, 76], [686, 14, 589, 78], [687, 12, 589, 78, "children"], [687, 20, 589, 78], [687, 36, 591, 12], [687, 40, 591, 12, "_jsxDevRuntime"], [687, 54, 591, 12], [687, 55, 591, 12, "jsxDEV"], [687, 61, 591, 12], [687, 63, 591, 13, "_expoBlur"], [687, 72, 591, 13], [687, 73, 591, 13, "BlurView"], [687, 81, 591, 21], [688, 14, 591, 22, "intensity"], [688, 23, 591, 31], [688, 25, 591, 33], [688, 27, 591, 36], [689, 14, 591, 37, "tint"], [689, 18, 591, 41], [689, 20, 591, 42], [689, 26, 591, 48], [690, 14, 591, 49, "style"], [690, 19, 591, 54], [690, 21, 591, 56], [690, 22, 591, 57, "styles"], [690, 28, 591, 63], [690, 29, 591, 64, "blurZone"], [690, 37, 591, 72], [690, 39, 591, 74], [691, 16, 592, 14, "left"], [691, 20, 592, 18], [691, 22, 592, 20], [691, 23, 592, 21], [692, 16, 593, 14, "top"], [692, 19, 593, 17], [692, 21, 593, 19, "viewSize"], [692, 29, 593, 27], [692, 30, 593, 28, "height"], [692, 36, 593, 34], [692, 39, 593, 37], [692, 42, 593, 40], [693, 16, 594, 14, "width"], [693, 21, 594, 19], [693, 23, 594, 21, "viewSize"], [693, 31, 594, 29], [693, 32, 594, 30, "width"], [693, 37, 594, 35], [694, 16, 595, 14, "height"], [694, 22, 595, 20], [694, 24, 595, 22, "viewSize"], [694, 32, 595, 30], [694, 33, 595, 31, "height"], [694, 39, 595, 37], [694, 42, 595, 40], [694, 46, 595, 44], [695, 16, 596, 14, "borderRadius"], [695, 28, 596, 26], [695, 30, 596, 28], [696, 14, 597, 12], [696, 15, 597, 13], [697, 12, 597, 15], [698, 14, 597, 15, "fileName"], [698, 22, 597, 15], [698, 24, 597, 15, "_jsxFileName"], [698, 36, 597, 15], [699, 14, 597, 15, "lineNumber"], [699, 24, 597, 15], [700, 14, 597, 15, "columnNumber"], [700, 26, 597, 15], [701, 12, 597, 15], [701, 19, 597, 17], [701, 20, 597, 18], [701, 35, 599, 12], [701, 39, 599, 12, "_jsxDevRuntime"], [701, 53, 599, 12], [701, 54, 599, 12, "jsxDEV"], [701, 60, 599, 12], [701, 62, 599, 13, "_expoBlur"], [701, 71, 599, 13], [701, 72, 599, 13, "BlurView"], [701, 80, 599, 21], [702, 14, 599, 22, "intensity"], [702, 23, 599, 31], [702, 25, 599, 33], [702, 27, 599, 36], [703, 14, 599, 37, "tint"], [703, 18, 599, 41], [703, 20, 599, 42], [703, 26, 599, 48], [704, 14, 599, 49, "style"], [704, 19, 599, 54], [704, 21, 599, 56], [704, 22, 599, 57, "styles"], [704, 28, 599, 63], [704, 29, 599, 64, "blurZone"], [704, 37, 599, 72], [704, 39, 599, 74], [705, 16, 600, 14, "left"], [705, 20, 600, 18], [705, 22, 600, 20], [705, 23, 600, 21], [706, 16, 601, 14, "top"], [706, 19, 601, 17], [706, 21, 601, 19], [706, 22, 601, 20], [707, 16, 602, 14, "width"], [707, 21, 602, 19], [707, 23, 602, 21, "viewSize"], [707, 31, 602, 29], [707, 32, 602, 30, "width"], [707, 37, 602, 35], [708, 16, 603, 14, "height"], [708, 22, 603, 20], [708, 24, 603, 22, "viewSize"], [708, 32, 603, 30], [708, 33, 603, 31, "height"], [708, 39, 603, 37], [708, 42, 603, 40], [708, 45, 603, 43], [709, 16, 604, 14, "borderRadius"], [709, 28, 604, 26], [709, 30, 604, 28], [710, 14, 605, 12], [710, 15, 605, 13], [711, 12, 605, 15], [712, 14, 605, 15, "fileName"], [712, 22, 605, 15], [712, 24, 605, 15, "_jsxFileName"], [712, 36, 605, 15], [713, 14, 605, 15, "lineNumber"], [713, 24, 605, 15], [714, 14, 605, 15, "columnNumber"], [714, 26, 605, 15], [715, 12, 605, 15], [715, 19, 605, 17], [715, 20, 605, 18], [715, 35, 607, 12], [715, 39, 607, 12, "_jsxDevRuntime"], [715, 53, 607, 12], [715, 54, 607, 12, "jsxDEV"], [715, 60, 607, 12], [715, 62, 607, 13, "_expoBlur"], [715, 71, 607, 13], [715, 72, 607, 13, "BlurView"], [715, 80, 607, 21], [716, 14, 607, 22, "intensity"], [716, 23, 607, 31], [716, 25, 607, 33], [716, 27, 607, 36], [717, 14, 607, 37, "tint"], [717, 18, 607, 41], [717, 20, 607, 42], [717, 26, 607, 48], [718, 14, 607, 49, "style"], [718, 19, 607, 54], [718, 21, 607, 56], [718, 22, 607, 57, "styles"], [718, 28, 607, 63], [718, 29, 607, 64, "blurZone"], [718, 37, 607, 72], [718, 39, 607, 74], [719, 16, 608, 14, "left"], [719, 20, 608, 18], [719, 22, 608, 20, "viewSize"], [719, 30, 608, 28], [719, 31, 608, 29, "width"], [719, 36, 608, 34], [719, 39, 608, 37], [719, 42, 608, 40], [719, 45, 608, 44, "viewSize"], [719, 53, 608, 52], [719, 54, 608, 53, "width"], [719, 59, 608, 58], [719, 62, 608, 61], [719, 66, 608, 66], [720, 16, 609, 14, "top"], [720, 19, 609, 17], [720, 21, 609, 19, "viewSize"], [720, 29, 609, 27], [720, 30, 609, 28, "height"], [720, 36, 609, 34], [720, 39, 609, 37], [720, 43, 609, 41], [720, 46, 609, 45, "viewSize"], [720, 54, 609, 53], [720, 55, 609, 54, "width"], [720, 60, 609, 59], [720, 63, 609, 62], [720, 67, 609, 67], [721, 16, 610, 14, "width"], [721, 21, 610, 19], [721, 23, 610, 21, "viewSize"], [721, 31, 610, 29], [721, 32, 610, 30, "width"], [721, 37, 610, 35], [721, 40, 610, 38], [721, 43, 610, 41], [722, 16, 611, 14, "height"], [722, 22, 611, 20], [722, 24, 611, 22, "viewSize"], [722, 32, 611, 30], [722, 33, 611, 31, "width"], [722, 38, 611, 36], [722, 41, 611, 39], [722, 44, 611, 42], [723, 16, 612, 14, "borderRadius"], [723, 28, 612, 26], [723, 30, 612, 29, "viewSize"], [723, 38, 612, 37], [723, 39, 612, 38, "width"], [723, 44, 612, 43], [723, 47, 612, 46], [723, 50, 612, 49], [723, 53, 612, 53], [724, 14, 613, 12], [724, 15, 613, 13], [725, 12, 613, 15], [726, 14, 613, 15, "fileName"], [726, 22, 613, 15], [726, 24, 613, 15, "_jsxFileName"], [726, 36, 613, 15], [727, 14, 613, 15, "lineNumber"], [727, 24, 613, 15], [728, 14, 613, 15, "columnNumber"], [728, 26, 613, 15], [729, 12, 613, 15], [729, 19, 613, 17], [729, 20, 613, 18], [729, 35, 614, 12], [729, 39, 614, 12, "_jsxDevRuntime"], [729, 53, 614, 12], [729, 54, 614, 12, "jsxDEV"], [729, 60, 614, 12], [729, 62, 614, 13, "_expoBlur"], [729, 71, 614, 13], [729, 72, 614, 13, "BlurView"], [729, 80, 614, 21], [730, 14, 614, 22, "intensity"], [730, 23, 614, 31], [730, 25, 614, 33], [730, 27, 614, 36], [731, 14, 614, 37, "tint"], [731, 18, 614, 41], [731, 20, 614, 42], [731, 26, 614, 48], [732, 14, 614, 49, "style"], [732, 19, 614, 54], [732, 21, 614, 56], [732, 22, 614, 57, "styles"], [732, 28, 614, 63], [732, 29, 614, 64, "blurZone"], [732, 37, 614, 72], [732, 39, 614, 74], [733, 16, 615, 14, "left"], [733, 20, 615, 18], [733, 22, 615, 20, "viewSize"], [733, 30, 615, 28], [733, 31, 615, 29, "width"], [733, 36, 615, 34], [733, 39, 615, 37], [733, 42, 615, 40], [733, 45, 615, 44, "viewSize"], [733, 53, 615, 52], [733, 54, 615, 53, "width"], [733, 59, 615, 58], [733, 62, 615, 61], [733, 66, 615, 66], [734, 16, 616, 14, "top"], [734, 19, 616, 17], [734, 21, 616, 19, "viewSize"], [734, 29, 616, 27], [734, 30, 616, 28, "height"], [734, 36, 616, 34], [734, 39, 616, 37], [734, 42, 616, 40], [734, 45, 616, 44, "viewSize"], [734, 53, 616, 52], [734, 54, 616, 53, "width"], [734, 59, 616, 58], [734, 62, 616, 61], [734, 66, 616, 66], [735, 16, 617, 14, "width"], [735, 21, 617, 19], [735, 23, 617, 21, "viewSize"], [735, 31, 617, 29], [735, 32, 617, 30, "width"], [735, 37, 617, 35], [735, 40, 617, 38], [735, 43, 617, 41], [736, 16, 618, 14, "height"], [736, 22, 618, 20], [736, 24, 618, 22, "viewSize"], [736, 32, 618, 30], [736, 33, 618, 31, "width"], [736, 38, 618, 36], [736, 41, 618, 39], [736, 44, 618, 42], [737, 16, 619, 14, "borderRadius"], [737, 28, 619, 26], [737, 30, 619, 29, "viewSize"], [737, 38, 619, 37], [737, 39, 619, 38, "width"], [737, 44, 619, 43], [737, 47, 619, 46], [737, 50, 619, 49], [737, 53, 619, 53], [738, 14, 620, 12], [738, 15, 620, 13], [739, 12, 620, 15], [740, 14, 620, 15, "fileName"], [740, 22, 620, 15], [740, 24, 620, 15, "_jsxFileName"], [740, 36, 620, 15], [741, 14, 620, 15, "lineNumber"], [741, 24, 620, 15], [742, 14, 620, 15, "columnNumber"], [742, 26, 620, 15], [743, 12, 620, 15], [743, 19, 620, 17], [743, 20, 620, 18], [743, 35, 621, 12], [743, 39, 621, 12, "_jsxDevRuntime"], [743, 53, 621, 12], [743, 54, 621, 12, "jsxDEV"], [743, 60, 621, 12], [743, 62, 621, 13, "_expoBlur"], [743, 71, 621, 13], [743, 72, 621, 13, "BlurView"], [743, 80, 621, 21], [744, 14, 621, 22, "intensity"], [744, 23, 621, 31], [744, 25, 621, 33], [744, 27, 621, 36], [745, 14, 621, 37, "tint"], [745, 18, 621, 41], [745, 20, 621, 42], [745, 26, 621, 48], [746, 14, 621, 49, "style"], [746, 19, 621, 54], [746, 21, 621, 56], [746, 22, 621, 57, "styles"], [746, 28, 621, 63], [746, 29, 621, 64, "blurZone"], [746, 37, 621, 72], [746, 39, 621, 74], [747, 16, 622, 14, "left"], [747, 20, 622, 18], [747, 22, 622, 20, "viewSize"], [747, 30, 622, 28], [747, 31, 622, 29, "width"], [747, 36, 622, 34], [747, 39, 622, 37], [747, 42, 622, 40], [747, 45, 622, 44, "viewSize"], [747, 53, 622, 52], [747, 54, 622, 53, "width"], [747, 59, 622, 58], [747, 62, 622, 61], [747, 66, 622, 66], [748, 16, 623, 14, "top"], [748, 19, 623, 17], [748, 21, 623, 19, "viewSize"], [748, 29, 623, 27], [748, 30, 623, 28, "height"], [748, 36, 623, 34], [748, 39, 623, 37], [748, 42, 623, 40], [748, 45, 623, 44, "viewSize"], [748, 53, 623, 52], [748, 54, 623, 53, "width"], [748, 59, 623, 58], [748, 62, 623, 61], [748, 66, 623, 66], [749, 16, 624, 14, "width"], [749, 21, 624, 19], [749, 23, 624, 21, "viewSize"], [749, 31, 624, 29], [749, 32, 624, 30, "width"], [749, 37, 624, 35], [749, 40, 624, 38], [749, 43, 624, 41], [750, 16, 625, 14, "height"], [750, 22, 625, 20], [750, 24, 625, 22, "viewSize"], [750, 32, 625, 30], [750, 33, 625, 31, "width"], [750, 38, 625, 36], [750, 41, 625, 39], [750, 44, 625, 42], [751, 16, 626, 14, "borderRadius"], [751, 28, 626, 26], [751, 30, 626, 29, "viewSize"], [751, 38, 626, 37], [751, 39, 626, 38, "width"], [751, 44, 626, 43], [751, 47, 626, 46], [751, 50, 626, 49], [751, 53, 626, 53], [752, 14, 627, 12], [752, 15, 627, 13], [753, 12, 627, 15], [754, 14, 627, 15, "fileName"], [754, 22, 627, 15], [754, 24, 627, 15, "_jsxFileName"], [754, 36, 627, 15], [755, 14, 627, 15, "lineNumber"], [755, 24, 627, 15], [756, 14, 627, 15, "columnNumber"], [756, 26, 627, 15], [757, 12, 627, 15], [757, 19, 627, 17], [757, 20, 627, 18], [757, 22, 629, 13, "__DEV__"], [757, 29, 629, 20], [757, 46, 630, 14], [757, 50, 630, 14, "_jsxDevRuntime"], [757, 64, 630, 14], [757, 65, 630, 14, "jsxDEV"], [757, 71, 630, 14], [757, 73, 630, 15, "_View"], [757, 78, 630, 15], [757, 79, 630, 15, "default"], [757, 86, 630, 19], [758, 14, 630, 20, "style"], [758, 19, 630, 25], [758, 21, 630, 27, "styles"], [758, 27, 630, 33], [758, 28, 630, 34, "previewChip"], [758, 39, 630, 46], [759, 14, 630, 46, "children"], [759, 22, 630, 46], [759, 37, 631, 16], [759, 41, 631, 16, "_jsxDevRuntime"], [759, 55, 631, 16], [759, 56, 631, 16, "jsxDEV"], [759, 62, 631, 16], [759, 64, 631, 17, "_Text"], [759, 69, 631, 17], [759, 70, 631, 17, "default"], [759, 77, 631, 21], [760, 16, 631, 22, "style"], [760, 21, 631, 27], [760, 23, 631, 29, "styles"], [760, 29, 631, 35], [760, 30, 631, 36, "previewChipText"], [760, 45, 631, 52], [761, 16, 631, 52, "children"], [761, 24, 631, 52], [761, 26, 631, 53], [762, 14, 631, 73], [763, 16, 631, 73, "fileName"], [763, 24, 631, 73], [763, 26, 631, 73, "_jsxFileName"], [763, 38, 631, 73], [764, 16, 631, 73, "lineNumber"], [764, 26, 631, 73], [765, 16, 631, 73, "columnNumber"], [765, 28, 631, 73], [766, 14, 631, 73], [766, 21, 631, 79], [767, 12, 631, 80], [768, 14, 631, 80, "fileName"], [768, 22, 631, 80], [768, 24, 631, 80, "_jsxFileName"], [768, 36, 631, 80], [769, 14, 631, 80, "lineNumber"], [769, 24, 631, 80], [770, 14, 631, 80, "columnNumber"], [770, 26, 631, 80], [771, 12, 631, 80], [771, 19, 632, 20], [771, 20, 633, 13], [772, 10, 633, 13], [773, 12, 633, 13, "fileName"], [773, 20, 633, 13], [773, 22, 633, 13, "_jsxFileName"], [773, 34, 633, 13], [774, 12, 633, 13, "lineNumber"], [774, 22, 633, 13], [775, 12, 633, 13, "columnNumber"], [775, 24, 633, 13], [776, 10, 633, 13], [776, 17, 634, 18], [776, 18, 634, 19], [777, 8, 634, 19], [777, 23, 635, 12], [777, 24, 636, 9], [777, 26, 638, 9, "isCameraReady"], [777, 39, 638, 22], [777, 56, 639, 10], [777, 60, 639, 10, "_jsxDevRuntime"], [777, 74, 639, 10], [777, 75, 639, 10, "jsxDEV"], [777, 81, 639, 10], [777, 83, 639, 10, "_jsxDevRuntime"], [777, 97, 639, 10], [777, 98, 639, 10, "Fragment"], [777, 106, 639, 10], [778, 10, 639, 10, "children"], [778, 18, 639, 10], [778, 34, 641, 12], [778, 38, 641, 12, "_jsxDevRuntime"], [778, 52, 641, 12], [778, 53, 641, 12, "jsxDEV"], [778, 59, 641, 12], [778, 61, 641, 13, "_View"], [778, 66, 641, 13], [778, 67, 641, 13, "default"], [778, 74, 641, 17], [779, 12, 641, 18, "style"], [779, 17, 641, 23], [779, 19, 641, 25, "styles"], [779, 25, 641, 31], [779, 26, 641, 32, "headerOverlay"], [779, 39, 641, 46], [780, 12, 641, 46, "children"], [780, 20, 641, 46], [780, 35, 642, 14], [780, 39, 642, 14, "_jsxDevRuntime"], [780, 53, 642, 14], [780, 54, 642, 14, "jsxDEV"], [780, 60, 642, 14], [780, 62, 642, 15, "_View"], [780, 67, 642, 15], [780, 68, 642, 15, "default"], [780, 75, 642, 19], [781, 14, 642, 20, "style"], [781, 19, 642, 25], [781, 21, 642, 27, "styles"], [781, 27, 642, 33], [781, 28, 642, 34, "headerContent"], [781, 41, 642, 48], [782, 14, 642, 48, "children"], [782, 22, 642, 48], [782, 38, 643, 16], [782, 42, 643, 16, "_jsxDevRuntime"], [782, 56, 643, 16], [782, 57, 643, 16, "jsxDEV"], [782, 63, 643, 16], [782, 65, 643, 17, "_View"], [782, 70, 643, 17], [782, 71, 643, 17, "default"], [782, 78, 643, 21], [783, 16, 643, 22, "style"], [783, 21, 643, 27], [783, 23, 643, 29, "styles"], [783, 29, 643, 35], [783, 30, 643, 36, "headerLeft"], [783, 40, 643, 47], [784, 16, 643, 47, "children"], [784, 24, 643, 47], [784, 40, 644, 18], [784, 44, 644, 18, "_jsxDevRuntime"], [784, 58, 644, 18], [784, 59, 644, 18, "jsxDEV"], [784, 65, 644, 18], [784, 67, 644, 19, "_Text"], [784, 72, 644, 19], [784, 73, 644, 19, "default"], [784, 80, 644, 23], [785, 18, 644, 24, "style"], [785, 23, 644, 29], [785, 25, 644, 31, "styles"], [785, 31, 644, 37], [785, 32, 644, 38, "headerTitle"], [785, 43, 644, 50], [786, 18, 644, 50, "children"], [786, 26, 644, 50], [786, 28, 644, 51], [787, 16, 644, 62], [788, 18, 644, 62, "fileName"], [788, 26, 644, 62], [788, 28, 644, 62, "_jsxFileName"], [788, 40, 644, 62], [789, 18, 644, 62, "lineNumber"], [789, 28, 644, 62], [790, 18, 644, 62, "columnNumber"], [790, 30, 644, 62], [791, 16, 644, 62], [791, 23, 644, 68], [791, 24, 644, 69], [791, 39, 645, 18], [791, 43, 645, 18, "_jsxDevRuntime"], [791, 57, 645, 18], [791, 58, 645, 18, "jsxDEV"], [791, 64, 645, 18], [791, 66, 645, 19, "_View"], [791, 71, 645, 19], [791, 72, 645, 19, "default"], [791, 79, 645, 23], [792, 18, 645, 24, "style"], [792, 23, 645, 29], [792, 25, 645, 31, "styles"], [792, 31, 645, 37], [792, 32, 645, 38, "subtitleRow"], [792, 43, 645, 50], [793, 18, 645, 50, "children"], [793, 26, 645, 50], [793, 42, 646, 20], [793, 46, 646, 20, "_jsxDevRuntime"], [793, 60, 646, 20], [793, 61, 646, 20, "jsxDEV"], [793, 67, 646, 20], [793, 69, 646, 21, "_Text"], [793, 74, 646, 21], [793, 75, 646, 21, "default"], [793, 82, 646, 25], [794, 20, 646, 26, "style"], [794, 25, 646, 31], [794, 27, 646, 33, "styles"], [794, 33, 646, 39], [794, 34, 646, 40, "webIcon"], [794, 41, 646, 48], [795, 20, 646, 48, "children"], [795, 28, 646, 48], [795, 30, 646, 49], [796, 18, 646, 51], [797, 20, 646, 51, "fileName"], [797, 28, 646, 51], [797, 30, 646, 51, "_jsxFileName"], [797, 42, 646, 51], [798, 20, 646, 51, "lineNumber"], [798, 30, 646, 51], [799, 20, 646, 51, "columnNumber"], [799, 32, 646, 51], [800, 18, 646, 51], [800, 25, 646, 57], [800, 26, 646, 58], [800, 41, 647, 20], [800, 45, 647, 20, "_jsxDevRuntime"], [800, 59, 647, 20], [800, 60, 647, 20, "jsxDEV"], [800, 66, 647, 20], [800, 68, 647, 21, "_Text"], [800, 73, 647, 21], [800, 74, 647, 21, "default"], [800, 81, 647, 25], [801, 20, 647, 26, "style"], [801, 25, 647, 31], [801, 27, 647, 33, "styles"], [801, 33, 647, 39], [801, 34, 647, 40, "headerSubtitle"], [801, 48, 647, 55], [802, 20, 647, 55, "children"], [802, 28, 647, 55], [802, 30, 647, 56], [803, 18, 647, 71], [804, 20, 647, 71, "fileName"], [804, 28, 647, 71], [804, 30, 647, 71, "_jsxFileName"], [804, 42, 647, 71], [805, 20, 647, 71, "lineNumber"], [805, 30, 647, 71], [806, 20, 647, 71, "columnNumber"], [806, 32, 647, 71], [807, 18, 647, 71], [807, 25, 647, 77], [807, 26, 647, 78], [808, 16, 647, 78], [809, 18, 647, 78, "fileName"], [809, 26, 647, 78], [809, 28, 647, 78, "_jsxFileName"], [809, 40, 647, 78], [810, 18, 647, 78, "lineNumber"], [810, 28, 647, 78], [811, 18, 647, 78, "columnNumber"], [811, 30, 647, 78], [812, 16, 647, 78], [812, 23, 648, 24], [812, 24, 648, 25], [812, 26, 649, 19, "challengeCode"], [812, 39, 649, 32], [812, 56, 650, 20], [812, 60, 650, 20, "_jsxDevRuntime"], [812, 74, 650, 20], [812, 75, 650, 20, "jsxDEV"], [812, 81, 650, 20], [812, 83, 650, 21, "_View"], [812, 88, 650, 21], [812, 89, 650, 21, "default"], [812, 96, 650, 25], [813, 18, 650, 26, "style"], [813, 23, 650, 31], [813, 25, 650, 33, "styles"], [813, 31, 650, 39], [813, 32, 650, 40, "challengeRow"], [813, 44, 650, 53], [814, 18, 650, 53, "children"], [814, 26, 650, 53], [814, 42, 651, 22], [814, 46, 651, 22, "_jsxDevRuntime"], [814, 60, 651, 22], [814, 61, 651, 22, "jsxDEV"], [814, 67, 651, 22], [814, 69, 651, 23, "_lucideReactNative"], [814, 87, 651, 23], [814, 88, 651, 23, "Shield"], [814, 94, 651, 29], [815, 20, 651, 30, "size"], [815, 24, 651, 34], [815, 26, 651, 36], [815, 28, 651, 39], [816, 20, 651, 40, "color"], [816, 25, 651, 45], [816, 27, 651, 46], [817, 18, 651, 52], [818, 20, 651, 52, "fileName"], [818, 28, 651, 52], [818, 30, 651, 52, "_jsxFileName"], [818, 42, 651, 52], [819, 20, 651, 52, "lineNumber"], [819, 30, 651, 52], [820, 20, 651, 52, "columnNumber"], [820, 32, 651, 52], [821, 18, 651, 52], [821, 25, 651, 54], [821, 26, 651, 55], [821, 41, 652, 22], [821, 45, 652, 22, "_jsxDevRuntime"], [821, 59, 652, 22], [821, 60, 652, 22, "jsxDEV"], [821, 66, 652, 22], [821, 68, 652, 23, "_Text"], [821, 73, 652, 23], [821, 74, 652, 23, "default"], [821, 81, 652, 27], [822, 20, 652, 28, "style"], [822, 25, 652, 33], [822, 27, 652, 35, "styles"], [822, 33, 652, 41], [822, 34, 652, 42, "challengeCode"], [822, 47, 652, 56], [823, 20, 652, 56, "children"], [823, 28, 652, 56], [823, 30, 652, 58, "challengeCode"], [824, 18, 652, 71], [825, 20, 652, 71, "fileName"], [825, 28, 652, 71], [825, 30, 652, 71, "_jsxFileName"], [825, 42, 652, 71], [826, 20, 652, 71, "lineNumber"], [826, 30, 652, 71], [827, 20, 652, 71, "columnNumber"], [827, 32, 652, 71], [828, 18, 652, 71], [828, 25, 652, 78], [828, 26, 652, 79], [829, 16, 652, 79], [830, 18, 652, 79, "fileName"], [830, 26, 652, 79], [830, 28, 652, 79, "_jsxFileName"], [830, 40, 652, 79], [831, 18, 652, 79, "lineNumber"], [831, 28, 652, 79], [832, 18, 652, 79, "columnNumber"], [832, 30, 652, 79], [833, 16, 652, 79], [833, 23, 653, 26], [833, 24, 654, 19], [834, 14, 654, 19], [835, 16, 654, 19, "fileName"], [835, 24, 654, 19], [835, 26, 654, 19, "_jsxFileName"], [835, 38, 654, 19], [836, 16, 654, 19, "lineNumber"], [836, 26, 654, 19], [837, 16, 654, 19, "columnNumber"], [837, 28, 654, 19], [838, 14, 654, 19], [838, 21, 655, 22], [838, 22, 655, 23], [838, 37, 656, 16], [838, 41, 656, 16, "_jsxDevRuntime"], [838, 55, 656, 16], [838, 56, 656, 16, "jsxDEV"], [838, 62, 656, 16], [838, 64, 656, 17, "_TouchableOpacity"], [838, 81, 656, 17], [838, 82, 656, 17, "default"], [838, 89, 656, 33], [839, 16, 656, 34, "onPress"], [839, 23, 656, 41], [839, 25, 656, 43, "onCancel"], [839, 33, 656, 52], [840, 16, 656, 53, "style"], [840, 21, 656, 58], [840, 23, 656, 60, "styles"], [840, 29, 656, 66], [840, 30, 656, 67, "closeButton"], [840, 41, 656, 79], [841, 16, 656, 79, "children"], [841, 24, 656, 79], [841, 39, 657, 18], [841, 43, 657, 18, "_jsxDevRuntime"], [841, 57, 657, 18], [841, 58, 657, 18, "jsxDEV"], [841, 64, 657, 18], [841, 66, 657, 19, "_lucideReactNative"], [841, 84, 657, 19], [841, 85, 657, 19, "X"], [841, 86, 657, 20], [842, 18, 657, 21, "size"], [842, 22, 657, 25], [842, 24, 657, 27], [842, 26, 657, 30], [843, 18, 657, 31, "color"], [843, 23, 657, 36], [843, 25, 657, 37], [844, 16, 657, 43], [845, 18, 657, 43, "fileName"], [845, 26, 657, 43], [845, 28, 657, 43, "_jsxFileName"], [845, 40, 657, 43], [846, 18, 657, 43, "lineNumber"], [846, 28, 657, 43], [847, 18, 657, 43, "columnNumber"], [847, 30, 657, 43], [848, 16, 657, 43], [848, 23, 657, 45], [849, 14, 657, 46], [850, 16, 657, 46, "fileName"], [850, 24, 657, 46], [850, 26, 657, 46, "_jsxFileName"], [850, 38, 657, 46], [851, 16, 657, 46, "lineNumber"], [851, 26, 657, 46], [852, 16, 657, 46, "columnNumber"], [852, 28, 657, 46], [853, 14, 657, 46], [853, 21, 658, 34], [853, 22, 658, 35], [854, 12, 658, 35], [855, 14, 658, 35, "fileName"], [855, 22, 658, 35], [855, 24, 658, 35, "_jsxFileName"], [855, 36, 658, 35], [856, 14, 658, 35, "lineNumber"], [856, 24, 658, 35], [857, 14, 658, 35, "columnNumber"], [857, 26, 658, 35], [858, 12, 658, 35], [858, 19, 659, 20], [859, 10, 659, 21], [860, 12, 659, 21, "fileName"], [860, 20, 659, 21], [860, 22, 659, 21, "_jsxFileName"], [860, 34, 659, 21], [861, 12, 659, 21, "lineNumber"], [861, 22, 659, 21], [862, 12, 659, 21, "columnNumber"], [862, 24, 659, 21], [863, 10, 659, 21], [863, 17, 660, 18], [863, 18, 660, 19], [863, 33, 662, 12], [863, 37, 662, 12, "_jsxDevRuntime"], [863, 51, 662, 12], [863, 52, 662, 12, "jsxDEV"], [863, 58, 662, 12], [863, 60, 662, 13, "_View"], [863, 65, 662, 13], [863, 66, 662, 13, "default"], [863, 73, 662, 17], [864, 12, 662, 18, "style"], [864, 17, 662, 23], [864, 19, 662, 25, "styles"], [864, 25, 662, 31], [864, 26, 662, 32, "privacyNotice"], [864, 39, 662, 46], [865, 12, 662, 46, "children"], [865, 20, 662, 46], [865, 36, 663, 14], [865, 40, 663, 14, "_jsxDevRuntime"], [865, 54, 663, 14], [865, 55, 663, 14, "jsxDEV"], [865, 61, 663, 14], [865, 63, 663, 15, "_lucideReactNative"], [865, 81, 663, 15], [865, 82, 663, 15, "Shield"], [865, 88, 663, 21], [866, 14, 663, 22, "size"], [866, 18, 663, 26], [866, 20, 663, 28], [866, 22, 663, 31], [867, 14, 663, 32, "color"], [867, 19, 663, 37], [867, 21, 663, 38], [868, 12, 663, 47], [869, 14, 663, 47, "fileName"], [869, 22, 663, 47], [869, 24, 663, 47, "_jsxFileName"], [869, 36, 663, 47], [870, 14, 663, 47, "lineNumber"], [870, 24, 663, 47], [871, 14, 663, 47, "columnNumber"], [871, 26, 663, 47], [872, 12, 663, 47], [872, 19, 663, 49], [872, 20, 663, 50], [872, 35, 664, 14], [872, 39, 664, 14, "_jsxDevRuntime"], [872, 53, 664, 14], [872, 54, 664, 14, "jsxDEV"], [872, 60, 664, 14], [872, 62, 664, 15, "_Text"], [872, 67, 664, 15], [872, 68, 664, 15, "default"], [872, 75, 664, 19], [873, 14, 664, 20, "style"], [873, 19, 664, 25], [873, 21, 664, 27, "styles"], [873, 27, 664, 33], [873, 28, 664, 34, "privacyText"], [873, 39, 664, 46], [874, 14, 664, 46, "children"], [874, 22, 664, 46], [874, 24, 664, 47], [875, 12, 666, 14], [876, 14, 666, 14, "fileName"], [876, 22, 666, 14], [876, 24, 666, 14, "_jsxFileName"], [876, 36, 666, 14], [877, 14, 666, 14, "lineNumber"], [877, 24, 666, 14], [878, 14, 666, 14, "columnNumber"], [878, 26, 666, 14], [879, 12, 666, 14], [879, 19, 666, 20], [879, 20, 666, 21], [880, 10, 666, 21], [881, 12, 666, 21, "fileName"], [881, 20, 666, 21], [881, 22, 666, 21, "_jsxFileName"], [881, 34, 666, 21], [882, 12, 666, 21, "lineNumber"], [882, 22, 666, 21], [883, 12, 666, 21, "columnNumber"], [883, 24, 666, 21], [884, 10, 666, 21], [884, 17, 667, 18], [884, 18, 667, 19], [884, 33, 669, 12], [884, 37, 669, 12, "_jsxDevRuntime"], [884, 51, 669, 12], [884, 52, 669, 12, "jsxDEV"], [884, 58, 669, 12], [884, 60, 669, 13, "_View"], [884, 65, 669, 13], [884, 66, 669, 13, "default"], [884, 73, 669, 17], [885, 12, 669, 18, "style"], [885, 17, 669, 23], [885, 19, 669, 25, "styles"], [885, 25, 669, 31], [885, 26, 669, 32, "footer<PERSON><PERSON><PERSON>"], [885, 39, 669, 46], [886, 12, 669, 46, "children"], [886, 20, 669, 46], [886, 36, 670, 14], [886, 40, 670, 14, "_jsxDevRuntime"], [886, 54, 670, 14], [886, 55, 670, 14, "jsxDEV"], [886, 61, 670, 14], [886, 63, 670, 15, "_Text"], [886, 68, 670, 15], [886, 69, 670, 15, "default"], [886, 76, 670, 19], [887, 14, 670, 20, "style"], [887, 19, 670, 25], [887, 21, 670, 27, "styles"], [887, 27, 670, 33], [887, 28, 670, 34, "instruction"], [887, 39, 670, 46], [888, 14, 670, 46, "children"], [888, 22, 670, 46], [888, 24, 670, 47], [889, 12, 672, 14], [890, 14, 672, 14, "fileName"], [890, 22, 672, 14], [890, 24, 672, 14, "_jsxFileName"], [890, 36, 672, 14], [891, 14, 672, 14, "lineNumber"], [891, 24, 672, 14], [892, 14, 672, 14, "columnNumber"], [892, 26, 672, 14], [893, 12, 672, 14], [893, 19, 672, 20], [893, 20, 672, 21], [893, 35, 674, 14], [893, 39, 674, 14, "_jsxDevRuntime"], [893, 53, 674, 14], [893, 54, 674, 14, "jsxDEV"], [893, 60, 674, 14], [893, 62, 674, 15, "_TouchableOpacity"], [893, 79, 674, 15], [893, 80, 674, 15, "default"], [893, 87, 674, 31], [894, 14, 675, 16, "onPress"], [894, 21, 675, 23], [894, 23, 675, 25, "capturePhoto"], [894, 35, 675, 38], [895, 14, 676, 16, "disabled"], [895, 22, 676, 24], [895, 24, 676, 26, "processingState"], [895, 39, 676, 41], [895, 44, 676, 46], [895, 50, 676, 52], [895, 54, 676, 56], [895, 55, 676, 57, "isCameraReady"], [895, 68, 676, 71], [896, 14, 677, 16, "style"], [896, 19, 677, 21], [896, 21, 677, 23], [896, 22, 678, 18, "styles"], [896, 28, 678, 24], [896, 29, 678, 25, "shutterButton"], [896, 42, 678, 38], [896, 44, 679, 18, "processingState"], [896, 59, 679, 33], [896, 64, 679, 38], [896, 70, 679, 44], [896, 74, 679, 48, "styles"], [896, 80, 679, 54], [896, 81, 679, 55, "shutterButtonDisabled"], [896, 102, 679, 76], [896, 103, 680, 18], [897, 14, 680, 18, "children"], [897, 22, 680, 18], [897, 24, 682, 17, "processingState"], [897, 39, 682, 32], [897, 44, 682, 37], [897, 50, 682, 43], [897, 66, 683, 18], [897, 70, 683, 18, "_jsxDevRuntime"], [897, 84, 683, 18], [897, 85, 683, 18, "jsxDEV"], [897, 91, 683, 18], [897, 93, 683, 19, "_View"], [897, 98, 683, 19], [897, 99, 683, 19, "default"], [897, 106, 683, 23], [898, 16, 683, 24, "style"], [898, 21, 683, 29], [898, 23, 683, 31, "styles"], [898, 29, 683, 37], [898, 30, 683, 38, "shutterInner"], [899, 14, 683, 51], [900, 16, 683, 51, "fileName"], [900, 24, 683, 51], [900, 26, 683, 51, "_jsxFileName"], [900, 38, 683, 51], [901, 16, 683, 51, "lineNumber"], [901, 26, 683, 51], [902, 16, 683, 51, "columnNumber"], [902, 28, 683, 51], [903, 14, 683, 51], [903, 21, 683, 53], [903, 22, 683, 54], [903, 38, 685, 18], [903, 42, 685, 18, "_jsxDevRuntime"], [903, 56, 685, 18], [903, 57, 685, 18, "jsxDEV"], [903, 63, 685, 18], [903, 65, 685, 19, "_ActivityIndicator"], [903, 83, 685, 19], [903, 84, 685, 19, "default"], [903, 91, 685, 36], [904, 16, 685, 37, "size"], [904, 20, 685, 41], [904, 22, 685, 42], [904, 29, 685, 49], [905, 16, 685, 50, "color"], [905, 21, 685, 55], [905, 23, 685, 56], [906, 14, 685, 65], [907, 16, 685, 65, "fileName"], [907, 24, 685, 65], [907, 26, 685, 65, "_jsxFileName"], [907, 38, 685, 65], [908, 16, 685, 65, "lineNumber"], [908, 26, 685, 65], [909, 16, 685, 65, "columnNumber"], [909, 28, 685, 65], [910, 14, 685, 65], [910, 21, 685, 67], [911, 12, 686, 17], [912, 14, 686, 17, "fileName"], [912, 22, 686, 17], [912, 24, 686, 17, "_jsxFileName"], [912, 36, 686, 17], [913, 14, 686, 17, "lineNumber"], [913, 24, 686, 17], [914, 14, 686, 17, "columnNumber"], [914, 26, 686, 17], [915, 12, 686, 17], [915, 19, 687, 32], [915, 20, 687, 33], [915, 35, 688, 14], [915, 39, 688, 14, "_jsxDevRuntime"], [915, 53, 688, 14], [915, 54, 688, 14, "jsxDEV"], [915, 60, 688, 14], [915, 62, 688, 15, "_Text"], [915, 67, 688, 15], [915, 68, 688, 15, "default"], [915, 75, 688, 19], [916, 14, 688, 20, "style"], [916, 19, 688, 25], [916, 21, 688, 27, "styles"], [916, 27, 688, 33], [916, 28, 688, 34, "privacyNote"], [916, 39, 688, 46], [917, 14, 688, 46, "children"], [917, 22, 688, 46], [917, 24, 688, 47], [918, 12, 690, 14], [919, 14, 690, 14, "fileName"], [919, 22, 690, 14], [919, 24, 690, 14, "_jsxFileName"], [919, 36, 690, 14], [920, 14, 690, 14, "lineNumber"], [920, 24, 690, 14], [921, 14, 690, 14, "columnNumber"], [921, 26, 690, 14], [922, 12, 690, 14], [922, 19, 690, 20], [922, 20, 690, 21], [923, 10, 690, 21], [924, 12, 690, 21, "fileName"], [924, 20, 690, 21], [924, 22, 690, 21, "_jsxFileName"], [924, 34, 690, 21], [925, 12, 690, 21, "lineNumber"], [925, 22, 690, 21], [926, 12, 690, 21, "columnNumber"], [926, 24, 690, 21], [927, 10, 690, 21], [927, 17, 691, 18], [927, 18, 691, 19], [928, 8, 691, 19], [928, 23, 692, 12], [928, 24, 693, 9], [929, 6, 693, 9], [930, 8, 693, 9, "fileName"], [930, 16, 693, 9], [930, 18, 693, 9, "_jsxFileName"], [930, 30, 693, 9], [931, 8, 693, 9, "lineNumber"], [931, 18, 693, 9], [932, 8, 693, 9, "columnNumber"], [932, 20, 693, 9], [933, 6, 693, 9], [933, 13, 694, 12], [933, 14, 694, 13], [933, 29, 696, 6], [933, 33, 696, 6, "_jsxDevRuntime"], [933, 47, 696, 6], [933, 48, 696, 6, "jsxDEV"], [933, 54, 696, 6], [933, 56, 696, 7, "_Modal"], [933, 62, 696, 7], [933, 63, 696, 7, "default"], [933, 70, 696, 12], [934, 8, 697, 8, "visible"], [934, 15, 697, 15], [934, 17, 697, 17, "processingState"], [934, 32, 697, 32], [934, 37, 697, 37], [934, 43, 697, 43], [934, 47, 697, 47, "processingState"], [934, 62, 697, 62], [934, 67, 697, 67], [934, 74, 697, 75], [935, 8, 698, 8, "transparent"], [935, 19, 698, 19], [936, 8, 699, 8, "animationType"], [936, 21, 699, 21], [936, 23, 699, 22], [936, 29, 699, 28], [937, 8, 699, 28, "children"], [937, 16, 699, 28], [937, 31, 701, 8], [937, 35, 701, 8, "_jsxDevRuntime"], [937, 49, 701, 8], [937, 50, 701, 8, "jsxDEV"], [937, 56, 701, 8], [937, 58, 701, 9, "_View"], [937, 63, 701, 9], [937, 64, 701, 9, "default"], [937, 71, 701, 13], [938, 10, 701, 14, "style"], [938, 15, 701, 19], [938, 17, 701, 21, "styles"], [938, 23, 701, 27], [938, 24, 701, 28, "processingModal"], [938, 39, 701, 44], [939, 10, 701, 44, "children"], [939, 18, 701, 44], [939, 33, 702, 10], [939, 37, 702, 10, "_jsxDevRuntime"], [939, 51, 702, 10], [939, 52, 702, 10, "jsxDEV"], [939, 58, 702, 10], [939, 60, 702, 11, "_View"], [939, 65, 702, 11], [939, 66, 702, 11, "default"], [939, 73, 702, 15], [940, 12, 702, 16, "style"], [940, 17, 702, 21], [940, 19, 702, 23, "styles"], [940, 25, 702, 29], [940, 26, 702, 30, "processingContent"], [940, 43, 702, 48], [941, 12, 702, 48, "children"], [941, 20, 702, 48], [941, 36, 703, 12], [941, 40, 703, 12, "_jsxDevRuntime"], [941, 54, 703, 12], [941, 55, 703, 12, "jsxDEV"], [941, 61, 703, 12], [941, 63, 703, 13, "_ActivityIndicator"], [941, 81, 703, 13], [941, 82, 703, 13, "default"], [941, 89, 703, 30], [942, 14, 703, 31, "size"], [942, 18, 703, 35], [942, 20, 703, 36], [942, 27, 703, 43], [943, 14, 703, 44, "color"], [943, 19, 703, 49], [943, 21, 703, 50], [944, 12, 703, 59], [945, 14, 703, 59, "fileName"], [945, 22, 703, 59], [945, 24, 703, 59, "_jsxFileName"], [945, 36, 703, 59], [946, 14, 703, 59, "lineNumber"], [946, 24, 703, 59], [947, 14, 703, 59, "columnNumber"], [947, 26, 703, 59], [948, 12, 703, 59], [948, 19, 703, 61], [948, 20, 703, 62], [948, 35, 705, 12], [948, 39, 705, 12, "_jsxDevRuntime"], [948, 53, 705, 12], [948, 54, 705, 12, "jsxDEV"], [948, 60, 705, 12], [948, 62, 705, 13, "_Text"], [948, 67, 705, 13], [948, 68, 705, 13, "default"], [948, 75, 705, 17], [949, 14, 705, 18, "style"], [949, 19, 705, 23], [949, 21, 705, 25, "styles"], [949, 27, 705, 31], [949, 28, 705, 32, "processingTitle"], [949, 43, 705, 48], [950, 14, 705, 48, "children"], [950, 22, 705, 48], [950, 25, 706, 15, "processingState"], [950, 40, 706, 30], [950, 45, 706, 35], [950, 56, 706, 46], [950, 60, 706, 50], [950, 80, 706, 70], [950, 82, 707, 15, "processingState"], [950, 97, 707, 30], [950, 102, 707, 35], [950, 113, 707, 46], [950, 117, 707, 50], [950, 146, 707, 79], [950, 148, 708, 15, "processingState"], [950, 163, 708, 30], [950, 168, 708, 35], [950, 180, 708, 47], [950, 184, 708, 51], [950, 216, 708, 83], [950, 218, 709, 15, "processingState"], [950, 233, 709, 30], [950, 238, 709, 35], [950, 249, 709, 46], [950, 253, 709, 50], [950, 275, 709, 72], [951, 12, 709, 72], [952, 14, 709, 72, "fileName"], [952, 22, 709, 72], [952, 24, 709, 72, "_jsxFileName"], [952, 36, 709, 72], [953, 14, 709, 72, "lineNumber"], [953, 24, 709, 72], [954, 14, 709, 72, "columnNumber"], [954, 26, 709, 72], [955, 12, 709, 72], [955, 19, 710, 18], [955, 20, 710, 19], [955, 35, 711, 12], [955, 39, 711, 12, "_jsxDevRuntime"], [955, 53, 711, 12], [955, 54, 711, 12, "jsxDEV"], [955, 60, 711, 12], [955, 62, 711, 13, "_View"], [955, 67, 711, 13], [955, 68, 711, 13, "default"], [955, 75, 711, 17], [956, 14, 711, 18, "style"], [956, 19, 711, 23], [956, 21, 711, 25, "styles"], [956, 27, 711, 31], [956, 28, 711, 32, "progressBar"], [956, 39, 711, 44], [957, 14, 711, 44, "children"], [957, 22, 711, 44], [957, 37, 712, 14], [957, 41, 712, 14, "_jsxDevRuntime"], [957, 55, 712, 14], [957, 56, 712, 14, "jsxDEV"], [957, 62, 712, 14], [957, 64, 712, 15, "_View"], [957, 69, 712, 15], [957, 70, 712, 15, "default"], [957, 77, 712, 19], [958, 16, 713, 16, "style"], [958, 21, 713, 21], [958, 23, 713, 23], [958, 24, 714, 18, "styles"], [958, 30, 714, 24], [958, 31, 714, 25, "progressFill"], [958, 43, 714, 37], [958, 45, 715, 18], [959, 18, 715, 20, "width"], [959, 23, 715, 25], [959, 25, 715, 27], [959, 28, 715, 30, "processingProgress"], [959, 46, 715, 48], [960, 16, 715, 52], [960, 17, 715, 53], [961, 14, 716, 18], [962, 16, 716, 18, "fileName"], [962, 24, 716, 18], [962, 26, 716, 18, "_jsxFileName"], [962, 38, 716, 18], [963, 16, 716, 18, "lineNumber"], [963, 26, 716, 18], [964, 16, 716, 18, "columnNumber"], [964, 28, 716, 18], [965, 14, 716, 18], [965, 21, 717, 15], [966, 12, 717, 16], [967, 14, 717, 16, "fileName"], [967, 22, 717, 16], [967, 24, 717, 16, "_jsxFileName"], [967, 36, 717, 16], [968, 14, 717, 16, "lineNumber"], [968, 24, 717, 16], [969, 14, 717, 16, "columnNumber"], [969, 26, 717, 16], [970, 12, 717, 16], [970, 19, 718, 18], [970, 20, 718, 19], [970, 35, 719, 12], [970, 39, 719, 12, "_jsxDevRuntime"], [970, 53, 719, 12], [970, 54, 719, 12, "jsxDEV"], [970, 60, 719, 12], [970, 62, 719, 13, "_Text"], [970, 67, 719, 13], [970, 68, 719, 13, "default"], [970, 75, 719, 17], [971, 14, 719, 18, "style"], [971, 19, 719, 23], [971, 21, 719, 25, "styles"], [971, 27, 719, 31], [971, 28, 719, 32, "processingDescription"], [971, 49, 719, 54], [972, 14, 719, 54, "children"], [972, 22, 719, 54], [972, 25, 720, 15, "processingState"], [972, 40, 720, 30], [972, 45, 720, 35], [972, 56, 720, 46], [972, 60, 720, 50], [972, 89, 720, 79], [972, 91, 721, 15, "processingState"], [972, 106, 721, 30], [972, 111, 721, 35], [972, 122, 721, 46], [972, 126, 721, 50], [972, 164, 721, 88], [972, 166, 722, 15, "processingState"], [972, 181, 722, 30], [972, 186, 722, 35], [972, 198, 722, 47], [972, 202, 722, 51], [972, 247, 722, 96], [972, 249, 723, 15, "processingState"], [972, 264, 723, 30], [972, 269, 723, 35], [972, 280, 723, 46], [972, 284, 723, 50], [972, 325, 723, 91], [973, 12, 723, 91], [974, 14, 723, 91, "fileName"], [974, 22, 723, 91], [974, 24, 723, 91, "_jsxFileName"], [974, 36, 723, 91], [975, 14, 723, 91, "lineNumber"], [975, 24, 723, 91], [976, 14, 723, 91, "columnNumber"], [976, 26, 723, 91], [977, 12, 723, 91], [977, 19, 724, 18], [977, 20, 724, 19], [977, 22, 725, 13, "processingState"], [977, 37, 725, 28], [977, 42, 725, 33], [977, 53, 725, 44], [977, 70, 726, 14], [977, 74, 726, 14, "_jsxDevRuntime"], [977, 88, 726, 14], [977, 89, 726, 14, "jsxDEV"], [977, 95, 726, 14], [977, 97, 726, 15, "_lucideReactNative"], [977, 115, 726, 15], [977, 116, 726, 15, "CheckCircle"], [977, 127, 726, 26], [978, 14, 726, 27, "size"], [978, 18, 726, 31], [978, 20, 726, 33], [978, 22, 726, 36], [979, 14, 726, 37, "color"], [979, 19, 726, 42], [979, 21, 726, 43], [979, 30, 726, 52], [980, 14, 726, 53, "style"], [980, 19, 726, 58], [980, 21, 726, 60, "styles"], [980, 27, 726, 66], [980, 28, 726, 67, "successIcon"], [981, 12, 726, 79], [982, 14, 726, 79, "fileName"], [982, 22, 726, 79], [982, 24, 726, 79, "_jsxFileName"], [982, 36, 726, 79], [983, 14, 726, 79, "lineNumber"], [983, 24, 726, 79], [984, 14, 726, 79, "columnNumber"], [984, 26, 726, 79], [985, 12, 726, 79], [985, 19, 726, 81], [985, 20, 727, 13], [986, 10, 727, 13], [987, 12, 727, 13, "fileName"], [987, 20, 727, 13], [987, 22, 727, 13, "_jsxFileName"], [987, 34, 727, 13], [988, 12, 727, 13, "lineNumber"], [988, 22, 727, 13], [989, 12, 727, 13, "columnNumber"], [989, 24, 727, 13], [990, 10, 727, 13], [990, 17, 728, 16], [991, 8, 728, 17], [992, 10, 728, 17, "fileName"], [992, 18, 728, 17], [992, 20, 728, 17, "_jsxFileName"], [992, 32, 728, 17], [993, 10, 728, 17, "lineNumber"], [993, 20, 728, 17], [994, 10, 728, 17, "columnNumber"], [994, 22, 728, 17], [995, 8, 728, 17], [995, 15, 729, 14], [996, 6, 729, 15], [997, 8, 729, 15, "fileName"], [997, 16, 729, 15], [997, 18, 729, 15, "_jsxFileName"], [997, 30, 729, 15], [998, 8, 729, 15, "lineNumber"], [998, 18, 729, 15], [999, 8, 729, 15, "columnNumber"], [999, 20, 729, 15], [1000, 6, 729, 15], [1000, 13, 730, 13], [1000, 14, 730, 14], [1000, 29, 732, 6], [1000, 33, 732, 6, "_jsxDevRuntime"], [1000, 47, 732, 6], [1000, 48, 732, 6, "jsxDEV"], [1000, 54, 732, 6], [1000, 56, 732, 7, "_Modal"], [1000, 62, 732, 7], [1000, 63, 732, 7, "default"], [1000, 70, 732, 12], [1001, 8, 733, 8, "visible"], [1001, 15, 733, 15], [1001, 17, 733, 17, "processingState"], [1001, 32, 733, 32], [1001, 37, 733, 37], [1001, 44, 733, 45], [1002, 8, 734, 8, "transparent"], [1002, 19, 734, 19], [1003, 8, 735, 8, "animationType"], [1003, 21, 735, 21], [1003, 23, 735, 22], [1003, 29, 735, 28], [1004, 8, 735, 28, "children"], [1004, 16, 735, 28], [1004, 31, 737, 8], [1004, 35, 737, 8, "_jsxDevRuntime"], [1004, 49, 737, 8], [1004, 50, 737, 8, "jsxDEV"], [1004, 56, 737, 8], [1004, 58, 737, 9, "_View"], [1004, 63, 737, 9], [1004, 64, 737, 9, "default"], [1004, 71, 737, 13], [1005, 10, 737, 14, "style"], [1005, 15, 737, 19], [1005, 17, 737, 21, "styles"], [1005, 23, 737, 27], [1005, 24, 737, 28, "processingModal"], [1005, 39, 737, 44], [1006, 10, 737, 44, "children"], [1006, 18, 737, 44], [1006, 33, 738, 10], [1006, 37, 738, 10, "_jsxDevRuntime"], [1006, 51, 738, 10], [1006, 52, 738, 10, "jsxDEV"], [1006, 58, 738, 10], [1006, 60, 738, 11, "_View"], [1006, 65, 738, 11], [1006, 66, 738, 11, "default"], [1006, 73, 738, 15], [1007, 12, 738, 16, "style"], [1007, 17, 738, 21], [1007, 19, 738, 23, "styles"], [1007, 25, 738, 29], [1007, 26, 738, 30, "errorContent"], [1007, 38, 738, 43], [1008, 12, 738, 43, "children"], [1008, 20, 738, 43], [1008, 36, 739, 12], [1008, 40, 739, 12, "_jsxDevRuntime"], [1008, 54, 739, 12], [1008, 55, 739, 12, "jsxDEV"], [1008, 61, 739, 12], [1008, 63, 739, 13, "_lucideReactNative"], [1008, 81, 739, 13], [1008, 82, 739, 13, "X"], [1008, 83, 739, 14], [1009, 14, 739, 15, "size"], [1009, 18, 739, 19], [1009, 20, 739, 21], [1009, 22, 739, 24], [1010, 14, 739, 25, "color"], [1010, 19, 739, 30], [1010, 21, 739, 31], [1011, 12, 739, 40], [1012, 14, 739, 40, "fileName"], [1012, 22, 739, 40], [1012, 24, 739, 40, "_jsxFileName"], [1012, 36, 739, 40], [1013, 14, 739, 40, "lineNumber"], [1013, 24, 739, 40], [1014, 14, 739, 40, "columnNumber"], [1014, 26, 739, 40], [1015, 12, 739, 40], [1015, 19, 739, 42], [1015, 20, 739, 43], [1015, 35, 740, 12], [1015, 39, 740, 12, "_jsxDevRuntime"], [1015, 53, 740, 12], [1015, 54, 740, 12, "jsxDEV"], [1015, 60, 740, 12], [1015, 62, 740, 13, "_Text"], [1015, 67, 740, 13], [1015, 68, 740, 13, "default"], [1015, 75, 740, 17], [1016, 14, 740, 18, "style"], [1016, 19, 740, 23], [1016, 21, 740, 25, "styles"], [1016, 27, 740, 31], [1016, 28, 740, 32, "errorTitle"], [1016, 38, 740, 43], [1017, 14, 740, 43, "children"], [1017, 22, 740, 43], [1017, 24, 740, 44], [1018, 12, 740, 61], [1019, 14, 740, 61, "fileName"], [1019, 22, 740, 61], [1019, 24, 740, 61, "_jsxFileName"], [1019, 36, 740, 61], [1020, 14, 740, 61, "lineNumber"], [1020, 24, 740, 61], [1021, 14, 740, 61, "columnNumber"], [1021, 26, 740, 61], [1022, 12, 740, 61], [1022, 19, 740, 67], [1022, 20, 740, 68], [1022, 35, 741, 12], [1022, 39, 741, 12, "_jsxDevRuntime"], [1022, 53, 741, 12], [1022, 54, 741, 12, "jsxDEV"], [1022, 60, 741, 12], [1022, 62, 741, 13, "_Text"], [1022, 67, 741, 13], [1022, 68, 741, 13, "default"], [1022, 75, 741, 17], [1023, 14, 741, 18, "style"], [1023, 19, 741, 23], [1023, 21, 741, 25, "styles"], [1023, 27, 741, 31], [1023, 28, 741, 32, "errorMessage"], [1023, 40, 741, 45], [1024, 14, 741, 45, "children"], [1024, 22, 741, 45], [1024, 24, 741, 47, "errorMessage"], [1025, 12, 741, 59], [1026, 14, 741, 59, "fileName"], [1026, 22, 741, 59], [1026, 24, 741, 59, "_jsxFileName"], [1026, 36, 741, 59], [1027, 14, 741, 59, "lineNumber"], [1027, 24, 741, 59], [1028, 14, 741, 59, "columnNumber"], [1028, 26, 741, 59], [1029, 12, 741, 59], [1029, 19, 741, 66], [1029, 20, 741, 67], [1029, 35, 742, 12], [1029, 39, 742, 12, "_jsxDevRuntime"], [1029, 53, 742, 12], [1029, 54, 742, 12, "jsxDEV"], [1029, 60, 742, 12], [1029, 62, 742, 13, "_TouchableOpacity"], [1029, 79, 742, 13], [1029, 80, 742, 13, "default"], [1029, 87, 742, 29], [1030, 14, 743, 14, "onPress"], [1030, 21, 743, 21], [1030, 23, 743, 23, "retryCapture"], [1030, 35, 743, 36], [1031, 14, 744, 14, "style"], [1031, 19, 744, 19], [1031, 21, 744, 21, "styles"], [1031, 27, 744, 27], [1031, 28, 744, 28, "primaryButton"], [1031, 41, 744, 42], [1032, 14, 744, 42, "children"], [1032, 22, 744, 42], [1032, 37, 746, 14], [1032, 41, 746, 14, "_jsxDevRuntime"], [1032, 55, 746, 14], [1032, 56, 746, 14, "jsxDEV"], [1032, 62, 746, 14], [1032, 64, 746, 15, "_Text"], [1032, 69, 746, 15], [1032, 70, 746, 15, "default"], [1032, 77, 746, 19], [1033, 16, 746, 20, "style"], [1033, 21, 746, 25], [1033, 23, 746, 27, "styles"], [1033, 29, 746, 33], [1033, 30, 746, 34, "primaryButtonText"], [1033, 47, 746, 52], [1034, 16, 746, 52, "children"], [1034, 24, 746, 52], [1034, 26, 746, 53], [1035, 14, 746, 62], [1036, 16, 746, 62, "fileName"], [1036, 24, 746, 62], [1036, 26, 746, 62, "_jsxFileName"], [1036, 38, 746, 62], [1037, 16, 746, 62, "lineNumber"], [1037, 26, 746, 62], [1038, 16, 746, 62, "columnNumber"], [1038, 28, 746, 62], [1039, 14, 746, 62], [1039, 21, 746, 68], [1040, 12, 746, 69], [1041, 14, 746, 69, "fileName"], [1041, 22, 746, 69], [1041, 24, 746, 69, "_jsxFileName"], [1041, 36, 746, 69], [1042, 14, 746, 69, "lineNumber"], [1042, 24, 746, 69], [1043, 14, 746, 69, "columnNumber"], [1043, 26, 746, 69], [1044, 12, 746, 69], [1044, 19, 747, 30], [1044, 20, 747, 31], [1044, 35, 748, 12], [1044, 39, 748, 12, "_jsxDevRuntime"], [1044, 53, 748, 12], [1044, 54, 748, 12, "jsxDEV"], [1044, 60, 748, 12], [1044, 62, 748, 13, "_TouchableOpacity"], [1044, 79, 748, 13], [1044, 80, 748, 13, "default"], [1044, 87, 748, 29], [1045, 14, 749, 14, "onPress"], [1045, 21, 749, 21], [1045, 23, 749, 23, "onCancel"], [1045, 31, 749, 32], [1046, 14, 750, 14, "style"], [1046, 19, 750, 19], [1046, 21, 750, 21, "styles"], [1046, 27, 750, 27], [1046, 28, 750, 28, "secondaryButton"], [1046, 43, 750, 44], [1047, 14, 750, 44, "children"], [1047, 22, 750, 44], [1047, 37, 752, 14], [1047, 41, 752, 14, "_jsxDevRuntime"], [1047, 55, 752, 14], [1047, 56, 752, 14, "jsxDEV"], [1047, 62, 752, 14], [1047, 64, 752, 15, "_Text"], [1047, 69, 752, 15], [1047, 70, 752, 15, "default"], [1047, 77, 752, 19], [1048, 16, 752, 20, "style"], [1048, 21, 752, 25], [1048, 23, 752, 27, "styles"], [1048, 29, 752, 33], [1048, 30, 752, 34, "secondaryButtonText"], [1048, 49, 752, 54], [1049, 16, 752, 54, "children"], [1049, 24, 752, 54], [1049, 26, 752, 55], [1050, 14, 752, 61], [1051, 16, 752, 61, "fileName"], [1051, 24, 752, 61], [1051, 26, 752, 61, "_jsxFileName"], [1051, 38, 752, 61], [1052, 16, 752, 61, "lineNumber"], [1052, 26, 752, 61], [1053, 16, 752, 61, "columnNumber"], [1053, 28, 752, 61], [1054, 14, 752, 61], [1054, 21, 752, 67], [1055, 12, 752, 68], [1056, 14, 752, 68, "fileName"], [1056, 22, 752, 68], [1056, 24, 752, 68, "_jsxFileName"], [1056, 36, 752, 68], [1057, 14, 752, 68, "lineNumber"], [1057, 24, 752, 68], [1058, 14, 752, 68, "columnNumber"], [1058, 26, 752, 68], [1059, 12, 752, 68], [1059, 19, 753, 30], [1059, 20, 753, 31], [1060, 10, 753, 31], [1061, 12, 753, 31, "fileName"], [1061, 20, 753, 31], [1061, 22, 753, 31, "_jsxFileName"], [1061, 34, 753, 31], [1062, 12, 753, 31, "lineNumber"], [1062, 22, 753, 31], [1063, 12, 753, 31, "columnNumber"], [1063, 24, 753, 31], [1064, 10, 753, 31], [1064, 17, 754, 16], [1065, 8, 754, 17], [1066, 10, 754, 17, "fileName"], [1066, 18, 754, 17], [1066, 20, 754, 17, "_jsxFileName"], [1066, 32, 754, 17], [1067, 10, 754, 17, "lineNumber"], [1067, 20, 754, 17], [1068, 10, 754, 17, "columnNumber"], [1068, 22, 754, 17], [1069, 8, 754, 17], [1069, 15, 755, 14], [1070, 6, 755, 15], [1071, 8, 755, 15, "fileName"], [1071, 16, 755, 15], [1071, 18, 755, 15, "_jsxFileName"], [1071, 30, 755, 15], [1072, 8, 755, 15, "lineNumber"], [1072, 18, 755, 15], [1073, 8, 755, 15, "columnNumber"], [1073, 20, 755, 15], [1074, 6, 755, 15], [1074, 13, 756, 13], [1074, 14, 756, 14], [1075, 4, 756, 14], [1076, 6, 756, 14, "fileName"], [1076, 14, 756, 14], [1076, 16, 756, 14, "_jsxFileName"], [1076, 28, 756, 14], [1077, 6, 756, 14, "lineNumber"], [1077, 16, 756, 14], [1078, 6, 756, 14, "columnNumber"], [1078, 18, 756, 14], [1079, 4, 756, 14], [1079, 11, 757, 10], [1079, 12, 757, 11], [1080, 2, 759, 0], [1081, 2, 759, 1, "_s"], [1081, 4, 759, 1], [1081, 5, 51, 24, "EchoCameraWeb"], [1081, 18, 51, 37], [1082, 4, 51, 37], [1082, 12, 58, 42, "useCameraPermissions"], [1082, 44, 58, 62], [1082, 46, 72, 19, "useUpload"], [1082, 64, 72, 28], [1083, 2, 72, 28], [1084, 2, 72, 28, "_c"], [1084, 4, 72, 28], [1084, 7, 51, 24, "EchoCameraWeb"], [1084, 20, 51, 37], [1085, 2, 760, 0], [1085, 8, 760, 6, "styles"], [1085, 14, 760, 12], [1085, 17, 760, 15, "StyleSheet"], [1085, 36, 760, 25], [1085, 37, 760, 26, "create"], [1085, 43, 760, 32], [1085, 44, 760, 33], [1086, 4, 761, 2, "container"], [1086, 13, 761, 11], [1086, 15, 761, 13], [1087, 6, 762, 4, "flex"], [1087, 10, 762, 8], [1087, 12, 762, 10], [1087, 13, 762, 11], [1088, 6, 763, 4, "backgroundColor"], [1088, 21, 763, 19], [1088, 23, 763, 21], [1089, 4, 764, 2], [1089, 5, 764, 3], [1090, 4, 765, 2, "cameraContainer"], [1090, 19, 765, 17], [1090, 21, 765, 19], [1091, 6, 766, 4, "flex"], [1091, 10, 766, 8], [1091, 12, 766, 10], [1091, 13, 766, 11], [1092, 6, 767, 4, "max<PERSON><PERSON><PERSON>"], [1092, 14, 767, 12], [1092, 16, 767, 14], [1092, 19, 767, 17], [1093, 6, 768, 4, "alignSelf"], [1093, 15, 768, 13], [1093, 17, 768, 15], [1093, 25, 768, 23], [1094, 6, 769, 4, "width"], [1094, 11, 769, 9], [1094, 13, 769, 11], [1095, 4, 770, 2], [1095, 5, 770, 3], [1096, 4, 771, 2, "camera"], [1096, 10, 771, 8], [1096, 12, 771, 10], [1097, 6, 772, 4, "flex"], [1097, 10, 772, 8], [1097, 12, 772, 10], [1098, 4, 773, 2], [1098, 5, 773, 3], [1099, 4, 774, 2, "headerOverlay"], [1099, 17, 774, 15], [1099, 19, 774, 17], [1100, 6, 775, 4, "position"], [1100, 14, 775, 12], [1100, 16, 775, 14], [1100, 26, 775, 24], [1101, 6, 776, 4, "top"], [1101, 9, 776, 7], [1101, 11, 776, 9], [1101, 12, 776, 10], [1102, 6, 777, 4, "left"], [1102, 10, 777, 8], [1102, 12, 777, 10], [1102, 13, 777, 11], [1103, 6, 778, 4, "right"], [1103, 11, 778, 9], [1103, 13, 778, 11], [1103, 14, 778, 12], [1104, 6, 779, 4, "backgroundColor"], [1104, 21, 779, 19], [1104, 23, 779, 21], [1104, 36, 779, 34], [1105, 6, 780, 4, "paddingTop"], [1105, 16, 780, 14], [1105, 18, 780, 16], [1105, 20, 780, 18], [1106, 6, 781, 4, "paddingHorizontal"], [1106, 23, 781, 21], [1106, 25, 781, 23], [1106, 27, 781, 25], [1107, 6, 782, 4, "paddingBottom"], [1107, 19, 782, 17], [1107, 21, 782, 19], [1108, 4, 783, 2], [1108, 5, 783, 3], [1109, 4, 784, 2, "headerContent"], [1109, 17, 784, 15], [1109, 19, 784, 17], [1110, 6, 785, 4, "flexDirection"], [1110, 19, 785, 17], [1110, 21, 785, 19], [1110, 26, 785, 24], [1111, 6, 786, 4, "justifyContent"], [1111, 20, 786, 18], [1111, 22, 786, 20], [1111, 37, 786, 35], [1112, 6, 787, 4, "alignItems"], [1112, 16, 787, 14], [1112, 18, 787, 16], [1113, 4, 788, 2], [1113, 5, 788, 3], [1114, 4, 789, 2, "headerLeft"], [1114, 14, 789, 12], [1114, 16, 789, 14], [1115, 6, 790, 4, "flex"], [1115, 10, 790, 8], [1115, 12, 790, 10], [1116, 4, 791, 2], [1116, 5, 791, 3], [1117, 4, 792, 2, "headerTitle"], [1117, 15, 792, 13], [1117, 17, 792, 15], [1118, 6, 793, 4, "fontSize"], [1118, 14, 793, 12], [1118, 16, 793, 14], [1118, 18, 793, 16], [1119, 6, 794, 4, "fontWeight"], [1119, 16, 794, 14], [1119, 18, 794, 16], [1119, 23, 794, 21], [1120, 6, 795, 4, "color"], [1120, 11, 795, 9], [1120, 13, 795, 11], [1120, 19, 795, 17], [1121, 6, 796, 4, "marginBottom"], [1121, 18, 796, 16], [1121, 20, 796, 18], [1122, 4, 797, 2], [1122, 5, 797, 3], [1123, 4, 798, 2, "subtitleRow"], [1123, 15, 798, 13], [1123, 17, 798, 15], [1124, 6, 799, 4, "flexDirection"], [1124, 19, 799, 17], [1124, 21, 799, 19], [1124, 26, 799, 24], [1125, 6, 800, 4, "alignItems"], [1125, 16, 800, 14], [1125, 18, 800, 16], [1125, 26, 800, 24], [1126, 6, 801, 4, "marginBottom"], [1126, 18, 801, 16], [1126, 20, 801, 18], [1127, 4, 802, 2], [1127, 5, 802, 3], [1128, 4, 803, 2, "webIcon"], [1128, 11, 803, 9], [1128, 13, 803, 11], [1129, 6, 804, 4, "fontSize"], [1129, 14, 804, 12], [1129, 16, 804, 14], [1129, 18, 804, 16], [1130, 6, 805, 4, "marginRight"], [1130, 17, 805, 15], [1130, 19, 805, 17], [1131, 4, 806, 2], [1131, 5, 806, 3], [1132, 4, 807, 2, "headerSubtitle"], [1132, 18, 807, 16], [1132, 20, 807, 18], [1133, 6, 808, 4, "fontSize"], [1133, 14, 808, 12], [1133, 16, 808, 14], [1133, 18, 808, 16], [1134, 6, 809, 4, "color"], [1134, 11, 809, 9], [1134, 13, 809, 11], [1134, 19, 809, 17], [1135, 6, 810, 4, "opacity"], [1135, 13, 810, 11], [1135, 15, 810, 13], [1136, 4, 811, 2], [1136, 5, 811, 3], [1137, 4, 812, 2, "challengeRow"], [1137, 16, 812, 14], [1137, 18, 812, 16], [1138, 6, 813, 4, "flexDirection"], [1138, 19, 813, 17], [1138, 21, 813, 19], [1138, 26, 813, 24], [1139, 6, 814, 4, "alignItems"], [1139, 16, 814, 14], [1139, 18, 814, 16], [1140, 4, 815, 2], [1140, 5, 815, 3], [1141, 4, 816, 2, "challengeCode"], [1141, 17, 816, 15], [1141, 19, 816, 17], [1142, 6, 817, 4, "fontSize"], [1142, 14, 817, 12], [1142, 16, 817, 14], [1142, 18, 817, 16], [1143, 6, 818, 4, "color"], [1143, 11, 818, 9], [1143, 13, 818, 11], [1143, 19, 818, 17], [1144, 6, 819, 4, "marginLeft"], [1144, 16, 819, 14], [1144, 18, 819, 16], [1144, 19, 819, 17], [1145, 6, 820, 4, "fontFamily"], [1145, 16, 820, 14], [1145, 18, 820, 16], [1146, 4, 821, 2], [1146, 5, 821, 3], [1147, 4, 822, 2, "closeButton"], [1147, 15, 822, 13], [1147, 17, 822, 15], [1148, 6, 823, 4, "padding"], [1148, 13, 823, 11], [1148, 15, 823, 13], [1149, 4, 824, 2], [1149, 5, 824, 3], [1150, 4, 825, 2, "privacyNotice"], [1150, 17, 825, 15], [1150, 19, 825, 17], [1151, 6, 826, 4, "position"], [1151, 14, 826, 12], [1151, 16, 826, 14], [1151, 26, 826, 24], [1152, 6, 827, 4, "top"], [1152, 9, 827, 7], [1152, 11, 827, 9], [1152, 14, 827, 12], [1153, 6, 828, 4, "left"], [1153, 10, 828, 8], [1153, 12, 828, 10], [1153, 14, 828, 12], [1154, 6, 829, 4, "right"], [1154, 11, 829, 9], [1154, 13, 829, 11], [1154, 15, 829, 13], [1155, 6, 830, 4, "backgroundColor"], [1155, 21, 830, 19], [1155, 23, 830, 21], [1155, 48, 830, 46], [1156, 6, 831, 4, "borderRadius"], [1156, 18, 831, 16], [1156, 20, 831, 18], [1156, 21, 831, 19], [1157, 6, 832, 4, "padding"], [1157, 13, 832, 11], [1157, 15, 832, 13], [1157, 17, 832, 15], [1158, 6, 833, 4, "flexDirection"], [1158, 19, 833, 17], [1158, 21, 833, 19], [1158, 26, 833, 24], [1159, 6, 834, 4, "alignItems"], [1159, 16, 834, 14], [1159, 18, 834, 16], [1160, 4, 835, 2], [1160, 5, 835, 3], [1161, 4, 836, 2, "privacyText"], [1161, 15, 836, 13], [1161, 17, 836, 15], [1162, 6, 837, 4, "color"], [1162, 11, 837, 9], [1162, 13, 837, 11], [1162, 19, 837, 17], [1163, 6, 838, 4, "fontSize"], [1163, 14, 838, 12], [1163, 16, 838, 14], [1163, 18, 838, 16], [1164, 6, 839, 4, "marginLeft"], [1164, 16, 839, 14], [1164, 18, 839, 16], [1164, 19, 839, 17], [1165, 6, 840, 4, "flex"], [1165, 10, 840, 8], [1165, 12, 840, 10], [1166, 4, 841, 2], [1166, 5, 841, 3], [1167, 4, 842, 2, "footer<PERSON><PERSON><PERSON>"], [1167, 17, 842, 15], [1167, 19, 842, 17], [1168, 6, 843, 4, "position"], [1168, 14, 843, 12], [1168, 16, 843, 14], [1168, 26, 843, 24], [1169, 6, 844, 4, "bottom"], [1169, 12, 844, 10], [1169, 14, 844, 12], [1169, 15, 844, 13], [1170, 6, 845, 4, "left"], [1170, 10, 845, 8], [1170, 12, 845, 10], [1170, 13, 845, 11], [1171, 6, 846, 4, "right"], [1171, 11, 846, 9], [1171, 13, 846, 11], [1171, 14, 846, 12], [1172, 6, 847, 4, "backgroundColor"], [1172, 21, 847, 19], [1172, 23, 847, 21], [1172, 36, 847, 34], [1173, 6, 848, 4, "paddingBottom"], [1173, 19, 848, 17], [1173, 21, 848, 19], [1173, 23, 848, 21], [1174, 6, 849, 4, "paddingTop"], [1174, 16, 849, 14], [1174, 18, 849, 16], [1174, 20, 849, 18], [1175, 6, 850, 4, "alignItems"], [1175, 16, 850, 14], [1175, 18, 850, 16], [1176, 4, 851, 2], [1176, 5, 851, 3], [1177, 4, 852, 2, "instruction"], [1177, 15, 852, 13], [1177, 17, 852, 15], [1178, 6, 853, 4, "fontSize"], [1178, 14, 853, 12], [1178, 16, 853, 14], [1178, 18, 853, 16], [1179, 6, 854, 4, "color"], [1179, 11, 854, 9], [1179, 13, 854, 11], [1179, 19, 854, 17], [1180, 6, 855, 4, "marginBottom"], [1180, 18, 855, 16], [1180, 20, 855, 18], [1181, 4, 856, 2], [1181, 5, 856, 3], [1182, 4, 857, 2, "shutterButton"], [1182, 17, 857, 15], [1182, 19, 857, 17], [1183, 6, 858, 4, "width"], [1183, 11, 858, 9], [1183, 13, 858, 11], [1183, 15, 858, 13], [1184, 6, 859, 4, "height"], [1184, 12, 859, 10], [1184, 14, 859, 12], [1184, 16, 859, 14], [1185, 6, 860, 4, "borderRadius"], [1185, 18, 860, 16], [1185, 20, 860, 18], [1185, 22, 860, 20], [1186, 6, 861, 4, "backgroundColor"], [1186, 21, 861, 19], [1186, 23, 861, 21], [1186, 29, 861, 27], [1187, 6, 862, 4, "justifyContent"], [1187, 20, 862, 18], [1187, 22, 862, 20], [1187, 30, 862, 28], [1188, 6, 863, 4, "alignItems"], [1188, 16, 863, 14], [1188, 18, 863, 16], [1188, 26, 863, 24], [1189, 6, 864, 4, "marginBottom"], [1189, 18, 864, 16], [1189, 20, 864, 18], [1189, 22, 864, 20], [1190, 6, 865, 4], [1190, 9, 865, 7, "Platform"], [1190, 26, 865, 15], [1190, 27, 865, 16, "select"], [1190, 33, 865, 22], [1190, 34, 865, 23], [1191, 8, 866, 6, "ios"], [1191, 11, 866, 9], [1191, 13, 866, 11], [1192, 10, 867, 8, "shadowColor"], [1192, 21, 867, 19], [1192, 23, 867, 21], [1192, 32, 867, 30], [1193, 10, 868, 8, "shadowOffset"], [1193, 22, 868, 20], [1193, 24, 868, 22], [1194, 12, 868, 24, "width"], [1194, 17, 868, 29], [1194, 19, 868, 31], [1194, 20, 868, 32], [1195, 12, 868, 34, "height"], [1195, 18, 868, 40], [1195, 20, 868, 42], [1196, 10, 868, 44], [1196, 11, 868, 45], [1197, 10, 869, 8, "shadowOpacity"], [1197, 23, 869, 21], [1197, 25, 869, 23], [1197, 28, 869, 26], [1198, 10, 870, 8, "shadowRadius"], [1198, 22, 870, 20], [1198, 24, 870, 22], [1199, 8, 871, 6], [1199, 9, 871, 7], [1200, 8, 872, 6, "android"], [1200, 15, 872, 13], [1200, 17, 872, 15], [1201, 10, 873, 8, "elevation"], [1201, 19, 873, 17], [1201, 21, 873, 19], [1202, 8, 874, 6], [1202, 9, 874, 7], [1203, 8, 875, 6, "web"], [1203, 11, 875, 9], [1203, 13, 875, 11], [1204, 10, 876, 8, "boxShadow"], [1204, 19, 876, 17], [1204, 21, 876, 19], [1205, 8, 877, 6], [1206, 6, 878, 4], [1206, 7, 878, 5], [1207, 4, 879, 2], [1207, 5, 879, 3], [1208, 4, 880, 2, "shutterButtonDisabled"], [1208, 25, 880, 23], [1208, 27, 880, 25], [1209, 6, 881, 4, "opacity"], [1209, 13, 881, 11], [1209, 15, 881, 13], [1210, 4, 882, 2], [1210, 5, 882, 3], [1211, 4, 883, 2, "shutterInner"], [1211, 16, 883, 14], [1211, 18, 883, 16], [1212, 6, 884, 4, "width"], [1212, 11, 884, 9], [1212, 13, 884, 11], [1212, 15, 884, 13], [1213, 6, 885, 4, "height"], [1213, 12, 885, 10], [1213, 14, 885, 12], [1213, 16, 885, 14], [1214, 6, 886, 4, "borderRadius"], [1214, 18, 886, 16], [1214, 20, 886, 18], [1214, 22, 886, 20], [1215, 6, 887, 4, "backgroundColor"], [1215, 21, 887, 19], [1215, 23, 887, 21], [1215, 29, 887, 27], [1216, 6, 888, 4, "borderWidth"], [1216, 17, 888, 15], [1216, 19, 888, 17], [1216, 20, 888, 18], [1217, 6, 889, 4, "borderColor"], [1217, 17, 889, 15], [1217, 19, 889, 17], [1218, 4, 890, 2], [1218, 5, 890, 3], [1219, 4, 891, 2, "privacyNote"], [1219, 15, 891, 13], [1219, 17, 891, 15], [1220, 6, 892, 4, "fontSize"], [1220, 14, 892, 12], [1220, 16, 892, 14], [1220, 18, 892, 16], [1221, 6, 893, 4, "color"], [1221, 11, 893, 9], [1221, 13, 893, 11], [1222, 4, 894, 2], [1222, 5, 894, 3], [1223, 4, 895, 2, "processingModal"], [1223, 19, 895, 17], [1223, 21, 895, 19], [1224, 6, 896, 4, "flex"], [1224, 10, 896, 8], [1224, 12, 896, 10], [1224, 13, 896, 11], [1225, 6, 897, 4, "backgroundColor"], [1225, 21, 897, 19], [1225, 23, 897, 21], [1225, 43, 897, 41], [1226, 6, 898, 4, "justifyContent"], [1226, 20, 898, 18], [1226, 22, 898, 20], [1226, 30, 898, 28], [1227, 6, 899, 4, "alignItems"], [1227, 16, 899, 14], [1227, 18, 899, 16], [1228, 4, 900, 2], [1228, 5, 900, 3], [1229, 4, 901, 2, "processingContent"], [1229, 21, 901, 19], [1229, 23, 901, 21], [1230, 6, 902, 4, "backgroundColor"], [1230, 21, 902, 19], [1230, 23, 902, 21], [1230, 29, 902, 27], [1231, 6, 903, 4, "borderRadius"], [1231, 18, 903, 16], [1231, 20, 903, 18], [1231, 22, 903, 20], [1232, 6, 904, 4, "padding"], [1232, 13, 904, 11], [1232, 15, 904, 13], [1232, 17, 904, 15], [1233, 6, 905, 4, "width"], [1233, 11, 905, 9], [1233, 13, 905, 11], [1233, 18, 905, 16], [1234, 6, 906, 4, "max<PERSON><PERSON><PERSON>"], [1234, 14, 906, 12], [1234, 16, 906, 14], [1234, 19, 906, 17], [1235, 6, 907, 4, "alignItems"], [1235, 16, 907, 14], [1235, 18, 907, 16], [1236, 4, 908, 2], [1236, 5, 908, 3], [1237, 4, 909, 2, "processingTitle"], [1237, 19, 909, 17], [1237, 21, 909, 19], [1238, 6, 910, 4, "fontSize"], [1238, 14, 910, 12], [1238, 16, 910, 14], [1238, 18, 910, 16], [1239, 6, 911, 4, "fontWeight"], [1239, 16, 911, 14], [1239, 18, 911, 16], [1239, 23, 911, 21], [1240, 6, 912, 4, "color"], [1240, 11, 912, 9], [1240, 13, 912, 11], [1240, 22, 912, 20], [1241, 6, 913, 4, "marginTop"], [1241, 15, 913, 13], [1241, 17, 913, 15], [1241, 19, 913, 17], [1242, 6, 914, 4, "marginBottom"], [1242, 18, 914, 16], [1242, 20, 914, 18], [1243, 4, 915, 2], [1243, 5, 915, 3], [1244, 4, 916, 2, "progressBar"], [1244, 15, 916, 13], [1244, 17, 916, 15], [1245, 6, 917, 4, "width"], [1245, 11, 917, 9], [1245, 13, 917, 11], [1245, 19, 917, 17], [1246, 6, 918, 4, "height"], [1246, 12, 918, 10], [1246, 14, 918, 12], [1246, 15, 918, 13], [1247, 6, 919, 4, "backgroundColor"], [1247, 21, 919, 19], [1247, 23, 919, 21], [1247, 32, 919, 30], [1248, 6, 920, 4, "borderRadius"], [1248, 18, 920, 16], [1248, 20, 920, 18], [1248, 21, 920, 19], [1249, 6, 921, 4, "overflow"], [1249, 14, 921, 12], [1249, 16, 921, 14], [1249, 24, 921, 22], [1250, 6, 922, 4, "marginBottom"], [1250, 18, 922, 16], [1250, 20, 922, 18], [1251, 4, 923, 2], [1251, 5, 923, 3], [1252, 4, 924, 2, "progressFill"], [1252, 16, 924, 14], [1252, 18, 924, 16], [1253, 6, 925, 4, "height"], [1253, 12, 925, 10], [1253, 14, 925, 12], [1253, 20, 925, 18], [1254, 6, 926, 4, "backgroundColor"], [1254, 21, 926, 19], [1254, 23, 926, 21], [1254, 32, 926, 30], [1255, 6, 927, 4, "borderRadius"], [1255, 18, 927, 16], [1255, 20, 927, 18], [1256, 4, 928, 2], [1256, 5, 928, 3], [1257, 4, 929, 2, "processingDescription"], [1257, 25, 929, 23], [1257, 27, 929, 25], [1258, 6, 930, 4, "fontSize"], [1258, 14, 930, 12], [1258, 16, 930, 14], [1258, 18, 930, 16], [1259, 6, 931, 4, "color"], [1259, 11, 931, 9], [1259, 13, 931, 11], [1259, 22, 931, 20], [1260, 6, 932, 4, "textAlign"], [1260, 15, 932, 13], [1260, 17, 932, 15], [1261, 4, 933, 2], [1261, 5, 933, 3], [1262, 4, 934, 2, "successIcon"], [1262, 15, 934, 13], [1262, 17, 934, 15], [1263, 6, 935, 4, "marginTop"], [1263, 15, 935, 13], [1263, 17, 935, 15], [1264, 4, 936, 2], [1264, 5, 936, 3], [1265, 4, 937, 2, "errorContent"], [1265, 16, 937, 14], [1265, 18, 937, 16], [1266, 6, 938, 4, "backgroundColor"], [1266, 21, 938, 19], [1266, 23, 938, 21], [1266, 29, 938, 27], [1267, 6, 939, 4, "borderRadius"], [1267, 18, 939, 16], [1267, 20, 939, 18], [1267, 22, 939, 20], [1268, 6, 940, 4, "padding"], [1268, 13, 940, 11], [1268, 15, 940, 13], [1268, 17, 940, 15], [1269, 6, 941, 4, "width"], [1269, 11, 941, 9], [1269, 13, 941, 11], [1269, 18, 941, 16], [1270, 6, 942, 4, "max<PERSON><PERSON><PERSON>"], [1270, 14, 942, 12], [1270, 16, 942, 14], [1270, 19, 942, 17], [1271, 6, 943, 4, "alignItems"], [1271, 16, 943, 14], [1271, 18, 943, 16], [1272, 4, 944, 2], [1272, 5, 944, 3], [1273, 4, 945, 2, "errorTitle"], [1273, 14, 945, 12], [1273, 16, 945, 14], [1274, 6, 946, 4, "fontSize"], [1274, 14, 946, 12], [1274, 16, 946, 14], [1274, 18, 946, 16], [1275, 6, 947, 4, "fontWeight"], [1275, 16, 947, 14], [1275, 18, 947, 16], [1275, 23, 947, 21], [1276, 6, 948, 4, "color"], [1276, 11, 948, 9], [1276, 13, 948, 11], [1276, 22, 948, 20], [1277, 6, 949, 4, "marginTop"], [1277, 15, 949, 13], [1277, 17, 949, 15], [1277, 19, 949, 17], [1278, 6, 950, 4, "marginBottom"], [1278, 18, 950, 16], [1278, 20, 950, 18], [1279, 4, 951, 2], [1279, 5, 951, 3], [1280, 4, 952, 2, "errorMessage"], [1280, 16, 952, 14], [1280, 18, 952, 16], [1281, 6, 953, 4, "fontSize"], [1281, 14, 953, 12], [1281, 16, 953, 14], [1281, 18, 953, 16], [1282, 6, 954, 4, "color"], [1282, 11, 954, 9], [1282, 13, 954, 11], [1282, 22, 954, 20], [1283, 6, 955, 4, "textAlign"], [1283, 15, 955, 13], [1283, 17, 955, 15], [1283, 25, 955, 23], [1284, 6, 956, 4, "marginBottom"], [1284, 18, 956, 16], [1284, 20, 956, 18], [1285, 4, 957, 2], [1285, 5, 957, 3], [1286, 4, 958, 2, "primaryButton"], [1286, 17, 958, 15], [1286, 19, 958, 17], [1287, 6, 959, 4, "backgroundColor"], [1287, 21, 959, 19], [1287, 23, 959, 21], [1287, 32, 959, 30], [1288, 6, 960, 4, "paddingHorizontal"], [1288, 23, 960, 21], [1288, 25, 960, 23], [1288, 27, 960, 25], [1289, 6, 961, 4, "paddingVertical"], [1289, 21, 961, 19], [1289, 23, 961, 21], [1289, 25, 961, 23], [1290, 6, 962, 4, "borderRadius"], [1290, 18, 962, 16], [1290, 20, 962, 18], [1290, 21, 962, 19], [1291, 6, 963, 4, "marginTop"], [1291, 15, 963, 13], [1291, 17, 963, 15], [1292, 4, 964, 2], [1292, 5, 964, 3], [1293, 4, 965, 2, "primaryButtonText"], [1293, 21, 965, 19], [1293, 23, 965, 21], [1294, 6, 966, 4, "color"], [1294, 11, 966, 9], [1294, 13, 966, 11], [1294, 19, 966, 17], [1295, 6, 967, 4, "fontSize"], [1295, 14, 967, 12], [1295, 16, 967, 14], [1295, 18, 967, 16], [1296, 6, 968, 4, "fontWeight"], [1296, 16, 968, 14], [1296, 18, 968, 16], [1297, 4, 969, 2], [1297, 5, 969, 3], [1298, 4, 970, 2, "secondaryButton"], [1298, 19, 970, 17], [1298, 21, 970, 19], [1299, 6, 971, 4, "paddingHorizontal"], [1299, 23, 971, 21], [1299, 25, 971, 23], [1299, 27, 971, 25], [1300, 6, 972, 4, "paddingVertical"], [1300, 21, 972, 19], [1300, 23, 972, 21], [1300, 25, 972, 23], [1301, 6, 973, 4, "marginTop"], [1301, 15, 973, 13], [1301, 17, 973, 15], [1302, 4, 974, 2], [1302, 5, 974, 3], [1303, 4, 975, 2, "secondaryButtonText"], [1303, 23, 975, 21], [1303, 25, 975, 23], [1304, 6, 976, 4, "color"], [1304, 11, 976, 9], [1304, 13, 976, 11], [1304, 22, 976, 20], [1305, 6, 977, 4, "fontSize"], [1305, 14, 977, 12], [1305, 16, 977, 14], [1306, 4, 978, 2], [1306, 5, 978, 3], [1307, 4, 979, 2, "permissionContent"], [1307, 21, 979, 19], [1307, 23, 979, 21], [1308, 6, 980, 4, "flex"], [1308, 10, 980, 8], [1308, 12, 980, 10], [1308, 13, 980, 11], [1309, 6, 981, 4, "justifyContent"], [1309, 20, 981, 18], [1309, 22, 981, 20], [1309, 30, 981, 28], [1310, 6, 982, 4, "alignItems"], [1310, 16, 982, 14], [1310, 18, 982, 16], [1310, 26, 982, 24], [1311, 6, 983, 4, "padding"], [1311, 13, 983, 11], [1311, 15, 983, 13], [1312, 4, 984, 2], [1312, 5, 984, 3], [1313, 4, 985, 2, "permissionTitle"], [1313, 19, 985, 17], [1313, 21, 985, 19], [1314, 6, 986, 4, "fontSize"], [1314, 14, 986, 12], [1314, 16, 986, 14], [1314, 18, 986, 16], [1315, 6, 987, 4, "fontWeight"], [1315, 16, 987, 14], [1315, 18, 987, 16], [1315, 23, 987, 21], [1316, 6, 988, 4, "color"], [1316, 11, 988, 9], [1316, 13, 988, 11], [1316, 22, 988, 20], [1317, 6, 989, 4, "marginTop"], [1317, 15, 989, 13], [1317, 17, 989, 15], [1317, 19, 989, 17], [1318, 6, 990, 4, "marginBottom"], [1318, 18, 990, 16], [1318, 20, 990, 18], [1319, 4, 991, 2], [1319, 5, 991, 3], [1320, 4, 992, 2, "permissionDescription"], [1320, 25, 992, 23], [1320, 27, 992, 25], [1321, 6, 993, 4, "fontSize"], [1321, 14, 993, 12], [1321, 16, 993, 14], [1321, 18, 993, 16], [1322, 6, 994, 4, "color"], [1322, 11, 994, 9], [1322, 13, 994, 11], [1322, 22, 994, 20], [1323, 6, 995, 4, "textAlign"], [1323, 15, 995, 13], [1323, 17, 995, 15], [1323, 25, 995, 23], [1324, 6, 996, 4, "marginBottom"], [1324, 18, 996, 16], [1324, 20, 996, 18], [1325, 4, 997, 2], [1325, 5, 997, 3], [1326, 4, 998, 2, "loadingText"], [1326, 15, 998, 13], [1326, 17, 998, 15], [1327, 6, 999, 4, "color"], [1327, 11, 999, 9], [1327, 13, 999, 11], [1327, 22, 999, 20], [1328, 6, 1000, 4, "marginTop"], [1328, 15, 1000, 13], [1328, 17, 1000, 15], [1329, 4, 1001, 2], [1329, 5, 1001, 3], [1330, 4, 1002, 2], [1331, 4, 1003, 2, "blurZone"], [1331, 12, 1003, 10], [1331, 14, 1003, 12], [1332, 6, 1004, 4, "position"], [1332, 14, 1004, 12], [1332, 16, 1004, 14], [1332, 26, 1004, 24], [1333, 6, 1005, 4, "overflow"], [1333, 14, 1005, 12], [1333, 16, 1005, 14], [1334, 4, 1006, 2], [1334, 5, 1006, 3], [1335, 4, 1007, 2, "previewChip"], [1335, 15, 1007, 13], [1335, 17, 1007, 15], [1336, 6, 1008, 4, "position"], [1336, 14, 1008, 12], [1336, 16, 1008, 14], [1336, 26, 1008, 24], [1337, 6, 1009, 4, "top"], [1337, 9, 1009, 7], [1337, 11, 1009, 9], [1337, 12, 1009, 10], [1338, 6, 1010, 4, "right"], [1338, 11, 1010, 9], [1338, 13, 1010, 11], [1338, 14, 1010, 12], [1339, 6, 1011, 4, "backgroundColor"], [1339, 21, 1011, 19], [1339, 23, 1011, 21], [1339, 40, 1011, 38], [1340, 6, 1012, 4, "paddingHorizontal"], [1340, 23, 1012, 21], [1340, 25, 1012, 23], [1340, 27, 1012, 25], [1341, 6, 1013, 4, "paddingVertical"], [1341, 21, 1013, 19], [1341, 23, 1013, 21], [1341, 24, 1013, 22], [1342, 6, 1014, 4, "borderRadius"], [1342, 18, 1014, 16], [1342, 20, 1014, 18], [1343, 4, 1015, 2], [1343, 5, 1015, 3], [1344, 4, 1016, 2, "previewChipText"], [1344, 19, 1016, 17], [1344, 21, 1016, 19], [1345, 6, 1017, 4, "color"], [1345, 11, 1017, 9], [1345, 13, 1017, 11], [1345, 19, 1017, 17], [1346, 6, 1018, 4, "fontSize"], [1346, 14, 1018, 12], [1346, 16, 1018, 14], [1346, 18, 1018, 16], [1347, 6, 1019, 4, "fontWeight"], [1347, 16, 1019, 14], [1347, 18, 1019, 16], [1348, 4, 1020, 2], [1349, 2, 1021, 0], [1349, 3, 1021, 1], [1349, 4, 1021, 2], [1350, 2, 1021, 3], [1350, 6, 1021, 3, "_c"], [1350, 8, 1021, 3], [1351, 2, 1021, 3, "$RefreshReg$"], [1351, 14, 1021, 3], [1351, 15, 1021, 3, "_c"], [1351, 17, 1021, 3], [1352, 0, 1021, 3], [1352, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "capturePhoto", "Promise$argument_0", "processImageWithFaceBlur", "browserDetections.map$argument_0", "faceDetections.map$argument_0", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;mCGE;wBCc,kCD;GHoC;mCKE;wBDY;OCI;gDC8B;YDO;8BDa;aCM;6CEc;YFO;oFGsB;UHM;8BIS;SJoD;uDDU;sBMC,wBN;OCC;GLe;6BWG;GX6B;kCYG;GZ8C;4BaE;mBCmD;SDE;GbO;uBeE;GfI;mCgBG;GhBM;YCE;GDK;oBiB2C;WjBG;yBkBC;WlBG;wBmBC;WnBI;CD4L"}}, "type": "js/module"}]}