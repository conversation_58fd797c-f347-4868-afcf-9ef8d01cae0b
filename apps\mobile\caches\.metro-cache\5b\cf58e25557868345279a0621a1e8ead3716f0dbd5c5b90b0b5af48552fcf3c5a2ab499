{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.enumKey = void 0;\n  const _worklet_5910800310262_init_data = {\n    code: \"function EnumJs1(k){return k.charAt(0).toUpperCase()+k.slice(1);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\dom\\\\nodes\\\\datatypes\\\\Enum.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"EnumJs1\\\",\\\"k\\\",\\\"charAt\\\",\\\"toUpperCase\\\",\\\"slice\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/dom/nodes/datatypes/Enum.js\\\"],\\\"mappings\\\":\\\"AAAuB,QAAC,CAAAA,OAAIA,CAAAC,CAAA,EAG1B,MAAO,CAAAA,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGF,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC,CAC/C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const enumKey = exports.enumKey = function () {\n    const _e = [new global.Error(), 1, -27];\n    const EnumJs1 = function (k) {\n      return k.charAt(0).toUpperCase() + k.slice(1);\n    };\n    EnumJs1.__closure = {};\n    EnumJs1.__workletHash = 5910800310262;\n    EnumJs1.__initData = _worklet_5910800310262_init_data;\n    EnumJs1.__stackDetails = _e;\n    return EnumJs1;\n  }();\n});", "lineCount": 23, "map": [[12, 2, 1, 7], [12, 8, 1, 13, "<PERSON><PERSON><PERSON><PERSON>"], [12, 15, 1, 20], [12, 18, 1, 20, "exports"], [12, 25, 1, 20], [12, 26, 1, 20, "<PERSON><PERSON><PERSON><PERSON>"], [12, 33, 1, 20], [12, 36, 1, 23], [13, 4, 1, 23], [13, 10, 1, 23, "_e"], [13, 12, 1, 23], [13, 20, 1, 23, "global"], [13, 26, 1, 23], [13, 27, 1, 23, "Error"], [13, 32, 1, 23], [14, 4, 1, 23], [14, 10, 1, 23, "EnumJs1"], [14, 17, 1, 23], [14, 29, 1, 23, "EnumJs1"], [14, 30, 1, 23, "k"], [14, 31, 1, 24], [14, 33, 1, 28], [15, 6, 4, 2], [15, 13, 4, 9, "k"], [15, 14, 4, 10], [15, 15, 4, 11, "char<PERSON>t"], [15, 21, 4, 17], [15, 22, 4, 18], [15, 23, 4, 19], [15, 24, 4, 20], [15, 25, 4, 21, "toUpperCase"], [15, 36, 4, 32], [15, 37, 4, 33], [15, 38, 4, 34], [15, 41, 4, 37, "k"], [15, 42, 4, 38], [15, 43, 4, 39, "slice"], [15, 48, 4, 44], [15, 49, 4, 45], [15, 50, 4, 46], [15, 51, 4, 47], [16, 4, 5, 0], [16, 5, 5, 1], [17, 4, 5, 1, "EnumJs1"], [17, 11, 5, 1], [17, 12, 5, 1, "__closure"], [17, 21, 5, 1], [18, 4, 5, 1, "EnumJs1"], [18, 11, 5, 1], [18, 12, 5, 1, "__workletHash"], [18, 25, 5, 1], [19, 4, 5, 1, "EnumJs1"], [19, 11, 5, 1], [19, 12, 5, 1, "__initData"], [19, 22, 5, 1], [19, 25, 5, 1, "_worklet_5910800310262_init_data"], [19, 57, 5, 1], [20, 4, 5, 1, "EnumJs1"], [20, 11, 5, 1], [20, 12, 5, 1, "__stackDetails"], [20, 26, 5, 1], [20, 29, 5, 1, "_e"], [20, 31, 5, 1], [21, 4, 5, 1], [21, 11, 5, 1, "EnumJs1"], [21, 18, 5, 1], [22, 2, 5, 1], [22, 3, 1, 23], [22, 5, 5, 1], [23, 0, 5, 2], [23, 3]], "functionMap": {"names": ["<global>", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA,uBC;CDI"}}, "type": "js/module"}]}