{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces\n        const predictions = await model.estimateFaces(tensor, false);\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sophisticated face detection criteria\n          if (analysis.skinRatio > 0.25 && analysis.hasVariation && analysis.brightness > 0.2 && analysis.brightness < 0.8) {\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      console.log(`[EchoCameraWeb] 🎨 STARTING BLUR: region (${Math.round(x)}, ${Math.round(y)}) ${Math.round(width)}x${Math.round(height)}`);\n\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      console.log(`[EchoCameraWeb] 📐 CLAMPED REGION: (${clampedX}, ${clampedY}) ${clampedWidth}x${clampedHeight}`);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.error(`[EchoCameraWeb] ❌ INVALID BLUR REGION: width=${clampedWidth}, height=${clampedHeight}`);\n        return;\n      }\n\n      // FIRST: Add a visible red rectangle to test if coordinates are correct\n      console.log(`[EchoCameraWeb] 🔴 Adding red test rectangle to verify coordinates...`);\n      ctx.fillStyle = 'rgba(255, 0, 0, 0.5)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n\n      // Wait a moment then apply actual blur\n      setTimeout(() => {\n        console.log(`[EchoCameraWeb] 🎨 Now applying actual blur over the red rectangle...`);\n\n        // Get the region to blur\n        const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n        const data = imageData.data;\n\n        // Apply heavy pixelation\n        const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n        console.log(`[EchoCameraWeb] 🔲 Applying heavy pixelation with size: ${pixelSize}px`);\n        for (let py = 0; py < clampedHeight; py += pixelSize) {\n          for (let px = 0; px < clampedWidth; px += pixelSize) {\n            // Get average color of the block\n            let r = 0,\n              g = 0,\n              b = 0,\n              count = 0;\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                r += data[index];\n                g += data[index + 1];\n                b += data[index + 2];\n                count++;\n              }\n            }\n            if (count > 0) {\n              r = Math.floor(r / count);\n              g = Math.floor(g / count);\n              b = Math.floor(b / count);\n\n              // Apply averaged color to entire block\n              for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n                for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                  const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                  data[index] = r;\n                  data[index + 1] = g;\n                  data[index + 2] = b;\n                  // Keep original alpha\n                }\n              }\n            }\n          }\n        }\n\n        // Apply additional blur passes\n        console.log(`[EchoCameraWeb] 🌫️ Applying additional blur passes...`);\n        for (let i = 0; i < 3; i++) {\n          applySimpleBlur(data, clampedWidth, clampedHeight);\n        }\n\n        // Put the blurred data back\n        ctx.putImageData(imageData, clampedX, clampedY);\n        console.log(`[EchoCameraWeb] ✅ BLUR COMPLETE: Applied to (${clampedX}, ${clampedY}) ${clampedWidth}x${clampedHeight}`);\n      }, 100);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          await loadTensorFlowFaceDetection();\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 824,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 825,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 823,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 834,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 835,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 836,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 840,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 844,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 843,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 833,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 832,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 857,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 881,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 877,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 890,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 909,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 916,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 923,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 933,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 932,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 891,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 946,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 948,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 949,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 947,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 953,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 954,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 952,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 945,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 959,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 958,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 944,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 943,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 966,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 964,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 972,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 985,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 987,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 990,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 971,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 856,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1005,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1007,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1014,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1013,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1021,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1028,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1004,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1003,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 998,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1041,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1042,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1043,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1048,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1044,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1054,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1050,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1040,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1039,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1034,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 854,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1645, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 68, 132, 66], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [179, 43, 188, 12, "analysis"], [179, 51, 188, 20], [179, 52, 188, 21, "hasVariation"], [179, 64, 188, 33], [179, 68, 189, 12, "analysis"], [179, 76, 189, 20], [179, 77, 189, 21, "brightness"], [179, 87, 189, 31], [179, 90, 189, 34], [179, 93, 189, 37], [179, 97, 190, 12, "analysis"], [179, 105, 190, 20], [179, 106, 190, 21, "brightness"], [179, 116, 190, 31], [179, 119, 190, 34], [179, 122, 190, 37], [179, 124, 190, 39], [180, 12, 192, 10, "faces"], [180, 17, 192, 15], [180, 18, 192, 16, "push"], [180, 22, 192, 20], [180, 23, 192, 21], [181, 14, 193, 12, "boundingBox"], [181, 25, 193, 23], [181, 27, 193, 25], [182, 16, 194, 14, "xCenter"], [182, 23, 194, 21], [182, 25, 194, 23], [182, 26, 194, 24, "x"], [182, 27, 194, 25], [182, 30, 194, 28, "blockSize"], [182, 39, 194, 37], [182, 42, 194, 40], [182, 43, 194, 41], [182, 47, 194, 45, "img"], [182, 50, 194, 48], [182, 51, 194, 49, "width"], [182, 56, 194, 54], [183, 16, 195, 14, "yCenter"], [183, 23, 195, 21], [183, 25, 195, 23], [183, 26, 195, 24, "y"], [183, 27, 195, 25], [183, 30, 195, 28, "blockSize"], [183, 39, 195, 37], [183, 42, 195, 40], [183, 43, 195, 41], [183, 47, 195, 45, "img"], [183, 50, 195, 48], [183, 51, 195, 49, "height"], [183, 57, 195, 55], [184, 16, 196, 14, "width"], [184, 21, 196, 19], [184, 23, 196, 22, "blockSize"], [184, 32, 196, 31], [184, 35, 196, 34], [184, 38, 196, 37], [184, 41, 196, 41, "img"], [184, 44, 196, 44], [184, 45, 196, 45, "width"], [184, 50, 196, 50], [185, 16, 197, 14, "height"], [185, 22, 197, 20], [185, 24, 197, 23, "blockSize"], [185, 33, 197, 32], [185, 36, 197, 35], [185, 39, 197, 38], [185, 42, 197, 42, "img"], [185, 45, 197, 45], [185, 46, 197, 46, "height"], [186, 14, 198, 12], [186, 15, 198, 13], [187, 14, 199, 12, "confidence"], [187, 24, 199, 22], [187, 26, 199, 24, "analysis"], [187, 34, 199, 32], [187, 35, 199, 33, "skinRatio"], [187, 44, 199, 42], [187, 47, 199, 45, "analysis"], [187, 55, 199, 53], [187, 56, 199, 54, "variation"], [188, 12, 200, 10], [188, 13, 200, 11], [188, 14, 200, 12], [189, 12, 202, 10, "console"], [189, 19, 202, 17], [189, 20, 202, 18, "log"], [189, 23, 202, 21], [189, 24, 202, 22], [189, 71, 202, 69, "Math"], [189, 75, 202, 73], [189, 76, 202, 74, "round"], [189, 81, 202, 79], [189, 82, 202, 80, "x"], [189, 83, 202, 81], [189, 84, 202, 82], [189, 89, 202, 87, "Math"], [189, 93, 202, 91], [189, 94, 202, 92, "round"], [189, 99, 202, 97], [189, 100, 202, 98, "y"], [189, 101, 202, 99], [189, 102, 202, 100], [189, 115, 202, 113], [189, 116, 202, 114, "analysis"], [189, 124, 202, 122], [189, 125, 202, 123, "skinRatio"], [189, 134, 202, 132], [189, 137, 202, 135], [189, 140, 202, 138], [189, 142, 202, 140, "toFixed"], [189, 149, 202, 147], [189, 150, 202, 148], [189, 151, 202, 149], [189, 152, 202, 150], [189, 169, 202, 167, "analysis"], [189, 177, 202, 175], [189, 178, 202, 176, "variation"], [189, 187, 202, 185], [189, 188, 202, 186, "toFixed"], [189, 195, 202, 193], [189, 196, 202, 194], [189, 197, 202, 195], [189, 198, 202, 196], [189, 215, 202, 213, "analysis"], [189, 223, 202, 221], [189, 224, 202, 222, "brightness"], [189, 234, 202, 232], [189, 235, 202, 233, "toFixed"], [189, 242, 202, 240], [189, 243, 202, 241], [189, 244, 202, 242], [189, 245, 202, 243], [189, 247, 202, 245], [189, 248, 202, 246], [190, 10, 203, 8], [191, 8, 204, 6], [192, 6, 205, 4], [194, 6, 207, 4], [195, 6, 208, 4, "faces"], [195, 11, 208, 9], [195, 12, 208, 10, "sort"], [195, 16, 208, 14], [195, 17, 208, 15], [195, 18, 208, 16, "a"], [195, 19, 208, 17], [195, 21, 208, 19, "b"], [195, 22, 208, 20], [195, 27, 208, 25], [195, 28, 208, 26, "b"], [195, 29, 208, 27], [195, 30, 208, 28, "confidence"], [195, 40, 208, 38], [195, 44, 208, 42], [195, 45, 208, 43], [195, 50, 208, 48, "a"], [195, 51, 208, 49], [195, 52, 208, 50, "confidence"], [195, 62, 208, 60], [195, 66, 208, 64], [195, 67, 208, 65], [195, 68, 208, 66], [195, 69, 208, 67], [196, 6, 209, 4], [196, 12, 209, 10, "mergedFaces"], [196, 23, 209, 21], [196, 26, 209, 24, "mergeFaceDetections"], [196, 45, 209, 43], [196, 46, 209, 44, "faces"], [196, 51, 209, 49], [196, 52, 209, 50], [197, 6, 211, 4, "console"], [197, 13, 211, 11], [197, 14, 211, 12, "log"], [197, 17, 211, 15], [197, 18, 211, 16], [197, 61, 211, 59, "faces"], [197, 66, 211, 64], [197, 67, 211, 65, "length"], [197, 73, 211, 71], [197, 90, 211, 88, "mergedFaces"], [197, 101, 211, 99], [197, 102, 211, 100, "length"], [197, 108, 211, 106], [197, 123, 211, 121], [197, 124, 211, 122], [198, 6, 212, 4], [198, 13, 212, 11, "mergedFaces"], [198, 24, 212, 22], [198, 25, 212, 23, "slice"], [198, 30, 212, 28], [198, 31, 212, 29], [198, 32, 212, 30], [198, 34, 212, 32], [198, 35, 212, 33], [198, 36, 212, 34], [198, 37, 212, 35], [198, 38, 212, 36], [199, 4, 213, 2], [199, 5, 213, 3], [200, 4, 215, 2], [200, 10, 215, 8, "analyzeRegionForFace"], [200, 30, 215, 28], [200, 33, 215, 31, "analyzeRegionForFace"], [200, 34, 215, 32, "data"], [200, 38, 215, 55], [200, 40, 215, 57, "startX"], [200, 46, 215, 71], [200, 48, 215, 73, "startY"], [200, 54, 215, 87], [200, 56, 215, 89, "size"], [200, 60, 215, 101], [200, 62, 215, 103, "imageWidth"], [200, 72, 215, 121], [200, 74, 215, 123, "imageHeight"], [200, 85, 215, 142], [200, 90, 215, 147], [201, 6, 216, 4], [201, 10, 216, 8, "skinPixels"], [201, 20, 216, 18], [201, 23, 216, 21], [201, 24, 216, 22], [202, 6, 217, 4], [202, 10, 217, 8, "totalPixels"], [202, 21, 217, 19], [202, 24, 217, 22], [202, 25, 217, 23], [203, 6, 218, 4], [203, 10, 218, 8, "totalBrightness"], [203, 25, 218, 23], [203, 28, 218, 26], [203, 29, 218, 27], [204, 6, 219, 4], [204, 10, 219, 8, "colorVariations"], [204, 25, 219, 23], [204, 28, 219, 26], [204, 29, 219, 27], [205, 6, 220, 4], [205, 10, 220, 8, "prevR"], [205, 15, 220, 13], [205, 18, 220, 16], [205, 19, 220, 17], [206, 8, 220, 19, "prevG"], [206, 13, 220, 24], [206, 16, 220, 27], [206, 17, 220, 28], [207, 8, 220, 30, "prevB"], [207, 13, 220, 35], [207, 16, 220, 38], [207, 17, 220, 39], [208, 6, 222, 4], [208, 11, 222, 9], [208, 15, 222, 13, "y"], [208, 16, 222, 14], [208, 19, 222, 17, "startY"], [208, 25, 222, 23], [208, 27, 222, 25, "y"], [208, 28, 222, 26], [208, 31, 222, 29, "startY"], [208, 37, 222, 35], [208, 40, 222, 38, "size"], [208, 44, 222, 42], [208, 48, 222, 46, "y"], [208, 49, 222, 47], [208, 52, 222, 50, "imageHeight"], [208, 63, 222, 61], [208, 65, 222, 63, "y"], [208, 66, 222, 64], [208, 68, 222, 66], [208, 70, 222, 68], [209, 8, 223, 6], [209, 13, 223, 11], [209, 17, 223, 15, "x"], [209, 18, 223, 16], [209, 21, 223, 19, "startX"], [209, 27, 223, 25], [209, 29, 223, 27, "x"], [209, 30, 223, 28], [209, 33, 223, 31, "startX"], [209, 39, 223, 37], [209, 42, 223, 40, "size"], [209, 46, 223, 44], [209, 50, 223, 48, "x"], [209, 51, 223, 49], [209, 54, 223, 52, "imageWidth"], [209, 64, 223, 62], [209, 66, 223, 64, "x"], [209, 67, 223, 65], [209, 69, 223, 67], [209, 71, 223, 69], [210, 10, 224, 8], [210, 16, 224, 14, "index"], [210, 21, 224, 19], [210, 24, 224, 22], [210, 25, 224, 23, "y"], [210, 26, 224, 24], [210, 29, 224, 27, "imageWidth"], [210, 39, 224, 37], [210, 42, 224, 40, "x"], [210, 43, 224, 41], [210, 47, 224, 45], [210, 48, 224, 46], [211, 10, 225, 8], [211, 16, 225, 14, "r"], [211, 17, 225, 15], [211, 20, 225, 18, "data"], [211, 24, 225, 22], [211, 25, 225, 23, "index"], [211, 30, 225, 28], [211, 31, 225, 29], [212, 10, 226, 8], [212, 16, 226, 14, "g"], [212, 17, 226, 15], [212, 20, 226, 18, "data"], [212, 24, 226, 22], [212, 25, 226, 23, "index"], [212, 30, 226, 28], [212, 33, 226, 31], [212, 34, 226, 32], [212, 35, 226, 33], [213, 10, 227, 8], [213, 16, 227, 14, "b"], [213, 17, 227, 15], [213, 20, 227, 18, "data"], [213, 24, 227, 22], [213, 25, 227, 23, "index"], [213, 30, 227, 28], [213, 33, 227, 31], [213, 34, 227, 32], [213, 35, 227, 33], [215, 10, 229, 8], [216, 10, 230, 8], [216, 14, 230, 12, "isSkinTone"], [216, 24, 230, 22], [216, 25, 230, 23, "r"], [216, 26, 230, 24], [216, 28, 230, 26, "g"], [216, 29, 230, 27], [216, 31, 230, 29, "b"], [216, 32, 230, 30], [216, 33, 230, 31], [216, 35, 230, 33], [217, 12, 231, 10, "skinPixels"], [217, 22, 231, 20], [217, 24, 231, 22], [218, 10, 232, 8], [220, 10, 234, 8], [221, 10, 235, 8], [221, 16, 235, 14, "brightness"], [221, 26, 235, 24], [221, 29, 235, 27], [221, 30, 235, 28, "r"], [221, 31, 235, 29], [221, 34, 235, 32, "g"], [221, 35, 235, 33], [221, 38, 235, 36, "b"], [221, 39, 235, 37], [221, 44, 235, 42], [221, 45, 235, 43], [221, 48, 235, 46], [221, 51, 235, 49], [221, 52, 235, 50], [222, 10, 236, 8, "totalBrightness"], [222, 25, 236, 23], [222, 29, 236, 27, "brightness"], [222, 39, 236, 37], [224, 10, 238, 8], [225, 10, 239, 8], [225, 14, 239, 12, "totalPixels"], [225, 25, 239, 23], [225, 28, 239, 26], [225, 29, 239, 27], [225, 31, 239, 29], [226, 12, 240, 10], [226, 18, 240, 16, "colorDiff"], [226, 27, 240, 25], [226, 30, 240, 28, "Math"], [226, 34, 240, 32], [226, 35, 240, 33, "abs"], [226, 38, 240, 36], [226, 39, 240, 37, "r"], [226, 40, 240, 38], [226, 43, 240, 41, "prevR"], [226, 48, 240, 46], [226, 49, 240, 47], [226, 52, 240, 50, "Math"], [226, 56, 240, 54], [226, 57, 240, 55, "abs"], [226, 60, 240, 58], [226, 61, 240, 59, "g"], [226, 62, 240, 60], [226, 65, 240, 63, "prevG"], [226, 70, 240, 68], [226, 71, 240, 69], [226, 74, 240, 72, "Math"], [226, 78, 240, 76], [226, 79, 240, 77, "abs"], [226, 82, 240, 80], [226, 83, 240, 81, "b"], [226, 84, 240, 82], [226, 87, 240, 85, "prevB"], [226, 92, 240, 90], [226, 93, 240, 91], [227, 12, 241, 10], [227, 16, 241, 14, "colorDiff"], [227, 25, 241, 23], [227, 28, 241, 26], [227, 30, 241, 28], [227, 32, 241, 30], [228, 14, 241, 32], [229, 14, 242, 12, "colorVariations"], [229, 29, 242, 27], [229, 31, 242, 29], [230, 12, 243, 10], [231, 10, 244, 8], [232, 10, 246, 8, "prevR"], [232, 15, 246, 13], [232, 18, 246, 16, "r"], [232, 19, 246, 17], [233, 10, 246, 19, "prevG"], [233, 15, 246, 24], [233, 18, 246, 27, "g"], [233, 19, 246, 28], [234, 10, 246, 30, "prevB"], [234, 15, 246, 35], [234, 18, 246, 38, "b"], [234, 19, 246, 39], [235, 10, 247, 8, "totalPixels"], [235, 21, 247, 19], [235, 23, 247, 21], [236, 8, 248, 6], [237, 6, 249, 4], [238, 6, 251, 4], [238, 13, 251, 11], [239, 8, 252, 6, "skinRatio"], [239, 17, 252, 15], [239, 19, 252, 17, "skinPixels"], [239, 29, 252, 27], [239, 32, 252, 30, "totalPixels"], [239, 43, 252, 41], [240, 8, 253, 6, "brightness"], [240, 18, 253, 16], [240, 20, 253, 18, "totalBrightness"], [240, 35, 253, 33], [240, 38, 253, 36, "totalPixels"], [240, 49, 253, 47], [241, 8, 254, 6, "variation"], [241, 17, 254, 15], [241, 19, 254, 17, "colorVariations"], [241, 34, 254, 32], [241, 37, 254, 35, "totalPixels"], [241, 48, 254, 46], [242, 8, 255, 6, "hasVariation"], [242, 20, 255, 18], [242, 22, 255, 20, "colorVariations"], [242, 37, 255, 35], [242, 40, 255, 38, "totalPixels"], [242, 51, 255, 49], [242, 54, 255, 52], [242, 57, 255, 55], [242, 58, 255, 56], [243, 6, 256, 4], [243, 7, 256, 5], [244, 4, 257, 2], [244, 5, 257, 3], [245, 4, 259, 2], [245, 10, 259, 8, "isSkinTone"], [245, 20, 259, 18], [245, 23, 259, 21, "isSkinTone"], [245, 24, 259, 22, "r"], [245, 25, 259, 31], [245, 27, 259, 33, "g"], [245, 28, 259, 42], [245, 30, 259, 44, "b"], [245, 31, 259, 53], [245, 36, 259, 58], [246, 6, 260, 4], [247, 6, 261, 4], [247, 13, 262, 6, "r"], [247, 14, 262, 7], [247, 17, 262, 10], [247, 19, 262, 12], [247, 23, 262, 16, "g"], [247, 24, 262, 17], [247, 27, 262, 20], [247, 29, 262, 22], [247, 33, 262, 26, "b"], [247, 34, 262, 27], [247, 37, 262, 30], [247, 39, 262, 32], [247, 43, 263, 6, "r"], [247, 44, 263, 7], [247, 47, 263, 10, "g"], [247, 48, 263, 11], [247, 52, 263, 15, "r"], [247, 53, 263, 16], [247, 56, 263, 19, "b"], [247, 57, 263, 20], [247, 61, 264, 6, "Math"], [247, 65, 264, 10], [247, 66, 264, 11, "abs"], [247, 69, 264, 14], [247, 70, 264, 15, "r"], [247, 71, 264, 16], [247, 74, 264, 19, "g"], [247, 75, 264, 20], [247, 76, 264, 21], [247, 79, 264, 24], [247, 81, 264, 26], [247, 85, 265, 6, "Math"], [247, 89, 265, 10], [247, 90, 265, 11, "max"], [247, 93, 265, 14], [247, 94, 265, 15, "r"], [247, 95, 265, 16], [247, 97, 265, 18, "g"], [247, 98, 265, 19], [247, 100, 265, 21, "b"], [247, 101, 265, 22], [247, 102, 265, 23], [247, 105, 265, 26, "Math"], [247, 109, 265, 30], [247, 110, 265, 31, "min"], [247, 113, 265, 34], [247, 114, 265, 35, "r"], [247, 115, 265, 36], [247, 117, 265, 38, "g"], [247, 118, 265, 39], [247, 120, 265, 41, "b"], [247, 121, 265, 42], [247, 122, 265, 43], [247, 125, 265, 46], [247, 127, 265, 48], [248, 4, 267, 2], [248, 5, 267, 3], [249, 4, 269, 2], [249, 10, 269, 8, "mergeFaceDetections"], [249, 29, 269, 27], [249, 32, 269, 31, "faces"], [249, 37, 269, 43], [249, 41, 269, 48], [250, 6, 270, 4], [250, 10, 270, 8, "faces"], [250, 15, 270, 13], [250, 16, 270, 14, "length"], [250, 22, 270, 20], [250, 26, 270, 24], [250, 27, 270, 25], [250, 29, 270, 27], [250, 36, 270, 34, "faces"], [250, 41, 270, 39], [251, 6, 272, 4], [251, 12, 272, 10, "merged"], [251, 18, 272, 16], [251, 21, 272, 19], [251, 23, 272, 21], [252, 6, 273, 4], [252, 12, 273, 10, "used"], [252, 16, 273, 14], [252, 19, 273, 17], [252, 23, 273, 21, "Set"], [252, 26, 273, 24], [252, 27, 273, 25], [252, 28, 273, 26], [253, 6, 275, 4], [253, 11, 275, 9], [253, 15, 275, 13, "i"], [253, 16, 275, 14], [253, 19, 275, 17], [253, 20, 275, 18], [253, 22, 275, 20, "i"], [253, 23, 275, 21], [253, 26, 275, 24, "faces"], [253, 31, 275, 29], [253, 32, 275, 30, "length"], [253, 38, 275, 36], [253, 40, 275, 38, "i"], [253, 41, 275, 39], [253, 43, 275, 41], [253, 45, 275, 43], [254, 8, 276, 6], [254, 12, 276, 10, "used"], [254, 16, 276, 14], [254, 17, 276, 15, "has"], [254, 20, 276, 18], [254, 21, 276, 19, "i"], [254, 22, 276, 20], [254, 23, 276, 21], [254, 25, 276, 23], [255, 8, 278, 6], [255, 12, 278, 10, "currentFace"], [255, 23, 278, 21], [255, 26, 278, 24, "faces"], [255, 31, 278, 29], [255, 32, 278, 30, "i"], [255, 33, 278, 31], [255, 34, 278, 32], [256, 8, 279, 6, "used"], [256, 12, 279, 10], [256, 13, 279, 11, "add"], [256, 16, 279, 14], [256, 17, 279, 15, "i"], [256, 18, 279, 16], [256, 19, 279, 17], [258, 8, 281, 6], [259, 8, 282, 6], [259, 13, 282, 11], [259, 17, 282, 15, "j"], [259, 18, 282, 16], [259, 21, 282, 19, "i"], [259, 22, 282, 20], [259, 25, 282, 23], [259, 26, 282, 24], [259, 28, 282, 26, "j"], [259, 29, 282, 27], [259, 32, 282, 30, "faces"], [259, 37, 282, 35], [259, 38, 282, 36, "length"], [259, 44, 282, 42], [259, 46, 282, 44, "j"], [259, 47, 282, 45], [259, 49, 282, 47], [259, 51, 282, 49], [260, 10, 283, 8], [260, 14, 283, 12, "used"], [260, 18, 283, 16], [260, 19, 283, 17, "has"], [260, 22, 283, 20], [260, 23, 283, 21, "j"], [260, 24, 283, 22], [260, 25, 283, 23], [260, 27, 283, 25], [261, 10, 285, 8], [261, 16, 285, 14, "overlap"], [261, 23, 285, 21], [261, 26, 285, 24, "calculateOverlap"], [261, 42, 285, 40], [261, 43, 285, 41, "currentFace"], [261, 54, 285, 52], [261, 55, 285, 53, "boundingBox"], [261, 66, 285, 64], [261, 68, 285, 66, "faces"], [261, 73, 285, 71], [261, 74, 285, 72, "j"], [261, 75, 285, 73], [261, 76, 285, 74], [261, 77, 285, 75, "boundingBox"], [261, 88, 285, 86], [261, 89, 285, 87], [262, 10, 286, 8], [262, 14, 286, 12, "overlap"], [262, 21, 286, 19], [262, 24, 286, 22], [262, 27, 286, 25], [262, 29, 286, 27], [263, 12, 286, 29], [264, 12, 287, 10], [265, 12, 288, 10, "currentFace"], [265, 23, 288, 21], [265, 26, 288, 24, "mergeTwoFaces"], [265, 39, 288, 37], [265, 40, 288, 38, "currentFace"], [265, 51, 288, 49], [265, 53, 288, 51, "faces"], [265, 58, 288, 56], [265, 59, 288, 57, "j"], [265, 60, 288, 58], [265, 61, 288, 59], [265, 62, 288, 60], [266, 12, 289, 10, "used"], [266, 16, 289, 14], [266, 17, 289, 15, "add"], [266, 20, 289, 18], [266, 21, 289, 19, "j"], [266, 22, 289, 20], [266, 23, 289, 21], [267, 10, 290, 8], [268, 8, 291, 6], [269, 8, 293, 6, "merged"], [269, 14, 293, 12], [269, 15, 293, 13, "push"], [269, 19, 293, 17], [269, 20, 293, 18, "currentFace"], [269, 31, 293, 29], [269, 32, 293, 30], [270, 6, 294, 4], [271, 6, 296, 4], [271, 13, 296, 11, "merged"], [271, 19, 296, 17], [272, 4, 297, 2], [272, 5, 297, 3], [273, 4, 299, 2], [273, 10, 299, 8, "calculateOverlap"], [273, 26, 299, 24], [273, 29, 299, 27, "calculateOverlap"], [273, 30, 299, 28, "box1"], [273, 34, 299, 37], [273, 36, 299, 39, "box2"], [273, 40, 299, 48], [273, 45, 299, 53], [274, 6, 300, 4], [274, 12, 300, 10, "x1"], [274, 14, 300, 12], [274, 17, 300, 15, "Math"], [274, 21, 300, 19], [274, 22, 300, 20, "max"], [274, 25, 300, 23], [274, 26, 300, 24, "box1"], [274, 30, 300, 28], [274, 31, 300, 29, "xCenter"], [274, 38, 300, 36], [274, 41, 300, 39, "box1"], [274, 45, 300, 43], [274, 46, 300, 44, "width"], [274, 51, 300, 49], [274, 54, 300, 50], [274, 55, 300, 51], [274, 57, 300, 53, "box2"], [274, 61, 300, 57], [274, 62, 300, 58, "xCenter"], [274, 69, 300, 65], [274, 72, 300, 68, "box2"], [274, 76, 300, 72], [274, 77, 300, 73, "width"], [274, 82, 300, 78], [274, 85, 300, 79], [274, 86, 300, 80], [274, 87, 300, 81], [275, 6, 301, 4], [275, 12, 301, 10, "y1"], [275, 14, 301, 12], [275, 17, 301, 15, "Math"], [275, 21, 301, 19], [275, 22, 301, 20, "max"], [275, 25, 301, 23], [275, 26, 301, 24, "box1"], [275, 30, 301, 28], [275, 31, 301, 29, "yCenter"], [275, 38, 301, 36], [275, 41, 301, 39, "box1"], [275, 45, 301, 43], [275, 46, 301, 44, "height"], [275, 52, 301, 50], [275, 55, 301, 51], [275, 56, 301, 52], [275, 58, 301, 54, "box2"], [275, 62, 301, 58], [275, 63, 301, 59, "yCenter"], [275, 70, 301, 66], [275, 73, 301, 69, "box2"], [275, 77, 301, 73], [275, 78, 301, 74, "height"], [275, 84, 301, 80], [275, 87, 301, 81], [275, 88, 301, 82], [275, 89, 301, 83], [276, 6, 302, 4], [276, 12, 302, 10, "x2"], [276, 14, 302, 12], [276, 17, 302, 15, "Math"], [276, 21, 302, 19], [276, 22, 302, 20, "min"], [276, 25, 302, 23], [276, 26, 302, 24, "box1"], [276, 30, 302, 28], [276, 31, 302, 29, "xCenter"], [276, 38, 302, 36], [276, 41, 302, 39, "box1"], [276, 45, 302, 43], [276, 46, 302, 44, "width"], [276, 51, 302, 49], [276, 54, 302, 50], [276, 55, 302, 51], [276, 57, 302, 53, "box2"], [276, 61, 302, 57], [276, 62, 302, 58, "xCenter"], [276, 69, 302, 65], [276, 72, 302, 68, "box2"], [276, 76, 302, 72], [276, 77, 302, 73, "width"], [276, 82, 302, 78], [276, 85, 302, 79], [276, 86, 302, 80], [276, 87, 302, 81], [277, 6, 303, 4], [277, 12, 303, 10, "y2"], [277, 14, 303, 12], [277, 17, 303, 15, "Math"], [277, 21, 303, 19], [277, 22, 303, 20, "min"], [277, 25, 303, 23], [277, 26, 303, 24, "box1"], [277, 30, 303, 28], [277, 31, 303, 29, "yCenter"], [277, 38, 303, 36], [277, 41, 303, 39, "box1"], [277, 45, 303, 43], [277, 46, 303, 44, "height"], [277, 52, 303, 50], [277, 55, 303, 51], [277, 56, 303, 52], [277, 58, 303, 54, "box2"], [277, 62, 303, 58], [277, 63, 303, 59, "yCenter"], [277, 70, 303, 66], [277, 73, 303, 69, "box2"], [277, 77, 303, 73], [277, 78, 303, 74, "height"], [277, 84, 303, 80], [277, 87, 303, 81], [277, 88, 303, 82], [277, 89, 303, 83], [278, 6, 305, 4], [278, 10, 305, 8, "x2"], [278, 12, 305, 10], [278, 16, 305, 14, "x1"], [278, 18, 305, 16], [278, 22, 305, 20, "y2"], [278, 24, 305, 22], [278, 28, 305, 26, "y1"], [278, 30, 305, 28], [278, 32, 305, 30], [278, 39, 305, 37], [278, 40, 305, 38], [279, 6, 307, 4], [279, 12, 307, 10, "overlapArea"], [279, 23, 307, 21], [279, 26, 307, 24], [279, 27, 307, 25, "x2"], [279, 29, 307, 27], [279, 32, 307, 30, "x1"], [279, 34, 307, 32], [279, 39, 307, 37, "y2"], [279, 41, 307, 39], [279, 44, 307, 42, "y1"], [279, 46, 307, 44], [279, 47, 307, 45], [280, 6, 308, 4], [280, 12, 308, 10, "box1Area"], [280, 20, 308, 18], [280, 23, 308, 21, "box1"], [280, 27, 308, 25], [280, 28, 308, 26, "width"], [280, 33, 308, 31], [280, 36, 308, 34, "box1"], [280, 40, 308, 38], [280, 41, 308, 39, "height"], [280, 47, 308, 45], [281, 6, 309, 4], [281, 12, 309, 10, "box2Area"], [281, 20, 309, 18], [281, 23, 309, 21, "box2"], [281, 27, 309, 25], [281, 28, 309, 26, "width"], [281, 33, 309, 31], [281, 36, 309, 34, "box2"], [281, 40, 309, 38], [281, 41, 309, 39, "height"], [281, 47, 309, 45], [282, 6, 311, 4], [282, 13, 311, 11, "overlapArea"], [282, 24, 311, 22], [282, 27, 311, 25, "Math"], [282, 31, 311, 29], [282, 32, 311, 30, "min"], [282, 35, 311, 33], [282, 36, 311, 34, "box1Area"], [282, 44, 311, 42], [282, 46, 311, 44, "box2Area"], [282, 54, 311, 52], [282, 55, 311, 53], [283, 4, 312, 2], [283, 5, 312, 3], [284, 4, 314, 2], [284, 10, 314, 8, "mergeTwoFaces"], [284, 23, 314, 21], [284, 26, 314, 24, "mergeTwoFaces"], [284, 27, 314, 25, "face1"], [284, 32, 314, 35], [284, 34, 314, 37, "face2"], [284, 39, 314, 47], [284, 44, 314, 52], [285, 6, 315, 4], [285, 12, 315, 10, "box1"], [285, 16, 315, 14], [285, 19, 315, 17, "face1"], [285, 24, 315, 22], [285, 25, 315, 23, "boundingBox"], [285, 36, 315, 34], [286, 6, 316, 4], [286, 12, 316, 10, "box2"], [286, 16, 316, 14], [286, 19, 316, 17, "face2"], [286, 24, 316, 22], [286, 25, 316, 23, "boundingBox"], [286, 36, 316, 34], [287, 6, 318, 4], [287, 12, 318, 10, "left"], [287, 16, 318, 14], [287, 19, 318, 17, "Math"], [287, 23, 318, 21], [287, 24, 318, 22, "min"], [287, 27, 318, 25], [287, 28, 318, 26, "box1"], [287, 32, 318, 30], [287, 33, 318, 31, "xCenter"], [287, 40, 318, 38], [287, 43, 318, 41, "box1"], [287, 47, 318, 45], [287, 48, 318, 46, "width"], [287, 53, 318, 51], [287, 56, 318, 52], [287, 57, 318, 53], [287, 59, 318, 55, "box2"], [287, 63, 318, 59], [287, 64, 318, 60, "xCenter"], [287, 71, 318, 67], [287, 74, 318, 70, "box2"], [287, 78, 318, 74], [287, 79, 318, 75, "width"], [287, 84, 318, 80], [287, 87, 318, 81], [287, 88, 318, 82], [287, 89, 318, 83], [288, 6, 319, 4], [288, 12, 319, 10, "right"], [288, 17, 319, 15], [288, 20, 319, 18, "Math"], [288, 24, 319, 22], [288, 25, 319, 23, "max"], [288, 28, 319, 26], [288, 29, 319, 27, "box1"], [288, 33, 319, 31], [288, 34, 319, 32, "xCenter"], [288, 41, 319, 39], [288, 44, 319, 42, "box1"], [288, 48, 319, 46], [288, 49, 319, 47, "width"], [288, 54, 319, 52], [288, 57, 319, 53], [288, 58, 319, 54], [288, 60, 319, 56, "box2"], [288, 64, 319, 60], [288, 65, 319, 61, "xCenter"], [288, 72, 319, 68], [288, 75, 319, 71, "box2"], [288, 79, 319, 75], [288, 80, 319, 76, "width"], [288, 85, 319, 81], [288, 88, 319, 82], [288, 89, 319, 83], [288, 90, 319, 84], [289, 6, 320, 4], [289, 12, 320, 10, "top"], [289, 15, 320, 13], [289, 18, 320, 16, "Math"], [289, 22, 320, 20], [289, 23, 320, 21, "min"], [289, 26, 320, 24], [289, 27, 320, 25, "box1"], [289, 31, 320, 29], [289, 32, 320, 30, "yCenter"], [289, 39, 320, 37], [289, 42, 320, 40, "box1"], [289, 46, 320, 44], [289, 47, 320, 45, "height"], [289, 53, 320, 51], [289, 56, 320, 52], [289, 57, 320, 53], [289, 59, 320, 55, "box2"], [289, 63, 320, 59], [289, 64, 320, 60, "yCenter"], [289, 71, 320, 67], [289, 74, 320, 70, "box2"], [289, 78, 320, 74], [289, 79, 320, 75, "height"], [289, 85, 320, 81], [289, 88, 320, 82], [289, 89, 320, 83], [289, 90, 320, 84], [290, 6, 321, 4], [290, 12, 321, 10, "bottom"], [290, 18, 321, 16], [290, 21, 321, 19, "Math"], [290, 25, 321, 23], [290, 26, 321, 24, "max"], [290, 29, 321, 27], [290, 30, 321, 28, "box1"], [290, 34, 321, 32], [290, 35, 321, 33, "yCenter"], [290, 42, 321, 40], [290, 45, 321, 43, "box1"], [290, 49, 321, 47], [290, 50, 321, 48, "height"], [290, 56, 321, 54], [290, 59, 321, 55], [290, 60, 321, 56], [290, 62, 321, 58, "box2"], [290, 66, 321, 62], [290, 67, 321, 63, "yCenter"], [290, 74, 321, 70], [290, 77, 321, 73, "box2"], [290, 81, 321, 77], [290, 82, 321, 78, "height"], [290, 88, 321, 84], [290, 91, 321, 85], [290, 92, 321, 86], [290, 93, 321, 87], [291, 6, 323, 4], [291, 13, 323, 11], [292, 8, 324, 6, "boundingBox"], [292, 19, 324, 17], [292, 21, 324, 19], [293, 10, 325, 8, "xCenter"], [293, 17, 325, 15], [293, 19, 325, 17], [293, 20, 325, 18, "left"], [293, 24, 325, 22], [293, 27, 325, 25, "right"], [293, 32, 325, 30], [293, 36, 325, 34], [293, 37, 325, 35], [294, 10, 326, 8, "yCenter"], [294, 17, 326, 15], [294, 19, 326, 17], [294, 20, 326, 18, "top"], [294, 23, 326, 21], [294, 26, 326, 24, "bottom"], [294, 32, 326, 30], [294, 36, 326, 34], [294, 37, 326, 35], [295, 10, 327, 8, "width"], [295, 15, 327, 13], [295, 17, 327, 15, "right"], [295, 22, 327, 20], [295, 25, 327, 23, "left"], [295, 29, 327, 27], [296, 10, 328, 8, "height"], [296, 16, 328, 14], [296, 18, 328, 16, "bottom"], [296, 24, 328, 22], [296, 27, 328, 25, "top"], [297, 8, 329, 6], [298, 6, 330, 4], [298, 7, 330, 5], [299, 4, 331, 2], [299, 5, 331, 3], [301, 4, 333, 2], [302, 4, 334, 2], [302, 10, 334, 8, "applyStrongBlur"], [302, 25, 334, 23], [302, 28, 334, 26, "applyStrongBlur"], [302, 29, 334, 27, "ctx"], [302, 32, 334, 56], [302, 34, 334, 58, "x"], [302, 35, 334, 67], [302, 37, 334, 69, "y"], [302, 38, 334, 78], [302, 40, 334, 80, "width"], [302, 45, 334, 93], [302, 47, 334, 95, "height"], [302, 53, 334, 109], [302, 58, 334, 114], [303, 6, 335, 4, "console"], [303, 13, 335, 11], [303, 14, 335, 12, "log"], [303, 17, 335, 15], [303, 18, 335, 16], [303, 63, 335, 61, "Math"], [303, 67, 335, 65], [303, 68, 335, 66, "round"], [303, 73, 335, 71], [303, 74, 335, 72, "x"], [303, 75, 335, 73], [303, 76, 335, 74], [303, 81, 335, 79, "Math"], [303, 85, 335, 83], [303, 86, 335, 84, "round"], [303, 91, 335, 89], [303, 92, 335, 90, "y"], [303, 93, 335, 91], [303, 94, 335, 92], [303, 99, 335, 97, "Math"], [303, 103, 335, 101], [303, 104, 335, 102, "round"], [303, 109, 335, 107], [303, 110, 335, 108, "width"], [303, 115, 335, 113], [303, 116, 335, 114], [303, 120, 335, 118, "Math"], [303, 124, 335, 122], [303, 125, 335, 123, "round"], [303, 130, 335, 128], [303, 131, 335, 129, "height"], [303, 137, 335, 135], [303, 138, 335, 136], [303, 140, 335, 138], [303, 141, 335, 139], [305, 6, 337, 4], [306, 6, 338, 4], [306, 12, 338, 10, "canvasWidth"], [306, 23, 338, 21], [306, 26, 338, 24, "ctx"], [306, 29, 338, 27], [306, 30, 338, 28, "canvas"], [306, 36, 338, 34], [306, 37, 338, 35, "width"], [306, 42, 338, 40], [307, 6, 339, 4], [307, 12, 339, 10, "canvasHeight"], [307, 24, 339, 22], [307, 27, 339, 25, "ctx"], [307, 30, 339, 28], [307, 31, 339, 29, "canvas"], [307, 37, 339, 35], [307, 38, 339, 36, "height"], [307, 44, 339, 42], [308, 6, 341, 4], [308, 12, 341, 10, "clampedX"], [308, 20, 341, 18], [308, 23, 341, 21, "Math"], [308, 27, 341, 25], [308, 28, 341, 26, "max"], [308, 31, 341, 29], [308, 32, 341, 30], [308, 33, 341, 31], [308, 35, 341, 33, "Math"], [308, 39, 341, 37], [308, 40, 341, 38, "min"], [308, 43, 341, 41], [308, 44, 341, 42, "Math"], [308, 48, 341, 46], [308, 49, 341, 47, "floor"], [308, 54, 341, 52], [308, 55, 341, 53, "x"], [308, 56, 341, 54], [308, 57, 341, 55], [308, 59, 341, 57, "canvasWidth"], [308, 70, 341, 68], [308, 73, 341, 71], [308, 74, 341, 72], [308, 75, 341, 73], [308, 76, 341, 74], [309, 6, 342, 4], [309, 12, 342, 10, "clampedY"], [309, 20, 342, 18], [309, 23, 342, 21, "Math"], [309, 27, 342, 25], [309, 28, 342, 26, "max"], [309, 31, 342, 29], [309, 32, 342, 30], [309, 33, 342, 31], [309, 35, 342, 33, "Math"], [309, 39, 342, 37], [309, 40, 342, 38, "min"], [309, 43, 342, 41], [309, 44, 342, 42, "Math"], [309, 48, 342, 46], [309, 49, 342, 47, "floor"], [309, 54, 342, 52], [309, 55, 342, 53, "y"], [309, 56, 342, 54], [309, 57, 342, 55], [309, 59, 342, 57, "canvasHeight"], [309, 71, 342, 69], [309, 74, 342, 72], [309, 75, 342, 73], [309, 76, 342, 74], [309, 77, 342, 75], [310, 6, 343, 4], [310, 12, 343, 10, "<PERSON><PERSON><PERSON><PERSON>"], [310, 24, 343, 22], [310, 27, 343, 25, "Math"], [310, 31, 343, 29], [310, 32, 343, 30, "min"], [310, 35, 343, 33], [310, 36, 343, 34, "Math"], [310, 40, 343, 38], [310, 41, 343, 39, "floor"], [310, 46, 343, 44], [310, 47, 343, 45, "width"], [310, 52, 343, 50], [310, 53, 343, 51], [310, 55, 343, 53, "canvasWidth"], [310, 66, 343, 64], [310, 69, 343, 67, "clampedX"], [310, 77, 343, 75], [310, 78, 343, 76], [311, 6, 344, 4], [311, 12, 344, 10, "clampedHeight"], [311, 25, 344, 23], [311, 28, 344, 26, "Math"], [311, 32, 344, 30], [311, 33, 344, 31, "min"], [311, 36, 344, 34], [311, 37, 344, 35, "Math"], [311, 41, 344, 39], [311, 42, 344, 40, "floor"], [311, 47, 344, 45], [311, 48, 344, 46, "height"], [311, 54, 344, 52], [311, 55, 344, 53], [311, 57, 344, 55, "canvasHeight"], [311, 69, 344, 67], [311, 72, 344, 70, "clampedY"], [311, 80, 344, 78], [311, 81, 344, 79], [312, 6, 346, 4, "console"], [312, 13, 346, 11], [312, 14, 346, 12, "log"], [312, 17, 346, 15], [312, 18, 346, 16], [312, 57, 346, 55, "clampedX"], [312, 65, 346, 63], [312, 70, 346, 68, "clampedY"], [312, 78, 346, 76], [312, 83, 346, 81, "<PERSON><PERSON><PERSON><PERSON>"], [312, 95, 346, 93], [312, 99, 346, 97, "clampedHeight"], [312, 112, 346, 110], [312, 114, 346, 112], [312, 115, 346, 113], [313, 6, 348, 4], [313, 10, 348, 8, "<PERSON><PERSON><PERSON><PERSON>"], [313, 22, 348, 20], [313, 26, 348, 24], [313, 27, 348, 25], [313, 31, 348, 29, "clampedHeight"], [313, 44, 348, 42], [313, 48, 348, 46], [313, 49, 348, 47], [313, 51, 348, 49], [314, 8, 349, 6, "console"], [314, 15, 349, 13], [314, 16, 349, 14, "error"], [314, 21, 349, 19], [314, 22, 349, 20], [314, 70, 349, 68, "<PERSON><PERSON><PERSON><PERSON>"], [314, 82, 349, 80], [314, 94, 349, 92, "clampedHeight"], [314, 107, 349, 105], [314, 109, 349, 107], [314, 110, 349, 108], [315, 8, 350, 6], [316, 6, 351, 4], [318, 6, 353, 4], [319, 6, 354, 4, "console"], [319, 13, 354, 11], [319, 14, 354, 12, "log"], [319, 17, 354, 15], [319, 18, 354, 16], [319, 89, 354, 87], [319, 90, 354, 88], [320, 6, 355, 4, "ctx"], [320, 9, 355, 7], [320, 10, 355, 8, "fillStyle"], [320, 19, 355, 17], [320, 22, 355, 20], [320, 44, 355, 42], [321, 6, 356, 4, "ctx"], [321, 9, 356, 7], [321, 10, 356, 8, "fillRect"], [321, 18, 356, 16], [321, 19, 356, 17, "clampedX"], [321, 27, 356, 25], [321, 29, 356, 27, "clampedY"], [321, 37, 356, 35], [321, 39, 356, 37, "<PERSON><PERSON><PERSON><PERSON>"], [321, 51, 356, 49], [321, 53, 356, 51, "clampedHeight"], [321, 66, 356, 64], [321, 67, 356, 65], [323, 6, 358, 4], [324, 6, 359, 4, "setTimeout"], [324, 16, 359, 14], [324, 17, 359, 15], [324, 23, 359, 21], [325, 8, 360, 6, "console"], [325, 15, 360, 13], [325, 16, 360, 14, "log"], [325, 19, 360, 17], [325, 20, 360, 18], [325, 91, 360, 89], [325, 92, 360, 90], [327, 8, 362, 6], [328, 8, 363, 6], [328, 14, 363, 12, "imageData"], [328, 23, 363, 21], [328, 26, 363, 24, "ctx"], [328, 29, 363, 27], [328, 30, 363, 28, "getImageData"], [328, 42, 363, 40], [328, 43, 363, 41, "clampedX"], [328, 51, 363, 49], [328, 53, 363, 51, "clampedY"], [328, 61, 363, 59], [328, 63, 363, 61, "<PERSON><PERSON><PERSON><PERSON>"], [328, 75, 363, 73], [328, 77, 363, 75, "clampedHeight"], [328, 90, 363, 88], [328, 91, 363, 89], [329, 8, 364, 6], [329, 14, 364, 12, "data"], [329, 18, 364, 16], [329, 21, 364, 19, "imageData"], [329, 30, 364, 28], [329, 31, 364, 29, "data"], [329, 35, 364, 33], [331, 8, 366, 6], [332, 8, 367, 6], [332, 14, 367, 12, "pixelSize"], [332, 23, 367, 21], [332, 26, 367, 24, "Math"], [332, 30, 367, 28], [332, 31, 367, 29, "max"], [332, 34, 367, 32], [332, 35, 367, 33], [332, 37, 367, 35], [332, 39, 367, 37, "Math"], [332, 43, 367, 41], [332, 44, 367, 42, "min"], [332, 47, 367, 45], [332, 48, 367, 46, "<PERSON><PERSON><PERSON><PERSON>"], [332, 60, 367, 58], [332, 62, 367, 60, "clampedHeight"], [332, 75, 367, 73], [332, 76, 367, 74], [332, 79, 367, 77], [332, 80, 367, 78], [332, 81, 367, 79], [333, 8, 368, 6, "console"], [333, 15, 368, 13], [333, 16, 368, 14, "log"], [333, 19, 368, 17], [333, 20, 368, 18], [333, 79, 368, 77, "pixelSize"], [333, 88, 368, 86], [333, 92, 368, 90], [333, 93, 368, 91], [334, 8, 370, 6], [334, 13, 370, 11], [334, 17, 370, 15, "py"], [334, 19, 370, 17], [334, 22, 370, 20], [334, 23, 370, 21], [334, 25, 370, 23, "py"], [334, 27, 370, 25], [334, 30, 370, 28, "clampedHeight"], [334, 43, 370, 41], [334, 45, 370, 43, "py"], [334, 47, 370, 45], [334, 51, 370, 49, "pixelSize"], [334, 60, 370, 58], [334, 62, 370, 60], [335, 10, 371, 8], [335, 15, 371, 13], [335, 19, 371, 17, "px"], [335, 21, 371, 19], [335, 24, 371, 22], [335, 25, 371, 23], [335, 27, 371, 25, "px"], [335, 29, 371, 27], [335, 32, 371, 30, "<PERSON><PERSON><PERSON><PERSON>"], [335, 44, 371, 42], [335, 46, 371, 44, "px"], [335, 48, 371, 46], [335, 52, 371, 50, "pixelSize"], [335, 61, 371, 59], [335, 63, 371, 61], [336, 12, 372, 10], [337, 12, 373, 10], [337, 16, 373, 14, "r"], [337, 17, 373, 15], [337, 20, 373, 18], [337, 21, 373, 19], [338, 14, 373, 21, "g"], [338, 15, 373, 22], [338, 18, 373, 25], [338, 19, 373, 26], [339, 14, 373, 28, "b"], [339, 15, 373, 29], [339, 18, 373, 32], [339, 19, 373, 33], [340, 14, 373, 35, "count"], [340, 19, 373, 40], [340, 22, 373, 43], [340, 23, 373, 44], [341, 12, 375, 10], [341, 17, 375, 15], [341, 21, 375, 19, "dy"], [341, 23, 375, 21], [341, 26, 375, 24], [341, 27, 375, 25], [341, 29, 375, 27, "dy"], [341, 31, 375, 29], [341, 34, 375, 32, "pixelSize"], [341, 43, 375, 41], [341, 47, 375, 45, "py"], [341, 49, 375, 47], [341, 52, 375, 50, "dy"], [341, 54, 375, 52], [341, 57, 375, 55, "clampedHeight"], [341, 70, 375, 68], [341, 72, 375, 70, "dy"], [341, 74, 375, 72], [341, 76, 375, 74], [341, 78, 375, 76], [342, 14, 376, 12], [342, 19, 376, 17], [342, 23, 376, 21, "dx"], [342, 25, 376, 23], [342, 28, 376, 26], [342, 29, 376, 27], [342, 31, 376, 29, "dx"], [342, 33, 376, 31], [342, 36, 376, 34, "pixelSize"], [342, 45, 376, 43], [342, 49, 376, 47, "px"], [342, 51, 376, 49], [342, 54, 376, 52, "dx"], [342, 56, 376, 54], [342, 59, 376, 57, "<PERSON><PERSON><PERSON><PERSON>"], [342, 71, 376, 69], [342, 73, 376, 71, "dx"], [342, 75, 376, 73], [342, 77, 376, 75], [342, 79, 376, 77], [343, 16, 377, 14], [343, 22, 377, 20, "index"], [343, 27, 377, 25], [343, 30, 377, 28], [343, 31, 377, 29], [343, 32, 377, 30, "py"], [343, 34, 377, 32], [343, 37, 377, 35, "dy"], [343, 39, 377, 37], [343, 43, 377, 41, "<PERSON><PERSON><PERSON><PERSON>"], [343, 55, 377, 53], [343, 59, 377, 57, "px"], [343, 61, 377, 59], [343, 64, 377, 62, "dx"], [343, 66, 377, 64], [343, 67, 377, 65], [343, 71, 377, 69], [343, 72, 377, 70], [344, 16, 378, 14, "r"], [344, 17, 378, 15], [344, 21, 378, 19, "data"], [344, 25, 378, 23], [344, 26, 378, 24, "index"], [344, 31, 378, 29], [344, 32, 378, 30], [345, 16, 379, 14, "g"], [345, 17, 379, 15], [345, 21, 379, 19, "data"], [345, 25, 379, 23], [345, 26, 379, 24, "index"], [345, 31, 379, 29], [345, 34, 379, 32], [345, 35, 379, 33], [345, 36, 379, 34], [346, 16, 380, 14, "b"], [346, 17, 380, 15], [346, 21, 380, 19, "data"], [346, 25, 380, 23], [346, 26, 380, 24, "index"], [346, 31, 380, 29], [346, 34, 380, 32], [346, 35, 380, 33], [346, 36, 380, 34], [347, 16, 381, 14, "count"], [347, 21, 381, 19], [347, 23, 381, 21], [348, 14, 382, 12], [349, 12, 383, 10], [350, 12, 385, 10], [350, 16, 385, 14, "count"], [350, 21, 385, 19], [350, 24, 385, 22], [350, 25, 385, 23], [350, 27, 385, 25], [351, 14, 386, 12, "r"], [351, 15, 386, 13], [351, 18, 386, 16, "Math"], [351, 22, 386, 20], [351, 23, 386, 21, "floor"], [351, 28, 386, 26], [351, 29, 386, 27, "r"], [351, 30, 386, 28], [351, 33, 386, 31, "count"], [351, 38, 386, 36], [351, 39, 386, 37], [352, 14, 387, 12, "g"], [352, 15, 387, 13], [352, 18, 387, 16, "Math"], [352, 22, 387, 20], [352, 23, 387, 21, "floor"], [352, 28, 387, 26], [352, 29, 387, 27, "g"], [352, 30, 387, 28], [352, 33, 387, 31, "count"], [352, 38, 387, 36], [352, 39, 387, 37], [353, 14, 388, 12, "b"], [353, 15, 388, 13], [353, 18, 388, 16, "Math"], [353, 22, 388, 20], [353, 23, 388, 21, "floor"], [353, 28, 388, 26], [353, 29, 388, 27, "b"], [353, 30, 388, 28], [353, 33, 388, 31, "count"], [353, 38, 388, 36], [353, 39, 388, 37], [355, 14, 390, 12], [356, 14, 391, 12], [356, 19, 391, 17], [356, 23, 391, 21, "dy"], [356, 25, 391, 23], [356, 28, 391, 26], [356, 29, 391, 27], [356, 31, 391, 29, "dy"], [356, 33, 391, 31], [356, 36, 391, 34, "pixelSize"], [356, 45, 391, 43], [356, 49, 391, 47, "py"], [356, 51, 391, 49], [356, 54, 391, 52, "dy"], [356, 56, 391, 54], [356, 59, 391, 57, "clampedHeight"], [356, 72, 391, 70], [356, 74, 391, 72, "dy"], [356, 76, 391, 74], [356, 78, 391, 76], [356, 80, 391, 78], [357, 16, 392, 14], [357, 21, 392, 19], [357, 25, 392, 23, "dx"], [357, 27, 392, 25], [357, 30, 392, 28], [357, 31, 392, 29], [357, 33, 392, 31, "dx"], [357, 35, 392, 33], [357, 38, 392, 36, "pixelSize"], [357, 47, 392, 45], [357, 51, 392, 49, "px"], [357, 53, 392, 51], [357, 56, 392, 54, "dx"], [357, 58, 392, 56], [357, 61, 392, 59, "<PERSON><PERSON><PERSON><PERSON>"], [357, 73, 392, 71], [357, 75, 392, 73, "dx"], [357, 77, 392, 75], [357, 79, 392, 77], [357, 81, 392, 79], [358, 18, 393, 16], [358, 24, 393, 22, "index"], [358, 29, 393, 27], [358, 32, 393, 30], [358, 33, 393, 31], [358, 34, 393, 32, "py"], [358, 36, 393, 34], [358, 39, 393, 37, "dy"], [358, 41, 393, 39], [358, 45, 393, 43, "<PERSON><PERSON><PERSON><PERSON>"], [358, 57, 393, 55], [358, 61, 393, 59, "px"], [358, 63, 393, 61], [358, 66, 393, 64, "dx"], [358, 68, 393, 66], [358, 69, 393, 67], [358, 73, 393, 71], [358, 74, 393, 72], [359, 18, 394, 16, "data"], [359, 22, 394, 20], [359, 23, 394, 21, "index"], [359, 28, 394, 26], [359, 29, 394, 27], [359, 32, 394, 30, "r"], [359, 33, 394, 31], [360, 18, 395, 16, "data"], [360, 22, 395, 20], [360, 23, 395, 21, "index"], [360, 28, 395, 26], [360, 31, 395, 29], [360, 32, 395, 30], [360, 33, 395, 31], [360, 36, 395, 34, "g"], [360, 37, 395, 35], [361, 18, 396, 16, "data"], [361, 22, 396, 20], [361, 23, 396, 21, "index"], [361, 28, 396, 26], [361, 31, 396, 29], [361, 32, 396, 30], [361, 33, 396, 31], [361, 36, 396, 34, "b"], [361, 37, 396, 35], [362, 18, 397, 16], [363, 16, 398, 14], [364, 14, 399, 12], [365, 12, 400, 10], [366, 10, 401, 8], [367, 8, 402, 6], [369, 8, 404, 6], [370, 8, 405, 6, "console"], [370, 15, 405, 13], [370, 16, 405, 14, "log"], [370, 19, 405, 17], [370, 20, 405, 18], [370, 76, 405, 74], [370, 77, 405, 75], [371, 8, 406, 6], [371, 13, 406, 11], [371, 17, 406, 15, "i"], [371, 18, 406, 16], [371, 21, 406, 19], [371, 22, 406, 20], [371, 24, 406, 22, "i"], [371, 25, 406, 23], [371, 28, 406, 26], [371, 29, 406, 27], [371, 31, 406, 29, "i"], [371, 32, 406, 30], [371, 34, 406, 32], [371, 36, 406, 34], [372, 10, 407, 8, "applySimpleBlur"], [372, 25, 407, 23], [372, 26, 407, 24, "data"], [372, 30, 407, 28], [372, 32, 407, 30, "<PERSON><PERSON><PERSON><PERSON>"], [372, 44, 407, 42], [372, 46, 407, 44, "clampedHeight"], [372, 59, 407, 57], [372, 60, 407, 58], [373, 8, 408, 6], [375, 8, 410, 6], [376, 8, 411, 6, "ctx"], [376, 11, 411, 9], [376, 12, 411, 10, "putImageData"], [376, 24, 411, 22], [376, 25, 411, 23, "imageData"], [376, 34, 411, 32], [376, 36, 411, 34, "clampedX"], [376, 44, 411, 42], [376, 46, 411, 44, "clampedY"], [376, 54, 411, 52], [376, 55, 411, 53], [377, 8, 412, 6, "console"], [377, 15, 412, 13], [377, 16, 412, 14, "log"], [377, 19, 412, 17], [377, 20, 412, 18], [377, 68, 412, 66, "clampedX"], [377, 76, 412, 74], [377, 81, 412, 79, "clampedY"], [377, 89, 412, 87], [377, 94, 412, 92, "<PERSON><PERSON><PERSON><PERSON>"], [377, 106, 412, 104], [377, 110, 412, 108, "clampedHeight"], [377, 123, 412, 121], [377, 125, 412, 123], [377, 126, 412, 124], [378, 6, 413, 4], [378, 7, 413, 5], [378, 9, 413, 7], [378, 12, 413, 10], [378, 13, 413, 11], [379, 4, 414, 2], [379, 5, 414, 3], [380, 4, 416, 2], [380, 10, 416, 8, "applySimpleBlur"], [380, 25, 416, 23], [380, 28, 416, 26, "applySimpleBlur"], [380, 29, 416, 27, "data"], [380, 33, 416, 50], [380, 35, 416, 52, "width"], [380, 40, 416, 65], [380, 42, 416, 67, "height"], [380, 48, 416, 81], [380, 53, 416, 86], [381, 6, 417, 4], [381, 12, 417, 10, "original"], [381, 20, 417, 18], [381, 23, 417, 21], [381, 27, 417, 25, "Uint8ClampedArray"], [381, 44, 417, 42], [381, 45, 417, 43, "data"], [381, 49, 417, 47], [381, 50, 417, 48], [382, 6, 419, 4], [382, 11, 419, 9], [382, 15, 419, 13, "y"], [382, 16, 419, 14], [382, 19, 419, 17], [382, 20, 419, 18], [382, 22, 419, 20, "y"], [382, 23, 419, 21], [382, 26, 419, 24, "height"], [382, 32, 419, 30], [382, 35, 419, 33], [382, 36, 419, 34], [382, 38, 419, 36, "y"], [382, 39, 419, 37], [382, 41, 419, 39], [382, 43, 419, 41], [383, 8, 420, 6], [383, 13, 420, 11], [383, 17, 420, 15, "x"], [383, 18, 420, 16], [383, 21, 420, 19], [383, 22, 420, 20], [383, 24, 420, 22, "x"], [383, 25, 420, 23], [383, 28, 420, 26, "width"], [383, 33, 420, 31], [383, 36, 420, 34], [383, 37, 420, 35], [383, 39, 420, 37, "x"], [383, 40, 420, 38], [383, 42, 420, 40], [383, 44, 420, 42], [384, 10, 421, 8], [384, 16, 421, 14, "index"], [384, 21, 421, 19], [384, 24, 421, 22], [384, 25, 421, 23, "y"], [384, 26, 421, 24], [384, 29, 421, 27, "width"], [384, 34, 421, 32], [384, 37, 421, 35, "x"], [384, 38, 421, 36], [384, 42, 421, 40], [384, 43, 421, 41], [386, 10, 423, 8], [387, 10, 424, 8], [387, 14, 424, 12, "r"], [387, 15, 424, 13], [387, 18, 424, 16], [387, 19, 424, 17], [388, 12, 424, 19, "g"], [388, 13, 424, 20], [388, 16, 424, 23], [388, 17, 424, 24], [389, 12, 424, 26, "b"], [389, 13, 424, 27], [389, 16, 424, 30], [389, 17, 424, 31], [390, 10, 425, 8], [390, 15, 425, 13], [390, 19, 425, 17, "dy"], [390, 21, 425, 19], [390, 24, 425, 22], [390, 25, 425, 23], [390, 26, 425, 24], [390, 28, 425, 26, "dy"], [390, 30, 425, 28], [390, 34, 425, 32], [390, 35, 425, 33], [390, 37, 425, 35, "dy"], [390, 39, 425, 37], [390, 41, 425, 39], [390, 43, 425, 41], [391, 12, 426, 10], [391, 17, 426, 15], [391, 21, 426, 19, "dx"], [391, 23, 426, 21], [391, 26, 426, 24], [391, 27, 426, 25], [391, 28, 426, 26], [391, 30, 426, 28, "dx"], [391, 32, 426, 30], [391, 36, 426, 34], [391, 37, 426, 35], [391, 39, 426, 37, "dx"], [391, 41, 426, 39], [391, 43, 426, 41], [391, 45, 426, 43], [392, 14, 427, 12], [392, 20, 427, 18, "neighborIndex"], [392, 33, 427, 31], [392, 36, 427, 34], [392, 37, 427, 35], [392, 38, 427, 36, "y"], [392, 39, 427, 37], [392, 42, 427, 40, "dy"], [392, 44, 427, 42], [392, 48, 427, 46, "width"], [392, 53, 427, 51], [392, 57, 427, 55, "x"], [392, 58, 427, 56], [392, 61, 427, 59, "dx"], [392, 63, 427, 61], [392, 64, 427, 62], [392, 68, 427, 66], [392, 69, 427, 67], [393, 14, 428, 12, "r"], [393, 15, 428, 13], [393, 19, 428, 17, "original"], [393, 27, 428, 25], [393, 28, 428, 26, "neighborIndex"], [393, 41, 428, 39], [393, 42, 428, 40], [394, 14, 429, 12, "g"], [394, 15, 429, 13], [394, 19, 429, 17, "original"], [394, 27, 429, 25], [394, 28, 429, 26, "neighborIndex"], [394, 41, 429, 39], [394, 44, 429, 42], [394, 45, 429, 43], [394, 46, 429, 44], [395, 14, 430, 12, "b"], [395, 15, 430, 13], [395, 19, 430, 17, "original"], [395, 27, 430, 25], [395, 28, 430, 26, "neighborIndex"], [395, 41, 430, 39], [395, 44, 430, 42], [395, 45, 430, 43], [395, 46, 430, 44], [396, 12, 431, 10], [397, 10, 432, 8], [398, 10, 434, 8, "data"], [398, 14, 434, 12], [398, 15, 434, 13, "index"], [398, 20, 434, 18], [398, 21, 434, 19], [398, 24, 434, 22, "r"], [398, 25, 434, 23], [398, 28, 434, 26], [398, 29, 434, 27], [399, 10, 435, 8, "data"], [399, 14, 435, 12], [399, 15, 435, 13, "index"], [399, 20, 435, 18], [399, 23, 435, 21], [399, 24, 435, 22], [399, 25, 435, 23], [399, 28, 435, 26, "g"], [399, 29, 435, 27], [399, 32, 435, 30], [399, 33, 435, 31], [400, 10, 436, 8, "data"], [400, 14, 436, 12], [400, 15, 436, 13, "index"], [400, 20, 436, 18], [400, 23, 436, 21], [400, 24, 436, 22], [400, 25, 436, 23], [400, 28, 436, 26, "b"], [400, 29, 436, 27], [400, 32, 436, 30], [400, 33, 436, 31], [401, 8, 437, 6], [402, 6, 438, 4], [403, 4, 439, 2], [403, 5, 439, 3], [404, 4, 441, 2], [404, 10, 441, 8, "applyFallbackFaceBlur"], [404, 31, 441, 29], [404, 34, 441, 32, "applyFallbackFaceBlur"], [404, 35, 441, 33, "ctx"], [404, 38, 441, 62], [404, 40, 441, 64, "imgWidth"], [404, 48, 441, 80], [404, 50, 441, 82, "imgHeight"], [404, 59, 441, 99], [404, 64, 441, 104], [405, 6, 442, 4, "console"], [405, 13, 442, 11], [405, 14, 442, 12, "log"], [405, 17, 442, 15], [405, 18, 442, 16], [405, 90, 442, 88], [405, 91, 442, 89], [407, 6, 444, 4], [408, 6, 445, 4], [408, 12, 445, 10, "areas"], [408, 17, 445, 15], [408, 20, 445, 18], [409, 6, 446, 6], [410, 6, 447, 6], [411, 8, 447, 8, "x"], [411, 9, 447, 9], [411, 11, 447, 11, "imgWidth"], [411, 19, 447, 19], [411, 22, 447, 22], [411, 26, 447, 26], [412, 8, 447, 28, "y"], [412, 9, 447, 29], [412, 11, 447, 31, "imgHeight"], [412, 20, 447, 40], [412, 23, 447, 43], [412, 27, 447, 47], [413, 8, 447, 49, "w"], [413, 9, 447, 50], [413, 11, 447, 52, "imgWidth"], [413, 19, 447, 60], [413, 22, 447, 63], [413, 25, 447, 66], [414, 8, 447, 68, "h"], [414, 9, 447, 69], [414, 11, 447, 71, "imgHeight"], [414, 20, 447, 80], [414, 23, 447, 83], [415, 6, 447, 87], [415, 7, 447, 88], [416, 6, 448, 6], [417, 6, 449, 6], [418, 8, 449, 8, "x"], [418, 9, 449, 9], [418, 11, 449, 11, "imgWidth"], [418, 19, 449, 19], [418, 22, 449, 22], [418, 25, 449, 25], [419, 8, 449, 27, "y"], [419, 9, 449, 28], [419, 11, 449, 30, "imgHeight"], [419, 20, 449, 39], [419, 23, 449, 42], [419, 26, 449, 45], [420, 8, 449, 47, "w"], [420, 9, 449, 48], [420, 11, 449, 50, "imgWidth"], [420, 19, 449, 58], [420, 22, 449, 61], [420, 26, 449, 65], [421, 8, 449, 67, "h"], [421, 9, 449, 68], [421, 11, 449, 70, "imgHeight"], [421, 20, 449, 79], [421, 23, 449, 82], [422, 6, 449, 86], [422, 7, 449, 87], [423, 6, 450, 6], [424, 6, 451, 6], [425, 8, 451, 8, "x"], [425, 9, 451, 9], [425, 11, 451, 11, "imgWidth"], [425, 19, 451, 19], [425, 22, 451, 22], [425, 26, 451, 26], [426, 8, 451, 28, "y"], [426, 9, 451, 29], [426, 11, 451, 31, "imgHeight"], [426, 20, 451, 40], [426, 23, 451, 43], [426, 26, 451, 46], [427, 8, 451, 48, "w"], [427, 9, 451, 49], [427, 11, 451, 51, "imgWidth"], [427, 19, 451, 59], [427, 22, 451, 62], [427, 26, 451, 66], [428, 8, 451, 68, "h"], [428, 9, 451, 69], [428, 11, 451, 71, "imgHeight"], [428, 20, 451, 80], [428, 23, 451, 83], [429, 6, 451, 87], [429, 7, 451, 88], [429, 8, 452, 5], [430, 6, 454, 4, "areas"], [430, 11, 454, 9], [430, 12, 454, 10, "for<PERSON>ach"], [430, 19, 454, 17], [430, 20, 454, 18], [430, 21, 454, 19, "area"], [430, 25, 454, 23], [430, 27, 454, 25, "index"], [430, 32, 454, 30], [430, 37, 454, 35], [431, 8, 455, 6, "console"], [431, 15, 455, 13], [431, 16, 455, 14, "log"], [431, 19, 455, 17], [431, 20, 455, 18], [431, 65, 455, 63, "index"], [431, 70, 455, 68], [431, 73, 455, 71], [431, 74, 455, 72], [431, 77, 455, 75], [431, 79, 455, 77, "area"], [431, 83, 455, 81], [431, 84, 455, 82], [432, 8, 456, 6, "applyStrongBlur"], [432, 23, 456, 21], [432, 24, 456, 22, "ctx"], [432, 27, 456, 25], [432, 29, 456, 27, "area"], [432, 33, 456, 31], [432, 34, 456, 32, "x"], [432, 35, 456, 33], [432, 37, 456, 35, "area"], [432, 41, 456, 39], [432, 42, 456, 40, "y"], [432, 43, 456, 41], [432, 45, 456, 43, "area"], [432, 49, 456, 47], [432, 50, 456, 48, "w"], [432, 51, 456, 49], [432, 53, 456, 51, "area"], [432, 57, 456, 55], [432, 58, 456, 56, "h"], [432, 59, 456, 57], [432, 60, 456, 58], [433, 6, 457, 4], [433, 7, 457, 5], [433, 8, 457, 6], [434, 4, 458, 2], [434, 5, 458, 3], [436, 4, 460, 2], [437, 4, 461, 2], [437, 10, 461, 8, "capturePhoto"], [437, 22, 461, 20], [437, 25, 461, 23], [437, 29, 461, 23, "useCallback"], [437, 47, 461, 34], [437, 49, 461, 35], [437, 61, 461, 47], [438, 6, 462, 4], [439, 6, 463, 4], [439, 12, 463, 10, "isDev"], [439, 17, 463, 15], [439, 20, 463, 18, "process"], [439, 27, 463, 25], [439, 28, 463, 26, "env"], [439, 31, 463, 29], [439, 32, 463, 30, "NODE_ENV"], [439, 40, 463, 38], [439, 45, 463, 43], [439, 58, 463, 56], [439, 62, 463, 60, "__DEV__"], [439, 69, 463, 67], [440, 6, 465, 4], [440, 10, 465, 8], [440, 11, 465, 9, "cameraRef"], [440, 20, 465, 18], [440, 21, 465, 19, "current"], [440, 28, 465, 26], [440, 32, 465, 30], [440, 33, 465, 31, "isDev"], [440, 38, 465, 36], [440, 40, 465, 38], [441, 8, 466, 6, "<PERSON><PERSON>"], [441, 22, 466, 11], [441, 23, 466, 12, "alert"], [441, 28, 466, 17], [441, 29, 466, 18], [441, 36, 466, 25], [441, 38, 466, 27], [441, 56, 466, 45], [441, 57, 466, 46], [442, 8, 467, 6], [443, 6, 468, 4], [444, 6, 469, 4], [444, 10, 469, 8], [445, 8, 470, 6, "setProcessingState"], [445, 26, 470, 24], [445, 27, 470, 25], [445, 38, 470, 36], [445, 39, 470, 37], [446, 8, 471, 6, "setProcessingProgress"], [446, 29, 471, 27], [446, 30, 471, 28], [446, 32, 471, 30], [446, 33, 471, 31], [447, 8, 472, 6], [448, 8, 473, 6], [449, 8, 474, 6], [450, 8, 475, 6], [450, 14, 475, 12], [450, 18, 475, 16, "Promise"], [450, 25, 475, 23], [450, 26, 475, 24, "resolve"], [450, 33, 475, 31], [450, 37, 475, 35, "setTimeout"], [450, 47, 475, 45], [450, 48, 475, 46, "resolve"], [450, 55, 475, 53], [450, 57, 475, 55], [450, 59, 475, 57], [450, 60, 475, 58], [450, 61, 475, 59], [451, 8, 476, 6], [452, 8, 477, 6], [452, 12, 477, 10, "photo"], [452, 17, 477, 15], [453, 8, 479, 6], [453, 12, 479, 10], [454, 10, 480, 8, "photo"], [454, 15, 480, 13], [454, 18, 480, 16], [454, 24, 480, 22, "cameraRef"], [454, 33, 480, 31], [454, 34, 480, 32, "current"], [454, 41, 480, 39], [454, 42, 480, 40, "takePictureAsync"], [454, 58, 480, 56], [454, 59, 480, 57], [455, 12, 481, 10, "quality"], [455, 19, 481, 17], [455, 21, 481, 19], [455, 24, 481, 22], [456, 12, 482, 10, "base64"], [456, 18, 482, 16], [456, 20, 482, 18], [456, 25, 482, 23], [457, 12, 483, 10, "skipProcessing"], [457, 26, 483, 24], [457, 28, 483, 26], [457, 32, 483, 30], [457, 33, 483, 32], [458, 10, 484, 8], [458, 11, 484, 9], [458, 12, 484, 10], [459, 8, 485, 6], [459, 9, 485, 7], [459, 10, 485, 8], [459, 17, 485, 15, "cameraError"], [459, 28, 485, 26], [459, 30, 485, 28], [460, 10, 486, 8, "console"], [460, 17, 486, 15], [460, 18, 486, 16, "log"], [460, 21, 486, 19], [460, 22, 486, 20], [460, 82, 486, 80], [460, 84, 486, 82, "cameraError"], [460, 95, 486, 93], [460, 96, 486, 94], [461, 10, 487, 8], [462, 10, 488, 8], [462, 14, 488, 12, "isDev"], [462, 19, 488, 17], [462, 21, 488, 19], [463, 12, 489, 10, "photo"], [463, 17, 489, 15], [463, 20, 489, 18], [464, 14, 490, 12, "uri"], [464, 17, 490, 15], [464, 19, 490, 17], [465, 12, 491, 10], [465, 13, 491, 11], [466, 10, 492, 8], [466, 11, 492, 9], [466, 17, 492, 15], [467, 12, 493, 10], [467, 18, 493, 16, "cameraError"], [467, 29, 493, 27], [468, 10, 494, 8], [469, 8, 495, 6], [470, 8, 496, 6], [470, 12, 496, 10], [470, 13, 496, 11, "photo"], [470, 18, 496, 16], [470, 20, 496, 18], [471, 10, 497, 8], [471, 16, 497, 14], [471, 20, 497, 18, "Error"], [471, 25, 497, 23], [471, 26, 497, 24], [471, 51, 497, 49], [471, 52, 497, 50], [472, 8, 498, 6], [473, 8, 499, 6, "console"], [473, 15, 499, 13], [473, 16, 499, 14, "log"], [473, 19, 499, 17], [473, 20, 499, 18], [473, 56, 499, 54], [473, 58, 499, 56, "photo"], [473, 63, 499, 61], [473, 64, 499, 62, "uri"], [473, 67, 499, 65], [473, 68, 499, 66], [474, 8, 500, 6, "setCapturedPhoto"], [474, 24, 500, 22], [474, 25, 500, 23, "photo"], [474, 30, 500, 28], [474, 31, 500, 29, "uri"], [474, 34, 500, 32], [474, 35, 500, 33], [475, 8, 501, 6, "setProcessingProgress"], [475, 29, 501, 27], [475, 30, 501, 28], [475, 32, 501, 30], [475, 33, 501, 31], [476, 8, 502, 6], [477, 8, 503, 6, "console"], [477, 15, 503, 13], [477, 16, 503, 14, "log"], [477, 19, 503, 17], [477, 20, 503, 18], [477, 73, 503, 71], [477, 74, 503, 72], [478, 8, 504, 6], [478, 14, 504, 12, "processImageWithFaceBlur"], [478, 38, 504, 36], [478, 39, 504, 37, "photo"], [478, 44, 504, 42], [478, 45, 504, 43, "uri"], [478, 48, 504, 46], [478, 49, 504, 47], [479, 8, 505, 6, "console"], [479, 15, 505, 13], [479, 16, 505, 14, "log"], [479, 19, 505, 17], [479, 20, 505, 18], [479, 71, 505, 69], [479, 72, 505, 70], [480, 6, 506, 4], [480, 7, 506, 5], [480, 8, 506, 6], [480, 15, 506, 13, "error"], [480, 20, 506, 18], [480, 22, 506, 20], [481, 8, 507, 6, "console"], [481, 15, 507, 13], [481, 16, 507, 14, "error"], [481, 21, 507, 19], [481, 22, 507, 20], [481, 54, 507, 52], [481, 56, 507, 54, "error"], [481, 61, 507, 59], [481, 62, 507, 60], [482, 8, 508, 6, "setErrorMessage"], [482, 23, 508, 21], [482, 24, 508, 22], [482, 68, 508, 66], [482, 69, 508, 67], [483, 8, 509, 6, "setProcessingState"], [483, 26, 509, 24], [483, 27, 509, 25], [483, 34, 509, 32], [483, 35, 509, 33], [484, 6, 510, 4], [485, 4, 511, 2], [485, 5, 511, 3], [485, 7, 511, 5], [485, 9, 511, 7], [485, 10, 511, 8], [486, 4, 512, 2], [487, 4, 513, 2], [487, 10, 513, 8, "processImageWithFaceBlur"], [487, 34, 513, 32], [487, 37, 513, 35], [487, 43, 513, 42, "photoUri"], [487, 51, 513, 58], [487, 55, 513, 63], [488, 6, 514, 4], [488, 10, 514, 8], [489, 8, 515, 6, "console"], [489, 15, 515, 13], [489, 16, 515, 14, "log"], [489, 19, 515, 17], [489, 20, 515, 18], [489, 84, 515, 82], [489, 85, 515, 83], [490, 8, 516, 6, "setProcessingState"], [490, 26, 516, 24], [490, 27, 516, 25], [490, 39, 516, 37], [490, 40, 516, 38], [491, 8, 517, 6, "setProcessingProgress"], [491, 29, 517, 27], [491, 30, 517, 28], [491, 32, 517, 30], [491, 33, 517, 31], [493, 8, 519, 6], [494, 8, 520, 6], [494, 14, 520, 12, "canvas"], [494, 20, 520, 18], [494, 23, 520, 21, "document"], [494, 31, 520, 29], [494, 32, 520, 30, "createElement"], [494, 45, 520, 43], [494, 46, 520, 44], [494, 54, 520, 52], [494, 55, 520, 53], [495, 8, 521, 6], [495, 14, 521, 12, "ctx"], [495, 17, 521, 15], [495, 20, 521, 18, "canvas"], [495, 26, 521, 24], [495, 27, 521, 25, "getContext"], [495, 37, 521, 35], [495, 38, 521, 36], [495, 42, 521, 40], [495, 43, 521, 41], [496, 8, 522, 6], [496, 12, 522, 10], [496, 13, 522, 11, "ctx"], [496, 16, 522, 14], [496, 18, 522, 16], [496, 24, 522, 22], [496, 28, 522, 26, "Error"], [496, 33, 522, 31], [496, 34, 522, 32], [496, 64, 522, 62], [496, 65, 522, 63], [498, 8, 524, 6], [499, 8, 525, 6], [499, 14, 525, 12, "img"], [499, 17, 525, 15], [499, 20, 525, 18], [499, 24, 525, 22, "Image"], [499, 29, 525, 27], [499, 30, 525, 28], [499, 31, 525, 29], [500, 8, 526, 6], [500, 14, 526, 12], [500, 18, 526, 16, "Promise"], [500, 25, 526, 23], [500, 26, 526, 24], [500, 27, 526, 25, "resolve"], [500, 34, 526, 32], [500, 36, 526, 34, "reject"], [500, 42, 526, 40], [500, 47, 526, 45], [501, 10, 527, 8, "img"], [501, 13, 527, 11], [501, 14, 527, 12, "onload"], [501, 20, 527, 18], [501, 23, 527, 21, "resolve"], [501, 30, 527, 28], [502, 10, 528, 8, "img"], [502, 13, 528, 11], [502, 14, 528, 12, "onerror"], [502, 21, 528, 19], [502, 24, 528, 22, "reject"], [502, 30, 528, 28], [503, 10, 529, 8, "img"], [503, 13, 529, 11], [503, 14, 529, 12, "src"], [503, 17, 529, 15], [503, 20, 529, 18, "photoUri"], [503, 28, 529, 26], [504, 8, 530, 6], [504, 9, 530, 7], [504, 10, 530, 8], [506, 8, 532, 6], [507, 8, 533, 6, "canvas"], [507, 14, 533, 12], [507, 15, 533, 13, "width"], [507, 20, 533, 18], [507, 23, 533, 21, "img"], [507, 26, 533, 24], [507, 27, 533, 25, "width"], [507, 32, 533, 30], [508, 8, 534, 6, "canvas"], [508, 14, 534, 12], [508, 15, 534, 13, "height"], [508, 21, 534, 19], [508, 24, 534, 22, "img"], [508, 27, 534, 25], [508, 28, 534, 26, "height"], [508, 34, 534, 32], [509, 8, 535, 6, "console"], [509, 15, 535, 13], [509, 16, 535, 14, "log"], [509, 19, 535, 17], [509, 20, 535, 18], [509, 54, 535, 52], [509, 56, 535, 54], [510, 10, 535, 56, "width"], [510, 15, 535, 61], [510, 17, 535, 63, "img"], [510, 20, 535, 66], [510, 21, 535, 67, "width"], [510, 26, 535, 72], [511, 10, 535, 74, "height"], [511, 16, 535, 80], [511, 18, 535, 82, "img"], [511, 21, 535, 85], [511, 22, 535, 86, "height"], [512, 8, 535, 93], [512, 9, 535, 94], [512, 10, 535, 95], [514, 8, 537, 6], [515, 8, 538, 6, "ctx"], [515, 11, 538, 9], [515, 12, 538, 10, "drawImage"], [515, 21, 538, 19], [515, 22, 538, 20, "img"], [515, 25, 538, 23], [515, 27, 538, 25], [515, 28, 538, 26], [515, 30, 538, 28], [515, 31, 538, 29], [515, 32, 538, 30], [516, 8, 539, 6, "console"], [516, 15, 539, 13], [516, 16, 539, 14, "log"], [516, 19, 539, 17], [516, 20, 539, 18], [516, 72, 539, 70], [516, 73, 539, 71], [517, 8, 541, 6, "setProcessingProgress"], [517, 29, 541, 27], [517, 30, 541, 28], [517, 32, 541, 30], [517, 33, 541, 31], [519, 8, 543, 6], [520, 8, 544, 6], [520, 12, 544, 10, "detectedFaces"], [520, 25, 544, 23], [520, 28, 544, 26], [520, 30, 544, 28], [521, 8, 546, 6, "console"], [521, 15, 546, 13], [521, 16, 546, 14, "log"], [521, 19, 546, 17], [521, 20, 546, 18], [521, 81, 546, 79], [521, 82, 546, 80], [523, 8, 548, 6], [524, 8, 549, 6], [524, 12, 549, 10], [525, 10, 550, 8], [525, 16, 550, 14, "loadTensorFlowFaceDetection"], [525, 43, 550, 41], [525, 44, 550, 42], [525, 45, 550, 43], [526, 10, 551, 8, "detectedFaces"], [526, 23, 551, 21], [526, 26, 551, 24], [526, 32, 551, 30, "detectFacesWithTensorFlow"], [526, 57, 551, 55], [526, 58, 551, 56, "img"], [526, 61, 551, 59], [526, 62, 551, 60], [527, 10, 552, 8, "console"], [527, 17, 552, 15], [527, 18, 552, 16, "log"], [527, 21, 552, 19], [527, 22, 552, 20], [527, 70, 552, 68, "detectedFaces"], [527, 83, 552, 81], [527, 84, 552, 82, "length"], [527, 90, 552, 88], [527, 98, 552, 96], [527, 99, 552, 97], [528, 8, 553, 6], [528, 9, 553, 7], [528, 10, 553, 8], [528, 17, 553, 15, "tensorFlowError"], [528, 32, 553, 30], [528, 34, 553, 32], [529, 10, 554, 8, "console"], [529, 17, 554, 15], [529, 18, 554, 16, "warn"], [529, 22, 554, 20], [529, 23, 554, 21], [529, 61, 554, 59], [529, 63, 554, 61, "tensorFlowError"], [529, 78, 554, 76], [529, 79, 554, 77], [531, 10, 556, 8], [532, 10, 557, 8, "console"], [532, 17, 557, 15], [532, 18, 557, 16, "log"], [532, 21, 557, 19], [532, 22, 557, 20], [532, 86, 557, 84], [532, 87, 557, 85], [533, 10, 558, 8, "detectedFaces"], [533, 23, 558, 21], [533, 26, 558, 24, "detectFacesHeuristic"], [533, 46, 558, 44], [533, 47, 558, 45, "img"], [533, 50, 558, 48], [533, 52, 558, 50, "ctx"], [533, 55, 558, 53], [533, 56, 558, 54], [534, 10, 559, 8, "console"], [534, 17, 559, 15], [534, 18, 559, 16, "log"], [534, 21, 559, 19], [534, 22, 559, 20], [534, 70, 559, 68, "detectedFaces"], [534, 83, 559, 81], [534, 84, 559, 82, "length"], [534, 90, 559, 88], [534, 98, 559, 96], [534, 99, 559, 97], [535, 8, 560, 6], [536, 8, 562, 6, "console"], [536, 15, 562, 13], [536, 16, 562, 14, "log"], [536, 19, 562, 17], [536, 20, 562, 18], [536, 72, 562, 70, "detectedFaces"], [536, 85, 562, 83], [536, 86, 562, 84, "length"], [536, 92, 562, 90], [536, 100, 562, 98], [536, 101, 562, 99], [537, 8, 563, 6], [537, 12, 563, 10, "detectedFaces"], [537, 25, 563, 23], [537, 26, 563, 24, "length"], [537, 32, 563, 30], [537, 35, 563, 33], [537, 36, 563, 34], [537, 38, 563, 36], [538, 10, 564, 8, "console"], [538, 17, 564, 15], [538, 18, 564, 16, "log"], [538, 21, 564, 19], [538, 22, 564, 20], [538, 66, 564, 64], [538, 68, 564, 66, "detectedFaces"], [538, 81, 564, 79], [538, 82, 564, 80, "map"], [538, 85, 564, 83], [538, 86, 564, 84], [538, 87, 564, 85, "face"], [538, 91, 564, 89], [538, 93, 564, 91, "i"], [538, 94, 564, 92], [538, 100, 564, 98], [539, 12, 565, 10, "faceNumber"], [539, 22, 565, 20], [539, 24, 565, 22, "i"], [539, 25, 565, 23], [539, 28, 565, 26], [539, 29, 565, 27], [540, 12, 566, 10, "centerX"], [540, 19, 566, 17], [540, 21, 566, 19, "face"], [540, 25, 566, 23], [540, 26, 566, 24, "boundingBox"], [540, 37, 566, 35], [540, 38, 566, 36, "xCenter"], [540, 45, 566, 43], [541, 12, 567, 10, "centerY"], [541, 19, 567, 17], [541, 21, 567, 19, "face"], [541, 25, 567, 23], [541, 26, 567, 24, "boundingBox"], [541, 37, 567, 35], [541, 38, 567, 36, "yCenter"], [541, 45, 567, 43], [542, 12, 568, 10, "width"], [542, 17, 568, 15], [542, 19, 568, 17, "face"], [542, 23, 568, 21], [542, 24, 568, 22, "boundingBox"], [542, 35, 568, 33], [542, 36, 568, 34, "width"], [542, 41, 568, 39], [543, 12, 569, 10, "height"], [543, 18, 569, 16], [543, 20, 569, 18, "face"], [543, 24, 569, 22], [543, 25, 569, 23, "boundingBox"], [543, 36, 569, 34], [543, 37, 569, 35, "height"], [544, 10, 570, 8], [544, 11, 570, 9], [544, 12, 570, 10], [544, 13, 570, 11], [544, 14, 570, 12], [545, 8, 571, 6], [545, 9, 571, 7], [545, 15, 571, 13], [546, 10, 572, 8, "console"], [546, 17, 572, 15], [546, 18, 572, 16, "log"], [546, 21, 572, 19], [546, 22, 572, 20], [546, 91, 572, 89], [546, 92, 572, 90], [547, 8, 573, 6], [548, 8, 575, 6, "setProcessingProgress"], [548, 29, 575, 27], [548, 30, 575, 28], [548, 32, 575, 30], [548, 33, 575, 31], [550, 8, 577, 6], [551, 8, 578, 6], [551, 12, 578, 10, "detectedFaces"], [551, 25, 578, 23], [551, 26, 578, 24, "length"], [551, 32, 578, 30], [551, 35, 578, 33], [551, 36, 578, 34], [551, 38, 578, 36], [552, 10, 579, 8, "console"], [552, 17, 579, 15], [552, 18, 579, 16, "log"], [552, 21, 579, 19], [552, 22, 579, 20], [552, 61, 579, 59, "detectedFaces"], [552, 74, 579, 72], [552, 75, 579, 73, "length"], [552, 81, 579, 79], [552, 101, 579, 99], [552, 102, 579, 100], [553, 10, 581, 8, "detectedFaces"], [553, 23, 581, 21], [553, 24, 581, 22, "for<PERSON>ach"], [553, 31, 581, 29], [553, 32, 581, 30], [553, 33, 581, 31, "detection"], [553, 42, 581, 40], [553, 44, 581, 42, "index"], [553, 49, 581, 47], [553, 54, 581, 52], [554, 12, 582, 10], [554, 18, 582, 16, "bbox"], [554, 22, 582, 20], [554, 25, 582, 23, "detection"], [554, 34, 582, 32], [554, 35, 582, 33, "boundingBox"], [554, 46, 582, 44], [556, 12, 584, 10], [557, 12, 585, 10], [557, 18, 585, 16, "faceX"], [557, 23, 585, 21], [557, 26, 585, 24, "bbox"], [557, 30, 585, 28], [557, 31, 585, 29, "xCenter"], [557, 38, 585, 36], [557, 41, 585, 39, "img"], [557, 44, 585, 42], [557, 45, 585, 43, "width"], [557, 50, 585, 48], [557, 53, 585, 52, "bbox"], [557, 57, 585, 56], [557, 58, 585, 57, "width"], [557, 63, 585, 62], [557, 66, 585, 65, "img"], [557, 69, 585, 68], [557, 70, 585, 69, "width"], [557, 75, 585, 74], [557, 78, 585, 78], [557, 79, 585, 79], [558, 12, 586, 10], [558, 18, 586, 16, "faceY"], [558, 23, 586, 21], [558, 26, 586, 24, "bbox"], [558, 30, 586, 28], [558, 31, 586, 29, "yCenter"], [558, 38, 586, 36], [558, 41, 586, 39, "img"], [558, 44, 586, 42], [558, 45, 586, 43, "height"], [558, 51, 586, 49], [558, 54, 586, 53, "bbox"], [558, 58, 586, 57], [558, 59, 586, 58, "height"], [558, 65, 586, 64], [558, 68, 586, 67, "img"], [558, 71, 586, 70], [558, 72, 586, 71, "height"], [558, 78, 586, 77], [558, 81, 586, 81], [558, 82, 586, 82], [559, 12, 587, 10], [559, 18, 587, 16, "faceWidth"], [559, 27, 587, 25], [559, 30, 587, 28, "bbox"], [559, 34, 587, 32], [559, 35, 587, 33, "width"], [559, 40, 587, 38], [559, 43, 587, 41, "img"], [559, 46, 587, 44], [559, 47, 587, 45, "width"], [559, 52, 587, 50], [560, 12, 588, 10], [560, 18, 588, 16, "faceHeight"], [560, 28, 588, 26], [560, 31, 588, 29, "bbox"], [560, 35, 588, 33], [560, 36, 588, 34, "height"], [560, 42, 588, 40], [560, 45, 588, 43, "img"], [560, 48, 588, 46], [560, 49, 588, 47, "height"], [560, 55, 588, 53], [562, 12, 590, 10], [563, 12, 591, 10], [563, 18, 591, 16, "padding"], [563, 25, 591, 23], [563, 28, 591, 26], [563, 31, 591, 29], [564, 12, 592, 10], [564, 18, 592, 16, "paddedX"], [564, 25, 592, 23], [564, 28, 592, 26, "Math"], [564, 32, 592, 30], [564, 33, 592, 31, "max"], [564, 36, 592, 34], [564, 37, 592, 35], [564, 38, 592, 36], [564, 40, 592, 38, "faceX"], [564, 45, 592, 43], [564, 48, 592, 46, "faceWidth"], [564, 57, 592, 55], [564, 60, 592, 58, "padding"], [564, 67, 592, 65], [564, 68, 592, 66], [565, 12, 593, 10], [565, 18, 593, 16, "paddedY"], [565, 25, 593, 23], [565, 28, 593, 26, "Math"], [565, 32, 593, 30], [565, 33, 593, 31, "max"], [565, 36, 593, 34], [565, 37, 593, 35], [565, 38, 593, 36], [565, 40, 593, 38, "faceY"], [565, 45, 593, 43], [565, 48, 593, 46, "faceHeight"], [565, 58, 593, 56], [565, 61, 593, 59, "padding"], [565, 68, 593, 66], [565, 69, 593, 67], [566, 12, 594, 10], [566, 18, 594, 16, "<PERSON><PERSON><PERSON><PERSON>"], [566, 29, 594, 27], [566, 32, 594, 30, "Math"], [566, 36, 594, 34], [566, 37, 594, 35, "min"], [566, 40, 594, 38], [566, 41, 594, 39, "img"], [566, 44, 594, 42], [566, 45, 594, 43, "width"], [566, 50, 594, 48], [566, 53, 594, 51, "paddedX"], [566, 60, 594, 58], [566, 62, 594, 60, "faceWidth"], [566, 71, 594, 69], [566, 75, 594, 73], [566, 76, 594, 74], [566, 79, 594, 77], [566, 80, 594, 78], [566, 83, 594, 81, "padding"], [566, 90, 594, 88], [566, 91, 594, 89], [566, 92, 594, 90], [567, 12, 595, 10], [567, 18, 595, 16, "paddedHeight"], [567, 30, 595, 28], [567, 33, 595, 31, "Math"], [567, 37, 595, 35], [567, 38, 595, 36, "min"], [567, 41, 595, 39], [567, 42, 595, 40, "img"], [567, 45, 595, 43], [567, 46, 595, 44, "height"], [567, 52, 595, 50], [567, 55, 595, 53, "paddedY"], [567, 62, 595, 60], [567, 64, 595, 62, "faceHeight"], [567, 74, 595, 72], [567, 78, 595, 76], [567, 79, 595, 77], [567, 82, 595, 80], [567, 83, 595, 81], [567, 86, 595, 84, "padding"], [567, 93, 595, 91], [567, 94, 595, 92], [567, 95, 595, 93], [568, 12, 597, 10, "console"], [568, 19, 597, 17], [568, 20, 597, 18, "log"], [568, 23, 597, 21], [568, 24, 597, 22], [568, 60, 597, 58, "index"], [568, 65, 597, 63], [568, 68, 597, 66], [568, 69, 597, 67], [568, 72, 597, 70], [568, 74, 597, 72], [569, 14, 598, 12, "original"], [569, 22, 598, 20], [569, 24, 598, 22], [570, 16, 598, 24, "x"], [570, 17, 598, 25], [570, 19, 598, 27, "Math"], [570, 23, 598, 31], [570, 24, 598, 32, "round"], [570, 29, 598, 37], [570, 30, 598, 38, "faceX"], [570, 35, 598, 43], [570, 36, 598, 44], [571, 16, 598, 46, "y"], [571, 17, 598, 47], [571, 19, 598, 49, "Math"], [571, 23, 598, 53], [571, 24, 598, 54, "round"], [571, 29, 598, 59], [571, 30, 598, 60, "faceY"], [571, 35, 598, 65], [571, 36, 598, 66], [572, 16, 598, 68, "w"], [572, 17, 598, 69], [572, 19, 598, 71, "Math"], [572, 23, 598, 75], [572, 24, 598, 76, "round"], [572, 29, 598, 81], [572, 30, 598, 82, "faceWidth"], [572, 39, 598, 91], [572, 40, 598, 92], [573, 16, 598, 94, "h"], [573, 17, 598, 95], [573, 19, 598, 97, "Math"], [573, 23, 598, 101], [573, 24, 598, 102, "round"], [573, 29, 598, 107], [573, 30, 598, 108, "faceHeight"], [573, 40, 598, 118], [574, 14, 598, 120], [574, 15, 598, 121], [575, 14, 599, 12, "padded"], [575, 20, 599, 18], [575, 22, 599, 20], [576, 16, 599, 22, "x"], [576, 17, 599, 23], [576, 19, 599, 25, "Math"], [576, 23, 599, 29], [576, 24, 599, 30, "round"], [576, 29, 599, 35], [576, 30, 599, 36, "paddedX"], [576, 37, 599, 43], [576, 38, 599, 44], [577, 16, 599, 46, "y"], [577, 17, 599, 47], [577, 19, 599, 49, "Math"], [577, 23, 599, 53], [577, 24, 599, 54, "round"], [577, 29, 599, 59], [577, 30, 599, 60, "paddedY"], [577, 37, 599, 67], [577, 38, 599, 68], [578, 16, 599, 70, "w"], [578, 17, 599, 71], [578, 19, 599, 73, "Math"], [578, 23, 599, 77], [578, 24, 599, 78, "round"], [578, 29, 599, 83], [578, 30, 599, 84, "<PERSON><PERSON><PERSON><PERSON>"], [578, 41, 599, 95], [578, 42, 599, 96], [579, 16, 599, 98, "h"], [579, 17, 599, 99], [579, 19, 599, 101, "Math"], [579, 23, 599, 105], [579, 24, 599, 106, "round"], [579, 29, 599, 111], [579, 30, 599, 112, "paddedHeight"], [579, 42, 599, 124], [580, 14, 599, 126], [581, 12, 600, 10], [581, 13, 600, 11], [581, 14, 600, 12], [583, 12, 602, 10], [584, 12, 603, 10, "console"], [584, 19, 603, 17], [584, 20, 603, 18, "log"], [584, 23, 603, 21], [584, 24, 603, 22], [584, 70, 603, 68], [584, 72, 603, 70], [585, 14, 604, 12, "width"], [585, 19, 604, 17], [585, 21, 604, 19, "canvas"], [585, 27, 604, 25], [585, 28, 604, 26, "width"], [585, 33, 604, 31], [586, 14, 605, 12, "height"], [586, 20, 605, 18], [586, 22, 605, 20, "canvas"], [586, 28, 605, 26], [586, 29, 605, 27, "height"], [586, 35, 605, 33], [587, 14, 606, 12, "contextValid"], [587, 26, 606, 24], [587, 28, 606, 26], [587, 29, 606, 27], [587, 30, 606, 28, "ctx"], [588, 12, 607, 10], [588, 13, 607, 11], [588, 14, 607, 12], [590, 12, 609, 10], [591, 12, 610, 10, "applyStrongBlur"], [591, 27, 610, 25], [591, 28, 610, 26, "ctx"], [591, 31, 610, 29], [591, 33, 610, 31, "paddedX"], [591, 40, 610, 38], [591, 42, 610, 40, "paddedY"], [591, 49, 610, 47], [591, 51, 610, 49, "<PERSON><PERSON><PERSON><PERSON>"], [591, 62, 610, 60], [591, 64, 610, 62, "paddedHeight"], [591, 76, 610, 74], [591, 77, 610, 75], [593, 12, 612, 10], [594, 12, 613, 10, "console"], [594, 19, 613, 17], [594, 20, 613, 18, "log"], [594, 23, 613, 21], [594, 24, 613, 22], [594, 102, 613, 100], [594, 103, 613, 101], [596, 12, 615, 10], [597, 12, 616, 10], [597, 18, 616, 16, "testImageData"], [597, 31, 616, 29], [597, 34, 616, 32, "ctx"], [597, 37, 616, 35], [597, 38, 616, 36, "getImageData"], [597, 50, 616, 48], [597, 51, 616, 49, "paddedX"], [597, 58, 616, 56], [597, 61, 616, 59], [597, 63, 616, 61], [597, 65, 616, 63, "paddedY"], [597, 72, 616, 70], [597, 75, 616, 73], [597, 77, 616, 75], [597, 79, 616, 77], [597, 81, 616, 79], [597, 83, 616, 81], [597, 85, 616, 83], [597, 86, 616, 84], [598, 12, 617, 10, "console"], [598, 19, 617, 17], [598, 20, 617, 18, "log"], [598, 23, 617, 21], [598, 24, 617, 22], [598, 70, 617, 68], [598, 72, 617, 70], [599, 14, 618, 12, "firstPixel"], [599, 24, 618, 22], [599, 26, 618, 24], [599, 27, 618, 25, "testImageData"], [599, 40, 618, 38], [599, 41, 618, 39, "data"], [599, 45, 618, 43], [599, 46, 618, 44], [599, 47, 618, 45], [599, 48, 618, 46], [599, 50, 618, 48, "testImageData"], [599, 63, 618, 61], [599, 64, 618, 62, "data"], [599, 68, 618, 66], [599, 69, 618, 67], [599, 70, 618, 68], [599, 71, 618, 69], [599, 73, 618, 71, "testImageData"], [599, 86, 618, 84], [599, 87, 618, 85, "data"], [599, 91, 618, 89], [599, 92, 618, 90], [599, 93, 618, 91], [599, 94, 618, 92], [599, 95, 618, 93], [600, 14, 619, 12, "secondPixel"], [600, 25, 619, 23], [600, 27, 619, 25], [600, 28, 619, 26, "testImageData"], [600, 41, 619, 39], [600, 42, 619, 40, "data"], [600, 46, 619, 44], [600, 47, 619, 45], [600, 48, 619, 46], [600, 49, 619, 47], [600, 51, 619, 49, "testImageData"], [600, 64, 619, 62], [600, 65, 619, 63, "data"], [600, 69, 619, 67], [600, 70, 619, 68], [600, 71, 619, 69], [600, 72, 619, 70], [600, 74, 619, 72, "testImageData"], [600, 87, 619, 85], [600, 88, 619, 86, "data"], [600, 92, 619, 90], [600, 93, 619, 91], [600, 94, 619, 92], [600, 95, 619, 93], [601, 12, 620, 10], [601, 13, 620, 11], [601, 14, 620, 12], [602, 12, 622, 10, "console"], [602, 19, 622, 17], [602, 20, 622, 18, "log"], [602, 23, 622, 21], [602, 24, 622, 22], [602, 50, 622, 48, "index"], [602, 55, 622, 53], [602, 58, 622, 56], [602, 59, 622, 57], [602, 79, 622, 77], [602, 80, 622, 78], [603, 10, 623, 8], [603, 11, 623, 9], [603, 12, 623, 10], [604, 10, 625, 8, "console"], [604, 17, 625, 15], [604, 18, 625, 16, "log"], [604, 21, 625, 19], [604, 22, 625, 20], [604, 48, 625, 46, "detectedFaces"], [604, 61, 625, 59], [604, 62, 625, 60, "length"], [604, 68, 625, 66], [604, 104, 625, 102], [604, 105, 625, 103], [605, 8, 626, 6], [605, 9, 626, 7], [605, 15, 626, 13], [606, 10, 627, 8, "console"], [606, 17, 627, 15], [606, 18, 627, 16, "log"], [606, 21, 627, 19], [606, 22, 627, 20], [606, 109, 627, 107], [606, 110, 627, 108], [607, 10, 628, 8], [608, 10, 629, 8, "applyFallbackFaceBlur"], [608, 31, 629, 29], [608, 32, 629, 30, "ctx"], [608, 35, 629, 33], [608, 37, 629, 35, "img"], [608, 40, 629, 38], [608, 41, 629, 39, "width"], [608, 46, 629, 44], [608, 48, 629, 46, "img"], [608, 51, 629, 49], [608, 52, 629, 50, "height"], [608, 58, 629, 56], [608, 59, 629, 57], [609, 8, 630, 6], [610, 8, 632, 6, "setProcessingProgress"], [610, 29, 632, 27], [610, 30, 632, 28], [610, 32, 632, 30], [610, 33, 632, 31], [612, 8, 634, 6], [613, 8, 635, 6, "console"], [613, 15, 635, 13], [613, 16, 635, 14, "log"], [613, 19, 635, 17], [613, 20, 635, 18], [613, 85, 635, 83], [613, 86, 635, 84], [614, 8, 636, 6], [614, 14, 636, 12, "blurredImageBlob"], [614, 30, 636, 28], [614, 33, 636, 31], [614, 39, 636, 37], [614, 43, 636, 41, "Promise"], [614, 50, 636, 48], [614, 51, 636, 56, "resolve"], [614, 58, 636, 63], [614, 62, 636, 68], [615, 10, 637, 8, "canvas"], [615, 16, 637, 14], [615, 17, 637, 15, "toBlob"], [615, 23, 637, 21], [615, 24, 637, 23, "blob"], [615, 28, 637, 27], [615, 32, 637, 32, "resolve"], [615, 39, 637, 39], [615, 40, 637, 40, "blob"], [615, 44, 637, 45], [615, 45, 637, 46], [615, 47, 637, 48], [615, 59, 637, 60], [615, 61, 637, 62], [615, 64, 637, 65], [615, 65, 637, 66], [616, 8, 638, 6], [616, 9, 638, 7], [616, 10, 638, 8], [617, 8, 640, 6], [617, 14, 640, 12, "blurredImageUrl"], [617, 29, 640, 27], [617, 32, 640, 30, "URL"], [617, 35, 640, 33], [617, 36, 640, 34, "createObjectURL"], [617, 51, 640, 49], [617, 52, 640, 50, "blurredImageBlob"], [617, 68, 640, 66], [617, 69, 640, 67], [618, 8, 641, 6, "console"], [618, 15, 641, 13], [618, 16, 641, 14, "log"], [618, 19, 641, 17], [618, 20, 641, 18], [618, 66, 641, 64], [618, 68, 641, 66, "blurredImageUrl"], [618, 83, 641, 81], [618, 84, 641, 82, "substring"], [618, 93, 641, 91], [618, 94, 641, 92], [618, 95, 641, 93], [618, 97, 641, 95], [618, 99, 641, 97], [618, 100, 641, 98], [618, 103, 641, 101], [618, 108, 641, 106], [618, 109, 641, 107], [619, 8, 643, 6, "setProcessingProgress"], [619, 29, 643, 27], [619, 30, 643, 28], [619, 33, 643, 31], [619, 34, 643, 32], [621, 8, 645, 6], [622, 8, 646, 6], [622, 14, 646, 12, "completeProcessing"], [622, 32, 646, 30], [622, 33, 646, 31, "blurredImageUrl"], [622, 48, 646, 46], [622, 49, 646, 47], [623, 6, 648, 4], [623, 7, 648, 5], [623, 8, 648, 6], [623, 15, 648, 13, "error"], [623, 20, 648, 18], [623, 22, 648, 20], [624, 8, 649, 6, "console"], [624, 15, 649, 13], [624, 16, 649, 14, "error"], [624, 21, 649, 19], [624, 22, 649, 20], [624, 57, 649, 55], [624, 59, 649, 57, "error"], [624, 64, 649, 62], [624, 65, 649, 63], [625, 8, 650, 6, "setErrorMessage"], [625, 23, 650, 21], [625, 24, 650, 22], [625, 50, 650, 48], [625, 51, 650, 49], [626, 8, 651, 6, "setProcessingState"], [626, 26, 651, 24], [626, 27, 651, 25], [626, 34, 651, 32], [626, 35, 651, 33], [627, 6, 652, 4], [628, 4, 653, 2], [628, 5, 653, 3], [630, 4, 655, 2], [631, 4, 656, 2], [631, 10, 656, 8, "completeProcessing"], [631, 28, 656, 26], [631, 31, 656, 29], [631, 37, 656, 36, "blurredImageUrl"], [631, 52, 656, 59], [631, 56, 656, 64], [632, 6, 657, 4], [632, 10, 657, 8], [633, 8, 658, 6, "setProcessingState"], [633, 26, 658, 24], [633, 27, 658, 25], [633, 37, 658, 35], [633, 38, 658, 36], [635, 8, 660, 6], [636, 8, 661, 6], [636, 14, 661, 12, "timestamp"], [636, 23, 661, 21], [636, 26, 661, 24, "Date"], [636, 30, 661, 28], [636, 31, 661, 29, "now"], [636, 34, 661, 32], [636, 35, 661, 33], [636, 36, 661, 34], [637, 8, 662, 6], [637, 14, 662, 12, "result"], [637, 20, 662, 18], [637, 23, 662, 21], [638, 10, 663, 8, "imageUrl"], [638, 18, 663, 16], [638, 20, 663, 18, "blurredImageUrl"], [638, 35, 663, 33], [639, 10, 664, 8, "localUri"], [639, 18, 664, 16], [639, 20, 664, 18, "blurredImageUrl"], [639, 35, 664, 33], [640, 10, 665, 8, "challengeCode"], [640, 23, 665, 21], [640, 25, 665, 23, "challengeCode"], [640, 38, 665, 36], [640, 42, 665, 40], [640, 44, 665, 42], [641, 10, 666, 8, "timestamp"], [641, 19, 666, 17], [642, 10, 667, 8, "jobId"], [642, 15, 667, 13], [642, 17, 667, 15], [642, 27, 667, 25, "timestamp"], [642, 36, 667, 34], [642, 38, 667, 36], [643, 10, 668, 8, "status"], [643, 16, 668, 14], [643, 18, 668, 16], [644, 8, 669, 6], [644, 9, 669, 7], [645, 8, 671, 6, "console"], [645, 15, 671, 13], [645, 16, 671, 14, "log"], [645, 19, 671, 17], [645, 20, 671, 18], [645, 100, 671, 98], [645, 102, 671, 100], [646, 10, 672, 8, "imageUrl"], [646, 18, 672, 16], [646, 20, 672, 18, "blurredImageUrl"], [646, 35, 672, 33], [646, 36, 672, 34, "substring"], [646, 45, 672, 43], [646, 46, 672, 44], [646, 47, 672, 45], [646, 49, 672, 47], [646, 51, 672, 49], [646, 52, 672, 50], [646, 55, 672, 53], [646, 60, 672, 58], [647, 10, 673, 8, "timestamp"], [647, 19, 673, 17], [648, 10, 674, 8, "jobId"], [648, 15, 674, 13], [648, 17, 674, 15, "result"], [648, 23, 674, 21], [648, 24, 674, 22, "jobId"], [649, 8, 675, 6], [649, 9, 675, 7], [649, 10, 675, 8], [651, 8, 677, 6], [652, 8, 678, 6, "onComplete"], [652, 18, 678, 16], [652, 19, 678, 17, "result"], [652, 25, 678, 23], [652, 26, 678, 24], [653, 6, 680, 4], [653, 7, 680, 5], [653, 8, 680, 6], [653, 15, 680, 13, "error"], [653, 20, 680, 18], [653, 22, 680, 20], [654, 8, 681, 6, "console"], [654, 15, 681, 13], [654, 16, 681, 14, "error"], [654, 21, 681, 19], [654, 22, 681, 20], [654, 57, 681, 55], [654, 59, 681, 57, "error"], [654, 64, 681, 62], [654, 65, 681, 63], [655, 8, 682, 6, "setErrorMessage"], [655, 23, 682, 21], [655, 24, 682, 22], [655, 56, 682, 54], [655, 57, 682, 55], [656, 8, 683, 6, "setProcessingState"], [656, 26, 683, 24], [656, 27, 683, 25], [656, 34, 683, 32], [656, 35, 683, 33], [657, 6, 684, 4], [658, 4, 685, 2], [658, 5, 685, 3], [660, 4, 687, 2], [661, 4, 688, 2], [661, 10, 688, 8, "triggerServerProcessing"], [661, 33, 688, 31], [661, 36, 688, 34], [661, 42, 688, 34, "triggerServerProcessing"], [661, 43, 688, 41, "privateImageUrl"], [661, 58, 688, 64], [661, 60, 688, 66, "timestamp"], [661, 69, 688, 83], [661, 74, 688, 88], [662, 6, 689, 4], [662, 10, 689, 8], [663, 8, 690, 6, "console"], [663, 15, 690, 13], [663, 16, 690, 14, "log"], [663, 19, 690, 17], [663, 20, 690, 18], [663, 74, 690, 72], [663, 76, 690, 74, "privateImageUrl"], [663, 91, 690, 89], [663, 92, 690, 90], [664, 8, 691, 6, "setProcessingState"], [664, 26, 691, 24], [664, 27, 691, 25], [664, 39, 691, 37], [664, 40, 691, 38], [665, 8, 692, 6, "setProcessingProgress"], [665, 29, 692, 27], [665, 30, 692, 28], [665, 32, 692, 30], [665, 33, 692, 31], [666, 8, 694, 6], [666, 14, 694, 12, "requestBody"], [666, 25, 694, 23], [666, 28, 694, 26], [667, 10, 695, 8, "imageUrl"], [667, 18, 695, 16], [667, 20, 695, 18, "privateImageUrl"], [667, 35, 695, 33], [668, 10, 696, 8, "userId"], [668, 16, 696, 14], [669, 10, 697, 8, "requestId"], [669, 19, 697, 17], [670, 10, 698, 8, "timestamp"], [670, 19, 698, 17], [671, 10, 699, 8, "platform"], [671, 18, 699, 16], [671, 20, 699, 18], [672, 8, 700, 6], [672, 9, 700, 7], [673, 8, 702, 6, "console"], [673, 15, 702, 13], [673, 16, 702, 14, "log"], [673, 19, 702, 17], [673, 20, 702, 18], [673, 65, 702, 63], [673, 67, 702, 65, "requestBody"], [673, 78, 702, 76], [673, 79, 702, 77], [675, 8, 704, 6], [676, 8, 705, 6], [676, 14, 705, 12, "response"], [676, 22, 705, 20], [676, 25, 705, 23], [676, 31, 705, 29, "fetch"], [676, 36, 705, 34], [676, 37, 705, 35], [676, 40, 705, 38, "API_BASE_URL"], [676, 52, 705, 50], [676, 72, 705, 70], [676, 74, 705, 72], [677, 10, 706, 8, "method"], [677, 16, 706, 14], [677, 18, 706, 16], [677, 24, 706, 22], [678, 10, 707, 8, "headers"], [678, 17, 707, 15], [678, 19, 707, 17], [679, 12, 708, 10], [679, 26, 708, 24], [679, 28, 708, 26], [679, 46, 708, 44], [680, 12, 709, 10], [680, 27, 709, 25], [680, 29, 709, 27], [680, 39, 709, 37], [680, 45, 709, 43, "getAuthToken"], [680, 57, 709, 55], [680, 58, 709, 56], [680, 59, 709, 57], [681, 10, 710, 8], [681, 11, 710, 9], [682, 10, 711, 8, "body"], [682, 14, 711, 12], [682, 16, 711, 14, "JSON"], [682, 20, 711, 18], [682, 21, 711, 19, "stringify"], [682, 30, 711, 28], [682, 31, 711, 29, "requestBody"], [682, 42, 711, 40], [683, 8, 712, 6], [683, 9, 712, 7], [683, 10, 712, 8], [684, 8, 714, 6], [684, 12, 714, 10], [684, 13, 714, 11, "response"], [684, 21, 714, 19], [684, 22, 714, 20, "ok"], [684, 24, 714, 22], [684, 26, 714, 24], [685, 10, 715, 8], [685, 16, 715, 14, "errorText"], [685, 25, 715, 23], [685, 28, 715, 26], [685, 34, 715, 32, "response"], [685, 42, 715, 40], [685, 43, 715, 41, "text"], [685, 47, 715, 45], [685, 48, 715, 46], [685, 49, 715, 47], [686, 10, 716, 8, "console"], [686, 17, 716, 15], [686, 18, 716, 16, "error"], [686, 23, 716, 21], [686, 24, 716, 22], [686, 68, 716, 66], [686, 70, 716, 68, "response"], [686, 78, 716, 76], [686, 79, 716, 77, "status"], [686, 85, 716, 83], [686, 87, 716, 85, "errorText"], [686, 96, 716, 94], [686, 97, 716, 95], [687, 10, 717, 8], [687, 16, 717, 14], [687, 20, 717, 18, "Error"], [687, 25, 717, 23], [687, 26, 717, 24], [687, 48, 717, 46, "response"], [687, 56, 717, 54], [687, 57, 717, 55, "status"], [687, 63, 717, 61], [687, 67, 717, 65, "response"], [687, 75, 717, 73], [687, 76, 717, 74, "statusText"], [687, 86, 717, 84], [687, 88, 717, 86], [687, 89, 717, 87], [688, 8, 718, 6], [689, 8, 720, 6], [689, 14, 720, 12, "result"], [689, 20, 720, 18], [689, 23, 720, 21], [689, 29, 720, 27, "response"], [689, 37, 720, 35], [689, 38, 720, 36, "json"], [689, 42, 720, 40], [689, 43, 720, 41], [689, 44, 720, 42], [690, 8, 721, 6, "console"], [690, 15, 721, 13], [690, 16, 721, 14, "log"], [690, 19, 721, 17], [690, 20, 721, 18], [690, 68, 721, 66], [690, 70, 721, 68, "result"], [690, 76, 721, 74], [690, 77, 721, 75], [691, 8, 723, 6], [691, 12, 723, 10], [691, 13, 723, 11, "result"], [691, 19, 723, 17], [691, 20, 723, 18, "jobId"], [691, 25, 723, 23], [691, 27, 723, 25], [692, 10, 724, 8], [692, 16, 724, 14], [692, 20, 724, 18, "Error"], [692, 25, 724, 23], [692, 26, 724, 24], [692, 70, 724, 68], [692, 71, 724, 69], [693, 8, 725, 6], [695, 8, 727, 6], [696, 8, 728, 6], [696, 14, 728, 12, "pollForCompletion"], [696, 31, 728, 29], [696, 32, 728, 30, "result"], [696, 38, 728, 36], [696, 39, 728, 37, "jobId"], [696, 44, 728, 42], [696, 46, 728, 44, "timestamp"], [696, 55, 728, 53], [696, 56, 728, 54], [697, 6, 729, 4], [697, 7, 729, 5], [697, 8, 729, 6], [697, 15, 729, 13, "error"], [697, 20, 729, 18], [697, 22, 729, 20], [698, 8, 730, 6, "console"], [698, 15, 730, 13], [698, 16, 730, 14, "error"], [698, 21, 730, 19], [698, 22, 730, 20], [698, 57, 730, 55], [698, 59, 730, 57, "error"], [698, 64, 730, 62], [698, 65, 730, 63], [699, 8, 731, 6, "setErrorMessage"], [699, 23, 731, 21], [699, 24, 731, 22], [699, 52, 731, 50, "error"], [699, 57, 731, 55], [699, 58, 731, 56, "message"], [699, 65, 731, 63], [699, 67, 731, 65], [699, 68, 731, 66], [700, 8, 732, 6, "setProcessingState"], [700, 26, 732, 24], [700, 27, 732, 25], [700, 34, 732, 32], [700, 35, 732, 33], [701, 6, 733, 4], [702, 4, 734, 2], [702, 5, 734, 3], [703, 4, 735, 2], [704, 4, 736, 2], [704, 10, 736, 8, "pollForCompletion"], [704, 27, 736, 25], [704, 30, 736, 28], [704, 36, 736, 28, "pollForCompletion"], [704, 37, 736, 35, "jobId"], [704, 42, 736, 48], [704, 44, 736, 50, "timestamp"], [704, 53, 736, 67], [704, 55, 736, 69, "attempts"], [704, 63, 736, 77], [704, 66, 736, 80], [704, 67, 736, 81], [704, 72, 736, 86], [705, 6, 737, 4], [705, 12, 737, 10, "MAX_ATTEMPTS"], [705, 24, 737, 22], [705, 27, 737, 25], [705, 29, 737, 27], [705, 30, 737, 28], [705, 31, 737, 29], [706, 6, 738, 4], [706, 12, 738, 10, "POLL_INTERVAL"], [706, 25, 738, 23], [706, 28, 738, 26], [706, 32, 738, 30], [706, 33, 738, 31], [706, 34, 738, 32], [708, 6, 740, 4, "console"], [708, 13, 740, 11], [708, 14, 740, 12, "log"], [708, 17, 740, 15], [708, 18, 740, 16], [708, 53, 740, 51, "attempts"], [708, 61, 740, 59], [708, 64, 740, 62], [708, 65, 740, 63], [708, 69, 740, 67, "MAX_ATTEMPTS"], [708, 81, 740, 79], [708, 93, 740, 91, "jobId"], [708, 98, 740, 96], [708, 100, 740, 98], [708, 101, 740, 99], [709, 6, 742, 4], [709, 10, 742, 8, "attempts"], [709, 18, 742, 16], [709, 22, 742, 20, "MAX_ATTEMPTS"], [709, 34, 742, 32], [709, 36, 742, 34], [710, 8, 743, 6, "console"], [710, 15, 743, 13], [710, 16, 743, 14, "error"], [710, 21, 743, 19], [710, 22, 743, 20], [710, 75, 743, 73], [710, 76, 743, 74], [711, 8, 744, 6, "setErrorMessage"], [711, 23, 744, 21], [711, 24, 744, 22], [711, 63, 744, 61], [711, 64, 744, 62], [712, 8, 745, 6, "setProcessingState"], [712, 26, 745, 24], [712, 27, 745, 25], [712, 34, 745, 32], [712, 35, 745, 33], [713, 8, 746, 6], [714, 6, 747, 4], [715, 6, 749, 4], [715, 10, 749, 8], [716, 8, 750, 6], [716, 14, 750, 12, "response"], [716, 22, 750, 20], [716, 25, 750, 23], [716, 31, 750, 29, "fetch"], [716, 36, 750, 34], [716, 37, 750, 35], [716, 40, 750, 38, "API_BASE_URL"], [716, 52, 750, 50], [716, 75, 750, 73, "jobId"], [716, 80, 750, 78], [716, 82, 750, 80], [716, 84, 750, 82], [717, 10, 751, 8, "headers"], [717, 17, 751, 15], [717, 19, 751, 17], [718, 12, 752, 10], [718, 27, 752, 25], [718, 29, 752, 27], [718, 39, 752, 37], [718, 45, 752, 43, "getAuthToken"], [718, 57, 752, 55], [718, 58, 752, 56], [718, 59, 752, 57], [719, 10, 753, 8], [720, 8, 754, 6], [720, 9, 754, 7], [720, 10, 754, 8], [721, 8, 756, 6], [721, 12, 756, 10], [721, 13, 756, 11, "response"], [721, 21, 756, 19], [721, 22, 756, 20, "ok"], [721, 24, 756, 22], [721, 26, 756, 24], [722, 10, 757, 8], [722, 16, 757, 14], [722, 20, 757, 18, "Error"], [722, 25, 757, 23], [722, 26, 757, 24], [722, 34, 757, 32, "response"], [722, 42, 757, 40], [722, 43, 757, 41, "status"], [722, 49, 757, 47], [722, 54, 757, 52, "response"], [722, 62, 757, 60], [722, 63, 757, 61, "statusText"], [722, 73, 757, 71], [722, 75, 757, 73], [722, 76, 757, 74], [723, 8, 758, 6], [724, 8, 760, 6], [724, 14, 760, 12, "status"], [724, 20, 760, 18], [724, 23, 760, 21], [724, 29, 760, 27, "response"], [724, 37, 760, 35], [724, 38, 760, 36, "json"], [724, 42, 760, 40], [724, 43, 760, 41], [724, 44, 760, 42], [725, 8, 761, 6, "console"], [725, 15, 761, 13], [725, 16, 761, 14, "log"], [725, 19, 761, 17], [725, 20, 761, 18], [725, 54, 761, 52], [725, 56, 761, 54, "status"], [725, 62, 761, 60], [725, 63, 761, 61], [726, 8, 763, 6], [726, 12, 763, 10, "status"], [726, 18, 763, 16], [726, 19, 763, 17, "status"], [726, 25, 763, 23], [726, 30, 763, 28], [726, 41, 763, 39], [726, 43, 763, 41], [727, 10, 764, 8, "console"], [727, 17, 764, 15], [727, 18, 764, 16, "log"], [727, 21, 764, 19], [727, 22, 764, 20], [727, 73, 764, 71], [727, 74, 764, 72], [728, 10, 765, 8, "setProcessingProgress"], [728, 31, 765, 29], [728, 32, 765, 30], [728, 35, 765, 33], [728, 36, 765, 34], [729, 10, 766, 8, "setProcessingState"], [729, 28, 766, 26], [729, 29, 766, 27], [729, 40, 766, 38], [729, 41, 766, 39], [730, 10, 767, 8], [731, 10, 768, 8], [731, 16, 768, 14, "result"], [731, 22, 768, 20], [731, 25, 768, 23], [732, 12, 769, 10, "imageUrl"], [732, 20, 769, 18], [732, 22, 769, 20, "status"], [732, 28, 769, 26], [732, 29, 769, 27, "publicUrl"], [732, 38, 769, 36], [733, 12, 769, 38], [734, 12, 770, 10, "localUri"], [734, 20, 770, 18], [734, 22, 770, 20, "capturedPhoto"], [734, 35, 770, 33], [734, 39, 770, 37, "status"], [734, 45, 770, 43], [734, 46, 770, 44, "publicUrl"], [734, 55, 770, 53], [735, 12, 770, 55], [736, 12, 771, 10, "challengeCode"], [736, 25, 771, 23], [736, 27, 771, 25, "challengeCode"], [736, 40, 771, 38], [736, 44, 771, 42], [736, 46, 771, 44], [737, 12, 772, 10, "timestamp"], [737, 21, 772, 19], [738, 12, 773, 10, "processingStatus"], [738, 28, 773, 26], [738, 30, 773, 28], [739, 10, 774, 8], [739, 11, 774, 9], [740, 10, 775, 8, "console"], [740, 17, 775, 15], [740, 18, 775, 16, "log"], [740, 21, 775, 19], [740, 22, 775, 20], [740, 57, 775, 55], [740, 59, 775, 57, "result"], [740, 65, 775, 63], [740, 66, 775, 64], [741, 10, 776, 8, "onComplete"], [741, 20, 776, 18], [741, 21, 776, 19, "result"], [741, 27, 776, 25], [741, 28, 776, 26], [742, 10, 777, 8], [743, 8, 778, 6], [743, 9, 778, 7], [743, 15, 778, 13], [743, 19, 778, 17, "status"], [743, 25, 778, 23], [743, 26, 778, 24, "status"], [743, 32, 778, 30], [743, 37, 778, 35], [743, 45, 778, 43], [743, 47, 778, 45], [744, 10, 779, 8, "console"], [744, 17, 779, 15], [744, 18, 779, 16, "error"], [744, 23, 779, 21], [744, 24, 779, 22], [744, 60, 779, 58], [744, 62, 779, 60, "status"], [744, 68, 779, 66], [744, 69, 779, 67, "error"], [744, 74, 779, 72], [744, 75, 779, 73], [745, 10, 780, 8], [745, 16, 780, 14], [745, 20, 780, 18, "Error"], [745, 25, 780, 23], [745, 26, 780, 24, "status"], [745, 32, 780, 30], [745, 33, 780, 31, "error"], [745, 38, 780, 36], [745, 42, 780, 40], [745, 61, 780, 59], [745, 62, 780, 60], [746, 8, 781, 6], [746, 9, 781, 7], [746, 15, 781, 13], [747, 10, 782, 8], [748, 10, 783, 8], [748, 16, 783, 14, "progressValue"], [748, 29, 783, 27], [748, 32, 783, 30], [748, 34, 783, 32], [748, 37, 783, 36, "attempts"], [748, 45, 783, 44], [748, 48, 783, 47, "MAX_ATTEMPTS"], [748, 60, 783, 59], [748, 63, 783, 63], [748, 65, 783, 65], [749, 10, 784, 8, "console"], [749, 17, 784, 15], [749, 18, 784, 16, "log"], [749, 21, 784, 19], [749, 22, 784, 20], [749, 71, 784, 69, "progressValue"], [749, 84, 784, 82], [749, 87, 784, 85], [749, 88, 784, 86], [750, 10, 785, 8, "setProcessingProgress"], [750, 31, 785, 29], [750, 32, 785, 30, "progressValue"], [750, 45, 785, 43], [750, 46, 785, 44], [751, 10, 787, 8, "setTimeout"], [751, 20, 787, 18], [751, 21, 787, 19], [751, 27, 787, 25], [752, 12, 788, 10, "pollForCompletion"], [752, 29, 788, 27], [752, 30, 788, 28, "jobId"], [752, 35, 788, 33], [752, 37, 788, 35, "timestamp"], [752, 46, 788, 44], [752, 48, 788, 46, "attempts"], [752, 56, 788, 54], [752, 59, 788, 57], [752, 60, 788, 58], [752, 61, 788, 59], [753, 10, 789, 8], [753, 11, 789, 9], [753, 13, 789, 11, "POLL_INTERVAL"], [753, 26, 789, 24], [753, 27, 789, 25], [754, 8, 790, 6], [755, 6, 791, 4], [755, 7, 791, 5], [755, 8, 791, 6], [755, 15, 791, 13, "error"], [755, 20, 791, 18], [755, 22, 791, 20], [756, 8, 792, 6, "console"], [756, 15, 792, 13], [756, 16, 792, 14, "error"], [756, 21, 792, 19], [756, 22, 792, 20], [756, 54, 792, 52], [756, 56, 792, 54, "error"], [756, 61, 792, 59], [756, 62, 792, 60], [757, 8, 793, 6, "setErrorMessage"], [757, 23, 793, 21], [757, 24, 793, 22], [757, 62, 793, 60, "error"], [757, 67, 793, 65], [757, 68, 793, 66, "message"], [757, 75, 793, 73], [757, 77, 793, 75], [757, 78, 793, 76], [758, 8, 794, 6, "setProcessingState"], [758, 26, 794, 24], [758, 27, 794, 25], [758, 34, 794, 32], [758, 35, 794, 33], [759, 6, 795, 4], [760, 4, 796, 2], [760, 5, 796, 3], [761, 4, 797, 2], [762, 4, 798, 2], [762, 10, 798, 8, "getAuthToken"], [762, 22, 798, 20], [762, 25, 798, 23], [762, 31, 798, 23, "getAuthToken"], [762, 32, 798, 23], [762, 37, 798, 52], [763, 6, 799, 4], [764, 6, 800, 4], [765, 6, 801, 4], [765, 13, 801, 11], [765, 30, 801, 28], [766, 4, 802, 2], [766, 5, 802, 3], [768, 4, 804, 2], [769, 4, 805, 2], [769, 10, 805, 8, "retryCapture"], [769, 22, 805, 20], [769, 25, 805, 23], [769, 29, 805, 23, "useCallback"], [769, 47, 805, 34], [769, 49, 805, 35], [769, 55, 805, 41], [770, 6, 806, 4, "console"], [770, 13, 806, 11], [770, 14, 806, 12, "log"], [770, 17, 806, 15], [770, 18, 806, 16], [770, 55, 806, 53], [770, 56, 806, 54], [771, 6, 807, 4, "setProcessingState"], [771, 24, 807, 22], [771, 25, 807, 23], [771, 31, 807, 29], [771, 32, 807, 30], [772, 6, 808, 4, "setErrorMessage"], [772, 21, 808, 19], [772, 22, 808, 20], [772, 24, 808, 22], [772, 25, 808, 23], [773, 6, 809, 4, "setCapturedPhoto"], [773, 22, 809, 20], [773, 23, 809, 21], [773, 25, 809, 23], [773, 26, 809, 24], [774, 6, 810, 4, "setProcessingProgress"], [774, 27, 810, 25], [774, 28, 810, 26], [774, 29, 810, 27], [774, 30, 810, 28], [775, 4, 811, 2], [775, 5, 811, 3], [775, 7, 811, 5], [775, 9, 811, 7], [775, 10, 811, 8], [776, 4, 812, 2], [777, 4, 813, 2], [777, 8, 813, 2, "useEffect"], [777, 24, 813, 11], [777, 26, 813, 12], [777, 32, 813, 18], [778, 6, 814, 4, "console"], [778, 13, 814, 11], [778, 14, 814, 12, "log"], [778, 17, 814, 15], [778, 18, 814, 16], [778, 53, 814, 51], [778, 55, 814, 53, "permission"], [778, 65, 814, 63], [778, 66, 814, 64], [779, 6, 815, 4], [779, 10, 815, 8, "permission"], [779, 20, 815, 18], [779, 22, 815, 20], [780, 8, 816, 6, "console"], [780, 15, 816, 13], [780, 16, 816, 14, "log"], [780, 19, 816, 17], [780, 20, 816, 18], [780, 57, 816, 55], [780, 59, 816, 57, "permission"], [780, 69, 816, 67], [780, 70, 816, 68, "granted"], [780, 77, 816, 75], [780, 78, 816, 76], [781, 6, 817, 4], [782, 4, 818, 2], [782, 5, 818, 3], [782, 7, 818, 5], [782, 8, 818, 6, "permission"], [782, 18, 818, 16], [782, 19, 818, 17], [782, 20, 818, 18], [783, 4, 819, 2], [784, 4, 820, 2], [784, 8, 820, 6], [784, 9, 820, 7, "permission"], [784, 19, 820, 17], [784, 21, 820, 19], [785, 6, 821, 4, "console"], [785, 13, 821, 11], [785, 14, 821, 12, "log"], [785, 17, 821, 15], [785, 18, 821, 16], [785, 67, 821, 65], [785, 68, 821, 66], [786, 6, 822, 4], [786, 26, 823, 6], [786, 30, 823, 6, "_jsxDevRuntime"], [786, 44, 823, 6], [786, 45, 823, 6, "jsxDEV"], [786, 51, 823, 6], [786, 53, 823, 7, "_View"], [786, 58, 823, 7], [786, 59, 823, 7, "default"], [786, 66, 823, 11], [787, 8, 823, 12, "style"], [787, 13, 823, 17], [787, 15, 823, 19, "styles"], [787, 21, 823, 25], [787, 22, 823, 26, "container"], [787, 31, 823, 36], [788, 8, 823, 36, "children"], [788, 16, 823, 36], [788, 32, 824, 8], [788, 36, 824, 8, "_jsxDevRuntime"], [788, 50, 824, 8], [788, 51, 824, 8, "jsxDEV"], [788, 57, 824, 8], [788, 59, 824, 9, "_ActivityIndicator"], [788, 77, 824, 9], [788, 78, 824, 9, "default"], [788, 85, 824, 26], [789, 10, 824, 27, "size"], [789, 14, 824, 31], [789, 16, 824, 32], [789, 23, 824, 39], [790, 10, 824, 40, "color"], [790, 15, 824, 45], [790, 17, 824, 46], [791, 8, 824, 55], [792, 10, 824, 55, "fileName"], [792, 18, 824, 55], [792, 20, 824, 55, "_jsxFileName"], [792, 32, 824, 55], [793, 10, 824, 55, "lineNumber"], [793, 20, 824, 55], [794, 10, 824, 55, "columnNumber"], [794, 22, 824, 55], [795, 8, 824, 55], [795, 15, 824, 57], [795, 16, 824, 58], [795, 31, 825, 8], [795, 35, 825, 8, "_jsxDevRuntime"], [795, 49, 825, 8], [795, 50, 825, 8, "jsxDEV"], [795, 56, 825, 8], [795, 58, 825, 9, "_Text"], [795, 63, 825, 9], [795, 64, 825, 9, "default"], [795, 71, 825, 13], [796, 10, 825, 14, "style"], [796, 15, 825, 19], [796, 17, 825, 21, "styles"], [796, 23, 825, 27], [796, 24, 825, 28, "loadingText"], [796, 35, 825, 40], [797, 10, 825, 40, "children"], [797, 18, 825, 40], [797, 20, 825, 41], [798, 8, 825, 58], [799, 10, 825, 58, "fileName"], [799, 18, 825, 58], [799, 20, 825, 58, "_jsxFileName"], [799, 32, 825, 58], [800, 10, 825, 58, "lineNumber"], [800, 20, 825, 58], [801, 10, 825, 58, "columnNumber"], [801, 22, 825, 58], [802, 8, 825, 58], [802, 15, 825, 64], [802, 16, 825, 65], [803, 6, 825, 65], [804, 8, 825, 65, "fileName"], [804, 16, 825, 65], [804, 18, 825, 65, "_jsxFileName"], [804, 30, 825, 65], [805, 8, 825, 65, "lineNumber"], [805, 18, 825, 65], [806, 8, 825, 65, "columnNumber"], [806, 20, 825, 65], [807, 6, 825, 65], [807, 13, 826, 12], [807, 14, 826, 13], [808, 4, 828, 2], [809, 4, 829, 2], [809, 8, 829, 6], [809, 9, 829, 7, "permission"], [809, 19, 829, 17], [809, 20, 829, 18, "granted"], [809, 27, 829, 25], [809, 29, 829, 27], [810, 6, 830, 4, "console"], [810, 13, 830, 11], [810, 14, 830, 12, "log"], [810, 17, 830, 15], [810, 18, 830, 16], [810, 93, 830, 91], [810, 94, 830, 92], [811, 6, 831, 4], [811, 26, 832, 6], [811, 30, 832, 6, "_jsxDevRuntime"], [811, 44, 832, 6], [811, 45, 832, 6, "jsxDEV"], [811, 51, 832, 6], [811, 53, 832, 7, "_View"], [811, 58, 832, 7], [811, 59, 832, 7, "default"], [811, 66, 832, 11], [812, 8, 832, 12, "style"], [812, 13, 832, 17], [812, 15, 832, 19, "styles"], [812, 21, 832, 25], [812, 22, 832, 26, "container"], [812, 31, 832, 36], [813, 8, 832, 36, "children"], [813, 16, 832, 36], [813, 31, 833, 8], [813, 35, 833, 8, "_jsxDevRuntime"], [813, 49, 833, 8], [813, 50, 833, 8, "jsxDEV"], [813, 56, 833, 8], [813, 58, 833, 9, "_View"], [813, 63, 833, 9], [813, 64, 833, 9, "default"], [813, 71, 833, 13], [814, 10, 833, 14, "style"], [814, 15, 833, 19], [814, 17, 833, 21, "styles"], [814, 23, 833, 27], [814, 24, 833, 28, "permissionContent"], [814, 41, 833, 46], [815, 10, 833, 46, "children"], [815, 18, 833, 46], [815, 34, 834, 10], [815, 38, 834, 10, "_jsxDevRuntime"], [815, 52, 834, 10], [815, 53, 834, 10, "jsxDEV"], [815, 59, 834, 10], [815, 61, 834, 11, "_lucideReactNative"], [815, 79, 834, 11], [815, 80, 834, 11, "Camera"], [815, 86, 834, 21], [816, 12, 834, 22, "size"], [816, 16, 834, 26], [816, 18, 834, 28], [816, 20, 834, 31], [817, 12, 834, 32, "color"], [817, 17, 834, 37], [817, 19, 834, 38], [818, 10, 834, 47], [819, 12, 834, 47, "fileName"], [819, 20, 834, 47], [819, 22, 834, 47, "_jsxFileName"], [819, 34, 834, 47], [820, 12, 834, 47, "lineNumber"], [820, 22, 834, 47], [821, 12, 834, 47, "columnNumber"], [821, 24, 834, 47], [822, 10, 834, 47], [822, 17, 834, 49], [822, 18, 834, 50], [822, 33, 835, 10], [822, 37, 835, 10, "_jsxDevRuntime"], [822, 51, 835, 10], [822, 52, 835, 10, "jsxDEV"], [822, 58, 835, 10], [822, 60, 835, 11, "_Text"], [822, 65, 835, 11], [822, 66, 835, 11, "default"], [822, 73, 835, 15], [823, 12, 835, 16, "style"], [823, 17, 835, 21], [823, 19, 835, 23, "styles"], [823, 25, 835, 29], [823, 26, 835, 30, "permissionTitle"], [823, 41, 835, 46], [824, 12, 835, 46, "children"], [824, 20, 835, 46], [824, 22, 835, 47], [825, 10, 835, 73], [826, 12, 835, 73, "fileName"], [826, 20, 835, 73], [826, 22, 835, 73, "_jsxFileName"], [826, 34, 835, 73], [827, 12, 835, 73, "lineNumber"], [827, 22, 835, 73], [828, 12, 835, 73, "columnNumber"], [828, 24, 835, 73], [829, 10, 835, 73], [829, 17, 835, 79], [829, 18, 835, 80], [829, 33, 836, 10], [829, 37, 836, 10, "_jsxDevRuntime"], [829, 51, 836, 10], [829, 52, 836, 10, "jsxDEV"], [829, 58, 836, 10], [829, 60, 836, 11, "_Text"], [829, 65, 836, 11], [829, 66, 836, 11, "default"], [829, 73, 836, 15], [830, 12, 836, 16, "style"], [830, 17, 836, 21], [830, 19, 836, 23, "styles"], [830, 25, 836, 29], [830, 26, 836, 30, "permissionDescription"], [830, 47, 836, 52], [831, 12, 836, 52, "children"], [831, 20, 836, 52], [831, 22, 836, 53], [832, 10, 839, 10], [833, 12, 839, 10, "fileName"], [833, 20, 839, 10], [833, 22, 839, 10, "_jsxFileName"], [833, 34, 839, 10], [834, 12, 839, 10, "lineNumber"], [834, 22, 839, 10], [835, 12, 839, 10, "columnNumber"], [835, 24, 839, 10], [836, 10, 839, 10], [836, 17, 839, 16], [836, 18, 839, 17], [836, 33, 840, 10], [836, 37, 840, 10, "_jsxDevRuntime"], [836, 51, 840, 10], [836, 52, 840, 10, "jsxDEV"], [836, 58, 840, 10], [836, 60, 840, 11, "_TouchableOpacity"], [836, 77, 840, 11], [836, 78, 840, 11, "default"], [836, 85, 840, 27], [837, 12, 840, 28, "onPress"], [837, 19, 840, 35], [837, 21, 840, 37, "requestPermission"], [837, 38, 840, 55], [838, 12, 840, 56, "style"], [838, 17, 840, 61], [838, 19, 840, 63, "styles"], [838, 25, 840, 69], [838, 26, 840, 70, "primaryButton"], [838, 39, 840, 84], [839, 12, 840, 84, "children"], [839, 20, 840, 84], [839, 35, 841, 12], [839, 39, 841, 12, "_jsxDevRuntime"], [839, 53, 841, 12], [839, 54, 841, 12, "jsxDEV"], [839, 60, 841, 12], [839, 62, 841, 13, "_Text"], [839, 67, 841, 13], [839, 68, 841, 13, "default"], [839, 75, 841, 17], [840, 14, 841, 18, "style"], [840, 19, 841, 23], [840, 21, 841, 25, "styles"], [840, 27, 841, 31], [840, 28, 841, 32, "primaryButtonText"], [840, 45, 841, 50], [841, 14, 841, 50, "children"], [841, 22, 841, 50], [841, 24, 841, 51], [842, 12, 841, 67], [843, 14, 841, 67, "fileName"], [843, 22, 841, 67], [843, 24, 841, 67, "_jsxFileName"], [843, 36, 841, 67], [844, 14, 841, 67, "lineNumber"], [844, 24, 841, 67], [845, 14, 841, 67, "columnNumber"], [845, 26, 841, 67], [846, 12, 841, 67], [846, 19, 841, 73], [847, 10, 841, 74], [848, 12, 841, 74, "fileName"], [848, 20, 841, 74], [848, 22, 841, 74, "_jsxFileName"], [848, 34, 841, 74], [849, 12, 841, 74, "lineNumber"], [849, 22, 841, 74], [850, 12, 841, 74, "columnNumber"], [850, 24, 841, 74], [851, 10, 841, 74], [851, 17, 842, 28], [851, 18, 842, 29], [851, 33, 843, 10], [851, 37, 843, 10, "_jsxDevRuntime"], [851, 51, 843, 10], [851, 52, 843, 10, "jsxDEV"], [851, 58, 843, 10], [851, 60, 843, 11, "_TouchableOpacity"], [851, 77, 843, 11], [851, 78, 843, 11, "default"], [851, 85, 843, 27], [852, 12, 843, 28, "onPress"], [852, 19, 843, 35], [852, 21, 843, 37, "onCancel"], [852, 29, 843, 46], [853, 12, 843, 47, "style"], [853, 17, 843, 52], [853, 19, 843, 54, "styles"], [853, 25, 843, 60], [853, 26, 843, 61, "secondaryButton"], [853, 41, 843, 77], [854, 12, 843, 77, "children"], [854, 20, 843, 77], [854, 35, 844, 12], [854, 39, 844, 12, "_jsxDevRuntime"], [854, 53, 844, 12], [854, 54, 844, 12, "jsxDEV"], [854, 60, 844, 12], [854, 62, 844, 13, "_Text"], [854, 67, 844, 13], [854, 68, 844, 13, "default"], [854, 75, 844, 17], [855, 14, 844, 18, "style"], [855, 19, 844, 23], [855, 21, 844, 25, "styles"], [855, 27, 844, 31], [855, 28, 844, 32, "secondaryButtonText"], [855, 47, 844, 52], [856, 14, 844, 52, "children"], [856, 22, 844, 52], [856, 24, 844, 53], [857, 12, 844, 59], [858, 14, 844, 59, "fileName"], [858, 22, 844, 59], [858, 24, 844, 59, "_jsxFileName"], [858, 36, 844, 59], [859, 14, 844, 59, "lineNumber"], [859, 24, 844, 59], [860, 14, 844, 59, "columnNumber"], [860, 26, 844, 59], [861, 12, 844, 59], [861, 19, 844, 65], [862, 10, 844, 66], [863, 12, 844, 66, "fileName"], [863, 20, 844, 66], [863, 22, 844, 66, "_jsxFileName"], [863, 34, 844, 66], [864, 12, 844, 66, "lineNumber"], [864, 22, 844, 66], [865, 12, 844, 66, "columnNumber"], [865, 24, 844, 66], [866, 10, 844, 66], [866, 17, 845, 28], [866, 18, 845, 29], [867, 8, 845, 29], [868, 10, 845, 29, "fileName"], [868, 18, 845, 29], [868, 20, 845, 29, "_jsxFileName"], [868, 32, 845, 29], [869, 10, 845, 29, "lineNumber"], [869, 20, 845, 29], [870, 10, 845, 29, "columnNumber"], [870, 22, 845, 29], [871, 8, 845, 29], [871, 15, 846, 14], [872, 6, 846, 15], [873, 8, 846, 15, "fileName"], [873, 16, 846, 15], [873, 18, 846, 15, "_jsxFileName"], [873, 30, 846, 15], [874, 8, 846, 15, "lineNumber"], [874, 18, 846, 15], [875, 8, 846, 15, "columnNumber"], [875, 20, 846, 15], [876, 6, 846, 15], [876, 13, 847, 12], [876, 14, 847, 13], [877, 4, 849, 2], [878, 4, 850, 2], [879, 4, 851, 2, "console"], [879, 11, 851, 9], [879, 12, 851, 10, "log"], [879, 15, 851, 13], [879, 16, 851, 14], [879, 55, 851, 53], [879, 56, 851, 54], [880, 4, 853, 2], [880, 24, 854, 4], [880, 28, 854, 4, "_jsxDevRuntime"], [880, 42, 854, 4], [880, 43, 854, 4, "jsxDEV"], [880, 49, 854, 4], [880, 51, 854, 5, "_View"], [880, 56, 854, 5], [880, 57, 854, 5, "default"], [880, 64, 854, 9], [881, 6, 854, 10, "style"], [881, 11, 854, 15], [881, 13, 854, 17, "styles"], [881, 19, 854, 23], [881, 20, 854, 24, "container"], [881, 29, 854, 34], [882, 6, 854, 34, "children"], [882, 14, 854, 34], [882, 30, 856, 6], [882, 34, 856, 6, "_jsxDevRuntime"], [882, 48, 856, 6], [882, 49, 856, 6, "jsxDEV"], [882, 55, 856, 6], [882, 57, 856, 7, "_View"], [882, 62, 856, 7], [882, 63, 856, 7, "default"], [882, 70, 856, 11], [883, 8, 856, 12, "style"], [883, 13, 856, 17], [883, 15, 856, 19, "styles"], [883, 21, 856, 25], [883, 22, 856, 26, "cameraContainer"], [883, 37, 856, 42], [884, 8, 856, 43, "id"], [884, 10, 856, 45], [884, 12, 856, 46], [884, 29, 856, 63], [885, 8, 856, 63, "children"], [885, 16, 856, 63], [885, 32, 857, 8], [885, 36, 857, 8, "_jsxDevRuntime"], [885, 50, 857, 8], [885, 51, 857, 8, "jsxDEV"], [885, 57, 857, 8], [885, 59, 857, 9, "_expoCamera"], [885, 70, 857, 9], [885, 71, 857, 9, "CameraView"], [885, 81, 857, 19], [886, 10, 858, 10, "ref"], [886, 13, 858, 13], [886, 15, 858, 15, "cameraRef"], [886, 24, 858, 25], [887, 10, 859, 10, "style"], [887, 15, 859, 15], [887, 17, 859, 17], [887, 18, 859, 18, "styles"], [887, 24, 859, 24], [887, 25, 859, 25, "camera"], [887, 31, 859, 31], [887, 33, 859, 33], [888, 12, 859, 35, "backgroundColor"], [888, 27, 859, 50], [888, 29, 859, 52], [889, 10, 859, 62], [889, 11, 859, 63], [889, 12, 859, 65], [890, 10, 860, 10, "facing"], [890, 16, 860, 16], [890, 18, 860, 17], [890, 24, 860, 23], [891, 10, 861, 10, "onLayout"], [891, 18, 861, 18], [891, 20, 861, 21, "e"], [891, 21, 861, 22], [891, 25, 861, 27], [892, 12, 862, 12, "console"], [892, 19, 862, 19], [892, 20, 862, 20, "log"], [892, 23, 862, 23], [892, 24, 862, 24], [892, 56, 862, 56], [892, 58, 862, 58, "e"], [892, 59, 862, 59], [892, 60, 862, 60, "nativeEvent"], [892, 71, 862, 71], [892, 72, 862, 72, "layout"], [892, 78, 862, 78], [892, 79, 862, 79], [893, 12, 863, 12, "setViewSize"], [893, 23, 863, 23], [893, 24, 863, 24], [894, 14, 863, 26, "width"], [894, 19, 863, 31], [894, 21, 863, 33, "e"], [894, 22, 863, 34], [894, 23, 863, 35, "nativeEvent"], [894, 34, 863, 46], [894, 35, 863, 47, "layout"], [894, 41, 863, 53], [894, 42, 863, 54, "width"], [894, 47, 863, 59], [895, 14, 863, 61, "height"], [895, 20, 863, 67], [895, 22, 863, 69, "e"], [895, 23, 863, 70], [895, 24, 863, 71, "nativeEvent"], [895, 35, 863, 82], [895, 36, 863, 83, "layout"], [895, 42, 863, 89], [895, 43, 863, 90, "height"], [896, 12, 863, 97], [896, 13, 863, 98], [896, 14, 863, 99], [897, 10, 864, 10], [897, 11, 864, 12], [898, 10, 865, 10, "onCameraReady"], [898, 23, 865, 23], [898, 25, 865, 25, "onCameraReady"], [898, 26, 865, 25], [898, 31, 865, 31], [899, 12, 866, 12, "console"], [899, 19, 866, 19], [899, 20, 866, 20, "log"], [899, 23, 866, 23], [899, 24, 866, 24], [899, 55, 866, 55], [899, 56, 866, 56], [900, 12, 867, 12, "setIsCameraReady"], [900, 28, 867, 28], [900, 29, 867, 29], [900, 33, 867, 33], [900, 34, 867, 34], [900, 35, 867, 35], [900, 36, 867, 36], [901, 10, 868, 10], [901, 11, 868, 12], [902, 10, 869, 10, "onMountError"], [902, 22, 869, 22], [902, 24, 869, 25, "error"], [902, 29, 869, 30], [902, 33, 869, 35], [903, 12, 870, 12, "console"], [903, 19, 870, 19], [903, 20, 870, 20, "error"], [903, 25, 870, 25], [903, 26, 870, 26], [903, 63, 870, 63], [903, 65, 870, 65, "error"], [903, 70, 870, 70], [903, 71, 870, 71], [904, 12, 871, 12, "setErrorMessage"], [904, 27, 871, 27], [904, 28, 871, 28], [904, 57, 871, 57], [904, 58, 871, 58], [905, 12, 872, 12, "setProcessingState"], [905, 30, 872, 30], [905, 31, 872, 31], [905, 38, 872, 38], [905, 39, 872, 39], [906, 10, 873, 10], [907, 8, 873, 12], [908, 10, 873, 12, "fileName"], [908, 18, 873, 12], [908, 20, 873, 12, "_jsxFileName"], [908, 32, 873, 12], [909, 10, 873, 12, "lineNumber"], [909, 20, 873, 12], [910, 10, 873, 12, "columnNumber"], [910, 22, 873, 12], [911, 8, 873, 12], [911, 15, 874, 9], [911, 16, 874, 10], [911, 18, 876, 9], [911, 19, 876, 10, "isCameraReady"], [911, 32, 876, 23], [911, 49, 877, 10], [911, 53, 877, 10, "_jsxDevRuntime"], [911, 67, 877, 10], [911, 68, 877, 10, "jsxDEV"], [911, 74, 877, 10], [911, 76, 877, 11, "_View"], [911, 81, 877, 11], [911, 82, 877, 11, "default"], [911, 89, 877, 15], [912, 10, 877, 16, "style"], [912, 15, 877, 21], [912, 17, 877, 23], [912, 18, 877, 24, "StyleSheet"], [912, 37, 877, 34], [912, 38, 877, 35, "absoluteFill"], [912, 50, 877, 47], [912, 52, 877, 49], [913, 12, 877, 51, "backgroundColor"], [913, 27, 877, 66], [913, 29, 877, 68], [913, 49, 877, 88], [914, 12, 877, 90, "justifyContent"], [914, 26, 877, 104], [914, 28, 877, 106], [914, 36, 877, 114], [915, 12, 877, 116, "alignItems"], [915, 22, 877, 126], [915, 24, 877, 128], [915, 32, 877, 136], [916, 12, 877, 138, "zIndex"], [916, 18, 877, 144], [916, 20, 877, 146], [917, 10, 877, 151], [917, 11, 877, 152], [917, 12, 877, 154], [918, 10, 877, 154, "children"], [918, 18, 877, 154], [918, 33, 878, 12], [918, 37, 878, 12, "_jsxDevRuntime"], [918, 51, 878, 12], [918, 52, 878, 12, "jsxDEV"], [918, 58, 878, 12], [918, 60, 878, 13, "_View"], [918, 65, 878, 13], [918, 66, 878, 13, "default"], [918, 73, 878, 17], [919, 12, 878, 18, "style"], [919, 17, 878, 23], [919, 19, 878, 25], [920, 14, 878, 27, "backgroundColor"], [920, 29, 878, 42], [920, 31, 878, 44], [920, 51, 878, 64], [921, 14, 878, 66, "padding"], [921, 21, 878, 73], [921, 23, 878, 75], [921, 25, 878, 77], [922, 14, 878, 79, "borderRadius"], [922, 26, 878, 91], [922, 28, 878, 93], [922, 30, 878, 95], [923, 14, 878, 97, "alignItems"], [923, 24, 878, 107], [923, 26, 878, 109], [924, 12, 878, 118], [924, 13, 878, 120], [925, 12, 878, 120, "children"], [925, 20, 878, 120], [925, 36, 879, 14], [925, 40, 879, 14, "_jsxDevRuntime"], [925, 54, 879, 14], [925, 55, 879, 14, "jsxDEV"], [925, 61, 879, 14], [925, 63, 879, 15, "_ActivityIndicator"], [925, 81, 879, 15], [925, 82, 879, 15, "default"], [925, 89, 879, 32], [926, 14, 879, 33, "size"], [926, 18, 879, 37], [926, 20, 879, 38], [926, 27, 879, 45], [927, 14, 879, 46, "color"], [927, 19, 879, 51], [927, 21, 879, 52], [927, 30, 879, 61], [928, 14, 879, 62, "style"], [928, 19, 879, 67], [928, 21, 879, 69], [929, 16, 879, 71, "marginBottom"], [929, 28, 879, 83], [929, 30, 879, 85], [930, 14, 879, 88], [931, 12, 879, 90], [932, 14, 879, 90, "fileName"], [932, 22, 879, 90], [932, 24, 879, 90, "_jsxFileName"], [932, 36, 879, 90], [933, 14, 879, 90, "lineNumber"], [933, 24, 879, 90], [934, 14, 879, 90, "columnNumber"], [934, 26, 879, 90], [935, 12, 879, 90], [935, 19, 879, 92], [935, 20, 879, 93], [935, 35, 880, 14], [935, 39, 880, 14, "_jsxDevRuntime"], [935, 53, 880, 14], [935, 54, 880, 14, "jsxDEV"], [935, 60, 880, 14], [935, 62, 880, 15, "_Text"], [935, 67, 880, 15], [935, 68, 880, 15, "default"], [935, 75, 880, 19], [936, 14, 880, 20, "style"], [936, 19, 880, 25], [936, 21, 880, 27], [937, 16, 880, 29, "color"], [937, 21, 880, 34], [937, 23, 880, 36], [937, 29, 880, 42], [938, 16, 880, 44, "fontSize"], [938, 24, 880, 52], [938, 26, 880, 54], [938, 28, 880, 56], [939, 16, 880, 58, "fontWeight"], [939, 26, 880, 68], [939, 28, 880, 70], [940, 14, 880, 76], [940, 15, 880, 78], [941, 14, 880, 78, "children"], [941, 22, 880, 78], [941, 24, 880, 79], [942, 12, 880, 101], [943, 14, 880, 101, "fileName"], [943, 22, 880, 101], [943, 24, 880, 101, "_jsxFileName"], [943, 36, 880, 101], [944, 14, 880, 101, "lineNumber"], [944, 24, 880, 101], [945, 14, 880, 101, "columnNumber"], [945, 26, 880, 101], [946, 12, 880, 101], [946, 19, 880, 107], [946, 20, 880, 108], [946, 35, 881, 14], [946, 39, 881, 14, "_jsxDevRuntime"], [946, 53, 881, 14], [946, 54, 881, 14, "jsxDEV"], [946, 60, 881, 14], [946, 62, 881, 15, "_Text"], [946, 67, 881, 15], [946, 68, 881, 15, "default"], [946, 75, 881, 19], [947, 14, 881, 20, "style"], [947, 19, 881, 25], [947, 21, 881, 27], [948, 16, 881, 29, "color"], [948, 21, 881, 34], [948, 23, 881, 36], [948, 32, 881, 45], [949, 16, 881, 47, "fontSize"], [949, 24, 881, 55], [949, 26, 881, 57], [949, 28, 881, 59], [950, 16, 881, 61, "marginTop"], [950, 25, 881, 70], [950, 27, 881, 72], [951, 14, 881, 74], [951, 15, 881, 76], [952, 14, 881, 76, "children"], [952, 22, 881, 76], [952, 24, 881, 77], [953, 12, 881, 88], [954, 14, 881, 88, "fileName"], [954, 22, 881, 88], [954, 24, 881, 88, "_jsxFileName"], [954, 36, 881, 88], [955, 14, 881, 88, "lineNumber"], [955, 24, 881, 88], [956, 14, 881, 88, "columnNumber"], [956, 26, 881, 88], [957, 12, 881, 88], [957, 19, 881, 94], [957, 20, 881, 95], [958, 10, 881, 95], [959, 12, 881, 95, "fileName"], [959, 20, 881, 95], [959, 22, 881, 95, "_jsxFileName"], [959, 34, 881, 95], [960, 12, 881, 95, "lineNumber"], [960, 22, 881, 95], [961, 12, 881, 95, "columnNumber"], [961, 24, 881, 95], [962, 10, 881, 95], [962, 17, 882, 18], [963, 8, 882, 19], [964, 10, 882, 19, "fileName"], [964, 18, 882, 19], [964, 20, 882, 19, "_jsxFileName"], [964, 32, 882, 19], [965, 10, 882, 19, "lineNumber"], [965, 20, 882, 19], [966, 10, 882, 19, "columnNumber"], [966, 22, 882, 19], [967, 8, 882, 19], [967, 15, 883, 16], [967, 16, 884, 9], [967, 18, 887, 9, "isCameraReady"], [967, 31, 887, 22], [967, 35, 887, 26, "previewBlurEnabled"], [967, 53, 887, 44], [967, 57, 887, 48, "viewSize"], [967, 65, 887, 56], [967, 66, 887, 57, "width"], [967, 71, 887, 62], [967, 74, 887, 65], [967, 75, 887, 66], [967, 92, 888, 10], [967, 96, 888, 10, "_jsxDevRuntime"], [967, 110, 888, 10], [967, 111, 888, 10, "jsxDEV"], [967, 117, 888, 10], [967, 119, 888, 10, "_jsxDevRuntime"], [967, 133, 888, 10], [967, 134, 888, 10, "Fragment"], [967, 142, 888, 10], [968, 10, 888, 10, "children"], [968, 18, 888, 10], [968, 34, 890, 12], [968, 38, 890, 12, "_jsxDevRuntime"], [968, 52, 890, 12], [968, 53, 890, 12, "jsxDEV"], [968, 59, 890, 12], [968, 61, 890, 13, "_LiveFaceCanvas"], [968, 76, 890, 13], [968, 77, 890, 13, "default"], [968, 84, 890, 27], [969, 12, 890, 28, "containerId"], [969, 23, 890, 39], [969, 25, 890, 40], [969, 42, 890, 57], [970, 12, 890, 58, "width"], [970, 17, 890, 63], [970, 19, 890, 65, "viewSize"], [970, 27, 890, 73], [970, 28, 890, 74, "width"], [970, 33, 890, 80], [971, 12, 890, 81, "height"], [971, 18, 890, 87], [971, 20, 890, 89, "viewSize"], [971, 28, 890, 97], [971, 29, 890, 98, "height"], [972, 10, 890, 105], [973, 12, 890, 105, "fileName"], [973, 20, 890, 105], [973, 22, 890, 105, "_jsxFileName"], [973, 34, 890, 105], [974, 12, 890, 105, "lineNumber"], [974, 22, 890, 105], [975, 12, 890, 105, "columnNumber"], [975, 24, 890, 105], [976, 10, 890, 105], [976, 17, 890, 107], [976, 18, 890, 108], [976, 33, 891, 12], [976, 37, 891, 12, "_jsxDevRuntime"], [976, 51, 891, 12], [976, 52, 891, 12, "jsxDEV"], [976, 58, 891, 12], [976, 60, 891, 13, "_View"], [976, 65, 891, 13], [976, 66, 891, 13, "default"], [976, 73, 891, 17], [977, 12, 891, 18, "style"], [977, 17, 891, 23], [977, 19, 891, 25], [977, 20, 891, 26, "StyleSheet"], [977, 39, 891, 36], [977, 40, 891, 37, "absoluteFill"], [977, 52, 891, 49], [977, 54, 891, 51], [978, 14, 891, 53, "pointerEvents"], [978, 27, 891, 66], [978, 29, 891, 68], [979, 12, 891, 75], [979, 13, 891, 76], [979, 14, 891, 78], [980, 12, 891, 78, "children"], [980, 20, 891, 78], [980, 36, 893, 12], [980, 40, 893, 12, "_jsxDevRuntime"], [980, 54, 893, 12], [980, 55, 893, 12, "jsxDEV"], [980, 61, 893, 12], [980, 63, 893, 13, "_expoBlur"], [980, 72, 893, 13], [980, 73, 893, 13, "BlurView"], [980, 81, 893, 21], [981, 14, 893, 22, "intensity"], [981, 23, 893, 31], [981, 25, 893, 33], [981, 27, 893, 36], [982, 14, 893, 37, "tint"], [982, 18, 893, 41], [982, 20, 893, 42], [982, 26, 893, 48], [983, 14, 893, 49, "style"], [983, 19, 893, 54], [983, 21, 893, 56], [983, 22, 893, 57, "styles"], [983, 28, 893, 63], [983, 29, 893, 64, "blurZone"], [983, 37, 893, 72], [983, 39, 893, 74], [984, 16, 894, 14, "left"], [984, 20, 894, 18], [984, 22, 894, 20], [984, 23, 894, 21], [985, 16, 895, 14, "top"], [985, 19, 895, 17], [985, 21, 895, 19, "viewSize"], [985, 29, 895, 27], [985, 30, 895, 28, "height"], [985, 36, 895, 34], [985, 39, 895, 37], [985, 42, 895, 40], [986, 16, 896, 14, "width"], [986, 21, 896, 19], [986, 23, 896, 21, "viewSize"], [986, 31, 896, 29], [986, 32, 896, 30, "width"], [986, 37, 896, 35], [987, 16, 897, 14, "height"], [987, 22, 897, 20], [987, 24, 897, 22, "viewSize"], [987, 32, 897, 30], [987, 33, 897, 31, "height"], [987, 39, 897, 37], [987, 42, 897, 40], [987, 46, 897, 44], [988, 16, 898, 14, "borderRadius"], [988, 28, 898, 26], [988, 30, 898, 28], [989, 14, 899, 12], [989, 15, 899, 13], [990, 12, 899, 15], [991, 14, 899, 15, "fileName"], [991, 22, 899, 15], [991, 24, 899, 15, "_jsxFileName"], [991, 36, 899, 15], [992, 14, 899, 15, "lineNumber"], [992, 24, 899, 15], [993, 14, 899, 15, "columnNumber"], [993, 26, 899, 15], [994, 12, 899, 15], [994, 19, 899, 17], [994, 20, 899, 18], [994, 35, 901, 12], [994, 39, 901, 12, "_jsxDevRuntime"], [994, 53, 901, 12], [994, 54, 901, 12, "jsxDEV"], [994, 60, 901, 12], [994, 62, 901, 13, "_expoBlur"], [994, 71, 901, 13], [994, 72, 901, 13, "BlurView"], [994, 80, 901, 21], [995, 14, 901, 22, "intensity"], [995, 23, 901, 31], [995, 25, 901, 33], [995, 27, 901, 36], [996, 14, 901, 37, "tint"], [996, 18, 901, 41], [996, 20, 901, 42], [996, 26, 901, 48], [997, 14, 901, 49, "style"], [997, 19, 901, 54], [997, 21, 901, 56], [997, 22, 901, 57, "styles"], [997, 28, 901, 63], [997, 29, 901, 64, "blurZone"], [997, 37, 901, 72], [997, 39, 901, 74], [998, 16, 902, 14, "left"], [998, 20, 902, 18], [998, 22, 902, 20], [998, 23, 902, 21], [999, 16, 903, 14, "top"], [999, 19, 903, 17], [999, 21, 903, 19], [999, 22, 903, 20], [1000, 16, 904, 14, "width"], [1000, 21, 904, 19], [1000, 23, 904, 21, "viewSize"], [1000, 31, 904, 29], [1000, 32, 904, 30, "width"], [1000, 37, 904, 35], [1001, 16, 905, 14, "height"], [1001, 22, 905, 20], [1001, 24, 905, 22, "viewSize"], [1001, 32, 905, 30], [1001, 33, 905, 31, "height"], [1001, 39, 905, 37], [1001, 42, 905, 40], [1001, 45, 905, 43], [1002, 16, 906, 14, "borderRadius"], [1002, 28, 906, 26], [1002, 30, 906, 28], [1003, 14, 907, 12], [1003, 15, 907, 13], [1004, 12, 907, 15], [1005, 14, 907, 15, "fileName"], [1005, 22, 907, 15], [1005, 24, 907, 15, "_jsxFileName"], [1005, 36, 907, 15], [1006, 14, 907, 15, "lineNumber"], [1006, 24, 907, 15], [1007, 14, 907, 15, "columnNumber"], [1007, 26, 907, 15], [1008, 12, 907, 15], [1008, 19, 907, 17], [1008, 20, 907, 18], [1008, 35, 909, 12], [1008, 39, 909, 12, "_jsxDevRuntime"], [1008, 53, 909, 12], [1008, 54, 909, 12, "jsxDEV"], [1008, 60, 909, 12], [1008, 62, 909, 13, "_expoBlur"], [1008, 71, 909, 13], [1008, 72, 909, 13, "BlurView"], [1008, 80, 909, 21], [1009, 14, 909, 22, "intensity"], [1009, 23, 909, 31], [1009, 25, 909, 33], [1009, 27, 909, 36], [1010, 14, 909, 37, "tint"], [1010, 18, 909, 41], [1010, 20, 909, 42], [1010, 26, 909, 48], [1011, 14, 909, 49, "style"], [1011, 19, 909, 54], [1011, 21, 909, 56], [1011, 22, 909, 57, "styles"], [1011, 28, 909, 63], [1011, 29, 909, 64, "blurZone"], [1011, 37, 909, 72], [1011, 39, 909, 74], [1012, 16, 910, 14, "left"], [1012, 20, 910, 18], [1012, 22, 910, 20, "viewSize"], [1012, 30, 910, 28], [1012, 31, 910, 29, "width"], [1012, 36, 910, 34], [1012, 39, 910, 37], [1012, 42, 910, 40], [1012, 45, 910, 44, "viewSize"], [1012, 53, 910, 52], [1012, 54, 910, 53, "width"], [1012, 59, 910, 58], [1012, 62, 910, 61], [1012, 66, 910, 66], [1013, 16, 911, 14, "top"], [1013, 19, 911, 17], [1013, 21, 911, 19, "viewSize"], [1013, 29, 911, 27], [1013, 30, 911, 28, "height"], [1013, 36, 911, 34], [1013, 39, 911, 37], [1013, 43, 911, 41], [1013, 46, 911, 45, "viewSize"], [1013, 54, 911, 53], [1013, 55, 911, 54, "width"], [1013, 60, 911, 59], [1013, 63, 911, 62], [1013, 67, 911, 67], [1014, 16, 912, 14, "width"], [1014, 21, 912, 19], [1014, 23, 912, 21, "viewSize"], [1014, 31, 912, 29], [1014, 32, 912, 30, "width"], [1014, 37, 912, 35], [1014, 40, 912, 38], [1014, 43, 912, 41], [1015, 16, 913, 14, "height"], [1015, 22, 913, 20], [1015, 24, 913, 22, "viewSize"], [1015, 32, 913, 30], [1015, 33, 913, 31, "width"], [1015, 38, 913, 36], [1015, 41, 913, 39], [1015, 44, 913, 42], [1016, 16, 914, 14, "borderRadius"], [1016, 28, 914, 26], [1016, 30, 914, 29, "viewSize"], [1016, 38, 914, 37], [1016, 39, 914, 38, "width"], [1016, 44, 914, 43], [1016, 47, 914, 46], [1016, 50, 914, 49], [1016, 53, 914, 53], [1017, 14, 915, 12], [1017, 15, 915, 13], [1018, 12, 915, 15], [1019, 14, 915, 15, "fileName"], [1019, 22, 915, 15], [1019, 24, 915, 15, "_jsxFileName"], [1019, 36, 915, 15], [1020, 14, 915, 15, "lineNumber"], [1020, 24, 915, 15], [1021, 14, 915, 15, "columnNumber"], [1021, 26, 915, 15], [1022, 12, 915, 15], [1022, 19, 915, 17], [1022, 20, 915, 18], [1022, 35, 916, 12], [1022, 39, 916, 12, "_jsxDevRuntime"], [1022, 53, 916, 12], [1022, 54, 916, 12, "jsxDEV"], [1022, 60, 916, 12], [1022, 62, 916, 13, "_expoBlur"], [1022, 71, 916, 13], [1022, 72, 916, 13, "BlurView"], [1022, 80, 916, 21], [1023, 14, 916, 22, "intensity"], [1023, 23, 916, 31], [1023, 25, 916, 33], [1023, 27, 916, 36], [1024, 14, 916, 37, "tint"], [1024, 18, 916, 41], [1024, 20, 916, 42], [1024, 26, 916, 48], [1025, 14, 916, 49, "style"], [1025, 19, 916, 54], [1025, 21, 916, 56], [1025, 22, 916, 57, "styles"], [1025, 28, 916, 63], [1025, 29, 916, 64, "blurZone"], [1025, 37, 916, 72], [1025, 39, 916, 74], [1026, 16, 917, 14, "left"], [1026, 20, 917, 18], [1026, 22, 917, 20, "viewSize"], [1026, 30, 917, 28], [1026, 31, 917, 29, "width"], [1026, 36, 917, 34], [1026, 39, 917, 37], [1026, 42, 917, 40], [1026, 45, 917, 44, "viewSize"], [1026, 53, 917, 52], [1026, 54, 917, 53, "width"], [1026, 59, 917, 58], [1026, 62, 917, 61], [1026, 66, 917, 66], [1027, 16, 918, 14, "top"], [1027, 19, 918, 17], [1027, 21, 918, 19, "viewSize"], [1027, 29, 918, 27], [1027, 30, 918, 28, "height"], [1027, 36, 918, 34], [1027, 39, 918, 37], [1027, 42, 918, 40], [1027, 45, 918, 44, "viewSize"], [1027, 53, 918, 52], [1027, 54, 918, 53, "width"], [1027, 59, 918, 58], [1027, 62, 918, 61], [1027, 66, 918, 66], [1028, 16, 919, 14, "width"], [1028, 21, 919, 19], [1028, 23, 919, 21, "viewSize"], [1028, 31, 919, 29], [1028, 32, 919, 30, "width"], [1028, 37, 919, 35], [1028, 40, 919, 38], [1028, 43, 919, 41], [1029, 16, 920, 14, "height"], [1029, 22, 920, 20], [1029, 24, 920, 22, "viewSize"], [1029, 32, 920, 30], [1029, 33, 920, 31, "width"], [1029, 38, 920, 36], [1029, 41, 920, 39], [1029, 44, 920, 42], [1030, 16, 921, 14, "borderRadius"], [1030, 28, 921, 26], [1030, 30, 921, 29, "viewSize"], [1030, 38, 921, 37], [1030, 39, 921, 38, "width"], [1030, 44, 921, 43], [1030, 47, 921, 46], [1030, 50, 921, 49], [1030, 53, 921, 53], [1031, 14, 922, 12], [1031, 15, 922, 13], [1032, 12, 922, 15], [1033, 14, 922, 15, "fileName"], [1033, 22, 922, 15], [1033, 24, 922, 15, "_jsxFileName"], [1033, 36, 922, 15], [1034, 14, 922, 15, "lineNumber"], [1034, 24, 922, 15], [1035, 14, 922, 15, "columnNumber"], [1035, 26, 922, 15], [1036, 12, 922, 15], [1036, 19, 922, 17], [1036, 20, 922, 18], [1036, 35, 923, 12], [1036, 39, 923, 12, "_jsxDevRuntime"], [1036, 53, 923, 12], [1036, 54, 923, 12, "jsxDEV"], [1036, 60, 923, 12], [1036, 62, 923, 13, "_expoBlur"], [1036, 71, 923, 13], [1036, 72, 923, 13, "BlurView"], [1036, 80, 923, 21], [1037, 14, 923, 22, "intensity"], [1037, 23, 923, 31], [1037, 25, 923, 33], [1037, 27, 923, 36], [1038, 14, 923, 37, "tint"], [1038, 18, 923, 41], [1038, 20, 923, 42], [1038, 26, 923, 48], [1039, 14, 923, 49, "style"], [1039, 19, 923, 54], [1039, 21, 923, 56], [1039, 22, 923, 57, "styles"], [1039, 28, 923, 63], [1039, 29, 923, 64, "blurZone"], [1039, 37, 923, 72], [1039, 39, 923, 74], [1040, 16, 924, 14, "left"], [1040, 20, 924, 18], [1040, 22, 924, 20, "viewSize"], [1040, 30, 924, 28], [1040, 31, 924, 29, "width"], [1040, 36, 924, 34], [1040, 39, 924, 37], [1040, 42, 924, 40], [1040, 45, 924, 44, "viewSize"], [1040, 53, 924, 52], [1040, 54, 924, 53, "width"], [1040, 59, 924, 58], [1040, 62, 924, 61], [1040, 66, 924, 66], [1041, 16, 925, 14, "top"], [1041, 19, 925, 17], [1041, 21, 925, 19, "viewSize"], [1041, 29, 925, 27], [1041, 30, 925, 28, "height"], [1041, 36, 925, 34], [1041, 39, 925, 37], [1041, 42, 925, 40], [1041, 45, 925, 44, "viewSize"], [1041, 53, 925, 52], [1041, 54, 925, 53, "width"], [1041, 59, 925, 58], [1041, 62, 925, 61], [1041, 66, 925, 66], [1042, 16, 926, 14, "width"], [1042, 21, 926, 19], [1042, 23, 926, 21, "viewSize"], [1042, 31, 926, 29], [1042, 32, 926, 30, "width"], [1042, 37, 926, 35], [1042, 40, 926, 38], [1042, 43, 926, 41], [1043, 16, 927, 14, "height"], [1043, 22, 927, 20], [1043, 24, 927, 22, "viewSize"], [1043, 32, 927, 30], [1043, 33, 927, 31, "width"], [1043, 38, 927, 36], [1043, 41, 927, 39], [1043, 44, 927, 42], [1044, 16, 928, 14, "borderRadius"], [1044, 28, 928, 26], [1044, 30, 928, 29, "viewSize"], [1044, 38, 928, 37], [1044, 39, 928, 38, "width"], [1044, 44, 928, 43], [1044, 47, 928, 46], [1044, 50, 928, 49], [1044, 53, 928, 53], [1045, 14, 929, 12], [1045, 15, 929, 13], [1046, 12, 929, 15], [1047, 14, 929, 15, "fileName"], [1047, 22, 929, 15], [1047, 24, 929, 15, "_jsxFileName"], [1047, 36, 929, 15], [1048, 14, 929, 15, "lineNumber"], [1048, 24, 929, 15], [1049, 14, 929, 15, "columnNumber"], [1049, 26, 929, 15], [1050, 12, 929, 15], [1050, 19, 929, 17], [1050, 20, 929, 18], [1050, 22, 931, 13, "__DEV__"], [1050, 29, 931, 20], [1050, 46, 932, 14], [1050, 50, 932, 14, "_jsxDevRuntime"], [1050, 64, 932, 14], [1050, 65, 932, 14, "jsxDEV"], [1050, 71, 932, 14], [1050, 73, 932, 15, "_View"], [1050, 78, 932, 15], [1050, 79, 932, 15, "default"], [1050, 86, 932, 19], [1051, 14, 932, 20, "style"], [1051, 19, 932, 25], [1051, 21, 932, 27, "styles"], [1051, 27, 932, 33], [1051, 28, 932, 34, "previewChip"], [1051, 39, 932, 46], [1052, 14, 932, 46, "children"], [1052, 22, 932, 46], [1052, 37, 933, 16], [1052, 41, 933, 16, "_jsxDevRuntime"], [1052, 55, 933, 16], [1052, 56, 933, 16, "jsxDEV"], [1052, 62, 933, 16], [1052, 64, 933, 17, "_Text"], [1052, 69, 933, 17], [1052, 70, 933, 17, "default"], [1052, 77, 933, 21], [1053, 16, 933, 22, "style"], [1053, 21, 933, 27], [1053, 23, 933, 29, "styles"], [1053, 29, 933, 35], [1053, 30, 933, 36, "previewChipText"], [1053, 45, 933, 52], [1054, 16, 933, 52, "children"], [1054, 24, 933, 52], [1054, 26, 933, 53], [1055, 14, 933, 73], [1056, 16, 933, 73, "fileName"], [1056, 24, 933, 73], [1056, 26, 933, 73, "_jsxFileName"], [1056, 38, 933, 73], [1057, 16, 933, 73, "lineNumber"], [1057, 26, 933, 73], [1058, 16, 933, 73, "columnNumber"], [1058, 28, 933, 73], [1059, 14, 933, 73], [1059, 21, 933, 79], [1060, 12, 933, 80], [1061, 14, 933, 80, "fileName"], [1061, 22, 933, 80], [1061, 24, 933, 80, "_jsxFileName"], [1061, 36, 933, 80], [1062, 14, 933, 80, "lineNumber"], [1062, 24, 933, 80], [1063, 14, 933, 80, "columnNumber"], [1063, 26, 933, 80], [1064, 12, 933, 80], [1064, 19, 934, 20], [1064, 20, 935, 13], [1065, 10, 935, 13], [1066, 12, 935, 13, "fileName"], [1066, 20, 935, 13], [1066, 22, 935, 13, "_jsxFileName"], [1066, 34, 935, 13], [1067, 12, 935, 13, "lineNumber"], [1067, 22, 935, 13], [1068, 12, 935, 13, "columnNumber"], [1068, 24, 935, 13], [1069, 10, 935, 13], [1069, 17, 936, 18], [1069, 18, 936, 19], [1070, 8, 936, 19], [1070, 23, 937, 12], [1070, 24, 938, 9], [1070, 26, 940, 9, "isCameraReady"], [1070, 39, 940, 22], [1070, 56, 941, 10], [1070, 60, 941, 10, "_jsxDevRuntime"], [1070, 74, 941, 10], [1070, 75, 941, 10, "jsxDEV"], [1070, 81, 941, 10], [1070, 83, 941, 10, "_jsxDevRuntime"], [1070, 97, 941, 10], [1070, 98, 941, 10, "Fragment"], [1070, 106, 941, 10], [1071, 10, 941, 10, "children"], [1071, 18, 941, 10], [1071, 34, 943, 12], [1071, 38, 943, 12, "_jsxDevRuntime"], [1071, 52, 943, 12], [1071, 53, 943, 12, "jsxDEV"], [1071, 59, 943, 12], [1071, 61, 943, 13, "_View"], [1071, 66, 943, 13], [1071, 67, 943, 13, "default"], [1071, 74, 943, 17], [1072, 12, 943, 18, "style"], [1072, 17, 943, 23], [1072, 19, 943, 25, "styles"], [1072, 25, 943, 31], [1072, 26, 943, 32, "headerOverlay"], [1072, 39, 943, 46], [1073, 12, 943, 46, "children"], [1073, 20, 943, 46], [1073, 35, 944, 14], [1073, 39, 944, 14, "_jsxDevRuntime"], [1073, 53, 944, 14], [1073, 54, 944, 14, "jsxDEV"], [1073, 60, 944, 14], [1073, 62, 944, 15, "_View"], [1073, 67, 944, 15], [1073, 68, 944, 15, "default"], [1073, 75, 944, 19], [1074, 14, 944, 20, "style"], [1074, 19, 944, 25], [1074, 21, 944, 27, "styles"], [1074, 27, 944, 33], [1074, 28, 944, 34, "headerContent"], [1074, 41, 944, 48], [1075, 14, 944, 48, "children"], [1075, 22, 944, 48], [1075, 38, 945, 16], [1075, 42, 945, 16, "_jsxDevRuntime"], [1075, 56, 945, 16], [1075, 57, 945, 16, "jsxDEV"], [1075, 63, 945, 16], [1075, 65, 945, 17, "_View"], [1075, 70, 945, 17], [1075, 71, 945, 17, "default"], [1075, 78, 945, 21], [1076, 16, 945, 22, "style"], [1076, 21, 945, 27], [1076, 23, 945, 29, "styles"], [1076, 29, 945, 35], [1076, 30, 945, 36, "headerLeft"], [1076, 40, 945, 47], [1077, 16, 945, 47, "children"], [1077, 24, 945, 47], [1077, 40, 946, 18], [1077, 44, 946, 18, "_jsxDevRuntime"], [1077, 58, 946, 18], [1077, 59, 946, 18, "jsxDEV"], [1077, 65, 946, 18], [1077, 67, 946, 19, "_Text"], [1077, 72, 946, 19], [1077, 73, 946, 19, "default"], [1077, 80, 946, 23], [1078, 18, 946, 24, "style"], [1078, 23, 946, 29], [1078, 25, 946, 31, "styles"], [1078, 31, 946, 37], [1078, 32, 946, 38, "headerTitle"], [1078, 43, 946, 50], [1079, 18, 946, 50, "children"], [1079, 26, 946, 50], [1079, 28, 946, 51], [1080, 16, 946, 62], [1081, 18, 946, 62, "fileName"], [1081, 26, 946, 62], [1081, 28, 946, 62, "_jsxFileName"], [1081, 40, 946, 62], [1082, 18, 946, 62, "lineNumber"], [1082, 28, 946, 62], [1083, 18, 946, 62, "columnNumber"], [1083, 30, 946, 62], [1084, 16, 946, 62], [1084, 23, 946, 68], [1084, 24, 946, 69], [1084, 39, 947, 18], [1084, 43, 947, 18, "_jsxDevRuntime"], [1084, 57, 947, 18], [1084, 58, 947, 18, "jsxDEV"], [1084, 64, 947, 18], [1084, 66, 947, 19, "_View"], [1084, 71, 947, 19], [1084, 72, 947, 19, "default"], [1084, 79, 947, 23], [1085, 18, 947, 24, "style"], [1085, 23, 947, 29], [1085, 25, 947, 31, "styles"], [1085, 31, 947, 37], [1085, 32, 947, 38, "subtitleRow"], [1085, 43, 947, 50], [1086, 18, 947, 50, "children"], [1086, 26, 947, 50], [1086, 42, 948, 20], [1086, 46, 948, 20, "_jsxDevRuntime"], [1086, 60, 948, 20], [1086, 61, 948, 20, "jsxDEV"], [1086, 67, 948, 20], [1086, 69, 948, 21, "_Text"], [1086, 74, 948, 21], [1086, 75, 948, 21, "default"], [1086, 82, 948, 25], [1087, 20, 948, 26, "style"], [1087, 25, 948, 31], [1087, 27, 948, 33, "styles"], [1087, 33, 948, 39], [1087, 34, 948, 40, "webIcon"], [1087, 41, 948, 48], [1088, 20, 948, 48, "children"], [1088, 28, 948, 48], [1088, 30, 948, 49], [1089, 18, 948, 51], [1090, 20, 948, 51, "fileName"], [1090, 28, 948, 51], [1090, 30, 948, 51, "_jsxFileName"], [1090, 42, 948, 51], [1091, 20, 948, 51, "lineNumber"], [1091, 30, 948, 51], [1092, 20, 948, 51, "columnNumber"], [1092, 32, 948, 51], [1093, 18, 948, 51], [1093, 25, 948, 57], [1093, 26, 948, 58], [1093, 41, 949, 20], [1093, 45, 949, 20, "_jsxDevRuntime"], [1093, 59, 949, 20], [1093, 60, 949, 20, "jsxDEV"], [1093, 66, 949, 20], [1093, 68, 949, 21, "_Text"], [1093, 73, 949, 21], [1093, 74, 949, 21, "default"], [1093, 81, 949, 25], [1094, 20, 949, 26, "style"], [1094, 25, 949, 31], [1094, 27, 949, 33, "styles"], [1094, 33, 949, 39], [1094, 34, 949, 40, "headerSubtitle"], [1094, 48, 949, 55], [1095, 20, 949, 55, "children"], [1095, 28, 949, 55], [1095, 30, 949, 56], [1096, 18, 949, 71], [1097, 20, 949, 71, "fileName"], [1097, 28, 949, 71], [1097, 30, 949, 71, "_jsxFileName"], [1097, 42, 949, 71], [1098, 20, 949, 71, "lineNumber"], [1098, 30, 949, 71], [1099, 20, 949, 71, "columnNumber"], [1099, 32, 949, 71], [1100, 18, 949, 71], [1100, 25, 949, 77], [1100, 26, 949, 78], [1101, 16, 949, 78], [1102, 18, 949, 78, "fileName"], [1102, 26, 949, 78], [1102, 28, 949, 78, "_jsxFileName"], [1102, 40, 949, 78], [1103, 18, 949, 78, "lineNumber"], [1103, 28, 949, 78], [1104, 18, 949, 78, "columnNumber"], [1104, 30, 949, 78], [1105, 16, 949, 78], [1105, 23, 950, 24], [1105, 24, 950, 25], [1105, 26, 951, 19, "challengeCode"], [1105, 39, 951, 32], [1105, 56, 952, 20], [1105, 60, 952, 20, "_jsxDevRuntime"], [1105, 74, 952, 20], [1105, 75, 952, 20, "jsxDEV"], [1105, 81, 952, 20], [1105, 83, 952, 21, "_View"], [1105, 88, 952, 21], [1105, 89, 952, 21, "default"], [1105, 96, 952, 25], [1106, 18, 952, 26, "style"], [1106, 23, 952, 31], [1106, 25, 952, 33, "styles"], [1106, 31, 952, 39], [1106, 32, 952, 40, "challengeRow"], [1106, 44, 952, 53], [1107, 18, 952, 53, "children"], [1107, 26, 952, 53], [1107, 42, 953, 22], [1107, 46, 953, 22, "_jsxDevRuntime"], [1107, 60, 953, 22], [1107, 61, 953, 22, "jsxDEV"], [1107, 67, 953, 22], [1107, 69, 953, 23, "_lucideReactNative"], [1107, 87, 953, 23], [1107, 88, 953, 23, "Shield"], [1107, 94, 953, 29], [1108, 20, 953, 30, "size"], [1108, 24, 953, 34], [1108, 26, 953, 36], [1108, 28, 953, 39], [1109, 20, 953, 40, "color"], [1109, 25, 953, 45], [1109, 27, 953, 46], [1110, 18, 953, 52], [1111, 20, 953, 52, "fileName"], [1111, 28, 953, 52], [1111, 30, 953, 52, "_jsxFileName"], [1111, 42, 953, 52], [1112, 20, 953, 52, "lineNumber"], [1112, 30, 953, 52], [1113, 20, 953, 52, "columnNumber"], [1113, 32, 953, 52], [1114, 18, 953, 52], [1114, 25, 953, 54], [1114, 26, 953, 55], [1114, 41, 954, 22], [1114, 45, 954, 22, "_jsxDevRuntime"], [1114, 59, 954, 22], [1114, 60, 954, 22, "jsxDEV"], [1114, 66, 954, 22], [1114, 68, 954, 23, "_Text"], [1114, 73, 954, 23], [1114, 74, 954, 23, "default"], [1114, 81, 954, 27], [1115, 20, 954, 28, "style"], [1115, 25, 954, 33], [1115, 27, 954, 35, "styles"], [1115, 33, 954, 41], [1115, 34, 954, 42, "challengeCode"], [1115, 47, 954, 56], [1116, 20, 954, 56, "children"], [1116, 28, 954, 56], [1116, 30, 954, 58, "challengeCode"], [1117, 18, 954, 71], [1118, 20, 954, 71, "fileName"], [1118, 28, 954, 71], [1118, 30, 954, 71, "_jsxFileName"], [1118, 42, 954, 71], [1119, 20, 954, 71, "lineNumber"], [1119, 30, 954, 71], [1120, 20, 954, 71, "columnNumber"], [1120, 32, 954, 71], [1121, 18, 954, 71], [1121, 25, 954, 78], [1121, 26, 954, 79], [1122, 16, 954, 79], [1123, 18, 954, 79, "fileName"], [1123, 26, 954, 79], [1123, 28, 954, 79, "_jsxFileName"], [1123, 40, 954, 79], [1124, 18, 954, 79, "lineNumber"], [1124, 28, 954, 79], [1125, 18, 954, 79, "columnNumber"], [1125, 30, 954, 79], [1126, 16, 954, 79], [1126, 23, 955, 26], [1126, 24, 956, 19], [1127, 14, 956, 19], [1128, 16, 956, 19, "fileName"], [1128, 24, 956, 19], [1128, 26, 956, 19, "_jsxFileName"], [1128, 38, 956, 19], [1129, 16, 956, 19, "lineNumber"], [1129, 26, 956, 19], [1130, 16, 956, 19, "columnNumber"], [1130, 28, 956, 19], [1131, 14, 956, 19], [1131, 21, 957, 22], [1131, 22, 957, 23], [1131, 37, 958, 16], [1131, 41, 958, 16, "_jsxDevRuntime"], [1131, 55, 958, 16], [1131, 56, 958, 16, "jsxDEV"], [1131, 62, 958, 16], [1131, 64, 958, 17, "_TouchableOpacity"], [1131, 81, 958, 17], [1131, 82, 958, 17, "default"], [1131, 89, 958, 33], [1132, 16, 958, 34, "onPress"], [1132, 23, 958, 41], [1132, 25, 958, 43, "onCancel"], [1132, 33, 958, 52], [1133, 16, 958, 53, "style"], [1133, 21, 958, 58], [1133, 23, 958, 60, "styles"], [1133, 29, 958, 66], [1133, 30, 958, 67, "closeButton"], [1133, 41, 958, 79], [1134, 16, 958, 79, "children"], [1134, 24, 958, 79], [1134, 39, 959, 18], [1134, 43, 959, 18, "_jsxDevRuntime"], [1134, 57, 959, 18], [1134, 58, 959, 18, "jsxDEV"], [1134, 64, 959, 18], [1134, 66, 959, 19, "_lucideReactNative"], [1134, 84, 959, 19], [1134, 85, 959, 19, "X"], [1134, 86, 959, 20], [1135, 18, 959, 21, "size"], [1135, 22, 959, 25], [1135, 24, 959, 27], [1135, 26, 959, 30], [1136, 18, 959, 31, "color"], [1136, 23, 959, 36], [1136, 25, 959, 37], [1137, 16, 959, 43], [1138, 18, 959, 43, "fileName"], [1138, 26, 959, 43], [1138, 28, 959, 43, "_jsxFileName"], [1138, 40, 959, 43], [1139, 18, 959, 43, "lineNumber"], [1139, 28, 959, 43], [1140, 18, 959, 43, "columnNumber"], [1140, 30, 959, 43], [1141, 16, 959, 43], [1141, 23, 959, 45], [1142, 14, 959, 46], [1143, 16, 959, 46, "fileName"], [1143, 24, 959, 46], [1143, 26, 959, 46, "_jsxFileName"], [1143, 38, 959, 46], [1144, 16, 959, 46, "lineNumber"], [1144, 26, 959, 46], [1145, 16, 959, 46, "columnNumber"], [1145, 28, 959, 46], [1146, 14, 959, 46], [1146, 21, 960, 34], [1146, 22, 960, 35], [1147, 12, 960, 35], [1148, 14, 960, 35, "fileName"], [1148, 22, 960, 35], [1148, 24, 960, 35, "_jsxFileName"], [1148, 36, 960, 35], [1149, 14, 960, 35, "lineNumber"], [1149, 24, 960, 35], [1150, 14, 960, 35, "columnNumber"], [1150, 26, 960, 35], [1151, 12, 960, 35], [1151, 19, 961, 20], [1152, 10, 961, 21], [1153, 12, 961, 21, "fileName"], [1153, 20, 961, 21], [1153, 22, 961, 21, "_jsxFileName"], [1153, 34, 961, 21], [1154, 12, 961, 21, "lineNumber"], [1154, 22, 961, 21], [1155, 12, 961, 21, "columnNumber"], [1155, 24, 961, 21], [1156, 10, 961, 21], [1156, 17, 962, 18], [1156, 18, 962, 19], [1156, 33, 964, 12], [1156, 37, 964, 12, "_jsxDevRuntime"], [1156, 51, 964, 12], [1156, 52, 964, 12, "jsxDEV"], [1156, 58, 964, 12], [1156, 60, 964, 13, "_View"], [1156, 65, 964, 13], [1156, 66, 964, 13, "default"], [1156, 73, 964, 17], [1157, 12, 964, 18, "style"], [1157, 17, 964, 23], [1157, 19, 964, 25, "styles"], [1157, 25, 964, 31], [1157, 26, 964, 32, "privacyNotice"], [1157, 39, 964, 46], [1158, 12, 964, 46, "children"], [1158, 20, 964, 46], [1158, 36, 965, 14], [1158, 40, 965, 14, "_jsxDevRuntime"], [1158, 54, 965, 14], [1158, 55, 965, 14, "jsxDEV"], [1158, 61, 965, 14], [1158, 63, 965, 15, "_lucideReactNative"], [1158, 81, 965, 15], [1158, 82, 965, 15, "Shield"], [1158, 88, 965, 21], [1159, 14, 965, 22, "size"], [1159, 18, 965, 26], [1159, 20, 965, 28], [1159, 22, 965, 31], [1160, 14, 965, 32, "color"], [1160, 19, 965, 37], [1160, 21, 965, 38], [1161, 12, 965, 47], [1162, 14, 965, 47, "fileName"], [1162, 22, 965, 47], [1162, 24, 965, 47, "_jsxFileName"], [1162, 36, 965, 47], [1163, 14, 965, 47, "lineNumber"], [1163, 24, 965, 47], [1164, 14, 965, 47, "columnNumber"], [1164, 26, 965, 47], [1165, 12, 965, 47], [1165, 19, 965, 49], [1165, 20, 965, 50], [1165, 35, 966, 14], [1165, 39, 966, 14, "_jsxDevRuntime"], [1165, 53, 966, 14], [1165, 54, 966, 14, "jsxDEV"], [1165, 60, 966, 14], [1165, 62, 966, 15, "_Text"], [1165, 67, 966, 15], [1165, 68, 966, 15, "default"], [1165, 75, 966, 19], [1166, 14, 966, 20, "style"], [1166, 19, 966, 25], [1166, 21, 966, 27, "styles"], [1166, 27, 966, 33], [1166, 28, 966, 34, "privacyText"], [1166, 39, 966, 46], [1167, 14, 966, 46, "children"], [1167, 22, 966, 46], [1167, 24, 966, 47], [1168, 12, 968, 14], [1169, 14, 968, 14, "fileName"], [1169, 22, 968, 14], [1169, 24, 968, 14, "_jsxFileName"], [1169, 36, 968, 14], [1170, 14, 968, 14, "lineNumber"], [1170, 24, 968, 14], [1171, 14, 968, 14, "columnNumber"], [1171, 26, 968, 14], [1172, 12, 968, 14], [1172, 19, 968, 20], [1172, 20, 968, 21], [1173, 10, 968, 21], [1174, 12, 968, 21, "fileName"], [1174, 20, 968, 21], [1174, 22, 968, 21, "_jsxFileName"], [1174, 34, 968, 21], [1175, 12, 968, 21, "lineNumber"], [1175, 22, 968, 21], [1176, 12, 968, 21, "columnNumber"], [1176, 24, 968, 21], [1177, 10, 968, 21], [1177, 17, 969, 18], [1177, 18, 969, 19], [1177, 33, 971, 12], [1177, 37, 971, 12, "_jsxDevRuntime"], [1177, 51, 971, 12], [1177, 52, 971, 12, "jsxDEV"], [1177, 58, 971, 12], [1177, 60, 971, 13, "_View"], [1177, 65, 971, 13], [1177, 66, 971, 13, "default"], [1177, 73, 971, 17], [1178, 12, 971, 18, "style"], [1178, 17, 971, 23], [1178, 19, 971, 25, "styles"], [1178, 25, 971, 31], [1178, 26, 971, 32, "footer<PERSON><PERSON><PERSON>"], [1178, 39, 971, 46], [1179, 12, 971, 46, "children"], [1179, 20, 971, 46], [1179, 36, 972, 14], [1179, 40, 972, 14, "_jsxDevRuntime"], [1179, 54, 972, 14], [1179, 55, 972, 14, "jsxDEV"], [1179, 61, 972, 14], [1179, 63, 972, 15, "_Text"], [1179, 68, 972, 15], [1179, 69, 972, 15, "default"], [1179, 76, 972, 19], [1180, 14, 972, 20, "style"], [1180, 19, 972, 25], [1180, 21, 972, 27, "styles"], [1180, 27, 972, 33], [1180, 28, 972, 34, "instruction"], [1180, 39, 972, 46], [1181, 14, 972, 46, "children"], [1181, 22, 972, 46], [1181, 24, 972, 47], [1182, 12, 974, 14], [1183, 14, 974, 14, "fileName"], [1183, 22, 974, 14], [1183, 24, 974, 14, "_jsxFileName"], [1183, 36, 974, 14], [1184, 14, 974, 14, "lineNumber"], [1184, 24, 974, 14], [1185, 14, 974, 14, "columnNumber"], [1185, 26, 974, 14], [1186, 12, 974, 14], [1186, 19, 974, 20], [1186, 20, 974, 21], [1186, 35, 976, 14], [1186, 39, 976, 14, "_jsxDevRuntime"], [1186, 53, 976, 14], [1186, 54, 976, 14, "jsxDEV"], [1186, 60, 976, 14], [1186, 62, 976, 15, "_TouchableOpacity"], [1186, 79, 976, 15], [1186, 80, 976, 15, "default"], [1186, 87, 976, 31], [1187, 14, 977, 16, "onPress"], [1187, 21, 977, 23], [1187, 23, 977, 25, "capturePhoto"], [1187, 35, 977, 38], [1188, 14, 978, 16, "disabled"], [1188, 22, 978, 24], [1188, 24, 978, 26, "processingState"], [1188, 39, 978, 41], [1188, 44, 978, 46], [1188, 50, 978, 52], [1188, 54, 978, 56], [1188, 55, 978, 57, "isCameraReady"], [1188, 68, 978, 71], [1189, 14, 979, 16, "style"], [1189, 19, 979, 21], [1189, 21, 979, 23], [1189, 22, 980, 18, "styles"], [1189, 28, 980, 24], [1189, 29, 980, 25, "shutterButton"], [1189, 42, 980, 38], [1189, 44, 981, 18, "processingState"], [1189, 59, 981, 33], [1189, 64, 981, 38], [1189, 70, 981, 44], [1189, 74, 981, 48, "styles"], [1189, 80, 981, 54], [1189, 81, 981, 55, "shutterButtonDisabled"], [1189, 102, 981, 76], [1189, 103, 982, 18], [1190, 14, 982, 18, "children"], [1190, 22, 982, 18], [1190, 24, 984, 17, "processingState"], [1190, 39, 984, 32], [1190, 44, 984, 37], [1190, 50, 984, 43], [1190, 66, 985, 18], [1190, 70, 985, 18, "_jsxDevRuntime"], [1190, 84, 985, 18], [1190, 85, 985, 18, "jsxDEV"], [1190, 91, 985, 18], [1190, 93, 985, 19, "_View"], [1190, 98, 985, 19], [1190, 99, 985, 19, "default"], [1190, 106, 985, 23], [1191, 16, 985, 24, "style"], [1191, 21, 985, 29], [1191, 23, 985, 31, "styles"], [1191, 29, 985, 37], [1191, 30, 985, 38, "shutterInner"], [1192, 14, 985, 51], [1193, 16, 985, 51, "fileName"], [1193, 24, 985, 51], [1193, 26, 985, 51, "_jsxFileName"], [1193, 38, 985, 51], [1194, 16, 985, 51, "lineNumber"], [1194, 26, 985, 51], [1195, 16, 985, 51, "columnNumber"], [1195, 28, 985, 51], [1196, 14, 985, 51], [1196, 21, 985, 53], [1196, 22, 985, 54], [1196, 38, 987, 18], [1196, 42, 987, 18, "_jsxDevRuntime"], [1196, 56, 987, 18], [1196, 57, 987, 18, "jsxDEV"], [1196, 63, 987, 18], [1196, 65, 987, 19, "_ActivityIndicator"], [1196, 83, 987, 19], [1196, 84, 987, 19, "default"], [1196, 91, 987, 36], [1197, 16, 987, 37, "size"], [1197, 20, 987, 41], [1197, 22, 987, 42], [1197, 29, 987, 49], [1198, 16, 987, 50, "color"], [1198, 21, 987, 55], [1198, 23, 987, 56], [1199, 14, 987, 65], [1200, 16, 987, 65, "fileName"], [1200, 24, 987, 65], [1200, 26, 987, 65, "_jsxFileName"], [1200, 38, 987, 65], [1201, 16, 987, 65, "lineNumber"], [1201, 26, 987, 65], [1202, 16, 987, 65, "columnNumber"], [1202, 28, 987, 65], [1203, 14, 987, 65], [1203, 21, 987, 67], [1204, 12, 988, 17], [1205, 14, 988, 17, "fileName"], [1205, 22, 988, 17], [1205, 24, 988, 17, "_jsxFileName"], [1205, 36, 988, 17], [1206, 14, 988, 17, "lineNumber"], [1206, 24, 988, 17], [1207, 14, 988, 17, "columnNumber"], [1207, 26, 988, 17], [1208, 12, 988, 17], [1208, 19, 989, 32], [1208, 20, 989, 33], [1208, 35, 990, 14], [1208, 39, 990, 14, "_jsxDevRuntime"], [1208, 53, 990, 14], [1208, 54, 990, 14, "jsxDEV"], [1208, 60, 990, 14], [1208, 62, 990, 15, "_Text"], [1208, 67, 990, 15], [1208, 68, 990, 15, "default"], [1208, 75, 990, 19], [1209, 14, 990, 20, "style"], [1209, 19, 990, 25], [1209, 21, 990, 27, "styles"], [1209, 27, 990, 33], [1209, 28, 990, 34, "privacyNote"], [1209, 39, 990, 46], [1210, 14, 990, 46, "children"], [1210, 22, 990, 46], [1210, 24, 990, 47], [1211, 12, 992, 14], [1212, 14, 992, 14, "fileName"], [1212, 22, 992, 14], [1212, 24, 992, 14, "_jsxFileName"], [1212, 36, 992, 14], [1213, 14, 992, 14, "lineNumber"], [1213, 24, 992, 14], [1214, 14, 992, 14, "columnNumber"], [1214, 26, 992, 14], [1215, 12, 992, 14], [1215, 19, 992, 20], [1215, 20, 992, 21], [1216, 10, 992, 21], [1217, 12, 992, 21, "fileName"], [1217, 20, 992, 21], [1217, 22, 992, 21, "_jsxFileName"], [1217, 34, 992, 21], [1218, 12, 992, 21, "lineNumber"], [1218, 22, 992, 21], [1219, 12, 992, 21, "columnNumber"], [1219, 24, 992, 21], [1220, 10, 992, 21], [1220, 17, 993, 18], [1220, 18, 993, 19], [1221, 8, 993, 19], [1221, 23, 994, 12], [1221, 24, 995, 9], [1222, 6, 995, 9], [1223, 8, 995, 9, "fileName"], [1223, 16, 995, 9], [1223, 18, 995, 9, "_jsxFileName"], [1223, 30, 995, 9], [1224, 8, 995, 9, "lineNumber"], [1224, 18, 995, 9], [1225, 8, 995, 9, "columnNumber"], [1225, 20, 995, 9], [1226, 6, 995, 9], [1226, 13, 996, 12], [1226, 14, 996, 13], [1226, 29, 998, 6], [1226, 33, 998, 6, "_jsxDevRuntime"], [1226, 47, 998, 6], [1226, 48, 998, 6, "jsxDEV"], [1226, 54, 998, 6], [1226, 56, 998, 7, "_Modal"], [1226, 62, 998, 7], [1226, 63, 998, 7, "default"], [1226, 70, 998, 12], [1227, 8, 999, 8, "visible"], [1227, 15, 999, 15], [1227, 17, 999, 17, "processingState"], [1227, 32, 999, 32], [1227, 37, 999, 37], [1227, 43, 999, 43], [1227, 47, 999, 47, "processingState"], [1227, 62, 999, 62], [1227, 67, 999, 67], [1227, 74, 999, 75], [1228, 8, 1000, 8, "transparent"], [1228, 19, 1000, 19], [1229, 8, 1001, 8, "animationType"], [1229, 21, 1001, 21], [1229, 23, 1001, 22], [1229, 29, 1001, 28], [1230, 8, 1001, 28, "children"], [1230, 16, 1001, 28], [1230, 31, 1003, 8], [1230, 35, 1003, 8, "_jsxDevRuntime"], [1230, 49, 1003, 8], [1230, 50, 1003, 8, "jsxDEV"], [1230, 56, 1003, 8], [1230, 58, 1003, 9, "_View"], [1230, 63, 1003, 9], [1230, 64, 1003, 9, "default"], [1230, 71, 1003, 13], [1231, 10, 1003, 14, "style"], [1231, 15, 1003, 19], [1231, 17, 1003, 21, "styles"], [1231, 23, 1003, 27], [1231, 24, 1003, 28, "processingModal"], [1231, 39, 1003, 44], [1232, 10, 1003, 44, "children"], [1232, 18, 1003, 44], [1232, 33, 1004, 10], [1232, 37, 1004, 10, "_jsxDevRuntime"], [1232, 51, 1004, 10], [1232, 52, 1004, 10, "jsxDEV"], [1232, 58, 1004, 10], [1232, 60, 1004, 11, "_View"], [1232, 65, 1004, 11], [1232, 66, 1004, 11, "default"], [1232, 73, 1004, 15], [1233, 12, 1004, 16, "style"], [1233, 17, 1004, 21], [1233, 19, 1004, 23, "styles"], [1233, 25, 1004, 29], [1233, 26, 1004, 30, "processingContent"], [1233, 43, 1004, 48], [1234, 12, 1004, 48, "children"], [1234, 20, 1004, 48], [1234, 36, 1005, 12], [1234, 40, 1005, 12, "_jsxDevRuntime"], [1234, 54, 1005, 12], [1234, 55, 1005, 12, "jsxDEV"], [1234, 61, 1005, 12], [1234, 63, 1005, 13, "_ActivityIndicator"], [1234, 81, 1005, 13], [1234, 82, 1005, 13, "default"], [1234, 89, 1005, 30], [1235, 14, 1005, 31, "size"], [1235, 18, 1005, 35], [1235, 20, 1005, 36], [1235, 27, 1005, 43], [1236, 14, 1005, 44, "color"], [1236, 19, 1005, 49], [1236, 21, 1005, 50], [1237, 12, 1005, 59], [1238, 14, 1005, 59, "fileName"], [1238, 22, 1005, 59], [1238, 24, 1005, 59, "_jsxFileName"], [1238, 36, 1005, 59], [1239, 14, 1005, 59, "lineNumber"], [1239, 24, 1005, 59], [1240, 14, 1005, 59, "columnNumber"], [1240, 26, 1005, 59], [1241, 12, 1005, 59], [1241, 19, 1005, 61], [1241, 20, 1005, 62], [1241, 35, 1007, 12], [1241, 39, 1007, 12, "_jsxDevRuntime"], [1241, 53, 1007, 12], [1241, 54, 1007, 12, "jsxDEV"], [1241, 60, 1007, 12], [1241, 62, 1007, 13, "_Text"], [1241, 67, 1007, 13], [1241, 68, 1007, 13, "default"], [1241, 75, 1007, 17], [1242, 14, 1007, 18, "style"], [1242, 19, 1007, 23], [1242, 21, 1007, 25, "styles"], [1242, 27, 1007, 31], [1242, 28, 1007, 32, "processingTitle"], [1242, 43, 1007, 48], [1243, 14, 1007, 48, "children"], [1243, 22, 1007, 48], [1243, 25, 1008, 15, "processingState"], [1243, 40, 1008, 30], [1243, 45, 1008, 35], [1243, 56, 1008, 46], [1243, 60, 1008, 50], [1243, 80, 1008, 70], [1243, 82, 1009, 15, "processingState"], [1243, 97, 1009, 30], [1243, 102, 1009, 35], [1243, 113, 1009, 46], [1243, 117, 1009, 50], [1243, 146, 1009, 79], [1243, 148, 1010, 15, "processingState"], [1243, 163, 1010, 30], [1243, 168, 1010, 35], [1243, 180, 1010, 47], [1243, 184, 1010, 51], [1243, 216, 1010, 83], [1243, 218, 1011, 15, "processingState"], [1243, 233, 1011, 30], [1243, 238, 1011, 35], [1243, 249, 1011, 46], [1243, 253, 1011, 50], [1243, 275, 1011, 72], [1244, 12, 1011, 72], [1245, 14, 1011, 72, "fileName"], [1245, 22, 1011, 72], [1245, 24, 1011, 72, "_jsxFileName"], [1245, 36, 1011, 72], [1246, 14, 1011, 72, "lineNumber"], [1246, 24, 1011, 72], [1247, 14, 1011, 72, "columnNumber"], [1247, 26, 1011, 72], [1248, 12, 1011, 72], [1248, 19, 1012, 18], [1248, 20, 1012, 19], [1248, 35, 1013, 12], [1248, 39, 1013, 12, "_jsxDevRuntime"], [1248, 53, 1013, 12], [1248, 54, 1013, 12, "jsxDEV"], [1248, 60, 1013, 12], [1248, 62, 1013, 13, "_View"], [1248, 67, 1013, 13], [1248, 68, 1013, 13, "default"], [1248, 75, 1013, 17], [1249, 14, 1013, 18, "style"], [1249, 19, 1013, 23], [1249, 21, 1013, 25, "styles"], [1249, 27, 1013, 31], [1249, 28, 1013, 32, "progressBar"], [1249, 39, 1013, 44], [1250, 14, 1013, 44, "children"], [1250, 22, 1013, 44], [1250, 37, 1014, 14], [1250, 41, 1014, 14, "_jsxDevRuntime"], [1250, 55, 1014, 14], [1250, 56, 1014, 14, "jsxDEV"], [1250, 62, 1014, 14], [1250, 64, 1014, 15, "_View"], [1250, 69, 1014, 15], [1250, 70, 1014, 15, "default"], [1250, 77, 1014, 19], [1251, 16, 1015, 16, "style"], [1251, 21, 1015, 21], [1251, 23, 1015, 23], [1251, 24, 1016, 18, "styles"], [1251, 30, 1016, 24], [1251, 31, 1016, 25, "progressFill"], [1251, 43, 1016, 37], [1251, 45, 1017, 18], [1252, 18, 1017, 20, "width"], [1252, 23, 1017, 25], [1252, 25, 1017, 27], [1252, 28, 1017, 30, "processingProgress"], [1252, 46, 1017, 48], [1253, 16, 1017, 52], [1253, 17, 1017, 53], [1254, 14, 1018, 18], [1255, 16, 1018, 18, "fileName"], [1255, 24, 1018, 18], [1255, 26, 1018, 18, "_jsxFileName"], [1255, 38, 1018, 18], [1256, 16, 1018, 18, "lineNumber"], [1256, 26, 1018, 18], [1257, 16, 1018, 18, "columnNumber"], [1257, 28, 1018, 18], [1258, 14, 1018, 18], [1258, 21, 1019, 15], [1259, 12, 1019, 16], [1260, 14, 1019, 16, "fileName"], [1260, 22, 1019, 16], [1260, 24, 1019, 16, "_jsxFileName"], [1260, 36, 1019, 16], [1261, 14, 1019, 16, "lineNumber"], [1261, 24, 1019, 16], [1262, 14, 1019, 16, "columnNumber"], [1262, 26, 1019, 16], [1263, 12, 1019, 16], [1263, 19, 1020, 18], [1263, 20, 1020, 19], [1263, 35, 1021, 12], [1263, 39, 1021, 12, "_jsxDevRuntime"], [1263, 53, 1021, 12], [1263, 54, 1021, 12, "jsxDEV"], [1263, 60, 1021, 12], [1263, 62, 1021, 13, "_Text"], [1263, 67, 1021, 13], [1263, 68, 1021, 13, "default"], [1263, 75, 1021, 17], [1264, 14, 1021, 18, "style"], [1264, 19, 1021, 23], [1264, 21, 1021, 25, "styles"], [1264, 27, 1021, 31], [1264, 28, 1021, 32, "processingDescription"], [1264, 49, 1021, 54], [1265, 14, 1021, 54, "children"], [1265, 22, 1021, 54], [1265, 25, 1022, 15, "processingState"], [1265, 40, 1022, 30], [1265, 45, 1022, 35], [1265, 56, 1022, 46], [1265, 60, 1022, 50], [1265, 89, 1022, 79], [1265, 91, 1023, 15, "processingState"], [1265, 106, 1023, 30], [1265, 111, 1023, 35], [1265, 122, 1023, 46], [1265, 126, 1023, 50], [1265, 164, 1023, 88], [1265, 166, 1024, 15, "processingState"], [1265, 181, 1024, 30], [1265, 186, 1024, 35], [1265, 198, 1024, 47], [1265, 202, 1024, 51], [1265, 247, 1024, 96], [1265, 249, 1025, 15, "processingState"], [1265, 264, 1025, 30], [1265, 269, 1025, 35], [1265, 280, 1025, 46], [1265, 284, 1025, 50], [1265, 325, 1025, 91], [1266, 12, 1025, 91], [1267, 14, 1025, 91, "fileName"], [1267, 22, 1025, 91], [1267, 24, 1025, 91, "_jsxFileName"], [1267, 36, 1025, 91], [1268, 14, 1025, 91, "lineNumber"], [1268, 24, 1025, 91], [1269, 14, 1025, 91, "columnNumber"], [1269, 26, 1025, 91], [1270, 12, 1025, 91], [1270, 19, 1026, 18], [1270, 20, 1026, 19], [1270, 22, 1027, 13, "processingState"], [1270, 37, 1027, 28], [1270, 42, 1027, 33], [1270, 53, 1027, 44], [1270, 70, 1028, 14], [1270, 74, 1028, 14, "_jsxDevRuntime"], [1270, 88, 1028, 14], [1270, 89, 1028, 14, "jsxDEV"], [1270, 95, 1028, 14], [1270, 97, 1028, 15, "_lucideReactNative"], [1270, 115, 1028, 15], [1270, 116, 1028, 15, "CheckCircle"], [1270, 127, 1028, 26], [1271, 14, 1028, 27, "size"], [1271, 18, 1028, 31], [1271, 20, 1028, 33], [1271, 22, 1028, 36], [1272, 14, 1028, 37, "color"], [1272, 19, 1028, 42], [1272, 21, 1028, 43], [1272, 30, 1028, 52], [1273, 14, 1028, 53, "style"], [1273, 19, 1028, 58], [1273, 21, 1028, 60, "styles"], [1273, 27, 1028, 66], [1273, 28, 1028, 67, "successIcon"], [1274, 12, 1028, 79], [1275, 14, 1028, 79, "fileName"], [1275, 22, 1028, 79], [1275, 24, 1028, 79, "_jsxFileName"], [1275, 36, 1028, 79], [1276, 14, 1028, 79, "lineNumber"], [1276, 24, 1028, 79], [1277, 14, 1028, 79, "columnNumber"], [1277, 26, 1028, 79], [1278, 12, 1028, 79], [1278, 19, 1028, 81], [1278, 20, 1029, 13], [1279, 10, 1029, 13], [1280, 12, 1029, 13, "fileName"], [1280, 20, 1029, 13], [1280, 22, 1029, 13, "_jsxFileName"], [1280, 34, 1029, 13], [1281, 12, 1029, 13, "lineNumber"], [1281, 22, 1029, 13], [1282, 12, 1029, 13, "columnNumber"], [1282, 24, 1029, 13], [1283, 10, 1029, 13], [1283, 17, 1030, 16], [1284, 8, 1030, 17], [1285, 10, 1030, 17, "fileName"], [1285, 18, 1030, 17], [1285, 20, 1030, 17, "_jsxFileName"], [1285, 32, 1030, 17], [1286, 10, 1030, 17, "lineNumber"], [1286, 20, 1030, 17], [1287, 10, 1030, 17, "columnNumber"], [1287, 22, 1030, 17], [1288, 8, 1030, 17], [1288, 15, 1031, 14], [1289, 6, 1031, 15], [1290, 8, 1031, 15, "fileName"], [1290, 16, 1031, 15], [1290, 18, 1031, 15, "_jsxFileName"], [1290, 30, 1031, 15], [1291, 8, 1031, 15, "lineNumber"], [1291, 18, 1031, 15], [1292, 8, 1031, 15, "columnNumber"], [1292, 20, 1031, 15], [1293, 6, 1031, 15], [1293, 13, 1032, 13], [1293, 14, 1032, 14], [1293, 29, 1034, 6], [1293, 33, 1034, 6, "_jsxDevRuntime"], [1293, 47, 1034, 6], [1293, 48, 1034, 6, "jsxDEV"], [1293, 54, 1034, 6], [1293, 56, 1034, 7, "_Modal"], [1293, 62, 1034, 7], [1293, 63, 1034, 7, "default"], [1293, 70, 1034, 12], [1294, 8, 1035, 8, "visible"], [1294, 15, 1035, 15], [1294, 17, 1035, 17, "processingState"], [1294, 32, 1035, 32], [1294, 37, 1035, 37], [1294, 44, 1035, 45], [1295, 8, 1036, 8, "transparent"], [1295, 19, 1036, 19], [1296, 8, 1037, 8, "animationType"], [1296, 21, 1037, 21], [1296, 23, 1037, 22], [1296, 29, 1037, 28], [1297, 8, 1037, 28, "children"], [1297, 16, 1037, 28], [1297, 31, 1039, 8], [1297, 35, 1039, 8, "_jsxDevRuntime"], [1297, 49, 1039, 8], [1297, 50, 1039, 8, "jsxDEV"], [1297, 56, 1039, 8], [1297, 58, 1039, 9, "_View"], [1297, 63, 1039, 9], [1297, 64, 1039, 9, "default"], [1297, 71, 1039, 13], [1298, 10, 1039, 14, "style"], [1298, 15, 1039, 19], [1298, 17, 1039, 21, "styles"], [1298, 23, 1039, 27], [1298, 24, 1039, 28, "processingModal"], [1298, 39, 1039, 44], [1299, 10, 1039, 44, "children"], [1299, 18, 1039, 44], [1299, 33, 1040, 10], [1299, 37, 1040, 10, "_jsxDevRuntime"], [1299, 51, 1040, 10], [1299, 52, 1040, 10, "jsxDEV"], [1299, 58, 1040, 10], [1299, 60, 1040, 11, "_View"], [1299, 65, 1040, 11], [1299, 66, 1040, 11, "default"], [1299, 73, 1040, 15], [1300, 12, 1040, 16, "style"], [1300, 17, 1040, 21], [1300, 19, 1040, 23, "styles"], [1300, 25, 1040, 29], [1300, 26, 1040, 30, "errorContent"], [1300, 38, 1040, 43], [1301, 12, 1040, 43, "children"], [1301, 20, 1040, 43], [1301, 36, 1041, 12], [1301, 40, 1041, 12, "_jsxDevRuntime"], [1301, 54, 1041, 12], [1301, 55, 1041, 12, "jsxDEV"], [1301, 61, 1041, 12], [1301, 63, 1041, 13, "_lucideReactNative"], [1301, 81, 1041, 13], [1301, 82, 1041, 13, "X"], [1301, 83, 1041, 14], [1302, 14, 1041, 15, "size"], [1302, 18, 1041, 19], [1302, 20, 1041, 21], [1302, 22, 1041, 24], [1303, 14, 1041, 25, "color"], [1303, 19, 1041, 30], [1303, 21, 1041, 31], [1304, 12, 1041, 40], [1305, 14, 1041, 40, "fileName"], [1305, 22, 1041, 40], [1305, 24, 1041, 40, "_jsxFileName"], [1305, 36, 1041, 40], [1306, 14, 1041, 40, "lineNumber"], [1306, 24, 1041, 40], [1307, 14, 1041, 40, "columnNumber"], [1307, 26, 1041, 40], [1308, 12, 1041, 40], [1308, 19, 1041, 42], [1308, 20, 1041, 43], [1308, 35, 1042, 12], [1308, 39, 1042, 12, "_jsxDevRuntime"], [1308, 53, 1042, 12], [1308, 54, 1042, 12, "jsxDEV"], [1308, 60, 1042, 12], [1308, 62, 1042, 13, "_Text"], [1308, 67, 1042, 13], [1308, 68, 1042, 13, "default"], [1308, 75, 1042, 17], [1309, 14, 1042, 18, "style"], [1309, 19, 1042, 23], [1309, 21, 1042, 25, "styles"], [1309, 27, 1042, 31], [1309, 28, 1042, 32, "errorTitle"], [1309, 38, 1042, 43], [1310, 14, 1042, 43, "children"], [1310, 22, 1042, 43], [1310, 24, 1042, 44], [1311, 12, 1042, 61], [1312, 14, 1042, 61, "fileName"], [1312, 22, 1042, 61], [1312, 24, 1042, 61, "_jsxFileName"], [1312, 36, 1042, 61], [1313, 14, 1042, 61, "lineNumber"], [1313, 24, 1042, 61], [1314, 14, 1042, 61, "columnNumber"], [1314, 26, 1042, 61], [1315, 12, 1042, 61], [1315, 19, 1042, 67], [1315, 20, 1042, 68], [1315, 35, 1043, 12], [1315, 39, 1043, 12, "_jsxDevRuntime"], [1315, 53, 1043, 12], [1315, 54, 1043, 12, "jsxDEV"], [1315, 60, 1043, 12], [1315, 62, 1043, 13, "_Text"], [1315, 67, 1043, 13], [1315, 68, 1043, 13, "default"], [1315, 75, 1043, 17], [1316, 14, 1043, 18, "style"], [1316, 19, 1043, 23], [1316, 21, 1043, 25, "styles"], [1316, 27, 1043, 31], [1316, 28, 1043, 32, "errorMessage"], [1316, 40, 1043, 45], [1317, 14, 1043, 45, "children"], [1317, 22, 1043, 45], [1317, 24, 1043, 47, "errorMessage"], [1318, 12, 1043, 59], [1319, 14, 1043, 59, "fileName"], [1319, 22, 1043, 59], [1319, 24, 1043, 59, "_jsxFileName"], [1319, 36, 1043, 59], [1320, 14, 1043, 59, "lineNumber"], [1320, 24, 1043, 59], [1321, 14, 1043, 59, "columnNumber"], [1321, 26, 1043, 59], [1322, 12, 1043, 59], [1322, 19, 1043, 66], [1322, 20, 1043, 67], [1322, 35, 1044, 12], [1322, 39, 1044, 12, "_jsxDevRuntime"], [1322, 53, 1044, 12], [1322, 54, 1044, 12, "jsxDEV"], [1322, 60, 1044, 12], [1322, 62, 1044, 13, "_TouchableOpacity"], [1322, 79, 1044, 13], [1322, 80, 1044, 13, "default"], [1322, 87, 1044, 29], [1323, 14, 1045, 14, "onPress"], [1323, 21, 1045, 21], [1323, 23, 1045, 23, "retryCapture"], [1323, 35, 1045, 36], [1324, 14, 1046, 14, "style"], [1324, 19, 1046, 19], [1324, 21, 1046, 21, "styles"], [1324, 27, 1046, 27], [1324, 28, 1046, 28, "primaryButton"], [1324, 41, 1046, 42], [1325, 14, 1046, 42, "children"], [1325, 22, 1046, 42], [1325, 37, 1048, 14], [1325, 41, 1048, 14, "_jsxDevRuntime"], [1325, 55, 1048, 14], [1325, 56, 1048, 14, "jsxDEV"], [1325, 62, 1048, 14], [1325, 64, 1048, 15, "_Text"], [1325, 69, 1048, 15], [1325, 70, 1048, 15, "default"], [1325, 77, 1048, 19], [1326, 16, 1048, 20, "style"], [1326, 21, 1048, 25], [1326, 23, 1048, 27, "styles"], [1326, 29, 1048, 33], [1326, 30, 1048, 34, "primaryButtonText"], [1326, 47, 1048, 52], [1327, 16, 1048, 52, "children"], [1327, 24, 1048, 52], [1327, 26, 1048, 53], [1328, 14, 1048, 62], [1329, 16, 1048, 62, "fileName"], [1329, 24, 1048, 62], [1329, 26, 1048, 62, "_jsxFileName"], [1329, 38, 1048, 62], [1330, 16, 1048, 62, "lineNumber"], [1330, 26, 1048, 62], [1331, 16, 1048, 62, "columnNumber"], [1331, 28, 1048, 62], [1332, 14, 1048, 62], [1332, 21, 1048, 68], [1333, 12, 1048, 69], [1334, 14, 1048, 69, "fileName"], [1334, 22, 1048, 69], [1334, 24, 1048, 69, "_jsxFileName"], [1334, 36, 1048, 69], [1335, 14, 1048, 69, "lineNumber"], [1335, 24, 1048, 69], [1336, 14, 1048, 69, "columnNumber"], [1336, 26, 1048, 69], [1337, 12, 1048, 69], [1337, 19, 1049, 30], [1337, 20, 1049, 31], [1337, 35, 1050, 12], [1337, 39, 1050, 12, "_jsxDevRuntime"], [1337, 53, 1050, 12], [1337, 54, 1050, 12, "jsxDEV"], [1337, 60, 1050, 12], [1337, 62, 1050, 13, "_TouchableOpacity"], [1337, 79, 1050, 13], [1337, 80, 1050, 13, "default"], [1337, 87, 1050, 29], [1338, 14, 1051, 14, "onPress"], [1338, 21, 1051, 21], [1338, 23, 1051, 23, "onCancel"], [1338, 31, 1051, 32], [1339, 14, 1052, 14, "style"], [1339, 19, 1052, 19], [1339, 21, 1052, 21, "styles"], [1339, 27, 1052, 27], [1339, 28, 1052, 28, "secondaryButton"], [1339, 43, 1052, 44], [1340, 14, 1052, 44, "children"], [1340, 22, 1052, 44], [1340, 37, 1054, 14], [1340, 41, 1054, 14, "_jsxDevRuntime"], [1340, 55, 1054, 14], [1340, 56, 1054, 14, "jsxDEV"], [1340, 62, 1054, 14], [1340, 64, 1054, 15, "_Text"], [1340, 69, 1054, 15], [1340, 70, 1054, 15, "default"], [1340, 77, 1054, 19], [1341, 16, 1054, 20, "style"], [1341, 21, 1054, 25], [1341, 23, 1054, 27, "styles"], [1341, 29, 1054, 33], [1341, 30, 1054, 34, "secondaryButtonText"], [1341, 49, 1054, 54], [1342, 16, 1054, 54, "children"], [1342, 24, 1054, 54], [1342, 26, 1054, 55], [1343, 14, 1054, 61], [1344, 16, 1054, 61, "fileName"], [1344, 24, 1054, 61], [1344, 26, 1054, 61, "_jsxFileName"], [1344, 38, 1054, 61], [1345, 16, 1054, 61, "lineNumber"], [1345, 26, 1054, 61], [1346, 16, 1054, 61, "columnNumber"], [1346, 28, 1054, 61], [1347, 14, 1054, 61], [1347, 21, 1054, 67], [1348, 12, 1054, 68], [1349, 14, 1054, 68, "fileName"], [1349, 22, 1054, 68], [1349, 24, 1054, 68, "_jsxFileName"], [1349, 36, 1054, 68], [1350, 14, 1054, 68, "lineNumber"], [1350, 24, 1054, 68], [1351, 14, 1054, 68, "columnNumber"], [1351, 26, 1054, 68], [1352, 12, 1054, 68], [1352, 19, 1055, 30], [1352, 20, 1055, 31], [1353, 10, 1055, 31], [1354, 12, 1055, 31, "fileName"], [1354, 20, 1055, 31], [1354, 22, 1055, 31, "_jsxFileName"], [1354, 34, 1055, 31], [1355, 12, 1055, 31, "lineNumber"], [1355, 22, 1055, 31], [1356, 12, 1055, 31, "columnNumber"], [1356, 24, 1055, 31], [1357, 10, 1055, 31], [1357, 17, 1056, 16], [1358, 8, 1056, 17], [1359, 10, 1056, 17, "fileName"], [1359, 18, 1056, 17], [1359, 20, 1056, 17, "_jsxFileName"], [1359, 32, 1056, 17], [1360, 10, 1056, 17, "lineNumber"], [1360, 20, 1056, 17], [1361, 10, 1056, 17, "columnNumber"], [1361, 22, 1056, 17], [1362, 8, 1056, 17], [1362, 15, 1057, 14], [1363, 6, 1057, 15], [1364, 8, 1057, 15, "fileName"], [1364, 16, 1057, 15], [1364, 18, 1057, 15, "_jsxFileName"], [1364, 30, 1057, 15], [1365, 8, 1057, 15, "lineNumber"], [1365, 18, 1057, 15], [1366, 8, 1057, 15, "columnNumber"], [1366, 20, 1057, 15], [1367, 6, 1057, 15], [1367, 13, 1058, 13], [1367, 14, 1058, 14], [1368, 4, 1058, 14], [1369, 6, 1058, 14, "fileName"], [1369, 14, 1058, 14], [1369, 16, 1058, 14, "_jsxFileName"], [1369, 28, 1058, 14], [1370, 6, 1058, 14, "lineNumber"], [1370, 16, 1058, 14], [1371, 6, 1058, 14, "columnNumber"], [1371, 18, 1058, 14], [1372, 4, 1058, 14], [1372, 11, 1059, 10], [1372, 12, 1059, 11], [1373, 2, 1061, 0], [1374, 2, 1061, 1, "_s"], [1374, 4, 1061, 1], [1374, 5, 51, 24, "EchoCameraWeb"], [1374, 18, 51, 37], [1375, 4, 51, 37], [1375, 12, 58, 42, "useCameraPermissions"], [1375, 44, 58, 62], [1375, 46, 72, 19, "useUpload"], [1375, 64, 72, 28], [1376, 2, 72, 28], [1377, 2, 72, 28, "_c"], [1377, 4, 72, 28], [1377, 7, 51, 24, "EchoCameraWeb"], [1377, 20, 51, 37], [1378, 2, 1062, 0], [1378, 8, 1062, 6, "styles"], [1378, 14, 1062, 12], [1378, 17, 1062, 15, "StyleSheet"], [1378, 36, 1062, 25], [1378, 37, 1062, 26, "create"], [1378, 43, 1062, 32], [1378, 44, 1062, 33], [1379, 4, 1063, 2, "container"], [1379, 13, 1063, 11], [1379, 15, 1063, 13], [1380, 6, 1064, 4, "flex"], [1380, 10, 1064, 8], [1380, 12, 1064, 10], [1380, 13, 1064, 11], [1381, 6, 1065, 4, "backgroundColor"], [1381, 21, 1065, 19], [1381, 23, 1065, 21], [1382, 4, 1066, 2], [1382, 5, 1066, 3], [1383, 4, 1067, 2, "cameraContainer"], [1383, 19, 1067, 17], [1383, 21, 1067, 19], [1384, 6, 1068, 4, "flex"], [1384, 10, 1068, 8], [1384, 12, 1068, 10], [1384, 13, 1068, 11], [1385, 6, 1069, 4, "max<PERSON><PERSON><PERSON>"], [1385, 14, 1069, 12], [1385, 16, 1069, 14], [1385, 19, 1069, 17], [1386, 6, 1070, 4, "alignSelf"], [1386, 15, 1070, 13], [1386, 17, 1070, 15], [1386, 25, 1070, 23], [1387, 6, 1071, 4, "width"], [1387, 11, 1071, 9], [1387, 13, 1071, 11], [1388, 4, 1072, 2], [1388, 5, 1072, 3], [1389, 4, 1073, 2, "camera"], [1389, 10, 1073, 8], [1389, 12, 1073, 10], [1390, 6, 1074, 4, "flex"], [1390, 10, 1074, 8], [1390, 12, 1074, 10], [1391, 4, 1075, 2], [1391, 5, 1075, 3], [1392, 4, 1076, 2, "headerOverlay"], [1392, 17, 1076, 15], [1392, 19, 1076, 17], [1393, 6, 1077, 4, "position"], [1393, 14, 1077, 12], [1393, 16, 1077, 14], [1393, 26, 1077, 24], [1394, 6, 1078, 4, "top"], [1394, 9, 1078, 7], [1394, 11, 1078, 9], [1394, 12, 1078, 10], [1395, 6, 1079, 4, "left"], [1395, 10, 1079, 8], [1395, 12, 1079, 10], [1395, 13, 1079, 11], [1396, 6, 1080, 4, "right"], [1396, 11, 1080, 9], [1396, 13, 1080, 11], [1396, 14, 1080, 12], [1397, 6, 1081, 4, "backgroundColor"], [1397, 21, 1081, 19], [1397, 23, 1081, 21], [1397, 36, 1081, 34], [1398, 6, 1082, 4, "paddingTop"], [1398, 16, 1082, 14], [1398, 18, 1082, 16], [1398, 20, 1082, 18], [1399, 6, 1083, 4, "paddingHorizontal"], [1399, 23, 1083, 21], [1399, 25, 1083, 23], [1399, 27, 1083, 25], [1400, 6, 1084, 4, "paddingBottom"], [1400, 19, 1084, 17], [1400, 21, 1084, 19], [1401, 4, 1085, 2], [1401, 5, 1085, 3], [1402, 4, 1086, 2, "headerContent"], [1402, 17, 1086, 15], [1402, 19, 1086, 17], [1403, 6, 1087, 4, "flexDirection"], [1403, 19, 1087, 17], [1403, 21, 1087, 19], [1403, 26, 1087, 24], [1404, 6, 1088, 4, "justifyContent"], [1404, 20, 1088, 18], [1404, 22, 1088, 20], [1404, 37, 1088, 35], [1405, 6, 1089, 4, "alignItems"], [1405, 16, 1089, 14], [1405, 18, 1089, 16], [1406, 4, 1090, 2], [1406, 5, 1090, 3], [1407, 4, 1091, 2, "headerLeft"], [1407, 14, 1091, 12], [1407, 16, 1091, 14], [1408, 6, 1092, 4, "flex"], [1408, 10, 1092, 8], [1408, 12, 1092, 10], [1409, 4, 1093, 2], [1409, 5, 1093, 3], [1410, 4, 1094, 2, "headerTitle"], [1410, 15, 1094, 13], [1410, 17, 1094, 15], [1411, 6, 1095, 4, "fontSize"], [1411, 14, 1095, 12], [1411, 16, 1095, 14], [1411, 18, 1095, 16], [1412, 6, 1096, 4, "fontWeight"], [1412, 16, 1096, 14], [1412, 18, 1096, 16], [1412, 23, 1096, 21], [1413, 6, 1097, 4, "color"], [1413, 11, 1097, 9], [1413, 13, 1097, 11], [1413, 19, 1097, 17], [1414, 6, 1098, 4, "marginBottom"], [1414, 18, 1098, 16], [1414, 20, 1098, 18], [1415, 4, 1099, 2], [1415, 5, 1099, 3], [1416, 4, 1100, 2, "subtitleRow"], [1416, 15, 1100, 13], [1416, 17, 1100, 15], [1417, 6, 1101, 4, "flexDirection"], [1417, 19, 1101, 17], [1417, 21, 1101, 19], [1417, 26, 1101, 24], [1418, 6, 1102, 4, "alignItems"], [1418, 16, 1102, 14], [1418, 18, 1102, 16], [1418, 26, 1102, 24], [1419, 6, 1103, 4, "marginBottom"], [1419, 18, 1103, 16], [1419, 20, 1103, 18], [1420, 4, 1104, 2], [1420, 5, 1104, 3], [1421, 4, 1105, 2, "webIcon"], [1421, 11, 1105, 9], [1421, 13, 1105, 11], [1422, 6, 1106, 4, "fontSize"], [1422, 14, 1106, 12], [1422, 16, 1106, 14], [1422, 18, 1106, 16], [1423, 6, 1107, 4, "marginRight"], [1423, 17, 1107, 15], [1423, 19, 1107, 17], [1424, 4, 1108, 2], [1424, 5, 1108, 3], [1425, 4, 1109, 2, "headerSubtitle"], [1425, 18, 1109, 16], [1425, 20, 1109, 18], [1426, 6, 1110, 4, "fontSize"], [1426, 14, 1110, 12], [1426, 16, 1110, 14], [1426, 18, 1110, 16], [1427, 6, 1111, 4, "color"], [1427, 11, 1111, 9], [1427, 13, 1111, 11], [1427, 19, 1111, 17], [1428, 6, 1112, 4, "opacity"], [1428, 13, 1112, 11], [1428, 15, 1112, 13], [1429, 4, 1113, 2], [1429, 5, 1113, 3], [1430, 4, 1114, 2, "challengeRow"], [1430, 16, 1114, 14], [1430, 18, 1114, 16], [1431, 6, 1115, 4, "flexDirection"], [1431, 19, 1115, 17], [1431, 21, 1115, 19], [1431, 26, 1115, 24], [1432, 6, 1116, 4, "alignItems"], [1432, 16, 1116, 14], [1432, 18, 1116, 16], [1433, 4, 1117, 2], [1433, 5, 1117, 3], [1434, 4, 1118, 2, "challengeCode"], [1434, 17, 1118, 15], [1434, 19, 1118, 17], [1435, 6, 1119, 4, "fontSize"], [1435, 14, 1119, 12], [1435, 16, 1119, 14], [1435, 18, 1119, 16], [1436, 6, 1120, 4, "color"], [1436, 11, 1120, 9], [1436, 13, 1120, 11], [1436, 19, 1120, 17], [1437, 6, 1121, 4, "marginLeft"], [1437, 16, 1121, 14], [1437, 18, 1121, 16], [1437, 19, 1121, 17], [1438, 6, 1122, 4, "fontFamily"], [1438, 16, 1122, 14], [1438, 18, 1122, 16], [1439, 4, 1123, 2], [1439, 5, 1123, 3], [1440, 4, 1124, 2, "closeButton"], [1440, 15, 1124, 13], [1440, 17, 1124, 15], [1441, 6, 1125, 4, "padding"], [1441, 13, 1125, 11], [1441, 15, 1125, 13], [1442, 4, 1126, 2], [1442, 5, 1126, 3], [1443, 4, 1127, 2, "privacyNotice"], [1443, 17, 1127, 15], [1443, 19, 1127, 17], [1444, 6, 1128, 4, "position"], [1444, 14, 1128, 12], [1444, 16, 1128, 14], [1444, 26, 1128, 24], [1445, 6, 1129, 4, "top"], [1445, 9, 1129, 7], [1445, 11, 1129, 9], [1445, 14, 1129, 12], [1446, 6, 1130, 4, "left"], [1446, 10, 1130, 8], [1446, 12, 1130, 10], [1446, 14, 1130, 12], [1447, 6, 1131, 4, "right"], [1447, 11, 1131, 9], [1447, 13, 1131, 11], [1447, 15, 1131, 13], [1448, 6, 1132, 4, "backgroundColor"], [1448, 21, 1132, 19], [1448, 23, 1132, 21], [1448, 48, 1132, 46], [1449, 6, 1133, 4, "borderRadius"], [1449, 18, 1133, 16], [1449, 20, 1133, 18], [1449, 21, 1133, 19], [1450, 6, 1134, 4, "padding"], [1450, 13, 1134, 11], [1450, 15, 1134, 13], [1450, 17, 1134, 15], [1451, 6, 1135, 4, "flexDirection"], [1451, 19, 1135, 17], [1451, 21, 1135, 19], [1451, 26, 1135, 24], [1452, 6, 1136, 4, "alignItems"], [1452, 16, 1136, 14], [1452, 18, 1136, 16], [1453, 4, 1137, 2], [1453, 5, 1137, 3], [1454, 4, 1138, 2, "privacyText"], [1454, 15, 1138, 13], [1454, 17, 1138, 15], [1455, 6, 1139, 4, "color"], [1455, 11, 1139, 9], [1455, 13, 1139, 11], [1455, 19, 1139, 17], [1456, 6, 1140, 4, "fontSize"], [1456, 14, 1140, 12], [1456, 16, 1140, 14], [1456, 18, 1140, 16], [1457, 6, 1141, 4, "marginLeft"], [1457, 16, 1141, 14], [1457, 18, 1141, 16], [1457, 19, 1141, 17], [1458, 6, 1142, 4, "flex"], [1458, 10, 1142, 8], [1458, 12, 1142, 10], [1459, 4, 1143, 2], [1459, 5, 1143, 3], [1460, 4, 1144, 2, "footer<PERSON><PERSON><PERSON>"], [1460, 17, 1144, 15], [1460, 19, 1144, 17], [1461, 6, 1145, 4, "position"], [1461, 14, 1145, 12], [1461, 16, 1145, 14], [1461, 26, 1145, 24], [1462, 6, 1146, 4, "bottom"], [1462, 12, 1146, 10], [1462, 14, 1146, 12], [1462, 15, 1146, 13], [1463, 6, 1147, 4, "left"], [1463, 10, 1147, 8], [1463, 12, 1147, 10], [1463, 13, 1147, 11], [1464, 6, 1148, 4, "right"], [1464, 11, 1148, 9], [1464, 13, 1148, 11], [1464, 14, 1148, 12], [1465, 6, 1149, 4, "backgroundColor"], [1465, 21, 1149, 19], [1465, 23, 1149, 21], [1465, 36, 1149, 34], [1466, 6, 1150, 4, "paddingBottom"], [1466, 19, 1150, 17], [1466, 21, 1150, 19], [1466, 23, 1150, 21], [1467, 6, 1151, 4, "paddingTop"], [1467, 16, 1151, 14], [1467, 18, 1151, 16], [1467, 20, 1151, 18], [1468, 6, 1152, 4, "alignItems"], [1468, 16, 1152, 14], [1468, 18, 1152, 16], [1469, 4, 1153, 2], [1469, 5, 1153, 3], [1470, 4, 1154, 2, "instruction"], [1470, 15, 1154, 13], [1470, 17, 1154, 15], [1471, 6, 1155, 4, "fontSize"], [1471, 14, 1155, 12], [1471, 16, 1155, 14], [1471, 18, 1155, 16], [1472, 6, 1156, 4, "color"], [1472, 11, 1156, 9], [1472, 13, 1156, 11], [1472, 19, 1156, 17], [1473, 6, 1157, 4, "marginBottom"], [1473, 18, 1157, 16], [1473, 20, 1157, 18], [1474, 4, 1158, 2], [1474, 5, 1158, 3], [1475, 4, 1159, 2, "shutterButton"], [1475, 17, 1159, 15], [1475, 19, 1159, 17], [1476, 6, 1160, 4, "width"], [1476, 11, 1160, 9], [1476, 13, 1160, 11], [1476, 15, 1160, 13], [1477, 6, 1161, 4, "height"], [1477, 12, 1161, 10], [1477, 14, 1161, 12], [1477, 16, 1161, 14], [1478, 6, 1162, 4, "borderRadius"], [1478, 18, 1162, 16], [1478, 20, 1162, 18], [1478, 22, 1162, 20], [1479, 6, 1163, 4, "backgroundColor"], [1479, 21, 1163, 19], [1479, 23, 1163, 21], [1479, 29, 1163, 27], [1480, 6, 1164, 4, "justifyContent"], [1480, 20, 1164, 18], [1480, 22, 1164, 20], [1480, 30, 1164, 28], [1481, 6, 1165, 4, "alignItems"], [1481, 16, 1165, 14], [1481, 18, 1165, 16], [1481, 26, 1165, 24], [1482, 6, 1166, 4, "marginBottom"], [1482, 18, 1166, 16], [1482, 20, 1166, 18], [1482, 22, 1166, 20], [1483, 6, 1167, 4], [1483, 9, 1167, 7, "Platform"], [1483, 26, 1167, 15], [1483, 27, 1167, 16, "select"], [1483, 33, 1167, 22], [1483, 34, 1167, 23], [1484, 8, 1168, 6, "ios"], [1484, 11, 1168, 9], [1484, 13, 1168, 11], [1485, 10, 1169, 8, "shadowColor"], [1485, 21, 1169, 19], [1485, 23, 1169, 21], [1485, 32, 1169, 30], [1486, 10, 1170, 8, "shadowOffset"], [1486, 22, 1170, 20], [1486, 24, 1170, 22], [1487, 12, 1170, 24, "width"], [1487, 17, 1170, 29], [1487, 19, 1170, 31], [1487, 20, 1170, 32], [1488, 12, 1170, 34, "height"], [1488, 18, 1170, 40], [1488, 20, 1170, 42], [1489, 10, 1170, 44], [1489, 11, 1170, 45], [1490, 10, 1171, 8, "shadowOpacity"], [1490, 23, 1171, 21], [1490, 25, 1171, 23], [1490, 28, 1171, 26], [1491, 10, 1172, 8, "shadowRadius"], [1491, 22, 1172, 20], [1491, 24, 1172, 22], [1492, 8, 1173, 6], [1492, 9, 1173, 7], [1493, 8, 1174, 6, "android"], [1493, 15, 1174, 13], [1493, 17, 1174, 15], [1494, 10, 1175, 8, "elevation"], [1494, 19, 1175, 17], [1494, 21, 1175, 19], [1495, 8, 1176, 6], [1495, 9, 1176, 7], [1496, 8, 1177, 6, "web"], [1496, 11, 1177, 9], [1496, 13, 1177, 11], [1497, 10, 1178, 8, "boxShadow"], [1497, 19, 1178, 17], [1497, 21, 1178, 19], [1498, 8, 1179, 6], [1499, 6, 1180, 4], [1499, 7, 1180, 5], [1500, 4, 1181, 2], [1500, 5, 1181, 3], [1501, 4, 1182, 2, "shutterButtonDisabled"], [1501, 25, 1182, 23], [1501, 27, 1182, 25], [1502, 6, 1183, 4, "opacity"], [1502, 13, 1183, 11], [1502, 15, 1183, 13], [1503, 4, 1184, 2], [1503, 5, 1184, 3], [1504, 4, 1185, 2, "shutterInner"], [1504, 16, 1185, 14], [1504, 18, 1185, 16], [1505, 6, 1186, 4, "width"], [1505, 11, 1186, 9], [1505, 13, 1186, 11], [1505, 15, 1186, 13], [1506, 6, 1187, 4, "height"], [1506, 12, 1187, 10], [1506, 14, 1187, 12], [1506, 16, 1187, 14], [1507, 6, 1188, 4, "borderRadius"], [1507, 18, 1188, 16], [1507, 20, 1188, 18], [1507, 22, 1188, 20], [1508, 6, 1189, 4, "backgroundColor"], [1508, 21, 1189, 19], [1508, 23, 1189, 21], [1508, 29, 1189, 27], [1509, 6, 1190, 4, "borderWidth"], [1509, 17, 1190, 15], [1509, 19, 1190, 17], [1509, 20, 1190, 18], [1510, 6, 1191, 4, "borderColor"], [1510, 17, 1191, 15], [1510, 19, 1191, 17], [1511, 4, 1192, 2], [1511, 5, 1192, 3], [1512, 4, 1193, 2, "privacyNote"], [1512, 15, 1193, 13], [1512, 17, 1193, 15], [1513, 6, 1194, 4, "fontSize"], [1513, 14, 1194, 12], [1513, 16, 1194, 14], [1513, 18, 1194, 16], [1514, 6, 1195, 4, "color"], [1514, 11, 1195, 9], [1514, 13, 1195, 11], [1515, 4, 1196, 2], [1515, 5, 1196, 3], [1516, 4, 1197, 2, "processingModal"], [1516, 19, 1197, 17], [1516, 21, 1197, 19], [1517, 6, 1198, 4, "flex"], [1517, 10, 1198, 8], [1517, 12, 1198, 10], [1517, 13, 1198, 11], [1518, 6, 1199, 4, "backgroundColor"], [1518, 21, 1199, 19], [1518, 23, 1199, 21], [1518, 43, 1199, 41], [1519, 6, 1200, 4, "justifyContent"], [1519, 20, 1200, 18], [1519, 22, 1200, 20], [1519, 30, 1200, 28], [1520, 6, 1201, 4, "alignItems"], [1520, 16, 1201, 14], [1520, 18, 1201, 16], [1521, 4, 1202, 2], [1521, 5, 1202, 3], [1522, 4, 1203, 2, "processingContent"], [1522, 21, 1203, 19], [1522, 23, 1203, 21], [1523, 6, 1204, 4, "backgroundColor"], [1523, 21, 1204, 19], [1523, 23, 1204, 21], [1523, 29, 1204, 27], [1524, 6, 1205, 4, "borderRadius"], [1524, 18, 1205, 16], [1524, 20, 1205, 18], [1524, 22, 1205, 20], [1525, 6, 1206, 4, "padding"], [1525, 13, 1206, 11], [1525, 15, 1206, 13], [1525, 17, 1206, 15], [1526, 6, 1207, 4, "width"], [1526, 11, 1207, 9], [1526, 13, 1207, 11], [1526, 18, 1207, 16], [1527, 6, 1208, 4, "max<PERSON><PERSON><PERSON>"], [1527, 14, 1208, 12], [1527, 16, 1208, 14], [1527, 19, 1208, 17], [1528, 6, 1209, 4, "alignItems"], [1528, 16, 1209, 14], [1528, 18, 1209, 16], [1529, 4, 1210, 2], [1529, 5, 1210, 3], [1530, 4, 1211, 2, "processingTitle"], [1530, 19, 1211, 17], [1530, 21, 1211, 19], [1531, 6, 1212, 4, "fontSize"], [1531, 14, 1212, 12], [1531, 16, 1212, 14], [1531, 18, 1212, 16], [1532, 6, 1213, 4, "fontWeight"], [1532, 16, 1213, 14], [1532, 18, 1213, 16], [1532, 23, 1213, 21], [1533, 6, 1214, 4, "color"], [1533, 11, 1214, 9], [1533, 13, 1214, 11], [1533, 22, 1214, 20], [1534, 6, 1215, 4, "marginTop"], [1534, 15, 1215, 13], [1534, 17, 1215, 15], [1534, 19, 1215, 17], [1535, 6, 1216, 4, "marginBottom"], [1535, 18, 1216, 16], [1535, 20, 1216, 18], [1536, 4, 1217, 2], [1536, 5, 1217, 3], [1537, 4, 1218, 2, "progressBar"], [1537, 15, 1218, 13], [1537, 17, 1218, 15], [1538, 6, 1219, 4, "width"], [1538, 11, 1219, 9], [1538, 13, 1219, 11], [1538, 19, 1219, 17], [1539, 6, 1220, 4, "height"], [1539, 12, 1220, 10], [1539, 14, 1220, 12], [1539, 15, 1220, 13], [1540, 6, 1221, 4, "backgroundColor"], [1540, 21, 1221, 19], [1540, 23, 1221, 21], [1540, 32, 1221, 30], [1541, 6, 1222, 4, "borderRadius"], [1541, 18, 1222, 16], [1541, 20, 1222, 18], [1541, 21, 1222, 19], [1542, 6, 1223, 4, "overflow"], [1542, 14, 1223, 12], [1542, 16, 1223, 14], [1542, 24, 1223, 22], [1543, 6, 1224, 4, "marginBottom"], [1543, 18, 1224, 16], [1543, 20, 1224, 18], [1544, 4, 1225, 2], [1544, 5, 1225, 3], [1545, 4, 1226, 2, "progressFill"], [1545, 16, 1226, 14], [1545, 18, 1226, 16], [1546, 6, 1227, 4, "height"], [1546, 12, 1227, 10], [1546, 14, 1227, 12], [1546, 20, 1227, 18], [1547, 6, 1228, 4, "backgroundColor"], [1547, 21, 1228, 19], [1547, 23, 1228, 21], [1547, 32, 1228, 30], [1548, 6, 1229, 4, "borderRadius"], [1548, 18, 1229, 16], [1548, 20, 1229, 18], [1549, 4, 1230, 2], [1549, 5, 1230, 3], [1550, 4, 1231, 2, "processingDescription"], [1550, 25, 1231, 23], [1550, 27, 1231, 25], [1551, 6, 1232, 4, "fontSize"], [1551, 14, 1232, 12], [1551, 16, 1232, 14], [1551, 18, 1232, 16], [1552, 6, 1233, 4, "color"], [1552, 11, 1233, 9], [1552, 13, 1233, 11], [1552, 22, 1233, 20], [1553, 6, 1234, 4, "textAlign"], [1553, 15, 1234, 13], [1553, 17, 1234, 15], [1554, 4, 1235, 2], [1554, 5, 1235, 3], [1555, 4, 1236, 2, "successIcon"], [1555, 15, 1236, 13], [1555, 17, 1236, 15], [1556, 6, 1237, 4, "marginTop"], [1556, 15, 1237, 13], [1556, 17, 1237, 15], [1557, 4, 1238, 2], [1557, 5, 1238, 3], [1558, 4, 1239, 2, "errorContent"], [1558, 16, 1239, 14], [1558, 18, 1239, 16], [1559, 6, 1240, 4, "backgroundColor"], [1559, 21, 1240, 19], [1559, 23, 1240, 21], [1559, 29, 1240, 27], [1560, 6, 1241, 4, "borderRadius"], [1560, 18, 1241, 16], [1560, 20, 1241, 18], [1560, 22, 1241, 20], [1561, 6, 1242, 4, "padding"], [1561, 13, 1242, 11], [1561, 15, 1242, 13], [1561, 17, 1242, 15], [1562, 6, 1243, 4, "width"], [1562, 11, 1243, 9], [1562, 13, 1243, 11], [1562, 18, 1243, 16], [1563, 6, 1244, 4, "max<PERSON><PERSON><PERSON>"], [1563, 14, 1244, 12], [1563, 16, 1244, 14], [1563, 19, 1244, 17], [1564, 6, 1245, 4, "alignItems"], [1564, 16, 1245, 14], [1564, 18, 1245, 16], [1565, 4, 1246, 2], [1565, 5, 1246, 3], [1566, 4, 1247, 2, "errorTitle"], [1566, 14, 1247, 12], [1566, 16, 1247, 14], [1567, 6, 1248, 4, "fontSize"], [1567, 14, 1248, 12], [1567, 16, 1248, 14], [1567, 18, 1248, 16], [1568, 6, 1249, 4, "fontWeight"], [1568, 16, 1249, 14], [1568, 18, 1249, 16], [1568, 23, 1249, 21], [1569, 6, 1250, 4, "color"], [1569, 11, 1250, 9], [1569, 13, 1250, 11], [1569, 22, 1250, 20], [1570, 6, 1251, 4, "marginTop"], [1570, 15, 1251, 13], [1570, 17, 1251, 15], [1570, 19, 1251, 17], [1571, 6, 1252, 4, "marginBottom"], [1571, 18, 1252, 16], [1571, 20, 1252, 18], [1572, 4, 1253, 2], [1572, 5, 1253, 3], [1573, 4, 1254, 2, "errorMessage"], [1573, 16, 1254, 14], [1573, 18, 1254, 16], [1574, 6, 1255, 4, "fontSize"], [1574, 14, 1255, 12], [1574, 16, 1255, 14], [1574, 18, 1255, 16], [1575, 6, 1256, 4, "color"], [1575, 11, 1256, 9], [1575, 13, 1256, 11], [1575, 22, 1256, 20], [1576, 6, 1257, 4, "textAlign"], [1576, 15, 1257, 13], [1576, 17, 1257, 15], [1576, 25, 1257, 23], [1577, 6, 1258, 4, "marginBottom"], [1577, 18, 1258, 16], [1577, 20, 1258, 18], [1578, 4, 1259, 2], [1578, 5, 1259, 3], [1579, 4, 1260, 2, "primaryButton"], [1579, 17, 1260, 15], [1579, 19, 1260, 17], [1580, 6, 1261, 4, "backgroundColor"], [1580, 21, 1261, 19], [1580, 23, 1261, 21], [1580, 32, 1261, 30], [1581, 6, 1262, 4, "paddingHorizontal"], [1581, 23, 1262, 21], [1581, 25, 1262, 23], [1581, 27, 1262, 25], [1582, 6, 1263, 4, "paddingVertical"], [1582, 21, 1263, 19], [1582, 23, 1263, 21], [1582, 25, 1263, 23], [1583, 6, 1264, 4, "borderRadius"], [1583, 18, 1264, 16], [1583, 20, 1264, 18], [1583, 21, 1264, 19], [1584, 6, 1265, 4, "marginTop"], [1584, 15, 1265, 13], [1584, 17, 1265, 15], [1585, 4, 1266, 2], [1585, 5, 1266, 3], [1586, 4, 1267, 2, "primaryButtonText"], [1586, 21, 1267, 19], [1586, 23, 1267, 21], [1587, 6, 1268, 4, "color"], [1587, 11, 1268, 9], [1587, 13, 1268, 11], [1587, 19, 1268, 17], [1588, 6, 1269, 4, "fontSize"], [1588, 14, 1269, 12], [1588, 16, 1269, 14], [1588, 18, 1269, 16], [1589, 6, 1270, 4, "fontWeight"], [1589, 16, 1270, 14], [1589, 18, 1270, 16], [1590, 4, 1271, 2], [1590, 5, 1271, 3], [1591, 4, 1272, 2, "secondaryButton"], [1591, 19, 1272, 17], [1591, 21, 1272, 19], [1592, 6, 1273, 4, "paddingHorizontal"], [1592, 23, 1273, 21], [1592, 25, 1273, 23], [1592, 27, 1273, 25], [1593, 6, 1274, 4, "paddingVertical"], [1593, 21, 1274, 19], [1593, 23, 1274, 21], [1593, 25, 1274, 23], [1594, 6, 1275, 4, "marginTop"], [1594, 15, 1275, 13], [1594, 17, 1275, 15], [1595, 4, 1276, 2], [1595, 5, 1276, 3], [1596, 4, 1277, 2, "secondaryButtonText"], [1596, 23, 1277, 21], [1596, 25, 1277, 23], [1597, 6, 1278, 4, "color"], [1597, 11, 1278, 9], [1597, 13, 1278, 11], [1597, 22, 1278, 20], [1598, 6, 1279, 4, "fontSize"], [1598, 14, 1279, 12], [1598, 16, 1279, 14], [1599, 4, 1280, 2], [1599, 5, 1280, 3], [1600, 4, 1281, 2, "permissionContent"], [1600, 21, 1281, 19], [1600, 23, 1281, 21], [1601, 6, 1282, 4, "flex"], [1601, 10, 1282, 8], [1601, 12, 1282, 10], [1601, 13, 1282, 11], [1602, 6, 1283, 4, "justifyContent"], [1602, 20, 1283, 18], [1602, 22, 1283, 20], [1602, 30, 1283, 28], [1603, 6, 1284, 4, "alignItems"], [1603, 16, 1284, 14], [1603, 18, 1284, 16], [1603, 26, 1284, 24], [1604, 6, 1285, 4, "padding"], [1604, 13, 1285, 11], [1604, 15, 1285, 13], [1605, 4, 1286, 2], [1605, 5, 1286, 3], [1606, 4, 1287, 2, "permissionTitle"], [1606, 19, 1287, 17], [1606, 21, 1287, 19], [1607, 6, 1288, 4, "fontSize"], [1607, 14, 1288, 12], [1607, 16, 1288, 14], [1607, 18, 1288, 16], [1608, 6, 1289, 4, "fontWeight"], [1608, 16, 1289, 14], [1608, 18, 1289, 16], [1608, 23, 1289, 21], [1609, 6, 1290, 4, "color"], [1609, 11, 1290, 9], [1609, 13, 1290, 11], [1609, 22, 1290, 20], [1610, 6, 1291, 4, "marginTop"], [1610, 15, 1291, 13], [1610, 17, 1291, 15], [1610, 19, 1291, 17], [1611, 6, 1292, 4, "marginBottom"], [1611, 18, 1292, 16], [1611, 20, 1292, 18], [1612, 4, 1293, 2], [1612, 5, 1293, 3], [1613, 4, 1294, 2, "permissionDescription"], [1613, 25, 1294, 23], [1613, 27, 1294, 25], [1614, 6, 1295, 4, "fontSize"], [1614, 14, 1295, 12], [1614, 16, 1295, 14], [1614, 18, 1295, 16], [1615, 6, 1296, 4, "color"], [1615, 11, 1296, 9], [1615, 13, 1296, 11], [1615, 22, 1296, 20], [1616, 6, 1297, 4, "textAlign"], [1616, 15, 1297, 13], [1616, 17, 1297, 15], [1616, 25, 1297, 23], [1617, 6, 1298, 4, "marginBottom"], [1617, 18, 1298, 16], [1617, 20, 1298, 18], [1618, 4, 1299, 2], [1618, 5, 1299, 3], [1619, 4, 1300, 2, "loadingText"], [1619, 15, 1300, 13], [1619, 17, 1300, 15], [1620, 6, 1301, 4, "color"], [1620, 11, 1301, 9], [1620, 13, 1301, 11], [1620, 22, 1301, 20], [1621, 6, 1302, 4, "marginTop"], [1621, 15, 1302, 13], [1621, 17, 1302, 15], [1622, 4, 1303, 2], [1622, 5, 1303, 3], [1623, 4, 1304, 2], [1624, 4, 1305, 2, "blurZone"], [1624, 12, 1305, 10], [1624, 14, 1305, 12], [1625, 6, 1306, 4, "position"], [1625, 14, 1306, 12], [1625, 16, 1306, 14], [1625, 26, 1306, 24], [1626, 6, 1307, 4, "overflow"], [1626, 14, 1307, 12], [1626, 16, 1307, 14], [1627, 4, 1308, 2], [1627, 5, 1308, 3], [1628, 4, 1309, 2, "previewChip"], [1628, 15, 1309, 13], [1628, 17, 1309, 15], [1629, 6, 1310, 4, "position"], [1629, 14, 1310, 12], [1629, 16, 1310, 14], [1629, 26, 1310, 24], [1630, 6, 1311, 4, "top"], [1630, 9, 1311, 7], [1630, 11, 1311, 9], [1630, 12, 1311, 10], [1631, 6, 1312, 4, "right"], [1631, 11, 1312, 9], [1631, 13, 1312, 11], [1631, 14, 1312, 12], [1632, 6, 1313, 4, "backgroundColor"], [1632, 21, 1313, 19], [1632, 23, 1313, 21], [1632, 40, 1313, 38], [1633, 6, 1314, 4, "paddingHorizontal"], [1633, 23, 1314, 21], [1633, 25, 1314, 23], [1633, 27, 1314, 25], [1634, 6, 1315, 4, "paddingVertical"], [1634, 21, 1315, 19], [1634, 23, 1315, 21], [1634, 24, 1315, 22], [1635, 6, 1316, 4, "borderRadius"], [1635, 18, 1316, 16], [1635, 20, 1316, 18], [1636, 4, 1317, 2], [1636, 5, 1317, 3], [1637, 4, 1318, 2, "previewChipText"], [1637, 19, 1318, 17], [1637, 21, 1318, 19], [1638, 6, 1319, 4, "color"], [1638, 11, 1319, 9], [1638, 13, 1319, 11], [1638, 19, 1319, 17], [1639, 6, 1320, 4, "fontSize"], [1639, 14, 1320, 12], [1639, 16, 1320, 14], [1639, 18, 1320, 16], [1640, 6, 1321, 4, "fontWeight"], [1640, 16, 1321, 14], [1640, 18, 1321, 16], [1641, 4, 1322, 2], [1642, 2, 1323, 0], [1642, 3, 1323, 1], [1642, 4, 1323, 2], [1643, 2, 1323, 3], [1643, 6, 1323, 3, "_c"], [1643, 8, 1323, 3], [1644, 2, 1323, 3, "$RefreshReg$"], [1644, 14, 1323, 3], [1644, 15, 1323, 3, "_c"], [1644, 17, 1323, 3], [1645, 0, 1323, 3], [1645, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "setTimeout$argument_0", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;+BSE;GT0C;qBUE;GVQ;8BWE;GX4B;2BYE;GZa;wBaE;GbiB;0BcG;eCyB;KDsD;GdC;0BgBE;GhBuB;gCiBE;kBCa;KDG;GjBC;mCmBG;wBfc,kCe;GnBoC;mCoBE;wBhBa;OgBI;oFCkC;UDM;8BEW;SF0C;uDhBa;sBmBC,wBnB;OgBC;GpBe;6BwBG;GxB6B;kCyBG;GzB8C;4B0BE;mBXmD;SWE;G1BO;uB2BE;G3BI;mC4BG;G5BM;YCE;GDK;oB6B2C;W7BG;yB8BC;W9BG;wB+BC;W/BI;CD4L"}}, "type": "js/module"}]}