# Installation
> `npm install --save @types/offscreencanvas`

# Summary
This package contains type definitions for offscreencanvas-browser ( https://html.spec.whatwg.org/multipage/canvas.html#the-offscreencanvas-interface ).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/offscreencanvas

Additional Details
 * Last updated: Fri, 08 Mar 2019 23:23:08 GMT
 * Dependencies: none
 * Global values: OffscreenCanvas, OffscreenCanvasRenderingContext2D, createImageBitmap

# Credits
These definitions were written by <PERSON> <https://github.com/kayahr>.
