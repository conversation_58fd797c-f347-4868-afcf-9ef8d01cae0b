{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createDrawingContext = void 0;\n  const _worklet_10674523464531_init_data = {\n    code: \"function DrawingContextJs1(Skia,paintPool,canvas){const paints=[];const colorFilters=[];const shaders=[];const imageFilters=[];const pathEffects=[];const paintDeclarations=[];let nextPaintIndex=1;paintPool[0]=Skia.Paint();paints.push(paintPool[0]);const savePaint=function(){if(nextPaintIndex>=paintPool.length){paintPool.push(Skia.Paint());}const nextPaint=paintPool[nextPaintIndex];nextPaint.assign(getCurrentPaint());paints.push(nextPaint);nextPaintIndex++;};const saveBackdropFilter=function(){let imageFilter=null;const imgf=imageFilters.pop();if(imgf){imageFilter=imgf;}else{const cf=colorFilters.pop();if(cf){imageFilter=Skia.ImageFilter.MakeColorFilter(cf,null);}}canvas.saveLayer(undefined,null,imageFilter);canvas.restore();};const getCurrentPaint=function(){return paints[paints.length-1];};const restorePaint=function(){return paints.pop();};const materializePaint=function(){if(colorFilters.length>0){getCurrentPaint().setColorFilter(colorFilters.reduceRight(function(inner,outer){return inner?Skia.ColorFilter.MakeCompose(outer,inner):outer;}));}if(shaders.length>0){getCurrentPaint().setShader(shaders[shaders.length-1]);}if(imageFilters.length>0){getCurrentPaint().setImageFilter(imageFilters.reduceRight(function(inner,outer){return inner?Skia.ImageFilter.MakeCompose(outer,inner):outer;}));}if(pathEffects.length>0){getCurrentPaint().setPathEffect(pathEffects.reduceRight(function(inner,outer){return inner?Skia.PathEffect.MakeCompose(outer,inner):outer;}));}colorFilters.length=0;shaders.length=0;imageFilters.length=0;pathEffects.length=0;};return{Skia:Skia,canvas:canvas,paints:paints,colorFilters:colorFilters,shaders:shaders,imageFilters:imageFilters,pathEffects:pathEffects,paintDeclarations:paintDeclarations,paintPool:paintPool,savePaint:savePaint,saveBackdropFilter:saveBackdropFilter,get paint(){return paints[paints.length-1];},restorePaint:restorePaint,materializePaint:materializePaint};}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\sksg\\\\Recorder\\\\DrawingContext.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"DrawingContextJs1\\\",\\\"Skia\\\",\\\"paintPool\\\",\\\"canvas\\\",\\\"paints\\\",\\\"colorFilters\\\",\\\"shaders\\\",\\\"imageFilters\\\",\\\"pathEffects\\\",\\\"paintDeclarations\\\",\\\"nextPaintIndex\\\",\\\"Paint\\\",\\\"push\\\",\\\"savePaint\\\",\\\"length\\\",\\\"nextPaint\\\",\\\"assign\\\",\\\"getCurrentPaint\\\",\\\"saveBackdropFilter\\\",\\\"imageFilter\\\",\\\"imgf\\\",\\\"pop\\\",\\\"cf\\\",\\\"ImageFilter\\\",\\\"MakeColorFilter\\\",\\\"saveLayer\\\",\\\"undefined\\\",\\\"restore\\\",\\\"restorePaint\\\",\\\"materializePaint\\\",\\\"setColorFilter\\\",\\\"reduceRight\\\",\\\"inner\\\",\\\"outer\\\",\\\"ColorFilter\\\",\\\"MakeCompose\\\",\\\"setShader\\\",\\\"setImageFilter\\\",\\\"setPathEffect\\\",\\\"PathEffect\\\",\\\"paint\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/sksg/Recorder/DrawingContext.js\\\"],\\\"mappings\\\":\\\"AAAoC,QAAC,CAAAA,iBAAeA,CAAAC,IAAE,CAAAC,SAAW,CAAAC,MAAA,EAI/D,KAAM,CAAAC,MAAM,CAAG,EAAE,CACjB,KAAM,CAAAC,YAAY,CAAG,EAAE,CACvB,KAAM,CAAAC,OAAO,CAAG,EAAE,CAClB,KAAM,CAAAC,YAAY,CAAG,EAAE,CACvB,KAAM,CAAAC,WAAW,CAAG,EAAE,CACtB,KAAM,CAAAC,iBAAiB,CAAG,EAAE,CAC5B,GAAI,CAAAC,cAAc,CAAG,CAAC,CAGtBR,SAAS,CAAC,CAAC,CAAC,CAAGD,IAAI,CAACU,KAAK,CAAC,CAAC,CAC3BP,MAAM,CAACQ,IAAI,CAACV,SAAS,CAAC,CAAC,CAAC,CAAC,CAGzB,KAAM,CAAAW,SAAS,CAAG,QAAAA,CAAA,CAAM,CAEtB,GAAIH,cAAc,EAAIR,SAAS,CAACY,MAAM,CAAE,CACtCZ,SAAS,CAACU,IAAI,CAACX,IAAI,CAACU,KAAK,CAAC,CAAC,CAAC,CAC9B,CACA,KAAM,CAAAI,SAAS,CAAGb,SAAS,CAACQ,cAAc,CAAC,CAC3CK,SAAS,CAACC,MAAM,CAACC,eAAe,CAAC,CAAC,CAAC,CACnCb,MAAM,CAACQ,IAAI,CAACG,SAAS,CAAC,CACtBL,cAAc,EAAE,CAClB,CAAC,CACD,KAAM,CAAAQ,kBAAkB,CAAG,QAAAA,CAAA,CAAM,CAC/B,GAAI,CAAAC,WAAW,CAAG,IAAI,CACtB,KAAM,CAAAC,IAAI,CAAGb,YAAY,CAACc,GAAG,CAAC,CAAC,CAC/B,GAAID,IAAI,CAAE,CACRD,WAAW,CAAGC,IAAI,CACpB,CAAC,IAAM,CACL,KAAM,CAAAE,EAAE,CAAGjB,YAAY,CAACgB,GAAG,CAAC,CAAC,CAC7B,GAAIC,EAAE,CAAE,CACNH,WAAW,CAAGlB,IAAI,CAACsB,WAAW,CAACC,eAAe,CAACF,EAAE,CAAE,IAAI,CAAC,CAC1D,CACF,CACAnB,MAAM,CAACsB,SAAS,CAACC,SAAS,CAAE,IAAI,CAAEP,WAAW,CAAC,CAC9ChB,MAAM,CAACwB,OAAO,CAAC,CAAC,CAClB,CAAC,CAGD,KAAM,CAAAV,eAAe,CAAG,QAAAA,CAAA,CAAM,CAC5B,MAAO,CAAAb,MAAM,CAACA,MAAM,CAACU,MAAM,CAAG,CAAC,CAAC,CAClC,CAAC,CACD,KAAM,CAAAc,YAAY,CAAG,QAAAA,CAAA,CAAM,CACzB,MAAO,CAAAxB,MAAM,CAACiB,GAAG,CAAC,CAAC,CACrB,CAAC,CACD,KAAM,CAAAQ,gBAAgB,CAAG,QAAAA,CAAA,CAAM,CAE7B,GAAIxB,YAAY,CAACS,MAAM,CAAG,CAAC,CAAE,CAC3BG,eAAe,CAAC,CAAC,CAACa,cAAc,CAACzB,YAAY,CAAC0B,WAAW,CAAC,SAACC,KAAK,CAAEC,KAAK,QAAK,CAAAD,KAAK,CAAG/B,IAAI,CAACiC,WAAW,CAACC,WAAW,CAACF,KAAK,CAAED,KAAK,CAAC,CAAGC,KAAK,GAAC,CAAC,CAC1I,CAEA,GAAI3B,OAAO,CAACQ,MAAM,CAAG,CAAC,CAAE,CACtBG,eAAe,CAAC,CAAC,CAACmB,SAAS,CAAC9B,OAAO,CAACA,OAAO,CAACQ,MAAM,CAAG,CAAC,CAAC,CAAC,CAC1D,CAEA,GAAIP,YAAY,CAACO,MAAM,CAAG,CAAC,CAAE,CAC3BG,eAAe,CAAC,CAAC,CAACoB,cAAc,CAAC9B,YAAY,CAACwB,WAAW,CAAC,SAACC,KAAK,CAAEC,KAAK,QAAK,CAAAD,KAAK,CAAG/B,IAAI,CAACsB,WAAW,CAACY,WAAW,CAACF,KAAK,CAAED,KAAK,CAAC,CAAGC,KAAK,GAAC,CAAC,CAC1I,CAGA,GAAIzB,WAAW,CAACM,MAAM,CAAG,CAAC,CAAE,CAC1BG,eAAe,CAAC,CAAC,CAACqB,aAAa,CAAC9B,WAAW,CAACuB,WAAW,CAAC,SAACC,KAAK,CAAEC,KAAK,QAAK,CAAAD,KAAK,CAAG/B,IAAI,CAACsC,UAAU,CAACJ,WAAW,CAACF,KAAK,CAAED,KAAK,CAAC,CAAGC,KAAK,GAAC,CAAC,CACvI,CAGA5B,YAAY,CAACS,MAAM,CAAG,CAAC,CACvBR,OAAO,CAACQ,MAAM,CAAG,CAAC,CAClBP,YAAY,CAACO,MAAM,CAAG,CAAC,CACvBN,WAAW,CAACM,MAAM,CAAG,CAAC,CACxB,CAAC,CAGD,MAAO,CAELb,IAAI,CAAJA,IAAI,CACJE,MAAM,CAANA,MAAM,CACNC,MAAM,CAANA,MAAM,CACNC,YAAY,CAAZA,YAAY,CACZC,OAAO,CAAPA,OAAO,CACPC,YAAY,CAAZA,YAAY,CACZC,WAAW,CAAXA,WAAW,CACXC,iBAAiB,CAAjBA,iBAAiB,CACjBP,SAAS,CAATA,SAAS,CAETW,SAAS,CAATA,SAAS,CACTK,kBAAkB,CAAlBA,kBAAkB,CAClB,GAAI,CAAAsB,KAAKA,CAAA,CAAG,CACV,MAAO,CAAApC,MAAM,CAACA,MAAM,CAACU,MAAM,CAAG,CAAC,CAAC,CAClC,CAAC,CAEDc,YAAY,CAAZA,YAAY,CACZC,gBAAA,CAAAA,gBACF,CAAC,CACH\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const createDrawingContext = exports.createDrawingContext = function () {\n    const _e = [new global.Error(), 1, -27];\n    const DrawingContextJs1 = function (Skia, paintPool, canvas) {\n      // State (formerly class fields)\n      const paints = [];\n      const colorFilters = [];\n      const shaders = [];\n      const imageFilters = [];\n      const pathEffects = [];\n      const paintDeclarations = [];\n      let nextPaintIndex = 1;\n\n      // Initialize first paint\n      paintPool[0] = Skia.Paint();\n      paints.push(paintPool[0]);\n\n      // Methods (formerly class methods)\n      const savePaint = () => {\n        // Get next available paint from pool or create new one if needed\n        if (nextPaintIndex >= paintPool.length) {\n          paintPool.push(Skia.Paint());\n        }\n        const nextPaint = paintPool[nextPaintIndex];\n        nextPaint.assign(getCurrentPaint()); // Reuse allocation by copying properties\n        paints.push(nextPaint);\n        nextPaintIndex++;\n      };\n      const saveBackdropFilter = () => {\n        let imageFilter = null;\n        const imgf = imageFilters.pop();\n        if (imgf) {\n          imageFilter = imgf;\n        } else {\n          const cf = colorFilters.pop();\n          if (cf) {\n            imageFilter = Skia.ImageFilter.MakeColorFilter(cf, null);\n          }\n        }\n        canvas.saveLayer(undefined, null, imageFilter);\n        canvas.restore();\n      };\n\n      // Equivalent to the `get paint()` getter in the original class\n      const getCurrentPaint = () => {\n        return paints[paints.length - 1];\n      };\n      const restorePaint = () => {\n        return paints.pop();\n      };\n      const materializePaint = () => {\n        // Color Filters\n        if (colorFilters.length > 0) {\n          getCurrentPaint().setColorFilter(colorFilters.reduceRight((inner, outer) => inner ? Skia.ColorFilter.MakeCompose(outer, inner) : outer));\n        }\n        // Shaders\n        if (shaders.length > 0) {\n          getCurrentPaint().setShader(shaders[shaders.length - 1]);\n        }\n        // Image Filters\n        if (imageFilters.length > 0) {\n          getCurrentPaint().setImageFilter(imageFilters.reduceRight((inner, outer) => inner ? Skia.ImageFilter.MakeCompose(outer, inner) : outer));\n        }\n\n        // Path Effects\n        if (pathEffects.length > 0) {\n          getCurrentPaint().setPathEffect(pathEffects.reduceRight((inner, outer) => inner ? Skia.PathEffect.MakeCompose(outer, inner) : outer));\n        }\n\n        // Clear arrays\n        colorFilters.length = 0;\n        shaders.length = 0;\n        imageFilters.length = 0;\n        pathEffects.length = 0;\n      };\n\n      // Return an object containing the Skia reference, the canvas, and the methods\n      return {\n        // Public fields\n        Skia,\n        canvas,\n        paints,\n        colorFilters,\n        shaders,\n        imageFilters,\n        pathEffects,\n        paintDeclarations,\n        paintPool,\n        // Public methods\n        savePaint,\n        saveBackdropFilter,\n        get paint() {\n          return paints[paints.length - 1];\n        },\n        // the \"getter\" for the current paint\n        restorePaint,\n        materializePaint\n      };\n    };\n    DrawingContextJs1.__closure = {};\n    DrawingContextJs1.__workletHash = 10674523464531;\n    DrawingContextJs1.__initData = _worklet_10674523464531_init_data;\n    DrawingContextJs1.__stackDetails = _e;\n    return DrawingContextJs1;\n  }();\n});", "lineCount": 116, "map": [[12, 2, 1, 7], [12, 8, 1, 13, "createDrawingContext"], [12, 28, 1, 33], [12, 31, 1, 33, "exports"], [12, 38, 1, 33], [12, 39, 1, 33, "createDrawingContext"], [12, 59, 1, 33], [12, 62, 1, 36], [13, 4, 1, 36], [13, 10, 1, 36, "_e"], [13, 12, 1, 36], [13, 20, 1, 36, "global"], [13, 26, 1, 36], [13, 27, 1, 36, "Error"], [13, 32, 1, 36], [14, 4, 1, 36], [14, 10, 1, 36, "DrawingContextJs1"], [14, 27, 1, 36], [14, 39, 1, 36, "DrawingContextJs1"], [14, 40, 1, 37, "Skia"], [14, 44, 1, 41], [14, 46, 1, 43, "paintPool"], [14, 55, 1, 52], [14, 57, 1, 54, "canvas"], [14, 63, 1, 60], [14, 65, 1, 65], [15, 6, 4, 2], [16, 6, 5, 2], [16, 12, 5, 8, "paints"], [16, 18, 5, 14], [16, 21, 5, 17], [16, 23, 5, 19], [17, 6, 6, 2], [17, 12, 6, 8, "colorFilters"], [17, 24, 6, 20], [17, 27, 6, 23], [17, 29, 6, 25], [18, 6, 7, 2], [18, 12, 7, 8, "shaders"], [18, 19, 7, 15], [18, 22, 7, 18], [18, 24, 7, 20], [19, 6, 8, 2], [19, 12, 8, 8, "imageFilters"], [19, 24, 8, 20], [19, 27, 8, 23], [19, 29, 8, 25], [20, 6, 9, 2], [20, 12, 9, 8, "pathEffects"], [20, 23, 9, 19], [20, 26, 9, 22], [20, 28, 9, 24], [21, 6, 10, 2], [21, 12, 10, 8, "paintDeclarations"], [21, 29, 10, 25], [21, 32, 10, 28], [21, 34, 10, 30], [22, 6, 11, 2], [22, 10, 11, 6, "nextPaintIndex"], [22, 24, 11, 20], [22, 27, 11, 23], [22, 28, 11, 24], [24, 6, 13, 2], [25, 6, 14, 2, "paintPool"], [25, 15, 14, 11], [25, 16, 14, 12], [25, 17, 14, 13], [25, 18, 14, 14], [25, 21, 14, 17, "Skia"], [25, 25, 14, 21], [25, 26, 14, 22, "Paint"], [25, 31, 14, 27], [25, 32, 14, 28], [25, 33, 14, 29], [26, 6, 15, 2, "paints"], [26, 12, 15, 8], [26, 13, 15, 9, "push"], [26, 17, 15, 13], [26, 18, 15, 14, "paintPool"], [26, 27, 15, 23], [26, 28, 15, 24], [26, 29, 15, 25], [26, 30, 15, 26], [26, 31, 15, 27], [28, 6, 17, 2], [29, 6, 18, 2], [29, 12, 18, 8, "save<PERSON><PERSON>t"], [29, 21, 18, 17], [29, 24, 18, 20, "save<PERSON><PERSON>t"], [29, 25, 18, 20], [29, 30, 18, 26], [30, 8, 19, 4], [31, 8, 20, 4], [31, 12, 20, 8, "nextPaintIndex"], [31, 26, 20, 22], [31, 30, 20, 26, "paintPool"], [31, 39, 20, 35], [31, 40, 20, 36, "length"], [31, 46, 20, 42], [31, 48, 20, 44], [32, 10, 21, 6, "paintPool"], [32, 19, 21, 15], [32, 20, 21, 16, "push"], [32, 24, 21, 20], [32, 25, 21, 21, "Skia"], [32, 29, 21, 25], [32, 30, 21, 26, "Paint"], [32, 35, 21, 31], [32, 36, 21, 32], [32, 37, 21, 33], [32, 38, 21, 34], [33, 8, 22, 4], [34, 8, 23, 4], [34, 14, 23, 10, "<PERSON><PERSON><PERSON><PERSON>"], [34, 23, 23, 19], [34, 26, 23, 22, "paintPool"], [34, 35, 23, 31], [34, 36, 23, 32, "nextPaintIndex"], [34, 50, 23, 46], [34, 51, 23, 47], [35, 8, 24, 4, "<PERSON><PERSON><PERSON><PERSON>"], [35, 17, 24, 13], [35, 18, 24, 14, "assign"], [35, 24, 24, 20], [35, 25, 24, 21, "getCurrent<PERSON><PERSON>t"], [35, 40, 24, 36], [35, 41, 24, 37], [35, 42, 24, 38], [35, 43, 24, 39], [35, 44, 24, 40], [35, 45, 24, 41], [36, 8, 25, 4, "paints"], [36, 14, 25, 10], [36, 15, 25, 11, "push"], [36, 19, 25, 15], [36, 20, 25, 16, "<PERSON><PERSON><PERSON><PERSON>"], [36, 29, 25, 25], [36, 30, 25, 26], [37, 8, 26, 4, "nextPaintIndex"], [37, 22, 26, 18], [37, 24, 26, 20], [38, 6, 27, 2], [38, 7, 27, 3], [39, 6, 28, 2], [39, 12, 28, 8, "saveBackdropFilter"], [39, 30, 28, 26], [39, 33, 28, 29, "saveBackdropFilter"], [39, 34, 28, 29], [39, 39, 28, 35], [40, 8, 29, 4], [40, 12, 29, 8, "imageFilter"], [40, 23, 29, 19], [40, 26, 29, 22], [40, 30, 29, 26], [41, 8, 30, 4], [41, 14, 30, 10, "imgf"], [41, 18, 30, 14], [41, 21, 30, 17, "imageFilters"], [41, 33, 30, 29], [41, 34, 30, 30, "pop"], [41, 37, 30, 33], [41, 38, 30, 34], [41, 39, 30, 35], [42, 8, 31, 4], [42, 12, 31, 8, "imgf"], [42, 16, 31, 12], [42, 18, 31, 14], [43, 10, 32, 6, "imageFilter"], [43, 21, 32, 17], [43, 24, 32, 20, "imgf"], [43, 28, 32, 24], [44, 8, 33, 4], [44, 9, 33, 5], [44, 15, 33, 11], [45, 10, 34, 6], [45, 16, 34, 12, "cf"], [45, 18, 34, 14], [45, 21, 34, 17, "colorFilters"], [45, 33, 34, 29], [45, 34, 34, 30, "pop"], [45, 37, 34, 33], [45, 38, 34, 34], [45, 39, 34, 35], [46, 10, 35, 6], [46, 14, 35, 10, "cf"], [46, 16, 35, 12], [46, 18, 35, 14], [47, 12, 36, 8, "imageFilter"], [47, 23, 36, 19], [47, 26, 36, 22, "Skia"], [47, 30, 36, 26], [47, 31, 36, 27, "ImageFilter"], [47, 42, 36, 38], [47, 43, 36, 39, "MakeColorFilter"], [47, 58, 36, 54], [47, 59, 36, 55, "cf"], [47, 61, 36, 57], [47, 63, 36, 59], [47, 67, 36, 63], [47, 68, 36, 64], [48, 10, 37, 6], [49, 8, 38, 4], [50, 8, 39, 4, "canvas"], [50, 14, 39, 10], [50, 15, 39, 11, "save<PERSON><PERSON><PERSON>"], [50, 24, 39, 20], [50, 25, 39, 21, "undefined"], [50, 34, 39, 30], [50, 36, 39, 32], [50, 40, 39, 36], [50, 42, 39, 38, "imageFilter"], [50, 53, 39, 49], [50, 54, 39, 50], [51, 8, 40, 4, "canvas"], [51, 14, 40, 10], [51, 15, 40, 11, "restore"], [51, 22, 40, 18], [51, 23, 40, 19], [51, 24, 40, 20], [52, 6, 41, 2], [52, 7, 41, 3], [54, 6, 43, 2], [55, 6, 44, 2], [55, 12, 44, 8, "getCurrent<PERSON><PERSON>t"], [55, 27, 44, 23], [55, 30, 44, 26, "getCurrent<PERSON><PERSON>t"], [55, 31, 44, 26], [55, 36, 44, 32], [56, 8, 45, 4], [56, 15, 45, 11, "paints"], [56, 21, 45, 17], [56, 22, 45, 18, "paints"], [56, 28, 45, 24], [56, 29, 45, 25, "length"], [56, 35, 45, 31], [56, 38, 45, 34], [56, 39, 45, 35], [56, 40, 45, 36], [57, 6, 46, 2], [57, 7, 46, 3], [58, 6, 47, 2], [58, 12, 47, 8, "<PERSON><PERSON><PERSON><PERSON>"], [58, 24, 47, 20], [58, 27, 47, 23, "<PERSON><PERSON><PERSON><PERSON>"], [58, 28, 47, 23], [58, 33, 47, 29], [59, 8, 48, 4], [59, 15, 48, 11, "paints"], [59, 21, 48, 17], [59, 22, 48, 18, "pop"], [59, 25, 48, 21], [59, 26, 48, 22], [59, 27, 48, 23], [60, 6, 49, 2], [60, 7, 49, 3], [61, 6, 50, 2], [61, 12, 50, 8, "materialize<PERSON><PERSON><PERSON>"], [61, 28, 50, 24], [61, 31, 50, 27, "materialize<PERSON><PERSON><PERSON>"], [61, 32, 50, 27], [61, 37, 50, 33], [62, 8, 51, 4], [63, 8, 52, 4], [63, 12, 52, 8, "colorFilters"], [63, 24, 52, 20], [63, 25, 52, 21, "length"], [63, 31, 52, 27], [63, 34, 52, 30], [63, 35, 52, 31], [63, 37, 52, 33], [64, 10, 53, 6, "getCurrent<PERSON><PERSON>t"], [64, 25, 53, 21], [64, 26, 53, 22], [64, 27, 53, 23], [64, 28, 53, 24, "setColorFilter"], [64, 42, 53, 38], [64, 43, 53, 39, "colorFilters"], [64, 55, 53, 51], [64, 56, 53, 52, "reduceRight"], [64, 67, 53, 63], [64, 68, 53, 64], [64, 69, 53, 65, "inner"], [64, 74, 53, 70], [64, 76, 53, 72, "outer"], [64, 81, 53, 77], [64, 86, 53, 82, "inner"], [64, 91, 53, 87], [64, 94, 53, 90, "Skia"], [64, 98, 53, 94], [64, 99, 53, 95, "ColorFilter"], [64, 110, 53, 106], [64, 111, 53, 107, "MakeCompose"], [64, 122, 53, 118], [64, 123, 53, 119, "outer"], [64, 128, 53, 124], [64, 130, 53, 126, "inner"], [64, 135, 53, 131], [64, 136, 53, 132], [64, 139, 53, 135, "outer"], [64, 144, 53, 140], [64, 145, 53, 141], [64, 146, 53, 142], [65, 8, 54, 4], [66, 8, 55, 4], [67, 8, 56, 4], [67, 12, 56, 8, "shaders"], [67, 19, 56, 15], [67, 20, 56, 16, "length"], [67, 26, 56, 22], [67, 29, 56, 25], [67, 30, 56, 26], [67, 32, 56, 28], [68, 10, 57, 6, "getCurrent<PERSON><PERSON>t"], [68, 25, 57, 21], [68, 26, 57, 22], [68, 27, 57, 23], [68, 28, 57, 24, "<PERSON><PERSON><PERSON><PERSON>"], [68, 37, 57, 33], [68, 38, 57, 34, "shaders"], [68, 45, 57, 41], [68, 46, 57, 42, "shaders"], [68, 53, 57, 49], [68, 54, 57, 50, "length"], [68, 60, 57, 56], [68, 63, 57, 59], [68, 64, 57, 60], [68, 65, 57, 61], [68, 66, 57, 62], [69, 8, 58, 4], [70, 8, 59, 4], [71, 8, 60, 4], [71, 12, 60, 8, "imageFilters"], [71, 24, 60, 20], [71, 25, 60, 21, "length"], [71, 31, 60, 27], [71, 34, 60, 30], [71, 35, 60, 31], [71, 37, 60, 33], [72, 10, 61, 6, "getCurrent<PERSON><PERSON>t"], [72, 25, 61, 21], [72, 26, 61, 22], [72, 27, 61, 23], [72, 28, 61, 24, "setImageFilter"], [72, 42, 61, 38], [72, 43, 61, 39, "imageFilters"], [72, 55, 61, 51], [72, 56, 61, 52, "reduceRight"], [72, 67, 61, 63], [72, 68, 61, 64], [72, 69, 61, 65, "inner"], [72, 74, 61, 70], [72, 76, 61, 72, "outer"], [72, 81, 61, 77], [72, 86, 61, 82, "inner"], [72, 91, 61, 87], [72, 94, 61, 90, "Skia"], [72, 98, 61, 94], [72, 99, 61, 95, "ImageFilter"], [72, 110, 61, 106], [72, 111, 61, 107, "MakeCompose"], [72, 122, 61, 118], [72, 123, 61, 119, "outer"], [72, 128, 61, 124], [72, 130, 61, 126, "inner"], [72, 135, 61, 131], [72, 136, 61, 132], [72, 139, 61, 135, "outer"], [72, 144, 61, 140], [72, 145, 61, 141], [72, 146, 61, 142], [73, 8, 62, 4], [75, 8, 64, 4], [76, 8, 65, 4], [76, 12, 65, 8, "pathEffects"], [76, 23, 65, 19], [76, 24, 65, 20, "length"], [76, 30, 65, 26], [76, 33, 65, 29], [76, 34, 65, 30], [76, 36, 65, 32], [77, 10, 66, 6, "getCurrent<PERSON><PERSON>t"], [77, 25, 66, 21], [77, 26, 66, 22], [77, 27, 66, 23], [77, 28, 66, 24, "setPathEffect"], [77, 41, 66, 37], [77, 42, 66, 38, "pathEffects"], [77, 53, 66, 49], [77, 54, 66, 50, "reduceRight"], [77, 65, 66, 61], [77, 66, 66, 62], [77, 67, 66, 63, "inner"], [77, 72, 66, 68], [77, 74, 66, 70, "outer"], [77, 79, 66, 75], [77, 84, 66, 80, "inner"], [77, 89, 66, 85], [77, 92, 66, 88, "Skia"], [77, 96, 66, 92], [77, 97, 66, 93, "PathEffect"], [77, 107, 66, 103], [77, 108, 66, 104, "MakeCompose"], [77, 119, 66, 115], [77, 120, 66, 116, "outer"], [77, 125, 66, 121], [77, 127, 66, 123, "inner"], [77, 132, 66, 128], [77, 133, 66, 129], [77, 136, 66, 132, "outer"], [77, 141, 66, 137], [77, 142, 66, 138], [77, 143, 66, 139], [78, 8, 67, 4], [80, 8, 69, 4], [81, 8, 70, 4, "colorFilters"], [81, 20, 70, 16], [81, 21, 70, 17, "length"], [81, 27, 70, 23], [81, 30, 70, 26], [81, 31, 70, 27], [82, 8, 71, 4, "shaders"], [82, 15, 71, 11], [82, 16, 71, 12, "length"], [82, 22, 71, 18], [82, 25, 71, 21], [82, 26, 71, 22], [83, 8, 72, 4, "imageFilters"], [83, 20, 72, 16], [83, 21, 72, 17, "length"], [83, 27, 72, 23], [83, 30, 72, 26], [83, 31, 72, 27], [84, 8, 73, 4, "pathEffects"], [84, 19, 73, 15], [84, 20, 73, 16, "length"], [84, 26, 73, 22], [84, 29, 73, 25], [84, 30, 73, 26], [85, 6, 74, 2], [85, 7, 74, 3], [87, 6, 76, 2], [88, 6, 77, 2], [88, 13, 77, 9], [89, 8, 78, 4], [90, 8, 79, 4, "Skia"], [90, 12, 79, 8], [91, 8, 80, 4, "canvas"], [91, 14, 80, 10], [92, 8, 81, 4, "paints"], [92, 14, 81, 10], [93, 8, 82, 4, "colorFilters"], [93, 20, 82, 16], [94, 8, 83, 4, "shaders"], [94, 15, 83, 11], [95, 8, 84, 4, "imageFilters"], [95, 20, 84, 16], [96, 8, 85, 4, "pathEffects"], [96, 19, 85, 15], [97, 8, 86, 4, "paintDeclarations"], [97, 25, 86, 21], [98, 8, 87, 4, "paintPool"], [98, 17, 87, 13], [99, 8, 88, 4], [100, 8, 89, 4, "save<PERSON><PERSON>t"], [100, 17, 89, 13], [101, 8, 90, 4, "saveBackdropFilter"], [101, 26, 90, 22], [102, 8, 91, 4], [102, 12, 91, 8, "paint"], [102, 17, 91, 13, "paint"], [102, 18, 91, 13], [102, 20, 91, 16], [103, 10, 92, 6], [103, 17, 92, 13, "paints"], [103, 23, 92, 19], [103, 24, 92, 20, "paints"], [103, 30, 92, 26], [103, 31, 92, 27, "length"], [103, 37, 92, 33], [103, 40, 92, 36], [103, 41, 92, 37], [103, 42, 92, 38], [104, 8, 93, 4], [104, 9, 93, 5], [105, 8, 94, 4], [106, 8, 95, 4, "<PERSON><PERSON><PERSON><PERSON>"], [106, 20, 95, 16], [107, 8, 96, 4, "materialize<PERSON><PERSON><PERSON>"], [108, 6, 97, 2], [108, 7, 97, 3], [109, 4, 98, 0], [109, 5, 98, 1], [110, 4, 98, 1, "DrawingContextJs1"], [110, 21, 98, 1], [110, 22, 98, 1, "__closure"], [110, 31, 98, 1], [111, 4, 98, 1, "DrawingContextJs1"], [111, 21, 98, 1], [111, 22, 98, 1, "__workletHash"], [111, 35, 98, 1], [112, 4, 98, 1, "DrawingContextJs1"], [112, 21, 98, 1], [112, 22, 98, 1, "__initData"], [112, 32, 98, 1], [112, 35, 98, 1, "_worklet_10674523464531_init_data"], [112, 68, 98, 1], [113, 4, 98, 1, "DrawingContextJs1"], [113, 21, 98, 1], [113, 22, 98, 1, "__stackDetails"], [113, 36, 98, 1], [113, 39, 98, 1, "_e"], [113, 41, 98, 1], [114, 4, 98, 1], [114, 11, 98, 1, "DrawingContextJs1"], [114, 28, 98, 1], [115, 2, 98, 1], [115, 3, 1, 36], [115, 5, 98, 1], [116, 0, 98, 2], [116, 3]], "functionMap": {"names": ["<global>", "createDrawingContext", "save<PERSON><PERSON>t", "saveBackdropFilter", "getCurrent<PERSON><PERSON>t", "<PERSON><PERSON><PERSON><PERSON>", "materialize<PERSON><PERSON><PERSON>", "colorFilters.reduceRight$argument_0", "imageFilters.reduceRight$argument_0", "pathEffects.reduceRight$argument_0", "get__paint"], "mappings": "AAA,oCC;oBCiB;GDS;6BEC;GFa;0BGG;GHE;uBIC;GJE;2BKC;gECG,4ED;gEEQ,4EF;8DGK,2EH;GLQ;ISiB;KTE;CDK"}}, "type": "js/module"}]}