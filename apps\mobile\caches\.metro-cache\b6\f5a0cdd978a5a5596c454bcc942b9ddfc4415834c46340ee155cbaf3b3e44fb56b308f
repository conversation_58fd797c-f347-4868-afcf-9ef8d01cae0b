{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 39, "index": 39}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../../../dom/nodes", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 40}, "end": {"line": 2, "column": 57, "index": 97}}], "key": "Z+GW5Ist+DDyIe4BLHPS68wWHKY=", "exportNames": ["*"]}}, {"name": "../Group", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 98}, "end": {"line": 3, "column": 33, "index": 131}}], "key": "nkmtmvfA8QTKCcfZTirG7rPnLHc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.fitbox = exports.FitBox = void 0;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _nodes = require(_dependencyMap[1], \"../../../dom/nodes\");\n  var _Group = require(_dependencyMap[2], \"../Group\");\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const _worklet_10163295002487_init_data = {\n    code: \"function FitBoxJs1(fit,src,dst,rotation=0){const{fitRects,rect2rect}=this.__closure;const rects=fitRects(fit,rotation===90||rotation===270?{x:0,y:0,width:src.height,height:src.width}:src,dst);const result=rect2rect(rects.src,rects.dst);if(rotation===90){return[...result,{translate:[src.height,0]},{rotate:Math.PI/2}];}if(rotation===180){return[...result,{translate:[src.width,src.height]},{rotate:Math.PI}];}if(rotation===270){return[...result,{translate:[0,src.width]},{rotate:-Math.PI/2}];}return result;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\renderer\\\\components\\\\shapes\\\\FitBox.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"FitBoxJs1\\\",\\\"fit\\\",\\\"src\\\",\\\"dst\\\",\\\"rotation\\\",\\\"fitRects\\\",\\\"rect2rect\\\",\\\"__closure\\\",\\\"rects\\\",\\\"x\\\",\\\"y\\\",\\\"width\\\",\\\"height\\\",\\\"result\\\",\\\"translate\\\",\\\"rotate\\\",\\\"Math\\\",\\\"PI\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/renderer/components/shapes/FitBox.js\\\"],\\\"mappings\\\":\\\"AAGsB,QAAC,CAAAA,SAAKA,CAAGC,GAAE,CAAGC,GAAE,CAAAC,GAAA,CAAQC,QAAS,UAAAC,QAAA,CAAAC,SAAA,OAAAC,SAAA,CAGrD,KAAM,CAAAC,KAAK,CAAGH,QAAQ,CAACJ,GAAG,CAAEG,QAAQ,GAAK,EAAE,EAAIA,QAAQ,GAAK,GAAG,CAAG,CAChEK,CAAC,CAAE,CAAC,CACJC,CAAC,CAAE,CAAC,CACJC,KAAK,CAAET,GAAG,CAACU,MAAM,CACjBA,MAAM,CAAEV,GAAG,CAACS,KACd,CAAC,CAAGT,GAAG,CAAEC,GAAG,CAAC,CACb,KAAM,CAAAU,MAAM,CAAGP,SAAS,CAACE,KAAK,CAACN,GAAG,CAAEM,KAAK,CAACL,GAAG,CAAC,CAC9C,GAAIC,QAAQ,GAAK,EAAE,CAAE,CACnB,MAAO,CAAC,GAAGS,MAAM,CAAE,CACjBC,SAAS,CAAE,CAACZ,GAAG,CAACU,MAAM,CAAE,CAAC,CAC3B,CAAC,CAAE,CACDG,MAAM,CAAEC,IAAI,CAACC,EAAE,CAAG,CACpB,CAAC,CAAC,CACJ,CACA,GAAIb,QAAQ,GAAK,GAAG,CAAE,CACpB,MAAO,CAAC,GAAGS,MAAM,CAAE,CACjBC,SAAS,CAAE,CAACZ,GAAG,CAACS,KAAK,CAAET,GAAG,CAACU,MAAM,CACnC,CAAC,CAAE,CACDG,MAAM,CAAEC,IAAI,CAACC,EACf,CAAC,CAAC,CACJ,CACA,GAAIb,QAAQ,GAAK,GAAG,CAAE,CACpB,MAAO,CAAC,GAAGS,MAAM,CAAE,CACjBC,SAAS,CAAE,CAAC,CAAC,CAAEZ,GAAG,CAACS,KAAK,CAC1B,CAAC,CAAE,CACDI,MAAM,CAAE,CAACC,IAAI,CAACC,EAAE,CAAG,CACrB,CAAC,CAAC,CACJ,CACA,MAAO,CAAAJ,MAAM,CACf\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const fitbox = exports.fitbox = function () {\n    const _e = [new global.Error(), -3, -27];\n    const FitBoxJs1 = function (fit, src, dst, rotation = 0) {\n      const rects = (0, _nodes.fitRects)(fit, rotation === 90 || rotation === 270 ? {\n        x: 0,\n        y: 0,\n        width: src.height,\n        height: src.width\n      } : src, dst);\n      const result = (0, _nodes.rect2rect)(rects.src, rects.dst);\n      if (rotation === 90) {\n        return [...result, {\n          translate: [src.height, 0]\n        }, {\n          rotate: Math.PI / 2\n        }];\n      }\n      if (rotation === 180) {\n        return [...result, {\n          translate: [src.width, src.height]\n        }, {\n          rotate: Math.PI\n        }];\n      }\n      if (rotation === 270) {\n        return [...result, {\n          translate: [0, src.width]\n        }, {\n          rotate: -Math.PI / 2\n        }];\n      }\n      return result;\n    };\n    FitBoxJs1.__closure = {\n      fitRects: _nodes.fitRects,\n      rect2rect: _nodes.rect2rect\n    };\n    FitBoxJs1.__workletHash = 10163295002487;\n    FitBoxJs1.__initData = _worklet_10163295002487_init_data;\n    FitBoxJs1.__stackDetails = _e;\n    return FitBoxJs1;\n  }();\n  const FitBox = ({\n    fit = \"contain\",\n    src,\n    dst,\n    children\n  }) => {\n    const transform = (0, _react.useMemo)(() => fitbox(fit, src, dst), [dst, fit, src]);\n    return /*#__PURE__*/_react.default.createElement(_Group.Group, {\n      transform: transform\n    }, children);\n  };\n  exports.FitBox = FitBox;\n});", "lineCount": 70, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "_interopRequireWildcard"], [6, 38, 1, 0], [6, 39, 1, 0, "require"], [6, 46, 1, 0], [6, 47, 1, 0, "_dependencyMap"], [6, 61, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_nodes"], [7, 12, 2, 0], [7, 15, 2, 0, "require"], [7, 22, 2, 0], [7, 23, 2, 0, "_dependencyMap"], [7, 37, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_Group"], [8, 12, 3, 0], [8, 15, 3, 0, "require"], [8, 22, 3, 0], [8, 23, 3, 0, "_dependencyMap"], [8, 37, 3, 0], [9, 2, 3, 33], [9, 11, 3, 33, "_interopRequireWildcard"], [9, 35, 3, 33, "e"], [9, 36, 3, 33], [9, 38, 3, 33, "t"], [9, 39, 3, 33], [9, 68, 3, 33, "WeakMap"], [9, 75, 3, 33], [9, 81, 3, 33, "r"], [9, 82, 3, 33], [9, 89, 3, 33, "WeakMap"], [9, 96, 3, 33], [9, 100, 3, 33, "n"], [9, 101, 3, 33], [9, 108, 3, 33, "WeakMap"], [9, 115, 3, 33], [9, 127, 3, 33, "_interopRequireWildcard"], [9, 150, 3, 33], [9, 162, 3, 33, "_interopRequireWildcard"], [9, 163, 3, 33, "e"], [9, 164, 3, 33], [9, 166, 3, 33, "t"], [9, 167, 3, 33], [9, 176, 3, 33, "t"], [9, 177, 3, 33], [9, 181, 3, 33, "e"], [9, 182, 3, 33], [9, 186, 3, 33, "e"], [9, 187, 3, 33], [9, 188, 3, 33, "__esModule"], [9, 198, 3, 33], [9, 207, 3, 33, "e"], [9, 208, 3, 33], [9, 214, 3, 33, "o"], [9, 215, 3, 33], [9, 217, 3, 33, "i"], [9, 218, 3, 33], [9, 220, 3, 33, "f"], [9, 221, 3, 33], [9, 226, 3, 33, "__proto__"], [9, 235, 3, 33], [9, 243, 3, 33, "default"], [9, 250, 3, 33], [9, 252, 3, 33, "e"], [9, 253, 3, 33], [9, 270, 3, 33, "e"], [9, 271, 3, 33], [9, 294, 3, 33, "e"], [9, 295, 3, 33], [9, 320, 3, 33, "e"], [9, 321, 3, 33], [9, 330, 3, 33, "f"], [9, 331, 3, 33], [9, 337, 3, 33, "o"], [9, 338, 3, 33], [9, 341, 3, 33, "t"], [9, 342, 3, 33], [9, 345, 3, 33, "n"], [9, 346, 3, 33], [9, 349, 3, 33, "r"], [9, 350, 3, 33], [9, 358, 3, 33, "o"], [9, 359, 3, 33], [9, 360, 3, 33, "has"], [9, 363, 3, 33], [9, 364, 3, 33, "e"], [9, 365, 3, 33], [9, 375, 3, 33, "o"], [9, 376, 3, 33], [9, 377, 3, 33, "get"], [9, 380, 3, 33], [9, 381, 3, 33, "e"], [9, 382, 3, 33], [9, 385, 3, 33, "o"], [9, 386, 3, 33], [9, 387, 3, 33, "set"], [9, 390, 3, 33], [9, 391, 3, 33, "e"], [9, 392, 3, 33], [9, 394, 3, 33, "f"], [9, 395, 3, 33], [9, 411, 3, 33, "t"], [9, 412, 3, 33], [9, 416, 3, 33, "e"], [9, 417, 3, 33], [9, 433, 3, 33, "t"], [9, 434, 3, 33], [9, 441, 3, 33, "hasOwnProperty"], [9, 455, 3, 33], [9, 456, 3, 33, "call"], [9, 460, 3, 33], [9, 461, 3, 33, "e"], [9, 462, 3, 33], [9, 464, 3, 33, "t"], [9, 465, 3, 33], [9, 472, 3, 33, "i"], [9, 473, 3, 33], [9, 477, 3, 33, "o"], [9, 478, 3, 33], [9, 481, 3, 33, "Object"], [9, 487, 3, 33], [9, 488, 3, 33, "defineProperty"], [9, 502, 3, 33], [9, 507, 3, 33, "Object"], [9, 513, 3, 33], [9, 514, 3, 33, "getOwnPropertyDescriptor"], [9, 538, 3, 33], [9, 539, 3, 33, "e"], [9, 540, 3, 33], [9, 542, 3, 33, "t"], [9, 543, 3, 33], [9, 550, 3, 33, "i"], [9, 551, 3, 33], [9, 552, 3, 33, "get"], [9, 555, 3, 33], [9, 559, 3, 33, "i"], [9, 560, 3, 33], [9, 561, 3, 33, "set"], [9, 564, 3, 33], [9, 568, 3, 33, "o"], [9, 569, 3, 33], [9, 570, 3, 33, "f"], [9, 571, 3, 33], [9, 573, 3, 33, "t"], [9, 574, 3, 33], [9, 576, 3, 33, "i"], [9, 577, 3, 33], [9, 581, 3, 33, "f"], [9, 582, 3, 33], [9, 583, 3, 33, "t"], [9, 584, 3, 33], [9, 588, 3, 33, "e"], [9, 589, 3, 33], [9, 590, 3, 33, "t"], [9, 591, 3, 33], [9, 602, 3, 33, "f"], [9, 603, 3, 33], [9, 608, 3, 33, "e"], [9, 609, 3, 33], [9, 611, 3, 33, "t"], [9, 612, 3, 33], [10, 2, 3, 33], [10, 8, 3, 33, "_worklet_10163295002487_init_data"], [10, 41, 3, 33], [11, 4, 3, 33, "code"], [11, 8, 3, 33], [12, 4, 3, 33, "location"], [12, 12, 3, 33], [13, 4, 3, 33, "sourceMap"], [13, 13, 3, 33], [14, 4, 3, 33, "version"], [14, 11, 3, 33], [15, 2, 3, 33], [16, 2, 4, 7], [16, 8, 4, 13, "fitbox"], [16, 14, 4, 19], [16, 17, 4, 19, "exports"], [16, 24, 4, 19], [16, 25, 4, 19, "fitbox"], [16, 31, 4, 19], [16, 34, 4, 22], [17, 4, 4, 22], [17, 10, 4, 22, "_e"], [17, 12, 4, 22], [17, 20, 4, 22, "global"], [17, 26, 4, 22], [17, 27, 4, 22, "Error"], [17, 32, 4, 22], [18, 4, 4, 22], [18, 10, 4, 22, "FitBoxJs1"], [18, 19, 4, 22], [18, 31, 4, 22, "FitBoxJs1"], [18, 32, 4, 23, "fit"], [18, 35, 4, 26], [18, 37, 4, 28, "src"], [18, 40, 4, 31], [18, 42, 4, 33, "dst"], [18, 45, 4, 36], [18, 47, 4, 38, "rotation"], [18, 55, 4, 46], [18, 58, 4, 49], [18, 59, 4, 50], [18, 61, 4, 55], [19, 6, 7, 2], [19, 12, 7, 8, "rects"], [19, 17, 7, 13], [19, 20, 7, 16], [19, 24, 7, 16, "fitRects"], [19, 39, 7, 24], [19, 41, 7, 25, "fit"], [19, 44, 7, 28], [19, 46, 7, 30, "rotation"], [19, 54, 7, 38], [19, 59, 7, 43], [19, 61, 7, 45], [19, 65, 7, 49, "rotation"], [19, 73, 7, 57], [19, 78, 7, 62], [19, 81, 7, 65], [19, 84, 7, 68], [20, 8, 8, 4, "x"], [20, 9, 8, 5], [20, 11, 8, 7], [20, 12, 8, 8], [21, 8, 9, 4, "y"], [21, 9, 9, 5], [21, 11, 9, 7], [21, 12, 9, 8], [22, 8, 10, 4, "width"], [22, 13, 10, 9], [22, 15, 10, 11, "src"], [22, 18, 10, 14], [22, 19, 10, 15, "height"], [22, 25, 10, 21], [23, 8, 11, 4, "height"], [23, 14, 11, 10], [23, 16, 11, 12, "src"], [23, 19, 11, 15], [23, 20, 11, 16, "width"], [24, 6, 12, 2], [24, 7, 12, 3], [24, 10, 12, 6, "src"], [24, 13, 12, 9], [24, 15, 12, 11, "dst"], [24, 18, 12, 14], [24, 19, 12, 15], [25, 6, 13, 2], [25, 12, 13, 8, "result"], [25, 18, 13, 14], [25, 21, 13, 17], [25, 25, 13, 17, "rect2rect"], [25, 41, 13, 26], [25, 43, 13, 27, "rects"], [25, 48, 13, 32], [25, 49, 13, 33, "src"], [25, 52, 13, 36], [25, 54, 13, 38, "rects"], [25, 59, 13, 43], [25, 60, 13, 44, "dst"], [25, 63, 13, 47], [25, 64, 13, 48], [26, 6, 14, 2], [26, 10, 14, 6, "rotation"], [26, 18, 14, 14], [26, 23, 14, 19], [26, 25, 14, 21], [26, 27, 14, 23], [27, 8, 15, 4], [27, 15, 15, 11], [27, 16, 15, 12], [27, 19, 15, 15, "result"], [27, 25, 15, 21], [27, 27, 15, 23], [28, 10, 16, 6, "translate"], [28, 19, 16, 15], [28, 21, 16, 17], [28, 22, 16, 18, "src"], [28, 25, 16, 21], [28, 26, 16, 22, "height"], [28, 32, 16, 28], [28, 34, 16, 30], [28, 35, 16, 31], [29, 8, 17, 4], [29, 9, 17, 5], [29, 11, 17, 7], [30, 10, 18, 6, "rotate"], [30, 16, 18, 12], [30, 18, 18, 14, "Math"], [30, 22, 18, 18], [30, 23, 18, 19, "PI"], [30, 25, 18, 21], [30, 28, 18, 24], [31, 8, 19, 4], [31, 9, 19, 5], [31, 10, 19, 6], [32, 6, 20, 2], [33, 6, 21, 2], [33, 10, 21, 6, "rotation"], [33, 18, 21, 14], [33, 23, 21, 19], [33, 26, 21, 22], [33, 28, 21, 24], [34, 8, 22, 4], [34, 15, 22, 11], [34, 16, 22, 12], [34, 19, 22, 15, "result"], [34, 25, 22, 21], [34, 27, 22, 23], [35, 10, 23, 6, "translate"], [35, 19, 23, 15], [35, 21, 23, 17], [35, 22, 23, 18, "src"], [35, 25, 23, 21], [35, 26, 23, 22, "width"], [35, 31, 23, 27], [35, 33, 23, 29, "src"], [35, 36, 23, 32], [35, 37, 23, 33, "height"], [35, 43, 23, 39], [36, 8, 24, 4], [36, 9, 24, 5], [36, 11, 24, 7], [37, 10, 25, 6, "rotate"], [37, 16, 25, 12], [37, 18, 25, 14, "Math"], [37, 22, 25, 18], [37, 23, 25, 19, "PI"], [38, 8, 26, 4], [38, 9, 26, 5], [38, 10, 26, 6], [39, 6, 27, 2], [40, 6, 28, 2], [40, 10, 28, 6, "rotation"], [40, 18, 28, 14], [40, 23, 28, 19], [40, 26, 28, 22], [40, 28, 28, 24], [41, 8, 29, 4], [41, 15, 29, 11], [41, 16, 29, 12], [41, 19, 29, 15, "result"], [41, 25, 29, 21], [41, 27, 29, 23], [42, 10, 30, 6, "translate"], [42, 19, 30, 15], [42, 21, 30, 17], [42, 22, 30, 18], [42, 23, 30, 19], [42, 25, 30, 21, "src"], [42, 28, 30, 24], [42, 29, 30, 25, "width"], [42, 34, 30, 30], [43, 8, 31, 4], [43, 9, 31, 5], [43, 11, 31, 7], [44, 10, 32, 6, "rotate"], [44, 16, 32, 12], [44, 18, 32, 14], [44, 19, 32, 15, "Math"], [44, 23, 32, 19], [44, 24, 32, 20, "PI"], [44, 26, 32, 22], [44, 29, 32, 25], [45, 8, 33, 4], [45, 9, 33, 5], [45, 10, 33, 6], [46, 6, 34, 2], [47, 6, 35, 2], [47, 13, 35, 9, "result"], [47, 19, 35, 15], [48, 4, 36, 0], [48, 5, 36, 1], [49, 4, 36, 1, "FitBoxJs1"], [49, 13, 36, 1], [49, 14, 36, 1, "__closure"], [49, 23, 36, 1], [50, 6, 36, 1, "fitRects"], [50, 14, 36, 1], [50, 16, 7, 16, "fitRects"], [50, 31, 7, 24], [51, 6, 7, 24, "rect2rect"], [51, 15, 7, 24], [51, 17, 13, 17, "rect2rect"], [52, 4, 13, 26], [53, 4, 13, 26, "FitBoxJs1"], [53, 13, 13, 26], [53, 14, 13, 26, "__workletHash"], [53, 27, 13, 26], [54, 4, 13, 26, "FitBoxJs1"], [54, 13, 13, 26], [54, 14, 13, 26, "__initData"], [54, 24, 13, 26], [54, 27, 13, 26, "_worklet_10163295002487_init_data"], [54, 60, 13, 26], [55, 4, 13, 26, "FitBoxJs1"], [55, 13, 13, 26], [55, 14, 13, 26, "__stackDetails"], [55, 28, 13, 26], [55, 31, 13, 26, "_e"], [55, 33, 13, 26], [56, 4, 13, 26], [56, 11, 13, 26, "FitBoxJs1"], [56, 20, 13, 26], [57, 2, 13, 26], [57, 3, 4, 22], [57, 5, 36, 1], [58, 2, 37, 7], [58, 8, 37, 13, "FitBox"], [58, 14, 37, 19], [58, 17, 37, 22, "FitBox"], [58, 18, 37, 23], [59, 4, 38, 2, "fit"], [59, 7, 38, 5], [59, 10, 38, 8], [59, 19, 38, 17], [60, 4, 39, 2, "src"], [60, 7, 39, 5], [61, 4, 40, 2, "dst"], [61, 7, 40, 5], [62, 4, 41, 2, "children"], [63, 2, 42, 0], [63, 3, 42, 1], [63, 8, 42, 6], [64, 4, 43, 2], [64, 10, 43, 8, "transform"], [64, 19, 43, 17], [64, 22, 43, 20], [64, 26, 43, 20, "useMemo"], [64, 40, 43, 27], [64, 42, 43, 28], [64, 48, 43, 34, "fitbox"], [64, 54, 43, 40], [64, 55, 43, 41, "fit"], [64, 58, 43, 44], [64, 60, 43, 46, "src"], [64, 63, 43, 49], [64, 65, 43, 51, "dst"], [64, 68, 43, 54], [64, 69, 43, 55], [64, 71, 43, 57], [64, 72, 43, 58, "dst"], [64, 75, 43, 61], [64, 77, 43, 63, "fit"], [64, 80, 43, 66], [64, 82, 43, 68, "src"], [64, 85, 43, 71], [64, 86, 43, 72], [64, 87, 43, 73], [65, 4, 44, 2], [65, 11, 44, 9], [65, 24, 44, 22, "React"], [65, 38, 44, 27], [65, 39, 44, 28, "createElement"], [65, 52, 44, 41], [65, 53, 44, 42, "Group"], [65, 65, 44, 47], [65, 67, 44, 49], [66, 6, 45, 4, "transform"], [66, 15, 45, 13], [66, 17, 45, 15, "transform"], [67, 4, 46, 2], [67, 5, 46, 3], [67, 7, 46, 5, "children"], [67, 15, 46, 13], [67, 16, 46, 14], [68, 2, 47, 0], [68, 3, 47, 1], [69, 2, 47, 2, "exports"], [69, 9, 47, 2], [69, 10, 47, 2, "FitBox"], [69, 16, 47, 2], [69, 19, 47, 2, "FitBox"], [69, 25, 47, 2], [70, 0, 47, 2], [70, 3]], "functionMap": {"names": ["<global>", "fitbox", "FitBox", "useMemo$argument_0"], "mappings": "AAA;sBCG;CDgC;sBEC;4BCM,2BD;CFI"}}, "type": "js/module"}]}