{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 30, "index": 30}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkSurface", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 31}, "end": {"line": 2, "column": 46, "index": 77}}], "key": "vQK10PRpfhFFB5jig/fepV76/LY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkSurfaceFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkSurface = require(_dependencyMap[1], \"./JsiSkSurface\");\n  class JsiSkSurfaceFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    Make(width, height) {\n      return new _JsiSkSurface.JsiSkSurface(this.CanvasKit, this.CanvasKit.MakeSurface(width, height));\n    }\n    MakeOffscreen(width, height) {\n      // OffscreenCanvas may be unvailable in some environments.\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      const OC = globalThis.OffscreenCanvas;\n      let surface;\n      if (OC === undefined) {\n        return this.Make(width, height);\n      } else {\n        const offscreen = new OC(width, height);\n        const webglContext = this.CanvasKit.GetWebGLContext(offscreen);\n        const grContext = this.CanvasKit.MakeWebGLContext(webglContext);\n        if (!grContext) {\n          throw new Error(\"Could not make a graphics context\");\n        }\n        surface = this.CanvasKit.MakeRenderTarget(grContext, width, height);\n      }\n      if (!surface) {\n        return null;\n      }\n      return new _JsiSkSurface.JsiSkSurface(this.CanvasKit, surface);\n    }\n  }\n  exports.JsiSkSurfaceFactory = JsiSkSurfaceFactory;\n});", "lineCount": 38, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkSurface"], [7, 19, 2, 0], [7, 22, 2, 0, "require"], [7, 29, 2, 0], [7, 30, 2, 0, "_dependencyMap"], [7, 44, 2, 0], [8, 2, 3, 7], [8, 8, 3, 13, "JsiSkSurfaceFactory"], [8, 27, 3, 32], [8, 36, 3, 41, "Host"], [8, 46, 3, 45], [8, 47, 3, 46], [9, 4, 4, 2, "constructor"], [9, 15, 4, 13, "constructor"], [9, 16, 4, 14, "CanvasKit"], [9, 25, 4, 23], [9, 27, 4, 25], [10, 6, 5, 4], [10, 11, 5, 9], [10, 12, 5, 10, "CanvasKit"], [10, 21, 5, 19], [10, 22, 5, 20], [11, 4, 6, 2], [12, 4, 7, 2, "Make"], [12, 8, 7, 6, "Make"], [12, 9, 7, 7, "width"], [12, 14, 7, 12], [12, 16, 7, 14, "height"], [12, 22, 7, 20], [12, 24, 7, 22], [13, 6, 8, 4], [13, 13, 8, 11], [13, 17, 8, 15, "JsiSkSurface"], [13, 43, 8, 27], [13, 44, 8, 28], [13, 48, 8, 32], [13, 49, 8, 33, "CanvasKit"], [13, 58, 8, 42], [13, 60, 8, 44], [13, 64, 8, 48], [13, 65, 8, 49, "CanvasKit"], [13, 74, 8, 58], [13, 75, 8, 59, "MakeSurface"], [13, 86, 8, 70], [13, 87, 8, 71, "width"], [13, 92, 8, 76], [13, 94, 8, 78, "height"], [13, 100, 8, 84], [13, 101, 8, 85], [13, 102, 8, 86], [14, 4, 9, 2], [15, 4, 10, 2, "MakeOffscreen"], [15, 17, 10, 15, "MakeOffscreen"], [15, 18, 10, 16, "width"], [15, 23, 10, 21], [15, 25, 10, 23, "height"], [15, 31, 10, 29], [15, 33, 10, 31], [16, 6, 11, 4], [17, 6, 12, 4], [18, 6, 13, 4], [18, 12, 13, 10, "OC"], [18, 14, 13, 12], [18, 17, 13, 15, "globalThis"], [18, 27, 13, 25], [18, 28, 13, 26, "OffscreenCanvas"], [18, 43, 13, 41], [19, 6, 14, 4], [19, 10, 14, 8, "surface"], [19, 17, 14, 15], [20, 6, 15, 4], [20, 10, 15, 8, "OC"], [20, 12, 15, 10], [20, 17, 15, 15, "undefined"], [20, 26, 15, 24], [20, 28, 15, 26], [21, 8, 16, 6], [21, 15, 16, 13], [21, 19, 16, 17], [21, 20, 16, 18, "Make"], [21, 24, 16, 22], [21, 25, 16, 23, "width"], [21, 30, 16, 28], [21, 32, 16, 30, "height"], [21, 38, 16, 36], [21, 39, 16, 37], [22, 6, 17, 4], [22, 7, 17, 5], [22, 13, 17, 11], [23, 8, 18, 6], [23, 14, 18, 12, "offscreen"], [23, 23, 18, 21], [23, 26, 18, 24], [23, 30, 18, 28, "OC"], [23, 32, 18, 30], [23, 33, 18, 31, "width"], [23, 38, 18, 36], [23, 40, 18, 38, "height"], [23, 46, 18, 44], [23, 47, 18, 45], [24, 8, 19, 6], [24, 14, 19, 12, "webglContext"], [24, 26, 19, 24], [24, 29, 19, 27], [24, 33, 19, 31], [24, 34, 19, 32, "CanvasKit"], [24, 43, 19, 41], [24, 44, 19, 42, "GetWebGLContext"], [24, 59, 19, 57], [24, 60, 19, 58, "offscreen"], [24, 69, 19, 67], [24, 70, 19, 68], [25, 8, 20, 6], [25, 14, 20, 12, "grContext"], [25, 23, 20, 21], [25, 26, 20, 24], [25, 30, 20, 28], [25, 31, 20, 29, "CanvasKit"], [25, 40, 20, 38], [25, 41, 20, 39, "MakeWebGLContext"], [25, 57, 20, 55], [25, 58, 20, 56, "webglContext"], [25, 70, 20, 68], [25, 71, 20, 69], [26, 8, 21, 6], [26, 12, 21, 10], [26, 13, 21, 11, "grContext"], [26, 22, 21, 20], [26, 24, 21, 22], [27, 10, 22, 8], [27, 16, 22, 14], [27, 20, 22, 18, "Error"], [27, 25, 22, 23], [27, 26, 22, 24], [27, 61, 22, 59], [27, 62, 22, 60], [28, 8, 23, 6], [29, 8, 24, 6, "surface"], [29, 15, 24, 13], [29, 18, 24, 16], [29, 22, 24, 20], [29, 23, 24, 21, "CanvasKit"], [29, 32, 24, 30], [29, 33, 24, 31, "MakeRenderTarget"], [29, 49, 24, 47], [29, 50, 24, 48, "grContext"], [29, 59, 24, 57], [29, 61, 24, 59, "width"], [29, 66, 24, 64], [29, 68, 24, 66, "height"], [29, 74, 24, 72], [29, 75, 24, 73], [30, 6, 25, 4], [31, 6, 26, 4], [31, 10, 26, 8], [31, 11, 26, 9, "surface"], [31, 18, 26, 16], [31, 20, 26, 18], [32, 8, 27, 6], [32, 15, 27, 13], [32, 19, 27, 17], [33, 6, 28, 4], [34, 6, 29, 4], [34, 13, 29, 11], [34, 17, 29, 15, "JsiSkSurface"], [34, 43, 29, 27], [34, 44, 29, 28], [34, 48, 29, 32], [34, 49, 29, 33, "CanvasKit"], [34, 58, 29, 42], [34, 60, 29, 44, "surface"], [34, 67, 29, 51], [34, 68, 29, 52], [35, 4, 30, 2], [36, 2, 31, 0], [37, 2, 31, 1, "exports"], [37, 9, 31, 1], [37, 10, 31, 1, "JsiSkSurfaceFactory"], [37, 29, 31, 1], [37, 32, 31, 1, "JsiSkSurfaceFactory"], [37, 51, 31, 1], [38, 0, 31, 1], [38, 3]], "functionMap": {"names": ["<global>", "JsiSkSurfaceFactory", "constructor", "Make", "MakeOffscreen"], "mappings": "AAA;OCE;ECC;GDE;EEC;GFE;EGC;GHoB;CDC"}}, "type": "js/module"}]}