{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 40, "index": 642}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkRect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 643}, "end": {"line": 5, "column": 40, "index": 683}}], "key": "VBkFjQz9GOtB0AbNPoXYbn3D5z0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkRRect = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkRect = require(_dependencyMap[1], \"./JsiSkRect\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class JsiSkRRect extends _Host.BaseHostObject {\n    static fromValue(CanvasKit, rect) {\n      if (rect instanceof _JsiSkRect.JsiSkRect) {\n        return rect.ref;\n      }\n      if (\"topLeft\" in rect && \"topRight\" in rect && \"bottomRight\" in rect && \"bottomLeft\" in rect) {\n        return Float32Array.of(rect.rect.x, rect.rect.y, rect.rect.x + rect.rect.width, rect.rect.y + rect.rect.height, rect.topLeft.x, rect.topLeft.y, rect.topRight.x, rect.topRight.y, rect.bottomRight.x, rect.bottomRight.y, rect.bottomLeft.x, rect.bottomLeft.y);\n      }\n      return CanvasKit.RRectXY(_JsiSkRect.JsiSkRect.fromValue(CanvasKit, rect.rect), rect.rx, rect.ry);\n    }\n    constructor(CanvasKit, rect, rx, ry) {\n      // based on https://github.com/google/skia/blob/main/src/core/SkRRect.cpp#L51\n      if (rx === Infinity || ry === Infinity) {\n        rx = ry = 0;\n      }\n      if (rect.width < rx + rx || rect.height < ry + ry) {\n        // At most one of these two divides will be by zero, and neither numerator is zero.\n        const scale = Math.min(rect.width / (rx + rx), rect.height / (ry + ry));\n        rx *= scale;\n        ry *= scale;\n      }\n      const ref = CanvasKit.RRectXY(_JsiSkRect.JsiSkRect.fromValue(CanvasKit, rect), rx, ry);\n      super(CanvasKit, ref, \"RRect\");\n      _defineProperty(this, \"dispose\", () => {\n        // Float32Array\n      });\n    }\n    get rx() {\n      return this.ref[4];\n    }\n    get ry() {\n      return this.ref[5];\n    }\n    get rect() {\n      return new _JsiSkRect.JsiSkRect(this.CanvasKit, Float32Array.of(this.ref[0], this.ref[1], this.ref[2], this.ref[3]));\n    }\n  }\n  exports.JsiSkRRect = JsiSkRRect;\n});", "lineCount": 68, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_Host"], [6, 11, 4, 0], [6, 14, 4, 0, "require"], [6, 21, 4, 0], [6, 22, 4, 0, "_dependencyMap"], [6, 36, 4, 0], [7, 2, 5, 0], [7, 6, 5, 0, "_JsiSkRect"], [7, 16, 5, 0], [7, 19, 5, 0, "require"], [7, 26, 5, 0], [7, 27, 5, 0, "_dependencyMap"], [7, 41, 5, 0], [8, 2, 1, 0], [8, 11, 1, 9, "_defineProperty"], [8, 26, 1, 24, "_defineProperty"], [8, 27, 1, 25, "e"], [8, 28, 1, 26], [8, 30, 1, 28, "r"], [8, 31, 1, 29], [8, 33, 1, 31, "t"], [8, 34, 1, 32], [8, 36, 1, 34], [9, 4, 1, 36], [9, 11, 1, 43], [9, 12, 1, 44, "r"], [9, 13, 1, 45], [9, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [9, 30, 1, 62], [9, 31, 1, 63, "r"], [9, 32, 1, 64], [9, 33, 1, 65], [9, 38, 1, 70, "e"], [9, 39, 1, 71], [9, 42, 1, 74, "Object"], [9, 48, 1, 80], [9, 49, 1, 81, "defineProperty"], [9, 63, 1, 95], [9, 64, 1, 96, "e"], [9, 65, 1, 97], [9, 67, 1, 99, "r"], [9, 68, 1, 100], [9, 70, 1, 102], [10, 6, 1, 104, "value"], [10, 11, 1, 109], [10, 13, 1, 111, "t"], [10, 14, 1, 112], [11, 6, 1, 114, "enumerable"], [11, 16, 1, 124], [11, 18, 1, 126], [11, 19, 1, 127], [11, 20, 1, 128], [12, 6, 1, 130, "configurable"], [12, 18, 1, 142], [12, 20, 1, 144], [12, 21, 1, 145], [12, 22, 1, 146], [13, 6, 1, 148, "writable"], [13, 14, 1, 156], [13, 16, 1, 158], [13, 17, 1, 159], [14, 4, 1, 161], [14, 5, 1, 162], [14, 6, 1, 163], [14, 9, 1, 166, "e"], [14, 10, 1, 167], [14, 11, 1, 168, "r"], [14, 12, 1, 169], [14, 13, 1, 170], [14, 16, 1, 173, "t"], [14, 17, 1, 174], [14, 19, 1, 176, "e"], [14, 20, 1, 177], [15, 2, 1, 179], [16, 2, 2, 0], [16, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [16, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [16, 26, 2, 24, "t"], [16, 27, 2, 25], [16, 29, 2, 27], [17, 4, 2, 29], [17, 8, 2, 33, "i"], [17, 9, 2, 34], [17, 12, 2, 37, "_toPrimitive"], [17, 24, 2, 49], [17, 25, 2, 50, "t"], [17, 26, 2, 51], [17, 28, 2, 53], [17, 36, 2, 61], [17, 37, 2, 62], [18, 4, 2, 64], [18, 11, 2, 71], [18, 19, 2, 79], [18, 23, 2, 83], [18, 30, 2, 90, "i"], [18, 31, 2, 91], [18, 34, 2, 94, "i"], [18, 35, 2, 95], [18, 38, 2, 98, "i"], [18, 39, 2, 99], [18, 42, 2, 102], [18, 44, 2, 104], [19, 2, 2, 106], [20, 2, 3, 0], [20, 11, 3, 9, "_toPrimitive"], [20, 23, 3, 21, "_toPrimitive"], [20, 24, 3, 22, "t"], [20, 25, 3, 23], [20, 27, 3, 25, "r"], [20, 28, 3, 26], [20, 30, 3, 28], [21, 4, 3, 30], [21, 8, 3, 34], [21, 16, 3, 42], [21, 20, 3, 46], [21, 27, 3, 53, "t"], [21, 28, 3, 54], [21, 32, 3, 58], [21, 33, 3, 59, "t"], [21, 34, 3, 60], [21, 36, 3, 62], [21, 43, 3, 69, "t"], [21, 44, 3, 70], [22, 4, 3, 72], [22, 8, 3, 76, "e"], [22, 9, 3, 77], [22, 12, 3, 80, "t"], [22, 13, 3, 81], [22, 14, 3, 82, "Symbol"], [22, 20, 3, 88], [22, 21, 3, 89, "toPrimitive"], [22, 32, 3, 100], [22, 33, 3, 101], [23, 4, 3, 103], [23, 8, 3, 107], [23, 13, 3, 112], [23, 14, 3, 113], [23, 19, 3, 118, "e"], [23, 20, 3, 119], [23, 22, 3, 121], [24, 6, 3, 123], [24, 10, 3, 127, "i"], [24, 11, 3, 128], [24, 14, 3, 131, "e"], [24, 15, 3, 132], [24, 16, 3, 133, "call"], [24, 20, 3, 137], [24, 21, 3, 138, "t"], [24, 22, 3, 139], [24, 24, 3, 141, "r"], [24, 25, 3, 142], [24, 29, 3, 146], [24, 38, 3, 155], [24, 39, 3, 156], [25, 6, 3, 158], [25, 10, 3, 162], [25, 18, 3, 170], [25, 22, 3, 174], [25, 29, 3, 181, "i"], [25, 30, 3, 182], [25, 32, 3, 184], [25, 39, 3, 191, "i"], [25, 40, 3, 192], [26, 6, 3, 194], [26, 12, 3, 200], [26, 16, 3, 204, "TypeError"], [26, 25, 3, 213], [26, 26, 3, 214], [26, 72, 3, 260], [26, 73, 3, 261], [27, 4, 3, 263], [28, 4, 3, 265], [28, 11, 3, 272], [28, 12, 3, 273], [28, 20, 3, 281], [28, 25, 3, 286, "r"], [28, 26, 3, 287], [28, 29, 3, 290, "String"], [28, 35, 3, 296], [28, 38, 3, 299, "Number"], [28, 44, 3, 305], [28, 46, 3, 307, "t"], [28, 47, 3, 308], [28, 48, 3, 309], [29, 2, 3, 311], [30, 2, 6, 7], [30, 8, 6, 13, "JsiSkRRect"], [30, 18, 6, 23], [30, 27, 6, 32, "BaseHostObject"], [30, 47, 6, 46], [30, 48, 6, 47], [31, 4, 7, 2], [31, 11, 7, 9, "fromValue"], [31, 20, 7, 18, "fromValue"], [31, 21, 7, 19, "CanvasKit"], [31, 30, 7, 28], [31, 32, 7, 30, "rect"], [31, 36, 7, 34], [31, 38, 7, 36], [32, 6, 8, 4], [32, 10, 8, 8, "rect"], [32, 14, 8, 12], [32, 26, 8, 24, "JsiSkRect"], [32, 46, 8, 33], [32, 48, 8, 35], [33, 8, 9, 6], [33, 15, 9, 13, "rect"], [33, 19, 9, 17], [33, 20, 9, 18, "ref"], [33, 23, 9, 21], [34, 6, 10, 4], [35, 6, 11, 4], [35, 10, 11, 8], [35, 19, 11, 17], [35, 23, 11, 21, "rect"], [35, 27, 11, 25], [35, 31, 11, 29], [35, 41, 11, 39], [35, 45, 11, 43, "rect"], [35, 49, 11, 47], [35, 53, 11, 51], [35, 66, 11, 64], [35, 70, 11, 68, "rect"], [35, 74, 11, 72], [35, 78, 11, 76], [35, 90, 11, 88], [35, 94, 11, 92, "rect"], [35, 98, 11, 96], [35, 100, 11, 98], [36, 8, 12, 6], [36, 15, 12, 13, "Float32Array"], [36, 27, 12, 25], [36, 28, 12, 26, "of"], [36, 30, 12, 28], [36, 31, 12, 29, "rect"], [36, 35, 12, 33], [36, 36, 12, 34, "rect"], [36, 40, 12, 38], [36, 41, 12, 39, "x"], [36, 42, 12, 40], [36, 44, 12, 42, "rect"], [36, 48, 12, 46], [36, 49, 12, 47, "rect"], [36, 53, 12, 51], [36, 54, 12, 52, "y"], [36, 55, 12, 53], [36, 57, 12, 55, "rect"], [36, 61, 12, 59], [36, 62, 12, 60, "rect"], [36, 66, 12, 64], [36, 67, 12, 65, "x"], [36, 68, 12, 66], [36, 71, 12, 69, "rect"], [36, 75, 12, 73], [36, 76, 12, 74, "rect"], [36, 80, 12, 78], [36, 81, 12, 79, "width"], [36, 86, 12, 84], [36, 88, 12, 86, "rect"], [36, 92, 12, 90], [36, 93, 12, 91, "rect"], [36, 97, 12, 95], [36, 98, 12, 96, "y"], [36, 99, 12, 97], [36, 102, 12, 100, "rect"], [36, 106, 12, 104], [36, 107, 12, 105, "rect"], [36, 111, 12, 109], [36, 112, 12, 110, "height"], [36, 118, 12, 116], [36, 120, 12, 118, "rect"], [36, 124, 12, 122], [36, 125, 12, 123, "topLeft"], [36, 132, 12, 130], [36, 133, 12, 131, "x"], [36, 134, 12, 132], [36, 136, 12, 134, "rect"], [36, 140, 12, 138], [36, 141, 12, 139, "topLeft"], [36, 148, 12, 146], [36, 149, 12, 147, "y"], [36, 150, 12, 148], [36, 152, 12, 150, "rect"], [36, 156, 12, 154], [36, 157, 12, 155, "topRight"], [36, 165, 12, 163], [36, 166, 12, 164, "x"], [36, 167, 12, 165], [36, 169, 12, 167, "rect"], [36, 173, 12, 171], [36, 174, 12, 172, "topRight"], [36, 182, 12, 180], [36, 183, 12, 181, "y"], [36, 184, 12, 182], [36, 186, 12, 184, "rect"], [36, 190, 12, 188], [36, 191, 12, 189, "bottomRight"], [36, 202, 12, 200], [36, 203, 12, 201, "x"], [36, 204, 12, 202], [36, 206, 12, 204, "rect"], [36, 210, 12, 208], [36, 211, 12, 209, "bottomRight"], [36, 222, 12, 220], [36, 223, 12, 221, "y"], [36, 224, 12, 222], [36, 226, 12, 224, "rect"], [36, 230, 12, 228], [36, 231, 12, 229, "bottomLeft"], [36, 241, 12, 239], [36, 242, 12, 240, "x"], [36, 243, 12, 241], [36, 245, 12, 243, "rect"], [36, 249, 12, 247], [36, 250, 12, 248, "bottomLeft"], [36, 260, 12, 258], [36, 261, 12, 259, "y"], [36, 262, 12, 260], [36, 263, 12, 261], [37, 6, 13, 4], [38, 6, 14, 4], [38, 13, 14, 11, "CanvasKit"], [38, 22, 14, 20], [38, 23, 14, 21, "RRectXY"], [38, 30, 14, 28], [38, 31, 14, 29, "JsiSkRect"], [38, 51, 14, 38], [38, 52, 14, 39, "fromValue"], [38, 61, 14, 48], [38, 62, 14, 49, "CanvasKit"], [38, 71, 14, 58], [38, 73, 14, 60, "rect"], [38, 77, 14, 64], [38, 78, 14, 65, "rect"], [38, 82, 14, 69], [38, 83, 14, 70], [38, 85, 14, 72, "rect"], [38, 89, 14, 76], [38, 90, 14, 77, "rx"], [38, 92, 14, 79], [38, 94, 14, 81, "rect"], [38, 98, 14, 85], [38, 99, 14, 86, "ry"], [38, 101, 14, 88], [38, 102, 14, 89], [39, 4, 15, 2], [40, 4, 16, 2, "constructor"], [40, 15, 16, 13, "constructor"], [40, 16, 16, 14, "CanvasKit"], [40, 25, 16, 23], [40, 27, 16, 25, "rect"], [40, 31, 16, 29], [40, 33, 16, 31, "rx"], [40, 35, 16, 33], [40, 37, 16, 35, "ry"], [40, 39, 16, 37], [40, 41, 16, 39], [41, 6, 17, 4], [42, 6, 18, 4], [42, 10, 18, 8, "rx"], [42, 12, 18, 10], [42, 17, 18, 15, "Infinity"], [42, 25, 18, 23], [42, 29, 18, 27, "ry"], [42, 31, 18, 29], [42, 36, 18, 34, "Infinity"], [42, 44, 18, 42], [42, 46, 18, 44], [43, 8, 19, 6, "rx"], [43, 10, 19, 8], [43, 13, 19, 11, "ry"], [43, 15, 19, 13], [43, 18, 19, 16], [43, 19, 19, 17], [44, 6, 20, 4], [45, 6, 21, 4], [45, 10, 21, 8, "rect"], [45, 14, 21, 12], [45, 15, 21, 13, "width"], [45, 20, 21, 18], [45, 23, 21, 21, "rx"], [45, 25, 21, 23], [45, 28, 21, 26, "rx"], [45, 30, 21, 28], [45, 34, 21, 32, "rect"], [45, 38, 21, 36], [45, 39, 21, 37, "height"], [45, 45, 21, 43], [45, 48, 21, 46, "ry"], [45, 50, 21, 48], [45, 53, 21, 51, "ry"], [45, 55, 21, 53], [45, 57, 21, 55], [46, 8, 22, 6], [47, 8, 23, 6], [47, 14, 23, 12, "scale"], [47, 19, 23, 17], [47, 22, 23, 20, "Math"], [47, 26, 23, 24], [47, 27, 23, 25, "min"], [47, 30, 23, 28], [47, 31, 23, 29, "rect"], [47, 35, 23, 33], [47, 36, 23, 34, "width"], [47, 41, 23, 39], [47, 45, 23, 43, "rx"], [47, 47, 23, 45], [47, 50, 23, 48, "rx"], [47, 52, 23, 50], [47, 53, 23, 51], [47, 55, 23, 53, "rect"], [47, 59, 23, 57], [47, 60, 23, 58, "height"], [47, 66, 23, 64], [47, 70, 23, 68, "ry"], [47, 72, 23, 70], [47, 75, 23, 73, "ry"], [47, 77, 23, 75], [47, 78, 23, 76], [47, 79, 23, 77], [48, 8, 24, 6, "rx"], [48, 10, 24, 8], [48, 14, 24, 12, "scale"], [48, 19, 24, 17], [49, 8, 25, 6, "ry"], [49, 10, 25, 8], [49, 14, 25, 12, "scale"], [49, 19, 25, 17], [50, 6, 26, 4], [51, 6, 27, 4], [51, 12, 27, 10, "ref"], [51, 15, 27, 13], [51, 18, 27, 16, "CanvasKit"], [51, 27, 27, 25], [51, 28, 27, 26, "RRectXY"], [51, 35, 27, 33], [51, 36, 27, 34, "JsiSkRect"], [51, 56, 27, 43], [51, 57, 27, 44, "fromValue"], [51, 66, 27, 53], [51, 67, 27, 54, "CanvasKit"], [51, 76, 27, 63], [51, 78, 27, 65, "rect"], [51, 82, 27, 69], [51, 83, 27, 70], [51, 85, 27, 72, "rx"], [51, 87, 27, 74], [51, 89, 27, 76, "ry"], [51, 91, 27, 78], [51, 92, 27, 79], [52, 6, 28, 4], [52, 11, 28, 9], [52, 12, 28, 10, "CanvasKit"], [52, 21, 28, 19], [52, 23, 28, 21, "ref"], [52, 26, 28, 24], [52, 28, 28, 26], [52, 35, 28, 33], [52, 36, 28, 34], [53, 6, 29, 4, "_defineProperty"], [53, 21, 29, 19], [53, 22, 29, 20], [53, 26, 29, 24], [53, 28, 29, 26], [53, 37, 29, 35], [53, 39, 29, 37], [53, 45, 29, 43], [54, 8, 30, 6], [55, 6, 30, 6], [55, 7, 31, 5], [55, 8, 31, 6], [56, 4, 32, 2], [57, 4, 33, 2], [57, 8, 33, 6, "rx"], [57, 10, 33, 8, "rx"], [57, 11, 33, 8], [57, 13, 33, 11], [58, 6, 34, 4], [58, 13, 34, 11], [58, 17, 34, 15], [58, 18, 34, 16, "ref"], [58, 21, 34, 19], [58, 22, 34, 20], [58, 23, 34, 21], [58, 24, 34, 22], [59, 4, 35, 2], [60, 4, 36, 2], [60, 8, 36, 6, "ry"], [60, 10, 36, 8, "ry"], [60, 11, 36, 8], [60, 13, 36, 11], [61, 6, 37, 4], [61, 13, 37, 11], [61, 17, 37, 15], [61, 18, 37, 16, "ref"], [61, 21, 37, 19], [61, 22, 37, 20], [61, 23, 37, 21], [61, 24, 37, 22], [62, 4, 38, 2], [63, 4, 39, 2], [63, 8, 39, 6, "rect"], [63, 12, 39, 10, "rect"], [63, 13, 39, 10], [63, 15, 39, 13], [64, 6, 40, 4], [64, 13, 40, 11], [64, 17, 40, 15, "JsiSkRect"], [64, 37, 40, 24], [64, 38, 40, 25], [64, 42, 40, 29], [64, 43, 40, 30, "CanvasKit"], [64, 52, 40, 39], [64, 54, 40, 41, "Float32Array"], [64, 66, 40, 53], [64, 67, 40, 54, "of"], [64, 69, 40, 56], [64, 70, 40, 57], [64, 74, 40, 61], [64, 75, 40, 62, "ref"], [64, 78, 40, 65], [64, 79, 40, 66], [64, 80, 40, 67], [64, 81, 40, 68], [64, 83, 40, 70], [64, 87, 40, 74], [64, 88, 40, 75, "ref"], [64, 91, 40, 78], [64, 92, 40, 79], [64, 93, 40, 80], [64, 94, 40, 81], [64, 96, 40, 83], [64, 100, 40, 87], [64, 101, 40, 88, "ref"], [64, 104, 40, 91], [64, 105, 40, 92], [64, 106, 40, 93], [64, 107, 40, 94], [64, 109, 40, 96], [64, 113, 40, 100], [64, 114, 40, 101, "ref"], [64, 117, 40, 104], [64, 118, 40, 105], [64, 119, 40, 106], [64, 120, 40, 107], [64, 121, 40, 108], [64, 122, 40, 109], [65, 4, 41, 2], [66, 2, 42, 0], [67, 2, 42, 1, "exports"], [67, 9, 42, 1], [67, 10, 42, 1, "JsiSkRRect"], [67, 20, 42, 1], [67, 23, 42, 1, "JsiSkRRect"], [67, 33, 42, 1], [68, 0, 42, 1], [68, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "JsiSkRRect", "fromValue", "constructor", "_defineProperty$argument_2", "get__rx", "get__ry", "get__rect"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGG;ECC;GDQ;EEC;qCCa;KDE;GFC;EIC;GJE;EKC;GLE;EMC;GNE;CHC"}}, "type": "js/module"}]}