{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../../skia/core/AnimatedImage", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 65, "index": 65}}], "key": "LhU/Px6ZGN7Mh77TF9+qamLywNY=", "exportNames": ["*"]}}, {"name": "./ReanimatedProxy", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 66}, "end": {"line": 2, "column": 36, "index": 102}}], "key": "9/hMKtQD5ZrdSt9i8tblq1v6X3Y=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedImageValue = void 0;\n  var _AnimatedImage = require(_dependencyMap[1], \"../../skia/core/AnimatedImage\");\n  var _ReanimatedProxy = _interopRequireDefault(require(_dependencyMap[2], \"./ReanimatedProxy\"));\n  const DEFAULT_FRAME_DURATION = 60;\n  const _worklet_11975102113195_init_data = {\n    code: \"function useAnimatedImageValueJs1(frameInfo){const{animatedImage,currentFrame,isPaused,lastTimestamp,frameDuration}=this.__closure;if(!animatedImage){currentFrame.value=null;return;}if(isPaused.value&&lastTimestamp.value!==-1){return;}const{timestamp:timestamp}=frameInfo;const elapsed=timestamp-lastTimestamp.value;if(elapsed<frameDuration){return;}animatedImage.decodeNextFrame();const oldFrame=currentFrame.value;currentFrame.value=animatedImage.getCurrentFrame();if(oldFrame){oldFrame.dispose();}lastTimestamp.value=timestamp;}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\useAnimatedImageValue.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"useAnimatedImageValueJs1\\\",\\\"frameInfo\\\",\\\"animatedImage\\\",\\\"currentFrame\\\",\\\"isPaused\\\",\\\"lastTimestamp\\\",\\\"frameDuration\\\",\\\"__closure\\\",\\\"value\\\",\\\"timestamp\\\",\\\"elapsed\\\",\\\"decodeNextFrame\\\",\\\"oldFrame\\\",\\\"getCurrentFrame\\\",\\\"dispose\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/useAnimatedImageValue.js\\\"],\\\"mappings\\\":\\\"AAauB,SAAAA,wBAAaA,CAAAC,SAAA,QAAAC,aAAA,CAAAC,YAAA,CAAAC,QAAA,CAAAC,aAAA,CAAAC,aAAA,OAAAC,SAAA,CAChC,GAAI,CAACL,aAAa,CAAE,CAClBC,YAAY,CAACK,KAAK,CAAG,IAAI,CACzB,OACF,CACA,GAAIJ,QAAQ,CAACI,KAAK,EAAIH,aAAa,CAACG,KAAK,GAAK,CAAC,CAAC,CAAE,CAChD,OACF,CACA,KAAM,CACJC,SAAA,CAAAA,SACF,CAAC,CAAGR,SAAS,CACb,KAAM,CAAAS,OAAO,CAAGD,SAAS,CAAGJ,aAAa,CAACG,KAAK,CAG/C,GAAIE,OAAO,CAAGJ,aAAa,CAAE,CAC3B,OACF,CAGAJ,aAAa,CAACS,eAAe,CAAC,CAAC,CAC/B,KAAM,CAAAC,QAAQ,CAAGT,YAAY,CAACK,KAAK,CACnCL,YAAY,CAACK,KAAK,CAAGN,aAAa,CAACW,eAAe,CAAC,CAAC,CACpD,GAAID,QAAQ,CAAE,CACZA,QAAQ,CAACE,OAAO,CAAC,CAAC,CACpB,CAGAT,aAAa,CAACG,KAAK,CAAGC,SAAS,CACjC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const useAnimatedImageValue = (source, paused) => {\n    const defaultPaused = _ReanimatedProxy.default.useSharedValue(false);\n    const isPaused = paused !== null && paused !== void 0 ? paused : defaultPaused;\n    const currentFrame = _ReanimatedProxy.default.useSharedValue(null);\n    const lastTimestamp = _ReanimatedProxy.default.useSharedValue(-1);\n    const animatedImage = (0, _AnimatedImage.useAnimatedImage)(source, err => {\n      console.error(err);\n      throw new Error(`Could not load animated image - got '${err.message}'`);\n    });\n    const frameDuration = (animatedImage === null || animatedImage === void 0 ? void 0 : animatedImage.currentFrameDuration()) || DEFAULT_FRAME_DURATION;\n    _ReanimatedProxy.default.useFrameCallback(function () {\n      const _e = [new global.Error(), -6, -27];\n      const useAnimatedImageValueJs1 = function (frameInfo) {\n        if (!animatedImage) {\n          currentFrame.value = null;\n          return;\n        }\n        if (isPaused.value && lastTimestamp.value !== -1) {\n          return;\n        }\n        const {\n          timestamp\n        } = frameInfo;\n        const elapsed = timestamp - lastTimestamp.value;\n\n        // Check if it's time to switch frames based on GIF frame duration\n        if (elapsed < frameDuration) {\n          return;\n        }\n\n        // Update the current frame\n        animatedImage.decodeNextFrame();\n        const oldFrame = currentFrame.value;\n        currentFrame.value = animatedImage.getCurrentFrame();\n        if (oldFrame) {\n          oldFrame.dispose();\n        }\n\n        // Update the last timestamp\n        lastTimestamp.value = timestamp;\n      };\n      useAnimatedImageValueJs1.__closure = {\n        animatedImage,\n        currentFrame,\n        isPaused,\n        lastTimestamp,\n        frameDuration\n      };\n      useAnimatedImageValueJs1.__workletHash = 11975102113195;\n      useAnimatedImageValueJs1.__initData = _worklet_11975102113195_init_data;\n      useAnimatedImageValueJs1.__stackDetails = _e;\n      return useAnimatedImageValueJs1;\n    }());\n    return currentFrame;\n  };\n  exports.useAnimatedImageValue = useAnimatedImageValue;\n});", "lineCount": 72, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_AnimatedImage"], [7, 20, 1, 0], [7, 23, 1, 0, "require"], [7, 30, 1, 0], [7, 31, 1, 0, "_dependencyMap"], [7, 45, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_ReanimatedProxy"], [8, 22, 2, 0], [8, 25, 2, 0, "_interopRequireDefault"], [8, 47, 2, 0], [8, 48, 2, 0, "require"], [8, 55, 2, 0], [8, 56, 2, 0, "_dependencyMap"], [8, 70, 2, 0], [9, 2, 3, 0], [9, 8, 3, 6, "DEFAULT_FRAME_DURATION"], [9, 30, 3, 28], [9, 33, 3, 31], [9, 35, 3, 33], [10, 2, 3, 34], [10, 8, 3, 34, "_worklet_11975102113195_init_data"], [10, 41, 3, 34], [11, 4, 3, 34, "code"], [11, 8, 3, 34], [12, 4, 3, 34, "location"], [12, 12, 3, 34], [13, 4, 3, 34, "sourceMap"], [13, 13, 3, 34], [14, 4, 3, 34, "version"], [14, 11, 3, 34], [15, 2, 3, 34], [16, 2, 4, 7], [16, 8, 4, 13, "useAnimatedImageValue"], [16, 29, 4, 34], [16, 32, 4, 37, "useAnimatedImageValue"], [16, 33, 4, 38, "source"], [16, 39, 4, 44], [16, 41, 4, 46, "paused"], [16, 47, 4, 52], [16, 52, 4, 57], [17, 4, 5, 2], [17, 10, 5, 8, "defaultPaused"], [17, 23, 5, 21], [17, 26, 5, 24, "<PERSON><PERSON>"], [17, 50, 5, 27], [17, 51, 5, 28, "useSharedValue"], [17, 65, 5, 42], [17, 66, 5, 43], [17, 71, 5, 48], [17, 72, 5, 49], [18, 4, 6, 2], [18, 10, 6, 8, "isPaused"], [18, 18, 6, 16], [18, 21, 6, 19, "paused"], [18, 27, 6, 25], [18, 32, 6, 30], [18, 36, 6, 34], [18, 40, 6, 38, "paused"], [18, 46, 6, 44], [18, 51, 6, 49], [18, 56, 6, 54], [18, 57, 6, 55], [18, 60, 6, 58, "paused"], [18, 66, 6, 64], [18, 69, 6, 67, "defaultPaused"], [18, 82, 6, 80], [19, 4, 7, 2], [19, 10, 7, 8, "currentFrame"], [19, 22, 7, 20], [19, 25, 7, 23, "<PERSON><PERSON>"], [19, 49, 7, 26], [19, 50, 7, 27, "useSharedValue"], [19, 64, 7, 41], [19, 65, 7, 42], [19, 69, 7, 46], [19, 70, 7, 47], [20, 4, 8, 2], [20, 10, 8, 8, "lastTimestamp"], [20, 23, 8, 21], [20, 26, 8, 24, "<PERSON><PERSON>"], [20, 50, 8, 27], [20, 51, 8, 28, "useSharedValue"], [20, 65, 8, 42], [20, 66, 8, 43], [20, 67, 8, 44], [20, 68, 8, 45], [20, 69, 8, 46], [21, 4, 9, 2], [21, 10, 9, 8, "animatedImage"], [21, 23, 9, 21], [21, 26, 9, 24], [21, 30, 9, 24, "useAnimatedImage"], [21, 61, 9, 40], [21, 63, 9, 41, "source"], [21, 69, 9, 47], [21, 71, 9, 49, "err"], [21, 74, 9, 52], [21, 78, 9, 56], [22, 6, 10, 4, "console"], [22, 13, 10, 11], [22, 14, 10, 12, "error"], [22, 19, 10, 17], [22, 20, 10, 18, "err"], [22, 23, 10, 21], [22, 24, 10, 22], [23, 6, 11, 4], [23, 12, 11, 10], [23, 16, 11, 14, "Error"], [23, 21, 11, 19], [23, 22, 11, 20], [23, 62, 11, 60, "err"], [23, 65, 11, 63], [23, 66, 11, 64, "message"], [23, 73, 11, 71], [23, 76, 11, 74], [23, 77, 11, 75], [24, 4, 12, 2], [24, 5, 12, 3], [24, 6, 12, 4], [25, 4, 13, 2], [25, 10, 13, 8, "frameDuration"], [25, 23, 13, 21], [25, 26, 13, 24], [25, 27, 13, 25, "animatedImage"], [25, 40, 13, 38], [25, 45, 13, 43], [25, 49, 13, 47], [25, 53, 13, 51, "animatedImage"], [25, 66, 13, 64], [25, 71, 13, 69], [25, 76, 13, 74], [25, 77, 13, 75], [25, 80, 13, 78], [25, 85, 13, 83], [25, 86, 13, 84], [25, 89, 13, 87, "animatedImage"], [25, 102, 13, 100], [25, 103, 13, 101, "currentFrameDuration"], [25, 123, 13, 121], [25, 124, 13, 122], [25, 125, 13, 123], [25, 130, 13, 128, "DEFAULT_FRAME_DURATION"], [25, 152, 13, 150], [26, 4, 14, 2, "<PERSON><PERSON>"], [26, 28, 14, 5], [26, 29, 14, 6, "useFrameCallback"], [26, 45, 14, 22], [26, 46, 14, 23], [27, 6, 14, 23], [27, 12, 14, 23, "_e"], [27, 14, 14, 23], [27, 22, 14, 23, "global"], [27, 28, 14, 23], [27, 29, 14, 23, "Error"], [27, 34, 14, 23], [28, 6, 14, 23], [28, 12, 14, 23, "useAnimatedImageValueJs1"], [28, 36, 14, 23], [28, 48, 14, 23, "useAnimatedImageValueJs1"], [28, 49, 14, 23, "frameInfo"], [28, 58, 14, 32], [28, 60, 14, 36], [29, 8, 15, 4], [29, 12, 15, 8], [29, 13, 15, 9, "animatedImage"], [29, 26, 15, 22], [29, 28, 15, 24], [30, 10, 16, 6, "currentFrame"], [30, 22, 16, 18], [30, 23, 16, 19, "value"], [30, 28, 16, 24], [30, 31, 16, 27], [30, 35, 16, 31], [31, 10, 17, 6], [32, 8, 18, 4], [33, 8, 19, 4], [33, 12, 19, 8, "isPaused"], [33, 20, 19, 16], [33, 21, 19, 17, "value"], [33, 26, 19, 22], [33, 30, 19, 26, "lastTimestamp"], [33, 43, 19, 39], [33, 44, 19, 40, "value"], [33, 49, 19, 45], [33, 54, 19, 50], [33, 55, 19, 51], [33, 56, 19, 52], [33, 58, 19, 54], [34, 10, 20, 6], [35, 8, 21, 4], [36, 8, 22, 4], [36, 14, 22, 10], [37, 10, 23, 6, "timestamp"], [38, 8, 24, 4], [38, 9, 24, 5], [38, 12, 24, 8, "frameInfo"], [38, 21, 24, 17], [39, 8, 25, 4], [39, 14, 25, 10, "elapsed"], [39, 21, 25, 17], [39, 24, 25, 20, "timestamp"], [39, 33, 25, 29], [39, 36, 25, 32, "lastTimestamp"], [39, 49, 25, 45], [39, 50, 25, 46, "value"], [39, 55, 25, 51], [41, 8, 27, 4], [42, 8, 28, 4], [42, 12, 28, 8, "elapsed"], [42, 19, 28, 15], [42, 22, 28, 18, "frameDuration"], [42, 35, 28, 31], [42, 37, 28, 33], [43, 10, 29, 6], [44, 8, 30, 4], [46, 8, 32, 4], [47, 8, 33, 4, "animatedImage"], [47, 21, 33, 17], [47, 22, 33, 18, "decodeNextFrame"], [47, 37, 33, 33], [47, 38, 33, 34], [47, 39, 33, 35], [48, 8, 34, 4], [48, 14, 34, 10, "old<PERSON><PERSON><PERSON>"], [48, 22, 34, 18], [48, 25, 34, 21, "currentFrame"], [48, 37, 34, 33], [48, 38, 34, 34, "value"], [48, 43, 34, 39], [49, 8, 35, 4, "currentFrame"], [49, 20, 35, 16], [49, 21, 35, 17, "value"], [49, 26, 35, 22], [49, 29, 35, 25, "animatedImage"], [49, 42, 35, 38], [49, 43, 35, 39, "getCurrentFrame"], [49, 58, 35, 54], [49, 59, 35, 55], [49, 60, 35, 56], [50, 8, 36, 4], [50, 12, 36, 8, "old<PERSON><PERSON><PERSON>"], [50, 20, 36, 16], [50, 22, 36, 18], [51, 10, 37, 6, "old<PERSON><PERSON><PERSON>"], [51, 18, 37, 14], [51, 19, 37, 15, "dispose"], [51, 26, 37, 22], [51, 27, 37, 23], [51, 28, 37, 24], [52, 8, 38, 4], [54, 8, 40, 4], [55, 8, 41, 4, "lastTimestamp"], [55, 21, 41, 17], [55, 22, 41, 18, "value"], [55, 27, 41, 23], [55, 30, 41, 26, "timestamp"], [55, 39, 41, 35], [56, 6, 42, 2], [56, 7, 42, 3], [57, 6, 42, 3, "useAnimatedImageValueJs1"], [57, 30, 42, 3], [57, 31, 42, 3, "__closure"], [57, 40, 42, 3], [58, 8, 42, 3, "animatedImage"], [58, 21, 42, 3], [59, 8, 42, 3, "currentFrame"], [59, 20, 42, 3], [60, 8, 42, 3, "isPaused"], [60, 16, 42, 3], [61, 8, 42, 3, "lastTimestamp"], [61, 21, 42, 3], [62, 8, 42, 3, "frameDuration"], [63, 6, 42, 3], [64, 6, 42, 3, "useAnimatedImageValueJs1"], [64, 30, 42, 3], [64, 31, 42, 3, "__workletHash"], [64, 44, 42, 3], [65, 6, 42, 3, "useAnimatedImageValueJs1"], [65, 30, 42, 3], [65, 31, 42, 3, "__initData"], [65, 41, 42, 3], [65, 44, 42, 3, "_worklet_11975102113195_init_data"], [65, 77, 42, 3], [66, 6, 42, 3, "useAnimatedImageValueJs1"], [66, 30, 42, 3], [66, 31, 42, 3, "__stackDetails"], [66, 45, 42, 3], [66, 48, 42, 3, "_e"], [66, 50, 42, 3], [67, 6, 42, 3], [67, 13, 42, 3, "useAnimatedImageValueJs1"], [67, 37, 42, 3], [68, 4, 42, 3], [68, 5, 14, 23], [68, 7, 42, 3], [68, 8, 42, 4], [69, 4, 43, 2], [69, 11, 43, 9, "currentFrame"], [69, 23, 43, 21], [70, 2, 44, 0], [70, 3, 44, 1], [71, 2, 44, 2, "exports"], [71, 9, 44, 2], [71, 10, 44, 2, "useAnimatedImageValue"], [71, 31, 44, 2], [71, 34, 44, 2, "useAnimatedImageValue"], [71, 55, 44, 2], [72, 0, 44, 2], [72, 3]], "functionMap": {"names": ["<global>", "useAnimatedImageValue", "useAnimatedImage$argument_1", "Rea.useFrameCallback$argument_0"], "mappings": "AAA;qCCG;iDCK;GDG;uBEE;GF4B;CDE"}}, "type": "js/module"}]}