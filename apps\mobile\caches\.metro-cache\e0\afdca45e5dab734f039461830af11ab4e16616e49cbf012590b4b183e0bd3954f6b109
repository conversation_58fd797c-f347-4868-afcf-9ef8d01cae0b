{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.LinearTransition = LinearTransition;\n  function LinearTransition(name, transitionData) {\n    const {\n      translateX,\n      translateY,\n      scaleX,\n      scaleY\n    } = transitionData;\n    const linearTransition = {\n      name,\n      style: {\n        0: {\n          transform: [{\n            translateX: `${translateX}px`,\n            translateY: `${translateY}px`,\n            scale: `${scaleX},${scaleY}`\n          }]\n        }\n      },\n      duration: 300\n    };\n    return linearTransition;\n  }\n});", "lineCount": 30, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "LinearTransition"], [7, 26, 1, 13], [7, 29, 1, 13, "LinearTransition"], [7, 45, 1, 13], [8, 2, 3, 7], [8, 11, 3, 16, "LinearTransition"], [8, 27, 3, 32, "LinearTransition"], [8, 28, 3, 33, "name"], [8, 32, 3, 37], [8, 34, 3, 39, "transitionData"], [8, 48, 3, 53], [8, 50, 3, 55], [9, 4, 4, 2], [9, 10, 4, 8], [10, 6, 5, 4, "translateX"], [10, 16, 5, 14], [11, 6, 6, 4, "translateY"], [11, 16, 6, 14], [12, 6, 7, 4, "scaleX"], [12, 12, 7, 10], [13, 6, 8, 4, "scaleY"], [14, 4, 9, 2], [14, 5, 9, 3], [14, 8, 9, 6, "transitionData"], [14, 22, 9, 20], [15, 4, 10, 2], [15, 10, 10, 8, "linearTransition"], [15, 26, 10, 24], [15, 29, 10, 27], [16, 6, 11, 4, "name"], [16, 10, 11, 8], [17, 6, 12, 4, "style"], [17, 11, 12, 9], [17, 13, 12, 11], [18, 8, 13, 6], [18, 9, 13, 7], [18, 11, 13, 9], [19, 10, 14, 8, "transform"], [19, 19, 14, 17], [19, 21, 14, 19], [19, 22, 14, 20], [20, 12, 15, 10, "translateX"], [20, 22, 15, 20], [20, 24, 15, 22], [20, 27, 15, 25, "translateX"], [20, 37, 15, 35], [20, 41, 15, 39], [21, 12, 16, 10, "translateY"], [21, 22, 16, 20], [21, 24, 16, 22], [21, 27, 16, 25, "translateY"], [21, 37, 16, 35], [21, 41, 16, 39], [22, 12, 17, 10, "scale"], [22, 17, 17, 15], [22, 19, 17, 17], [22, 22, 17, 20, "scaleX"], [22, 28, 17, 26], [22, 32, 17, 30, "scaleY"], [22, 38, 17, 36], [23, 10, 18, 8], [23, 11, 18, 9], [24, 8, 19, 6], [25, 6, 20, 4], [25, 7, 20, 5], [26, 6, 21, 4, "duration"], [26, 14, 21, 12], [26, 16, 21, 14], [27, 4, 22, 2], [27, 5, 22, 3], [28, 4, 23, 2], [28, 11, 23, 9, "linearTransition"], [28, 27, 23, 25], [29, 2, 24, 0], [30, 0, 24, 1], [30, 3]], "functionMap": {"names": ["<global>", "LinearTransition"], "mappings": "AAA;OCE;CDqB"}}, "type": "js/module"}]}