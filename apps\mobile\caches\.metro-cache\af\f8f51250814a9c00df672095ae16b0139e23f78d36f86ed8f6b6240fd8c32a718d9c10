{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BlazeFaceCanvas;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[1], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\web\\\\BlazeFaceCanvas.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * BlazeFace Canvas Component\n   *\n   * Uses TensorFlow.js BlazeFace model for accurate real-time face detection and blurring.\n   * This implementation matches the working solution from test-blazeface-integration.html\n   * with proper coordinate mapping and mirror effect handling.\n   */\n  function BlazeFaceCanvas({\n    containerId,\n    width,\n    height,\n    onReady\n  }) {\n    _s();\n    const canvasRef = (0, _react.useRef)(null);\n    const rafRef = (0, _react.useRef)(null);\n    const modelRef = (0, _react.useRef)(null);\n    const [isLoading, setIsLoading] = (0, _react.useState)(true);\n    const [faceCount, setFaceCount] = (0, _react.useState)(0);\n    (0, _react.useEffect)(() => {\n      console.log('[BlazeFaceCanvas] Starting initialization...', {\n        containerId,\n        width,\n        height\n      });\n      const container = document.getElementById(containerId);\n      if (!container) {\n        console.error('[BlazeFaceCanvas] Container not found:', containerId);\n        return;\n      }\n      console.log('[BlazeFaceCanvas] Container found:', container);\n      console.log('[BlazeFaceCanvas] Container children:', container.children);\n      const video = container.querySelector('video');\n      if (!video) {\n        console.error('[BlazeFaceCanvas] Video element not found in container');\n        console.log('[BlazeFaceCanvas] Available elements:', container.querySelectorAll('*'));\n        return;\n      }\n      console.log('[BlazeFaceCanvas] Video element found:', video);\n      console.log('[BlazeFaceCanvas] Video dimensions:', video.videoWidth, 'x', video.videoHeight);\n      console.log('[BlazeFaceCanvas] Video ready state:', video.readyState);\n      const canvas = canvasRef.current;\n      if (!canvas) {\n        console.error('[BlazeFaceCanvas] Canvas ref not available');\n        return;\n      }\n\n      // Set canvas size to match video dimensions when available\n      const updateCanvasSize = () => {\n        if (video.videoWidth && video.videoHeight) {\n          canvas.width = video.videoWidth;\n          canvas.height = video.videoHeight;\n          canvas.style.width = '100%';\n          canvas.style.height = '100%';\n          console.log('[BlazeFaceCanvas] Canvas resized to match video:', video.videoWidth, 'x', video.videoHeight);\n        } else {\n          // Fallback to provided dimensions\n          canvas.width = width;\n          canvas.height = height;\n          console.log('[BlazeFaceCanvas] Canvas resized to provided dimensions:', width, 'x', height);\n        }\n      };\n      const ctx = canvas.getContext('2d');\n      if (!ctx) {\n        console.error('[BlazeFaceCanvas] Canvas context not available');\n        return;\n      }\n      let isDetecting = true;\n\n      // Helper function to load scripts\n      const loadScript = src => {\n        return new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = src;\n          script.onload = () => resolve();\n          script.onerror = () => reject(new Error(`Failed to load ${src}`));\n          document.head.appendChild(script);\n        });\n      };\n\n      // Load TensorFlow.js and BlazeFace model - matching working test implementation\n      const loadModel = async () => {\n        try {\n          console.log('[BlazeFaceCanvas] Loading TensorFlow.js...');\n\n          // Load TensorFlow.js\n          if (!window.tf) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.20.0/dist/tf.min.js');\n          }\n          console.log('[BlazeFaceCanvas] Loading BlazeFace model...');\n\n          // Load BlazeFace model\n          if (!window.blazeface) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js');\n          }\n\n          // Initialize BlazeFace model\n          console.log('[BlazeFaceCanvas] Initializing BlazeFace model...');\n          modelRef.current = await window.blazeface.load();\n          console.log('[BlazeFaceCanvas] ✅ BlazeFace model loaded successfully');\n          setIsLoading(false);\n\n          // Update canvas size once video is ready\n          updateCanvasSize();\n\n          // Notify parent that BlazeFace is ready\n          if (onReady) {\n            onReady();\n          }\n\n          // Start detection loop\n          detectLoop();\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] ❌ Failed to load model:', error);\n          setIsLoading(false);\n        }\n      };\n      const detectLoop = async () => {\n        if (!isDetecting || !modelRef.current) return;\n        try {\n          // Check if video has valid dimensions\n          if (!video.videoWidth || !video.videoHeight) {\n            rafRef.current = requestAnimationFrame(detectLoop);\n            return;\n          }\n\n          // Create tensor from video\n          const tf = window.tf;\n          const tensor = tf.browser.fromPixels(video);\n\n          // Detect faces with same confidence threshold as working test\n          const predictions = await modelRef.current.estimateFaces(tensor, false, 0.6);\n          tensor.dispose();\n\n          // Clear canvas - DON'T draw the original video, just clear for overlay\n          ctx.clearRect(0, 0, canvas.width, canvas.height);\n          if (predictions.length > 0) {\n            console.log('[BlazeFaceCanvas] Drawing', predictions.length, 'face(s)');\n            setFaceCount(predictions.length);\n\n            // Process each detected face - OVERLAY BLURRED PATCHES like in working test\n            predictions.forEach((prediction, index) => {\n              console.log('[BlazeFaceCanvas] Processing face', index, ':', prediction);\n              const [x1, y1] = prediction.topLeft;\n              const [x2, y2] = prediction.bottomRight;\n\n              // Fix coordinate order\n              let minX = Math.min(x1, x2);\n              let maxX = Math.max(x1, x2);\n              const minY = Math.min(y1, y2);\n              const maxY = Math.max(y1, y2);\n\n              // Account for horizontal flip (mirror effect) - CRITICAL for front camera\n              const canvasWidth = canvas.width;\n              const flippedMinX = canvasWidth - maxX;\n              const flippedMaxX = canvasWidth - minX;\n              minX = flippedMinX;\n              maxX = flippedMaxX;\n\n              // Calculate face dimensions\n              const faceWidth = maxX - minX;\n              const faceHeight = maxY - minY;\n              if (faceWidth <= 0 || faceHeight <= 0) {\n                return;\n              }\n\n              // Expand the bounding box for better coverage\n              const centerX = (minX + maxX) / 2;\n              const centerY = (minY + maxY) / 2;\n              const expandedWidth = faceWidth * 1.5;\n              const expandedHeight = faceHeight * 1.8;\n\n              // Ensure positive radii\n              const radiusX = Math.max(expandedWidth / 2, 10);\n              const radiusY = Math.max(expandedHeight / 2, 10);\n\n              // Apply OVERLAY elliptical blur patch - like working test\n              ctx.save();\n              ctx.beginPath();\n              ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, Math.PI * 2);\n              ctx.clip();\n              ctx.filter = 'blur(20px)'; // Match working test blur intensity\n              ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n              ctx.restore();\n\n              // Add debug rectangle to show detection area (optional)\n              if (__DEV__) {\n                ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';\n                ctx.lineWidth = 2;\n                ctx.strokeRect(minX, minY, faceWidth, faceHeight);\n              }\n            });\n          } else {\n            setFaceCount(0);\n          }\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] Detection error:', error);\n        }\n\n        // Continue detection loop\n        if (isDetecting) {\n          rafRef.current = requestAnimationFrame(detectLoop);\n        }\n      };\n\n      // Wait for video to be ready before starting\n      const waitForVideoAndStart = () => {\n        if (video.readyState >= 2) {\n          // HAVE_CURRENT_DATA\n          loadModel();\n        } else {\n          video.addEventListener('loadeddata', loadModel, {\n            once: true\n          });\n        }\n      };\n\n      // Start the process\n      waitForVideoAndStart();\n\n      // Cleanup function\n      return () => {\n        isDetecting = false;\n        if (rafRef.current) {\n          cancelAnimationFrame(rafRef.current);\n        }\n      };\n    }, [containerId, width, height]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          position: 'absolute',\n          left: 0,\n          top: 0,\n          width: '100%',\n          height: '100%',\n          pointerEvents: 'none',\n          zIndex: 5,\n          // Above video but below UI controls\n          objectFit: 'cover',\n          backgroundColor: 'transparent'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 7\n      }, this), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(59, 130, 246, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(59, 130, 246, 0.3)'\n        },\n        children: \"Loading BlazeFace model...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this), !isLoading && faceCount > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(16, 185, 129, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(16, 185, 129, 0.3)'\n        },\n        children: [\"\\uD83D\\uDEE1\\uFE0F Protecting \", faceCount, \" face\", faceCount > 1 ? 's' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this), !isLoading && faceCount === 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(107, 114, 128, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(107, 114, 128, 0.3)'\n        },\n        children: \"\\uD83D\\uDC40 Looking for faces...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  _s(BlazeFaceCanvas, \"4hwCZvaM14AzCul7FunDVbR3j+4=\");\n  _c = BlazeFaceCanvas;\n  var _c;\n  $RefreshReg$(_c, \"BlazeFaceCanvas\");\n});", "lineCount": 322, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "_interopRequireWildcard"], [6, 38, 1, 0], [6, 39, 1, 0, "require"], [6, 46, 1, 0], [6, 47, 1, 0, "_dependencyMap"], [6, 61, 1, 0], [7, 2, 1, 59], [7, 6, 1, 59, "_jsxDevRuntime"], [7, 20, 1, 59], [7, 23, 1, 59, "require"], [7, 30, 1, 59], [7, 31, 1, 59, "_dependencyMap"], [7, 45, 1, 59], [8, 2, 1, 59], [8, 6, 1, 59, "_jsxFileName"], [8, 18, 1, 59], [9, 4, 1, 59, "_s"], [9, 6, 1, 59], [9, 9, 1, 59, "$RefreshSig$"], [9, 21, 1, 59], [10, 2, 1, 59], [10, 11, 1, 59, "_interopRequireWildcard"], [10, 35, 1, 59, "e"], [10, 36, 1, 59], [10, 38, 1, 59, "t"], [10, 39, 1, 59], [10, 68, 1, 59, "WeakMap"], [10, 75, 1, 59], [10, 81, 1, 59, "r"], [10, 82, 1, 59], [10, 89, 1, 59, "WeakMap"], [10, 96, 1, 59], [10, 100, 1, 59, "n"], [10, 101, 1, 59], [10, 108, 1, 59, "WeakMap"], [10, 115, 1, 59], [10, 127, 1, 59, "_interopRequireWildcard"], [10, 150, 1, 59], [10, 162, 1, 59, "_interopRequireWildcard"], [10, 163, 1, 59, "e"], [10, 164, 1, 59], [10, 166, 1, 59, "t"], [10, 167, 1, 59], [10, 176, 1, 59, "t"], [10, 177, 1, 59], [10, 181, 1, 59, "e"], [10, 182, 1, 59], [10, 186, 1, 59, "e"], [10, 187, 1, 59], [10, 188, 1, 59, "__esModule"], [10, 198, 1, 59], [10, 207, 1, 59, "e"], [10, 208, 1, 59], [10, 214, 1, 59, "o"], [10, 215, 1, 59], [10, 217, 1, 59, "i"], [10, 218, 1, 59], [10, 220, 1, 59, "f"], [10, 221, 1, 59], [10, 226, 1, 59, "__proto__"], [10, 235, 1, 59], [10, 243, 1, 59, "default"], [10, 250, 1, 59], [10, 252, 1, 59, "e"], [10, 253, 1, 59], [10, 270, 1, 59, "e"], [10, 271, 1, 59], [10, 294, 1, 59, "e"], [10, 295, 1, 59], [10, 320, 1, 59, "e"], [10, 321, 1, 59], [10, 330, 1, 59, "f"], [10, 331, 1, 59], [10, 337, 1, 59, "o"], [10, 338, 1, 59], [10, 341, 1, 59, "t"], [10, 342, 1, 59], [10, 345, 1, 59, "n"], [10, 346, 1, 59], [10, 349, 1, 59, "r"], [10, 350, 1, 59], [10, 358, 1, 59, "o"], [10, 359, 1, 59], [10, 360, 1, 59, "has"], [10, 363, 1, 59], [10, 364, 1, 59, "e"], [10, 365, 1, 59], [10, 375, 1, 59, "o"], [10, 376, 1, 59], [10, 377, 1, 59, "get"], [10, 380, 1, 59], [10, 381, 1, 59, "e"], [10, 382, 1, 59], [10, 385, 1, 59, "o"], [10, 386, 1, 59], [10, 387, 1, 59, "set"], [10, 390, 1, 59], [10, 391, 1, 59, "e"], [10, 392, 1, 59], [10, 394, 1, 59, "f"], [10, 395, 1, 59], [10, 411, 1, 59, "t"], [10, 412, 1, 59], [10, 416, 1, 59, "e"], [10, 417, 1, 59], [10, 433, 1, 59, "t"], [10, 434, 1, 59], [10, 441, 1, 59, "hasOwnProperty"], [10, 455, 1, 59], [10, 456, 1, 59, "call"], [10, 460, 1, 59], [10, 461, 1, 59, "e"], [10, 462, 1, 59], [10, 464, 1, 59, "t"], [10, 465, 1, 59], [10, 472, 1, 59, "i"], [10, 473, 1, 59], [10, 477, 1, 59, "o"], [10, 478, 1, 59], [10, 481, 1, 59, "Object"], [10, 487, 1, 59], [10, 488, 1, 59, "defineProperty"], [10, 502, 1, 59], [10, 507, 1, 59, "Object"], [10, 513, 1, 59], [10, 514, 1, 59, "getOwnPropertyDescriptor"], [10, 538, 1, 59], [10, 539, 1, 59, "e"], [10, 540, 1, 59], [10, 542, 1, 59, "t"], [10, 543, 1, 59], [10, 550, 1, 59, "i"], [10, 551, 1, 59], [10, 552, 1, 59, "get"], [10, 555, 1, 59], [10, 559, 1, 59, "i"], [10, 560, 1, 59], [10, 561, 1, 59, "set"], [10, 564, 1, 59], [10, 568, 1, 59, "o"], [10, 569, 1, 59], [10, 570, 1, 59, "f"], [10, 571, 1, 59], [10, 573, 1, 59, "t"], [10, 574, 1, 59], [10, 576, 1, 59, "i"], [10, 577, 1, 59], [10, 581, 1, 59, "f"], [10, 582, 1, 59], [10, 583, 1, 59, "t"], [10, 584, 1, 59], [10, 588, 1, 59, "e"], [10, 589, 1, 59], [10, 590, 1, 59, "t"], [10, 591, 1, 59], [10, 602, 1, 59, "f"], [10, 603, 1, 59], [10, 608, 1, 59, "e"], [10, 609, 1, 59], [10, 611, 1, 59, "t"], [10, 612, 1, 59], [11, 2, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [15, 0, 14, 0], [16, 0, 15, 0], [17, 0, 16, 0], [18, 2, 17, 15], [18, 11, 17, 24, "BlazeFaceCanvas"], [18, 26, 17, 39, "BlazeFaceCanvas"], [18, 27, 17, 40], [19, 4, 17, 42, "containerId"], [19, 15, 17, 53], [20, 4, 17, 55, "width"], [20, 9, 17, 60], [21, 4, 17, 62, "height"], [21, 10, 17, 68], [22, 4, 17, 70, "onReady"], [23, 2, 17, 100], [23, 3, 17, 101], [23, 5, 17, 103], [24, 4, 17, 103, "_s"], [24, 6, 17, 103], [25, 4, 18, 2], [25, 10, 18, 8, "canvasRef"], [25, 19, 18, 17], [25, 22, 18, 20], [25, 26, 18, 20, "useRef"], [25, 39, 18, 26], [25, 41, 18, 53], [25, 45, 18, 57], [25, 46, 18, 58], [26, 4, 19, 2], [26, 10, 19, 8, "rafRef"], [26, 16, 19, 14], [26, 19, 19, 17], [26, 23, 19, 17, "useRef"], [26, 36, 19, 23], [26, 38, 19, 39], [26, 42, 19, 43], [26, 43, 19, 44], [27, 4, 20, 2], [27, 10, 20, 8, "modelRef"], [27, 18, 20, 16], [27, 21, 20, 19], [27, 25, 20, 19, "useRef"], [27, 38, 20, 25], [27, 40, 20, 38], [27, 44, 20, 42], [27, 45, 20, 43], [28, 4, 21, 2], [28, 10, 21, 8], [28, 11, 21, 9, "isLoading"], [28, 20, 21, 18], [28, 22, 21, 20, "setIsLoading"], [28, 34, 21, 32], [28, 35, 21, 33], [28, 38, 21, 36], [28, 42, 21, 36, "useState"], [28, 57, 21, 44], [28, 59, 21, 45], [28, 63, 21, 49], [28, 64, 21, 50], [29, 4, 22, 2], [29, 10, 22, 8], [29, 11, 22, 9, "faceCount"], [29, 20, 22, 18], [29, 22, 22, 20, "setFaceCount"], [29, 34, 22, 32], [29, 35, 22, 33], [29, 38, 22, 36], [29, 42, 22, 36, "useState"], [29, 57, 22, 44], [29, 59, 22, 45], [29, 60, 22, 46], [29, 61, 22, 47], [30, 4, 24, 2], [30, 8, 24, 2, "useEffect"], [30, 24, 24, 11], [30, 26, 24, 12], [30, 32, 24, 18], [31, 6, 25, 4, "console"], [31, 13, 25, 11], [31, 14, 25, 12, "log"], [31, 17, 25, 15], [31, 18, 25, 16], [31, 64, 25, 62], [31, 66, 25, 64], [32, 8, 25, 66, "containerId"], [32, 19, 25, 77], [33, 8, 25, 79, "width"], [33, 13, 25, 84], [34, 8, 25, 86, "height"], [35, 6, 25, 93], [35, 7, 25, 94], [35, 8, 25, 95], [36, 6, 27, 4], [36, 12, 27, 10, "container"], [36, 21, 27, 19], [36, 24, 27, 22, "document"], [36, 32, 27, 30], [36, 33, 27, 31, "getElementById"], [36, 47, 27, 45], [36, 48, 27, 46, "containerId"], [36, 59, 27, 57], [36, 60, 27, 58], [37, 6, 28, 4], [37, 10, 28, 8], [37, 11, 28, 9, "container"], [37, 20, 28, 18], [37, 22, 28, 20], [38, 8, 29, 6, "console"], [38, 15, 29, 13], [38, 16, 29, 14, "error"], [38, 21, 29, 19], [38, 22, 29, 20], [38, 62, 29, 60], [38, 64, 29, 62, "containerId"], [38, 75, 29, 73], [38, 76, 29, 74], [39, 8, 30, 6], [40, 6, 31, 4], [41, 6, 33, 4, "console"], [41, 13, 33, 11], [41, 14, 33, 12, "log"], [41, 17, 33, 15], [41, 18, 33, 16], [41, 54, 33, 52], [41, 56, 33, 54, "container"], [41, 65, 33, 63], [41, 66, 33, 64], [42, 6, 34, 4, "console"], [42, 13, 34, 11], [42, 14, 34, 12, "log"], [42, 17, 34, 15], [42, 18, 34, 16], [42, 57, 34, 55], [42, 59, 34, 57, "container"], [42, 68, 34, 66], [42, 69, 34, 67, "children"], [42, 77, 34, 75], [42, 78, 34, 76], [43, 6, 36, 4], [43, 12, 36, 10, "video"], [43, 17, 36, 40], [43, 20, 36, 43, "container"], [43, 29, 36, 52], [43, 30, 36, 53, "querySelector"], [43, 43, 36, 66], [43, 44, 36, 67], [43, 51, 36, 74], [43, 52, 36, 75], [44, 6, 37, 4], [44, 10, 37, 8], [44, 11, 37, 9, "video"], [44, 16, 37, 14], [44, 18, 37, 16], [45, 8, 38, 6, "console"], [45, 15, 38, 13], [45, 16, 38, 14, "error"], [45, 21, 38, 19], [45, 22, 38, 20], [45, 78, 38, 76], [45, 79, 38, 77], [46, 8, 39, 6, "console"], [46, 15, 39, 13], [46, 16, 39, 14, "log"], [46, 19, 39, 17], [46, 20, 39, 18], [46, 59, 39, 57], [46, 61, 39, 59, "container"], [46, 70, 39, 68], [46, 71, 39, 69, "querySelectorAll"], [46, 87, 39, 85], [46, 88, 39, 86], [46, 91, 39, 89], [46, 92, 39, 90], [46, 93, 39, 91], [47, 8, 40, 6], [48, 6, 41, 4], [49, 6, 43, 4, "console"], [49, 13, 43, 11], [49, 14, 43, 12, "log"], [49, 17, 43, 15], [49, 18, 43, 16], [49, 58, 43, 56], [49, 60, 43, 58, "video"], [49, 65, 43, 63], [49, 66, 43, 64], [50, 6, 44, 4, "console"], [50, 13, 44, 11], [50, 14, 44, 12, "log"], [50, 17, 44, 15], [50, 18, 44, 16], [50, 55, 44, 53], [50, 57, 44, 55, "video"], [50, 62, 44, 60], [50, 63, 44, 61, "videoWidth"], [50, 73, 44, 71], [50, 75, 44, 73], [50, 78, 44, 76], [50, 80, 44, 78, "video"], [50, 85, 44, 83], [50, 86, 44, 84, "videoHeight"], [50, 97, 44, 95], [50, 98, 44, 96], [51, 6, 45, 4, "console"], [51, 13, 45, 11], [51, 14, 45, 12, "log"], [51, 17, 45, 15], [51, 18, 45, 16], [51, 56, 45, 54], [51, 58, 45, 56, "video"], [51, 63, 45, 61], [51, 64, 45, 62, "readyState"], [51, 74, 45, 72], [51, 75, 45, 73], [52, 6, 47, 4], [52, 12, 47, 10, "canvas"], [52, 18, 47, 16], [52, 21, 47, 19, "canvasRef"], [52, 30, 47, 28], [52, 31, 47, 29, "current"], [52, 38, 47, 36], [53, 6, 48, 4], [53, 10, 48, 8], [53, 11, 48, 9, "canvas"], [53, 17, 48, 15], [53, 19, 48, 17], [54, 8, 49, 6, "console"], [54, 15, 49, 13], [54, 16, 49, 14, "error"], [54, 21, 49, 19], [54, 22, 49, 20], [54, 66, 49, 64], [54, 67, 49, 65], [55, 8, 50, 6], [56, 6, 51, 4], [58, 6, 53, 4], [59, 6, 54, 4], [59, 12, 54, 10, "updateCanvasSize"], [59, 28, 54, 26], [59, 31, 54, 29, "updateCanvasSize"], [59, 32, 54, 29], [59, 37, 54, 35], [60, 8, 55, 6], [60, 12, 55, 10, "video"], [60, 17, 55, 15], [60, 18, 55, 16, "videoWidth"], [60, 28, 55, 26], [60, 32, 55, 30, "video"], [60, 37, 55, 35], [60, 38, 55, 36, "videoHeight"], [60, 49, 55, 47], [60, 51, 55, 49], [61, 10, 56, 8, "canvas"], [61, 16, 56, 14], [61, 17, 56, 15, "width"], [61, 22, 56, 20], [61, 25, 56, 23, "video"], [61, 30, 56, 28], [61, 31, 56, 29, "videoWidth"], [61, 41, 56, 39], [62, 10, 57, 8, "canvas"], [62, 16, 57, 14], [62, 17, 57, 15, "height"], [62, 23, 57, 21], [62, 26, 57, 24, "video"], [62, 31, 57, 29], [62, 32, 57, 30, "videoHeight"], [62, 43, 57, 41], [63, 10, 58, 8, "canvas"], [63, 16, 58, 14], [63, 17, 58, 15, "style"], [63, 22, 58, 20], [63, 23, 58, 21, "width"], [63, 28, 58, 26], [63, 31, 58, 29], [63, 37, 58, 35], [64, 10, 59, 8, "canvas"], [64, 16, 59, 14], [64, 17, 59, 15, "style"], [64, 22, 59, 20], [64, 23, 59, 21, "height"], [64, 29, 59, 27], [64, 32, 59, 30], [64, 38, 59, 36], [65, 10, 60, 8, "console"], [65, 17, 60, 15], [65, 18, 60, 16, "log"], [65, 21, 60, 19], [65, 22, 60, 20], [65, 72, 60, 70], [65, 74, 60, 72, "video"], [65, 79, 60, 77], [65, 80, 60, 78, "videoWidth"], [65, 90, 60, 88], [65, 92, 60, 90], [65, 95, 60, 93], [65, 97, 60, 95, "video"], [65, 102, 60, 100], [65, 103, 60, 101, "videoHeight"], [65, 114, 60, 112], [65, 115, 60, 113], [66, 8, 61, 6], [66, 9, 61, 7], [66, 15, 61, 13], [67, 10, 62, 8], [68, 10, 63, 8, "canvas"], [68, 16, 63, 14], [68, 17, 63, 15, "width"], [68, 22, 63, 20], [68, 25, 63, 23, "width"], [68, 30, 63, 28], [69, 10, 64, 8, "canvas"], [69, 16, 64, 14], [69, 17, 64, 15, "height"], [69, 23, 64, 21], [69, 26, 64, 24, "height"], [69, 32, 64, 30], [70, 10, 65, 8, "console"], [70, 17, 65, 15], [70, 18, 65, 16, "log"], [70, 21, 65, 19], [70, 22, 65, 20], [70, 80, 65, 78], [70, 82, 65, 80, "width"], [70, 87, 65, 85], [70, 89, 65, 87], [70, 92, 65, 90], [70, 94, 65, 92, "height"], [70, 100, 65, 98], [70, 101, 65, 99], [71, 8, 66, 6], [72, 6, 67, 4], [72, 7, 67, 5], [73, 6, 69, 4], [73, 12, 69, 10, "ctx"], [73, 15, 69, 13], [73, 18, 69, 16, "canvas"], [73, 24, 69, 22], [73, 25, 69, 23, "getContext"], [73, 35, 69, 33], [73, 36, 69, 34], [73, 40, 69, 38], [73, 41, 69, 39], [74, 6, 70, 4], [74, 10, 70, 8], [74, 11, 70, 9, "ctx"], [74, 14, 70, 12], [74, 16, 70, 14], [75, 8, 71, 6, "console"], [75, 15, 71, 13], [75, 16, 71, 14, "error"], [75, 21, 71, 19], [75, 22, 71, 20], [75, 70, 71, 68], [75, 71, 71, 69], [76, 8, 72, 6], [77, 6, 73, 4], [78, 6, 75, 4], [78, 10, 75, 8, "isDetecting"], [78, 21, 75, 19], [78, 24, 75, 22], [78, 28, 75, 26], [80, 6, 77, 4], [81, 6, 78, 4], [81, 12, 78, 10, "loadScript"], [81, 22, 78, 20], [81, 25, 78, 24, "src"], [81, 28, 78, 35], [81, 32, 78, 55], [82, 8, 79, 6], [82, 15, 79, 13], [82, 19, 79, 17, "Promise"], [82, 26, 79, 24], [82, 27, 79, 25], [82, 28, 79, 26, "resolve"], [82, 35, 79, 33], [82, 37, 79, 35, "reject"], [82, 43, 79, 41], [82, 48, 79, 46], [83, 10, 80, 8], [83, 16, 80, 14, "script"], [83, 22, 80, 20], [83, 25, 80, 23, "document"], [83, 33, 80, 31], [83, 34, 80, 32, "createElement"], [83, 47, 80, 45], [83, 48, 80, 46], [83, 56, 80, 54], [83, 57, 80, 55], [84, 10, 81, 8, "script"], [84, 16, 81, 14], [84, 17, 81, 15, "src"], [84, 20, 81, 18], [84, 23, 81, 21, "src"], [84, 26, 81, 24], [85, 10, 82, 8, "script"], [85, 16, 82, 14], [85, 17, 82, 15, "onload"], [85, 23, 82, 21], [85, 26, 82, 24], [85, 32, 82, 30, "resolve"], [85, 39, 82, 37], [85, 40, 82, 38], [85, 41, 82, 39], [86, 10, 83, 8, "script"], [86, 16, 83, 14], [86, 17, 83, 15, "onerror"], [86, 24, 83, 22], [86, 27, 83, 25], [86, 33, 83, 31, "reject"], [86, 39, 83, 37], [86, 40, 83, 38], [86, 44, 83, 42, "Error"], [86, 49, 83, 47], [86, 50, 83, 48], [86, 68, 83, 66, "src"], [86, 71, 83, 69], [86, 73, 83, 71], [86, 74, 83, 72], [86, 75, 83, 73], [87, 10, 84, 8, "document"], [87, 18, 84, 16], [87, 19, 84, 17, "head"], [87, 23, 84, 21], [87, 24, 84, 22, "append<PERSON><PERSON><PERSON>"], [87, 35, 84, 33], [87, 36, 84, 34, "script"], [87, 42, 84, 40], [87, 43, 84, 41], [88, 8, 85, 6], [88, 9, 85, 7], [88, 10, 85, 8], [89, 6, 86, 4], [89, 7, 86, 5], [91, 6, 88, 4], [92, 6, 89, 4], [92, 12, 89, 10, "loadModel"], [92, 21, 89, 19], [92, 24, 89, 22], [92, 30, 89, 22, "loadModel"], [92, 31, 89, 22], [92, 36, 89, 34], [93, 8, 90, 6], [93, 12, 90, 10], [94, 10, 91, 8, "console"], [94, 17, 91, 15], [94, 18, 91, 16, "log"], [94, 21, 91, 19], [94, 22, 91, 20], [94, 66, 91, 64], [94, 67, 91, 65], [96, 10, 93, 8], [97, 10, 94, 8], [97, 14, 94, 12], [97, 15, 94, 14, "window"], [97, 21, 94, 20], [97, 22, 94, 29, "tf"], [97, 24, 94, 31], [97, 26, 94, 33], [98, 12, 95, 10], [98, 18, 95, 16, "loadScript"], [98, 28, 95, 26], [98, 29, 95, 27], [98, 98, 95, 96], [98, 99, 95, 97], [99, 10, 96, 8], [100, 10, 98, 8, "console"], [100, 17, 98, 15], [100, 18, 98, 16, "log"], [100, 21, 98, 19], [100, 22, 98, 20], [100, 68, 98, 66], [100, 69, 98, 67], [102, 10, 100, 8], [103, 10, 101, 8], [103, 14, 101, 12], [103, 15, 101, 14, "window"], [103, 21, 101, 20], [103, 22, 101, 29, "blazeface"], [103, 31, 101, 38], [103, 33, 101, 40], [104, 12, 102, 10], [104, 18, 102, 16, "loadScript"], [104, 28, 102, 26], [104, 29, 102, 27], [104, 112, 102, 110], [104, 113, 102, 111], [105, 10, 103, 8], [107, 10, 105, 8], [108, 10, 106, 8, "console"], [108, 17, 106, 15], [108, 18, 106, 16, "log"], [108, 21, 106, 19], [108, 22, 106, 20], [108, 73, 106, 71], [108, 74, 106, 72], [109, 10, 107, 8, "modelRef"], [109, 18, 107, 16], [109, 19, 107, 17, "current"], [109, 26, 107, 24], [109, 29, 107, 27], [109, 35, 107, 34, "window"], [109, 41, 107, 40], [109, 42, 107, 49, "blazeface"], [109, 51, 107, 58], [109, 52, 107, 59, "load"], [109, 56, 107, 63], [109, 57, 107, 64], [109, 58, 107, 65], [110, 10, 108, 8, "console"], [110, 17, 108, 15], [110, 18, 108, 16, "log"], [110, 21, 108, 19], [110, 22, 108, 20], [110, 79, 108, 77], [110, 80, 108, 78], [111, 10, 110, 8, "setIsLoading"], [111, 22, 110, 20], [111, 23, 110, 21], [111, 28, 110, 26], [111, 29, 110, 27], [113, 10, 112, 8], [114, 10, 113, 8, "updateCanvasSize"], [114, 26, 113, 24], [114, 27, 113, 25], [114, 28, 113, 26], [116, 10, 115, 8], [117, 10, 116, 8], [117, 14, 116, 12, "onReady"], [117, 21, 116, 19], [117, 23, 116, 21], [118, 12, 117, 10, "onReady"], [118, 19, 117, 17], [118, 20, 117, 18], [118, 21, 117, 19], [119, 10, 118, 8], [121, 10, 120, 8], [122, 10, 121, 8, "detectLoop"], [122, 20, 121, 18], [122, 21, 121, 19], [122, 22, 121, 20], [123, 8, 123, 6], [123, 9, 123, 7], [123, 10, 123, 8], [123, 17, 123, 15, "error"], [123, 22, 123, 20], [123, 24, 123, 22], [124, 10, 124, 8, "console"], [124, 17, 124, 15], [124, 18, 124, 16, "error"], [124, 23, 124, 21], [124, 24, 124, 22], [124, 67, 124, 65], [124, 69, 124, 67, "error"], [124, 74, 124, 72], [124, 75, 124, 73], [125, 10, 125, 8, "setIsLoading"], [125, 22, 125, 20], [125, 23, 125, 21], [125, 28, 125, 26], [125, 29, 125, 27], [126, 8, 126, 6], [127, 6, 127, 4], [127, 7, 127, 5], [128, 6, 129, 4], [128, 12, 129, 10, "detectLoop"], [128, 22, 129, 20], [128, 25, 129, 23], [128, 31, 129, 23, "detectLoop"], [128, 32, 129, 23], [128, 37, 129, 35], [129, 8, 130, 6], [129, 12, 130, 10], [129, 13, 130, 11, "isDetecting"], [129, 24, 130, 22], [129, 28, 130, 26], [129, 29, 130, 27, "modelRef"], [129, 37, 130, 35], [129, 38, 130, 36, "current"], [129, 45, 130, 43], [129, 47, 130, 45], [130, 8, 132, 6], [130, 12, 132, 10], [131, 10, 133, 8], [132, 10, 134, 8], [132, 14, 134, 12], [132, 15, 134, 13, "video"], [132, 20, 134, 18], [132, 21, 134, 19, "videoWidth"], [132, 31, 134, 29], [132, 35, 134, 33], [132, 36, 134, 34, "video"], [132, 41, 134, 39], [132, 42, 134, 40, "videoHeight"], [132, 53, 134, 51], [132, 55, 134, 53], [133, 12, 135, 10, "rafRef"], [133, 18, 135, 16], [133, 19, 135, 17, "current"], [133, 26, 135, 24], [133, 29, 135, 27, "requestAnimationFrame"], [133, 50, 135, 48], [133, 51, 135, 49, "detectLoop"], [133, 61, 135, 59], [133, 62, 135, 60], [134, 12, 136, 10], [135, 10, 137, 8], [137, 10, 139, 8], [138, 10, 140, 8], [138, 16, 140, 14, "tf"], [138, 18, 140, 16], [138, 21, 140, 20, "window"], [138, 27, 140, 26], [138, 28, 140, 35, "tf"], [138, 30, 140, 37], [139, 10, 141, 8], [139, 16, 141, 14, "tensor"], [139, 22, 141, 20], [139, 25, 141, 23, "tf"], [139, 27, 141, 25], [139, 28, 141, 26, "browser"], [139, 35, 141, 33], [139, 36, 141, 34, "fromPixels"], [139, 46, 141, 44], [139, 47, 141, 45, "video"], [139, 52, 141, 50], [139, 53, 141, 51], [141, 10, 143, 8], [142, 10, 144, 8], [142, 16, 144, 14, "predictions"], [142, 27, 144, 25], [142, 30, 144, 28], [142, 36, 144, 34, "modelRef"], [142, 44, 144, 42], [142, 45, 144, 43, "current"], [142, 52, 144, 50], [142, 53, 144, 51, "estimateFaces"], [142, 66, 144, 64], [142, 67, 144, 65, "tensor"], [142, 73, 144, 71], [142, 75, 144, 73], [142, 80, 144, 78], [142, 82, 144, 80], [142, 85, 144, 83], [142, 86, 144, 84], [143, 10, 145, 8, "tensor"], [143, 16, 145, 14], [143, 17, 145, 15, "dispose"], [143, 24, 145, 22], [143, 25, 145, 23], [143, 26, 145, 24], [145, 10, 147, 8], [146, 10, 148, 8, "ctx"], [146, 13, 148, 11], [146, 14, 148, 12, "clearRect"], [146, 23, 148, 21], [146, 24, 148, 22], [146, 25, 148, 23], [146, 27, 148, 25], [146, 28, 148, 26], [146, 30, 148, 28, "canvas"], [146, 36, 148, 34], [146, 37, 148, 35, "width"], [146, 42, 148, 40], [146, 44, 148, 42, "canvas"], [146, 50, 148, 48], [146, 51, 148, 49, "height"], [146, 57, 148, 55], [146, 58, 148, 56], [147, 10, 150, 8], [147, 14, 150, 12, "predictions"], [147, 25, 150, 23], [147, 26, 150, 24, "length"], [147, 32, 150, 30], [147, 35, 150, 33], [147, 36, 150, 34], [147, 38, 150, 36], [148, 12, 151, 10, "console"], [148, 19, 151, 17], [148, 20, 151, 18, "log"], [148, 23, 151, 21], [148, 24, 151, 22], [148, 51, 151, 49], [148, 53, 151, 51, "predictions"], [148, 64, 151, 62], [148, 65, 151, 63, "length"], [148, 71, 151, 69], [148, 73, 151, 71], [148, 82, 151, 80], [148, 83, 151, 81], [149, 12, 152, 10, "setFaceCount"], [149, 24, 152, 22], [149, 25, 152, 23, "predictions"], [149, 36, 152, 34], [149, 37, 152, 35, "length"], [149, 43, 152, 41], [149, 44, 152, 42], [151, 12, 154, 10], [152, 12, 155, 10, "predictions"], [152, 23, 155, 21], [152, 24, 155, 22, "for<PERSON>ach"], [152, 31, 155, 29], [152, 32, 155, 30], [152, 33, 155, 31, "prediction"], [152, 43, 155, 46], [152, 45, 155, 48, "index"], [152, 50, 155, 61], [152, 55, 155, 66], [153, 14, 156, 12, "console"], [153, 21, 156, 19], [153, 22, 156, 20, "log"], [153, 25, 156, 23], [153, 26, 156, 24], [153, 61, 156, 59], [153, 63, 156, 61, "index"], [153, 68, 156, 66], [153, 70, 156, 68], [153, 73, 156, 71], [153, 75, 156, 73, "prediction"], [153, 85, 156, 83], [153, 86, 156, 84], [154, 14, 157, 12], [154, 20, 157, 18], [154, 21, 157, 19, "x1"], [154, 23, 157, 21], [154, 25, 157, 23, "y1"], [154, 27, 157, 25], [154, 28, 157, 26], [154, 31, 157, 29, "prediction"], [154, 41, 157, 39], [154, 42, 157, 40, "topLeft"], [154, 49, 157, 47], [155, 14, 158, 12], [155, 20, 158, 18], [155, 21, 158, 19, "x2"], [155, 23, 158, 21], [155, 25, 158, 23, "y2"], [155, 27, 158, 25], [155, 28, 158, 26], [155, 31, 158, 29, "prediction"], [155, 41, 158, 39], [155, 42, 158, 40, "bottomRight"], [155, 53, 158, 51], [157, 14, 160, 12], [158, 14, 161, 12], [158, 18, 161, 16, "minX"], [158, 22, 161, 20], [158, 25, 161, 23, "Math"], [158, 29, 161, 27], [158, 30, 161, 28, "min"], [158, 33, 161, 31], [158, 34, 161, 32, "x1"], [158, 36, 161, 34], [158, 38, 161, 36, "x2"], [158, 40, 161, 38], [158, 41, 161, 39], [159, 14, 162, 12], [159, 18, 162, 16, "maxX"], [159, 22, 162, 20], [159, 25, 162, 23, "Math"], [159, 29, 162, 27], [159, 30, 162, 28, "max"], [159, 33, 162, 31], [159, 34, 162, 32, "x1"], [159, 36, 162, 34], [159, 38, 162, 36, "x2"], [159, 40, 162, 38], [159, 41, 162, 39], [160, 14, 163, 12], [160, 20, 163, 18, "minY"], [160, 24, 163, 22], [160, 27, 163, 25, "Math"], [160, 31, 163, 29], [160, 32, 163, 30, "min"], [160, 35, 163, 33], [160, 36, 163, 34, "y1"], [160, 38, 163, 36], [160, 40, 163, 38, "y2"], [160, 42, 163, 40], [160, 43, 163, 41], [161, 14, 164, 12], [161, 20, 164, 18, "maxY"], [161, 24, 164, 22], [161, 27, 164, 25, "Math"], [161, 31, 164, 29], [161, 32, 164, 30, "max"], [161, 35, 164, 33], [161, 36, 164, 34, "y1"], [161, 38, 164, 36], [161, 40, 164, 38, "y2"], [161, 42, 164, 40], [161, 43, 164, 41], [163, 14, 166, 12], [164, 14, 167, 12], [164, 20, 167, 18, "canvasWidth"], [164, 31, 167, 29], [164, 34, 167, 32, "canvas"], [164, 40, 167, 38], [164, 41, 167, 39, "width"], [164, 46, 167, 44], [165, 14, 168, 12], [165, 20, 168, 18, "flippedMinX"], [165, 31, 168, 29], [165, 34, 168, 32, "canvasWidth"], [165, 45, 168, 43], [165, 48, 168, 46, "maxX"], [165, 52, 168, 50], [166, 14, 169, 12], [166, 20, 169, 18, "flippedMaxX"], [166, 31, 169, 29], [166, 34, 169, 32, "canvasWidth"], [166, 45, 169, 43], [166, 48, 169, 46, "minX"], [166, 52, 169, 50], [167, 14, 170, 12, "minX"], [167, 18, 170, 16], [167, 21, 170, 19, "flippedMinX"], [167, 32, 170, 30], [168, 14, 171, 12, "maxX"], [168, 18, 171, 16], [168, 21, 171, 19, "flippedMaxX"], [168, 32, 171, 30], [170, 14, 173, 12], [171, 14, 174, 12], [171, 20, 174, 18, "faceWidth"], [171, 29, 174, 27], [171, 32, 174, 30, "maxX"], [171, 36, 174, 34], [171, 39, 174, 37, "minX"], [171, 43, 174, 41], [172, 14, 175, 12], [172, 20, 175, 18, "faceHeight"], [172, 30, 175, 28], [172, 33, 175, 31, "maxY"], [172, 37, 175, 35], [172, 40, 175, 38, "minY"], [172, 44, 175, 42], [173, 14, 177, 12], [173, 18, 177, 16, "faceWidth"], [173, 27, 177, 25], [173, 31, 177, 29], [173, 32, 177, 30], [173, 36, 177, 34, "faceHeight"], [173, 46, 177, 44], [173, 50, 177, 48], [173, 51, 177, 49], [173, 53, 177, 51], [174, 16, 178, 14], [175, 14, 179, 12], [177, 14, 181, 12], [178, 14, 182, 12], [178, 20, 182, 18, "centerX"], [178, 27, 182, 25], [178, 30, 182, 28], [178, 31, 182, 29, "minX"], [178, 35, 182, 33], [178, 38, 182, 36, "maxX"], [178, 42, 182, 40], [178, 46, 182, 44], [178, 47, 182, 45], [179, 14, 183, 12], [179, 20, 183, 18, "centerY"], [179, 27, 183, 25], [179, 30, 183, 28], [179, 31, 183, 29, "minY"], [179, 35, 183, 33], [179, 38, 183, 36, "maxY"], [179, 42, 183, 40], [179, 46, 183, 44], [179, 47, 183, 45], [180, 14, 184, 12], [180, 20, 184, 18, "expandedWidth"], [180, 33, 184, 31], [180, 36, 184, 34, "faceWidth"], [180, 45, 184, 43], [180, 48, 184, 46], [180, 51, 184, 49], [181, 14, 185, 12], [181, 20, 185, 18, "expandedHeight"], [181, 34, 185, 32], [181, 37, 185, 35, "faceHeight"], [181, 47, 185, 45], [181, 50, 185, 48], [181, 53, 185, 51], [183, 14, 187, 12], [184, 14, 188, 12], [184, 20, 188, 18, "radiusX"], [184, 27, 188, 25], [184, 30, 188, 28, "Math"], [184, 34, 188, 32], [184, 35, 188, 33, "max"], [184, 38, 188, 36], [184, 39, 188, 37, "expandedWidth"], [184, 52, 188, 50], [184, 55, 188, 53], [184, 56, 188, 54], [184, 58, 188, 56], [184, 60, 188, 58], [184, 61, 188, 59], [185, 14, 189, 12], [185, 20, 189, 18, "radiusY"], [185, 27, 189, 25], [185, 30, 189, 28, "Math"], [185, 34, 189, 32], [185, 35, 189, 33, "max"], [185, 38, 189, 36], [185, 39, 189, 37, "expandedHeight"], [185, 53, 189, 51], [185, 56, 189, 54], [185, 57, 189, 55], [185, 59, 189, 57], [185, 61, 189, 59], [185, 62, 189, 60], [187, 14, 191, 12], [188, 14, 192, 12, "ctx"], [188, 17, 192, 15], [188, 18, 192, 16, "save"], [188, 22, 192, 20], [188, 23, 192, 21], [188, 24, 192, 22], [189, 14, 193, 12, "ctx"], [189, 17, 193, 15], [189, 18, 193, 16, "beginPath"], [189, 27, 193, 25], [189, 28, 193, 26], [189, 29, 193, 27], [190, 14, 194, 12, "ctx"], [190, 17, 194, 15], [190, 18, 194, 16, "ellipse"], [190, 25, 194, 23], [190, 26, 194, 24, "centerX"], [190, 33, 194, 31], [190, 35, 194, 33, "centerY"], [190, 42, 194, 40], [190, 44, 194, 42, "radiusX"], [190, 51, 194, 49], [190, 53, 194, 51, "radiusY"], [190, 60, 194, 58], [190, 62, 194, 60], [190, 63, 194, 61], [190, 65, 194, 63], [190, 66, 194, 64], [190, 68, 194, 66, "Math"], [190, 72, 194, 70], [190, 73, 194, 71, "PI"], [190, 75, 194, 73], [190, 78, 194, 76], [190, 79, 194, 77], [190, 80, 194, 78], [191, 14, 195, 12, "ctx"], [191, 17, 195, 15], [191, 18, 195, 16, "clip"], [191, 22, 195, 20], [191, 23, 195, 21], [191, 24, 195, 22], [192, 14, 196, 12, "ctx"], [192, 17, 196, 15], [192, 18, 196, 16, "filter"], [192, 24, 196, 22], [192, 27, 196, 25], [192, 39, 196, 37], [192, 40, 196, 38], [192, 41, 196, 39], [193, 14, 197, 12, "ctx"], [193, 17, 197, 15], [193, 18, 197, 16, "drawImage"], [193, 27, 197, 25], [193, 28, 197, 26, "video"], [193, 33, 197, 31], [193, 35, 197, 33], [193, 36, 197, 34], [193, 38, 197, 36], [193, 39, 197, 37], [193, 41, 197, 39, "canvas"], [193, 47, 197, 45], [193, 48, 197, 46, "width"], [193, 53, 197, 51], [193, 55, 197, 53, "canvas"], [193, 61, 197, 59], [193, 62, 197, 60, "height"], [193, 68, 197, 66], [193, 69, 197, 67], [194, 14, 198, 12, "ctx"], [194, 17, 198, 15], [194, 18, 198, 16, "restore"], [194, 25, 198, 23], [194, 26, 198, 24], [194, 27, 198, 25], [196, 14, 200, 12], [197, 14, 201, 12], [197, 18, 201, 16, "__DEV__"], [197, 25, 201, 23], [197, 27, 201, 25], [198, 16, 202, 14, "ctx"], [198, 19, 202, 17], [198, 20, 202, 18, "strokeStyle"], [198, 31, 202, 29], [198, 34, 202, 32], [198, 56, 202, 54], [199, 16, 203, 14, "ctx"], [199, 19, 203, 17], [199, 20, 203, 18, "lineWidth"], [199, 29, 203, 27], [199, 32, 203, 30], [199, 33, 203, 31], [200, 16, 204, 14, "ctx"], [200, 19, 204, 17], [200, 20, 204, 18, "strokeRect"], [200, 30, 204, 28], [200, 31, 204, 29, "minX"], [200, 35, 204, 33], [200, 37, 204, 35, "minY"], [200, 41, 204, 39], [200, 43, 204, 41, "faceWidth"], [200, 52, 204, 50], [200, 54, 204, 52, "faceHeight"], [200, 64, 204, 62], [200, 65, 204, 63], [201, 14, 205, 12], [202, 12, 206, 10], [202, 13, 206, 11], [202, 14, 206, 12], [203, 10, 207, 8], [203, 11, 207, 9], [203, 17, 207, 15], [204, 12, 208, 10, "setFaceCount"], [204, 24, 208, 22], [204, 25, 208, 23], [204, 26, 208, 24], [204, 27, 208, 25], [205, 10, 209, 8], [206, 8, 211, 6], [206, 9, 211, 7], [206, 10, 211, 8], [206, 17, 211, 15, "error"], [206, 22, 211, 20], [206, 24, 211, 22], [207, 10, 212, 8, "console"], [207, 17, 212, 15], [207, 18, 212, 16, "error"], [207, 23, 212, 21], [207, 24, 212, 22], [207, 60, 212, 58], [207, 62, 212, 60, "error"], [207, 67, 212, 65], [207, 68, 212, 66], [208, 8, 213, 6], [210, 8, 215, 6], [211, 8, 216, 6], [211, 12, 216, 10, "isDetecting"], [211, 23, 216, 21], [211, 25, 216, 23], [212, 10, 217, 8, "rafRef"], [212, 16, 217, 14], [212, 17, 217, 15, "current"], [212, 24, 217, 22], [212, 27, 217, 25, "requestAnimationFrame"], [212, 48, 217, 46], [212, 49, 217, 47, "detectLoop"], [212, 59, 217, 57], [212, 60, 217, 58], [213, 8, 218, 6], [214, 6, 219, 4], [214, 7, 219, 5], [216, 6, 221, 4], [217, 6, 222, 4], [217, 12, 222, 10, "waitForVideoAndStart"], [217, 32, 222, 30], [217, 35, 222, 33, "waitForVideoAndStart"], [217, 36, 222, 33], [217, 41, 222, 39], [218, 8, 223, 6], [218, 12, 223, 10, "video"], [218, 17, 223, 15], [218, 18, 223, 16, "readyState"], [218, 28, 223, 26], [218, 32, 223, 30], [218, 33, 223, 31], [218, 35, 223, 33], [219, 10, 223, 35], [220, 10, 224, 8, "loadModel"], [220, 19, 224, 17], [220, 20, 224, 18], [220, 21, 224, 19], [221, 8, 225, 6], [221, 9, 225, 7], [221, 15, 225, 13], [222, 10, 226, 8, "video"], [222, 15, 226, 13], [222, 16, 226, 14, "addEventListener"], [222, 32, 226, 30], [222, 33, 226, 31], [222, 45, 226, 43], [222, 47, 226, 45, "loadModel"], [222, 56, 226, 54], [222, 58, 226, 56], [223, 12, 226, 58, "once"], [223, 16, 226, 62], [223, 18, 226, 64], [224, 10, 226, 69], [224, 11, 226, 70], [224, 12, 226, 71], [225, 8, 227, 6], [226, 6, 228, 4], [226, 7, 228, 5], [228, 6, 230, 4], [229, 6, 231, 4, "waitForVideoAndStart"], [229, 26, 231, 24], [229, 27, 231, 25], [229, 28, 231, 26], [231, 6, 233, 4], [232, 6, 234, 4], [232, 13, 234, 11], [232, 19, 234, 17], [233, 8, 235, 6, "isDetecting"], [233, 19, 235, 17], [233, 22, 235, 20], [233, 27, 235, 25], [234, 8, 236, 6], [234, 12, 236, 10, "rafRef"], [234, 18, 236, 16], [234, 19, 236, 17, "current"], [234, 26, 236, 24], [234, 28, 236, 26], [235, 10, 237, 8, "cancelAnimationFrame"], [235, 30, 237, 28], [235, 31, 237, 29, "rafRef"], [235, 37, 237, 35], [235, 38, 237, 36, "current"], [235, 45, 237, 43], [235, 46, 237, 44], [236, 8, 238, 6], [237, 6, 239, 4], [237, 7, 239, 5], [238, 4, 240, 2], [238, 5, 240, 3], [238, 7, 240, 5], [238, 8, 240, 6, "containerId"], [238, 19, 240, 17], [238, 21, 240, 19, "width"], [238, 26, 240, 24], [238, 28, 240, 26, "height"], [238, 34, 240, 32], [238, 35, 240, 33], [238, 36, 240, 34], [239, 4, 242, 2], [239, 24, 243, 4], [239, 28, 243, 4, "_jsxDevRuntime"], [239, 42, 243, 4], [239, 43, 243, 4, "jsxDEV"], [239, 49, 243, 4], [239, 51, 243, 4, "_jsxDevRuntime"], [239, 65, 243, 4], [239, 66, 243, 4, "Fragment"], [239, 74, 243, 4], [240, 6, 243, 4, "children"], [240, 14, 243, 4], [240, 30, 244, 6], [240, 34, 244, 6, "_jsxDevRuntime"], [240, 48, 244, 6], [240, 49, 244, 6, "jsxDEV"], [240, 55, 244, 6], [241, 8, 245, 8, "ref"], [241, 11, 245, 11], [241, 13, 245, 13, "canvasRef"], [241, 22, 245, 23], [242, 8, 246, 8, "style"], [242, 13, 246, 13], [242, 15, 246, 15], [243, 10, 247, 10, "position"], [243, 18, 247, 18], [243, 20, 247, 20], [243, 30, 247, 30], [244, 10, 248, 10, "left"], [244, 14, 248, 14], [244, 16, 248, 16], [244, 17, 248, 17], [245, 10, 249, 10, "top"], [245, 13, 249, 13], [245, 15, 249, 15], [245, 16, 249, 16], [246, 10, 250, 10, "width"], [246, 15, 250, 15], [246, 17, 250, 17], [246, 23, 250, 23], [247, 10, 251, 10, "height"], [247, 16, 251, 16], [247, 18, 251, 18], [247, 24, 251, 24], [248, 10, 252, 10, "pointerEvents"], [248, 23, 252, 23], [248, 25, 252, 25], [248, 31, 252, 31], [249, 10, 253, 10, "zIndex"], [249, 16, 253, 16], [249, 18, 253, 18], [249, 19, 253, 19], [250, 10, 253, 21], [251, 10, 254, 10, "objectFit"], [251, 19, 254, 19], [251, 21, 254, 21], [251, 28, 254, 28], [252, 10, 255, 10, "backgroundColor"], [252, 25, 255, 25], [252, 27, 255, 27], [253, 8, 256, 8], [254, 6, 256, 10], [255, 8, 256, 10, "fileName"], [255, 16, 256, 10], [255, 18, 256, 10, "_jsxFileName"], [255, 30, 256, 10], [256, 8, 256, 10, "lineNumber"], [256, 18, 256, 10], [257, 8, 256, 10, "columnNumber"], [257, 20, 256, 10], [258, 6, 256, 10], [258, 13, 257, 7], [258, 14, 257, 8], [258, 16, 259, 7, "isLoading"], [258, 25, 259, 16], [258, 42, 260, 8], [258, 46, 260, 8, "_jsxDevRuntime"], [258, 60, 260, 8], [258, 61, 260, 8, "jsxDEV"], [258, 67, 260, 8], [259, 8, 260, 13, "style"], [259, 13, 260, 18], [259, 15, 260, 20], [260, 10, 261, 10, "position"], [260, 18, 261, 18], [260, 20, 261, 20], [260, 30, 261, 30], [261, 10, 262, 10, "top"], [261, 13, 262, 13], [261, 15, 262, 15], [261, 17, 262, 17], [262, 10, 263, 10, "left"], [262, 14, 263, 14], [262, 16, 263, 16], [262, 18, 263, 18], [263, 10, 264, 10, "background"], [263, 20, 264, 20], [263, 22, 264, 22], [263, 47, 264, 47], [264, 10, 265, 10, "color"], [264, 15, 265, 15], [264, 17, 265, 17], [264, 24, 265, 24], [265, 10, 266, 10, "padding"], [265, 17, 266, 17], [265, 19, 266, 19], [265, 29, 266, 29], [266, 10, 267, 10, "borderRadius"], [266, 22, 267, 22], [266, 24, 267, 24], [266, 29, 267, 29], [267, 10, 268, 10, "fontSize"], [267, 18, 268, 18], [267, 20, 268, 20], [267, 26, 268, 26], [268, 10, 269, 10, "fontWeight"], [268, 20, 269, 20], [268, 22, 269, 22], [268, 27, 269, 27], [269, 10, 270, 10, "zIndex"], [269, 16, 270, 16], [269, 18, 270, 18], [269, 20, 270, 20], [270, 10, 271, 10, "border"], [270, 16, 271, 16], [270, 18, 271, 18], [271, 8, 272, 8], [271, 9, 272, 10], [272, 8, 272, 10, "children"], [272, 16, 272, 10], [272, 18, 272, 11], [273, 6, 274, 8], [274, 8, 274, 8, "fileName"], [274, 16, 274, 8], [274, 18, 274, 8, "_jsxFileName"], [274, 30, 274, 8], [275, 8, 274, 8, "lineNumber"], [275, 18, 274, 8], [276, 8, 274, 8, "columnNumber"], [276, 20, 274, 8], [277, 6, 274, 8], [277, 13, 274, 13], [277, 14, 275, 7], [277, 16, 276, 7], [277, 17, 276, 8, "isLoading"], [277, 26, 276, 17], [277, 30, 276, 21, "faceCount"], [277, 39, 276, 30], [277, 42, 276, 33], [277, 43, 276, 34], [277, 60, 277, 8], [277, 64, 277, 8, "_jsxDevRuntime"], [277, 78, 277, 8], [277, 79, 277, 8, "jsxDEV"], [277, 85, 277, 8], [278, 8, 277, 13, "style"], [278, 13, 277, 18], [278, 15, 277, 20], [279, 10, 278, 10, "position"], [279, 18, 278, 18], [279, 20, 278, 20], [279, 30, 278, 30], [280, 10, 279, 10, "top"], [280, 13, 279, 13], [280, 15, 279, 15], [280, 17, 279, 17], [281, 10, 280, 10, "left"], [281, 14, 280, 14], [281, 16, 280, 16], [281, 18, 280, 18], [282, 10, 281, 10, "background"], [282, 20, 281, 20], [282, 22, 281, 22], [282, 47, 281, 47], [283, 10, 282, 10, "color"], [283, 15, 282, 15], [283, 17, 282, 17], [283, 24, 282, 24], [284, 10, 283, 10, "padding"], [284, 17, 283, 17], [284, 19, 283, 19], [284, 29, 283, 29], [285, 10, 284, 10, "borderRadius"], [285, 22, 284, 22], [285, 24, 284, 24], [285, 29, 284, 29], [286, 10, 285, 10, "fontSize"], [286, 18, 285, 18], [286, 20, 285, 20], [286, 26, 285, 26], [287, 10, 286, 10, "fontWeight"], [287, 20, 286, 20], [287, 22, 286, 22], [287, 27, 286, 27], [288, 10, 287, 10, "zIndex"], [288, 16, 287, 16], [288, 18, 287, 18], [288, 20, 287, 20], [289, 10, 288, 10, "border"], [289, 16, 288, 16], [289, 18, 288, 18], [290, 8, 289, 8], [290, 9, 289, 10], [291, 8, 289, 10, "children"], [291, 16, 289, 10], [291, 19, 289, 11], [291, 51, 290, 25], [291, 53, 290, 26, "faceCount"], [291, 62, 290, 35], [291, 64, 290, 36], [291, 71, 290, 41], [291, 73, 290, 42, "faceCount"], [291, 82, 290, 51], [291, 85, 290, 54], [291, 86, 290, 55], [291, 89, 290, 58], [291, 92, 290, 61], [291, 95, 290, 64], [291, 97, 290, 66], [292, 6, 290, 66], [293, 8, 290, 66, "fileName"], [293, 16, 290, 66], [293, 18, 290, 66, "_jsxFileName"], [293, 30, 290, 66], [294, 8, 290, 66, "lineNumber"], [294, 18, 290, 66], [295, 8, 290, 66, "columnNumber"], [295, 20, 290, 66], [296, 6, 290, 66], [296, 13, 291, 13], [296, 14, 292, 7], [296, 16, 293, 7], [296, 17, 293, 8, "isLoading"], [296, 26, 293, 17], [296, 30, 293, 21, "faceCount"], [296, 39, 293, 30], [296, 44, 293, 35], [296, 45, 293, 36], [296, 62, 294, 8], [296, 66, 294, 8, "_jsxDevRuntime"], [296, 80, 294, 8], [296, 81, 294, 8, "jsxDEV"], [296, 87, 294, 8], [297, 8, 294, 13, "style"], [297, 13, 294, 18], [297, 15, 294, 20], [298, 10, 295, 10, "position"], [298, 18, 295, 18], [298, 20, 295, 20], [298, 30, 295, 30], [299, 10, 296, 10, "top"], [299, 13, 296, 13], [299, 15, 296, 15], [299, 17, 296, 17], [300, 10, 297, 10, "left"], [300, 14, 297, 14], [300, 16, 297, 16], [300, 18, 297, 18], [301, 10, 298, 10, "background"], [301, 20, 298, 20], [301, 22, 298, 22], [301, 48, 298, 48], [302, 10, 299, 10, "color"], [302, 15, 299, 15], [302, 17, 299, 17], [302, 24, 299, 24], [303, 10, 300, 10, "padding"], [303, 17, 300, 17], [303, 19, 300, 19], [303, 29, 300, 29], [304, 10, 301, 10, "borderRadius"], [304, 22, 301, 22], [304, 24, 301, 24], [304, 29, 301, 29], [305, 10, 302, 10, "fontSize"], [305, 18, 302, 18], [305, 20, 302, 20], [305, 26, 302, 26], [306, 10, 303, 10, "fontWeight"], [306, 20, 303, 20], [306, 22, 303, 22], [306, 27, 303, 27], [307, 10, 304, 10, "zIndex"], [307, 16, 304, 16], [307, 18, 304, 18], [307, 20, 304, 20], [308, 10, 305, 10, "border"], [308, 16, 305, 16], [308, 18, 305, 18], [309, 8, 306, 8], [309, 9, 306, 10], [310, 8, 306, 10, "children"], [310, 16, 306, 10], [310, 18, 306, 11], [311, 6, 308, 8], [312, 8, 308, 8, "fileName"], [312, 16, 308, 8], [312, 18, 308, 8, "_jsxFileName"], [312, 30, 308, 8], [313, 8, 308, 8, "lineNumber"], [313, 18, 308, 8], [314, 8, 308, 8, "columnNumber"], [314, 20, 308, 8], [315, 6, 308, 8], [315, 13, 308, 13], [315, 14, 309, 7], [316, 4, 309, 7], [316, 19, 310, 6], [316, 20, 310, 7], [317, 2, 312, 0], [318, 2, 312, 1, "_s"], [318, 4, 312, 1], [318, 5, 17, 24, "BlazeFaceCanvas"], [318, 20, 17, 39], [319, 2, 17, 39, "_c"], [319, 4, 17, 39], [319, 7, 17, 24, "BlazeFaceCanvas"], [319, 22, 17, 39], [320, 2, 17, 39], [320, 6, 17, 39, "_c"], [320, 8, 17, 39], [321, 2, 17, 39, "$RefreshReg$"], [321, 14, 17, 39], [321, 15, 17, 39, "_c"], [321, 17, 17, 39], [322, 0, 17, 39], [322, 3]], "functionMap": {"names": ["<global>", "BlazeFaceCanvas", "useEffect$argument_0", "updateCanvasSize", "loadScript", "Promise$argument_0", "script.onload", "script.onerror", "loadModel", "detectLoop", "predictions.forEach$argument_0", "waitForVideoAndStart", "<anonymous>"], "mappings": "AAA;eCgB;YCO;6BC8B;KDa;uBEW;yBCC;wBCG,eD;yBEC,gDF;ODE;KFC;sBMG;KNsC;uBOE;8BC0B;WDmD;KPa;iCSG;KTM;WUM;KVK;GDC;CDwE"}}, "type": "js/module"}]}