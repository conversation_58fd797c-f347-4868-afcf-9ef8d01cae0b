{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces\n        const predictions = await model.estimateFaces(tensor, false);\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sophisticated face detection criteria\n          if (analysis.skinRatio > 0.25 && analysis.hasVariation && analysis.brightness > 0.2 && analysis.brightness < 0.8) {\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Get the region to blur\n      const imageData = ctx.getImageData(x, y, width, height);\n      const data = imageData.data;\n\n      // Apply multiple blur effects\n\n      // 1. Heavy pixelation\n      const pixelSize = Math.max(25, Math.min(width, height) / 6);\n      console.log(`[EchoCameraWeb] 🔲 Applying heavy pixelation with size: ${pixelSize}px`);\n      for (let py = 0; py < height; py += pixelSize) {\n        for (let px = 0; px < width; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < height; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < width; dx++) {\n              const index = ((py + dy) * width + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < height; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < width; dx++) {\n                const index = ((py + dy) * width + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // 2. Additional gaussian-like blur\n      console.log(`[EchoCameraWeb] 🌫️ Applying additional blur effect`);\n      for (let i = 0; i < 3; i++) {\n        // Multiple passes\n        applySimpleBlur(data, width, height);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, x, y);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          await loadTensorFlowFaceDetection();\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 780,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 778,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 790,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 791,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 796,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 788,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 787,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 835,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 836,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 845,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 848,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 856,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 878,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 888,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 887,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 846,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 901,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 903,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 904,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 902,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 908,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 909,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 907,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 900,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 914,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 913,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 899,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 898,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 920,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 921,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 919,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 927,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 940,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 942,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 931,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 945,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 811,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 983,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 959,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 958,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 953,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 996,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 997,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1003,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1009,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1005,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 995,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 994,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 989,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 809,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1605, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 68, 132, 66], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [179, 43, 188, 12, "analysis"], [179, 51, 188, 20], [179, 52, 188, 21, "hasVariation"], [179, 64, 188, 33], [179, 68, 189, 12, "analysis"], [179, 76, 189, 20], [179, 77, 189, 21, "brightness"], [179, 87, 189, 31], [179, 90, 189, 34], [179, 93, 189, 37], [179, 97, 190, 12, "analysis"], [179, 105, 190, 20], [179, 106, 190, 21, "brightness"], [179, 116, 190, 31], [179, 119, 190, 34], [179, 122, 190, 37], [179, 124, 190, 39], [180, 12, 192, 10, "faces"], [180, 17, 192, 15], [180, 18, 192, 16, "push"], [180, 22, 192, 20], [180, 23, 192, 21], [181, 14, 193, 12, "boundingBox"], [181, 25, 193, 23], [181, 27, 193, 25], [182, 16, 194, 14, "xCenter"], [182, 23, 194, 21], [182, 25, 194, 23], [182, 26, 194, 24, "x"], [182, 27, 194, 25], [182, 30, 194, 28, "blockSize"], [182, 39, 194, 37], [182, 42, 194, 40], [182, 43, 194, 41], [182, 47, 194, 45, "img"], [182, 50, 194, 48], [182, 51, 194, 49, "width"], [182, 56, 194, 54], [183, 16, 195, 14, "yCenter"], [183, 23, 195, 21], [183, 25, 195, 23], [183, 26, 195, 24, "y"], [183, 27, 195, 25], [183, 30, 195, 28, "blockSize"], [183, 39, 195, 37], [183, 42, 195, 40], [183, 43, 195, 41], [183, 47, 195, 45, "img"], [183, 50, 195, 48], [183, 51, 195, 49, "height"], [183, 57, 195, 55], [184, 16, 196, 14, "width"], [184, 21, 196, 19], [184, 23, 196, 22, "blockSize"], [184, 32, 196, 31], [184, 35, 196, 34], [184, 38, 196, 37], [184, 41, 196, 41, "img"], [184, 44, 196, 44], [184, 45, 196, 45, "width"], [184, 50, 196, 50], [185, 16, 197, 14, "height"], [185, 22, 197, 20], [185, 24, 197, 23, "blockSize"], [185, 33, 197, 32], [185, 36, 197, 35], [185, 39, 197, 38], [185, 42, 197, 42, "img"], [185, 45, 197, 45], [185, 46, 197, 46, "height"], [186, 14, 198, 12], [186, 15, 198, 13], [187, 14, 199, 12, "confidence"], [187, 24, 199, 22], [187, 26, 199, 24, "analysis"], [187, 34, 199, 32], [187, 35, 199, 33, "skinRatio"], [187, 44, 199, 42], [187, 47, 199, 45, "analysis"], [187, 55, 199, 53], [187, 56, 199, 54, "variation"], [188, 12, 200, 10], [188, 13, 200, 11], [188, 14, 200, 12], [189, 12, 202, 10, "console"], [189, 19, 202, 17], [189, 20, 202, 18, "log"], [189, 23, 202, 21], [189, 24, 202, 22], [189, 71, 202, 69, "Math"], [189, 75, 202, 73], [189, 76, 202, 74, "round"], [189, 81, 202, 79], [189, 82, 202, 80, "x"], [189, 83, 202, 81], [189, 84, 202, 82], [189, 89, 202, 87, "Math"], [189, 93, 202, 91], [189, 94, 202, 92, "round"], [189, 99, 202, 97], [189, 100, 202, 98, "y"], [189, 101, 202, 99], [189, 102, 202, 100], [189, 115, 202, 113], [189, 116, 202, 114, "analysis"], [189, 124, 202, 122], [189, 125, 202, 123, "skinRatio"], [189, 134, 202, 132], [189, 137, 202, 135], [189, 140, 202, 138], [189, 142, 202, 140, "toFixed"], [189, 149, 202, 147], [189, 150, 202, 148], [189, 151, 202, 149], [189, 152, 202, 150], [189, 169, 202, 167, "analysis"], [189, 177, 202, 175], [189, 178, 202, 176, "variation"], [189, 187, 202, 185], [189, 188, 202, 186, "toFixed"], [189, 195, 202, 193], [189, 196, 202, 194], [189, 197, 202, 195], [189, 198, 202, 196], [189, 215, 202, 213, "analysis"], [189, 223, 202, 221], [189, 224, 202, 222, "brightness"], [189, 234, 202, 232], [189, 235, 202, 233, "toFixed"], [189, 242, 202, 240], [189, 243, 202, 241], [189, 244, 202, 242], [189, 245, 202, 243], [189, 247, 202, 245], [189, 248, 202, 246], [190, 10, 203, 8], [191, 8, 204, 6], [192, 6, 205, 4], [194, 6, 207, 4], [195, 6, 208, 4, "faces"], [195, 11, 208, 9], [195, 12, 208, 10, "sort"], [195, 16, 208, 14], [195, 17, 208, 15], [195, 18, 208, 16, "a"], [195, 19, 208, 17], [195, 21, 208, 19, "b"], [195, 22, 208, 20], [195, 27, 208, 25], [195, 28, 208, 26, "b"], [195, 29, 208, 27], [195, 30, 208, 28, "confidence"], [195, 40, 208, 38], [195, 44, 208, 42], [195, 45, 208, 43], [195, 50, 208, 48, "a"], [195, 51, 208, 49], [195, 52, 208, 50, "confidence"], [195, 62, 208, 60], [195, 66, 208, 64], [195, 67, 208, 65], [195, 68, 208, 66], [195, 69, 208, 67], [196, 6, 209, 4], [196, 12, 209, 10, "mergedFaces"], [196, 23, 209, 21], [196, 26, 209, 24, "mergeFaceDetections"], [196, 45, 209, 43], [196, 46, 209, 44, "faces"], [196, 51, 209, 49], [196, 52, 209, 50], [197, 6, 211, 4, "console"], [197, 13, 211, 11], [197, 14, 211, 12, "log"], [197, 17, 211, 15], [197, 18, 211, 16], [197, 61, 211, 59, "faces"], [197, 66, 211, 64], [197, 67, 211, 65, "length"], [197, 73, 211, 71], [197, 90, 211, 88, "mergedFaces"], [197, 101, 211, 99], [197, 102, 211, 100, "length"], [197, 108, 211, 106], [197, 123, 211, 121], [197, 124, 211, 122], [198, 6, 212, 4], [198, 13, 212, 11, "mergedFaces"], [198, 24, 212, 22], [198, 25, 212, 23, "slice"], [198, 30, 212, 28], [198, 31, 212, 29], [198, 32, 212, 30], [198, 34, 212, 32], [198, 35, 212, 33], [198, 36, 212, 34], [198, 37, 212, 35], [198, 38, 212, 36], [199, 4, 213, 2], [199, 5, 213, 3], [200, 4, 215, 2], [200, 10, 215, 8, "analyzeRegionForFace"], [200, 30, 215, 28], [200, 33, 215, 31, "analyzeRegionForFace"], [200, 34, 215, 32, "data"], [200, 38, 215, 55], [200, 40, 215, 57, "startX"], [200, 46, 215, 71], [200, 48, 215, 73, "startY"], [200, 54, 215, 87], [200, 56, 215, 89, "size"], [200, 60, 215, 101], [200, 62, 215, 103, "imageWidth"], [200, 72, 215, 121], [200, 74, 215, 123, "imageHeight"], [200, 85, 215, 142], [200, 90, 215, 147], [201, 6, 216, 4], [201, 10, 216, 8, "skinPixels"], [201, 20, 216, 18], [201, 23, 216, 21], [201, 24, 216, 22], [202, 6, 217, 4], [202, 10, 217, 8, "totalPixels"], [202, 21, 217, 19], [202, 24, 217, 22], [202, 25, 217, 23], [203, 6, 218, 4], [203, 10, 218, 8, "totalBrightness"], [203, 25, 218, 23], [203, 28, 218, 26], [203, 29, 218, 27], [204, 6, 219, 4], [204, 10, 219, 8, "colorVariations"], [204, 25, 219, 23], [204, 28, 219, 26], [204, 29, 219, 27], [205, 6, 220, 4], [205, 10, 220, 8, "prevR"], [205, 15, 220, 13], [205, 18, 220, 16], [205, 19, 220, 17], [206, 8, 220, 19, "prevG"], [206, 13, 220, 24], [206, 16, 220, 27], [206, 17, 220, 28], [207, 8, 220, 30, "prevB"], [207, 13, 220, 35], [207, 16, 220, 38], [207, 17, 220, 39], [208, 6, 222, 4], [208, 11, 222, 9], [208, 15, 222, 13, "y"], [208, 16, 222, 14], [208, 19, 222, 17, "startY"], [208, 25, 222, 23], [208, 27, 222, 25, "y"], [208, 28, 222, 26], [208, 31, 222, 29, "startY"], [208, 37, 222, 35], [208, 40, 222, 38, "size"], [208, 44, 222, 42], [208, 48, 222, 46, "y"], [208, 49, 222, 47], [208, 52, 222, 50, "imageHeight"], [208, 63, 222, 61], [208, 65, 222, 63, "y"], [208, 66, 222, 64], [208, 68, 222, 66], [208, 70, 222, 68], [209, 8, 223, 6], [209, 13, 223, 11], [209, 17, 223, 15, "x"], [209, 18, 223, 16], [209, 21, 223, 19, "startX"], [209, 27, 223, 25], [209, 29, 223, 27, "x"], [209, 30, 223, 28], [209, 33, 223, 31, "startX"], [209, 39, 223, 37], [209, 42, 223, 40, "size"], [209, 46, 223, 44], [209, 50, 223, 48, "x"], [209, 51, 223, 49], [209, 54, 223, 52, "imageWidth"], [209, 64, 223, 62], [209, 66, 223, 64, "x"], [209, 67, 223, 65], [209, 69, 223, 67], [209, 71, 223, 69], [210, 10, 224, 8], [210, 16, 224, 14, "index"], [210, 21, 224, 19], [210, 24, 224, 22], [210, 25, 224, 23, "y"], [210, 26, 224, 24], [210, 29, 224, 27, "imageWidth"], [210, 39, 224, 37], [210, 42, 224, 40, "x"], [210, 43, 224, 41], [210, 47, 224, 45], [210, 48, 224, 46], [211, 10, 225, 8], [211, 16, 225, 14, "r"], [211, 17, 225, 15], [211, 20, 225, 18, "data"], [211, 24, 225, 22], [211, 25, 225, 23, "index"], [211, 30, 225, 28], [211, 31, 225, 29], [212, 10, 226, 8], [212, 16, 226, 14, "g"], [212, 17, 226, 15], [212, 20, 226, 18, "data"], [212, 24, 226, 22], [212, 25, 226, 23, "index"], [212, 30, 226, 28], [212, 33, 226, 31], [212, 34, 226, 32], [212, 35, 226, 33], [213, 10, 227, 8], [213, 16, 227, 14, "b"], [213, 17, 227, 15], [213, 20, 227, 18, "data"], [213, 24, 227, 22], [213, 25, 227, 23, "index"], [213, 30, 227, 28], [213, 33, 227, 31], [213, 34, 227, 32], [213, 35, 227, 33], [215, 10, 229, 8], [216, 10, 230, 8], [216, 14, 230, 12, "isSkinTone"], [216, 24, 230, 22], [216, 25, 230, 23, "r"], [216, 26, 230, 24], [216, 28, 230, 26, "g"], [216, 29, 230, 27], [216, 31, 230, 29, "b"], [216, 32, 230, 30], [216, 33, 230, 31], [216, 35, 230, 33], [217, 12, 231, 10, "skinPixels"], [217, 22, 231, 20], [217, 24, 231, 22], [218, 10, 232, 8], [220, 10, 234, 8], [221, 10, 235, 8], [221, 16, 235, 14, "brightness"], [221, 26, 235, 24], [221, 29, 235, 27], [221, 30, 235, 28, "r"], [221, 31, 235, 29], [221, 34, 235, 32, "g"], [221, 35, 235, 33], [221, 38, 235, 36, "b"], [221, 39, 235, 37], [221, 44, 235, 42], [221, 45, 235, 43], [221, 48, 235, 46], [221, 51, 235, 49], [221, 52, 235, 50], [222, 10, 236, 8, "totalBrightness"], [222, 25, 236, 23], [222, 29, 236, 27, "brightness"], [222, 39, 236, 37], [224, 10, 238, 8], [225, 10, 239, 8], [225, 14, 239, 12, "totalPixels"], [225, 25, 239, 23], [225, 28, 239, 26], [225, 29, 239, 27], [225, 31, 239, 29], [226, 12, 240, 10], [226, 18, 240, 16, "colorDiff"], [226, 27, 240, 25], [226, 30, 240, 28, "Math"], [226, 34, 240, 32], [226, 35, 240, 33, "abs"], [226, 38, 240, 36], [226, 39, 240, 37, "r"], [226, 40, 240, 38], [226, 43, 240, 41, "prevR"], [226, 48, 240, 46], [226, 49, 240, 47], [226, 52, 240, 50, "Math"], [226, 56, 240, 54], [226, 57, 240, 55, "abs"], [226, 60, 240, 58], [226, 61, 240, 59, "g"], [226, 62, 240, 60], [226, 65, 240, 63, "prevG"], [226, 70, 240, 68], [226, 71, 240, 69], [226, 74, 240, 72, "Math"], [226, 78, 240, 76], [226, 79, 240, 77, "abs"], [226, 82, 240, 80], [226, 83, 240, 81, "b"], [226, 84, 240, 82], [226, 87, 240, 85, "prevB"], [226, 92, 240, 90], [226, 93, 240, 91], [227, 12, 241, 10], [227, 16, 241, 14, "colorDiff"], [227, 25, 241, 23], [227, 28, 241, 26], [227, 30, 241, 28], [227, 32, 241, 30], [228, 14, 241, 32], [229, 14, 242, 12, "colorVariations"], [229, 29, 242, 27], [229, 31, 242, 29], [230, 12, 243, 10], [231, 10, 244, 8], [232, 10, 246, 8, "prevR"], [232, 15, 246, 13], [232, 18, 246, 16, "r"], [232, 19, 246, 17], [233, 10, 246, 19, "prevG"], [233, 15, 246, 24], [233, 18, 246, 27, "g"], [233, 19, 246, 28], [234, 10, 246, 30, "prevB"], [234, 15, 246, 35], [234, 18, 246, 38, "b"], [234, 19, 246, 39], [235, 10, 247, 8, "totalPixels"], [235, 21, 247, 19], [235, 23, 247, 21], [236, 8, 248, 6], [237, 6, 249, 4], [238, 6, 251, 4], [238, 13, 251, 11], [239, 8, 252, 6, "skinRatio"], [239, 17, 252, 15], [239, 19, 252, 17, "skinPixels"], [239, 29, 252, 27], [239, 32, 252, 30, "totalPixels"], [239, 43, 252, 41], [240, 8, 253, 6, "brightness"], [240, 18, 253, 16], [240, 20, 253, 18, "totalBrightness"], [240, 35, 253, 33], [240, 38, 253, 36, "totalPixels"], [240, 49, 253, 47], [241, 8, 254, 6, "variation"], [241, 17, 254, 15], [241, 19, 254, 17, "colorVariations"], [241, 34, 254, 32], [241, 37, 254, 35, "totalPixels"], [241, 48, 254, 46], [242, 8, 255, 6, "hasVariation"], [242, 20, 255, 18], [242, 22, 255, 20, "colorVariations"], [242, 37, 255, 35], [242, 40, 255, 38, "totalPixels"], [242, 51, 255, 49], [242, 54, 255, 52], [242, 57, 255, 55], [242, 58, 255, 56], [243, 6, 256, 4], [243, 7, 256, 5], [244, 4, 257, 2], [244, 5, 257, 3], [245, 4, 259, 2], [245, 10, 259, 8, "isSkinTone"], [245, 20, 259, 18], [245, 23, 259, 21, "isSkinTone"], [245, 24, 259, 22, "r"], [245, 25, 259, 31], [245, 27, 259, 33, "g"], [245, 28, 259, 42], [245, 30, 259, 44, "b"], [245, 31, 259, 53], [245, 36, 259, 58], [246, 6, 260, 4], [247, 6, 261, 4], [247, 13, 262, 6, "r"], [247, 14, 262, 7], [247, 17, 262, 10], [247, 19, 262, 12], [247, 23, 262, 16, "g"], [247, 24, 262, 17], [247, 27, 262, 20], [247, 29, 262, 22], [247, 33, 262, 26, "b"], [247, 34, 262, 27], [247, 37, 262, 30], [247, 39, 262, 32], [247, 43, 263, 6, "r"], [247, 44, 263, 7], [247, 47, 263, 10, "g"], [247, 48, 263, 11], [247, 52, 263, 15, "r"], [247, 53, 263, 16], [247, 56, 263, 19, "b"], [247, 57, 263, 20], [247, 61, 264, 6, "Math"], [247, 65, 264, 10], [247, 66, 264, 11, "abs"], [247, 69, 264, 14], [247, 70, 264, 15, "r"], [247, 71, 264, 16], [247, 74, 264, 19, "g"], [247, 75, 264, 20], [247, 76, 264, 21], [247, 79, 264, 24], [247, 81, 264, 26], [247, 85, 265, 6, "Math"], [247, 89, 265, 10], [247, 90, 265, 11, "max"], [247, 93, 265, 14], [247, 94, 265, 15, "r"], [247, 95, 265, 16], [247, 97, 265, 18, "g"], [247, 98, 265, 19], [247, 100, 265, 21, "b"], [247, 101, 265, 22], [247, 102, 265, 23], [247, 105, 265, 26, "Math"], [247, 109, 265, 30], [247, 110, 265, 31, "min"], [247, 113, 265, 34], [247, 114, 265, 35, "r"], [247, 115, 265, 36], [247, 117, 265, 38, "g"], [247, 118, 265, 39], [247, 120, 265, 41, "b"], [247, 121, 265, 42], [247, 122, 265, 43], [247, 125, 265, 46], [247, 127, 265, 48], [248, 4, 267, 2], [248, 5, 267, 3], [249, 4, 269, 2], [249, 10, 269, 8, "mergeFaceDetections"], [249, 29, 269, 27], [249, 32, 269, 31, "faces"], [249, 37, 269, 43], [249, 41, 269, 48], [250, 6, 270, 4], [250, 10, 270, 8, "faces"], [250, 15, 270, 13], [250, 16, 270, 14, "length"], [250, 22, 270, 20], [250, 26, 270, 24], [250, 27, 270, 25], [250, 29, 270, 27], [250, 36, 270, 34, "faces"], [250, 41, 270, 39], [251, 6, 272, 4], [251, 12, 272, 10, "merged"], [251, 18, 272, 16], [251, 21, 272, 19], [251, 23, 272, 21], [252, 6, 273, 4], [252, 12, 273, 10, "used"], [252, 16, 273, 14], [252, 19, 273, 17], [252, 23, 273, 21, "Set"], [252, 26, 273, 24], [252, 27, 273, 25], [252, 28, 273, 26], [253, 6, 275, 4], [253, 11, 275, 9], [253, 15, 275, 13, "i"], [253, 16, 275, 14], [253, 19, 275, 17], [253, 20, 275, 18], [253, 22, 275, 20, "i"], [253, 23, 275, 21], [253, 26, 275, 24, "faces"], [253, 31, 275, 29], [253, 32, 275, 30, "length"], [253, 38, 275, 36], [253, 40, 275, 38, "i"], [253, 41, 275, 39], [253, 43, 275, 41], [253, 45, 275, 43], [254, 8, 276, 6], [254, 12, 276, 10, "used"], [254, 16, 276, 14], [254, 17, 276, 15, "has"], [254, 20, 276, 18], [254, 21, 276, 19, "i"], [254, 22, 276, 20], [254, 23, 276, 21], [254, 25, 276, 23], [255, 8, 278, 6], [255, 12, 278, 10, "currentFace"], [255, 23, 278, 21], [255, 26, 278, 24, "faces"], [255, 31, 278, 29], [255, 32, 278, 30, "i"], [255, 33, 278, 31], [255, 34, 278, 32], [256, 8, 279, 6, "used"], [256, 12, 279, 10], [256, 13, 279, 11, "add"], [256, 16, 279, 14], [256, 17, 279, 15, "i"], [256, 18, 279, 16], [256, 19, 279, 17], [258, 8, 281, 6], [259, 8, 282, 6], [259, 13, 282, 11], [259, 17, 282, 15, "j"], [259, 18, 282, 16], [259, 21, 282, 19, "i"], [259, 22, 282, 20], [259, 25, 282, 23], [259, 26, 282, 24], [259, 28, 282, 26, "j"], [259, 29, 282, 27], [259, 32, 282, 30, "faces"], [259, 37, 282, 35], [259, 38, 282, 36, "length"], [259, 44, 282, 42], [259, 46, 282, 44, "j"], [259, 47, 282, 45], [259, 49, 282, 47], [259, 51, 282, 49], [260, 10, 283, 8], [260, 14, 283, 12, "used"], [260, 18, 283, 16], [260, 19, 283, 17, "has"], [260, 22, 283, 20], [260, 23, 283, 21, "j"], [260, 24, 283, 22], [260, 25, 283, 23], [260, 27, 283, 25], [261, 10, 285, 8], [261, 16, 285, 14, "overlap"], [261, 23, 285, 21], [261, 26, 285, 24, "calculateOverlap"], [261, 42, 285, 40], [261, 43, 285, 41, "currentFace"], [261, 54, 285, 52], [261, 55, 285, 53, "boundingBox"], [261, 66, 285, 64], [261, 68, 285, 66, "faces"], [261, 73, 285, 71], [261, 74, 285, 72, "j"], [261, 75, 285, 73], [261, 76, 285, 74], [261, 77, 285, 75, "boundingBox"], [261, 88, 285, 86], [261, 89, 285, 87], [262, 10, 286, 8], [262, 14, 286, 12, "overlap"], [262, 21, 286, 19], [262, 24, 286, 22], [262, 27, 286, 25], [262, 29, 286, 27], [263, 12, 286, 29], [264, 12, 287, 10], [265, 12, 288, 10, "currentFace"], [265, 23, 288, 21], [265, 26, 288, 24, "mergeTwoFaces"], [265, 39, 288, 37], [265, 40, 288, 38, "currentFace"], [265, 51, 288, 49], [265, 53, 288, 51, "faces"], [265, 58, 288, 56], [265, 59, 288, 57, "j"], [265, 60, 288, 58], [265, 61, 288, 59], [265, 62, 288, 60], [266, 12, 289, 10, "used"], [266, 16, 289, 14], [266, 17, 289, 15, "add"], [266, 20, 289, 18], [266, 21, 289, 19, "j"], [266, 22, 289, 20], [266, 23, 289, 21], [267, 10, 290, 8], [268, 8, 291, 6], [269, 8, 293, 6, "merged"], [269, 14, 293, 12], [269, 15, 293, 13, "push"], [269, 19, 293, 17], [269, 20, 293, 18, "currentFace"], [269, 31, 293, 29], [269, 32, 293, 30], [270, 6, 294, 4], [271, 6, 296, 4], [271, 13, 296, 11, "merged"], [271, 19, 296, 17], [272, 4, 297, 2], [272, 5, 297, 3], [273, 4, 299, 2], [273, 10, 299, 8, "calculateOverlap"], [273, 26, 299, 24], [273, 29, 299, 27, "calculateOverlap"], [273, 30, 299, 28, "box1"], [273, 34, 299, 37], [273, 36, 299, 39, "box2"], [273, 40, 299, 48], [273, 45, 299, 53], [274, 6, 300, 4], [274, 12, 300, 10, "x1"], [274, 14, 300, 12], [274, 17, 300, 15, "Math"], [274, 21, 300, 19], [274, 22, 300, 20, "max"], [274, 25, 300, 23], [274, 26, 300, 24, "box1"], [274, 30, 300, 28], [274, 31, 300, 29, "xCenter"], [274, 38, 300, 36], [274, 41, 300, 39, "box1"], [274, 45, 300, 43], [274, 46, 300, 44, "width"], [274, 51, 300, 49], [274, 54, 300, 50], [274, 55, 300, 51], [274, 57, 300, 53, "box2"], [274, 61, 300, 57], [274, 62, 300, 58, "xCenter"], [274, 69, 300, 65], [274, 72, 300, 68, "box2"], [274, 76, 300, 72], [274, 77, 300, 73, "width"], [274, 82, 300, 78], [274, 85, 300, 79], [274, 86, 300, 80], [274, 87, 300, 81], [275, 6, 301, 4], [275, 12, 301, 10, "y1"], [275, 14, 301, 12], [275, 17, 301, 15, "Math"], [275, 21, 301, 19], [275, 22, 301, 20, "max"], [275, 25, 301, 23], [275, 26, 301, 24, "box1"], [275, 30, 301, 28], [275, 31, 301, 29, "yCenter"], [275, 38, 301, 36], [275, 41, 301, 39, "box1"], [275, 45, 301, 43], [275, 46, 301, 44, "height"], [275, 52, 301, 50], [275, 55, 301, 51], [275, 56, 301, 52], [275, 58, 301, 54, "box2"], [275, 62, 301, 58], [275, 63, 301, 59, "yCenter"], [275, 70, 301, 66], [275, 73, 301, 69, "box2"], [275, 77, 301, 73], [275, 78, 301, 74, "height"], [275, 84, 301, 80], [275, 87, 301, 81], [275, 88, 301, 82], [275, 89, 301, 83], [276, 6, 302, 4], [276, 12, 302, 10, "x2"], [276, 14, 302, 12], [276, 17, 302, 15, "Math"], [276, 21, 302, 19], [276, 22, 302, 20, "min"], [276, 25, 302, 23], [276, 26, 302, 24, "box1"], [276, 30, 302, 28], [276, 31, 302, 29, "xCenter"], [276, 38, 302, 36], [276, 41, 302, 39, "box1"], [276, 45, 302, 43], [276, 46, 302, 44, "width"], [276, 51, 302, 49], [276, 54, 302, 50], [276, 55, 302, 51], [276, 57, 302, 53, "box2"], [276, 61, 302, 57], [276, 62, 302, 58, "xCenter"], [276, 69, 302, 65], [276, 72, 302, 68, "box2"], [276, 76, 302, 72], [276, 77, 302, 73, "width"], [276, 82, 302, 78], [276, 85, 302, 79], [276, 86, 302, 80], [276, 87, 302, 81], [277, 6, 303, 4], [277, 12, 303, 10, "y2"], [277, 14, 303, 12], [277, 17, 303, 15, "Math"], [277, 21, 303, 19], [277, 22, 303, 20, "min"], [277, 25, 303, 23], [277, 26, 303, 24, "box1"], [277, 30, 303, 28], [277, 31, 303, 29, "yCenter"], [277, 38, 303, 36], [277, 41, 303, 39, "box1"], [277, 45, 303, 43], [277, 46, 303, 44, "height"], [277, 52, 303, 50], [277, 55, 303, 51], [277, 56, 303, 52], [277, 58, 303, 54, "box2"], [277, 62, 303, 58], [277, 63, 303, 59, "yCenter"], [277, 70, 303, 66], [277, 73, 303, 69, "box2"], [277, 77, 303, 73], [277, 78, 303, 74, "height"], [277, 84, 303, 80], [277, 87, 303, 81], [277, 88, 303, 82], [277, 89, 303, 83], [278, 6, 305, 4], [278, 10, 305, 8, "x2"], [278, 12, 305, 10], [278, 16, 305, 14, "x1"], [278, 18, 305, 16], [278, 22, 305, 20, "y2"], [278, 24, 305, 22], [278, 28, 305, 26, "y1"], [278, 30, 305, 28], [278, 32, 305, 30], [278, 39, 305, 37], [278, 40, 305, 38], [279, 6, 307, 4], [279, 12, 307, 10, "overlapArea"], [279, 23, 307, 21], [279, 26, 307, 24], [279, 27, 307, 25, "x2"], [279, 29, 307, 27], [279, 32, 307, 30, "x1"], [279, 34, 307, 32], [279, 39, 307, 37, "y2"], [279, 41, 307, 39], [279, 44, 307, 42, "y1"], [279, 46, 307, 44], [279, 47, 307, 45], [280, 6, 308, 4], [280, 12, 308, 10, "box1Area"], [280, 20, 308, 18], [280, 23, 308, 21, "box1"], [280, 27, 308, 25], [280, 28, 308, 26, "width"], [280, 33, 308, 31], [280, 36, 308, 34, "box1"], [280, 40, 308, 38], [280, 41, 308, 39, "height"], [280, 47, 308, 45], [281, 6, 309, 4], [281, 12, 309, 10, "box2Area"], [281, 20, 309, 18], [281, 23, 309, 21, "box2"], [281, 27, 309, 25], [281, 28, 309, 26, "width"], [281, 33, 309, 31], [281, 36, 309, 34, "box2"], [281, 40, 309, 38], [281, 41, 309, 39, "height"], [281, 47, 309, 45], [282, 6, 311, 4], [282, 13, 311, 11, "overlapArea"], [282, 24, 311, 22], [282, 27, 311, 25, "Math"], [282, 31, 311, 29], [282, 32, 311, 30, "min"], [282, 35, 311, 33], [282, 36, 311, 34, "box1Area"], [282, 44, 311, 42], [282, 46, 311, 44, "box2Area"], [282, 54, 311, 52], [282, 55, 311, 53], [283, 4, 312, 2], [283, 5, 312, 3], [284, 4, 314, 2], [284, 10, 314, 8, "mergeTwoFaces"], [284, 23, 314, 21], [284, 26, 314, 24, "mergeTwoFaces"], [284, 27, 314, 25, "face1"], [284, 32, 314, 35], [284, 34, 314, 37, "face2"], [284, 39, 314, 47], [284, 44, 314, 52], [285, 6, 315, 4], [285, 12, 315, 10, "box1"], [285, 16, 315, 14], [285, 19, 315, 17, "face1"], [285, 24, 315, 22], [285, 25, 315, 23, "boundingBox"], [285, 36, 315, 34], [286, 6, 316, 4], [286, 12, 316, 10, "box2"], [286, 16, 316, 14], [286, 19, 316, 17, "face2"], [286, 24, 316, 22], [286, 25, 316, 23, "boundingBox"], [286, 36, 316, 34], [287, 6, 318, 4], [287, 12, 318, 10, "left"], [287, 16, 318, 14], [287, 19, 318, 17, "Math"], [287, 23, 318, 21], [287, 24, 318, 22, "min"], [287, 27, 318, 25], [287, 28, 318, 26, "box1"], [287, 32, 318, 30], [287, 33, 318, 31, "xCenter"], [287, 40, 318, 38], [287, 43, 318, 41, "box1"], [287, 47, 318, 45], [287, 48, 318, 46, "width"], [287, 53, 318, 51], [287, 56, 318, 52], [287, 57, 318, 53], [287, 59, 318, 55, "box2"], [287, 63, 318, 59], [287, 64, 318, 60, "xCenter"], [287, 71, 318, 67], [287, 74, 318, 70, "box2"], [287, 78, 318, 74], [287, 79, 318, 75, "width"], [287, 84, 318, 80], [287, 87, 318, 81], [287, 88, 318, 82], [287, 89, 318, 83], [288, 6, 319, 4], [288, 12, 319, 10, "right"], [288, 17, 319, 15], [288, 20, 319, 18, "Math"], [288, 24, 319, 22], [288, 25, 319, 23, "max"], [288, 28, 319, 26], [288, 29, 319, 27, "box1"], [288, 33, 319, 31], [288, 34, 319, 32, "xCenter"], [288, 41, 319, 39], [288, 44, 319, 42, "box1"], [288, 48, 319, 46], [288, 49, 319, 47, "width"], [288, 54, 319, 52], [288, 57, 319, 53], [288, 58, 319, 54], [288, 60, 319, 56, "box2"], [288, 64, 319, 60], [288, 65, 319, 61, "xCenter"], [288, 72, 319, 68], [288, 75, 319, 71, "box2"], [288, 79, 319, 75], [288, 80, 319, 76, "width"], [288, 85, 319, 81], [288, 88, 319, 82], [288, 89, 319, 83], [288, 90, 319, 84], [289, 6, 320, 4], [289, 12, 320, 10, "top"], [289, 15, 320, 13], [289, 18, 320, 16, "Math"], [289, 22, 320, 20], [289, 23, 320, 21, "min"], [289, 26, 320, 24], [289, 27, 320, 25, "box1"], [289, 31, 320, 29], [289, 32, 320, 30, "yCenter"], [289, 39, 320, 37], [289, 42, 320, 40, "box1"], [289, 46, 320, 44], [289, 47, 320, 45, "height"], [289, 53, 320, 51], [289, 56, 320, 52], [289, 57, 320, 53], [289, 59, 320, 55, "box2"], [289, 63, 320, 59], [289, 64, 320, 60, "yCenter"], [289, 71, 320, 67], [289, 74, 320, 70, "box2"], [289, 78, 320, 74], [289, 79, 320, 75, "height"], [289, 85, 320, 81], [289, 88, 320, 82], [289, 89, 320, 83], [289, 90, 320, 84], [290, 6, 321, 4], [290, 12, 321, 10, "bottom"], [290, 18, 321, 16], [290, 21, 321, 19, "Math"], [290, 25, 321, 23], [290, 26, 321, 24, "max"], [290, 29, 321, 27], [290, 30, 321, 28, "box1"], [290, 34, 321, 32], [290, 35, 321, 33, "yCenter"], [290, 42, 321, 40], [290, 45, 321, 43, "box1"], [290, 49, 321, 47], [290, 50, 321, 48, "height"], [290, 56, 321, 54], [290, 59, 321, 55], [290, 60, 321, 56], [290, 62, 321, 58, "box2"], [290, 66, 321, 62], [290, 67, 321, 63, "yCenter"], [290, 74, 321, 70], [290, 77, 321, 73, "box2"], [290, 81, 321, 77], [290, 82, 321, 78, "height"], [290, 88, 321, 84], [290, 91, 321, 85], [290, 92, 321, 86], [290, 93, 321, 87], [291, 6, 323, 4], [291, 13, 323, 11], [292, 8, 324, 6, "boundingBox"], [292, 19, 324, 17], [292, 21, 324, 19], [293, 10, 325, 8, "xCenter"], [293, 17, 325, 15], [293, 19, 325, 17], [293, 20, 325, 18, "left"], [293, 24, 325, 22], [293, 27, 325, 25, "right"], [293, 32, 325, 30], [293, 36, 325, 34], [293, 37, 325, 35], [294, 10, 326, 8, "yCenter"], [294, 17, 326, 15], [294, 19, 326, 17], [294, 20, 326, 18, "top"], [294, 23, 326, 21], [294, 26, 326, 24, "bottom"], [294, 32, 326, 30], [294, 36, 326, 34], [294, 37, 326, 35], [295, 10, 327, 8, "width"], [295, 15, 327, 13], [295, 17, 327, 15, "right"], [295, 22, 327, 20], [295, 25, 327, 23, "left"], [295, 29, 327, 27], [296, 10, 328, 8, "height"], [296, 16, 328, 14], [296, 18, 328, 16, "bottom"], [296, 24, 328, 22], [296, 27, 328, 25, "top"], [297, 8, 329, 6], [298, 6, 330, 4], [298, 7, 330, 5], [299, 4, 331, 2], [299, 5, 331, 3], [301, 4, 333, 2], [302, 4, 334, 2], [302, 10, 334, 8, "applyStrongBlur"], [302, 25, 334, 23], [302, 28, 334, 26, "applyStrongBlur"], [302, 29, 334, 27, "ctx"], [302, 32, 334, 56], [302, 34, 334, 58, "x"], [302, 35, 334, 67], [302, 37, 334, 69, "y"], [302, 38, 334, 78], [302, 40, 334, 80, "width"], [302, 45, 334, 93], [302, 47, 334, 95, "height"], [302, 53, 334, 109], [302, 58, 334, 114], [303, 6, 335, 4], [304, 6, 336, 4], [304, 12, 336, 10, "imageData"], [304, 21, 336, 19], [304, 24, 336, 22, "ctx"], [304, 27, 336, 25], [304, 28, 336, 26, "getImageData"], [304, 40, 336, 38], [304, 41, 336, 39, "x"], [304, 42, 336, 40], [304, 44, 336, 42, "y"], [304, 45, 336, 43], [304, 47, 336, 45, "width"], [304, 52, 336, 50], [304, 54, 336, 52, "height"], [304, 60, 336, 58], [304, 61, 336, 59], [305, 6, 337, 4], [305, 12, 337, 10, "data"], [305, 16, 337, 14], [305, 19, 337, 17, "imageData"], [305, 28, 337, 26], [305, 29, 337, 27, "data"], [305, 33, 337, 31], [307, 6, 339, 4], [309, 6, 341, 4], [310, 6, 342, 4], [310, 12, 342, 10, "pixelSize"], [310, 21, 342, 19], [310, 24, 342, 22, "Math"], [310, 28, 342, 26], [310, 29, 342, 27, "max"], [310, 32, 342, 30], [310, 33, 342, 31], [310, 35, 342, 33], [310, 37, 342, 35, "Math"], [310, 41, 342, 39], [310, 42, 342, 40, "min"], [310, 45, 342, 43], [310, 46, 342, 44, "width"], [310, 51, 342, 49], [310, 53, 342, 51, "height"], [310, 59, 342, 57], [310, 60, 342, 58], [310, 63, 342, 61], [310, 64, 342, 62], [310, 65, 342, 63], [311, 6, 343, 4, "console"], [311, 13, 343, 11], [311, 14, 343, 12, "log"], [311, 17, 343, 15], [311, 18, 343, 16], [311, 77, 343, 75, "pixelSize"], [311, 86, 343, 84], [311, 90, 343, 88], [311, 91, 343, 89], [312, 6, 345, 4], [312, 11, 345, 9], [312, 15, 345, 13, "py"], [312, 17, 345, 15], [312, 20, 345, 18], [312, 21, 345, 19], [312, 23, 345, 21, "py"], [312, 25, 345, 23], [312, 28, 345, 26, "height"], [312, 34, 345, 32], [312, 36, 345, 34, "py"], [312, 38, 345, 36], [312, 42, 345, 40, "pixelSize"], [312, 51, 345, 49], [312, 53, 345, 51], [313, 8, 346, 6], [313, 13, 346, 11], [313, 17, 346, 15, "px"], [313, 19, 346, 17], [313, 22, 346, 20], [313, 23, 346, 21], [313, 25, 346, 23, "px"], [313, 27, 346, 25], [313, 30, 346, 28, "width"], [313, 35, 346, 33], [313, 37, 346, 35, "px"], [313, 39, 346, 37], [313, 43, 346, 41, "pixelSize"], [313, 52, 346, 50], [313, 54, 346, 52], [314, 10, 347, 8], [315, 10, 348, 8], [315, 14, 348, 12, "r"], [315, 15, 348, 13], [315, 18, 348, 16], [315, 19, 348, 17], [316, 12, 348, 19, "g"], [316, 13, 348, 20], [316, 16, 348, 23], [316, 17, 348, 24], [317, 12, 348, 26, "b"], [317, 13, 348, 27], [317, 16, 348, 30], [317, 17, 348, 31], [318, 12, 348, 33, "count"], [318, 17, 348, 38], [318, 20, 348, 41], [318, 21, 348, 42], [319, 10, 350, 8], [319, 15, 350, 13], [319, 19, 350, 17, "dy"], [319, 21, 350, 19], [319, 24, 350, 22], [319, 25, 350, 23], [319, 27, 350, 25, "dy"], [319, 29, 350, 27], [319, 32, 350, 30, "pixelSize"], [319, 41, 350, 39], [319, 45, 350, 43, "py"], [319, 47, 350, 45], [319, 50, 350, 48, "dy"], [319, 52, 350, 50], [319, 55, 350, 53, "height"], [319, 61, 350, 59], [319, 63, 350, 61, "dy"], [319, 65, 350, 63], [319, 67, 350, 65], [319, 69, 350, 67], [320, 12, 351, 10], [320, 17, 351, 15], [320, 21, 351, 19, "dx"], [320, 23, 351, 21], [320, 26, 351, 24], [320, 27, 351, 25], [320, 29, 351, 27, "dx"], [320, 31, 351, 29], [320, 34, 351, 32, "pixelSize"], [320, 43, 351, 41], [320, 47, 351, 45, "px"], [320, 49, 351, 47], [320, 52, 351, 50, "dx"], [320, 54, 351, 52], [320, 57, 351, 55, "width"], [320, 62, 351, 60], [320, 64, 351, 62, "dx"], [320, 66, 351, 64], [320, 68, 351, 66], [320, 70, 351, 68], [321, 14, 352, 12], [321, 20, 352, 18, "index"], [321, 25, 352, 23], [321, 28, 352, 26], [321, 29, 352, 27], [321, 30, 352, 28, "py"], [321, 32, 352, 30], [321, 35, 352, 33, "dy"], [321, 37, 352, 35], [321, 41, 352, 39, "width"], [321, 46, 352, 44], [321, 50, 352, 48, "px"], [321, 52, 352, 50], [321, 55, 352, 53, "dx"], [321, 57, 352, 55], [321, 58, 352, 56], [321, 62, 352, 60], [321, 63, 352, 61], [322, 14, 353, 12, "r"], [322, 15, 353, 13], [322, 19, 353, 17, "data"], [322, 23, 353, 21], [322, 24, 353, 22, "index"], [322, 29, 353, 27], [322, 30, 353, 28], [323, 14, 354, 12, "g"], [323, 15, 354, 13], [323, 19, 354, 17, "data"], [323, 23, 354, 21], [323, 24, 354, 22, "index"], [323, 29, 354, 27], [323, 32, 354, 30], [323, 33, 354, 31], [323, 34, 354, 32], [324, 14, 355, 12, "b"], [324, 15, 355, 13], [324, 19, 355, 17, "data"], [324, 23, 355, 21], [324, 24, 355, 22, "index"], [324, 29, 355, 27], [324, 32, 355, 30], [324, 33, 355, 31], [324, 34, 355, 32], [325, 14, 356, 12, "count"], [325, 19, 356, 17], [325, 21, 356, 19], [326, 12, 357, 10], [327, 10, 358, 8], [328, 10, 360, 8], [328, 14, 360, 12, "count"], [328, 19, 360, 17], [328, 22, 360, 20], [328, 23, 360, 21], [328, 25, 360, 23], [329, 12, 361, 10, "r"], [329, 13, 361, 11], [329, 16, 361, 14, "Math"], [329, 20, 361, 18], [329, 21, 361, 19, "floor"], [329, 26, 361, 24], [329, 27, 361, 25, "r"], [329, 28, 361, 26], [329, 31, 361, 29, "count"], [329, 36, 361, 34], [329, 37, 361, 35], [330, 12, 362, 10, "g"], [330, 13, 362, 11], [330, 16, 362, 14, "Math"], [330, 20, 362, 18], [330, 21, 362, 19, "floor"], [330, 26, 362, 24], [330, 27, 362, 25, "g"], [330, 28, 362, 26], [330, 31, 362, 29, "count"], [330, 36, 362, 34], [330, 37, 362, 35], [331, 12, 363, 10, "b"], [331, 13, 363, 11], [331, 16, 363, 14, "Math"], [331, 20, 363, 18], [331, 21, 363, 19, "floor"], [331, 26, 363, 24], [331, 27, 363, 25, "b"], [331, 28, 363, 26], [331, 31, 363, 29, "count"], [331, 36, 363, 34], [331, 37, 363, 35], [333, 12, 365, 10], [334, 12, 366, 10], [334, 17, 366, 15], [334, 21, 366, 19, "dy"], [334, 23, 366, 21], [334, 26, 366, 24], [334, 27, 366, 25], [334, 29, 366, 27, "dy"], [334, 31, 366, 29], [334, 34, 366, 32, "pixelSize"], [334, 43, 366, 41], [334, 47, 366, 45, "py"], [334, 49, 366, 47], [334, 52, 366, 50, "dy"], [334, 54, 366, 52], [334, 57, 366, 55, "height"], [334, 63, 366, 61], [334, 65, 366, 63, "dy"], [334, 67, 366, 65], [334, 69, 366, 67], [334, 71, 366, 69], [335, 14, 367, 12], [335, 19, 367, 17], [335, 23, 367, 21, "dx"], [335, 25, 367, 23], [335, 28, 367, 26], [335, 29, 367, 27], [335, 31, 367, 29, "dx"], [335, 33, 367, 31], [335, 36, 367, 34, "pixelSize"], [335, 45, 367, 43], [335, 49, 367, 47, "px"], [335, 51, 367, 49], [335, 54, 367, 52, "dx"], [335, 56, 367, 54], [335, 59, 367, 57, "width"], [335, 64, 367, 62], [335, 66, 367, 64, "dx"], [335, 68, 367, 66], [335, 70, 367, 68], [335, 72, 367, 70], [336, 16, 368, 14], [336, 22, 368, 20, "index"], [336, 27, 368, 25], [336, 30, 368, 28], [336, 31, 368, 29], [336, 32, 368, 30, "py"], [336, 34, 368, 32], [336, 37, 368, 35, "dy"], [336, 39, 368, 37], [336, 43, 368, 41, "width"], [336, 48, 368, 46], [336, 52, 368, 50, "px"], [336, 54, 368, 52], [336, 57, 368, 55, "dx"], [336, 59, 368, 57], [336, 60, 368, 58], [336, 64, 368, 62], [336, 65, 368, 63], [337, 16, 369, 14, "data"], [337, 20, 369, 18], [337, 21, 369, 19, "index"], [337, 26, 369, 24], [337, 27, 369, 25], [337, 30, 369, 28, "r"], [337, 31, 369, 29], [338, 16, 370, 14, "data"], [338, 20, 370, 18], [338, 21, 370, 19, "index"], [338, 26, 370, 24], [338, 29, 370, 27], [338, 30, 370, 28], [338, 31, 370, 29], [338, 34, 370, 32, "g"], [338, 35, 370, 33], [339, 16, 371, 14, "data"], [339, 20, 371, 18], [339, 21, 371, 19, "index"], [339, 26, 371, 24], [339, 29, 371, 27], [339, 30, 371, 28], [339, 31, 371, 29], [339, 34, 371, 32, "b"], [339, 35, 371, 33], [340, 16, 372, 14], [341, 14, 373, 12], [342, 12, 374, 10], [343, 10, 375, 8], [344, 8, 376, 6], [345, 6, 377, 4], [347, 6, 379, 4], [348, 6, 380, 4, "console"], [348, 13, 380, 11], [348, 14, 380, 12, "log"], [348, 17, 380, 15], [348, 18, 380, 16], [348, 71, 380, 69], [348, 72, 380, 70], [349, 6, 381, 4], [349, 11, 381, 9], [349, 15, 381, 13, "i"], [349, 16, 381, 14], [349, 19, 381, 17], [349, 20, 381, 18], [349, 22, 381, 20, "i"], [349, 23, 381, 21], [349, 26, 381, 24], [349, 27, 381, 25], [349, 29, 381, 27, "i"], [349, 30, 381, 28], [349, 32, 381, 30], [349, 34, 381, 32], [350, 8, 381, 34], [351, 8, 382, 6, "applySimpleBlur"], [351, 23, 382, 21], [351, 24, 382, 22, "data"], [351, 28, 382, 26], [351, 30, 382, 28, "width"], [351, 35, 382, 33], [351, 37, 382, 35, "height"], [351, 43, 382, 41], [351, 44, 382, 42], [352, 6, 383, 4], [354, 6, 385, 4], [355, 6, 386, 4, "ctx"], [355, 9, 386, 7], [355, 10, 386, 8, "putImageData"], [355, 22, 386, 20], [355, 23, 386, 21, "imageData"], [355, 32, 386, 30], [355, 34, 386, 32, "x"], [355, 35, 386, 33], [355, 37, 386, 35, "y"], [355, 38, 386, 36], [355, 39, 386, 37], [356, 4, 387, 2], [356, 5, 387, 3], [357, 4, 389, 2], [357, 10, 389, 8, "applySimpleBlur"], [357, 25, 389, 23], [357, 28, 389, 26, "applySimpleBlur"], [357, 29, 389, 27, "data"], [357, 33, 389, 50], [357, 35, 389, 52, "width"], [357, 40, 389, 65], [357, 42, 389, 67, "height"], [357, 48, 389, 81], [357, 53, 389, 86], [358, 6, 390, 4], [358, 12, 390, 10, "original"], [358, 20, 390, 18], [358, 23, 390, 21], [358, 27, 390, 25, "Uint8ClampedArray"], [358, 44, 390, 42], [358, 45, 390, 43, "data"], [358, 49, 390, 47], [358, 50, 390, 48], [359, 6, 392, 4], [359, 11, 392, 9], [359, 15, 392, 13, "y"], [359, 16, 392, 14], [359, 19, 392, 17], [359, 20, 392, 18], [359, 22, 392, 20, "y"], [359, 23, 392, 21], [359, 26, 392, 24, "height"], [359, 32, 392, 30], [359, 35, 392, 33], [359, 36, 392, 34], [359, 38, 392, 36, "y"], [359, 39, 392, 37], [359, 41, 392, 39], [359, 43, 392, 41], [360, 8, 393, 6], [360, 13, 393, 11], [360, 17, 393, 15, "x"], [360, 18, 393, 16], [360, 21, 393, 19], [360, 22, 393, 20], [360, 24, 393, 22, "x"], [360, 25, 393, 23], [360, 28, 393, 26, "width"], [360, 33, 393, 31], [360, 36, 393, 34], [360, 37, 393, 35], [360, 39, 393, 37, "x"], [360, 40, 393, 38], [360, 42, 393, 40], [360, 44, 393, 42], [361, 10, 394, 8], [361, 16, 394, 14, "index"], [361, 21, 394, 19], [361, 24, 394, 22], [361, 25, 394, 23, "y"], [361, 26, 394, 24], [361, 29, 394, 27, "width"], [361, 34, 394, 32], [361, 37, 394, 35, "x"], [361, 38, 394, 36], [361, 42, 394, 40], [361, 43, 394, 41], [363, 10, 396, 8], [364, 10, 397, 8], [364, 14, 397, 12, "r"], [364, 15, 397, 13], [364, 18, 397, 16], [364, 19, 397, 17], [365, 12, 397, 19, "g"], [365, 13, 397, 20], [365, 16, 397, 23], [365, 17, 397, 24], [366, 12, 397, 26, "b"], [366, 13, 397, 27], [366, 16, 397, 30], [366, 17, 397, 31], [367, 10, 398, 8], [367, 15, 398, 13], [367, 19, 398, 17, "dy"], [367, 21, 398, 19], [367, 24, 398, 22], [367, 25, 398, 23], [367, 26, 398, 24], [367, 28, 398, 26, "dy"], [367, 30, 398, 28], [367, 34, 398, 32], [367, 35, 398, 33], [367, 37, 398, 35, "dy"], [367, 39, 398, 37], [367, 41, 398, 39], [367, 43, 398, 41], [368, 12, 399, 10], [368, 17, 399, 15], [368, 21, 399, 19, "dx"], [368, 23, 399, 21], [368, 26, 399, 24], [368, 27, 399, 25], [368, 28, 399, 26], [368, 30, 399, 28, "dx"], [368, 32, 399, 30], [368, 36, 399, 34], [368, 37, 399, 35], [368, 39, 399, 37, "dx"], [368, 41, 399, 39], [368, 43, 399, 41], [368, 45, 399, 43], [369, 14, 400, 12], [369, 20, 400, 18, "neighborIndex"], [369, 33, 400, 31], [369, 36, 400, 34], [369, 37, 400, 35], [369, 38, 400, 36, "y"], [369, 39, 400, 37], [369, 42, 400, 40, "dy"], [369, 44, 400, 42], [369, 48, 400, 46, "width"], [369, 53, 400, 51], [369, 57, 400, 55, "x"], [369, 58, 400, 56], [369, 61, 400, 59, "dx"], [369, 63, 400, 61], [369, 64, 400, 62], [369, 68, 400, 66], [369, 69, 400, 67], [370, 14, 401, 12, "r"], [370, 15, 401, 13], [370, 19, 401, 17, "original"], [370, 27, 401, 25], [370, 28, 401, 26, "neighborIndex"], [370, 41, 401, 39], [370, 42, 401, 40], [371, 14, 402, 12, "g"], [371, 15, 402, 13], [371, 19, 402, 17, "original"], [371, 27, 402, 25], [371, 28, 402, 26, "neighborIndex"], [371, 41, 402, 39], [371, 44, 402, 42], [371, 45, 402, 43], [371, 46, 402, 44], [372, 14, 403, 12, "b"], [372, 15, 403, 13], [372, 19, 403, 17, "original"], [372, 27, 403, 25], [372, 28, 403, 26, "neighborIndex"], [372, 41, 403, 39], [372, 44, 403, 42], [372, 45, 403, 43], [372, 46, 403, 44], [373, 12, 404, 10], [374, 10, 405, 8], [375, 10, 407, 8, "data"], [375, 14, 407, 12], [375, 15, 407, 13, "index"], [375, 20, 407, 18], [375, 21, 407, 19], [375, 24, 407, 22, "r"], [375, 25, 407, 23], [375, 28, 407, 26], [375, 29, 407, 27], [376, 10, 408, 8, "data"], [376, 14, 408, 12], [376, 15, 408, 13, "index"], [376, 20, 408, 18], [376, 23, 408, 21], [376, 24, 408, 22], [376, 25, 408, 23], [376, 28, 408, 26, "g"], [376, 29, 408, 27], [376, 32, 408, 30], [376, 33, 408, 31], [377, 10, 409, 8, "data"], [377, 14, 409, 12], [377, 15, 409, 13, "index"], [377, 20, 409, 18], [377, 23, 409, 21], [377, 24, 409, 22], [377, 25, 409, 23], [377, 28, 409, 26, "b"], [377, 29, 409, 27], [377, 32, 409, 30], [377, 33, 409, 31], [378, 8, 410, 6], [379, 6, 411, 4], [380, 4, 412, 2], [380, 5, 412, 3], [381, 4, 414, 2], [381, 10, 414, 8, "applyFallbackFaceBlur"], [381, 31, 414, 29], [381, 34, 414, 32, "applyFallbackFaceBlur"], [381, 35, 414, 33, "ctx"], [381, 38, 414, 62], [381, 40, 414, 64, "imgWidth"], [381, 48, 414, 80], [381, 50, 414, 82, "imgHeight"], [381, 59, 414, 99], [381, 64, 414, 104], [382, 6, 415, 4, "console"], [382, 13, 415, 11], [382, 14, 415, 12, "log"], [382, 17, 415, 15], [382, 18, 415, 16], [382, 90, 415, 88], [382, 91, 415, 89], [384, 6, 417, 4], [385, 6, 418, 4], [385, 12, 418, 10, "areas"], [385, 17, 418, 15], [385, 20, 418, 18], [386, 6, 419, 6], [387, 6, 420, 6], [388, 8, 420, 8, "x"], [388, 9, 420, 9], [388, 11, 420, 11, "imgWidth"], [388, 19, 420, 19], [388, 22, 420, 22], [388, 26, 420, 26], [389, 8, 420, 28, "y"], [389, 9, 420, 29], [389, 11, 420, 31, "imgHeight"], [389, 20, 420, 40], [389, 23, 420, 43], [389, 27, 420, 47], [390, 8, 420, 49, "w"], [390, 9, 420, 50], [390, 11, 420, 52, "imgWidth"], [390, 19, 420, 60], [390, 22, 420, 63], [390, 25, 420, 66], [391, 8, 420, 68, "h"], [391, 9, 420, 69], [391, 11, 420, 71, "imgHeight"], [391, 20, 420, 80], [391, 23, 420, 83], [392, 6, 420, 87], [392, 7, 420, 88], [393, 6, 421, 6], [394, 6, 422, 6], [395, 8, 422, 8, "x"], [395, 9, 422, 9], [395, 11, 422, 11, "imgWidth"], [395, 19, 422, 19], [395, 22, 422, 22], [395, 25, 422, 25], [396, 8, 422, 27, "y"], [396, 9, 422, 28], [396, 11, 422, 30, "imgHeight"], [396, 20, 422, 39], [396, 23, 422, 42], [396, 26, 422, 45], [397, 8, 422, 47, "w"], [397, 9, 422, 48], [397, 11, 422, 50, "imgWidth"], [397, 19, 422, 58], [397, 22, 422, 61], [397, 26, 422, 65], [398, 8, 422, 67, "h"], [398, 9, 422, 68], [398, 11, 422, 70, "imgHeight"], [398, 20, 422, 79], [398, 23, 422, 82], [399, 6, 422, 86], [399, 7, 422, 87], [400, 6, 423, 6], [401, 6, 424, 6], [402, 8, 424, 8, "x"], [402, 9, 424, 9], [402, 11, 424, 11, "imgWidth"], [402, 19, 424, 19], [402, 22, 424, 22], [402, 26, 424, 26], [403, 8, 424, 28, "y"], [403, 9, 424, 29], [403, 11, 424, 31, "imgHeight"], [403, 20, 424, 40], [403, 23, 424, 43], [403, 26, 424, 46], [404, 8, 424, 48, "w"], [404, 9, 424, 49], [404, 11, 424, 51, "imgWidth"], [404, 19, 424, 59], [404, 22, 424, 62], [404, 26, 424, 66], [405, 8, 424, 68, "h"], [405, 9, 424, 69], [405, 11, 424, 71, "imgHeight"], [405, 20, 424, 80], [405, 23, 424, 83], [406, 6, 424, 87], [406, 7, 424, 88], [406, 8, 425, 5], [407, 6, 427, 4, "areas"], [407, 11, 427, 9], [407, 12, 427, 10, "for<PERSON>ach"], [407, 19, 427, 17], [407, 20, 427, 18], [407, 21, 427, 19, "area"], [407, 25, 427, 23], [407, 27, 427, 25, "index"], [407, 32, 427, 30], [407, 37, 427, 35], [408, 8, 428, 6, "console"], [408, 15, 428, 13], [408, 16, 428, 14, "log"], [408, 19, 428, 17], [408, 20, 428, 18], [408, 65, 428, 63, "index"], [408, 70, 428, 68], [408, 73, 428, 71], [408, 74, 428, 72], [408, 77, 428, 75], [408, 79, 428, 77, "area"], [408, 83, 428, 81], [408, 84, 428, 82], [409, 8, 429, 6, "applyStrongBlur"], [409, 23, 429, 21], [409, 24, 429, 22, "ctx"], [409, 27, 429, 25], [409, 29, 429, 27, "area"], [409, 33, 429, 31], [409, 34, 429, 32, "x"], [409, 35, 429, 33], [409, 37, 429, 35, "area"], [409, 41, 429, 39], [409, 42, 429, 40, "y"], [409, 43, 429, 41], [409, 45, 429, 43, "area"], [409, 49, 429, 47], [409, 50, 429, 48, "w"], [409, 51, 429, 49], [409, 53, 429, 51, "area"], [409, 57, 429, 55], [409, 58, 429, 56, "h"], [409, 59, 429, 57], [409, 60, 429, 58], [410, 6, 430, 4], [410, 7, 430, 5], [410, 8, 430, 6], [411, 4, 431, 2], [411, 5, 431, 3], [413, 4, 433, 2], [414, 4, 434, 2], [414, 10, 434, 8, "capturePhoto"], [414, 22, 434, 20], [414, 25, 434, 23], [414, 29, 434, 23, "useCallback"], [414, 47, 434, 34], [414, 49, 434, 35], [414, 61, 434, 47], [415, 6, 435, 4], [416, 6, 436, 4], [416, 12, 436, 10, "isDev"], [416, 17, 436, 15], [416, 20, 436, 18, "process"], [416, 27, 436, 25], [416, 28, 436, 26, "env"], [416, 31, 436, 29], [416, 32, 436, 30, "NODE_ENV"], [416, 40, 436, 38], [416, 45, 436, 43], [416, 58, 436, 56], [416, 62, 436, 60, "__DEV__"], [416, 69, 436, 67], [417, 6, 438, 4], [417, 10, 438, 8], [417, 11, 438, 9, "cameraRef"], [417, 20, 438, 18], [417, 21, 438, 19, "current"], [417, 28, 438, 26], [417, 32, 438, 30], [417, 33, 438, 31, "isDev"], [417, 38, 438, 36], [417, 40, 438, 38], [418, 8, 439, 6, "<PERSON><PERSON>"], [418, 22, 439, 11], [418, 23, 439, 12, "alert"], [418, 28, 439, 17], [418, 29, 439, 18], [418, 36, 439, 25], [418, 38, 439, 27], [418, 56, 439, 45], [418, 57, 439, 46], [419, 8, 440, 6], [420, 6, 441, 4], [421, 6, 442, 4], [421, 10, 442, 8], [422, 8, 443, 6, "setProcessingState"], [422, 26, 443, 24], [422, 27, 443, 25], [422, 38, 443, 36], [422, 39, 443, 37], [423, 8, 444, 6, "setProcessingProgress"], [423, 29, 444, 27], [423, 30, 444, 28], [423, 32, 444, 30], [423, 33, 444, 31], [424, 8, 445, 6], [425, 8, 446, 6], [426, 8, 447, 6], [427, 8, 448, 6], [427, 14, 448, 12], [427, 18, 448, 16, "Promise"], [427, 25, 448, 23], [427, 26, 448, 24, "resolve"], [427, 33, 448, 31], [427, 37, 448, 35, "setTimeout"], [427, 47, 448, 45], [427, 48, 448, 46, "resolve"], [427, 55, 448, 53], [427, 57, 448, 55], [427, 59, 448, 57], [427, 60, 448, 58], [427, 61, 448, 59], [428, 8, 449, 6], [429, 8, 450, 6], [429, 12, 450, 10, "photo"], [429, 17, 450, 15], [430, 8, 452, 6], [430, 12, 452, 10], [431, 10, 453, 8, "photo"], [431, 15, 453, 13], [431, 18, 453, 16], [431, 24, 453, 22, "cameraRef"], [431, 33, 453, 31], [431, 34, 453, 32, "current"], [431, 41, 453, 39], [431, 42, 453, 40, "takePictureAsync"], [431, 58, 453, 56], [431, 59, 453, 57], [432, 12, 454, 10, "quality"], [432, 19, 454, 17], [432, 21, 454, 19], [432, 24, 454, 22], [433, 12, 455, 10, "base64"], [433, 18, 455, 16], [433, 20, 455, 18], [433, 25, 455, 23], [434, 12, 456, 10, "skipProcessing"], [434, 26, 456, 24], [434, 28, 456, 26], [434, 32, 456, 30], [434, 33, 456, 32], [435, 10, 457, 8], [435, 11, 457, 9], [435, 12, 457, 10], [436, 8, 458, 6], [436, 9, 458, 7], [436, 10, 458, 8], [436, 17, 458, 15, "cameraError"], [436, 28, 458, 26], [436, 30, 458, 28], [437, 10, 459, 8, "console"], [437, 17, 459, 15], [437, 18, 459, 16, "log"], [437, 21, 459, 19], [437, 22, 459, 20], [437, 82, 459, 80], [437, 84, 459, 82, "cameraError"], [437, 95, 459, 93], [437, 96, 459, 94], [438, 10, 460, 8], [439, 10, 461, 8], [439, 14, 461, 12, "isDev"], [439, 19, 461, 17], [439, 21, 461, 19], [440, 12, 462, 10, "photo"], [440, 17, 462, 15], [440, 20, 462, 18], [441, 14, 463, 12, "uri"], [441, 17, 463, 15], [441, 19, 463, 17], [442, 12, 464, 10], [442, 13, 464, 11], [443, 10, 465, 8], [443, 11, 465, 9], [443, 17, 465, 15], [444, 12, 466, 10], [444, 18, 466, 16, "cameraError"], [444, 29, 466, 27], [445, 10, 467, 8], [446, 8, 468, 6], [447, 8, 469, 6], [447, 12, 469, 10], [447, 13, 469, 11, "photo"], [447, 18, 469, 16], [447, 20, 469, 18], [448, 10, 470, 8], [448, 16, 470, 14], [448, 20, 470, 18, "Error"], [448, 25, 470, 23], [448, 26, 470, 24], [448, 51, 470, 49], [448, 52, 470, 50], [449, 8, 471, 6], [450, 8, 472, 6, "console"], [450, 15, 472, 13], [450, 16, 472, 14, "log"], [450, 19, 472, 17], [450, 20, 472, 18], [450, 56, 472, 54], [450, 58, 472, 56, "photo"], [450, 63, 472, 61], [450, 64, 472, 62, "uri"], [450, 67, 472, 65], [450, 68, 472, 66], [451, 8, 473, 6, "setCapturedPhoto"], [451, 24, 473, 22], [451, 25, 473, 23, "photo"], [451, 30, 473, 28], [451, 31, 473, 29, "uri"], [451, 34, 473, 32], [451, 35, 473, 33], [452, 8, 474, 6, "setProcessingProgress"], [452, 29, 474, 27], [452, 30, 474, 28], [452, 32, 474, 30], [452, 33, 474, 31], [453, 8, 475, 6], [454, 8, 476, 6, "console"], [454, 15, 476, 13], [454, 16, 476, 14, "log"], [454, 19, 476, 17], [454, 20, 476, 18], [454, 73, 476, 71], [454, 74, 476, 72], [455, 8, 477, 6], [455, 14, 477, 12, "processImageWithFaceBlur"], [455, 38, 477, 36], [455, 39, 477, 37, "photo"], [455, 44, 477, 42], [455, 45, 477, 43, "uri"], [455, 48, 477, 46], [455, 49, 477, 47], [456, 8, 478, 6, "console"], [456, 15, 478, 13], [456, 16, 478, 14, "log"], [456, 19, 478, 17], [456, 20, 478, 18], [456, 71, 478, 69], [456, 72, 478, 70], [457, 6, 479, 4], [457, 7, 479, 5], [457, 8, 479, 6], [457, 15, 479, 13, "error"], [457, 20, 479, 18], [457, 22, 479, 20], [458, 8, 480, 6, "console"], [458, 15, 480, 13], [458, 16, 480, 14, "error"], [458, 21, 480, 19], [458, 22, 480, 20], [458, 54, 480, 52], [458, 56, 480, 54, "error"], [458, 61, 480, 59], [458, 62, 480, 60], [459, 8, 481, 6, "setErrorMessage"], [459, 23, 481, 21], [459, 24, 481, 22], [459, 68, 481, 66], [459, 69, 481, 67], [460, 8, 482, 6, "setProcessingState"], [460, 26, 482, 24], [460, 27, 482, 25], [460, 34, 482, 32], [460, 35, 482, 33], [461, 6, 483, 4], [462, 4, 484, 2], [462, 5, 484, 3], [462, 7, 484, 5], [462, 9, 484, 7], [462, 10, 484, 8], [463, 4, 485, 2], [464, 4, 486, 2], [464, 10, 486, 8, "processImageWithFaceBlur"], [464, 34, 486, 32], [464, 37, 486, 35], [464, 43, 486, 42, "photoUri"], [464, 51, 486, 58], [464, 55, 486, 63], [465, 6, 487, 4], [465, 10, 487, 8], [466, 8, 488, 6, "console"], [466, 15, 488, 13], [466, 16, 488, 14, "log"], [466, 19, 488, 17], [466, 20, 488, 18], [466, 84, 488, 82], [466, 85, 488, 83], [467, 8, 489, 6, "setProcessingState"], [467, 26, 489, 24], [467, 27, 489, 25], [467, 39, 489, 37], [467, 40, 489, 38], [468, 8, 490, 6, "setProcessingProgress"], [468, 29, 490, 27], [468, 30, 490, 28], [468, 32, 490, 30], [468, 33, 490, 31], [470, 8, 492, 6], [471, 8, 493, 6], [471, 14, 493, 12, "canvas"], [471, 20, 493, 18], [471, 23, 493, 21, "document"], [471, 31, 493, 29], [471, 32, 493, 30, "createElement"], [471, 45, 493, 43], [471, 46, 493, 44], [471, 54, 493, 52], [471, 55, 493, 53], [472, 8, 494, 6], [472, 14, 494, 12, "ctx"], [472, 17, 494, 15], [472, 20, 494, 18, "canvas"], [472, 26, 494, 24], [472, 27, 494, 25, "getContext"], [472, 37, 494, 35], [472, 38, 494, 36], [472, 42, 494, 40], [472, 43, 494, 41], [473, 8, 495, 6], [473, 12, 495, 10], [473, 13, 495, 11, "ctx"], [473, 16, 495, 14], [473, 18, 495, 16], [473, 24, 495, 22], [473, 28, 495, 26, "Error"], [473, 33, 495, 31], [473, 34, 495, 32], [473, 64, 495, 62], [473, 65, 495, 63], [475, 8, 497, 6], [476, 8, 498, 6], [476, 14, 498, 12, "img"], [476, 17, 498, 15], [476, 20, 498, 18], [476, 24, 498, 22, "Image"], [476, 29, 498, 27], [476, 30, 498, 28], [476, 31, 498, 29], [477, 8, 499, 6], [477, 14, 499, 12], [477, 18, 499, 16, "Promise"], [477, 25, 499, 23], [477, 26, 499, 24], [477, 27, 499, 25, "resolve"], [477, 34, 499, 32], [477, 36, 499, 34, "reject"], [477, 42, 499, 40], [477, 47, 499, 45], [478, 10, 500, 8, "img"], [478, 13, 500, 11], [478, 14, 500, 12, "onload"], [478, 20, 500, 18], [478, 23, 500, 21, "resolve"], [478, 30, 500, 28], [479, 10, 501, 8, "img"], [479, 13, 501, 11], [479, 14, 501, 12, "onerror"], [479, 21, 501, 19], [479, 24, 501, 22, "reject"], [479, 30, 501, 28], [480, 10, 502, 8, "img"], [480, 13, 502, 11], [480, 14, 502, 12, "src"], [480, 17, 502, 15], [480, 20, 502, 18, "photoUri"], [480, 28, 502, 26], [481, 8, 503, 6], [481, 9, 503, 7], [481, 10, 503, 8], [483, 8, 505, 6], [484, 8, 506, 6, "canvas"], [484, 14, 506, 12], [484, 15, 506, 13, "width"], [484, 20, 506, 18], [484, 23, 506, 21, "img"], [484, 26, 506, 24], [484, 27, 506, 25, "width"], [484, 32, 506, 30], [485, 8, 507, 6, "canvas"], [485, 14, 507, 12], [485, 15, 507, 13, "height"], [485, 21, 507, 19], [485, 24, 507, 22, "img"], [485, 27, 507, 25], [485, 28, 507, 26, "height"], [485, 34, 507, 32], [486, 8, 508, 6, "console"], [486, 15, 508, 13], [486, 16, 508, 14, "log"], [486, 19, 508, 17], [486, 20, 508, 18], [486, 54, 508, 52], [486, 56, 508, 54], [487, 10, 508, 56, "width"], [487, 15, 508, 61], [487, 17, 508, 63, "img"], [487, 20, 508, 66], [487, 21, 508, 67, "width"], [487, 26, 508, 72], [488, 10, 508, 74, "height"], [488, 16, 508, 80], [488, 18, 508, 82, "img"], [488, 21, 508, 85], [488, 22, 508, 86, "height"], [489, 8, 508, 93], [489, 9, 508, 94], [489, 10, 508, 95], [491, 8, 510, 6], [492, 8, 511, 6, "ctx"], [492, 11, 511, 9], [492, 12, 511, 10, "drawImage"], [492, 21, 511, 19], [492, 22, 511, 20, "img"], [492, 25, 511, 23], [492, 27, 511, 25], [492, 28, 511, 26], [492, 30, 511, 28], [492, 31, 511, 29], [492, 32, 511, 30], [493, 8, 512, 6, "console"], [493, 15, 512, 13], [493, 16, 512, 14, "log"], [493, 19, 512, 17], [493, 20, 512, 18], [493, 72, 512, 70], [493, 73, 512, 71], [494, 8, 514, 6, "setProcessingProgress"], [494, 29, 514, 27], [494, 30, 514, 28], [494, 32, 514, 30], [494, 33, 514, 31], [496, 8, 516, 6], [497, 8, 517, 6], [497, 12, 517, 10, "detectedFaces"], [497, 25, 517, 23], [497, 28, 517, 26], [497, 30, 517, 28], [498, 8, 519, 6, "console"], [498, 15, 519, 13], [498, 16, 519, 14, "log"], [498, 19, 519, 17], [498, 20, 519, 18], [498, 81, 519, 79], [498, 82, 519, 80], [500, 8, 521, 6], [501, 8, 522, 6], [501, 12, 522, 10], [502, 10, 523, 8], [502, 16, 523, 14, "loadTensorFlowFaceDetection"], [502, 43, 523, 41], [502, 44, 523, 42], [502, 45, 523, 43], [503, 10, 524, 8, "detectedFaces"], [503, 23, 524, 21], [503, 26, 524, 24], [503, 32, 524, 30, "detectFacesWithTensorFlow"], [503, 57, 524, 55], [503, 58, 524, 56, "img"], [503, 61, 524, 59], [503, 62, 524, 60], [504, 10, 525, 8, "console"], [504, 17, 525, 15], [504, 18, 525, 16, "log"], [504, 21, 525, 19], [504, 22, 525, 20], [504, 70, 525, 68, "detectedFaces"], [504, 83, 525, 81], [504, 84, 525, 82, "length"], [504, 90, 525, 88], [504, 98, 525, 96], [504, 99, 525, 97], [505, 8, 526, 6], [505, 9, 526, 7], [505, 10, 526, 8], [505, 17, 526, 15, "tensorFlowError"], [505, 32, 526, 30], [505, 34, 526, 32], [506, 10, 527, 8, "console"], [506, 17, 527, 15], [506, 18, 527, 16, "warn"], [506, 22, 527, 20], [506, 23, 527, 21], [506, 61, 527, 59], [506, 63, 527, 61, "tensorFlowError"], [506, 78, 527, 76], [506, 79, 527, 77], [508, 10, 529, 8], [509, 10, 530, 8, "console"], [509, 17, 530, 15], [509, 18, 530, 16, "log"], [509, 21, 530, 19], [509, 22, 530, 20], [509, 86, 530, 84], [509, 87, 530, 85], [510, 10, 531, 8, "detectedFaces"], [510, 23, 531, 21], [510, 26, 531, 24, "detectFacesHeuristic"], [510, 46, 531, 44], [510, 47, 531, 45, "img"], [510, 50, 531, 48], [510, 52, 531, 50, "ctx"], [510, 55, 531, 53], [510, 56, 531, 54], [511, 10, 532, 8, "console"], [511, 17, 532, 15], [511, 18, 532, 16, "log"], [511, 21, 532, 19], [511, 22, 532, 20], [511, 70, 532, 68, "detectedFaces"], [511, 83, 532, 81], [511, 84, 532, 82, "length"], [511, 90, 532, 88], [511, 98, 532, 96], [511, 99, 532, 97], [512, 8, 533, 6], [513, 8, 535, 6, "console"], [513, 15, 535, 13], [513, 16, 535, 14, "log"], [513, 19, 535, 17], [513, 20, 535, 18], [513, 72, 535, 70, "detectedFaces"], [513, 85, 535, 83], [513, 86, 535, 84, "length"], [513, 92, 535, 90], [513, 100, 535, 98], [513, 101, 535, 99], [514, 8, 536, 6], [514, 12, 536, 10, "detectedFaces"], [514, 25, 536, 23], [514, 26, 536, 24, "length"], [514, 32, 536, 30], [514, 35, 536, 33], [514, 36, 536, 34], [514, 38, 536, 36], [515, 10, 537, 8, "console"], [515, 17, 537, 15], [515, 18, 537, 16, "log"], [515, 21, 537, 19], [515, 22, 537, 20], [515, 66, 537, 64], [515, 68, 537, 66, "detectedFaces"], [515, 81, 537, 79], [515, 82, 537, 80, "map"], [515, 85, 537, 83], [515, 86, 537, 84], [515, 87, 537, 85, "face"], [515, 91, 537, 89], [515, 93, 537, 91, "i"], [515, 94, 537, 92], [515, 100, 537, 98], [516, 12, 538, 10, "faceNumber"], [516, 22, 538, 20], [516, 24, 538, 22, "i"], [516, 25, 538, 23], [516, 28, 538, 26], [516, 29, 538, 27], [517, 12, 539, 10, "centerX"], [517, 19, 539, 17], [517, 21, 539, 19, "face"], [517, 25, 539, 23], [517, 26, 539, 24, "boundingBox"], [517, 37, 539, 35], [517, 38, 539, 36, "xCenter"], [517, 45, 539, 43], [518, 12, 540, 10, "centerY"], [518, 19, 540, 17], [518, 21, 540, 19, "face"], [518, 25, 540, 23], [518, 26, 540, 24, "boundingBox"], [518, 37, 540, 35], [518, 38, 540, 36, "yCenter"], [518, 45, 540, 43], [519, 12, 541, 10, "width"], [519, 17, 541, 15], [519, 19, 541, 17, "face"], [519, 23, 541, 21], [519, 24, 541, 22, "boundingBox"], [519, 35, 541, 33], [519, 36, 541, 34, "width"], [519, 41, 541, 39], [520, 12, 542, 10, "height"], [520, 18, 542, 16], [520, 20, 542, 18, "face"], [520, 24, 542, 22], [520, 25, 542, 23, "boundingBox"], [520, 36, 542, 34], [520, 37, 542, 35, "height"], [521, 10, 543, 8], [521, 11, 543, 9], [521, 12, 543, 10], [521, 13, 543, 11], [521, 14, 543, 12], [522, 8, 544, 6], [522, 9, 544, 7], [522, 15, 544, 13], [523, 10, 545, 8, "console"], [523, 17, 545, 15], [523, 18, 545, 16, "log"], [523, 21, 545, 19], [523, 22, 545, 20], [523, 91, 545, 89], [523, 92, 545, 90], [524, 8, 546, 6], [525, 8, 548, 6, "setProcessingProgress"], [525, 29, 548, 27], [525, 30, 548, 28], [525, 32, 548, 30], [525, 33, 548, 31], [527, 8, 550, 6], [528, 8, 551, 6], [528, 12, 551, 10, "detectedFaces"], [528, 25, 551, 23], [528, 26, 551, 24, "length"], [528, 32, 551, 30], [528, 35, 551, 33], [528, 36, 551, 34], [528, 38, 551, 36], [529, 10, 552, 8, "console"], [529, 17, 552, 15], [529, 18, 552, 16, "log"], [529, 21, 552, 19], [529, 22, 552, 20], [529, 61, 552, 59, "detectedFaces"], [529, 74, 552, 72], [529, 75, 552, 73, "length"], [529, 81, 552, 79], [529, 101, 552, 99], [529, 102, 552, 100], [530, 10, 554, 8, "detectedFaces"], [530, 23, 554, 21], [530, 24, 554, 22, "for<PERSON>ach"], [530, 31, 554, 29], [530, 32, 554, 30], [530, 33, 554, 31, "detection"], [530, 42, 554, 40], [530, 44, 554, 42, "index"], [530, 49, 554, 47], [530, 54, 554, 52], [531, 12, 555, 10], [531, 18, 555, 16, "bbox"], [531, 22, 555, 20], [531, 25, 555, 23, "detection"], [531, 34, 555, 32], [531, 35, 555, 33, "boundingBox"], [531, 46, 555, 44], [533, 12, 557, 10], [534, 12, 558, 10], [534, 18, 558, 16, "faceX"], [534, 23, 558, 21], [534, 26, 558, 24, "bbox"], [534, 30, 558, 28], [534, 31, 558, 29, "xCenter"], [534, 38, 558, 36], [534, 41, 558, 39, "img"], [534, 44, 558, 42], [534, 45, 558, 43, "width"], [534, 50, 558, 48], [534, 53, 558, 52, "bbox"], [534, 57, 558, 56], [534, 58, 558, 57, "width"], [534, 63, 558, 62], [534, 66, 558, 65, "img"], [534, 69, 558, 68], [534, 70, 558, 69, "width"], [534, 75, 558, 74], [534, 78, 558, 78], [534, 79, 558, 79], [535, 12, 559, 10], [535, 18, 559, 16, "faceY"], [535, 23, 559, 21], [535, 26, 559, 24, "bbox"], [535, 30, 559, 28], [535, 31, 559, 29, "yCenter"], [535, 38, 559, 36], [535, 41, 559, 39, "img"], [535, 44, 559, 42], [535, 45, 559, 43, "height"], [535, 51, 559, 49], [535, 54, 559, 53, "bbox"], [535, 58, 559, 57], [535, 59, 559, 58, "height"], [535, 65, 559, 64], [535, 68, 559, 67, "img"], [535, 71, 559, 70], [535, 72, 559, 71, "height"], [535, 78, 559, 77], [535, 81, 559, 81], [535, 82, 559, 82], [536, 12, 560, 10], [536, 18, 560, 16, "faceWidth"], [536, 27, 560, 25], [536, 30, 560, 28, "bbox"], [536, 34, 560, 32], [536, 35, 560, 33, "width"], [536, 40, 560, 38], [536, 43, 560, 41, "img"], [536, 46, 560, 44], [536, 47, 560, 45, "width"], [536, 52, 560, 50], [537, 12, 561, 10], [537, 18, 561, 16, "faceHeight"], [537, 28, 561, 26], [537, 31, 561, 29, "bbox"], [537, 35, 561, 33], [537, 36, 561, 34, "height"], [537, 42, 561, 40], [537, 45, 561, 43, "img"], [537, 48, 561, 46], [537, 49, 561, 47, "height"], [537, 55, 561, 53], [539, 12, 563, 10], [540, 12, 564, 10], [540, 18, 564, 16, "padding"], [540, 25, 564, 23], [540, 28, 564, 26], [540, 31, 564, 29], [541, 12, 565, 10], [541, 18, 565, 16, "paddedX"], [541, 25, 565, 23], [541, 28, 565, 26, "Math"], [541, 32, 565, 30], [541, 33, 565, 31, "max"], [541, 36, 565, 34], [541, 37, 565, 35], [541, 38, 565, 36], [541, 40, 565, 38, "faceX"], [541, 45, 565, 43], [541, 48, 565, 46, "faceWidth"], [541, 57, 565, 55], [541, 60, 565, 58, "padding"], [541, 67, 565, 65], [541, 68, 565, 66], [542, 12, 566, 10], [542, 18, 566, 16, "paddedY"], [542, 25, 566, 23], [542, 28, 566, 26, "Math"], [542, 32, 566, 30], [542, 33, 566, 31, "max"], [542, 36, 566, 34], [542, 37, 566, 35], [542, 38, 566, 36], [542, 40, 566, 38, "faceY"], [542, 45, 566, 43], [542, 48, 566, 46, "faceHeight"], [542, 58, 566, 56], [542, 61, 566, 59, "padding"], [542, 68, 566, 66], [542, 69, 566, 67], [543, 12, 567, 10], [543, 18, 567, 16, "<PERSON><PERSON><PERSON><PERSON>"], [543, 29, 567, 27], [543, 32, 567, 30, "Math"], [543, 36, 567, 34], [543, 37, 567, 35, "min"], [543, 40, 567, 38], [543, 41, 567, 39, "img"], [543, 44, 567, 42], [543, 45, 567, 43, "width"], [543, 50, 567, 48], [543, 53, 567, 51, "paddedX"], [543, 60, 567, 58], [543, 62, 567, 60, "faceWidth"], [543, 71, 567, 69], [543, 75, 567, 73], [543, 76, 567, 74], [543, 79, 567, 77], [543, 80, 567, 78], [543, 83, 567, 81, "padding"], [543, 90, 567, 88], [543, 91, 567, 89], [543, 92, 567, 90], [544, 12, 568, 10], [544, 18, 568, 16, "paddedHeight"], [544, 30, 568, 28], [544, 33, 568, 31, "Math"], [544, 37, 568, 35], [544, 38, 568, 36, "min"], [544, 41, 568, 39], [544, 42, 568, 40, "img"], [544, 45, 568, 43], [544, 46, 568, 44, "height"], [544, 52, 568, 50], [544, 55, 568, 53, "paddedY"], [544, 62, 568, 60], [544, 64, 568, 62, "faceHeight"], [544, 74, 568, 72], [544, 78, 568, 76], [544, 79, 568, 77], [544, 82, 568, 80], [544, 83, 568, 81], [544, 86, 568, 84, "padding"], [544, 93, 568, 91], [544, 94, 568, 92], [544, 95, 568, 93], [545, 12, 570, 10, "console"], [545, 19, 570, 17], [545, 20, 570, 18, "log"], [545, 23, 570, 21], [545, 24, 570, 22], [545, 60, 570, 58, "index"], [545, 65, 570, 63], [545, 68, 570, 66], [545, 69, 570, 67], [545, 72, 570, 70], [545, 74, 570, 72], [546, 14, 571, 12, "original"], [546, 22, 571, 20], [546, 24, 571, 22], [547, 16, 571, 24, "x"], [547, 17, 571, 25], [547, 19, 571, 27, "Math"], [547, 23, 571, 31], [547, 24, 571, 32, "round"], [547, 29, 571, 37], [547, 30, 571, 38, "faceX"], [547, 35, 571, 43], [547, 36, 571, 44], [548, 16, 571, 46, "y"], [548, 17, 571, 47], [548, 19, 571, 49, "Math"], [548, 23, 571, 53], [548, 24, 571, 54, "round"], [548, 29, 571, 59], [548, 30, 571, 60, "faceY"], [548, 35, 571, 65], [548, 36, 571, 66], [549, 16, 571, 68, "w"], [549, 17, 571, 69], [549, 19, 571, 71, "Math"], [549, 23, 571, 75], [549, 24, 571, 76, "round"], [549, 29, 571, 81], [549, 30, 571, 82, "faceWidth"], [549, 39, 571, 91], [549, 40, 571, 92], [550, 16, 571, 94, "h"], [550, 17, 571, 95], [550, 19, 571, 97, "Math"], [550, 23, 571, 101], [550, 24, 571, 102, "round"], [550, 29, 571, 107], [550, 30, 571, 108, "faceHeight"], [550, 40, 571, 118], [551, 14, 571, 120], [551, 15, 571, 121], [552, 14, 572, 12, "padded"], [552, 20, 572, 18], [552, 22, 572, 20], [553, 16, 572, 22, "x"], [553, 17, 572, 23], [553, 19, 572, 25, "Math"], [553, 23, 572, 29], [553, 24, 572, 30, "round"], [553, 29, 572, 35], [553, 30, 572, 36, "paddedX"], [553, 37, 572, 43], [553, 38, 572, 44], [554, 16, 572, 46, "y"], [554, 17, 572, 47], [554, 19, 572, 49, "Math"], [554, 23, 572, 53], [554, 24, 572, 54, "round"], [554, 29, 572, 59], [554, 30, 572, 60, "paddedY"], [554, 37, 572, 67], [554, 38, 572, 68], [555, 16, 572, 70, "w"], [555, 17, 572, 71], [555, 19, 572, 73, "Math"], [555, 23, 572, 77], [555, 24, 572, 78, "round"], [555, 29, 572, 83], [555, 30, 572, 84, "<PERSON><PERSON><PERSON><PERSON>"], [555, 41, 572, 95], [555, 42, 572, 96], [556, 16, 572, 98, "h"], [556, 17, 572, 99], [556, 19, 572, 101, "Math"], [556, 23, 572, 105], [556, 24, 572, 106, "round"], [556, 29, 572, 111], [556, 30, 572, 112, "paddedHeight"], [556, 42, 572, 124], [557, 14, 572, 126], [558, 12, 573, 10], [558, 13, 573, 11], [558, 14, 573, 12], [560, 12, 575, 10], [561, 12, 576, 10, "applyStrongBlur"], [561, 27, 576, 25], [561, 28, 576, 26, "ctx"], [561, 31, 576, 29], [561, 33, 576, 31, "paddedX"], [561, 40, 576, 38], [561, 42, 576, 40, "paddedY"], [561, 49, 576, 47], [561, 51, 576, 49, "<PERSON><PERSON><PERSON><PERSON>"], [561, 62, 576, 60], [561, 64, 576, 62, "paddedHeight"], [561, 76, 576, 74], [561, 77, 576, 75], [562, 12, 577, 10, "console"], [562, 19, 577, 17], [562, 20, 577, 18, "log"], [562, 23, 577, 21], [562, 24, 577, 22], [562, 50, 577, 48, "index"], [562, 55, 577, 53], [562, 58, 577, 56], [562, 59, 577, 57], [562, 79, 577, 77], [562, 80, 577, 78], [563, 10, 578, 8], [563, 11, 578, 9], [563, 12, 578, 10], [564, 10, 580, 8, "console"], [564, 17, 580, 15], [564, 18, 580, 16, "log"], [564, 21, 580, 19], [564, 22, 580, 20], [564, 48, 580, 46, "detectedFaces"], [564, 61, 580, 59], [564, 62, 580, 60, "length"], [564, 68, 580, 66], [564, 104, 580, 102], [564, 105, 580, 103], [565, 8, 581, 6], [565, 9, 581, 7], [565, 15, 581, 13], [566, 10, 582, 8, "console"], [566, 17, 582, 15], [566, 18, 582, 16, "log"], [566, 21, 582, 19], [566, 22, 582, 20], [566, 109, 582, 107], [566, 110, 582, 108], [567, 10, 583, 8], [568, 10, 584, 8, "applyFallbackFaceBlur"], [568, 31, 584, 29], [568, 32, 584, 30, "ctx"], [568, 35, 584, 33], [568, 37, 584, 35, "img"], [568, 40, 584, 38], [568, 41, 584, 39, "width"], [568, 46, 584, 44], [568, 48, 584, 46, "img"], [568, 51, 584, 49], [568, 52, 584, 50, "height"], [568, 58, 584, 56], [568, 59, 584, 57], [569, 8, 585, 6], [570, 8, 587, 6, "setProcessingProgress"], [570, 29, 587, 27], [570, 30, 587, 28], [570, 32, 587, 30], [570, 33, 587, 31], [572, 8, 589, 6], [573, 8, 590, 6, "console"], [573, 15, 590, 13], [573, 16, 590, 14, "log"], [573, 19, 590, 17], [573, 20, 590, 18], [573, 85, 590, 83], [573, 86, 590, 84], [574, 8, 591, 6], [574, 14, 591, 12, "blurredImageBlob"], [574, 30, 591, 28], [574, 33, 591, 31], [574, 39, 591, 37], [574, 43, 591, 41, "Promise"], [574, 50, 591, 48], [574, 51, 591, 56, "resolve"], [574, 58, 591, 63], [574, 62, 591, 68], [575, 10, 592, 8, "canvas"], [575, 16, 592, 14], [575, 17, 592, 15, "toBlob"], [575, 23, 592, 21], [575, 24, 592, 23, "blob"], [575, 28, 592, 27], [575, 32, 592, 32, "resolve"], [575, 39, 592, 39], [575, 40, 592, 40, "blob"], [575, 44, 592, 45], [575, 45, 592, 46], [575, 47, 592, 48], [575, 59, 592, 60], [575, 61, 592, 62], [575, 64, 592, 65], [575, 65, 592, 66], [576, 8, 593, 6], [576, 9, 593, 7], [576, 10, 593, 8], [577, 8, 595, 6], [577, 14, 595, 12, "blurredImageUrl"], [577, 29, 595, 27], [577, 32, 595, 30, "URL"], [577, 35, 595, 33], [577, 36, 595, 34, "createObjectURL"], [577, 51, 595, 49], [577, 52, 595, 50, "blurredImageBlob"], [577, 68, 595, 66], [577, 69, 595, 67], [578, 8, 596, 6, "console"], [578, 15, 596, 13], [578, 16, 596, 14, "log"], [578, 19, 596, 17], [578, 20, 596, 18], [578, 66, 596, 64], [578, 68, 596, 66, "blurredImageUrl"], [578, 83, 596, 81], [578, 84, 596, 82, "substring"], [578, 93, 596, 91], [578, 94, 596, 92], [578, 95, 596, 93], [578, 97, 596, 95], [578, 99, 596, 97], [578, 100, 596, 98], [578, 103, 596, 101], [578, 108, 596, 106], [578, 109, 596, 107], [579, 8, 598, 6, "setProcessingProgress"], [579, 29, 598, 27], [579, 30, 598, 28], [579, 33, 598, 31], [579, 34, 598, 32], [581, 8, 600, 6], [582, 8, 601, 6], [582, 14, 601, 12, "completeProcessing"], [582, 32, 601, 30], [582, 33, 601, 31, "blurredImageUrl"], [582, 48, 601, 46], [582, 49, 601, 47], [583, 6, 603, 4], [583, 7, 603, 5], [583, 8, 603, 6], [583, 15, 603, 13, "error"], [583, 20, 603, 18], [583, 22, 603, 20], [584, 8, 604, 6, "console"], [584, 15, 604, 13], [584, 16, 604, 14, "error"], [584, 21, 604, 19], [584, 22, 604, 20], [584, 57, 604, 55], [584, 59, 604, 57, "error"], [584, 64, 604, 62], [584, 65, 604, 63], [585, 8, 605, 6, "setErrorMessage"], [585, 23, 605, 21], [585, 24, 605, 22], [585, 50, 605, 48], [585, 51, 605, 49], [586, 8, 606, 6, "setProcessingState"], [586, 26, 606, 24], [586, 27, 606, 25], [586, 34, 606, 32], [586, 35, 606, 33], [587, 6, 607, 4], [588, 4, 608, 2], [588, 5, 608, 3], [590, 4, 610, 2], [591, 4, 611, 2], [591, 10, 611, 8, "completeProcessing"], [591, 28, 611, 26], [591, 31, 611, 29], [591, 37, 611, 36, "blurredImageUrl"], [591, 52, 611, 59], [591, 56, 611, 64], [592, 6, 612, 4], [592, 10, 612, 8], [593, 8, 613, 6, "setProcessingState"], [593, 26, 613, 24], [593, 27, 613, 25], [593, 37, 613, 35], [593, 38, 613, 36], [595, 8, 615, 6], [596, 8, 616, 6], [596, 14, 616, 12, "timestamp"], [596, 23, 616, 21], [596, 26, 616, 24, "Date"], [596, 30, 616, 28], [596, 31, 616, 29, "now"], [596, 34, 616, 32], [596, 35, 616, 33], [596, 36, 616, 34], [597, 8, 617, 6], [597, 14, 617, 12, "result"], [597, 20, 617, 18], [597, 23, 617, 21], [598, 10, 618, 8, "imageUrl"], [598, 18, 618, 16], [598, 20, 618, 18, "blurredImageUrl"], [598, 35, 618, 33], [599, 10, 619, 8, "localUri"], [599, 18, 619, 16], [599, 20, 619, 18, "blurredImageUrl"], [599, 35, 619, 33], [600, 10, 620, 8, "challengeCode"], [600, 23, 620, 21], [600, 25, 620, 23, "challengeCode"], [600, 38, 620, 36], [600, 42, 620, 40], [600, 44, 620, 42], [601, 10, 621, 8, "timestamp"], [601, 19, 621, 17], [602, 10, 622, 8, "jobId"], [602, 15, 622, 13], [602, 17, 622, 15], [602, 27, 622, 25, "timestamp"], [602, 36, 622, 34], [602, 38, 622, 36], [603, 10, 623, 8, "status"], [603, 16, 623, 14], [603, 18, 623, 16], [604, 8, 624, 6], [604, 9, 624, 7], [605, 8, 626, 6, "console"], [605, 15, 626, 13], [605, 16, 626, 14, "log"], [605, 19, 626, 17], [605, 20, 626, 18], [605, 100, 626, 98], [605, 102, 626, 100], [606, 10, 627, 8, "imageUrl"], [606, 18, 627, 16], [606, 20, 627, 18, "blurredImageUrl"], [606, 35, 627, 33], [606, 36, 627, 34, "substring"], [606, 45, 627, 43], [606, 46, 627, 44], [606, 47, 627, 45], [606, 49, 627, 47], [606, 51, 627, 49], [606, 52, 627, 50], [606, 55, 627, 53], [606, 60, 627, 58], [607, 10, 628, 8, "timestamp"], [607, 19, 628, 17], [608, 10, 629, 8, "jobId"], [608, 15, 629, 13], [608, 17, 629, 15, "result"], [608, 23, 629, 21], [608, 24, 629, 22, "jobId"], [609, 8, 630, 6], [609, 9, 630, 7], [609, 10, 630, 8], [611, 8, 632, 6], [612, 8, 633, 6, "onComplete"], [612, 18, 633, 16], [612, 19, 633, 17, "result"], [612, 25, 633, 23], [612, 26, 633, 24], [613, 6, 635, 4], [613, 7, 635, 5], [613, 8, 635, 6], [613, 15, 635, 13, "error"], [613, 20, 635, 18], [613, 22, 635, 20], [614, 8, 636, 6, "console"], [614, 15, 636, 13], [614, 16, 636, 14, "error"], [614, 21, 636, 19], [614, 22, 636, 20], [614, 57, 636, 55], [614, 59, 636, 57, "error"], [614, 64, 636, 62], [614, 65, 636, 63], [615, 8, 637, 6, "setErrorMessage"], [615, 23, 637, 21], [615, 24, 637, 22], [615, 56, 637, 54], [615, 57, 637, 55], [616, 8, 638, 6, "setProcessingState"], [616, 26, 638, 24], [616, 27, 638, 25], [616, 34, 638, 32], [616, 35, 638, 33], [617, 6, 639, 4], [618, 4, 640, 2], [618, 5, 640, 3], [620, 4, 642, 2], [621, 4, 643, 2], [621, 10, 643, 8, "triggerServerProcessing"], [621, 33, 643, 31], [621, 36, 643, 34], [621, 42, 643, 34, "triggerServerProcessing"], [621, 43, 643, 41, "privateImageUrl"], [621, 58, 643, 64], [621, 60, 643, 66, "timestamp"], [621, 69, 643, 83], [621, 74, 643, 88], [622, 6, 644, 4], [622, 10, 644, 8], [623, 8, 645, 6, "console"], [623, 15, 645, 13], [623, 16, 645, 14, "log"], [623, 19, 645, 17], [623, 20, 645, 18], [623, 74, 645, 72], [623, 76, 645, 74, "privateImageUrl"], [623, 91, 645, 89], [623, 92, 645, 90], [624, 8, 646, 6, "setProcessingState"], [624, 26, 646, 24], [624, 27, 646, 25], [624, 39, 646, 37], [624, 40, 646, 38], [625, 8, 647, 6, "setProcessingProgress"], [625, 29, 647, 27], [625, 30, 647, 28], [625, 32, 647, 30], [625, 33, 647, 31], [626, 8, 649, 6], [626, 14, 649, 12, "requestBody"], [626, 25, 649, 23], [626, 28, 649, 26], [627, 10, 650, 8, "imageUrl"], [627, 18, 650, 16], [627, 20, 650, 18, "privateImageUrl"], [627, 35, 650, 33], [628, 10, 651, 8, "userId"], [628, 16, 651, 14], [629, 10, 652, 8, "requestId"], [629, 19, 652, 17], [630, 10, 653, 8, "timestamp"], [630, 19, 653, 17], [631, 10, 654, 8, "platform"], [631, 18, 654, 16], [631, 20, 654, 18], [632, 8, 655, 6], [632, 9, 655, 7], [633, 8, 657, 6, "console"], [633, 15, 657, 13], [633, 16, 657, 14, "log"], [633, 19, 657, 17], [633, 20, 657, 18], [633, 65, 657, 63], [633, 67, 657, 65, "requestBody"], [633, 78, 657, 76], [633, 79, 657, 77], [635, 8, 659, 6], [636, 8, 660, 6], [636, 14, 660, 12, "response"], [636, 22, 660, 20], [636, 25, 660, 23], [636, 31, 660, 29, "fetch"], [636, 36, 660, 34], [636, 37, 660, 35], [636, 40, 660, 38, "API_BASE_URL"], [636, 52, 660, 50], [636, 72, 660, 70], [636, 74, 660, 72], [637, 10, 661, 8, "method"], [637, 16, 661, 14], [637, 18, 661, 16], [637, 24, 661, 22], [638, 10, 662, 8, "headers"], [638, 17, 662, 15], [638, 19, 662, 17], [639, 12, 663, 10], [639, 26, 663, 24], [639, 28, 663, 26], [639, 46, 663, 44], [640, 12, 664, 10], [640, 27, 664, 25], [640, 29, 664, 27], [640, 39, 664, 37], [640, 45, 664, 43, "getAuthToken"], [640, 57, 664, 55], [640, 58, 664, 56], [640, 59, 664, 57], [641, 10, 665, 8], [641, 11, 665, 9], [642, 10, 666, 8, "body"], [642, 14, 666, 12], [642, 16, 666, 14, "JSON"], [642, 20, 666, 18], [642, 21, 666, 19, "stringify"], [642, 30, 666, 28], [642, 31, 666, 29, "requestBody"], [642, 42, 666, 40], [643, 8, 667, 6], [643, 9, 667, 7], [643, 10, 667, 8], [644, 8, 669, 6], [644, 12, 669, 10], [644, 13, 669, 11, "response"], [644, 21, 669, 19], [644, 22, 669, 20, "ok"], [644, 24, 669, 22], [644, 26, 669, 24], [645, 10, 670, 8], [645, 16, 670, 14, "errorText"], [645, 25, 670, 23], [645, 28, 670, 26], [645, 34, 670, 32, "response"], [645, 42, 670, 40], [645, 43, 670, 41, "text"], [645, 47, 670, 45], [645, 48, 670, 46], [645, 49, 670, 47], [646, 10, 671, 8, "console"], [646, 17, 671, 15], [646, 18, 671, 16, "error"], [646, 23, 671, 21], [646, 24, 671, 22], [646, 68, 671, 66], [646, 70, 671, 68, "response"], [646, 78, 671, 76], [646, 79, 671, 77, "status"], [646, 85, 671, 83], [646, 87, 671, 85, "errorText"], [646, 96, 671, 94], [646, 97, 671, 95], [647, 10, 672, 8], [647, 16, 672, 14], [647, 20, 672, 18, "Error"], [647, 25, 672, 23], [647, 26, 672, 24], [647, 48, 672, 46, "response"], [647, 56, 672, 54], [647, 57, 672, 55, "status"], [647, 63, 672, 61], [647, 67, 672, 65, "response"], [647, 75, 672, 73], [647, 76, 672, 74, "statusText"], [647, 86, 672, 84], [647, 88, 672, 86], [647, 89, 672, 87], [648, 8, 673, 6], [649, 8, 675, 6], [649, 14, 675, 12, "result"], [649, 20, 675, 18], [649, 23, 675, 21], [649, 29, 675, 27, "response"], [649, 37, 675, 35], [649, 38, 675, 36, "json"], [649, 42, 675, 40], [649, 43, 675, 41], [649, 44, 675, 42], [650, 8, 676, 6, "console"], [650, 15, 676, 13], [650, 16, 676, 14, "log"], [650, 19, 676, 17], [650, 20, 676, 18], [650, 68, 676, 66], [650, 70, 676, 68, "result"], [650, 76, 676, 74], [650, 77, 676, 75], [651, 8, 678, 6], [651, 12, 678, 10], [651, 13, 678, 11, "result"], [651, 19, 678, 17], [651, 20, 678, 18, "jobId"], [651, 25, 678, 23], [651, 27, 678, 25], [652, 10, 679, 8], [652, 16, 679, 14], [652, 20, 679, 18, "Error"], [652, 25, 679, 23], [652, 26, 679, 24], [652, 70, 679, 68], [652, 71, 679, 69], [653, 8, 680, 6], [655, 8, 682, 6], [656, 8, 683, 6], [656, 14, 683, 12, "pollForCompletion"], [656, 31, 683, 29], [656, 32, 683, 30, "result"], [656, 38, 683, 36], [656, 39, 683, 37, "jobId"], [656, 44, 683, 42], [656, 46, 683, 44, "timestamp"], [656, 55, 683, 53], [656, 56, 683, 54], [657, 6, 684, 4], [657, 7, 684, 5], [657, 8, 684, 6], [657, 15, 684, 13, "error"], [657, 20, 684, 18], [657, 22, 684, 20], [658, 8, 685, 6, "console"], [658, 15, 685, 13], [658, 16, 685, 14, "error"], [658, 21, 685, 19], [658, 22, 685, 20], [658, 57, 685, 55], [658, 59, 685, 57, "error"], [658, 64, 685, 62], [658, 65, 685, 63], [659, 8, 686, 6, "setErrorMessage"], [659, 23, 686, 21], [659, 24, 686, 22], [659, 52, 686, 50, "error"], [659, 57, 686, 55], [659, 58, 686, 56, "message"], [659, 65, 686, 63], [659, 67, 686, 65], [659, 68, 686, 66], [660, 8, 687, 6, "setProcessingState"], [660, 26, 687, 24], [660, 27, 687, 25], [660, 34, 687, 32], [660, 35, 687, 33], [661, 6, 688, 4], [662, 4, 689, 2], [662, 5, 689, 3], [663, 4, 690, 2], [664, 4, 691, 2], [664, 10, 691, 8, "pollForCompletion"], [664, 27, 691, 25], [664, 30, 691, 28], [664, 36, 691, 28, "pollForCompletion"], [664, 37, 691, 35, "jobId"], [664, 42, 691, 48], [664, 44, 691, 50, "timestamp"], [664, 53, 691, 67], [664, 55, 691, 69, "attempts"], [664, 63, 691, 77], [664, 66, 691, 80], [664, 67, 691, 81], [664, 72, 691, 86], [665, 6, 692, 4], [665, 12, 692, 10, "MAX_ATTEMPTS"], [665, 24, 692, 22], [665, 27, 692, 25], [665, 29, 692, 27], [665, 30, 692, 28], [665, 31, 692, 29], [666, 6, 693, 4], [666, 12, 693, 10, "POLL_INTERVAL"], [666, 25, 693, 23], [666, 28, 693, 26], [666, 32, 693, 30], [666, 33, 693, 31], [666, 34, 693, 32], [668, 6, 695, 4, "console"], [668, 13, 695, 11], [668, 14, 695, 12, "log"], [668, 17, 695, 15], [668, 18, 695, 16], [668, 53, 695, 51, "attempts"], [668, 61, 695, 59], [668, 64, 695, 62], [668, 65, 695, 63], [668, 69, 695, 67, "MAX_ATTEMPTS"], [668, 81, 695, 79], [668, 93, 695, 91, "jobId"], [668, 98, 695, 96], [668, 100, 695, 98], [668, 101, 695, 99], [669, 6, 697, 4], [669, 10, 697, 8, "attempts"], [669, 18, 697, 16], [669, 22, 697, 20, "MAX_ATTEMPTS"], [669, 34, 697, 32], [669, 36, 697, 34], [670, 8, 698, 6, "console"], [670, 15, 698, 13], [670, 16, 698, 14, "error"], [670, 21, 698, 19], [670, 22, 698, 20], [670, 75, 698, 73], [670, 76, 698, 74], [671, 8, 699, 6, "setErrorMessage"], [671, 23, 699, 21], [671, 24, 699, 22], [671, 63, 699, 61], [671, 64, 699, 62], [672, 8, 700, 6, "setProcessingState"], [672, 26, 700, 24], [672, 27, 700, 25], [672, 34, 700, 32], [672, 35, 700, 33], [673, 8, 701, 6], [674, 6, 702, 4], [675, 6, 704, 4], [675, 10, 704, 8], [676, 8, 705, 6], [676, 14, 705, 12, "response"], [676, 22, 705, 20], [676, 25, 705, 23], [676, 31, 705, 29, "fetch"], [676, 36, 705, 34], [676, 37, 705, 35], [676, 40, 705, 38, "API_BASE_URL"], [676, 52, 705, 50], [676, 75, 705, 73, "jobId"], [676, 80, 705, 78], [676, 82, 705, 80], [676, 84, 705, 82], [677, 10, 706, 8, "headers"], [677, 17, 706, 15], [677, 19, 706, 17], [678, 12, 707, 10], [678, 27, 707, 25], [678, 29, 707, 27], [678, 39, 707, 37], [678, 45, 707, 43, "getAuthToken"], [678, 57, 707, 55], [678, 58, 707, 56], [678, 59, 707, 57], [679, 10, 708, 8], [680, 8, 709, 6], [680, 9, 709, 7], [680, 10, 709, 8], [681, 8, 711, 6], [681, 12, 711, 10], [681, 13, 711, 11, "response"], [681, 21, 711, 19], [681, 22, 711, 20, "ok"], [681, 24, 711, 22], [681, 26, 711, 24], [682, 10, 712, 8], [682, 16, 712, 14], [682, 20, 712, 18, "Error"], [682, 25, 712, 23], [682, 26, 712, 24], [682, 34, 712, 32, "response"], [682, 42, 712, 40], [682, 43, 712, 41, "status"], [682, 49, 712, 47], [682, 54, 712, 52, "response"], [682, 62, 712, 60], [682, 63, 712, 61, "statusText"], [682, 73, 712, 71], [682, 75, 712, 73], [682, 76, 712, 74], [683, 8, 713, 6], [684, 8, 715, 6], [684, 14, 715, 12, "status"], [684, 20, 715, 18], [684, 23, 715, 21], [684, 29, 715, 27, "response"], [684, 37, 715, 35], [684, 38, 715, 36, "json"], [684, 42, 715, 40], [684, 43, 715, 41], [684, 44, 715, 42], [685, 8, 716, 6, "console"], [685, 15, 716, 13], [685, 16, 716, 14, "log"], [685, 19, 716, 17], [685, 20, 716, 18], [685, 54, 716, 52], [685, 56, 716, 54, "status"], [685, 62, 716, 60], [685, 63, 716, 61], [686, 8, 718, 6], [686, 12, 718, 10, "status"], [686, 18, 718, 16], [686, 19, 718, 17, "status"], [686, 25, 718, 23], [686, 30, 718, 28], [686, 41, 718, 39], [686, 43, 718, 41], [687, 10, 719, 8, "console"], [687, 17, 719, 15], [687, 18, 719, 16, "log"], [687, 21, 719, 19], [687, 22, 719, 20], [687, 73, 719, 71], [687, 74, 719, 72], [688, 10, 720, 8, "setProcessingProgress"], [688, 31, 720, 29], [688, 32, 720, 30], [688, 35, 720, 33], [688, 36, 720, 34], [689, 10, 721, 8, "setProcessingState"], [689, 28, 721, 26], [689, 29, 721, 27], [689, 40, 721, 38], [689, 41, 721, 39], [690, 10, 722, 8], [691, 10, 723, 8], [691, 16, 723, 14, "result"], [691, 22, 723, 20], [691, 25, 723, 23], [692, 12, 724, 10, "imageUrl"], [692, 20, 724, 18], [692, 22, 724, 20, "status"], [692, 28, 724, 26], [692, 29, 724, 27, "publicUrl"], [692, 38, 724, 36], [693, 12, 724, 38], [694, 12, 725, 10, "localUri"], [694, 20, 725, 18], [694, 22, 725, 20, "capturedPhoto"], [694, 35, 725, 33], [694, 39, 725, 37, "status"], [694, 45, 725, 43], [694, 46, 725, 44, "publicUrl"], [694, 55, 725, 53], [695, 12, 725, 55], [696, 12, 726, 10, "challengeCode"], [696, 25, 726, 23], [696, 27, 726, 25, "challengeCode"], [696, 40, 726, 38], [696, 44, 726, 42], [696, 46, 726, 44], [697, 12, 727, 10, "timestamp"], [697, 21, 727, 19], [698, 12, 728, 10, "processingStatus"], [698, 28, 728, 26], [698, 30, 728, 28], [699, 10, 729, 8], [699, 11, 729, 9], [700, 10, 730, 8, "console"], [700, 17, 730, 15], [700, 18, 730, 16, "log"], [700, 21, 730, 19], [700, 22, 730, 20], [700, 57, 730, 55], [700, 59, 730, 57, "result"], [700, 65, 730, 63], [700, 66, 730, 64], [701, 10, 731, 8, "onComplete"], [701, 20, 731, 18], [701, 21, 731, 19, "result"], [701, 27, 731, 25], [701, 28, 731, 26], [702, 10, 732, 8], [703, 8, 733, 6], [703, 9, 733, 7], [703, 15, 733, 13], [703, 19, 733, 17, "status"], [703, 25, 733, 23], [703, 26, 733, 24, "status"], [703, 32, 733, 30], [703, 37, 733, 35], [703, 45, 733, 43], [703, 47, 733, 45], [704, 10, 734, 8, "console"], [704, 17, 734, 15], [704, 18, 734, 16, "error"], [704, 23, 734, 21], [704, 24, 734, 22], [704, 60, 734, 58], [704, 62, 734, 60, "status"], [704, 68, 734, 66], [704, 69, 734, 67, "error"], [704, 74, 734, 72], [704, 75, 734, 73], [705, 10, 735, 8], [705, 16, 735, 14], [705, 20, 735, 18, "Error"], [705, 25, 735, 23], [705, 26, 735, 24, "status"], [705, 32, 735, 30], [705, 33, 735, 31, "error"], [705, 38, 735, 36], [705, 42, 735, 40], [705, 61, 735, 59], [705, 62, 735, 60], [706, 8, 736, 6], [706, 9, 736, 7], [706, 15, 736, 13], [707, 10, 737, 8], [708, 10, 738, 8], [708, 16, 738, 14, "progressValue"], [708, 29, 738, 27], [708, 32, 738, 30], [708, 34, 738, 32], [708, 37, 738, 36, "attempts"], [708, 45, 738, 44], [708, 48, 738, 47, "MAX_ATTEMPTS"], [708, 60, 738, 59], [708, 63, 738, 63], [708, 65, 738, 65], [709, 10, 739, 8, "console"], [709, 17, 739, 15], [709, 18, 739, 16, "log"], [709, 21, 739, 19], [709, 22, 739, 20], [709, 71, 739, 69, "progressValue"], [709, 84, 739, 82], [709, 87, 739, 85], [709, 88, 739, 86], [710, 10, 740, 8, "setProcessingProgress"], [710, 31, 740, 29], [710, 32, 740, 30, "progressValue"], [710, 45, 740, 43], [710, 46, 740, 44], [711, 10, 742, 8, "setTimeout"], [711, 20, 742, 18], [711, 21, 742, 19], [711, 27, 742, 25], [712, 12, 743, 10, "pollForCompletion"], [712, 29, 743, 27], [712, 30, 743, 28, "jobId"], [712, 35, 743, 33], [712, 37, 743, 35, "timestamp"], [712, 46, 743, 44], [712, 48, 743, 46, "attempts"], [712, 56, 743, 54], [712, 59, 743, 57], [712, 60, 743, 58], [712, 61, 743, 59], [713, 10, 744, 8], [713, 11, 744, 9], [713, 13, 744, 11, "POLL_INTERVAL"], [713, 26, 744, 24], [713, 27, 744, 25], [714, 8, 745, 6], [715, 6, 746, 4], [715, 7, 746, 5], [715, 8, 746, 6], [715, 15, 746, 13, "error"], [715, 20, 746, 18], [715, 22, 746, 20], [716, 8, 747, 6, "console"], [716, 15, 747, 13], [716, 16, 747, 14, "error"], [716, 21, 747, 19], [716, 22, 747, 20], [716, 54, 747, 52], [716, 56, 747, 54, "error"], [716, 61, 747, 59], [716, 62, 747, 60], [717, 8, 748, 6, "setErrorMessage"], [717, 23, 748, 21], [717, 24, 748, 22], [717, 62, 748, 60, "error"], [717, 67, 748, 65], [717, 68, 748, 66, "message"], [717, 75, 748, 73], [717, 77, 748, 75], [717, 78, 748, 76], [718, 8, 749, 6, "setProcessingState"], [718, 26, 749, 24], [718, 27, 749, 25], [718, 34, 749, 32], [718, 35, 749, 33], [719, 6, 750, 4], [720, 4, 751, 2], [720, 5, 751, 3], [721, 4, 752, 2], [722, 4, 753, 2], [722, 10, 753, 8, "getAuthToken"], [722, 22, 753, 20], [722, 25, 753, 23], [722, 31, 753, 23, "getAuthToken"], [722, 32, 753, 23], [722, 37, 753, 52], [723, 6, 754, 4], [724, 6, 755, 4], [725, 6, 756, 4], [725, 13, 756, 11], [725, 30, 756, 28], [726, 4, 757, 2], [726, 5, 757, 3], [728, 4, 759, 2], [729, 4, 760, 2], [729, 10, 760, 8, "retryCapture"], [729, 22, 760, 20], [729, 25, 760, 23], [729, 29, 760, 23, "useCallback"], [729, 47, 760, 34], [729, 49, 760, 35], [729, 55, 760, 41], [730, 6, 761, 4, "console"], [730, 13, 761, 11], [730, 14, 761, 12, "log"], [730, 17, 761, 15], [730, 18, 761, 16], [730, 55, 761, 53], [730, 56, 761, 54], [731, 6, 762, 4, "setProcessingState"], [731, 24, 762, 22], [731, 25, 762, 23], [731, 31, 762, 29], [731, 32, 762, 30], [732, 6, 763, 4, "setErrorMessage"], [732, 21, 763, 19], [732, 22, 763, 20], [732, 24, 763, 22], [732, 25, 763, 23], [733, 6, 764, 4, "setCapturedPhoto"], [733, 22, 764, 20], [733, 23, 764, 21], [733, 25, 764, 23], [733, 26, 764, 24], [734, 6, 765, 4, "setProcessingProgress"], [734, 27, 765, 25], [734, 28, 765, 26], [734, 29, 765, 27], [734, 30, 765, 28], [735, 4, 766, 2], [735, 5, 766, 3], [735, 7, 766, 5], [735, 9, 766, 7], [735, 10, 766, 8], [736, 4, 767, 2], [737, 4, 768, 2], [737, 8, 768, 2, "useEffect"], [737, 24, 768, 11], [737, 26, 768, 12], [737, 32, 768, 18], [738, 6, 769, 4, "console"], [738, 13, 769, 11], [738, 14, 769, 12, "log"], [738, 17, 769, 15], [738, 18, 769, 16], [738, 53, 769, 51], [738, 55, 769, 53, "permission"], [738, 65, 769, 63], [738, 66, 769, 64], [739, 6, 770, 4], [739, 10, 770, 8, "permission"], [739, 20, 770, 18], [739, 22, 770, 20], [740, 8, 771, 6, "console"], [740, 15, 771, 13], [740, 16, 771, 14, "log"], [740, 19, 771, 17], [740, 20, 771, 18], [740, 57, 771, 55], [740, 59, 771, 57, "permission"], [740, 69, 771, 67], [740, 70, 771, 68, "granted"], [740, 77, 771, 75], [740, 78, 771, 76], [741, 6, 772, 4], [742, 4, 773, 2], [742, 5, 773, 3], [742, 7, 773, 5], [742, 8, 773, 6, "permission"], [742, 18, 773, 16], [742, 19, 773, 17], [742, 20, 773, 18], [743, 4, 774, 2], [744, 4, 775, 2], [744, 8, 775, 6], [744, 9, 775, 7, "permission"], [744, 19, 775, 17], [744, 21, 775, 19], [745, 6, 776, 4, "console"], [745, 13, 776, 11], [745, 14, 776, 12, "log"], [745, 17, 776, 15], [745, 18, 776, 16], [745, 67, 776, 65], [745, 68, 776, 66], [746, 6, 777, 4], [746, 26, 778, 6], [746, 30, 778, 6, "_jsxDevRuntime"], [746, 44, 778, 6], [746, 45, 778, 6, "jsxDEV"], [746, 51, 778, 6], [746, 53, 778, 7, "_View"], [746, 58, 778, 7], [746, 59, 778, 7, "default"], [746, 66, 778, 11], [747, 8, 778, 12, "style"], [747, 13, 778, 17], [747, 15, 778, 19, "styles"], [747, 21, 778, 25], [747, 22, 778, 26, "container"], [747, 31, 778, 36], [748, 8, 778, 36, "children"], [748, 16, 778, 36], [748, 32, 779, 8], [748, 36, 779, 8, "_jsxDevRuntime"], [748, 50, 779, 8], [748, 51, 779, 8, "jsxDEV"], [748, 57, 779, 8], [748, 59, 779, 9, "_ActivityIndicator"], [748, 77, 779, 9], [748, 78, 779, 9, "default"], [748, 85, 779, 26], [749, 10, 779, 27, "size"], [749, 14, 779, 31], [749, 16, 779, 32], [749, 23, 779, 39], [750, 10, 779, 40, "color"], [750, 15, 779, 45], [750, 17, 779, 46], [751, 8, 779, 55], [752, 10, 779, 55, "fileName"], [752, 18, 779, 55], [752, 20, 779, 55, "_jsxFileName"], [752, 32, 779, 55], [753, 10, 779, 55, "lineNumber"], [753, 20, 779, 55], [754, 10, 779, 55, "columnNumber"], [754, 22, 779, 55], [755, 8, 779, 55], [755, 15, 779, 57], [755, 16, 779, 58], [755, 31, 780, 8], [755, 35, 780, 8, "_jsxDevRuntime"], [755, 49, 780, 8], [755, 50, 780, 8, "jsxDEV"], [755, 56, 780, 8], [755, 58, 780, 9, "_Text"], [755, 63, 780, 9], [755, 64, 780, 9, "default"], [755, 71, 780, 13], [756, 10, 780, 14, "style"], [756, 15, 780, 19], [756, 17, 780, 21, "styles"], [756, 23, 780, 27], [756, 24, 780, 28, "loadingText"], [756, 35, 780, 40], [757, 10, 780, 40, "children"], [757, 18, 780, 40], [757, 20, 780, 41], [758, 8, 780, 58], [759, 10, 780, 58, "fileName"], [759, 18, 780, 58], [759, 20, 780, 58, "_jsxFileName"], [759, 32, 780, 58], [760, 10, 780, 58, "lineNumber"], [760, 20, 780, 58], [761, 10, 780, 58, "columnNumber"], [761, 22, 780, 58], [762, 8, 780, 58], [762, 15, 780, 64], [762, 16, 780, 65], [763, 6, 780, 65], [764, 8, 780, 65, "fileName"], [764, 16, 780, 65], [764, 18, 780, 65, "_jsxFileName"], [764, 30, 780, 65], [765, 8, 780, 65, "lineNumber"], [765, 18, 780, 65], [766, 8, 780, 65, "columnNumber"], [766, 20, 780, 65], [767, 6, 780, 65], [767, 13, 781, 12], [767, 14, 781, 13], [768, 4, 783, 2], [769, 4, 784, 2], [769, 8, 784, 6], [769, 9, 784, 7, "permission"], [769, 19, 784, 17], [769, 20, 784, 18, "granted"], [769, 27, 784, 25], [769, 29, 784, 27], [770, 6, 785, 4, "console"], [770, 13, 785, 11], [770, 14, 785, 12, "log"], [770, 17, 785, 15], [770, 18, 785, 16], [770, 93, 785, 91], [770, 94, 785, 92], [771, 6, 786, 4], [771, 26, 787, 6], [771, 30, 787, 6, "_jsxDevRuntime"], [771, 44, 787, 6], [771, 45, 787, 6, "jsxDEV"], [771, 51, 787, 6], [771, 53, 787, 7, "_View"], [771, 58, 787, 7], [771, 59, 787, 7, "default"], [771, 66, 787, 11], [772, 8, 787, 12, "style"], [772, 13, 787, 17], [772, 15, 787, 19, "styles"], [772, 21, 787, 25], [772, 22, 787, 26, "container"], [772, 31, 787, 36], [773, 8, 787, 36, "children"], [773, 16, 787, 36], [773, 31, 788, 8], [773, 35, 788, 8, "_jsxDevRuntime"], [773, 49, 788, 8], [773, 50, 788, 8, "jsxDEV"], [773, 56, 788, 8], [773, 58, 788, 9, "_View"], [773, 63, 788, 9], [773, 64, 788, 9, "default"], [773, 71, 788, 13], [774, 10, 788, 14, "style"], [774, 15, 788, 19], [774, 17, 788, 21, "styles"], [774, 23, 788, 27], [774, 24, 788, 28, "permissionContent"], [774, 41, 788, 46], [775, 10, 788, 46, "children"], [775, 18, 788, 46], [775, 34, 789, 10], [775, 38, 789, 10, "_jsxDevRuntime"], [775, 52, 789, 10], [775, 53, 789, 10, "jsxDEV"], [775, 59, 789, 10], [775, 61, 789, 11, "_lucideReactNative"], [775, 79, 789, 11], [775, 80, 789, 11, "Camera"], [775, 86, 789, 21], [776, 12, 789, 22, "size"], [776, 16, 789, 26], [776, 18, 789, 28], [776, 20, 789, 31], [777, 12, 789, 32, "color"], [777, 17, 789, 37], [777, 19, 789, 38], [778, 10, 789, 47], [779, 12, 789, 47, "fileName"], [779, 20, 789, 47], [779, 22, 789, 47, "_jsxFileName"], [779, 34, 789, 47], [780, 12, 789, 47, "lineNumber"], [780, 22, 789, 47], [781, 12, 789, 47, "columnNumber"], [781, 24, 789, 47], [782, 10, 789, 47], [782, 17, 789, 49], [782, 18, 789, 50], [782, 33, 790, 10], [782, 37, 790, 10, "_jsxDevRuntime"], [782, 51, 790, 10], [782, 52, 790, 10, "jsxDEV"], [782, 58, 790, 10], [782, 60, 790, 11, "_Text"], [782, 65, 790, 11], [782, 66, 790, 11, "default"], [782, 73, 790, 15], [783, 12, 790, 16, "style"], [783, 17, 790, 21], [783, 19, 790, 23, "styles"], [783, 25, 790, 29], [783, 26, 790, 30, "permissionTitle"], [783, 41, 790, 46], [784, 12, 790, 46, "children"], [784, 20, 790, 46], [784, 22, 790, 47], [785, 10, 790, 73], [786, 12, 790, 73, "fileName"], [786, 20, 790, 73], [786, 22, 790, 73, "_jsxFileName"], [786, 34, 790, 73], [787, 12, 790, 73, "lineNumber"], [787, 22, 790, 73], [788, 12, 790, 73, "columnNumber"], [788, 24, 790, 73], [789, 10, 790, 73], [789, 17, 790, 79], [789, 18, 790, 80], [789, 33, 791, 10], [789, 37, 791, 10, "_jsxDevRuntime"], [789, 51, 791, 10], [789, 52, 791, 10, "jsxDEV"], [789, 58, 791, 10], [789, 60, 791, 11, "_Text"], [789, 65, 791, 11], [789, 66, 791, 11, "default"], [789, 73, 791, 15], [790, 12, 791, 16, "style"], [790, 17, 791, 21], [790, 19, 791, 23, "styles"], [790, 25, 791, 29], [790, 26, 791, 30, "permissionDescription"], [790, 47, 791, 52], [791, 12, 791, 52, "children"], [791, 20, 791, 52], [791, 22, 791, 53], [792, 10, 794, 10], [793, 12, 794, 10, "fileName"], [793, 20, 794, 10], [793, 22, 794, 10, "_jsxFileName"], [793, 34, 794, 10], [794, 12, 794, 10, "lineNumber"], [794, 22, 794, 10], [795, 12, 794, 10, "columnNumber"], [795, 24, 794, 10], [796, 10, 794, 10], [796, 17, 794, 16], [796, 18, 794, 17], [796, 33, 795, 10], [796, 37, 795, 10, "_jsxDevRuntime"], [796, 51, 795, 10], [796, 52, 795, 10, "jsxDEV"], [796, 58, 795, 10], [796, 60, 795, 11, "_TouchableOpacity"], [796, 77, 795, 11], [796, 78, 795, 11, "default"], [796, 85, 795, 27], [797, 12, 795, 28, "onPress"], [797, 19, 795, 35], [797, 21, 795, 37, "requestPermission"], [797, 38, 795, 55], [798, 12, 795, 56, "style"], [798, 17, 795, 61], [798, 19, 795, 63, "styles"], [798, 25, 795, 69], [798, 26, 795, 70, "primaryButton"], [798, 39, 795, 84], [799, 12, 795, 84, "children"], [799, 20, 795, 84], [799, 35, 796, 12], [799, 39, 796, 12, "_jsxDevRuntime"], [799, 53, 796, 12], [799, 54, 796, 12, "jsxDEV"], [799, 60, 796, 12], [799, 62, 796, 13, "_Text"], [799, 67, 796, 13], [799, 68, 796, 13, "default"], [799, 75, 796, 17], [800, 14, 796, 18, "style"], [800, 19, 796, 23], [800, 21, 796, 25, "styles"], [800, 27, 796, 31], [800, 28, 796, 32, "primaryButtonText"], [800, 45, 796, 50], [801, 14, 796, 50, "children"], [801, 22, 796, 50], [801, 24, 796, 51], [802, 12, 796, 67], [803, 14, 796, 67, "fileName"], [803, 22, 796, 67], [803, 24, 796, 67, "_jsxFileName"], [803, 36, 796, 67], [804, 14, 796, 67, "lineNumber"], [804, 24, 796, 67], [805, 14, 796, 67, "columnNumber"], [805, 26, 796, 67], [806, 12, 796, 67], [806, 19, 796, 73], [807, 10, 796, 74], [808, 12, 796, 74, "fileName"], [808, 20, 796, 74], [808, 22, 796, 74, "_jsxFileName"], [808, 34, 796, 74], [809, 12, 796, 74, "lineNumber"], [809, 22, 796, 74], [810, 12, 796, 74, "columnNumber"], [810, 24, 796, 74], [811, 10, 796, 74], [811, 17, 797, 28], [811, 18, 797, 29], [811, 33, 798, 10], [811, 37, 798, 10, "_jsxDevRuntime"], [811, 51, 798, 10], [811, 52, 798, 10, "jsxDEV"], [811, 58, 798, 10], [811, 60, 798, 11, "_TouchableOpacity"], [811, 77, 798, 11], [811, 78, 798, 11, "default"], [811, 85, 798, 27], [812, 12, 798, 28, "onPress"], [812, 19, 798, 35], [812, 21, 798, 37, "onCancel"], [812, 29, 798, 46], [813, 12, 798, 47, "style"], [813, 17, 798, 52], [813, 19, 798, 54, "styles"], [813, 25, 798, 60], [813, 26, 798, 61, "secondaryButton"], [813, 41, 798, 77], [814, 12, 798, 77, "children"], [814, 20, 798, 77], [814, 35, 799, 12], [814, 39, 799, 12, "_jsxDevRuntime"], [814, 53, 799, 12], [814, 54, 799, 12, "jsxDEV"], [814, 60, 799, 12], [814, 62, 799, 13, "_Text"], [814, 67, 799, 13], [814, 68, 799, 13, "default"], [814, 75, 799, 17], [815, 14, 799, 18, "style"], [815, 19, 799, 23], [815, 21, 799, 25, "styles"], [815, 27, 799, 31], [815, 28, 799, 32, "secondaryButtonText"], [815, 47, 799, 52], [816, 14, 799, 52, "children"], [816, 22, 799, 52], [816, 24, 799, 53], [817, 12, 799, 59], [818, 14, 799, 59, "fileName"], [818, 22, 799, 59], [818, 24, 799, 59, "_jsxFileName"], [818, 36, 799, 59], [819, 14, 799, 59, "lineNumber"], [819, 24, 799, 59], [820, 14, 799, 59, "columnNumber"], [820, 26, 799, 59], [821, 12, 799, 59], [821, 19, 799, 65], [822, 10, 799, 66], [823, 12, 799, 66, "fileName"], [823, 20, 799, 66], [823, 22, 799, 66, "_jsxFileName"], [823, 34, 799, 66], [824, 12, 799, 66, "lineNumber"], [824, 22, 799, 66], [825, 12, 799, 66, "columnNumber"], [825, 24, 799, 66], [826, 10, 799, 66], [826, 17, 800, 28], [826, 18, 800, 29], [827, 8, 800, 29], [828, 10, 800, 29, "fileName"], [828, 18, 800, 29], [828, 20, 800, 29, "_jsxFileName"], [828, 32, 800, 29], [829, 10, 800, 29, "lineNumber"], [829, 20, 800, 29], [830, 10, 800, 29, "columnNumber"], [830, 22, 800, 29], [831, 8, 800, 29], [831, 15, 801, 14], [832, 6, 801, 15], [833, 8, 801, 15, "fileName"], [833, 16, 801, 15], [833, 18, 801, 15, "_jsxFileName"], [833, 30, 801, 15], [834, 8, 801, 15, "lineNumber"], [834, 18, 801, 15], [835, 8, 801, 15, "columnNumber"], [835, 20, 801, 15], [836, 6, 801, 15], [836, 13, 802, 12], [836, 14, 802, 13], [837, 4, 804, 2], [838, 4, 805, 2], [839, 4, 806, 2, "console"], [839, 11, 806, 9], [839, 12, 806, 10, "log"], [839, 15, 806, 13], [839, 16, 806, 14], [839, 55, 806, 53], [839, 56, 806, 54], [840, 4, 808, 2], [840, 24, 809, 4], [840, 28, 809, 4, "_jsxDevRuntime"], [840, 42, 809, 4], [840, 43, 809, 4, "jsxDEV"], [840, 49, 809, 4], [840, 51, 809, 5, "_View"], [840, 56, 809, 5], [840, 57, 809, 5, "default"], [840, 64, 809, 9], [841, 6, 809, 10, "style"], [841, 11, 809, 15], [841, 13, 809, 17, "styles"], [841, 19, 809, 23], [841, 20, 809, 24, "container"], [841, 29, 809, 34], [842, 6, 809, 34, "children"], [842, 14, 809, 34], [842, 30, 811, 6], [842, 34, 811, 6, "_jsxDevRuntime"], [842, 48, 811, 6], [842, 49, 811, 6, "jsxDEV"], [842, 55, 811, 6], [842, 57, 811, 7, "_View"], [842, 62, 811, 7], [842, 63, 811, 7, "default"], [842, 70, 811, 11], [843, 8, 811, 12, "style"], [843, 13, 811, 17], [843, 15, 811, 19, "styles"], [843, 21, 811, 25], [843, 22, 811, 26, "cameraContainer"], [843, 37, 811, 42], [844, 8, 811, 43, "id"], [844, 10, 811, 45], [844, 12, 811, 46], [844, 29, 811, 63], [845, 8, 811, 63, "children"], [845, 16, 811, 63], [845, 32, 812, 8], [845, 36, 812, 8, "_jsxDevRuntime"], [845, 50, 812, 8], [845, 51, 812, 8, "jsxDEV"], [845, 57, 812, 8], [845, 59, 812, 9, "_expoCamera"], [845, 70, 812, 9], [845, 71, 812, 9, "CameraView"], [845, 81, 812, 19], [846, 10, 813, 10, "ref"], [846, 13, 813, 13], [846, 15, 813, 15, "cameraRef"], [846, 24, 813, 25], [847, 10, 814, 10, "style"], [847, 15, 814, 15], [847, 17, 814, 17], [847, 18, 814, 18, "styles"], [847, 24, 814, 24], [847, 25, 814, 25, "camera"], [847, 31, 814, 31], [847, 33, 814, 33], [848, 12, 814, 35, "backgroundColor"], [848, 27, 814, 50], [848, 29, 814, 52], [849, 10, 814, 62], [849, 11, 814, 63], [849, 12, 814, 65], [850, 10, 815, 10, "facing"], [850, 16, 815, 16], [850, 18, 815, 17], [850, 24, 815, 23], [851, 10, 816, 10, "onLayout"], [851, 18, 816, 18], [851, 20, 816, 21, "e"], [851, 21, 816, 22], [851, 25, 816, 27], [852, 12, 817, 12, "console"], [852, 19, 817, 19], [852, 20, 817, 20, "log"], [852, 23, 817, 23], [852, 24, 817, 24], [852, 56, 817, 56], [852, 58, 817, 58, "e"], [852, 59, 817, 59], [852, 60, 817, 60, "nativeEvent"], [852, 71, 817, 71], [852, 72, 817, 72, "layout"], [852, 78, 817, 78], [852, 79, 817, 79], [853, 12, 818, 12, "setViewSize"], [853, 23, 818, 23], [853, 24, 818, 24], [854, 14, 818, 26, "width"], [854, 19, 818, 31], [854, 21, 818, 33, "e"], [854, 22, 818, 34], [854, 23, 818, 35, "nativeEvent"], [854, 34, 818, 46], [854, 35, 818, 47, "layout"], [854, 41, 818, 53], [854, 42, 818, 54, "width"], [854, 47, 818, 59], [855, 14, 818, 61, "height"], [855, 20, 818, 67], [855, 22, 818, 69, "e"], [855, 23, 818, 70], [855, 24, 818, 71, "nativeEvent"], [855, 35, 818, 82], [855, 36, 818, 83, "layout"], [855, 42, 818, 89], [855, 43, 818, 90, "height"], [856, 12, 818, 97], [856, 13, 818, 98], [856, 14, 818, 99], [857, 10, 819, 10], [857, 11, 819, 12], [858, 10, 820, 10, "onCameraReady"], [858, 23, 820, 23], [858, 25, 820, 25, "onCameraReady"], [858, 26, 820, 25], [858, 31, 820, 31], [859, 12, 821, 12, "console"], [859, 19, 821, 19], [859, 20, 821, 20, "log"], [859, 23, 821, 23], [859, 24, 821, 24], [859, 55, 821, 55], [859, 56, 821, 56], [860, 12, 822, 12, "setIsCameraReady"], [860, 28, 822, 28], [860, 29, 822, 29], [860, 33, 822, 33], [860, 34, 822, 34], [860, 35, 822, 35], [860, 36, 822, 36], [861, 10, 823, 10], [861, 11, 823, 12], [862, 10, 824, 10, "onMountError"], [862, 22, 824, 22], [862, 24, 824, 25, "error"], [862, 29, 824, 30], [862, 33, 824, 35], [863, 12, 825, 12, "console"], [863, 19, 825, 19], [863, 20, 825, 20, "error"], [863, 25, 825, 25], [863, 26, 825, 26], [863, 63, 825, 63], [863, 65, 825, 65, "error"], [863, 70, 825, 70], [863, 71, 825, 71], [864, 12, 826, 12, "setErrorMessage"], [864, 27, 826, 27], [864, 28, 826, 28], [864, 57, 826, 57], [864, 58, 826, 58], [865, 12, 827, 12, "setProcessingState"], [865, 30, 827, 30], [865, 31, 827, 31], [865, 38, 827, 38], [865, 39, 827, 39], [866, 10, 828, 10], [867, 8, 828, 12], [868, 10, 828, 12, "fileName"], [868, 18, 828, 12], [868, 20, 828, 12, "_jsxFileName"], [868, 32, 828, 12], [869, 10, 828, 12, "lineNumber"], [869, 20, 828, 12], [870, 10, 828, 12, "columnNumber"], [870, 22, 828, 12], [871, 8, 828, 12], [871, 15, 829, 9], [871, 16, 829, 10], [871, 18, 831, 9], [871, 19, 831, 10, "isCameraReady"], [871, 32, 831, 23], [871, 49, 832, 10], [871, 53, 832, 10, "_jsxDevRuntime"], [871, 67, 832, 10], [871, 68, 832, 10, "jsxDEV"], [871, 74, 832, 10], [871, 76, 832, 11, "_View"], [871, 81, 832, 11], [871, 82, 832, 11, "default"], [871, 89, 832, 15], [872, 10, 832, 16, "style"], [872, 15, 832, 21], [872, 17, 832, 23], [872, 18, 832, 24, "StyleSheet"], [872, 37, 832, 34], [872, 38, 832, 35, "absoluteFill"], [872, 50, 832, 47], [872, 52, 832, 49], [873, 12, 832, 51, "backgroundColor"], [873, 27, 832, 66], [873, 29, 832, 68], [873, 49, 832, 88], [874, 12, 832, 90, "justifyContent"], [874, 26, 832, 104], [874, 28, 832, 106], [874, 36, 832, 114], [875, 12, 832, 116, "alignItems"], [875, 22, 832, 126], [875, 24, 832, 128], [875, 32, 832, 136], [876, 12, 832, 138, "zIndex"], [876, 18, 832, 144], [876, 20, 832, 146], [877, 10, 832, 151], [877, 11, 832, 152], [877, 12, 832, 154], [878, 10, 832, 154, "children"], [878, 18, 832, 154], [878, 33, 833, 12], [878, 37, 833, 12, "_jsxDevRuntime"], [878, 51, 833, 12], [878, 52, 833, 12, "jsxDEV"], [878, 58, 833, 12], [878, 60, 833, 13, "_View"], [878, 65, 833, 13], [878, 66, 833, 13, "default"], [878, 73, 833, 17], [879, 12, 833, 18, "style"], [879, 17, 833, 23], [879, 19, 833, 25], [880, 14, 833, 27, "backgroundColor"], [880, 29, 833, 42], [880, 31, 833, 44], [880, 51, 833, 64], [881, 14, 833, 66, "padding"], [881, 21, 833, 73], [881, 23, 833, 75], [881, 25, 833, 77], [882, 14, 833, 79, "borderRadius"], [882, 26, 833, 91], [882, 28, 833, 93], [882, 30, 833, 95], [883, 14, 833, 97, "alignItems"], [883, 24, 833, 107], [883, 26, 833, 109], [884, 12, 833, 118], [884, 13, 833, 120], [885, 12, 833, 120, "children"], [885, 20, 833, 120], [885, 36, 834, 14], [885, 40, 834, 14, "_jsxDevRuntime"], [885, 54, 834, 14], [885, 55, 834, 14, "jsxDEV"], [885, 61, 834, 14], [885, 63, 834, 15, "_ActivityIndicator"], [885, 81, 834, 15], [885, 82, 834, 15, "default"], [885, 89, 834, 32], [886, 14, 834, 33, "size"], [886, 18, 834, 37], [886, 20, 834, 38], [886, 27, 834, 45], [887, 14, 834, 46, "color"], [887, 19, 834, 51], [887, 21, 834, 52], [887, 30, 834, 61], [888, 14, 834, 62, "style"], [888, 19, 834, 67], [888, 21, 834, 69], [889, 16, 834, 71, "marginBottom"], [889, 28, 834, 83], [889, 30, 834, 85], [890, 14, 834, 88], [891, 12, 834, 90], [892, 14, 834, 90, "fileName"], [892, 22, 834, 90], [892, 24, 834, 90, "_jsxFileName"], [892, 36, 834, 90], [893, 14, 834, 90, "lineNumber"], [893, 24, 834, 90], [894, 14, 834, 90, "columnNumber"], [894, 26, 834, 90], [895, 12, 834, 90], [895, 19, 834, 92], [895, 20, 834, 93], [895, 35, 835, 14], [895, 39, 835, 14, "_jsxDevRuntime"], [895, 53, 835, 14], [895, 54, 835, 14, "jsxDEV"], [895, 60, 835, 14], [895, 62, 835, 15, "_Text"], [895, 67, 835, 15], [895, 68, 835, 15, "default"], [895, 75, 835, 19], [896, 14, 835, 20, "style"], [896, 19, 835, 25], [896, 21, 835, 27], [897, 16, 835, 29, "color"], [897, 21, 835, 34], [897, 23, 835, 36], [897, 29, 835, 42], [898, 16, 835, 44, "fontSize"], [898, 24, 835, 52], [898, 26, 835, 54], [898, 28, 835, 56], [899, 16, 835, 58, "fontWeight"], [899, 26, 835, 68], [899, 28, 835, 70], [900, 14, 835, 76], [900, 15, 835, 78], [901, 14, 835, 78, "children"], [901, 22, 835, 78], [901, 24, 835, 79], [902, 12, 835, 101], [903, 14, 835, 101, "fileName"], [903, 22, 835, 101], [903, 24, 835, 101, "_jsxFileName"], [903, 36, 835, 101], [904, 14, 835, 101, "lineNumber"], [904, 24, 835, 101], [905, 14, 835, 101, "columnNumber"], [905, 26, 835, 101], [906, 12, 835, 101], [906, 19, 835, 107], [906, 20, 835, 108], [906, 35, 836, 14], [906, 39, 836, 14, "_jsxDevRuntime"], [906, 53, 836, 14], [906, 54, 836, 14, "jsxDEV"], [906, 60, 836, 14], [906, 62, 836, 15, "_Text"], [906, 67, 836, 15], [906, 68, 836, 15, "default"], [906, 75, 836, 19], [907, 14, 836, 20, "style"], [907, 19, 836, 25], [907, 21, 836, 27], [908, 16, 836, 29, "color"], [908, 21, 836, 34], [908, 23, 836, 36], [908, 32, 836, 45], [909, 16, 836, 47, "fontSize"], [909, 24, 836, 55], [909, 26, 836, 57], [909, 28, 836, 59], [910, 16, 836, 61, "marginTop"], [910, 25, 836, 70], [910, 27, 836, 72], [911, 14, 836, 74], [911, 15, 836, 76], [912, 14, 836, 76, "children"], [912, 22, 836, 76], [912, 24, 836, 77], [913, 12, 836, 88], [914, 14, 836, 88, "fileName"], [914, 22, 836, 88], [914, 24, 836, 88, "_jsxFileName"], [914, 36, 836, 88], [915, 14, 836, 88, "lineNumber"], [915, 24, 836, 88], [916, 14, 836, 88, "columnNumber"], [916, 26, 836, 88], [917, 12, 836, 88], [917, 19, 836, 94], [917, 20, 836, 95], [918, 10, 836, 95], [919, 12, 836, 95, "fileName"], [919, 20, 836, 95], [919, 22, 836, 95, "_jsxFileName"], [919, 34, 836, 95], [920, 12, 836, 95, "lineNumber"], [920, 22, 836, 95], [921, 12, 836, 95, "columnNumber"], [921, 24, 836, 95], [922, 10, 836, 95], [922, 17, 837, 18], [923, 8, 837, 19], [924, 10, 837, 19, "fileName"], [924, 18, 837, 19], [924, 20, 837, 19, "_jsxFileName"], [924, 32, 837, 19], [925, 10, 837, 19, "lineNumber"], [925, 20, 837, 19], [926, 10, 837, 19, "columnNumber"], [926, 22, 837, 19], [927, 8, 837, 19], [927, 15, 838, 16], [927, 16, 839, 9], [927, 18, 842, 9, "isCameraReady"], [927, 31, 842, 22], [927, 35, 842, 26, "previewBlurEnabled"], [927, 53, 842, 44], [927, 57, 842, 48, "viewSize"], [927, 65, 842, 56], [927, 66, 842, 57, "width"], [927, 71, 842, 62], [927, 74, 842, 65], [927, 75, 842, 66], [927, 92, 843, 10], [927, 96, 843, 10, "_jsxDevRuntime"], [927, 110, 843, 10], [927, 111, 843, 10, "jsxDEV"], [927, 117, 843, 10], [927, 119, 843, 10, "_jsxDevRuntime"], [927, 133, 843, 10], [927, 134, 843, 10, "Fragment"], [927, 142, 843, 10], [928, 10, 843, 10, "children"], [928, 18, 843, 10], [928, 34, 845, 12], [928, 38, 845, 12, "_jsxDevRuntime"], [928, 52, 845, 12], [928, 53, 845, 12, "jsxDEV"], [928, 59, 845, 12], [928, 61, 845, 13, "_LiveFaceCanvas"], [928, 76, 845, 13], [928, 77, 845, 13, "default"], [928, 84, 845, 27], [929, 12, 845, 28, "containerId"], [929, 23, 845, 39], [929, 25, 845, 40], [929, 42, 845, 57], [930, 12, 845, 58, "width"], [930, 17, 845, 63], [930, 19, 845, 65, "viewSize"], [930, 27, 845, 73], [930, 28, 845, 74, "width"], [930, 33, 845, 80], [931, 12, 845, 81, "height"], [931, 18, 845, 87], [931, 20, 845, 89, "viewSize"], [931, 28, 845, 97], [931, 29, 845, 98, "height"], [932, 10, 845, 105], [933, 12, 845, 105, "fileName"], [933, 20, 845, 105], [933, 22, 845, 105, "_jsxFileName"], [933, 34, 845, 105], [934, 12, 845, 105, "lineNumber"], [934, 22, 845, 105], [935, 12, 845, 105, "columnNumber"], [935, 24, 845, 105], [936, 10, 845, 105], [936, 17, 845, 107], [936, 18, 845, 108], [936, 33, 846, 12], [936, 37, 846, 12, "_jsxDevRuntime"], [936, 51, 846, 12], [936, 52, 846, 12, "jsxDEV"], [936, 58, 846, 12], [936, 60, 846, 13, "_View"], [936, 65, 846, 13], [936, 66, 846, 13, "default"], [936, 73, 846, 17], [937, 12, 846, 18, "style"], [937, 17, 846, 23], [937, 19, 846, 25], [937, 20, 846, 26, "StyleSheet"], [937, 39, 846, 36], [937, 40, 846, 37, "absoluteFill"], [937, 52, 846, 49], [937, 54, 846, 51], [938, 14, 846, 53, "pointerEvents"], [938, 27, 846, 66], [938, 29, 846, 68], [939, 12, 846, 75], [939, 13, 846, 76], [939, 14, 846, 78], [940, 12, 846, 78, "children"], [940, 20, 846, 78], [940, 36, 848, 12], [940, 40, 848, 12, "_jsxDevRuntime"], [940, 54, 848, 12], [940, 55, 848, 12, "jsxDEV"], [940, 61, 848, 12], [940, 63, 848, 13, "_expoBlur"], [940, 72, 848, 13], [940, 73, 848, 13, "BlurView"], [940, 81, 848, 21], [941, 14, 848, 22, "intensity"], [941, 23, 848, 31], [941, 25, 848, 33], [941, 27, 848, 36], [942, 14, 848, 37, "tint"], [942, 18, 848, 41], [942, 20, 848, 42], [942, 26, 848, 48], [943, 14, 848, 49, "style"], [943, 19, 848, 54], [943, 21, 848, 56], [943, 22, 848, 57, "styles"], [943, 28, 848, 63], [943, 29, 848, 64, "blurZone"], [943, 37, 848, 72], [943, 39, 848, 74], [944, 16, 849, 14, "left"], [944, 20, 849, 18], [944, 22, 849, 20], [944, 23, 849, 21], [945, 16, 850, 14, "top"], [945, 19, 850, 17], [945, 21, 850, 19, "viewSize"], [945, 29, 850, 27], [945, 30, 850, 28, "height"], [945, 36, 850, 34], [945, 39, 850, 37], [945, 42, 850, 40], [946, 16, 851, 14, "width"], [946, 21, 851, 19], [946, 23, 851, 21, "viewSize"], [946, 31, 851, 29], [946, 32, 851, 30, "width"], [946, 37, 851, 35], [947, 16, 852, 14, "height"], [947, 22, 852, 20], [947, 24, 852, 22, "viewSize"], [947, 32, 852, 30], [947, 33, 852, 31, "height"], [947, 39, 852, 37], [947, 42, 852, 40], [947, 46, 852, 44], [948, 16, 853, 14, "borderRadius"], [948, 28, 853, 26], [948, 30, 853, 28], [949, 14, 854, 12], [949, 15, 854, 13], [950, 12, 854, 15], [951, 14, 854, 15, "fileName"], [951, 22, 854, 15], [951, 24, 854, 15, "_jsxFileName"], [951, 36, 854, 15], [952, 14, 854, 15, "lineNumber"], [952, 24, 854, 15], [953, 14, 854, 15, "columnNumber"], [953, 26, 854, 15], [954, 12, 854, 15], [954, 19, 854, 17], [954, 20, 854, 18], [954, 35, 856, 12], [954, 39, 856, 12, "_jsxDevRuntime"], [954, 53, 856, 12], [954, 54, 856, 12, "jsxDEV"], [954, 60, 856, 12], [954, 62, 856, 13, "_expoBlur"], [954, 71, 856, 13], [954, 72, 856, 13, "BlurView"], [954, 80, 856, 21], [955, 14, 856, 22, "intensity"], [955, 23, 856, 31], [955, 25, 856, 33], [955, 27, 856, 36], [956, 14, 856, 37, "tint"], [956, 18, 856, 41], [956, 20, 856, 42], [956, 26, 856, 48], [957, 14, 856, 49, "style"], [957, 19, 856, 54], [957, 21, 856, 56], [957, 22, 856, 57, "styles"], [957, 28, 856, 63], [957, 29, 856, 64, "blurZone"], [957, 37, 856, 72], [957, 39, 856, 74], [958, 16, 857, 14, "left"], [958, 20, 857, 18], [958, 22, 857, 20], [958, 23, 857, 21], [959, 16, 858, 14, "top"], [959, 19, 858, 17], [959, 21, 858, 19], [959, 22, 858, 20], [960, 16, 859, 14, "width"], [960, 21, 859, 19], [960, 23, 859, 21, "viewSize"], [960, 31, 859, 29], [960, 32, 859, 30, "width"], [960, 37, 859, 35], [961, 16, 860, 14, "height"], [961, 22, 860, 20], [961, 24, 860, 22, "viewSize"], [961, 32, 860, 30], [961, 33, 860, 31, "height"], [961, 39, 860, 37], [961, 42, 860, 40], [961, 45, 860, 43], [962, 16, 861, 14, "borderRadius"], [962, 28, 861, 26], [962, 30, 861, 28], [963, 14, 862, 12], [963, 15, 862, 13], [964, 12, 862, 15], [965, 14, 862, 15, "fileName"], [965, 22, 862, 15], [965, 24, 862, 15, "_jsxFileName"], [965, 36, 862, 15], [966, 14, 862, 15, "lineNumber"], [966, 24, 862, 15], [967, 14, 862, 15, "columnNumber"], [967, 26, 862, 15], [968, 12, 862, 15], [968, 19, 862, 17], [968, 20, 862, 18], [968, 35, 864, 12], [968, 39, 864, 12, "_jsxDevRuntime"], [968, 53, 864, 12], [968, 54, 864, 12, "jsxDEV"], [968, 60, 864, 12], [968, 62, 864, 13, "_expoBlur"], [968, 71, 864, 13], [968, 72, 864, 13, "BlurView"], [968, 80, 864, 21], [969, 14, 864, 22, "intensity"], [969, 23, 864, 31], [969, 25, 864, 33], [969, 27, 864, 36], [970, 14, 864, 37, "tint"], [970, 18, 864, 41], [970, 20, 864, 42], [970, 26, 864, 48], [971, 14, 864, 49, "style"], [971, 19, 864, 54], [971, 21, 864, 56], [971, 22, 864, 57, "styles"], [971, 28, 864, 63], [971, 29, 864, 64, "blurZone"], [971, 37, 864, 72], [971, 39, 864, 74], [972, 16, 865, 14, "left"], [972, 20, 865, 18], [972, 22, 865, 20, "viewSize"], [972, 30, 865, 28], [972, 31, 865, 29, "width"], [972, 36, 865, 34], [972, 39, 865, 37], [972, 42, 865, 40], [972, 45, 865, 44, "viewSize"], [972, 53, 865, 52], [972, 54, 865, 53, "width"], [972, 59, 865, 58], [972, 62, 865, 61], [972, 66, 865, 66], [973, 16, 866, 14, "top"], [973, 19, 866, 17], [973, 21, 866, 19, "viewSize"], [973, 29, 866, 27], [973, 30, 866, 28, "height"], [973, 36, 866, 34], [973, 39, 866, 37], [973, 43, 866, 41], [973, 46, 866, 45, "viewSize"], [973, 54, 866, 53], [973, 55, 866, 54, "width"], [973, 60, 866, 59], [973, 63, 866, 62], [973, 67, 866, 67], [974, 16, 867, 14, "width"], [974, 21, 867, 19], [974, 23, 867, 21, "viewSize"], [974, 31, 867, 29], [974, 32, 867, 30, "width"], [974, 37, 867, 35], [974, 40, 867, 38], [974, 43, 867, 41], [975, 16, 868, 14, "height"], [975, 22, 868, 20], [975, 24, 868, 22, "viewSize"], [975, 32, 868, 30], [975, 33, 868, 31, "width"], [975, 38, 868, 36], [975, 41, 868, 39], [975, 44, 868, 42], [976, 16, 869, 14, "borderRadius"], [976, 28, 869, 26], [976, 30, 869, 29, "viewSize"], [976, 38, 869, 37], [976, 39, 869, 38, "width"], [976, 44, 869, 43], [976, 47, 869, 46], [976, 50, 869, 49], [976, 53, 869, 53], [977, 14, 870, 12], [977, 15, 870, 13], [978, 12, 870, 15], [979, 14, 870, 15, "fileName"], [979, 22, 870, 15], [979, 24, 870, 15, "_jsxFileName"], [979, 36, 870, 15], [980, 14, 870, 15, "lineNumber"], [980, 24, 870, 15], [981, 14, 870, 15, "columnNumber"], [981, 26, 870, 15], [982, 12, 870, 15], [982, 19, 870, 17], [982, 20, 870, 18], [982, 35, 871, 12], [982, 39, 871, 12, "_jsxDevRuntime"], [982, 53, 871, 12], [982, 54, 871, 12, "jsxDEV"], [982, 60, 871, 12], [982, 62, 871, 13, "_expoBlur"], [982, 71, 871, 13], [982, 72, 871, 13, "BlurView"], [982, 80, 871, 21], [983, 14, 871, 22, "intensity"], [983, 23, 871, 31], [983, 25, 871, 33], [983, 27, 871, 36], [984, 14, 871, 37, "tint"], [984, 18, 871, 41], [984, 20, 871, 42], [984, 26, 871, 48], [985, 14, 871, 49, "style"], [985, 19, 871, 54], [985, 21, 871, 56], [985, 22, 871, 57, "styles"], [985, 28, 871, 63], [985, 29, 871, 64, "blurZone"], [985, 37, 871, 72], [985, 39, 871, 74], [986, 16, 872, 14, "left"], [986, 20, 872, 18], [986, 22, 872, 20, "viewSize"], [986, 30, 872, 28], [986, 31, 872, 29, "width"], [986, 36, 872, 34], [986, 39, 872, 37], [986, 42, 872, 40], [986, 45, 872, 44, "viewSize"], [986, 53, 872, 52], [986, 54, 872, 53, "width"], [986, 59, 872, 58], [986, 62, 872, 61], [986, 66, 872, 66], [987, 16, 873, 14, "top"], [987, 19, 873, 17], [987, 21, 873, 19, "viewSize"], [987, 29, 873, 27], [987, 30, 873, 28, "height"], [987, 36, 873, 34], [987, 39, 873, 37], [987, 42, 873, 40], [987, 45, 873, 44, "viewSize"], [987, 53, 873, 52], [987, 54, 873, 53, "width"], [987, 59, 873, 58], [987, 62, 873, 61], [987, 66, 873, 66], [988, 16, 874, 14, "width"], [988, 21, 874, 19], [988, 23, 874, 21, "viewSize"], [988, 31, 874, 29], [988, 32, 874, 30, "width"], [988, 37, 874, 35], [988, 40, 874, 38], [988, 43, 874, 41], [989, 16, 875, 14, "height"], [989, 22, 875, 20], [989, 24, 875, 22, "viewSize"], [989, 32, 875, 30], [989, 33, 875, 31, "width"], [989, 38, 875, 36], [989, 41, 875, 39], [989, 44, 875, 42], [990, 16, 876, 14, "borderRadius"], [990, 28, 876, 26], [990, 30, 876, 29, "viewSize"], [990, 38, 876, 37], [990, 39, 876, 38, "width"], [990, 44, 876, 43], [990, 47, 876, 46], [990, 50, 876, 49], [990, 53, 876, 53], [991, 14, 877, 12], [991, 15, 877, 13], [992, 12, 877, 15], [993, 14, 877, 15, "fileName"], [993, 22, 877, 15], [993, 24, 877, 15, "_jsxFileName"], [993, 36, 877, 15], [994, 14, 877, 15, "lineNumber"], [994, 24, 877, 15], [995, 14, 877, 15, "columnNumber"], [995, 26, 877, 15], [996, 12, 877, 15], [996, 19, 877, 17], [996, 20, 877, 18], [996, 35, 878, 12], [996, 39, 878, 12, "_jsxDevRuntime"], [996, 53, 878, 12], [996, 54, 878, 12, "jsxDEV"], [996, 60, 878, 12], [996, 62, 878, 13, "_expoBlur"], [996, 71, 878, 13], [996, 72, 878, 13, "BlurView"], [996, 80, 878, 21], [997, 14, 878, 22, "intensity"], [997, 23, 878, 31], [997, 25, 878, 33], [997, 27, 878, 36], [998, 14, 878, 37, "tint"], [998, 18, 878, 41], [998, 20, 878, 42], [998, 26, 878, 48], [999, 14, 878, 49, "style"], [999, 19, 878, 54], [999, 21, 878, 56], [999, 22, 878, 57, "styles"], [999, 28, 878, 63], [999, 29, 878, 64, "blurZone"], [999, 37, 878, 72], [999, 39, 878, 74], [1000, 16, 879, 14, "left"], [1000, 20, 879, 18], [1000, 22, 879, 20, "viewSize"], [1000, 30, 879, 28], [1000, 31, 879, 29, "width"], [1000, 36, 879, 34], [1000, 39, 879, 37], [1000, 42, 879, 40], [1000, 45, 879, 44, "viewSize"], [1000, 53, 879, 52], [1000, 54, 879, 53, "width"], [1000, 59, 879, 58], [1000, 62, 879, 61], [1000, 66, 879, 66], [1001, 16, 880, 14, "top"], [1001, 19, 880, 17], [1001, 21, 880, 19, "viewSize"], [1001, 29, 880, 27], [1001, 30, 880, 28, "height"], [1001, 36, 880, 34], [1001, 39, 880, 37], [1001, 42, 880, 40], [1001, 45, 880, 44, "viewSize"], [1001, 53, 880, 52], [1001, 54, 880, 53, "width"], [1001, 59, 880, 58], [1001, 62, 880, 61], [1001, 66, 880, 66], [1002, 16, 881, 14, "width"], [1002, 21, 881, 19], [1002, 23, 881, 21, "viewSize"], [1002, 31, 881, 29], [1002, 32, 881, 30, "width"], [1002, 37, 881, 35], [1002, 40, 881, 38], [1002, 43, 881, 41], [1003, 16, 882, 14, "height"], [1003, 22, 882, 20], [1003, 24, 882, 22, "viewSize"], [1003, 32, 882, 30], [1003, 33, 882, 31, "width"], [1003, 38, 882, 36], [1003, 41, 882, 39], [1003, 44, 882, 42], [1004, 16, 883, 14, "borderRadius"], [1004, 28, 883, 26], [1004, 30, 883, 29, "viewSize"], [1004, 38, 883, 37], [1004, 39, 883, 38, "width"], [1004, 44, 883, 43], [1004, 47, 883, 46], [1004, 50, 883, 49], [1004, 53, 883, 53], [1005, 14, 884, 12], [1005, 15, 884, 13], [1006, 12, 884, 15], [1007, 14, 884, 15, "fileName"], [1007, 22, 884, 15], [1007, 24, 884, 15, "_jsxFileName"], [1007, 36, 884, 15], [1008, 14, 884, 15, "lineNumber"], [1008, 24, 884, 15], [1009, 14, 884, 15, "columnNumber"], [1009, 26, 884, 15], [1010, 12, 884, 15], [1010, 19, 884, 17], [1010, 20, 884, 18], [1010, 22, 886, 13, "__DEV__"], [1010, 29, 886, 20], [1010, 46, 887, 14], [1010, 50, 887, 14, "_jsxDevRuntime"], [1010, 64, 887, 14], [1010, 65, 887, 14, "jsxDEV"], [1010, 71, 887, 14], [1010, 73, 887, 15, "_View"], [1010, 78, 887, 15], [1010, 79, 887, 15, "default"], [1010, 86, 887, 19], [1011, 14, 887, 20, "style"], [1011, 19, 887, 25], [1011, 21, 887, 27, "styles"], [1011, 27, 887, 33], [1011, 28, 887, 34, "previewChip"], [1011, 39, 887, 46], [1012, 14, 887, 46, "children"], [1012, 22, 887, 46], [1012, 37, 888, 16], [1012, 41, 888, 16, "_jsxDevRuntime"], [1012, 55, 888, 16], [1012, 56, 888, 16, "jsxDEV"], [1012, 62, 888, 16], [1012, 64, 888, 17, "_Text"], [1012, 69, 888, 17], [1012, 70, 888, 17, "default"], [1012, 77, 888, 21], [1013, 16, 888, 22, "style"], [1013, 21, 888, 27], [1013, 23, 888, 29, "styles"], [1013, 29, 888, 35], [1013, 30, 888, 36, "previewChipText"], [1013, 45, 888, 52], [1014, 16, 888, 52, "children"], [1014, 24, 888, 52], [1014, 26, 888, 53], [1015, 14, 888, 73], [1016, 16, 888, 73, "fileName"], [1016, 24, 888, 73], [1016, 26, 888, 73, "_jsxFileName"], [1016, 38, 888, 73], [1017, 16, 888, 73, "lineNumber"], [1017, 26, 888, 73], [1018, 16, 888, 73, "columnNumber"], [1018, 28, 888, 73], [1019, 14, 888, 73], [1019, 21, 888, 79], [1020, 12, 888, 80], [1021, 14, 888, 80, "fileName"], [1021, 22, 888, 80], [1021, 24, 888, 80, "_jsxFileName"], [1021, 36, 888, 80], [1022, 14, 888, 80, "lineNumber"], [1022, 24, 888, 80], [1023, 14, 888, 80, "columnNumber"], [1023, 26, 888, 80], [1024, 12, 888, 80], [1024, 19, 889, 20], [1024, 20, 890, 13], [1025, 10, 890, 13], [1026, 12, 890, 13, "fileName"], [1026, 20, 890, 13], [1026, 22, 890, 13, "_jsxFileName"], [1026, 34, 890, 13], [1027, 12, 890, 13, "lineNumber"], [1027, 22, 890, 13], [1028, 12, 890, 13, "columnNumber"], [1028, 24, 890, 13], [1029, 10, 890, 13], [1029, 17, 891, 18], [1029, 18, 891, 19], [1030, 8, 891, 19], [1030, 23, 892, 12], [1030, 24, 893, 9], [1030, 26, 895, 9, "isCameraReady"], [1030, 39, 895, 22], [1030, 56, 896, 10], [1030, 60, 896, 10, "_jsxDevRuntime"], [1030, 74, 896, 10], [1030, 75, 896, 10, "jsxDEV"], [1030, 81, 896, 10], [1030, 83, 896, 10, "_jsxDevRuntime"], [1030, 97, 896, 10], [1030, 98, 896, 10, "Fragment"], [1030, 106, 896, 10], [1031, 10, 896, 10, "children"], [1031, 18, 896, 10], [1031, 34, 898, 12], [1031, 38, 898, 12, "_jsxDevRuntime"], [1031, 52, 898, 12], [1031, 53, 898, 12, "jsxDEV"], [1031, 59, 898, 12], [1031, 61, 898, 13, "_View"], [1031, 66, 898, 13], [1031, 67, 898, 13, "default"], [1031, 74, 898, 17], [1032, 12, 898, 18, "style"], [1032, 17, 898, 23], [1032, 19, 898, 25, "styles"], [1032, 25, 898, 31], [1032, 26, 898, 32, "headerOverlay"], [1032, 39, 898, 46], [1033, 12, 898, 46, "children"], [1033, 20, 898, 46], [1033, 35, 899, 14], [1033, 39, 899, 14, "_jsxDevRuntime"], [1033, 53, 899, 14], [1033, 54, 899, 14, "jsxDEV"], [1033, 60, 899, 14], [1033, 62, 899, 15, "_View"], [1033, 67, 899, 15], [1033, 68, 899, 15, "default"], [1033, 75, 899, 19], [1034, 14, 899, 20, "style"], [1034, 19, 899, 25], [1034, 21, 899, 27, "styles"], [1034, 27, 899, 33], [1034, 28, 899, 34, "headerContent"], [1034, 41, 899, 48], [1035, 14, 899, 48, "children"], [1035, 22, 899, 48], [1035, 38, 900, 16], [1035, 42, 900, 16, "_jsxDevRuntime"], [1035, 56, 900, 16], [1035, 57, 900, 16, "jsxDEV"], [1035, 63, 900, 16], [1035, 65, 900, 17, "_View"], [1035, 70, 900, 17], [1035, 71, 900, 17, "default"], [1035, 78, 900, 21], [1036, 16, 900, 22, "style"], [1036, 21, 900, 27], [1036, 23, 900, 29, "styles"], [1036, 29, 900, 35], [1036, 30, 900, 36, "headerLeft"], [1036, 40, 900, 47], [1037, 16, 900, 47, "children"], [1037, 24, 900, 47], [1037, 40, 901, 18], [1037, 44, 901, 18, "_jsxDevRuntime"], [1037, 58, 901, 18], [1037, 59, 901, 18, "jsxDEV"], [1037, 65, 901, 18], [1037, 67, 901, 19, "_Text"], [1037, 72, 901, 19], [1037, 73, 901, 19, "default"], [1037, 80, 901, 23], [1038, 18, 901, 24, "style"], [1038, 23, 901, 29], [1038, 25, 901, 31, "styles"], [1038, 31, 901, 37], [1038, 32, 901, 38, "headerTitle"], [1038, 43, 901, 50], [1039, 18, 901, 50, "children"], [1039, 26, 901, 50], [1039, 28, 901, 51], [1040, 16, 901, 62], [1041, 18, 901, 62, "fileName"], [1041, 26, 901, 62], [1041, 28, 901, 62, "_jsxFileName"], [1041, 40, 901, 62], [1042, 18, 901, 62, "lineNumber"], [1042, 28, 901, 62], [1043, 18, 901, 62, "columnNumber"], [1043, 30, 901, 62], [1044, 16, 901, 62], [1044, 23, 901, 68], [1044, 24, 901, 69], [1044, 39, 902, 18], [1044, 43, 902, 18, "_jsxDevRuntime"], [1044, 57, 902, 18], [1044, 58, 902, 18, "jsxDEV"], [1044, 64, 902, 18], [1044, 66, 902, 19, "_View"], [1044, 71, 902, 19], [1044, 72, 902, 19, "default"], [1044, 79, 902, 23], [1045, 18, 902, 24, "style"], [1045, 23, 902, 29], [1045, 25, 902, 31, "styles"], [1045, 31, 902, 37], [1045, 32, 902, 38, "subtitleRow"], [1045, 43, 902, 50], [1046, 18, 902, 50, "children"], [1046, 26, 902, 50], [1046, 42, 903, 20], [1046, 46, 903, 20, "_jsxDevRuntime"], [1046, 60, 903, 20], [1046, 61, 903, 20, "jsxDEV"], [1046, 67, 903, 20], [1046, 69, 903, 21, "_Text"], [1046, 74, 903, 21], [1046, 75, 903, 21, "default"], [1046, 82, 903, 25], [1047, 20, 903, 26, "style"], [1047, 25, 903, 31], [1047, 27, 903, 33, "styles"], [1047, 33, 903, 39], [1047, 34, 903, 40, "webIcon"], [1047, 41, 903, 48], [1048, 20, 903, 48, "children"], [1048, 28, 903, 48], [1048, 30, 903, 49], [1049, 18, 903, 51], [1050, 20, 903, 51, "fileName"], [1050, 28, 903, 51], [1050, 30, 903, 51, "_jsxFileName"], [1050, 42, 903, 51], [1051, 20, 903, 51, "lineNumber"], [1051, 30, 903, 51], [1052, 20, 903, 51, "columnNumber"], [1052, 32, 903, 51], [1053, 18, 903, 51], [1053, 25, 903, 57], [1053, 26, 903, 58], [1053, 41, 904, 20], [1053, 45, 904, 20, "_jsxDevRuntime"], [1053, 59, 904, 20], [1053, 60, 904, 20, "jsxDEV"], [1053, 66, 904, 20], [1053, 68, 904, 21, "_Text"], [1053, 73, 904, 21], [1053, 74, 904, 21, "default"], [1053, 81, 904, 25], [1054, 20, 904, 26, "style"], [1054, 25, 904, 31], [1054, 27, 904, 33, "styles"], [1054, 33, 904, 39], [1054, 34, 904, 40, "headerSubtitle"], [1054, 48, 904, 55], [1055, 20, 904, 55, "children"], [1055, 28, 904, 55], [1055, 30, 904, 56], [1056, 18, 904, 71], [1057, 20, 904, 71, "fileName"], [1057, 28, 904, 71], [1057, 30, 904, 71, "_jsxFileName"], [1057, 42, 904, 71], [1058, 20, 904, 71, "lineNumber"], [1058, 30, 904, 71], [1059, 20, 904, 71, "columnNumber"], [1059, 32, 904, 71], [1060, 18, 904, 71], [1060, 25, 904, 77], [1060, 26, 904, 78], [1061, 16, 904, 78], [1062, 18, 904, 78, "fileName"], [1062, 26, 904, 78], [1062, 28, 904, 78, "_jsxFileName"], [1062, 40, 904, 78], [1063, 18, 904, 78, "lineNumber"], [1063, 28, 904, 78], [1064, 18, 904, 78, "columnNumber"], [1064, 30, 904, 78], [1065, 16, 904, 78], [1065, 23, 905, 24], [1065, 24, 905, 25], [1065, 26, 906, 19, "challengeCode"], [1065, 39, 906, 32], [1065, 56, 907, 20], [1065, 60, 907, 20, "_jsxDevRuntime"], [1065, 74, 907, 20], [1065, 75, 907, 20, "jsxDEV"], [1065, 81, 907, 20], [1065, 83, 907, 21, "_View"], [1065, 88, 907, 21], [1065, 89, 907, 21, "default"], [1065, 96, 907, 25], [1066, 18, 907, 26, "style"], [1066, 23, 907, 31], [1066, 25, 907, 33, "styles"], [1066, 31, 907, 39], [1066, 32, 907, 40, "challengeRow"], [1066, 44, 907, 53], [1067, 18, 907, 53, "children"], [1067, 26, 907, 53], [1067, 42, 908, 22], [1067, 46, 908, 22, "_jsxDevRuntime"], [1067, 60, 908, 22], [1067, 61, 908, 22, "jsxDEV"], [1067, 67, 908, 22], [1067, 69, 908, 23, "_lucideReactNative"], [1067, 87, 908, 23], [1067, 88, 908, 23, "Shield"], [1067, 94, 908, 29], [1068, 20, 908, 30, "size"], [1068, 24, 908, 34], [1068, 26, 908, 36], [1068, 28, 908, 39], [1069, 20, 908, 40, "color"], [1069, 25, 908, 45], [1069, 27, 908, 46], [1070, 18, 908, 52], [1071, 20, 908, 52, "fileName"], [1071, 28, 908, 52], [1071, 30, 908, 52, "_jsxFileName"], [1071, 42, 908, 52], [1072, 20, 908, 52, "lineNumber"], [1072, 30, 908, 52], [1073, 20, 908, 52, "columnNumber"], [1073, 32, 908, 52], [1074, 18, 908, 52], [1074, 25, 908, 54], [1074, 26, 908, 55], [1074, 41, 909, 22], [1074, 45, 909, 22, "_jsxDevRuntime"], [1074, 59, 909, 22], [1074, 60, 909, 22, "jsxDEV"], [1074, 66, 909, 22], [1074, 68, 909, 23, "_Text"], [1074, 73, 909, 23], [1074, 74, 909, 23, "default"], [1074, 81, 909, 27], [1075, 20, 909, 28, "style"], [1075, 25, 909, 33], [1075, 27, 909, 35, "styles"], [1075, 33, 909, 41], [1075, 34, 909, 42, "challengeCode"], [1075, 47, 909, 56], [1076, 20, 909, 56, "children"], [1076, 28, 909, 56], [1076, 30, 909, 58, "challengeCode"], [1077, 18, 909, 71], [1078, 20, 909, 71, "fileName"], [1078, 28, 909, 71], [1078, 30, 909, 71, "_jsxFileName"], [1078, 42, 909, 71], [1079, 20, 909, 71, "lineNumber"], [1079, 30, 909, 71], [1080, 20, 909, 71, "columnNumber"], [1080, 32, 909, 71], [1081, 18, 909, 71], [1081, 25, 909, 78], [1081, 26, 909, 79], [1082, 16, 909, 79], [1083, 18, 909, 79, "fileName"], [1083, 26, 909, 79], [1083, 28, 909, 79, "_jsxFileName"], [1083, 40, 909, 79], [1084, 18, 909, 79, "lineNumber"], [1084, 28, 909, 79], [1085, 18, 909, 79, "columnNumber"], [1085, 30, 909, 79], [1086, 16, 909, 79], [1086, 23, 910, 26], [1086, 24, 911, 19], [1087, 14, 911, 19], [1088, 16, 911, 19, "fileName"], [1088, 24, 911, 19], [1088, 26, 911, 19, "_jsxFileName"], [1088, 38, 911, 19], [1089, 16, 911, 19, "lineNumber"], [1089, 26, 911, 19], [1090, 16, 911, 19, "columnNumber"], [1090, 28, 911, 19], [1091, 14, 911, 19], [1091, 21, 912, 22], [1091, 22, 912, 23], [1091, 37, 913, 16], [1091, 41, 913, 16, "_jsxDevRuntime"], [1091, 55, 913, 16], [1091, 56, 913, 16, "jsxDEV"], [1091, 62, 913, 16], [1091, 64, 913, 17, "_TouchableOpacity"], [1091, 81, 913, 17], [1091, 82, 913, 17, "default"], [1091, 89, 913, 33], [1092, 16, 913, 34, "onPress"], [1092, 23, 913, 41], [1092, 25, 913, 43, "onCancel"], [1092, 33, 913, 52], [1093, 16, 913, 53, "style"], [1093, 21, 913, 58], [1093, 23, 913, 60, "styles"], [1093, 29, 913, 66], [1093, 30, 913, 67, "closeButton"], [1093, 41, 913, 79], [1094, 16, 913, 79, "children"], [1094, 24, 913, 79], [1094, 39, 914, 18], [1094, 43, 914, 18, "_jsxDevRuntime"], [1094, 57, 914, 18], [1094, 58, 914, 18, "jsxDEV"], [1094, 64, 914, 18], [1094, 66, 914, 19, "_lucideReactNative"], [1094, 84, 914, 19], [1094, 85, 914, 19, "X"], [1094, 86, 914, 20], [1095, 18, 914, 21, "size"], [1095, 22, 914, 25], [1095, 24, 914, 27], [1095, 26, 914, 30], [1096, 18, 914, 31, "color"], [1096, 23, 914, 36], [1096, 25, 914, 37], [1097, 16, 914, 43], [1098, 18, 914, 43, "fileName"], [1098, 26, 914, 43], [1098, 28, 914, 43, "_jsxFileName"], [1098, 40, 914, 43], [1099, 18, 914, 43, "lineNumber"], [1099, 28, 914, 43], [1100, 18, 914, 43, "columnNumber"], [1100, 30, 914, 43], [1101, 16, 914, 43], [1101, 23, 914, 45], [1102, 14, 914, 46], [1103, 16, 914, 46, "fileName"], [1103, 24, 914, 46], [1103, 26, 914, 46, "_jsxFileName"], [1103, 38, 914, 46], [1104, 16, 914, 46, "lineNumber"], [1104, 26, 914, 46], [1105, 16, 914, 46, "columnNumber"], [1105, 28, 914, 46], [1106, 14, 914, 46], [1106, 21, 915, 34], [1106, 22, 915, 35], [1107, 12, 915, 35], [1108, 14, 915, 35, "fileName"], [1108, 22, 915, 35], [1108, 24, 915, 35, "_jsxFileName"], [1108, 36, 915, 35], [1109, 14, 915, 35, "lineNumber"], [1109, 24, 915, 35], [1110, 14, 915, 35, "columnNumber"], [1110, 26, 915, 35], [1111, 12, 915, 35], [1111, 19, 916, 20], [1112, 10, 916, 21], [1113, 12, 916, 21, "fileName"], [1113, 20, 916, 21], [1113, 22, 916, 21, "_jsxFileName"], [1113, 34, 916, 21], [1114, 12, 916, 21, "lineNumber"], [1114, 22, 916, 21], [1115, 12, 916, 21, "columnNumber"], [1115, 24, 916, 21], [1116, 10, 916, 21], [1116, 17, 917, 18], [1116, 18, 917, 19], [1116, 33, 919, 12], [1116, 37, 919, 12, "_jsxDevRuntime"], [1116, 51, 919, 12], [1116, 52, 919, 12, "jsxDEV"], [1116, 58, 919, 12], [1116, 60, 919, 13, "_View"], [1116, 65, 919, 13], [1116, 66, 919, 13, "default"], [1116, 73, 919, 17], [1117, 12, 919, 18, "style"], [1117, 17, 919, 23], [1117, 19, 919, 25, "styles"], [1117, 25, 919, 31], [1117, 26, 919, 32, "privacyNotice"], [1117, 39, 919, 46], [1118, 12, 919, 46, "children"], [1118, 20, 919, 46], [1118, 36, 920, 14], [1118, 40, 920, 14, "_jsxDevRuntime"], [1118, 54, 920, 14], [1118, 55, 920, 14, "jsxDEV"], [1118, 61, 920, 14], [1118, 63, 920, 15, "_lucideReactNative"], [1118, 81, 920, 15], [1118, 82, 920, 15, "Shield"], [1118, 88, 920, 21], [1119, 14, 920, 22, "size"], [1119, 18, 920, 26], [1119, 20, 920, 28], [1119, 22, 920, 31], [1120, 14, 920, 32, "color"], [1120, 19, 920, 37], [1120, 21, 920, 38], [1121, 12, 920, 47], [1122, 14, 920, 47, "fileName"], [1122, 22, 920, 47], [1122, 24, 920, 47, "_jsxFileName"], [1122, 36, 920, 47], [1123, 14, 920, 47, "lineNumber"], [1123, 24, 920, 47], [1124, 14, 920, 47, "columnNumber"], [1124, 26, 920, 47], [1125, 12, 920, 47], [1125, 19, 920, 49], [1125, 20, 920, 50], [1125, 35, 921, 14], [1125, 39, 921, 14, "_jsxDevRuntime"], [1125, 53, 921, 14], [1125, 54, 921, 14, "jsxDEV"], [1125, 60, 921, 14], [1125, 62, 921, 15, "_Text"], [1125, 67, 921, 15], [1125, 68, 921, 15, "default"], [1125, 75, 921, 19], [1126, 14, 921, 20, "style"], [1126, 19, 921, 25], [1126, 21, 921, 27, "styles"], [1126, 27, 921, 33], [1126, 28, 921, 34, "privacyText"], [1126, 39, 921, 46], [1127, 14, 921, 46, "children"], [1127, 22, 921, 46], [1127, 24, 921, 47], [1128, 12, 923, 14], [1129, 14, 923, 14, "fileName"], [1129, 22, 923, 14], [1129, 24, 923, 14, "_jsxFileName"], [1129, 36, 923, 14], [1130, 14, 923, 14, "lineNumber"], [1130, 24, 923, 14], [1131, 14, 923, 14, "columnNumber"], [1131, 26, 923, 14], [1132, 12, 923, 14], [1132, 19, 923, 20], [1132, 20, 923, 21], [1133, 10, 923, 21], [1134, 12, 923, 21, "fileName"], [1134, 20, 923, 21], [1134, 22, 923, 21, "_jsxFileName"], [1134, 34, 923, 21], [1135, 12, 923, 21, "lineNumber"], [1135, 22, 923, 21], [1136, 12, 923, 21, "columnNumber"], [1136, 24, 923, 21], [1137, 10, 923, 21], [1137, 17, 924, 18], [1137, 18, 924, 19], [1137, 33, 926, 12], [1137, 37, 926, 12, "_jsxDevRuntime"], [1137, 51, 926, 12], [1137, 52, 926, 12, "jsxDEV"], [1137, 58, 926, 12], [1137, 60, 926, 13, "_View"], [1137, 65, 926, 13], [1137, 66, 926, 13, "default"], [1137, 73, 926, 17], [1138, 12, 926, 18, "style"], [1138, 17, 926, 23], [1138, 19, 926, 25, "styles"], [1138, 25, 926, 31], [1138, 26, 926, 32, "footer<PERSON><PERSON><PERSON>"], [1138, 39, 926, 46], [1139, 12, 926, 46, "children"], [1139, 20, 926, 46], [1139, 36, 927, 14], [1139, 40, 927, 14, "_jsxDevRuntime"], [1139, 54, 927, 14], [1139, 55, 927, 14, "jsxDEV"], [1139, 61, 927, 14], [1139, 63, 927, 15, "_Text"], [1139, 68, 927, 15], [1139, 69, 927, 15, "default"], [1139, 76, 927, 19], [1140, 14, 927, 20, "style"], [1140, 19, 927, 25], [1140, 21, 927, 27, "styles"], [1140, 27, 927, 33], [1140, 28, 927, 34, "instruction"], [1140, 39, 927, 46], [1141, 14, 927, 46, "children"], [1141, 22, 927, 46], [1141, 24, 927, 47], [1142, 12, 929, 14], [1143, 14, 929, 14, "fileName"], [1143, 22, 929, 14], [1143, 24, 929, 14, "_jsxFileName"], [1143, 36, 929, 14], [1144, 14, 929, 14, "lineNumber"], [1144, 24, 929, 14], [1145, 14, 929, 14, "columnNumber"], [1145, 26, 929, 14], [1146, 12, 929, 14], [1146, 19, 929, 20], [1146, 20, 929, 21], [1146, 35, 931, 14], [1146, 39, 931, 14, "_jsxDevRuntime"], [1146, 53, 931, 14], [1146, 54, 931, 14, "jsxDEV"], [1146, 60, 931, 14], [1146, 62, 931, 15, "_TouchableOpacity"], [1146, 79, 931, 15], [1146, 80, 931, 15, "default"], [1146, 87, 931, 31], [1147, 14, 932, 16, "onPress"], [1147, 21, 932, 23], [1147, 23, 932, 25, "capturePhoto"], [1147, 35, 932, 38], [1148, 14, 933, 16, "disabled"], [1148, 22, 933, 24], [1148, 24, 933, 26, "processingState"], [1148, 39, 933, 41], [1148, 44, 933, 46], [1148, 50, 933, 52], [1148, 54, 933, 56], [1148, 55, 933, 57, "isCameraReady"], [1148, 68, 933, 71], [1149, 14, 934, 16, "style"], [1149, 19, 934, 21], [1149, 21, 934, 23], [1149, 22, 935, 18, "styles"], [1149, 28, 935, 24], [1149, 29, 935, 25, "shutterButton"], [1149, 42, 935, 38], [1149, 44, 936, 18, "processingState"], [1149, 59, 936, 33], [1149, 64, 936, 38], [1149, 70, 936, 44], [1149, 74, 936, 48, "styles"], [1149, 80, 936, 54], [1149, 81, 936, 55, "shutterButtonDisabled"], [1149, 102, 936, 76], [1149, 103, 937, 18], [1150, 14, 937, 18, "children"], [1150, 22, 937, 18], [1150, 24, 939, 17, "processingState"], [1150, 39, 939, 32], [1150, 44, 939, 37], [1150, 50, 939, 43], [1150, 66, 940, 18], [1150, 70, 940, 18, "_jsxDevRuntime"], [1150, 84, 940, 18], [1150, 85, 940, 18, "jsxDEV"], [1150, 91, 940, 18], [1150, 93, 940, 19, "_View"], [1150, 98, 940, 19], [1150, 99, 940, 19, "default"], [1150, 106, 940, 23], [1151, 16, 940, 24, "style"], [1151, 21, 940, 29], [1151, 23, 940, 31, "styles"], [1151, 29, 940, 37], [1151, 30, 940, 38, "shutterInner"], [1152, 14, 940, 51], [1153, 16, 940, 51, "fileName"], [1153, 24, 940, 51], [1153, 26, 940, 51, "_jsxFileName"], [1153, 38, 940, 51], [1154, 16, 940, 51, "lineNumber"], [1154, 26, 940, 51], [1155, 16, 940, 51, "columnNumber"], [1155, 28, 940, 51], [1156, 14, 940, 51], [1156, 21, 940, 53], [1156, 22, 940, 54], [1156, 38, 942, 18], [1156, 42, 942, 18, "_jsxDevRuntime"], [1156, 56, 942, 18], [1156, 57, 942, 18, "jsxDEV"], [1156, 63, 942, 18], [1156, 65, 942, 19, "_ActivityIndicator"], [1156, 83, 942, 19], [1156, 84, 942, 19, "default"], [1156, 91, 942, 36], [1157, 16, 942, 37, "size"], [1157, 20, 942, 41], [1157, 22, 942, 42], [1157, 29, 942, 49], [1158, 16, 942, 50, "color"], [1158, 21, 942, 55], [1158, 23, 942, 56], [1159, 14, 942, 65], [1160, 16, 942, 65, "fileName"], [1160, 24, 942, 65], [1160, 26, 942, 65, "_jsxFileName"], [1160, 38, 942, 65], [1161, 16, 942, 65, "lineNumber"], [1161, 26, 942, 65], [1162, 16, 942, 65, "columnNumber"], [1162, 28, 942, 65], [1163, 14, 942, 65], [1163, 21, 942, 67], [1164, 12, 943, 17], [1165, 14, 943, 17, "fileName"], [1165, 22, 943, 17], [1165, 24, 943, 17, "_jsxFileName"], [1165, 36, 943, 17], [1166, 14, 943, 17, "lineNumber"], [1166, 24, 943, 17], [1167, 14, 943, 17, "columnNumber"], [1167, 26, 943, 17], [1168, 12, 943, 17], [1168, 19, 944, 32], [1168, 20, 944, 33], [1168, 35, 945, 14], [1168, 39, 945, 14, "_jsxDevRuntime"], [1168, 53, 945, 14], [1168, 54, 945, 14, "jsxDEV"], [1168, 60, 945, 14], [1168, 62, 945, 15, "_Text"], [1168, 67, 945, 15], [1168, 68, 945, 15, "default"], [1168, 75, 945, 19], [1169, 14, 945, 20, "style"], [1169, 19, 945, 25], [1169, 21, 945, 27, "styles"], [1169, 27, 945, 33], [1169, 28, 945, 34, "privacyNote"], [1169, 39, 945, 46], [1170, 14, 945, 46, "children"], [1170, 22, 945, 46], [1170, 24, 945, 47], [1171, 12, 947, 14], [1172, 14, 947, 14, "fileName"], [1172, 22, 947, 14], [1172, 24, 947, 14, "_jsxFileName"], [1172, 36, 947, 14], [1173, 14, 947, 14, "lineNumber"], [1173, 24, 947, 14], [1174, 14, 947, 14, "columnNumber"], [1174, 26, 947, 14], [1175, 12, 947, 14], [1175, 19, 947, 20], [1175, 20, 947, 21], [1176, 10, 947, 21], [1177, 12, 947, 21, "fileName"], [1177, 20, 947, 21], [1177, 22, 947, 21, "_jsxFileName"], [1177, 34, 947, 21], [1178, 12, 947, 21, "lineNumber"], [1178, 22, 947, 21], [1179, 12, 947, 21, "columnNumber"], [1179, 24, 947, 21], [1180, 10, 947, 21], [1180, 17, 948, 18], [1180, 18, 948, 19], [1181, 8, 948, 19], [1181, 23, 949, 12], [1181, 24, 950, 9], [1182, 6, 950, 9], [1183, 8, 950, 9, "fileName"], [1183, 16, 950, 9], [1183, 18, 950, 9, "_jsxFileName"], [1183, 30, 950, 9], [1184, 8, 950, 9, "lineNumber"], [1184, 18, 950, 9], [1185, 8, 950, 9, "columnNumber"], [1185, 20, 950, 9], [1186, 6, 950, 9], [1186, 13, 951, 12], [1186, 14, 951, 13], [1186, 29, 953, 6], [1186, 33, 953, 6, "_jsxDevRuntime"], [1186, 47, 953, 6], [1186, 48, 953, 6, "jsxDEV"], [1186, 54, 953, 6], [1186, 56, 953, 7, "_Modal"], [1186, 62, 953, 7], [1186, 63, 953, 7, "default"], [1186, 70, 953, 12], [1187, 8, 954, 8, "visible"], [1187, 15, 954, 15], [1187, 17, 954, 17, "processingState"], [1187, 32, 954, 32], [1187, 37, 954, 37], [1187, 43, 954, 43], [1187, 47, 954, 47, "processingState"], [1187, 62, 954, 62], [1187, 67, 954, 67], [1187, 74, 954, 75], [1188, 8, 955, 8, "transparent"], [1188, 19, 955, 19], [1189, 8, 956, 8, "animationType"], [1189, 21, 956, 21], [1189, 23, 956, 22], [1189, 29, 956, 28], [1190, 8, 956, 28, "children"], [1190, 16, 956, 28], [1190, 31, 958, 8], [1190, 35, 958, 8, "_jsxDevRuntime"], [1190, 49, 958, 8], [1190, 50, 958, 8, "jsxDEV"], [1190, 56, 958, 8], [1190, 58, 958, 9, "_View"], [1190, 63, 958, 9], [1190, 64, 958, 9, "default"], [1190, 71, 958, 13], [1191, 10, 958, 14, "style"], [1191, 15, 958, 19], [1191, 17, 958, 21, "styles"], [1191, 23, 958, 27], [1191, 24, 958, 28, "processingModal"], [1191, 39, 958, 44], [1192, 10, 958, 44, "children"], [1192, 18, 958, 44], [1192, 33, 959, 10], [1192, 37, 959, 10, "_jsxDevRuntime"], [1192, 51, 959, 10], [1192, 52, 959, 10, "jsxDEV"], [1192, 58, 959, 10], [1192, 60, 959, 11, "_View"], [1192, 65, 959, 11], [1192, 66, 959, 11, "default"], [1192, 73, 959, 15], [1193, 12, 959, 16, "style"], [1193, 17, 959, 21], [1193, 19, 959, 23, "styles"], [1193, 25, 959, 29], [1193, 26, 959, 30, "processingContent"], [1193, 43, 959, 48], [1194, 12, 959, 48, "children"], [1194, 20, 959, 48], [1194, 36, 960, 12], [1194, 40, 960, 12, "_jsxDevRuntime"], [1194, 54, 960, 12], [1194, 55, 960, 12, "jsxDEV"], [1194, 61, 960, 12], [1194, 63, 960, 13, "_ActivityIndicator"], [1194, 81, 960, 13], [1194, 82, 960, 13, "default"], [1194, 89, 960, 30], [1195, 14, 960, 31, "size"], [1195, 18, 960, 35], [1195, 20, 960, 36], [1195, 27, 960, 43], [1196, 14, 960, 44, "color"], [1196, 19, 960, 49], [1196, 21, 960, 50], [1197, 12, 960, 59], [1198, 14, 960, 59, "fileName"], [1198, 22, 960, 59], [1198, 24, 960, 59, "_jsxFileName"], [1198, 36, 960, 59], [1199, 14, 960, 59, "lineNumber"], [1199, 24, 960, 59], [1200, 14, 960, 59, "columnNumber"], [1200, 26, 960, 59], [1201, 12, 960, 59], [1201, 19, 960, 61], [1201, 20, 960, 62], [1201, 35, 962, 12], [1201, 39, 962, 12, "_jsxDevRuntime"], [1201, 53, 962, 12], [1201, 54, 962, 12, "jsxDEV"], [1201, 60, 962, 12], [1201, 62, 962, 13, "_Text"], [1201, 67, 962, 13], [1201, 68, 962, 13, "default"], [1201, 75, 962, 17], [1202, 14, 962, 18, "style"], [1202, 19, 962, 23], [1202, 21, 962, 25, "styles"], [1202, 27, 962, 31], [1202, 28, 962, 32, "processingTitle"], [1202, 43, 962, 48], [1203, 14, 962, 48, "children"], [1203, 22, 962, 48], [1203, 25, 963, 15, "processingState"], [1203, 40, 963, 30], [1203, 45, 963, 35], [1203, 56, 963, 46], [1203, 60, 963, 50], [1203, 80, 963, 70], [1203, 82, 964, 15, "processingState"], [1203, 97, 964, 30], [1203, 102, 964, 35], [1203, 113, 964, 46], [1203, 117, 964, 50], [1203, 146, 964, 79], [1203, 148, 965, 15, "processingState"], [1203, 163, 965, 30], [1203, 168, 965, 35], [1203, 180, 965, 47], [1203, 184, 965, 51], [1203, 216, 965, 83], [1203, 218, 966, 15, "processingState"], [1203, 233, 966, 30], [1203, 238, 966, 35], [1203, 249, 966, 46], [1203, 253, 966, 50], [1203, 275, 966, 72], [1204, 12, 966, 72], [1205, 14, 966, 72, "fileName"], [1205, 22, 966, 72], [1205, 24, 966, 72, "_jsxFileName"], [1205, 36, 966, 72], [1206, 14, 966, 72, "lineNumber"], [1206, 24, 966, 72], [1207, 14, 966, 72, "columnNumber"], [1207, 26, 966, 72], [1208, 12, 966, 72], [1208, 19, 967, 18], [1208, 20, 967, 19], [1208, 35, 968, 12], [1208, 39, 968, 12, "_jsxDevRuntime"], [1208, 53, 968, 12], [1208, 54, 968, 12, "jsxDEV"], [1208, 60, 968, 12], [1208, 62, 968, 13, "_View"], [1208, 67, 968, 13], [1208, 68, 968, 13, "default"], [1208, 75, 968, 17], [1209, 14, 968, 18, "style"], [1209, 19, 968, 23], [1209, 21, 968, 25, "styles"], [1209, 27, 968, 31], [1209, 28, 968, 32, "progressBar"], [1209, 39, 968, 44], [1210, 14, 968, 44, "children"], [1210, 22, 968, 44], [1210, 37, 969, 14], [1210, 41, 969, 14, "_jsxDevRuntime"], [1210, 55, 969, 14], [1210, 56, 969, 14, "jsxDEV"], [1210, 62, 969, 14], [1210, 64, 969, 15, "_View"], [1210, 69, 969, 15], [1210, 70, 969, 15, "default"], [1210, 77, 969, 19], [1211, 16, 970, 16, "style"], [1211, 21, 970, 21], [1211, 23, 970, 23], [1211, 24, 971, 18, "styles"], [1211, 30, 971, 24], [1211, 31, 971, 25, "progressFill"], [1211, 43, 971, 37], [1211, 45, 972, 18], [1212, 18, 972, 20, "width"], [1212, 23, 972, 25], [1212, 25, 972, 27], [1212, 28, 972, 30, "processingProgress"], [1212, 46, 972, 48], [1213, 16, 972, 52], [1213, 17, 972, 53], [1214, 14, 973, 18], [1215, 16, 973, 18, "fileName"], [1215, 24, 973, 18], [1215, 26, 973, 18, "_jsxFileName"], [1215, 38, 973, 18], [1216, 16, 973, 18, "lineNumber"], [1216, 26, 973, 18], [1217, 16, 973, 18, "columnNumber"], [1217, 28, 973, 18], [1218, 14, 973, 18], [1218, 21, 974, 15], [1219, 12, 974, 16], [1220, 14, 974, 16, "fileName"], [1220, 22, 974, 16], [1220, 24, 974, 16, "_jsxFileName"], [1220, 36, 974, 16], [1221, 14, 974, 16, "lineNumber"], [1221, 24, 974, 16], [1222, 14, 974, 16, "columnNumber"], [1222, 26, 974, 16], [1223, 12, 974, 16], [1223, 19, 975, 18], [1223, 20, 975, 19], [1223, 35, 976, 12], [1223, 39, 976, 12, "_jsxDevRuntime"], [1223, 53, 976, 12], [1223, 54, 976, 12, "jsxDEV"], [1223, 60, 976, 12], [1223, 62, 976, 13, "_Text"], [1223, 67, 976, 13], [1223, 68, 976, 13, "default"], [1223, 75, 976, 17], [1224, 14, 976, 18, "style"], [1224, 19, 976, 23], [1224, 21, 976, 25, "styles"], [1224, 27, 976, 31], [1224, 28, 976, 32, "processingDescription"], [1224, 49, 976, 54], [1225, 14, 976, 54, "children"], [1225, 22, 976, 54], [1225, 25, 977, 15, "processingState"], [1225, 40, 977, 30], [1225, 45, 977, 35], [1225, 56, 977, 46], [1225, 60, 977, 50], [1225, 89, 977, 79], [1225, 91, 978, 15, "processingState"], [1225, 106, 978, 30], [1225, 111, 978, 35], [1225, 122, 978, 46], [1225, 126, 978, 50], [1225, 164, 978, 88], [1225, 166, 979, 15, "processingState"], [1225, 181, 979, 30], [1225, 186, 979, 35], [1225, 198, 979, 47], [1225, 202, 979, 51], [1225, 247, 979, 96], [1225, 249, 980, 15, "processingState"], [1225, 264, 980, 30], [1225, 269, 980, 35], [1225, 280, 980, 46], [1225, 284, 980, 50], [1225, 325, 980, 91], [1226, 12, 980, 91], [1227, 14, 980, 91, "fileName"], [1227, 22, 980, 91], [1227, 24, 980, 91, "_jsxFileName"], [1227, 36, 980, 91], [1228, 14, 980, 91, "lineNumber"], [1228, 24, 980, 91], [1229, 14, 980, 91, "columnNumber"], [1229, 26, 980, 91], [1230, 12, 980, 91], [1230, 19, 981, 18], [1230, 20, 981, 19], [1230, 22, 982, 13, "processingState"], [1230, 37, 982, 28], [1230, 42, 982, 33], [1230, 53, 982, 44], [1230, 70, 983, 14], [1230, 74, 983, 14, "_jsxDevRuntime"], [1230, 88, 983, 14], [1230, 89, 983, 14, "jsxDEV"], [1230, 95, 983, 14], [1230, 97, 983, 15, "_lucideReactNative"], [1230, 115, 983, 15], [1230, 116, 983, 15, "CheckCircle"], [1230, 127, 983, 26], [1231, 14, 983, 27, "size"], [1231, 18, 983, 31], [1231, 20, 983, 33], [1231, 22, 983, 36], [1232, 14, 983, 37, "color"], [1232, 19, 983, 42], [1232, 21, 983, 43], [1232, 30, 983, 52], [1233, 14, 983, 53, "style"], [1233, 19, 983, 58], [1233, 21, 983, 60, "styles"], [1233, 27, 983, 66], [1233, 28, 983, 67, "successIcon"], [1234, 12, 983, 79], [1235, 14, 983, 79, "fileName"], [1235, 22, 983, 79], [1235, 24, 983, 79, "_jsxFileName"], [1235, 36, 983, 79], [1236, 14, 983, 79, "lineNumber"], [1236, 24, 983, 79], [1237, 14, 983, 79, "columnNumber"], [1237, 26, 983, 79], [1238, 12, 983, 79], [1238, 19, 983, 81], [1238, 20, 984, 13], [1239, 10, 984, 13], [1240, 12, 984, 13, "fileName"], [1240, 20, 984, 13], [1240, 22, 984, 13, "_jsxFileName"], [1240, 34, 984, 13], [1241, 12, 984, 13, "lineNumber"], [1241, 22, 984, 13], [1242, 12, 984, 13, "columnNumber"], [1242, 24, 984, 13], [1243, 10, 984, 13], [1243, 17, 985, 16], [1244, 8, 985, 17], [1245, 10, 985, 17, "fileName"], [1245, 18, 985, 17], [1245, 20, 985, 17, "_jsxFileName"], [1245, 32, 985, 17], [1246, 10, 985, 17, "lineNumber"], [1246, 20, 985, 17], [1247, 10, 985, 17, "columnNumber"], [1247, 22, 985, 17], [1248, 8, 985, 17], [1248, 15, 986, 14], [1249, 6, 986, 15], [1250, 8, 986, 15, "fileName"], [1250, 16, 986, 15], [1250, 18, 986, 15, "_jsxFileName"], [1250, 30, 986, 15], [1251, 8, 986, 15, "lineNumber"], [1251, 18, 986, 15], [1252, 8, 986, 15, "columnNumber"], [1252, 20, 986, 15], [1253, 6, 986, 15], [1253, 13, 987, 13], [1253, 14, 987, 14], [1253, 29, 989, 6], [1253, 33, 989, 6, "_jsxDevRuntime"], [1253, 47, 989, 6], [1253, 48, 989, 6, "jsxDEV"], [1253, 54, 989, 6], [1253, 56, 989, 7, "_Modal"], [1253, 62, 989, 7], [1253, 63, 989, 7, "default"], [1253, 70, 989, 12], [1254, 8, 990, 8, "visible"], [1254, 15, 990, 15], [1254, 17, 990, 17, "processingState"], [1254, 32, 990, 32], [1254, 37, 990, 37], [1254, 44, 990, 45], [1255, 8, 991, 8, "transparent"], [1255, 19, 991, 19], [1256, 8, 992, 8, "animationType"], [1256, 21, 992, 21], [1256, 23, 992, 22], [1256, 29, 992, 28], [1257, 8, 992, 28, "children"], [1257, 16, 992, 28], [1257, 31, 994, 8], [1257, 35, 994, 8, "_jsxDevRuntime"], [1257, 49, 994, 8], [1257, 50, 994, 8, "jsxDEV"], [1257, 56, 994, 8], [1257, 58, 994, 9, "_View"], [1257, 63, 994, 9], [1257, 64, 994, 9, "default"], [1257, 71, 994, 13], [1258, 10, 994, 14, "style"], [1258, 15, 994, 19], [1258, 17, 994, 21, "styles"], [1258, 23, 994, 27], [1258, 24, 994, 28, "processingModal"], [1258, 39, 994, 44], [1259, 10, 994, 44, "children"], [1259, 18, 994, 44], [1259, 33, 995, 10], [1259, 37, 995, 10, "_jsxDevRuntime"], [1259, 51, 995, 10], [1259, 52, 995, 10, "jsxDEV"], [1259, 58, 995, 10], [1259, 60, 995, 11, "_View"], [1259, 65, 995, 11], [1259, 66, 995, 11, "default"], [1259, 73, 995, 15], [1260, 12, 995, 16, "style"], [1260, 17, 995, 21], [1260, 19, 995, 23, "styles"], [1260, 25, 995, 29], [1260, 26, 995, 30, "errorContent"], [1260, 38, 995, 43], [1261, 12, 995, 43, "children"], [1261, 20, 995, 43], [1261, 36, 996, 12], [1261, 40, 996, 12, "_jsxDevRuntime"], [1261, 54, 996, 12], [1261, 55, 996, 12, "jsxDEV"], [1261, 61, 996, 12], [1261, 63, 996, 13, "_lucideReactNative"], [1261, 81, 996, 13], [1261, 82, 996, 13, "X"], [1261, 83, 996, 14], [1262, 14, 996, 15, "size"], [1262, 18, 996, 19], [1262, 20, 996, 21], [1262, 22, 996, 24], [1263, 14, 996, 25, "color"], [1263, 19, 996, 30], [1263, 21, 996, 31], [1264, 12, 996, 40], [1265, 14, 996, 40, "fileName"], [1265, 22, 996, 40], [1265, 24, 996, 40, "_jsxFileName"], [1265, 36, 996, 40], [1266, 14, 996, 40, "lineNumber"], [1266, 24, 996, 40], [1267, 14, 996, 40, "columnNumber"], [1267, 26, 996, 40], [1268, 12, 996, 40], [1268, 19, 996, 42], [1268, 20, 996, 43], [1268, 35, 997, 12], [1268, 39, 997, 12, "_jsxDevRuntime"], [1268, 53, 997, 12], [1268, 54, 997, 12, "jsxDEV"], [1268, 60, 997, 12], [1268, 62, 997, 13, "_Text"], [1268, 67, 997, 13], [1268, 68, 997, 13, "default"], [1268, 75, 997, 17], [1269, 14, 997, 18, "style"], [1269, 19, 997, 23], [1269, 21, 997, 25, "styles"], [1269, 27, 997, 31], [1269, 28, 997, 32, "errorTitle"], [1269, 38, 997, 43], [1270, 14, 997, 43, "children"], [1270, 22, 997, 43], [1270, 24, 997, 44], [1271, 12, 997, 61], [1272, 14, 997, 61, "fileName"], [1272, 22, 997, 61], [1272, 24, 997, 61, "_jsxFileName"], [1272, 36, 997, 61], [1273, 14, 997, 61, "lineNumber"], [1273, 24, 997, 61], [1274, 14, 997, 61, "columnNumber"], [1274, 26, 997, 61], [1275, 12, 997, 61], [1275, 19, 997, 67], [1275, 20, 997, 68], [1275, 35, 998, 12], [1275, 39, 998, 12, "_jsxDevRuntime"], [1275, 53, 998, 12], [1275, 54, 998, 12, "jsxDEV"], [1275, 60, 998, 12], [1275, 62, 998, 13, "_Text"], [1275, 67, 998, 13], [1275, 68, 998, 13, "default"], [1275, 75, 998, 17], [1276, 14, 998, 18, "style"], [1276, 19, 998, 23], [1276, 21, 998, 25, "styles"], [1276, 27, 998, 31], [1276, 28, 998, 32, "errorMessage"], [1276, 40, 998, 45], [1277, 14, 998, 45, "children"], [1277, 22, 998, 45], [1277, 24, 998, 47, "errorMessage"], [1278, 12, 998, 59], [1279, 14, 998, 59, "fileName"], [1279, 22, 998, 59], [1279, 24, 998, 59, "_jsxFileName"], [1279, 36, 998, 59], [1280, 14, 998, 59, "lineNumber"], [1280, 24, 998, 59], [1281, 14, 998, 59, "columnNumber"], [1281, 26, 998, 59], [1282, 12, 998, 59], [1282, 19, 998, 66], [1282, 20, 998, 67], [1282, 35, 999, 12], [1282, 39, 999, 12, "_jsxDevRuntime"], [1282, 53, 999, 12], [1282, 54, 999, 12, "jsxDEV"], [1282, 60, 999, 12], [1282, 62, 999, 13, "_TouchableOpacity"], [1282, 79, 999, 13], [1282, 80, 999, 13, "default"], [1282, 87, 999, 29], [1283, 14, 1000, 14, "onPress"], [1283, 21, 1000, 21], [1283, 23, 1000, 23, "retryCapture"], [1283, 35, 1000, 36], [1284, 14, 1001, 14, "style"], [1284, 19, 1001, 19], [1284, 21, 1001, 21, "styles"], [1284, 27, 1001, 27], [1284, 28, 1001, 28, "primaryButton"], [1284, 41, 1001, 42], [1285, 14, 1001, 42, "children"], [1285, 22, 1001, 42], [1285, 37, 1003, 14], [1285, 41, 1003, 14, "_jsxDevRuntime"], [1285, 55, 1003, 14], [1285, 56, 1003, 14, "jsxDEV"], [1285, 62, 1003, 14], [1285, 64, 1003, 15, "_Text"], [1285, 69, 1003, 15], [1285, 70, 1003, 15, "default"], [1285, 77, 1003, 19], [1286, 16, 1003, 20, "style"], [1286, 21, 1003, 25], [1286, 23, 1003, 27, "styles"], [1286, 29, 1003, 33], [1286, 30, 1003, 34, "primaryButtonText"], [1286, 47, 1003, 52], [1287, 16, 1003, 52, "children"], [1287, 24, 1003, 52], [1287, 26, 1003, 53], [1288, 14, 1003, 62], [1289, 16, 1003, 62, "fileName"], [1289, 24, 1003, 62], [1289, 26, 1003, 62, "_jsxFileName"], [1289, 38, 1003, 62], [1290, 16, 1003, 62, "lineNumber"], [1290, 26, 1003, 62], [1291, 16, 1003, 62, "columnNumber"], [1291, 28, 1003, 62], [1292, 14, 1003, 62], [1292, 21, 1003, 68], [1293, 12, 1003, 69], [1294, 14, 1003, 69, "fileName"], [1294, 22, 1003, 69], [1294, 24, 1003, 69, "_jsxFileName"], [1294, 36, 1003, 69], [1295, 14, 1003, 69, "lineNumber"], [1295, 24, 1003, 69], [1296, 14, 1003, 69, "columnNumber"], [1296, 26, 1003, 69], [1297, 12, 1003, 69], [1297, 19, 1004, 30], [1297, 20, 1004, 31], [1297, 35, 1005, 12], [1297, 39, 1005, 12, "_jsxDevRuntime"], [1297, 53, 1005, 12], [1297, 54, 1005, 12, "jsxDEV"], [1297, 60, 1005, 12], [1297, 62, 1005, 13, "_TouchableOpacity"], [1297, 79, 1005, 13], [1297, 80, 1005, 13, "default"], [1297, 87, 1005, 29], [1298, 14, 1006, 14, "onPress"], [1298, 21, 1006, 21], [1298, 23, 1006, 23, "onCancel"], [1298, 31, 1006, 32], [1299, 14, 1007, 14, "style"], [1299, 19, 1007, 19], [1299, 21, 1007, 21, "styles"], [1299, 27, 1007, 27], [1299, 28, 1007, 28, "secondaryButton"], [1299, 43, 1007, 44], [1300, 14, 1007, 44, "children"], [1300, 22, 1007, 44], [1300, 37, 1009, 14], [1300, 41, 1009, 14, "_jsxDevRuntime"], [1300, 55, 1009, 14], [1300, 56, 1009, 14, "jsxDEV"], [1300, 62, 1009, 14], [1300, 64, 1009, 15, "_Text"], [1300, 69, 1009, 15], [1300, 70, 1009, 15, "default"], [1300, 77, 1009, 19], [1301, 16, 1009, 20, "style"], [1301, 21, 1009, 25], [1301, 23, 1009, 27, "styles"], [1301, 29, 1009, 33], [1301, 30, 1009, 34, "secondaryButtonText"], [1301, 49, 1009, 54], [1302, 16, 1009, 54, "children"], [1302, 24, 1009, 54], [1302, 26, 1009, 55], [1303, 14, 1009, 61], [1304, 16, 1009, 61, "fileName"], [1304, 24, 1009, 61], [1304, 26, 1009, 61, "_jsxFileName"], [1304, 38, 1009, 61], [1305, 16, 1009, 61, "lineNumber"], [1305, 26, 1009, 61], [1306, 16, 1009, 61, "columnNumber"], [1306, 28, 1009, 61], [1307, 14, 1009, 61], [1307, 21, 1009, 67], [1308, 12, 1009, 68], [1309, 14, 1009, 68, "fileName"], [1309, 22, 1009, 68], [1309, 24, 1009, 68, "_jsxFileName"], [1309, 36, 1009, 68], [1310, 14, 1009, 68, "lineNumber"], [1310, 24, 1009, 68], [1311, 14, 1009, 68, "columnNumber"], [1311, 26, 1009, 68], [1312, 12, 1009, 68], [1312, 19, 1010, 30], [1312, 20, 1010, 31], [1313, 10, 1010, 31], [1314, 12, 1010, 31, "fileName"], [1314, 20, 1010, 31], [1314, 22, 1010, 31, "_jsxFileName"], [1314, 34, 1010, 31], [1315, 12, 1010, 31, "lineNumber"], [1315, 22, 1010, 31], [1316, 12, 1010, 31, "columnNumber"], [1316, 24, 1010, 31], [1317, 10, 1010, 31], [1317, 17, 1011, 16], [1318, 8, 1011, 17], [1319, 10, 1011, 17, "fileName"], [1319, 18, 1011, 17], [1319, 20, 1011, 17, "_jsxFileName"], [1319, 32, 1011, 17], [1320, 10, 1011, 17, "lineNumber"], [1320, 20, 1011, 17], [1321, 10, 1011, 17, "columnNumber"], [1321, 22, 1011, 17], [1322, 8, 1011, 17], [1322, 15, 1012, 14], [1323, 6, 1012, 15], [1324, 8, 1012, 15, "fileName"], [1324, 16, 1012, 15], [1324, 18, 1012, 15, "_jsxFileName"], [1324, 30, 1012, 15], [1325, 8, 1012, 15, "lineNumber"], [1325, 18, 1012, 15], [1326, 8, 1012, 15, "columnNumber"], [1326, 20, 1012, 15], [1327, 6, 1012, 15], [1327, 13, 1013, 13], [1327, 14, 1013, 14], [1328, 4, 1013, 14], [1329, 6, 1013, 14, "fileName"], [1329, 14, 1013, 14], [1329, 16, 1013, 14, "_jsxFileName"], [1329, 28, 1013, 14], [1330, 6, 1013, 14, "lineNumber"], [1330, 16, 1013, 14], [1331, 6, 1013, 14, "columnNumber"], [1331, 18, 1013, 14], [1332, 4, 1013, 14], [1332, 11, 1014, 10], [1332, 12, 1014, 11], [1333, 2, 1016, 0], [1334, 2, 1016, 1, "_s"], [1334, 4, 1016, 1], [1334, 5, 51, 24, "EchoCameraWeb"], [1334, 18, 51, 37], [1335, 4, 51, 37], [1335, 12, 58, 42, "useCameraPermissions"], [1335, 44, 58, 62], [1335, 46, 72, 19, "useUpload"], [1335, 64, 72, 28], [1336, 2, 72, 28], [1337, 2, 72, 28, "_c"], [1337, 4, 72, 28], [1337, 7, 51, 24, "EchoCameraWeb"], [1337, 20, 51, 37], [1338, 2, 1017, 0], [1338, 8, 1017, 6, "styles"], [1338, 14, 1017, 12], [1338, 17, 1017, 15, "StyleSheet"], [1338, 36, 1017, 25], [1338, 37, 1017, 26, "create"], [1338, 43, 1017, 32], [1338, 44, 1017, 33], [1339, 4, 1018, 2, "container"], [1339, 13, 1018, 11], [1339, 15, 1018, 13], [1340, 6, 1019, 4, "flex"], [1340, 10, 1019, 8], [1340, 12, 1019, 10], [1340, 13, 1019, 11], [1341, 6, 1020, 4, "backgroundColor"], [1341, 21, 1020, 19], [1341, 23, 1020, 21], [1342, 4, 1021, 2], [1342, 5, 1021, 3], [1343, 4, 1022, 2, "cameraContainer"], [1343, 19, 1022, 17], [1343, 21, 1022, 19], [1344, 6, 1023, 4, "flex"], [1344, 10, 1023, 8], [1344, 12, 1023, 10], [1344, 13, 1023, 11], [1345, 6, 1024, 4, "max<PERSON><PERSON><PERSON>"], [1345, 14, 1024, 12], [1345, 16, 1024, 14], [1345, 19, 1024, 17], [1346, 6, 1025, 4, "alignSelf"], [1346, 15, 1025, 13], [1346, 17, 1025, 15], [1346, 25, 1025, 23], [1347, 6, 1026, 4, "width"], [1347, 11, 1026, 9], [1347, 13, 1026, 11], [1348, 4, 1027, 2], [1348, 5, 1027, 3], [1349, 4, 1028, 2, "camera"], [1349, 10, 1028, 8], [1349, 12, 1028, 10], [1350, 6, 1029, 4, "flex"], [1350, 10, 1029, 8], [1350, 12, 1029, 10], [1351, 4, 1030, 2], [1351, 5, 1030, 3], [1352, 4, 1031, 2, "headerOverlay"], [1352, 17, 1031, 15], [1352, 19, 1031, 17], [1353, 6, 1032, 4, "position"], [1353, 14, 1032, 12], [1353, 16, 1032, 14], [1353, 26, 1032, 24], [1354, 6, 1033, 4, "top"], [1354, 9, 1033, 7], [1354, 11, 1033, 9], [1354, 12, 1033, 10], [1355, 6, 1034, 4, "left"], [1355, 10, 1034, 8], [1355, 12, 1034, 10], [1355, 13, 1034, 11], [1356, 6, 1035, 4, "right"], [1356, 11, 1035, 9], [1356, 13, 1035, 11], [1356, 14, 1035, 12], [1357, 6, 1036, 4, "backgroundColor"], [1357, 21, 1036, 19], [1357, 23, 1036, 21], [1357, 36, 1036, 34], [1358, 6, 1037, 4, "paddingTop"], [1358, 16, 1037, 14], [1358, 18, 1037, 16], [1358, 20, 1037, 18], [1359, 6, 1038, 4, "paddingHorizontal"], [1359, 23, 1038, 21], [1359, 25, 1038, 23], [1359, 27, 1038, 25], [1360, 6, 1039, 4, "paddingBottom"], [1360, 19, 1039, 17], [1360, 21, 1039, 19], [1361, 4, 1040, 2], [1361, 5, 1040, 3], [1362, 4, 1041, 2, "headerContent"], [1362, 17, 1041, 15], [1362, 19, 1041, 17], [1363, 6, 1042, 4, "flexDirection"], [1363, 19, 1042, 17], [1363, 21, 1042, 19], [1363, 26, 1042, 24], [1364, 6, 1043, 4, "justifyContent"], [1364, 20, 1043, 18], [1364, 22, 1043, 20], [1364, 37, 1043, 35], [1365, 6, 1044, 4, "alignItems"], [1365, 16, 1044, 14], [1365, 18, 1044, 16], [1366, 4, 1045, 2], [1366, 5, 1045, 3], [1367, 4, 1046, 2, "headerLeft"], [1367, 14, 1046, 12], [1367, 16, 1046, 14], [1368, 6, 1047, 4, "flex"], [1368, 10, 1047, 8], [1368, 12, 1047, 10], [1369, 4, 1048, 2], [1369, 5, 1048, 3], [1370, 4, 1049, 2, "headerTitle"], [1370, 15, 1049, 13], [1370, 17, 1049, 15], [1371, 6, 1050, 4, "fontSize"], [1371, 14, 1050, 12], [1371, 16, 1050, 14], [1371, 18, 1050, 16], [1372, 6, 1051, 4, "fontWeight"], [1372, 16, 1051, 14], [1372, 18, 1051, 16], [1372, 23, 1051, 21], [1373, 6, 1052, 4, "color"], [1373, 11, 1052, 9], [1373, 13, 1052, 11], [1373, 19, 1052, 17], [1374, 6, 1053, 4, "marginBottom"], [1374, 18, 1053, 16], [1374, 20, 1053, 18], [1375, 4, 1054, 2], [1375, 5, 1054, 3], [1376, 4, 1055, 2, "subtitleRow"], [1376, 15, 1055, 13], [1376, 17, 1055, 15], [1377, 6, 1056, 4, "flexDirection"], [1377, 19, 1056, 17], [1377, 21, 1056, 19], [1377, 26, 1056, 24], [1378, 6, 1057, 4, "alignItems"], [1378, 16, 1057, 14], [1378, 18, 1057, 16], [1378, 26, 1057, 24], [1379, 6, 1058, 4, "marginBottom"], [1379, 18, 1058, 16], [1379, 20, 1058, 18], [1380, 4, 1059, 2], [1380, 5, 1059, 3], [1381, 4, 1060, 2, "webIcon"], [1381, 11, 1060, 9], [1381, 13, 1060, 11], [1382, 6, 1061, 4, "fontSize"], [1382, 14, 1061, 12], [1382, 16, 1061, 14], [1382, 18, 1061, 16], [1383, 6, 1062, 4, "marginRight"], [1383, 17, 1062, 15], [1383, 19, 1062, 17], [1384, 4, 1063, 2], [1384, 5, 1063, 3], [1385, 4, 1064, 2, "headerSubtitle"], [1385, 18, 1064, 16], [1385, 20, 1064, 18], [1386, 6, 1065, 4, "fontSize"], [1386, 14, 1065, 12], [1386, 16, 1065, 14], [1386, 18, 1065, 16], [1387, 6, 1066, 4, "color"], [1387, 11, 1066, 9], [1387, 13, 1066, 11], [1387, 19, 1066, 17], [1388, 6, 1067, 4, "opacity"], [1388, 13, 1067, 11], [1388, 15, 1067, 13], [1389, 4, 1068, 2], [1389, 5, 1068, 3], [1390, 4, 1069, 2, "challengeRow"], [1390, 16, 1069, 14], [1390, 18, 1069, 16], [1391, 6, 1070, 4, "flexDirection"], [1391, 19, 1070, 17], [1391, 21, 1070, 19], [1391, 26, 1070, 24], [1392, 6, 1071, 4, "alignItems"], [1392, 16, 1071, 14], [1392, 18, 1071, 16], [1393, 4, 1072, 2], [1393, 5, 1072, 3], [1394, 4, 1073, 2, "challengeCode"], [1394, 17, 1073, 15], [1394, 19, 1073, 17], [1395, 6, 1074, 4, "fontSize"], [1395, 14, 1074, 12], [1395, 16, 1074, 14], [1395, 18, 1074, 16], [1396, 6, 1075, 4, "color"], [1396, 11, 1075, 9], [1396, 13, 1075, 11], [1396, 19, 1075, 17], [1397, 6, 1076, 4, "marginLeft"], [1397, 16, 1076, 14], [1397, 18, 1076, 16], [1397, 19, 1076, 17], [1398, 6, 1077, 4, "fontFamily"], [1398, 16, 1077, 14], [1398, 18, 1077, 16], [1399, 4, 1078, 2], [1399, 5, 1078, 3], [1400, 4, 1079, 2, "closeButton"], [1400, 15, 1079, 13], [1400, 17, 1079, 15], [1401, 6, 1080, 4, "padding"], [1401, 13, 1080, 11], [1401, 15, 1080, 13], [1402, 4, 1081, 2], [1402, 5, 1081, 3], [1403, 4, 1082, 2, "privacyNotice"], [1403, 17, 1082, 15], [1403, 19, 1082, 17], [1404, 6, 1083, 4, "position"], [1404, 14, 1083, 12], [1404, 16, 1083, 14], [1404, 26, 1083, 24], [1405, 6, 1084, 4, "top"], [1405, 9, 1084, 7], [1405, 11, 1084, 9], [1405, 14, 1084, 12], [1406, 6, 1085, 4, "left"], [1406, 10, 1085, 8], [1406, 12, 1085, 10], [1406, 14, 1085, 12], [1407, 6, 1086, 4, "right"], [1407, 11, 1086, 9], [1407, 13, 1086, 11], [1407, 15, 1086, 13], [1408, 6, 1087, 4, "backgroundColor"], [1408, 21, 1087, 19], [1408, 23, 1087, 21], [1408, 48, 1087, 46], [1409, 6, 1088, 4, "borderRadius"], [1409, 18, 1088, 16], [1409, 20, 1088, 18], [1409, 21, 1088, 19], [1410, 6, 1089, 4, "padding"], [1410, 13, 1089, 11], [1410, 15, 1089, 13], [1410, 17, 1089, 15], [1411, 6, 1090, 4, "flexDirection"], [1411, 19, 1090, 17], [1411, 21, 1090, 19], [1411, 26, 1090, 24], [1412, 6, 1091, 4, "alignItems"], [1412, 16, 1091, 14], [1412, 18, 1091, 16], [1413, 4, 1092, 2], [1413, 5, 1092, 3], [1414, 4, 1093, 2, "privacyText"], [1414, 15, 1093, 13], [1414, 17, 1093, 15], [1415, 6, 1094, 4, "color"], [1415, 11, 1094, 9], [1415, 13, 1094, 11], [1415, 19, 1094, 17], [1416, 6, 1095, 4, "fontSize"], [1416, 14, 1095, 12], [1416, 16, 1095, 14], [1416, 18, 1095, 16], [1417, 6, 1096, 4, "marginLeft"], [1417, 16, 1096, 14], [1417, 18, 1096, 16], [1417, 19, 1096, 17], [1418, 6, 1097, 4, "flex"], [1418, 10, 1097, 8], [1418, 12, 1097, 10], [1419, 4, 1098, 2], [1419, 5, 1098, 3], [1420, 4, 1099, 2, "footer<PERSON><PERSON><PERSON>"], [1420, 17, 1099, 15], [1420, 19, 1099, 17], [1421, 6, 1100, 4, "position"], [1421, 14, 1100, 12], [1421, 16, 1100, 14], [1421, 26, 1100, 24], [1422, 6, 1101, 4, "bottom"], [1422, 12, 1101, 10], [1422, 14, 1101, 12], [1422, 15, 1101, 13], [1423, 6, 1102, 4, "left"], [1423, 10, 1102, 8], [1423, 12, 1102, 10], [1423, 13, 1102, 11], [1424, 6, 1103, 4, "right"], [1424, 11, 1103, 9], [1424, 13, 1103, 11], [1424, 14, 1103, 12], [1425, 6, 1104, 4, "backgroundColor"], [1425, 21, 1104, 19], [1425, 23, 1104, 21], [1425, 36, 1104, 34], [1426, 6, 1105, 4, "paddingBottom"], [1426, 19, 1105, 17], [1426, 21, 1105, 19], [1426, 23, 1105, 21], [1427, 6, 1106, 4, "paddingTop"], [1427, 16, 1106, 14], [1427, 18, 1106, 16], [1427, 20, 1106, 18], [1428, 6, 1107, 4, "alignItems"], [1428, 16, 1107, 14], [1428, 18, 1107, 16], [1429, 4, 1108, 2], [1429, 5, 1108, 3], [1430, 4, 1109, 2, "instruction"], [1430, 15, 1109, 13], [1430, 17, 1109, 15], [1431, 6, 1110, 4, "fontSize"], [1431, 14, 1110, 12], [1431, 16, 1110, 14], [1431, 18, 1110, 16], [1432, 6, 1111, 4, "color"], [1432, 11, 1111, 9], [1432, 13, 1111, 11], [1432, 19, 1111, 17], [1433, 6, 1112, 4, "marginBottom"], [1433, 18, 1112, 16], [1433, 20, 1112, 18], [1434, 4, 1113, 2], [1434, 5, 1113, 3], [1435, 4, 1114, 2, "shutterButton"], [1435, 17, 1114, 15], [1435, 19, 1114, 17], [1436, 6, 1115, 4, "width"], [1436, 11, 1115, 9], [1436, 13, 1115, 11], [1436, 15, 1115, 13], [1437, 6, 1116, 4, "height"], [1437, 12, 1116, 10], [1437, 14, 1116, 12], [1437, 16, 1116, 14], [1438, 6, 1117, 4, "borderRadius"], [1438, 18, 1117, 16], [1438, 20, 1117, 18], [1438, 22, 1117, 20], [1439, 6, 1118, 4, "backgroundColor"], [1439, 21, 1118, 19], [1439, 23, 1118, 21], [1439, 29, 1118, 27], [1440, 6, 1119, 4, "justifyContent"], [1440, 20, 1119, 18], [1440, 22, 1119, 20], [1440, 30, 1119, 28], [1441, 6, 1120, 4, "alignItems"], [1441, 16, 1120, 14], [1441, 18, 1120, 16], [1441, 26, 1120, 24], [1442, 6, 1121, 4, "marginBottom"], [1442, 18, 1121, 16], [1442, 20, 1121, 18], [1442, 22, 1121, 20], [1443, 6, 1122, 4], [1443, 9, 1122, 7, "Platform"], [1443, 26, 1122, 15], [1443, 27, 1122, 16, "select"], [1443, 33, 1122, 22], [1443, 34, 1122, 23], [1444, 8, 1123, 6, "ios"], [1444, 11, 1123, 9], [1444, 13, 1123, 11], [1445, 10, 1124, 8, "shadowColor"], [1445, 21, 1124, 19], [1445, 23, 1124, 21], [1445, 32, 1124, 30], [1446, 10, 1125, 8, "shadowOffset"], [1446, 22, 1125, 20], [1446, 24, 1125, 22], [1447, 12, 1125, 24, "width"], [1447, 17, 1125, 29], [1447, 19, 1125, 31], [1447, 20, 1125, 32], [1448, 12, 1125, 34, "height"], [1448, 18, 1125, 40], [1448, 20, 1125, 42], [1449, 10, 1125, 44], [1449, 11, 1125, 45], [1450, 10, 1126, 8, "shadowOpacity"], [1450, 23, 1126, 21], [1450, 25, 1126, 23], [1450, 28, 1126, 26], [1451, 10, 1127, 8, "shadowRadius"], [1451, 22, 1127, 20], [1451, 24, 1127, 22], [1452, 8, 1128, 6], [1452, 9, 1128, 7], [1453, 8, 1129, 6, "android"], [1453, 15, 1129, 13], [1453, 17, 1129, 15], [1454, 10, 1130, 8, "elevation"], [1454, 19, 1130, 17], [1454, 21, 1130, 19], [1455, 8, 1131, 6], [1455, 9, 1131, 7], [1456, 8, 1132, 6, "web"], [1456, 11, 1132, 9], [1456, 13, 1132, 11], [1457, 10, 1133, 8, "boxShadow"], [1457, 19, 1133, 17], [1457, 21, 1133, 19], [1458, 8, 1134, 6], [1459, 6, 1135, 4], [1459, 7, 1135, 5], [1460, 4, 1136, 2], [1460, 5, 1136, 3], [1461, 4, 1137, 2, "shutterButtonDisabled"], [1461, 25, 1137, 23], [1461, 27, 1137, 25], [1462, 6, 1138, 4, "opacity"], [1462, 13, 1138, 11], [1462, 15, 1138, 13], [1463, 4, 1139, 2], [1463, 5, 1139, 3], [1464, 4, 1140, 2, "shutterInner"], [1464, 16, 1140, 14], [1464, 18, 1140, 16], [1465, 6, 1141, 4, "width"], [1465, 11, 1141, 9], [1465, 13, 1141, 11], [1465, 15, 1141, 13], [1466, 6, 1142, 4, "height"], [1466, 12, 1142, 10], [1466, 14, 1142, 12], [1466, 16, 1142, 14], [1467, 6, 1143, 4, "borderRadius"], [1467, 18, 1143, 16], [1467, 20, 1143, 18], [1467, 22, 1143, 20], [1468, 6, 1144, 4, "backgroundColor"], [1468, 21, 1144, 19], [1468, 23, 1144, 21], [1468, 29, 1144, 27], [1469, 6, 1145, 4, "borderWidth"], [1469, 17, 1145, 15], [1469, 19, 1145, 17], [1469, 20, 1145, 18], [1470, 6, 1146, 4, "borderColor"], [1470, 17, 1146, 15], [1470, 19, 1146, 17], [1471, 4, 1147, 2], [1471, 5, 1147, 3], [1472, 4, 1148, 2, "privacyNote"], [1472, 15, 1148, 13], [1472, 17, 1148, 15], [1473, 6, 1149, 4, "fontSize"], [1473, 14, 1149, 12], [1473, 16, 1149, 14], [1473, 18, 1149, 16], [1474, 6, 1150, 4, "color"], [1474, 11, 1150, 9], [1474, 13, 1150, 11], [1475, 4, 1151, 2], [1475, 5, 1151, 3], [1476, 4, 1152, 2, "processingModal"], [1476, 19, 1152, 17], [1476, 21, 1152, 19], [1477, 6, 1153, 4, "flex"], [1477, 10, 1153, 8], [1477, 12, 1153, 10], [1477, 13, 1153, 11], [1478, 6, 1154, 4, "backgroundColor"], [1478, 21, 1154, 19], [1478, 23, 1154, 21], [1478, 43, 1154, 41], [1479, 6, 1155, 4, "justifyContent"], [1479, 20, 1155, 18], [1479, 22, 1155, 20], [1479, 30, 1155, 28], [1480, 6, 1156, 4, "alignItems"], [1480, 16, 1156, 14], [1480, 18, 1156, 16], [1481, 4, 1157, 2], [1481, 5, 1157, 3], [1482, 4, 1158, 2, "processingContent"], [1482, 21, 1158, 19], [1482, 23, 1158, 21], [1483, 6, 1159, 4, "backgroundColor"], [1483, 21, 1159, 19], [1483, 23, 1159, 21], [1483, 29, 1159, 27], [1484, 6, 1160, 4, "borderRadius"], [1484, 18, 1160, 16], [1484, 20, 1160, 18], [1484, 22, 1160, 20], [1485, 6, 1161, 4, "padding"], [1485, 13, 1161, 11], [1485, 15, 1161, 13], [1485, 17, 1161, 15], [1486, 6, 1162, 4, "width"], [1486, 11, 1162, 9], [1486, 13, 1162, 11], [1486, 18, 1162, 16], [1487, 6, 1163, 4, "max<PERSON><PERSON><PERSON>"], [1487, 14, 1163, 12], [1487, 16, 1163, 14], [1487, 19, 1163, 17], [1488, 6, 1164, 4, "alignItems"], [1488, 16, 1164, 14], [1488, 18, 1164, 16], [1489, 4, 1165, 2], [1489, 5, 1165, 3], [1490, 4, 1166, 2, "processingTitle"], [1490, 19, 1166, 17], [1490, 21, 1166, 19], [1491, 6, 1167, 4, "fontSize"], [1491, 14, 1167, 12], [1491, 16, 1167, 14], [1491, 18, 1167, 16], [1492, 6, 1168, 4, "fontWeight"], [1492, 16, 1168, 14], [1492, 18, 1168, 16], [1492, 23, 1168, 21], [1493, 6, 1169, 4, "color"], [1493, 11, 1169, 9], [1493, 13, 1169, 11], [1493, 22, 1169, 20], [1494, 6, 1170, 4, "marginTop"], [1494, 15, 1170, 13], [1494, 17, 1170, 15], [1494, 19, 1170, 17], [1495, 6, 1171, 4, "marginBottom"], [1495, 18, 1171, 16], [1495, 20, 1171, 18], [1496, 4, 1172, 2], [1496, 5, 1172, 3], [1497, 4, 1173, 2, "progressBar"], [1497, 15, 1173, 13], [1497, 17, 1173, 15], [1498, 6, 1174, 4, "width"], [1498, 11, 1174, 9], [1498, 13, 1174, 11], [1498, 19, 1174, 17], [1499, 6, 1175, 4, "height"], [1499, 12, 1175, 10], [1499, 14, 1175, 12], [1499, 15, 1175, 13], [1500, 6, 1176, 4, "backgroundColor"], [1500, 21, 1176, 19], [1500, 23, 1176, 21], [1500, 32, 1176, 30], [1501, 6, 1177, 4, "borderRadius"], [1501, 18, 1177, 16], [1501, 20, 1177, 18], [1501, 21, 1177, 19], [1502, 6, 1178, 4, "overflow"], [1502, 14, 1178, 12], [1502, 16, 1178, 14], [1502, 24, 1178, 22], [1503, 6, 1179, 4, "marginBottom"], [1503, 18, 1179, 16], [1503, 20, 1179, 18], [1504, 4, 1180, 2], [1504, 5, 1180, 3], [1505, 4, 1181, 2, "progressFill"], [1505, 16, 1181, 14], [1505, 18, 1181, 16], [1506, 6, 1182, 4, "height"], [1506, 12, 1182, 10], [1506, 14, 1182, 12], [1506, 20, 1182, 18], [1507, 6, 1183, 4, "backgroundColor"], [1507, 21, 1183, 19], [1507, 23, 1183, 21], [1507, 32, 1183, 30], [1508, 6, 1184, 4, "borderRadius"], [1508, 18, 1184, 16], [1508, 20, 1184, 18], [1509, 4, 1185, 2], [1509, 5, 1185, 3], [1510, 4, 1186, 2, "processingDescription"], [1510, 25, 1186, 23], [1510, 27, 1186, 25], [1511, 6, 1187, 4, "fontSize"], [1511, 14, 1187, 12], [1511, 16, 1187, 14], [1511, 18, 1187, 16], [1512, 6, 1188, 4, "color"], [1512, 11, 1188, 9], [1512, 13, 1188, 11], [1512, 22, 1188, 20], [1513, 6, 1189, 4, "textAlign"], [1513, 15, 1189, 13], [1513, 17, 1189, 15], [1514, 4, 1190, 2], [1514, 5, 1190, 3], [1515, 4, 1191, 2, "successIcon"], [1515, 15, 1191, 13], [1515, 17, 1191, 15], [1516, 6, 1192, 4, "marginTop"], [1516, 15, 1192, 13], [1516, 17, 1192, 15], [1517, 4, 1193, 2], [1517, 5, 1193, 3], [1518, 4, 1194, 2, "errorContent"], [1518, 16, 1194, 14], [1518, 18, 1194, 16], [1519, 6, 1195, 4, "backgroundColor"], [1519, 21, 1195, 19], [1519, 23, 1195, 21], [1519, 29, 1195, 27], [1520, 6, 1196, 4, "borderRadius"], [1520, 18, 1196, 16], [1520, 20, 1196, 18], [1520, 22, 1196, 20], [1521, 6, 1197, 4, "padding"], [1521, 13, 1197, 11], [1521, 15, 1197, 13], [1521, 17, 1197, 15], [1522, 6, 1198, 4, "width"], [1522, 11, 1198, 9], [1522, 13, 1198, 11], [1522, 18, 1198, 16], [1523, 6, 1199, 4, "max<PERSON><PERSON><PERSON>"], [1523, 14, 1199, 12], [1523, 16, 1199, 14], [1523, 19, 1199, 17], [1524, 6, 1200, 4, "alignItems"], [1524, 16, 1200, 14], [1524, 18, 1200, 16], [1525, 4, 1201, 2], [1525, 5, 1201, 3], [1526, 4, 1202, 2, "errorTitle"], [1526, 14, 1202, 12], [1526, 16, 1202, 14], [1527, 6, 1203, 4, "fontSize"], [1527, 14, 1203, 12], [1527, 16, 1203, 14], [1527, 18, 1203, 16], [1528, 6, 1204, 4, "fontWeight"], [1528, 16, 1204, 14], [1528, 18, 1204, 16], [1528, 23, 1204, 21], [1529, 6, 1205, 4, "color"], [1529, 11, 1205, 9], [1529, 13, 1205, 11], [1529, 22, 1205, 20], [1530, 6, 1206, 4, "marginTop"], [1530, 15, 1206, 13], [1530, 17, 1206, 15], [1530, 19, 1206, 17], [1531, 6, 1207, 4, "marginBottom"], [1531, 18, 1207, 16], [1531, 20, 1207, 18], [1532, 4, 1208, 2], [1532, 5, 1208, 3], [1533, 4, 1209, 2, "errorMessage"], [1533, 16, 1209, 14], [1533, 18, 1209, 16], [1534, 6, 1210, 4, "fontSize"], [1534, 14, 1210, 12], [1534, 16, 1210, 14], [1534, 18, 1210, 16], [1535, 6, 1211, 4, "color"], [1535, 11, 1211, 9], [1535, 13, 1211, 11], [1535, 22, 1211, 20], [1536, 6, 1212, 4, "textAlign"], [1536, 15, 1212, 13], [1536, 17, 1212, 15], [1536, 25, 1212, 23], [1537, 6, 1213, 4, "marginBottom"], [1537, 18, 1213, 16], [1537, 20, 1213, 18], [1538, 4, 1214, 2], [1538, 5, 1214, 3], [1539, 4, 1215, 2, "primaryButton"], [1539, 17, 1215, 15], [1539, 19, 1215, 17], [1540, 6, 1216, 4, "backgroundColor"], [1540, 21, 1216, 19], [1540, 23, 1216, 21], [1540, 32, 1216, 30], [1541, 6, 1217, 4, "paddingHorizontal"], [1541, 23, 1217, 21], [1541, 25, 1217, 23], [1541, 27, 1217, 25], [1542, 6, 1218, 4, "paddingVertical"], [1542, 21, 1218, 19], [1542, 23, 1218, 21], [1542, 25, 1218, 23], [1543, 6, 1219, 4, "borderRadius"], [1543, 18, 1219, 16], [1543, 20, 1219, 18], [1543, 21, 1219, 19], [1544, 6, 1220, 4, "marginTop"], [1544, 15, 1220, 13], [1544, 17, 1220, 15], [1545, 4, 1221, 2], [1545, 5, 1221, 3], [1546, 4, 1222, 2, "primaryButtonText"], [1546, 21, 1222, 19], [1546, 23, 1222, 21], [1547, 6, 1223, 4, "color"], [1547, 11, 1223, 9], [1547, 13, 1223, 11], [1547, 19, 1223, 17], [1548, 6, 1224, 4, "fontSize"], [1548, 14, 1224, 12], [1548, 16, 1224, 14], [1548, 18, 1224, 16], [1549, 6, 1225, 4, "fontWeight"], [1549, 16, 1225, 14], [1549, 18, 1225, 16], [1550, 4, 1226, 2], [1550, 5, 1226, 3], [1551, 4, 1227, 2, "secondaryButton"], [1551, 19, 1227, 17], [1551, 21, 1227, 19], [1552, 6, 1228, 4, "paddingHorizontal"], [1552, 23, 1228, 21], [1552, 25, 1228, 23], [1552, 27, 1228, 25], [1553, 6, 1229, 4, "paddingVertical"], [1553, 21, 1229, 19], [1553, 23, 1229, 21], [1553, 25, 1229, 23], [1554, 6, 1230, 4, "marginTop"], [1554, 15, 1230, 13], [1554, 17, 1230, 15], [1555, 4, 1231, 2], [1555, 5, 1231, 3], [1556, 4, 1232, 2, "secondaryButtonText"], [1556, 23, 1232, 21], [1556, 25, 1232, 23], [1557, 6, 1233, 4, "color"], [1557, 11, 1233, 9], [1557, 13, 1233, 11], [1557, 22, 1233, 20], [1558, 6, 1234, 4, "fontSize"], [1558, 14, 1234, 12], [1558, 16, 1234, 14], [1559, 4, 1235, 2], [1559, 5, 1235, 3], [1560, 4, 1236, 2, "permissionContent"], [1560, 21, 1236, 19], [1560, 23, 1236, 21], [1561, 6, 1237, 4, "flex"], [1561, 10, 1237, 8], [1561, 12, 1237, 10], [1561, 13, 1237, 11], [1562, 6, 1238, 4, "justifyContent"], [1562, 20, 1238, 18], [1562, 22, 1238, 20], [1562, 30, 1238, 28], [1563, 6, 1239, 4, "alignItems"], [1563, 16, 1239, 14], [1563, 18, 1239, 16], [1563, 26, 1239, 24], [1564, 6, 1240, 4, "padding"], [1564, 13, 1240, 11], [1564, 15, 1240, 13], [1565, 4, 1241, 2], [1565, 5, 1241, 3], [1566, 4, 1242, 2, "permissionTitle"], [1566, 19, 1242, 17], [1566, 21, 1242, 19], [1567, 6, 1243, 4, "fontSize"], [1567, 14, 1243, 12], [1567, 16, 1243, 14], [1567, 18, 1243, 16], [1568, 6, 1244, 4, "fontWeight"], [1568, 16, 1244, 14], [1568, 18, 1244, 16], [1568, 23, 1244, 21], [1569, 6, 1245, 4, "color"], [1569, 11, 1245, 9], [1569, 13, 1245, 11], [1569, 22, 1245, 20], [1570, 6, 1246, 4, "marginTop"], [1570, 15, 1246, 13], [1570, 17, 1246, 15], [1570, 19, 1246, 17], [1571, 6, 1247, 4, "marginBottom"], [1571, 18, 1247, 16], [1571, 20, 1247, 18], [1572, 4, 1248, 2], [1572, 5, 1248, 3], [1573, 4, 1249, 2, "permissionDescription"], [1573, 25, 1249, 23], [1573, 27, 1249, 25], [1574, 6, 1250, 4, "fontSize"], [1574, 14, 1250, 12], [1574, 16, 1250, 14], [1574, 18, 1250, 16], [1575, 6, 1251, 4, "color"], [1575, 11, 1251, 9], [1575, 13, 1251, 11], [1575, 22, 1251, 20], [1576, 6, 1252, 4, "textAlign"], [1576, 15, 1252, 13], [1576, 17, 1252, 15], [1576, 25, 1252, 23], [1577, 6, 1253, 4, "marginBottom"], [1577, 18, 1253, 16], [1577, 20, 1253, 18], [1578, 4, 1254, 2], [1578, 5, 1254, 3], [1579, 4, 1255, 2, "loadingText"], [1579, 15, 1255, 13], [1579, 17, 1255, 15], [1580, 6, 1256, 4, "color"], [1580, 11, 1256, 9], [1580, 13, 1256, 11], [1580, 22, 1256, 20], [1581, 6, 1257, 4, "marginTop"], [1581, 15, 1257, 13], [1581, 17, 1257, 15], [1582, 4, 1258, 2], [1582, 5, 1258, 3], [1583, 4, 1259, 2], [1584, 4, 1260, 2, "blurZone"], [1584, 12, 1260, 10], [1584, 14, 1260, 12], [1585, 6, 1261, 4, "position"], [1585, 14, 1261, 12], [1585, 16, 1261, 14], [1585, 26, 1261, 24], [1586, 6, 1262, 4, "overflow"], [1586, 14, 1262, 12], [1586, 16, 1262, 14], [1587, 4, 1263, 2], [1587, 5, 1263, 3], [1588, 4, 1264, 2, "previewChip"], [1588, 15, 1264, 13], [1588, 17, 1264, 15], [1589, 6, 1265, 4, "position"], [1589, 14, 1265, 12], [1589, 16, 1265, 14], [1589, 26, 1265, 24], [1590, 6, 1266, 4, "top"], [1590, 9, 1266, 7], [1590, 11, 1266, 9], [1590, 12, 1266, 10], [1591, 6, 1267, 4, "right"], [1591, 11, 1267, 9], [1591, 13, 1267, 11], [1591, 14, 1267, 12], [1592, 6, 1268, 4, "backgroundColor"], [1592, 21, 1268, 19], [1592, 23, 1268, 21], [1592, 40, 1268, 38], [1593, 6, 1269, 4, "paddingHorizontal"], [1593, 23, 1269, 21], [1593, 25, 1269, 23], [1593, 27, 1269, 25], [1594, 6, 1270, 4, "paddingVertical"], [1594, 21, 1270, 19], [1594, 23, 1270, 21], [1594, 24, 1270, 22], [1595, 6, 1271, 4, "borderRadius"], [1595, 18, 1271, 16], [1595, 20, 1271, 18], [1596, 4, 1272, 2], [1596, 5, 1272, 3], [1597, 4, 1273, 2, "previewChipText"], [1597, 19, 1273, 17], [1597, 21, 1273, 19], [1598, 6, 1274, 4, "color"], [1598, 11, 1274, 9], [1598, 13, 1274, 11], [1598, 19, 1274, 17], [1599, 6, 1275, 4, "fontSize"], [1599, 14, 1275, 12], [1599, 16, 1275, 14], [1599, 18, 1275, 16], [1600, 6, 1276, 4, "fontWeight"], [1600, 16, 1276, 14], [1600, 18, 1276, 16], [1601, 4, 1277, 2], [1602, 2, 1278, 0], [1602, 3, 1278, 1], [1602, 4, 1278, 2], [1603, 2, 1278, 3], [1603, 6, 1278, 3, "_c"], [1603, 8, 1278, 3], [1604, 2, 1278, 3, "$RefreshReg$"], [1604, 14, 1278, 3], [1604, 15, 1278, 3, "_c"], [1604, 17, 1278, 3], [1605, 0, 1278, 3], [1605, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;+BSE;GT0C;qBUE;GVQ;8BWE;GX4B;2BYE;GZa;wBaE;GbiB;0BcG;GdqD;0BeE;GfuB;gCgBE;kBCa;KDG;GhBC;mCkBG;wBdc,kCc;GlBoC;mCmBE;wBfa;OeI;oFCkC;UDM;8BEW;SFwB;uDfa;sBkBC,wBlB;OeC;GnBe;6BuBG;GvB6B;kCwBG;GxB8C;4ByBE;mBCmD;SDE;GzBO;uB2BE;G3BI;mC4BG;G5BM;YCE;GDK;oB6B2C;W7BG;yB8BC;W9BG;wB+BC;W/BI;CD4L"}}, "type": "js/module"}]}