{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "../external/reanimated/ReanimatedProxy", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 57, "index": 659}}], "key": "fbTuz78GsadUAYKlaJ3h86Nz//A=", "exportNames": ["*"]}}, {"name": "../external/reanimated/renderHelpers", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 660}, "end": {"line": 5, "column": 72, "index": 732}}], "key": "yayQYbKx/OsJEYI8tbD4MpE63c4=", "exportNames": ["*"]}}, {"name": "./Recorder/Recorder", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 733}, "end": {"line": 6, "column": 47, "index": 780}}], "key": "Yp14ztT0BW00eyWGD2Q+Jzb1RBs=", "exportNames": ["*"]}}, {"name": "./Recorder/Visitor", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 781}, "end": {"line": 7, "column": 43, "index": 824}}], "key": "RtqQDEGXeMBLTYLIYom32UKkm90=", "exportNames": ["*"]}}, {"name": "./Recorder/Player", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 825}, "end": {"line": 8, "column": 43, "index": 868}}], "key": "X8N3vNeOigFvxdAqlMJQVthRzjg=", "exportNames": ["*"]}}, {"name": "./Recorder/DrawingContext", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 869}, "end": {"line": 9, "column": 65, "index": 934}}], "key": "XL/bDMuZBKua/NUqczqJSfGoxSw=", "exportNames": ["*"]}}, {"name": "./Recorder/ReanimatedRecorder", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 10, "column": 0, "index": 935}, "end": {"line": 10, "column": 67, "index": 1002}}], "key": "6rLLzywRgZFdbC4rlzV3y0l/Uog=", "exportNames": ["*"]}}, {"name": "../views/api", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 1003}, "end": {"line": 11, "column": 22, "index": 1025}}], "key": "HAOgUmBotlALkoLLqYP3y1G9R74=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createContainer = exports.Container = void 0;\n  var _ReanimatedProxy = _interopRequireDefault(require(_dependencyMap[1], \"../external/reanimated/ReanimatedProxy\"));\n  var _renderHelpers = require(_dependencyMap[2], \"../external/reanimated/renderHelpers\");\n  var _Recorder = require(_dependencyMap[3], \"./Recorder/Recorder\");\n  var _Visitor = require(_dependencyMap[4], \"./Recorder/Visitor\");\n  var _Player = require(_dependencyMap[5], \"./Recorder/Player\");\n  var _DrawingContext = require(_dependencyMap[6], \"./Recorder/DrawingContext\");\n  var _ReanimatedRecorder = require(_dependencyMap[7], \"./Recorder/ReanimatedRecorder\");\n  require(_dependencyMap[8], \"../views/api\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  const _worklet_8967528127835_init_data = {\n    code: \"function ContainerJs1(Skia,nativeId,recording){const{createDrawingContext,replay,SkiaViewApi}=this.__closure;const rec=Skia.PictureRecorder();const canvas=rec.beginRecording();const ctx=createDrawingContext(Skia,recording.paintPool,canvas);replay(ctx,recording.commands);const picture=rec.finishRecordingAsPicture();SkiaViewApi.setJsiProperty(nativeId,\\\"picture\\\",picture);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\sksg\\\\Container.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"ContainerJs1\\\",\\\"Skia\\\",\\\"nativeId\\\",\\\"recording\\\",\\\"createDrawingContext\\\",\\\"replay\\\",\\\"SkiaViewApi\\\",\\\"__closure\\\",\\\"rec\\\",\\\"PictureRecorder\\\",\\\"canvas\\\",\\\"beginRecording\\\",\\\"ctx\\\",\\\"paintPool\\\",\\\"commands\\\",\\\"picture\\\",\\\"finishRecordingAsPicture\\\",\\\"setJsiProperty\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/sksg/Container.js\\\"],\\\"mappings\\\":\\\"AAWqB,QAAC,CAAAA,YAAMA,CAAAC,IAAQ,CAAEC,QAAS,CAAKC,SAAA,QAAAC,oBAAA,CAAAC,MAAA,CAAAC,WAAA,OAAAC,SAAA,CAGlD,KAAM,CAAAC,GAAG,CAAGP,IAAI,CAACQ,eAAe,CAAC,CAAC,CAClC,KAAM,CAAAC,MAAM,CAAGF,GAAG,CAACG,cAAc,CAAC,CAAC,CAGnC,KAAM,CAAAC,GAAG,CAAGR,oBAAoB,CAACH,IAAI,CAAEE,SAAS,CAACU,SAAS,CAAEH,MAAM,CAAC,CACnEL,MAAM,CAACO,GAAG,CAAET,SAAS,CAACW,QAAQ,CAAC,CAC/B,KAAM,CAAAC,OAAO,CAAGP,GAAG,CAACQ,wBAAwB,CAAC,CAAC,CAG9CV,WAAW,CAACW,cAAc,CAACf,QAAQ,CAAE,SAAS,CAAEa,OAAO,CAAC,CAC1D\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const drawOnscreen = function () {\n    const _e = [new global.Error(), -4, -27];\n    const ContainerJs1 = function (Skia, nativeId, recording) {\n      const rec = Skia.PictureRecorder();\n      const canvas = rec.beginRecording();\n      //const start = performance.now();\n\n      const ctx = (0, _DrawingContext.createDrawingContext)(Skia, recording.paintPool, canvas);\n      (0, _Player.replay)(ctx, recording.commands);\n      const picture = rec.finishRecordingAsPicture();\n      //const end = performance.now();\n      //console.log(\"Recording time: \", end - start);\n      SkiaViewApi.setJsiProperty(nativeId, \"picture\", picture);\n    };\n    ContainerJs1.__closure = {\n      createDrawingContext: _DrawingContext.createDrawingContext,\n      replay: _Player.replay,\n      SkiaViewApi\n    };\n    ContainerJs1.__workletHash = 8967528127835;\n    ContainerJs1.__initData = _worklet_8967528127835_init_data;\n    ContainerJs1.__stackDetails = _e;\n    return ContainerJs1;\n  }();\n  const _worklet_16400839430523_init_data = {\n    code: \"function ContainerJs2(nativeId,recorder){const{SkiaViewApi}=this.__closure;const picture=recorder.play();SkiaViewApi.setJsiProperty(nativeId,\\\"picture\\\",picture);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\sksg\\\\Container.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"ContainerJs2\\\",\\\"nativeId\\\",\\\"recorder\\\",\\\"SkiaViewApi\\\",\\\"__closure\\\",\\\"picture\\\",\\\"play\\\",\\\"setJsiProperty\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/sksg/Container.js\\\"],\\\"mappings\\\":\\\"AAyB2B,QAAC,CAAAA,YAAUA,CAAAC,QAAQ,CAAKC,QAAA,QAAAC,WAAA,OAAAC,SAAA,CAIjD,KAAM,CAAAC,OAAO,CAAGH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAG/BH,WAAW,CAACI,cAAc,CAACN,QAAQ,CAAE,SAAS,CAAEI,OAAO,CAAC,CAC1D\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const nativeDrawOnscreen = function () {\n    const _e = [new global.Error(), -2, -27];\n    const ContainerJs2 = function (nativeId, recorder) {\n      //const start = performance.now();\n      const picture = recorder.play();\n      //const end = performance.now();\n      //console.log(\"Recording time: \", end - start);\n      SkiaViewApi.setJsiProperty(nativeId, \"picture\", picture);\n    };\n    ContainerJs2.__closure = {\n      SkiaViewApi\n    };\n    ContainerJs2.__workletHash = 16400839430523;\n    ContainerJs2.__initData = _worklet_16400839430523_init_data;\n    ContainerJs2.__stackDetails = _e;\n    return ContainerJs2;\n  }();\n  class Container {\n    constructor(Skia, nativeId) {\n      this.Skia = Skia;\n      this.nativeId = nativeId;\n      _defineProperty(this, \"_root\", []);\n      _defineProperty(this, \"recording\", null);\n      _defineProperty(this, \"unmounted\", false);\n    }\n    get root() {\n      return this._root;\n    }\n    set root(value) {\n      this._root = value;\n    }\n    unmount() {\n      this.unmounted = true;\n    }\n    drawOnCanvas(canvas) {\n      if (!this.recording) {\n        throw new Error(\"No recording to draw\");\n      }\n      const ctx = (0, _DrawingContext.createDrawingContext)(this.Skia, this.recording.paintPool, canvas);\n      (0, _Player.replay)(ctx, this.recording.commands);\n    }\n  }\n  exports.Container = Container;\n  class StaticContainer extends Container {\n    constructor(Skia, nativeId) {\n      super(Skia, nativeId);\n    }\n    redraw() {\n      const recorder = new _Recorder.Recorder();\n      (0, _Visitor.visit)(recorder, this.root);\n      this.recording = recorder.getRecording();\n      const isOnScreen = this.nativeId !== -1;\n      if (isOnScreen) {\n        const rec = this.Skia.PictureRecorder();\n        const canvas = rec.beginRecording();\n        this.drawOnCanvas(canvas);\n        const picture = rec.finishRecordingAsPicture();\n        SkiaViewApi.setJsiProperty(this.nativeId, \"picture\", picture);\n      }\n    }\n  }\n  const _worklet_6119364411013_init_data = {\n    code: \"function ContainerJs3(){const{drawOnscreen,Skia,nativeId,recording}=this.__closure;drawOnscreen(Skia,nativeId,recording);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\sksg\\\\Container.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"ContainerJs3\\\",\\\"drawOnscreen\\\",\\\"Skia\\\",\\\"nativeId\\\",\\\"recording\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/sksg/Container.js\\\"],\\\"mappings\\\":\\\"AAyGsC,SAAAA,YAAMA,CAAA,QAAAC,YAAA,CAAAC,IAAA,CAAAC,QAAA,CAAAC,SAAA,OAAAC,SAAA,CAGpCJ,YAAY,CAACC,IAAI,CAAEC,QAAQ,CAAEC,SAAS,CAAC,CACzC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_16394818889410_init_data = {\n    code: \"function ContainerJs4(){const{drawOnscreen,Skia,nativeId,recording}=this.__closure;drawOnscreen(Skia,nativeId,recording);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\sksg\\\\Container.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"ContainerJs4\\\",\\\"drawOnscreen\\\",\\\"Skia\\\",\\\"nativeId\\\",\\\"recording\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/sksg/Container.js\\\"],\\\"mappings\\\":\\\"AA+GgB,SAAAA,YAAMA,CAAA,QAAAC,YAAA,CAAAC,IAAA,CAAAC,QAAA,CAAAC,SAAA,OAAAC,SAAA,CAGhBJ,YAAY,CAACC,IAAI,CAAEC,QAAQ,CAAEC,SAAS,CAAC,CACzC\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  class ReanimatedContainer extends Container {\n    constructor(Skia, nativeId) {\n      super(Skia, nativeId);\n      _defineProperty(this, \"mapperId\", null);\n    }\n    redraw() {\n      if (this.mapperId !== null) {\n        _ReanimatedProxy.default.stopMapper(this.mapperId);\n      }\n      if (this.unmounted) {\n        return;\n      }\n      const recorder = new _Recorder.Recorder();\n      (0, _Visitor.visit)(recorder, this.root);\n      const record = recorder.getRecording();\n      const {\n        animationValues\n      } = record;\n      this.recording = {\n        commands: record.commands,\n        paintPool: record.paintPool\n      };\n      const {\n        nativeId,\n        Skia,\n        recording\n      } = this;\n      if (animationValues.size > 0) {\n        this.mapperId = _ReanimatedProxy.default.startMapper(function () {\n          const _e = [new global.Error(), -5, -27];\n          const ContainerJs3 = function () {\n            drawOnscreen(Skia, nativeId, recording);\n          };\n          ContainerJs3.__closure = {\n            drawOnscreen,\n            Skia,\n            nativeId,\n            recording\n          };\n          ContainerJs3.__workletHash = 6119364411013;\n          ContainerJs3.__initData = _worklet_6119364411013_init_data;\n          ContainerJs3.__stackDetails = _e;\n          return ContainerJs3;\n        }(), Array.from(animationValues));\n      }\n      _ReanimatedProxy.default.runOnUI(function () {\n        const _e = [new global.Error(), -5, -27];\n        const ContainerJs4 = function () {\n          drawOnscreen(Skia, nativeId, recording);\n        };\n        ContainerJs4.__closure = {\n          drawOnscreen,\n          Skia,\n          nativeId,\n          recording\n        };\n        ContainerJs4.__workletHash = 16394818889410;\n        ContainerJs4.__initData = _worklet_16394818889410_init_data;\n        ContainerJs4.__stackDetails = _e;\n        return ContainerJs4;\n      }())();\n    }\n  }\n  const _worklet_4890725954467_init_data = {\n    code: \"function ContainerJs5(){const{nativeDrawOnscreen,nativeId,sharedRecorder}=this.__closure;nativeDrawOnscreen(nativeId,sharedRecorder);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\sksg\\\\Container.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"ContainerJs5\\\",\\\"nativeDrawOnscreen\\\",\\\"nativeId\\\",\\\"sharedRecorder\\\",\\\"__closure\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/sksg/Container.js\\\"],\\\"mappings\\\":\\\"AA0IgB,SAAAA,YAAMA,CAAA,QAAAC,kBAAA,CAAAC,QAAA,CAAAC,cAAA,OAAAC,SAAA,CAGhBH,kBAAkB,CAACC,QAAQ,CAAEC,cAAc,CAAC,CAC9C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const _worklet_7727529195901_init_data = {\n    code: \"function ContainerJs6(){const{sharedRecorder,sharedValues,nativeDrawOnscreen,nativeId}=this.__closure;sharedRecorder.applyUpdates(sharedValues);nativeDrawOnscreen(nativeId,sharedRecorder);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\sksg\\\\Container.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"ContainerJs6\\\",\\\"sharedRecorder\\\",\\\"sharedValues\\\",\\\"nativeDrawOnscreen\\\",\\\"nativeId\\\",\\\"__closure\\\",\\\"applyUpdates\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/sksg/Container.js\\\"],\\\"mappings\\\":\\\"AAgJsC,SAAAA,YAAMA,CAAA,QAAAC,cAAA,CAAAC,YAAA,CAAAC,kBAAA,CAAAC,QAAA,OAAAC,SAAA,CAGpCJ,cAAc,CAACK,YAAY,CAACJ,YAAY,CAAC,CACzCC,kBAAkB,CAACC,QAAQ,CAAEH,cAAc,CAAC,CAC9C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  class NativeReanimatedContainer extends Container {\n    constructor(Skia, nativeId) {\n      super(Skia, nativeId);\n      _defineProperty(this, \"mapperId\", null);\n    }\n    redraw() {\n      if (this.mapperId !== null) {\n        _ReanimatedProxy.default.stopMapper(this.mapperId);\n      }\n      if (this.unmounted) {\n        return;\n      }\n      const {\n        nativeId,\n        Skia\n      } = this;\n      const recorder = new _ReanimatedRecorder.ReanimatedRecorder(Skia);\n      (0, _Visitor.visit)(recorder, this.root);\n      const sharedValues = recorder.getSharedValues();\n      const sharedRecorder = recorder.getRecorder();\n      _ReanimatedProxy.default.runOnUI(function () {\n        const _e = [new global.Error(), -4, -27];\n        const ContainerJs5 = function () {\n          nativeDrawOnscreen(nativeId, sharedRecorder);\n        };\n        ContainerJs5.__closure = {\n          nativeDrawOnscreen,\n          nativeId,\n          sharedRecorder\n        };\n        ContainerJs5.__workletHash = 4890725954467;\n        ContainerJs5.__initData = _worklet_4890725954467_init_data;\n        ContainerJs5.__stackDetails = _e;\n        return ContainerJs5;\n      }())();\n      if (sharedValues.length > 0) {\n        this.mapperId = _ReanimatedProxy.default.startMapper(function () {\n          const _e = [new global.Error(), -5, -27];\n          const ContainerJs6 = function () {\n            sharedRecorder.applyUpdates(sharedValues);\n            nativeDrawOnscreen(nativeId, sharedRecorder);\n          };\n          ContainerJs6.__closure = {\n            sharedRecorder,\n            sharedValues,\n            nativeDrawOnscreen,\n            nativeId\n          };\n          ContainerJs6.__workletHash = 7727529195901;\n          ContainerJs6.__initData = _worklet_7727529195901_init_data;\n          ContainerJs6.__stackDetails = _e;\n          return ContainerJs6;\n        }(), sharedValues);\n      }\n    }\n  }\n  const createContainer = (Skia, nativeId) => {\n    const web = global.SkiaViewApi && global.SkiaViewApi.web;\n    if (_renderHelpers.HAS_REANIMATED_3 && nativeId !== -1) {\n      if (!web) {\n        return new NativeReanimatedContainer(Skia, nativeId);\n      } else {\n        return new ReanimatedContainer(Skia, nativeId);\n      }\n    } else {\n      return new StaticContainer(Skia, nativeId);\n    }\n  };\n  exports.createContainer = createContainer;\n});", "lineCount": 290, "map": [[7, 2, 4, 0], [7, 6, 4, 0, "_ReanimatedProxy"], [7, 22, 4, 0], [7, 25, 4, 0, "_interopRequireDefault"], [7, 47, 4, 0], [7, 48, 4, 0, "require"], [7, 55, 4, 0], [7, 56, 4, 0, "_dependencyMap"], [7, 70, 4, 0], [8, 2, 5, 0], [8, 6, 5, 0, "_renderHelpers"], [8, 20, 5, 0], [8, 23, 5, 0, "require"], [8, 30, 5, 0], [8, 31, 5, 0, "_dependencyMap"], [8, 45, 5, 0], [9, 2, 6, 0], [9, 6, 6, 0, "_Recorder"], [9, 15, 6, 0], [9, 18, 6, 0, "require"], [9, 25, 6, 0], [9, 26, 6, 0, "_dependencyMap"], [9, 40, 6, 0], [10, 2, 7, 0], [10, 6, 7, 0, "_Visitor"], [10, 14, 7, 0], [10, 17, 7, 0, "require"], [10, 24, 7, 0], [10, 25, 7, 0, "_dependencyMap"], [10, 39, 7, 0], [11, 2, 8, 0], [11, 6, 8, 0, "_Player"], [11, 13, 8, 0], [11, 16, 8, 0, "require"], [11, 23, 8, 0], [11, 24, 8, 0, "_dependencyMap"], [11, 38, 8, 0], [12, 2, 9, 0], [12, 6, 9, 0, "_DrawingContext"], [12, 21, 9, 0], [12, 24, 9, 0, "require"], [12, 31, 9, 0], [12, 32, 9, 0, "_dependencyMap"], [12, 46, 9, 0], [13, 2, 10, 0], [13, 6, 10, 0, "_ReanimatedRecorder"], [13, 25, 10, 0], [13, 28, 10, 0, "require"], [13, 35, 10, 0], [13, 36, 10, 0, "_dependencyMap"], [13, 50, 10, 0], [14, 2, 11, 0, "require"], [14, 9, 11, 0], [14, 10, 11, 0, "_dependencyMap"], [14, 24, 11, 0], [15, 2, 1, 0], [15, 11, 1, 9, "_defineProperty"], [15, 26, 1, 24, "_defineProperty"], [15, 27, 1, 25, "e"], [15, 28, 1, 26], [15, 30, 1, 28, "r"], [15, 31, 1, 29], [15, 33, 1, 31, "t"], [15, 34, 1, 32], [15, 36, 1, 34], [16, 4, 1, 36], [16, 11, 1, 43], [16, 12, 1, 44, "r"], [16, 13, 1, 45], [16, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [16, 30, 1, 62], [16, 31, 1, 63, "r"], [16, 32, 1, 64], [16, 33, 1, 65], [16, 38, 1, 70, "e"], [16, 39, 1, 71], [16, 42, 1, 74, "Object"], [16, 48, 1, 80], [16, 49, 1, 81, "defineProperty"], [16, 63, 1, 95], [16, 64, 1, 96, "e"], [16, 65, 1, 97], [16, 67, 1, 99, "r"], [16, 68, 1, 100], [16, 70, 1, 102], [17, 6, 1, 104, "value"], [17, 11, 1, 109], [17, 13, 1, 111, "t"], [17, 14, 1, 112], [18, 6, 1, 114, "enumerable"], [18, 16, 1, 124], [18, 18, 1, 126], [18, 19, 1, 127], [18, 20, 1, 128], [19, 6, 1, 130, "configurable"], [19, 18, 1, 142], [19, 20, 1, 144], [19, 21, 1, 145], [19, 22, 1, 146], [20, 6, 1, 148, "writable"], [20, 14, 1, 156], [20, 16, 1, 158], [20, 17, 1, 159], [21, 4, 1, 161], [21, 5, 1, 162], [21, 6, 1, 163], [21, 9, 1, 166, "e"], [21, 10, 1, 167], [21, 11, 1, 168, "r"], [21, 12, 1, 169], [21, 13, 1, 170], [21, 16, 1, 173, "t"], [21, 17, 1, 174], [21, 19, 1, 176, "e"], [21, 20, 1, 177], [22, 2, 1, 179], [23, 2, 2, 0], [23, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [23, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [23, 26, 2, 24, "t"], [23, 27, 2, 25], [23, 29, 2, 27], [24, 4, 2, 29], [24, 8, 2, 33, "i"], [24, 9, 2, 34], [24, 12, 2, 37, "_toPrimitive"], [24, 24, 2, 49], [24, 25, 2, 50, "t"], [24, 26, 2, 51], [24, 28, 2, 53], [24, 36, 2, 61], [24, 37, 2, 62], [25, 4, 2, 64], [25, 11, 2, 71], [25, 19, 2, 79], [25, 23, 2, 83], [25, 30, 2, 90, "i"], [25, 31, 2, 91], [25, 34, 2, 94, "i"], [25, 35, 2, 95], [25, 38, 2, 98, "i"], [25, 39, 2, 99], [25, 42, 2, 102], [25, 44, 2, 104], [26, 2, 2, 106], [27, 2, 3, 0], [27, 11, 3, 9, "_toPrimitive"], [27, 23, 3, 21, "_toPrimitive"], [27, 24, 3, 22, "t"], [27, 25, 3, 23], [27, 27, 3, 25, "r"], [27, 28, 3, 26], [27, 30, 3, 28], [28, 4, 3, 30], [28, 8, 3, 34], [28, 16, 3, 42], [28, 20, 3, 46], [28, 27, 3, 53, "t"], [28, 28, 3, 54], [28, 32, 3, 58], [28, 33, 3, 59, "t"], [28, 34, 3, 60], [28, 36, 3, 62], [28, 43, 3, 69, "t"], [28, 44, 3, 70], [29, 4, 3, 72], [29, 8, 3, 76, "e"], [29, 9, 3, 77], [29, 12, 3, 80, "t"], [29, 13, 3, 81], [29, 14, 3, 82, "Symbol"], [29, 20, 3, 88], [29, 21, 3, 89, "toPrimitive"], [29, 32, 3, 100], [29, 33, 3, 101], [30, 4, 3, 103], [30, 8, 3, 107], [30, 13, 3, 112], [30, 14, 3, 113], [30, 19, 3, 118, "e"], [30, 20, 3, 119], [30, 22, 3, 121], [31, 6, 3, 123], [31, 10, 3, 127, "i"], [31, 11, 3, 128], [31, 14, 3, 131, "e"], [31, 15, 3, 132], [31, 16, 3, 133, "call"], [31, 20, 3, 137], [31, 21, 3, 138, "t"], [31, 22, 3, 139], [31, 24, 3, 141, "r"], [31, 25, 3, 142], [31, 29, 3, 146], [31, 38, 3, 155], [31, 39, 3, 156], [32, 6, 3, 158], [32, 10, 3, 162], [32, 18, 3, 170], [32, 22, 3, 174], [32, 29, 3, 181, "i"], [32, 30, 3, 182], [32, 32, 3, 184], [32, 39, 3, 191, "i"], [32, 40, 3, 192], [33, 6, 3, 194], [33, 12, 3, 200], [33, 16, 3, 204, "TypeError"], [33, 25, 3, 213], [33, 26, 3, 214], [33, 72, 3, 260], [33, 73, 3, 261], [34, 4, 3, 263], [35, 4, 3, 265], [35, 11, 3, 272], [35, 12, 3, 273], [35, 20, 3, 281], [35, 25, 3, 286, "r"], [35, 26, 3, 287], [35, 29, 3, 290, "String"], [35, 35, 3, 296], [35, 38, 3, 299, "Number"], [35, 44, 3, 305], [35, 46, 3, 307, "t"], [35, 47, 3, 308], [35, 48, 3, 309], [36, 2, 3, 311], [37, 2, 3, 312], [37, 8, 3, 312, "_worklet_8967528127835_init_data"], [37, 40, 3, 312], [38, 4, 3, 312, "code"], [38, 8, 3, 312], [39, 4, 3, 312, "location"], [39, 12, 3, 312], [40, 4, 3, 312, "sourceMap"], [40, 13, 3, 312], [41, 4, 3, 312, "version"], [41, 11, 3, 312], [42, 2, 3, 312], [43, 2, 12, 0], [43, 8, 12, 6, "drawOnscreen"], [43, 20, 12, 18], [43, 23, 12, 21], [44, 4, 12, 21], [44, 10, 12, 21, "_e"], [44, 12, 12, 21], [44, 20, 12, 21, "global"], [44, 26, 12, 21], [44, 27, 12, 21, "Error"], [44, 32, 12, 21], [45, 4, 12, 21], [45, 10, 12, 21, "ContainerJs1"], [45, 22, 12, 21], [45, 34, 12, 21, "ContainerJs1"], [45, 35, 12, 22, "Skia"], [45, 39, 12, 26], [45, 41, 12, 28, "nativeId"], [45, 49, 12, 36], [45, 51, 12, 38, "recording"], [45, 60, 12, 47], [45, 62, 12, 52], [46, 6, 15, 2], [46, 12, 15, 8, "rec"], [46, 15, 15, 11], [46, 18, 15, 14, "Skia"], [46, 22, 15, 18], [46, 23, 15, 19, "PictureRecorder"], [46, 38, 15, 34], [46, 39, 15, 35], [46, 40, 15, 36], [47, 6, 16, 2], [47, 12, 16, 8, "canvas"], [47, 18, 16, 14], [47, 21, 16, 17, "rec"], [47, 24, 16, 20], [47, 25, 16, 21, "beginRecording"], [47, 39, 16, 35], [47, 40, 16, 36], [47, 41, 16, 37], [48, 6, 17, 2], [50, 6, 19, 2], [50, 12, 19, 8, "ctx"], [50, 15, 19, 11], [50, 18, 19, 14], [50, 22, 19, 14, "createDrawingContext"], [50, 58, 19, 34], [50, 60, 19, 35, "Skia"], [50, 64, 19, 39], [50, 66, 19, 41, "recording"], [50, 75, 19, 50], [50, 76, 19, 51, "paintPool"], [50, 85, 19, 60], [50, 87, 19, 62, "canvas"], [50, 93, 19, 68], [50, 94, 19, 69], [51, 6, 20, 2], [51, 10, 20, 2, "replay"], [51, 24, 20, 8], [51, 26, 20, 9, "ctx"], [51, 29, 20, 12], [51, 31, 20, 14, "recording"], [51, 40, 20, 23], [51, 41, 20, 24, "commands"], [51, 49, 20, 32], [51, 50, 20, 33], [52, 6, 21, 2], [52, 12, 21, 8, "picture"], [52, 19, 21, 15], [52, 22, 21, 18, "rec"], [52, 25, 21, 21], [52, 26, 21, 22, "finishRecordingAsPicture"], [52, 50, 21, 46], [52, 51, 21, 47], [52, 52, 21, 48], [53, 6, 22, 2], [54, 6, 23, 2], [55, 6, 24, 2, "SkiaViewApi"], [55, 17, 24, 13], [55, 18, 24, 14, "setJsiProperty"], [55, 32, 24, 28], [55, 33, 24, 29, "nativeId"], [55, 41, 24, 37], [55, 43, 24, 39], [55, 52, 24, 48], [55, 54, 24, 50, "picture"], [55, 61, 24, 57], [55, 62, 24, 58], [56, 4, 25, 0], [56, 5, 25, 1], [57, 4, 25, 1, "ContainerJs1"], [57, 16, 25, 1], [57, 17, 25, 1, "__closure"], [57, 26, 25, 1], [58, 6, 25, 1, "createDrawingContext"], [58, 26, 25, 1], [58, 28, 19, 14, "createDrawingContext"], [58, 64, 19, 34], [59, 6, 19, 34, "replay"], [59, 12, 19, 34], [59, 14, 20, 2, "replay"], [59, 28, 20, 8], [60, 6, 20, 8, "SkiaViewApi"], [61, 4, 20, 8], [62, 4, 20, 8, "ContainerJs1"], [62, 16, 20, 8], [62, 17, 20, 8, "__workletHash"], [62, 30, 20, 8], [63, 4, 20, 8, "ContainerJs1"], [63, 16, 20, 8], [63, 17, 20, 8, "__initData"], [63, 27, 20, 8], [63, 30, 20, 8, "_worklet_8967528127835_init_data"], [63, 62, 20, 8], [64, 4, 20, 8, "ContainerJs1"], [64, 16, 20, 8], [64, 17, 20, 8, "__stackDetails"], [64, 31, 20, 8], [64, 34, 20, 8, "_e"], [64, 36, 20, 8], [65, 4, 20, 8], [65, 11, 20, 8, "ContainerJs1"], [65, 23, 20, 8], [66, 2, 20, 8], [66, 3, 12, 21], [66, 5, 25, 1], [67, 2, 25, 2], [67, 8, 25, 2, "_worklet_16400839430523_init_data"], [67, 41, 25, 2], [68, 4, 25, 2, "code"], [68, 8, 25, 2], [69, 4, 25, 2, "location"], [69, 12, 25, 2], [70, 4, 25, 2, "sourceMap"], [70, 13, 25, 2], [71, 4, 25, 2, "version"], [71, 11, 25, 2], [72, 2, 25, 2], [73, 2, 26, 0], [73, 8, 26, 6, "nativeDrawOnscreen"], [73, 26, 26, 24], [73, 29, 26, 27], [74, 4, 26, 27], [74, 10, 26, 27, "_e"], [74, 12, 26, 27], [74, 20, 26, 27, "global"], [74, 26, 26, 27], [74, 27, 26, 27, "Error"], [74, 32, 26, 27], [75, 4, 26, 27], [75, 10, 26, 27, "ContainerJs2"], [75, 22, 26, 27], [75, 34, 26, 27, "ContainerJs2"], [75, 35, 26, 28, "nativeId"], [75, 43, 26, 36], [75, 45, 26, 38, "recorder"], [75, 53, 26, 46], [75, 55, 26, 51], [76, 6, 29, 2], [77, 6, 30, 2], [77, 12, 30, 8, "picture"], [77, 19, 30, 15], [77, 22, 30, 18, "recorder"], [77, 30, 30, 26], [77, 31, 30, 27, "play"], [77, 35, 30, 31], [77, 36, 30, 32], [77, 37, 30, 33], [78, 6, 31, 2], [79, 6, 32, 2], [80, 6, 33, 2, "SkiaViewApi"], [80, 17, 33, 13], [80, 18, 33, 14, "setJsiProperty"], [80, 32, 33, 28], [80, 33, 33, 29, "nativeId"], [80, 41, 33, 37], [80, 43, 33, 39], [80, 52, 33, 48], [80, 54, 33, 50, "picture"], [80, 61, 33, 57], [80, 62, 33, 58], [81, 4, 34, 0], [81, 5, 34, 1], [82, 4, 34, 1, "ContainerJs2"], [82, 16, 34, 1], [82, 17, 34, 1, "__closure"], [82, 26, 34, 1], [83, 6, 34, 1, "SkiaViewApi"], [84, 4, 34, 1], [85, 4, 34, 1, "ContainerJs2"], [85, 16, 34, 1], [85, 17, 34, 1, "__workletHash"], [85, 30, 34, 1], [86, 4, 34, 1, "ContainerJs2"], [86, 16, 34, 1], [86, 17, 34, 1, "__initData"], [86, 27, 34, 1], [86, 30, 34, 1, "_worklet_16400839430523_init_data"], [86, 63, 34, 1], [87, 4, 34, 1, "ContainerJs2"], [87, 16, 34, 1], [87, 17, 34, 1, "__stackDetails"], [87, 31, 34, 1], [87, 34, 34, 1, "_e"], [87, 36, 34, 1], [88, 4, 34, 1], [88, 11, 34, 1, "ContainerJs2"], [88, 23, 34, 1], [89, 2, 34, 1], [89, 3, 26, 27], [89, 5, 34, 1], [90, 2, 35, 7], [90, 8, 35, 13, "Container"], [90, 17, 35, 22], [90, 18, 35, 23], [91, 4, 36, 2, "constructor"], [91, 15, 36, 13, "constructor"], [91, 16, 36, 14, "Skia"], [91, 20, 36, 18], [91, 22, 36, 20, "nativeId"], [91, 30, 36, 28], [91, 32, 36, 30], [92, 6, 37, 4], [92, 10, 37, 8], [92, 11, 37, 9, "Skia"], [92, 15, 37, 13], [92, 18, 37, 16, "Skia"], [92, 22, 37, 20], [93, 6, 38, 4], [93, 10, 38, 8], [93, 11, 38, 9, "nativeId"], [93, 19, 38, 17], [93, 22, 38, 20, "nativeId"], [93, 30, 38, 28], [94, 6, 39, 4, "_defineProperty"], [94, 21, 39, 19], [94, 22, 39, 20], [94, 26, 39, 24], [94, 28, 39, 26], [94, 35, 39, 33], [94, 37, 39, 35], [94, 39, 39, 37], [94, 40, 39, 38], [95, 6, 40, 4, "_defineProperty"], [95, 21, 40, 19], [95, 22, 40, 20], [95, 26, 40, 24], [95, 28, 40, 26], [95, 39, 40, 37], [95, 41, 40, 39], [95, 45, 40, 43], [95, 46, 40, 44], [96, 6, 41, 4, "_defineProperty"], [96, 21, 41, 19], [96, 22, 41, 20], [96, 26, 41, 24], [96, 28, 41, 26], [96, 39, 41, 37], [96, 41, 41, 39], [96, 46, 41, 44], [96, 47, 41, 45], [97, 4, 42, 2], [98, 4, 43, 2], [98, 8, 43, 6, "root"], [98, 12, 43, 10, "root"], [98, 13, 43, 10], [98, 15, 43, 13], [99, 6, 44, 4], [99, 13, 44, 11], [99, 17, 44, 15], [99, 18, 44, 16, "_root"], [99, 23, 44, 21], [100, 4, 45, 2], [101, 4, 46, 2], [101, 8, 46, 6, "root"], [101, 12, 46, 10, "root"], [101, 13, 46, 11, "value"], [101, 18, 46, 16], [101, 20, 46, 18], [102, 6, 47, 4], [102, 10, 47, 8], [102, 11, 47, 9, "_root"], [102, 16, 47, 14], [102, 19, 47, 17, "value"], [102, 24, 47, 22], [103, 4, 48, 2], [104, 4, 49, 2, "unmount"], [104, 11, 49, 9, "unmount"], [104, 12, 49, 9], [104, 14, 49, 12], [105, 6, 50, 4], [105, 10, 50, 8], [105, 11, 50, 9, "unmounted"], [105, 20, 50, 18], [105, 23, 50, 21], [105, 27, 50, 25], [106, 4, 51, 2], [107, 4, 52, 2, "drawOnCanvas"], [107, 16, 52, 14, "drawOnCanvas"], [107, 17, 52, 15, "canvas"], [107, 23, 52, 21], [107, 25, 52, 23], [108, 6, 53, 4], [108, 10, 53, 8], [108, 11, 53, 9], [108, 15, 53, 13], [108, 16, 53, 14, "recording"], [108, 25, 53, 23], [108, 27, 53, 25], [109, 8, 54, 6], [109, 14, 54, 12], [109, 18, 54, 16, "Error"], [109, 23, 54, 21], [109, 24, 54, 22], [109, 46, 54, 44], [109, 47, 54, 45], [110, 6, 55, 4], [111, 6, 56, 4], [111, 12, 56, 10, "ctx"], [111, 15, 56, 13], [111, 18, 56, 16], [111, 22, 56, 16, "createDrawingContext"], [111, 58, 56, 36], [111, 60, 56, 37], [111, 64, 56, 41], [111, 65, 56, 42, "Skia"], [111, 69, 56, 46], [111, 71, 56, 48], [111, 75, 56, 52], [111, 76, 56, 53, "recording"], [111, 85, 56, 62], [111, 86, 56, 63, "paintPool"], [111, 95, 56, 72], [111, 97, 56, 74, "canvas"], [111, 103, 56, 80], [111, 104, 56, 81], [112, 6, 57, 4], [112, 10, 57, 4, "replay"], [112, 24, 57, 10], [112, 26, 57, 11, "ctx"], [112, 29, 57, 14], [112, 31, 57, 16], [112, 35, 57, 20], [112, 36, 57, 21, "recording"], [112, 45, 57, 30], [112, 46, 57, 31, "commands"], [112, 54, 57, 39], [112, 55, 57, 40], [113, 4, 58, 2], [114, 2, 59, 0], [115, 2, 59, 1, "exports"], [115, 9, 59, 1], [115, 10, 59, 1, "Container"], [115, 19, 59, 1], [115, 22, 59, 1, "Container"], [115, 31, 59, 1], [116, 2, 60, 0], [116, 8, 60, 6, "StaticContainer"], [116, 23, 60, 21], [116, 32, 60, 30, "Container"], [116, 41, 60, 39], [116, 42, 60, 40], [117, 4, 61, 2, "constructor"], [117, 15, 61, 13, "constructor"], [117, 16, 61, 14, "Skia"], [117, 20, 61, 18], [117, 22, 61, 20, "nativeId"], [117, 30, 61, 28], [117, 32, 61, 30], [118, 6, 62, 4], [118, 11, 62, 9], [118, 12, 62, 10, "Skia"], [118, 16, 62, 14], [118, 18, 62, 16, "nativeId"], [118, 26, 62, 24], [118, 27, 62, 25], [119, 4, 63, 2], [120, 4, 64, 2, "redraw"], [120, 10, 64, 8, "redraw"], [120, 11, 64, 8], [120, 13, 64, 11], [121, 6, 65, 4], [121, 12, 65, 10, "recorder"], [121, 20, 65, 18], [121, 23, 65, 21], [121, 27, 65, 25, "Recorder"], [121, 45, 65, 33], [121, 46, 65, 34], [121, 47, 65, 35], [122, 6, 66, 4], [122, 10, 66, 4, "visit"], [122, 24, 66, 9], [122, 26, 66, 10, "recorder"], [122, 34, 66, 18], [122, 36, 66, 20], [122, 40, 66, 24], [122, 41, 66, 25, "root"], [122, 45, 66, 29], [122, 46, 66, 30], [123, 6, 67, 4], [123, 10, 67, 8], [123, 11, 67, 9, "recording"], [123, 20, 67, 18], [123, 23, 67, 21, "recorder"], [123, 31, 67, 29], [123, 32, 67, 30, "getRecording"], [123, 44, 67, 42], [123, 45, 67, 43], [123, 46, 67, 44], [124, 6, 68, 4], [124, 12, 68, 10, "isOnScreen"], [124, 22, 68, 20], [124, 25, 68, 23], [124, 29, 68, 27], [124, 30, 68, 28, "nativeId"], [124, 38, 68, 36], [124, 43, 68, 41], [124, 44, 68, 42], [124, 45, 68, 43], [125, 6, 69, 4], [125, 10, 69, 8, "isOnScreen"], [125, 20, 69, 18], [125, 22, 69, 20], [126, 8, 70, 6], [126, 14, 70, 12, "rec"], [126, 17, 70, 15], [126, 20, 70, 18], [126, 24, 70, 22], [126, 25, 70, 23, "Skia"], [126, 29, 70, 27], [126, 30, 70, 28, "PictureRecorder"], [126, 45, 70, 43], [126, 46, 70, 44], [126, 47, 70, 45], [127, 8, 71, 6], [127, 14, 71, 12, "canvas"], [127, 20, 71, 18], [127, 23, 71, 21, "rec"], [127, 26, 71, 24], [127, 27, 71, 25, "beginRecording"], [127, 41, 71, 39], [127, 42, 71, 40], [127, 43, 71, 41], [128, 8, 72, 6], [128, 12, 72, 10], [128, 13, 72, 11, "drawOnCanvas"], [128, 25, 72, 23], [128, 26, 72, 24, "canvas"], [128, 32, 72, 30], [128, 33, 72, 31], [129, 8, 73, 6], [129, 14, 73, 12, "picture"], [129, 21, 73, 19], [129, 24, 73, 22, "rec"], [129, 27, 73, 25], [129, 28, 73, 26, "finishRecordingAsPicture"], [129, 52, 73, 50], [129, 53, 73, 51], [129, 54, 73, 52], [130, 8, 74, 6, "SkiaViewApi"], [130, 19, 74, 17], [130, 20, 74, 18, "setJsiProperty"], [130, 34, 74, 32], [130, 35, 74, 33], [130, 39, 74, 37], [130, 40, 74, 38, "nativeId"], [130, 48, 74, 46], [130, 50, 74, 48], [130, 59, 74, 57], [130, 61, 74, 59, "picture"], [130, 68, 74, 66], [130, 69, 74, 67], [131, 6, 75, 4], [132, 4, 76, 2], [133, 2, 77, 0], [134, 2, 77, 1], [134, 8, 77, 1, "_worklet_6119364411013_init_data"], [134, 40, 77, 1], [135, 4, 77, 1, "code"], [135, 8, 77, 1], [136, 4, 77, 1, "location"], [136, 12, 77, 1], [137, 4, 77, 1, "sourceMap"], [137, 13, 77, 1], [138, 4, 77, 1, "version"], [138, 11, 77, 1], [139, 2, 77, 1], [140, 2, 77, 1], [140, 8, 77, 1, "_worklet_16394818889410_init_data"], [140, 41, 77, 1], [141, 4, 77, 1, "code"], [141, 8, 77, 1], [142, 4, 77, 1, "location"], [142, 12, 77, 1], [143, 4, 77, 1, "sourceMap"], [143, 13, 77, 1], [144, 4, 77, 1, "version"], [144, 11, 77, 1], [145, 2, 77, 1], [146, 2, 78, 0], [146, 8, 78, 6, "Reanimated<PERSON><PERSON><PERSON>"], [146, 27, 78, 25], [146, 36, 78, 34, "Container"], [146, 45, 78, 43], [146, 46, 78, 44], [147, 4, 79, 2, "constructor"], [147, 15, 79, 13, "constructor"], [147, 16, 79, 14, "Skia"], [147, 20, 79, 18], [147, 22, 79, 20, "nativeId"], [147, 30, 79, 28], [147, 32, 79, 30], [148, 6, 80, 4], [148, 11, 80, 9], [148, 12, 80, 10, "Skia"], [148, 16, 80, 14], [148, 18, 80, 16, "nativeId"], [148, 26, 80, 24], [148, 27, 80, 25], [149, 6, 81, 4, "_defineProperty"], [149, 21, 81, 19], [149, 22, 81, 20], [149, 26, 81, 24], [149, 28, 81, 26], [149, 38, 81, 36], [149, 40, 81, 38], [149, 44, 81, 42], [149, 45, 81, 43], [150, 4, 82, 2], [151, 4, 83, 2, "redraw"], [151, 10, 83, 8, "redraw"], [151, 11, 83, 8], [151, 13, 83, 11], [152, 6, 84, 4], [152, 10, 84, 8], [152, 14, 84, 12], [152, 15, 84, 13, "mapperId"], [152, 23, 84, 21], [152, 28, 84, 26], [152, 32, 84, 30], [152, 34, 84, 32], [153, 8, 85, 6, "<PERSON><PERSON>"], [153, 32, 85, 9], [153, 33, 85, 10, "stopMapper"], [153, 43, 85, 20], [153, 44, 85, 21], [153, 48, 85, 25], [153, 49, 85, 26, "mapperId"], [153, 57, 85, 34], [153, 58, 85, 35], [154, 6, 86, 4], [155, 6, 87, 4], [155, 10, 87, 8], [155, 14, 87, 12], [155, 15, 87, 13, "unmounted"], [155, 24, 87, 22], [155, 26, 87, 24], [156, 8, 88, 6], [157, 6, 89, 4], [158, 6, 90, 4], [158, 12, 90, 10, "recorder"], [158, 20, 90, 18], [158, 23, 90, 21], [158, 27, 90, 25, "Recorder"], [158, 45, 90, 33], [158, 46, 90, 34], [158, 47, 90, 35], [159, 6, 91, 4], [159, 10, 91, 4, "visit"], [159, 24, 91, 9], [159, 26, 91, 10, "recorder"], [159, 34, 91, 18], [159, 36, 91, 20], [159, 40, 91, 24], [159, 41, 91, 25, "root"], [159, 45, 91, 29], [159, 46, 91, 30], [160, 6, 92, 4], [160, 12, 92, 10, "record"], [160, 18, 92, 16], [160, 21, 92, 19, "recorder"], [160, 29, 92, 27], [160, 30, 92, 28, "getRecording"], [160, 42, 92, 40], [160, 43, 92, 41], [160, 44, 92, 42], [161, 6, 93, 4], [161, 12, 93, 10], [162, 8, 94, 6, "animationValues"], [163, 6, 95, 4], [163, 7, 95, 5], [163, 10, 95, 8, "record"], [163, 16, 95, 14], [164, 6, 96, 4], [164, 10, 96, 8], [164, 11, 96, 9, "recording"], [164, 20, 96, 18], [164, 23, 96, 21], [165, 8, 97, 6, "commands"], [165, 16, 97, 14], [165, 18, 97, 16, "record"], [165, 24, 97, 22], [165, 25, 97, 23, "commands"], [165, 33, 97, 31], [166, 8, 98, 6, "paintPool"], [166, 17, 98, 15], [166, 19, 98, 17, "record"], [166, 25, 98, 23], [166, 26, 98, 24, "paintPool"], [167, 6, 99, 4], [167, 7, 99, 5], [168, 6, 100, 4], [168, 12, 100, 10], [169, 8, 101, 6, "nativeId"], [169, 16, 101, 14], [170, 8, 102, 6, "Skia"], [170, 12, 102, 10], [171, 8, 103, 6, "recording"], [172, 6, 104, 4], [172, 7, 104, 5], [172, 10, 104, 8], [172, 14, 104, 12], [173, 6, 105, 4], [173, 10, 105, 8, "animationValues"], [173, 25, 105, 23], [173, 26, 105, 24, "size"], [173, 30, 105, 28], [173, 33, 105, 31], [173, 34, 105, 32], [173, 36, 105, 34], [174, 8, 106, 6], [174, 12, 106, 10], [174, 13, 106, 11, "mapperId"], [174, 21, 106, 19], [174, 24, 106, 22, "<PERSON><PERSON>"], [174, 48, 106, 25], [174, 49, 106, 26, "startMapper"], [174, 60, 106, 37], [174, 61, 106, 38], [175, 10, 106, 38], [175, 16, 106, 38, "_e"], [175, 18, 106, 38], [175, 26, 106, 38, "global"], [175, 32, 106, 38], [175, 33, 106, 38, "Error"], [175, 38, 106, 38], [176, 10, 106, 38], [176, 16, 106, 38, "ContainerJs3"], [176, 28, 106, 38], [176, 40, 106, 38, "ContainerJs3"], [176, 41, 106, 38], [176, 43, 106, 44], [177, 12, 109, 8, "drawOnscreen"], [177, 24, 109, 20], [177, 25, 109, 21, "Skia"], [177, 29, 109, 25], [177, 31, 109, 27, "nativeId"], [177, 39, 109, 35], [177, 41, 109, 37, "recording"], [177, 50, 109, 46], [177, 51, 109, 47], [178, 10, 110, 6], [178, 11, 110, 7], [179, 10, 110, 7, "ContainerJs3"], [179, 22, 110, 7], [179, 23, 110, 7, "__closure"], [179, 32, 110, 7], [180, 12, 110, 7, "drawOnscreen"], [180, 24, 110, 7], [181, 12, 110, 7, "Skia"], [181, 16, 110, 7], [182, 12, 110, 7, "nativeId"], [182, 20, 110, 7], [183, 12, 110, 7, "recording"], [184, 10, 110, 7], [185, 10, 110, 7, "ContainerJs3"], [185, 22, 110, 7], [185, 23, 110, 7, "__workletHash"], [185, 36, 110, 7], [186, 10, 110, 7, "ContainerJs3"], [186, 22, 110, 7], [186, 23, 110, 7, "__initData"], [186, 33, 110, 7], [186, 36, 110, 7, "_worklet_6119364411013_init_data"], [186, 68, 110, 7], [187, 10, 110, 7, "ContainerJs3"], [187, 22, 110, 7], [187, 23, 110, 7, "__stackDetails"], [187, 37, 110, 7], [187, 40, 110, 7, "_e"], [187, 42, 110, 7], [188, 10, 110, 7], [188, 17, 110, 7, "ContainerJs3"], [188, 29, 110, 7], [189, 8, 110, 7], [189, 9, 106, 38], [189, 13, 110, 9, "Array"], [189, 18, 110, 14], [189, 19, 110, 15, "from"], [189, 23, 110, 19], [189, 24, 110, 20, "animationValues"], [189, 39, 110, 35], [189, 40, 110, 36], [189, 41, 110, 37], [190, 6, 111, 4], [191, 6, 112, 4, "<PERSON><PERSON>"], [191, 30, 112, 7], [191, 31, 112, 8, "runOnUI"], [191, 38, 112, 15], [191, 39, 112, 16], [192, 8, 112, 16], [192, 14, 112, 16, "_e"], [192, 16, 112, 16], [192, 24, 112, 16, "global"], [192, 30, 112, 16], [192, 31, 112, 16, "Error"], [192, 36, 112, 16], [193, 8, 112, 16], [193, 14, 112, 16, "ContainerJs4"], [193, 26, 112, 16], [193, 38, 112, 16, "ContainerJs4"], [193, 39, 112, 16], [193, 41, 112, 22], [194, 10, 115, 6, "drawOnscreen"], [194, 22, 115, 18], [194, 23, 115, 19, "Skia"], [194, 27, 115, 23], [194, 29, 115, 25, "nativeId"], [194, 37, 115, 33], [194, 39, 115, 35, "recording"], [194, 48, 115, 44], [194, 49, 115, 45], [195, 8, 116, 4], [195, 9, 116, 5], [196, 8, 116, 5, "ContainerJs4"], [196, 20, 116, 5], [196, 21, 116, 5, "__closure"], [196, 30, 116, 5], [197, 10, 116, 5, "drawOnscreen"], [197, 22, 116, 5], [198, 10, 116, 5, "Skia"], [198, 14, 116, 5], [199, 10, 116, 5, "nativeId"], [199, 18, 116, 5], [200, 10, 116, 5, "recording"], [201, 8, 116, 5], [202, 8, 116, 5, "ContainerJs4"], [202, 20, 116, 5], [202, 21, 116, 5, "__workletHash"], [202, 34, 116, 5], [203, 8, 116, 5, "ContainerJs4"], [203, 20, 116, 5], [203, 21, 116, 5, "__initData"], [203, 31, 116, 5], [203, 34, 116, 5, "_worklet_16394818889410_init_data"], [203, 67, 116, 5], [204, 8, 116, 5, "ContainerJs4"], [204, 20, 116, 5], [204, 21, 116, 5, "__stackDetails"], [204, 35, 116, 5], [204, 38, 116, 5, "_e"], [204, 40, 116, 5], [205, 8, 116, 5], [205, 15, 116, 5, "ContainerJs4"], [205, 27, 116, 5], [206, 6, 116, 5], [206, 7, 112, 16], [206, 9, 116, 5], [206, 10, 116, 6], [206, 11, 116, 7], [206, 12, 116, 8], [207, 4, 117, 2], [208, 2, 118, 0], [209, 2, 118, 1], [209, 8, 118, 1, "_worklet_4890725954467_init_data"], [209, 40, 118, 1], [210, 4, 118, 1, "code"], [210, 8, 118, 1], [211, 4, 118, 1, "location"], [211, 12, 118, 1], [212, 4, 118, 1, "sourceMap"], [212, 13, 118, 1], [213, 4, 118, 1, "version"], [213, 11, 118, 1], [214, 2, 118, 1], [215, 2, 118, 1], [215, 8, 118, 1, "_worklet_7727529195901_init_data"], [215, 40, 118, 1], [216, 4, 118, 1, "code"], [216, 8, 118, 1], [217, 4, 118, 1, "location"], [217, 12, 118, 1], [218, 4, 118, 1, "sourceMap"], [218, 13, 118, 1], [219, 4, 118, 1, "version"], [219, 11, 118, 1], [220, 2, 118, 1], [221, 2, 119, 0], [221, 8, 119, 6, "<PERSON><PERSON><PERSON><PERSON>"], [221, 33, 119, 31], [221, 42, 119, 40, "Container"], [221, 51, 119, 49], [221, 52, 119, 50], [222, 4, 120, 2, "constructor"], [222, 15, 120, 13, "constructor"], [222, 16, 120, 14, "Skia"], [222, 20, 120, 18], [222, 22, 120, 20, "nativeId"], [222, 30, 120, 28], [222, 32, 120, 30], [223, 6, 121, 4], [223, 11, 121, 9], [223, 12, 121, 10, "Skia"], [223, 16, 121, 14], [223, 18, 121, 16, "nativeId"], [223, 26, 121, 24], [223, 27, 121, 25], [224, 6, 122, 4, "_defineProperty"], [224, 21, 122, 19], [224, 22, 122, 20], [224, 26, 122, 24], [224, 28, 122, 26], [224, 38, 122, 36], [224, 40, 122, 38], [224, 44, 122, 42], [224, 45, 122, 43], [225, 4, 123, 2], [226, 4, 124, 2, "redraw"], [226, 10, 124, 8, "redraw"], [226, 11, 124, 8], [226, 13, 124, 11], [227, 6, 125, 4], [227, 10, 125, 8], [227, 14, 125, 12], [227, 15, 125, 13, "mapperId"], [227, 23, 125, 21], [227, 28, 125, 26], [227, 32, 125, 30], [227, 34, 125, 32], [228, 8, 126, 6, "<PERSON><PERSON>"], [228, 32, 126, 9], [228, 33, 126, 10, "stopMapper"], [228, 43, 126, 20], [228, 44, 126, 21], [228, 48, 126, 25], [228, 49, 126, 26, "mapperId"], [228, 57, 126, 34], [228, 58, 126, 35], [229, 6, 127, 4], [230, 6, 128, 4], [230, 10, 128, 8], [230, 14, 128, 12], [230, 15, 128, 13, "unmounted"], [230, 24, 128, 22], [230, 26, 128, 24], [231, 8, 129, 6], [232, 6, 130, 4], [233, 6, 131, 4], [233, 12, 131, 10], [234, 8, 132, 6, "nativeId"], [234, 16, 132, 14], [235, 8, 133, 6, "Skia"], [236, 6, 134, 4], [236, 7, 134, 5], [236, 10, 134, 8], [236, 14, 134, 12], [237, 6, 135, 4], [237, 12, 135, 10, "recorder"], [237, 20, 135, 18], [237, 23, 135, 21], [237, 27, 135, 25, "ReanimatedRecorder"], [237, 65, 135, 43], [237, 66, 135, 44, "Skia"], [237, 70, 135, 48], [237, 71, 135, 49], [238, 6, 136, 4], [238, 10, 136, 4, "visit"], [238, 24, 136, 9], [238, 26, 136, 10, "recorder"], [238, 34, 136, 18], [238, 36, 136, 20], [238, 40, 136, 24], [238, 41, 136, 25, "root"], [238, 45, 136, 29], [238, 46, 136, 30], [239, 6, 137, 4], [239, 12, 137, 10, "sharedValues"], [239, 24, 137, 22], [239, 27, 137, 25, "recorder"], [239, 35, 137, 33], [239, 36, 137, 34, "getSharedValues"], [239, 51, 137, 49], [239, 52, 137, 50], [239, 53, 137, 51], [240, 6, 138, 4], [240, 12, 138, 10, "sharedRecorder"], [240, 26, 138, 24], [240, 29, 138, 27, "recorder"], [240, 37, 138, 35], [240, 38, 138, 36, "getRecorder"], [240, 49, 138, 47], [240, 50, 138, 48], [240, 51, 138, 49], [241, 6, 139, 4, "<PERSON><PERSON>"], [241, 30, 139, 7], [241, 31, 139, 8, "runOnUI"], [241, 38, 139, 15], [241, 39, 139, 16], [242, 8, 139, 16], [242, 14, 139, 16, "_e"], [242, 16, 139, 16], [242, 24, 139, 16, "global"], [242, 30, 139, 16], [242, 31, 139, 16, "Error"], [242, 36, 139, 16], [243, 8, 139, 16], [243, 14, 139, 16, "ContainerJs5"], [243, 26, 139, 16], [243, 38, 139, 16, "ContainerJs5"], [243, 39, 139, 16], [243, 41, 139, 22], [244, 10, 142, 6, "nativeDrawOnscreen"], [244, 28, 142, 24], [244, 29, 142, 25, "nativeId"], [244, 37, 142, 33], [244, 39, 142, 35, "sharedRecorder"], [244, 53, 142, 49], [244, 54, 142, 50], [245, 8, 143, 4], [245, 9, 143, 5], [246, 8, 143, 5, "ContainerJs5"], [246, 20, 143, 5], [246, 21, 143, 5, "__closure"], [246, 30, 143, 5], [247, 10, 143, 5, "nativeDrawOnscreen"], [247, 28, 143, 5], [248, 10, 143, 5, "nativeId"], [248, 18, 143, 5], [249, 10, 143, 5, "sharedRecorder"], [250, 8, 143, 5], [251, 8, 143, 5, "ContainerJs5"], [251, 20, 143, 5], [251, 21, 143, 5, "__workletHash"], [251, 34, 143, 5], [252, 8, 143, 5, "ContainerJs5"], [252, 20, 143, 5], [252, 21, 143, 5, "__initData"], [252, 31, 143, 5], [252, 34, 143, 5, "_worklet_4890725954467_init_data"], [252, 66, 143, 5], [253, 8, 143, 5, "ContainerJs5"], [253, 20, 143, 5], [253, 21, 143, 5, "__stackDetails"], [253, 35, 143, 5], [253, 38, 143, 5, "_e"], [253, 40, 143, 5], [254, 8, 143, 5], [254, 15, 143, 5, "ContainerJs5"], [254, 27, 143, 5], [255, 6, 143, 5], [255, 7, 139, 16], [255, 9, 143, 5], [255, 10, 143, 6], [255, 11, 143, 7], [255, 12, 143, 8], [256, 6, 144, 4], [256, 10, 144, 8, "sharedValues"], [256, 22, 144, 20], [256, 23, 144, 21, "length"], [256, 29, 144, 27], [256, 32, 144, 30], [256, 33, 144, 31], [256, 35, 144, 33], [257, 8, 145, 6], [257, 12, 145, 10], [257, 13, 145, 11, "mapperId"], [257, 21, 145, 19], [257, 24, 145, 22, "<PERSON><PERSON>"], [257, 48, 145, 25], [257, 49, 145, 26, "startMapper"], [257, 60, 145, 37], [257, 61, 145, 38], [258, 10, 145, 38], [258, 16, 145, 38, "_e"], [258, 18, 145, 38], [258, 26, 145, 38, "global"], [258, 32, 145, 38], [258, 33, 145, 38, "Error"], [258, 38, 145, 38], [259, 10, 145, 38], [259, 16, 145, 38, "ContainerJs6"], [259, 28, 145, 38], [259, 40, 145, 38, "ContainerJs6"], [259, 41, 145, 38], [259, 43, 145, 44], [260, 12, 148, 8, "sharedRecorder"], [260, 26, 148, 22], [260, 27, 148, 23, "applyUpdates"], [260, 39, 148, 35], [260, 40, 148, 36, "sharedValues"], [260, 52, 148, 48], [260, 53, 148, 49], [261, 12, 149, 8, "nativeDrawOnscreen"], [261, 30, 149, 26], [261, 31, 149, 27, "nativeId"], [261, 39, 149, 35], [261, 41, 149, 37, "sharedRecorder"], [261, 55, 149, 51], [261, 56, 149, 52], [262, 10, 150, 6], [262, 11, 150, 7], [263, 10, 150, 7, "ContainerJs6"], [263, 22, 150, 7], [263, 23, 150, 7, "__closure"], [263, 32, 150, 7], [264, 12, 150, 7, "sharedRecorder"], [264, 26, 150, 7], [265, 12, 150, 7, "sharedValues"], [265, 24, 150, 7], [266, 12, 150, 7, "nativeDrawOnscreen"], [266, 30, 150, 7], [267, 12, 150, 7, "nativeId"], [268, 10, 150, 7], [269, 10, 150, 7, "ContainerJs6"], [269, 22, 150, 7], [269, 23, 150, 7, "__workletHash"], [269, 36, 150, 7], [270, 10, 150, 7, "ContainerJs6"], [270, 22, 150, 7], [270, 23, 150, 7, "__initData"], [270, 33, 150, 7], [270, 36, 150, 7, "_worklet_7727529195901_init_data"], [270, 68, 150, 7], [271, 10, 150, 7, "ContainerJs6"], [271, 22, 150, 7], [271, 23, 150, 7, "__stackDetails"], [271, 37, 150, 7], [271, 40, 150, 7, "_e"], [271, 42, 150, 7], [272, 10, 150, 7], [272, 17, 150, 7, "ContainerJs6"], [272, 29, 150, 7], [273, 8, 150, 7], [273, 9, 145, 38], [273, 13, 150, 9, "sharedValues"], [273, 25, 150, 21], [273, 26, 150, 22], [274, 6, 151, 4], [275, 4, 152, 2], [276, 2, 153, 0], [277, 2, 154, 7], [277, 8, 154, 13, "createContainer"], [277, 23, 154, 28], [277, 26, 154, 31, "createContainer"], [277, 27, 154, 32, "Skia"], [277, 31, 154, 36], [277, 33, 154, 38, "nativeId"], [277, 41, 154, 46], [277, 46, 154, 51], [278, 4, 155, 2], [278, 10, 155, 8, "web"], [278, 13, 155, 11], [278, 16, 155, 14, "global"], [278, 22, 155, 20], [278, 23, 155, 21, "SkiaViewApi"], [278, 34, 155, 32], [278, 38, 155, 36, "global"], [278, 44, 155, 42], [278, 45, 155, 43, "SkiaViewApi"], [278, 56, 155, 54], [278, 57, 155, 55, "web"], [278, 60, 155, 58], [279, 4, 156, 2], [279, 8, 156, 6, "HAS_REANIMATED_3"], [279, 39, 156, 22], [279, 43, 156, 26, "nativeId"], [279, 51, 156, 34], [279, 56, 156, 39], [279, 57, 156, 40], [279, 58, 156, 41], [279, 60, 156, 43], [280, 6, 157, 4], [280, 10, 157, 8], [280, 11, 157, 9, "web"], [280, 14, 157, 12], [280, 16, 157, 14], [281, 8, 158, 6], [281, 15, 158, 13], [281, 19, 158, 17, "<PERSON><PERSON><PERSON><PERSON>"], [281, 44, 158, 42], [281, 45, 158, 43, "Skia"], [281, 49, 158, 47], [281, 51, 158, 49, "nativeId"], [281, 59, 158, 57], [281, 60, 158, 58], [282, 6, 159, 4], [282, 7, 159, 5], [282, 13, 159, 11], [283, 8, 160, 6], [283, 15, 160, 13], [283, 19, 160, 17, "Reanimated<PERSON><PERSON><PERSON>"], [283, 38, 160, 36], [283, 39, 160, 37, "Skia"], [283, 43, 160, 41], [283, 45, 160, 43, "nativeId"], [283, 53, 160, 51], [283, 54, 160, 52], [284, 6, 161, 4], [285, 4, 162, 2], [285, 5, 162, 3], [285, 11, 162, 9], [286, 6, 163, 4], [286, 13, 163, 11], [286, 17, 163, 15, "StaticContainer"], [286, 32, 163, 30], [286, 33, 163, 31, "Skia"], [286, 37, 163, 35], [286, 39, 163, 37, "nativeId"], [286, 47, 163, 45], [286, 48, 163, 46], [287, 4, 164, 2], [288, 2, 165, 0], [288, 3, 165, 1], [289, 2, 165, 2, "exports"], [289, 9, 165, 2], [289, 10, 165, 2, "createContainer"], [289, 25, 165, 2], [289, 28, 165, 2, "createContainer"], [289, 43, 165, 2], [290, 0, 165, 2], [290, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "drawOnscreen", "nativeDrawOnscreen", "Container", "constructor", "get__root", "set__root", "unmount", "drawOnCanvas", "StaticContainer", "StaticContainer#constructor", "StaticContainer#redraw", "Reanimated<PERSON><PERSON><PERSON>", "ReanimatedContainer#constructor", "ReanimatedContainer#redraw", "Rea.startMapper$argument_0", "Rea.runOnUI$argument_0", "<PERSON><PERSON><PERSON><PERSON>", "NativeReanimatedContainer#constructor", "NativeReanimatedContainer#redraw", "createContainer"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;qBGS;CHa;2BIC;CJQ;OKC;ECC;GDM;EEC;GFE;EGC;GHE;EIC;GJE;EKC;GLM;CLC;AWC;ECC;GDE;EEC;GFY;CXC;AcC;ECC;GDG;EEC;sCCuB;ODI;gBEE;KFI;GFC;CdC;AmBC;ECC;GDG;EEC;gBHe;KGI;sCJE;OIK;GFE;CnBC;+BsBC;CtBW"}}, "type": "js/module"}]}