import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal } from 'react-native';
import EchoCameraWeb from './camera/EchoCameraWeb';

/**
 * Simple test component to verify BlazeFace integration
 * This can be easily added to any screen for testing
 */
export default function BlazeFaceTest() {
  const [showCamera, setShowCamera] = useState(false);
  const [result, setResult] = useState<any>(null);

  const handleCameraComplete = (cameraResult: any) => {
    console.log('BlazeFace test result:', cameraResult);
    setResult(cameraResult);
    setShowCamera(false);
  };

  const handleCameraCancel = () => {
    setShowCamera(false);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🎯 BlazeFace Integration Test</Text>
      <Text style={styles.description}>
        Test the BlazeFace face detection and blurring functionality
      </Text>
      
      <TouchableOpacity
        style={styles.testButton}
        onPress={() => setShowCamera(true)}
      >
        <Text style={styles.testButtonText}>Start BlazeFace Test</Text>
      </TouchableOpacity>

      {result && (
        <View style={styles.resultContainer}>
          <Text style={styles.resultTitle}>✅ Test Result:</Text>
          <Text style={styles.resultText}>
            Image URL: {result.imageUrl ? '✅ Generated' : '❌ Missing'}
          </Text>
          <Text style={styles.resultText}>
            Challenge Code: {result.challengeCode || 'N/A'}
          </Text>
          <Text style={styles.resultText}>
            Timestamp: {new Date(result.timestamp).toLocaleTimeString()}
          </Text>
        </View>
      )}

      <Modal
        visible={showCamera}
        animationType="slide"
        presentationStyle="fullScreen"
      >
        <EchoCameraWeb
          userId="test-user"
          requestId="test-request"
          onComplete={handleCameraComplete}
          onCancel={handleCameraCancel}
        />
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    margin: 20,
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 20,
    lineHeight: 20,
  },
  testButton: {
    backgroundColor: '#3B82F6',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  testButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  resultContainer: {
    backgroundColor: '#F0FDF4',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#10B981',
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#15803D',
    marginBottom: 8,
  },
  resultText: {
    fontSize: 14,
    color: '#15803D',
    marginBottom: 4,
  },
});
