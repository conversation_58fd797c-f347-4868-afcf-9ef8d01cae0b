{"dependencies": [{"name": "../Skia", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "5eRJ3Y/mp/EEiynYa3WwzXcSMXc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useSVG = void 0;\n  var _Skia = require(_dependencyMap[0], \"../Skia\");\n  const useSVG = (source, onError) => {\n    if (source === null || source === undefined) {\n      throw new Error(`Invalid svg data source. Got: ${source}`);\n    }\n    if (typeof source !== \"object\" || source instanceof Uint8Array || typeof source.default !== \"string\") {\n      throw new Error(`Invalid svg data source. Make sure that the source resolves to a string. Got: ${JSON.stringify(source, null, 2)}`);\n    }\n    const svg = _Skia.Skia.SVG.MakeFromString(source.default);\n    if (svg === null && onError !== undefined) {\n      onError(new Error(\"Failed to create SVG from source.\"));\n    }\n    return svg;\n  };\n  exports.useSVG = useSVG;\n});", "lineCount": 21, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Skia"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 7], [7, 8, 2, 13, "useSVG"], [7, 14, 2, 19], [7, 17, 2, 22, "useSVG"], [7, 18, 2, 23, "source"], [7, 24, 2, 29], [7, 26, 2, 31, "onError"], [7, 33, 2, 38], [7, 38, 2, 43], [8, 4, 3, 2], [8, 8, 3, 6, "source"], [8, 14, 3, 12], [8, 19, 3, 17], [8, 23, 3, 21], [8, 27, 3, 25, "source"], [8, 33, 3, 31], [8, 38, 3, 36, "undefined"], [8, 47, 3, 45], [8, 49, 3, 47], [9, 6, 4, 4], [9, 12, 4, 10], [9, 16, 4, 14, "Error"], [9, 21, 4, 19], [9, 22, 4, 20], [9, 55, 4, 53, "source"], [9, 61, 4, 59], [9, 63, 4, 61], [9, 64, 4, 62], [10, 4, 5, 2], [11, 4, 6, 2], [11, 8, 6, 6], [11, 15, 6, 13, "source"], [11, 21, 6, 19], [11, 26, 6, 24], [11, 34, 6, 32], [11, 38, 6, 36, "source"], [11, 44, 6, 42], [11, 56, 6, 54, "Uint8Array"], [11, 66, 6, 64], [11, 70, 6, 68], [11, 77, 6, 75, "source"], [11, 83, 6, 81], [11, 84, 6, 82, "default"], [11, 91, 6, 89], [11, 96, 6, 94], [11, 104, 6, 102], [11, 106, 6, 104], [12, 6, 7, 4], [12, 12, 7, 10], [12, 16, 7, 14, "Error"], [12, 21, 7, 19], [12, 22, 7, 20], [12, 103, 7, 101, "JSON"], [12, 107, 7, 105], [12, 108, 7, 106, "stringify"], [12, 117, 7, 115], [12, 118, 7, 116, "source"], [12, 124, 7, 122], [12, 126, 7, 124], [12, 130, 7, 128], [12, 132, 7, 130], [12, 133, 7, 131], [12, 134, 7, 132], [12, 136, 7, 134], [12, 137, 7, 135], [13, 4, 8, 2], [14, 4, 9, 2], [14, 10, 9, 8, "svg"], [14, 13, 9, 11], [14, 16, 9, 14, "Skia"], [14, 26, 9, 18], [14, 27, 9, 19, "SVG"], [14, 30, 9, 22], [14, 31, 9, 23, "MakeFromString"], [14, 45, 9, 37], [14, 46, 9, 38, "source"], [14, 52, 9, 44], [14, 53, 9, 45, "default"], [14, 60, 9, 52], [14, 61, 9, 53], [15, 4, 10, 2], [15, 8, 10, 6, "svg"], [15, 11, 10, 9], [15, 16, 10, 14], [15, 20, 10, 18], [15, 24, 10, 22, "onError"], [15, 31, 10, 29], [15, 36, 10, 34, "undefined"], [15, 45, 10, 43], [15, 47, 10, 45], [16, 6, 11, 4, "onError"], [16, 13, 11, 11], [16, 14, 11, 12], [16, 18, 11, 16, "Error"], [16, 23, 11, 21], [16, 24, 11, 22], [16, 59, 11, 57], [16, 60, 11, 58], [16, 61, 11, 59], [17, 4, 12, 2], [18, 4, 13, 2], [18, 11, 13, 9, "svg"], [18, 14, 13, 12], [19, 2, 14, 0], [19, 3, 14, 1], [20, 2, 14, 2, "exports"], [20, 9, 14, 2], [20, 10, 14, 2, "useSVG"], [20, 16, 14, 2], [20, 19, 14, 2, "useSVG"], [20, 25, 14, 2], [21, 0, 14, 2], [21, 3]], "functionMap": {"names": ["<global>", "useSVG"], "mappings": "AAA;sBCC;CDY"}}, "type": "js/module"}]}