<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Detection API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        video {
            width: 100%;
            max-width: 400px;
            height: auto;
            border: 1px solid #ccc;
        }
        canvas {
            border: 1px solid #ccc;
            margin-top: 10px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>Face Detection API Test</h1>
    <p>This page tests the face detection capabilities available in your browser.</p>

    <div class="test-section">
        <h2>1. Browser API Support Check</h2>
        <div id="api-support"></div>
    </div>

    <div class="test-section">
        <h2>2. Camera Access Test</h2>
        <button onclick="startCamera()">Start Camera</button>
        <button onclick="stopCamera()">Stop Camera</button>
        <div id="camera-status"></div>
        <video id="video" autoplay muted playsinline style="display: none;"></video>
    </div>

    <div class="test-section">
        <h2>3. Face Detection Test</h2>
        <button onclick="testFaceDetection()" id="detect-btn" disabled>Test Face Detection</button>
        <div id="detection-status"></div>
        <canvas id="canvas" width="400" height="300" style="display: none;"></canvas>
    </div>

    <div class="test-section">
        <h2>4. MediaPipe Fallback Test</h2>
        <button onclick="testMediaPipe()" id="mediapipe-btn">Test MediaPipe</button>
        <div id="mediapipe-status"></div>
    </div>

    <script>
        let video, canvas, ctx;
        let stream = null;
        let faceDetector = null;
        let mediapipeDetector = null;

        // Initialize elements
        document.addEventListener('DOMContentLoaded', function() {
            video = document.getElementById('video');
            canvas = document.getElementById('canvas');
            ctx = canvas.getContext('2d');
            
            checkAPISupport();
        });

        function logStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            element.appendChild(div);
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        function checkAPISupport() {
            const apiSupport = document.getElementById('api-support');
            
            // Check FaceDetector API
            if ('FaceDetector' in window) {
                logStatus('api-support', '✅ Native FaceDetector API is supported', 'success');
                try {
                    faceDetector = new FaceDetector({ fastMode: true, maxDetectedFaces: 10 });
                    logStatus('api-support', '✅ FaceDetector instance created successfully', 'success');
                } catch (error) {
                    logStatus('api-support', `❌ Error creating FaceDetector: ${error.message}`, 'error');
                }
            } else {
                logStatus('api-support', '❌ Native FaceDetector API not supported', 'warning');
            }

            // Check MediaDevices API
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                logStatus('api-support', '✅ Camera access API supported', 'success');
            } else {
                logStatus('api-support', '❌ Camera access API not supported', 'error');
            }

            // Check Canvas API
            if (canvas && ctx) {
                logStatus('api-support', '✅ Canvas API supported', 'success');
            } else {
                logStatus('api-support', '❌ Canvas API not supported', 'error');
            }
        }

        async function startCamera() {
            try {
                logStatus('camera-status', 'Requesting camera access...', 'info');
                
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { 
                        width: { ideal: 640 }, 
                        height: { ideal: 480 },
                        facingMode: 'user'
                    } 
                });
                
                video.srcObject = stream;
                video.style.display = 'block';
                
                video.onloadedmetadata = () => {
                    logStatus('camera-status', '✅ Camera started successfully', 'success');
                    document.getElementById('detect-btn').disabled = false;
                };
                
            } catch (error) {
                logStatus('camera-status', `❌ Camera access failed: ${error.message}`, 'error');
            }
        }

        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                stream = null;
                video.style.display = 'none';
                canvas.style.display = 'none';
                document.getElementById('detect-btn').disabled = true;
                logStatus('camera-status', '🛑 Camera stopped', 'info');
            }
        }

        async function testFaceDetection() {
            if (!faceDetector) {
                logStatus('detection-status', '❌ No FaceDetector available', 'error');
                return;
            }

            if (!video.videoWidth || !video.videoHeight) {
                logStatus('detection-status', '❌ Video not ready', 'error');
                return;
            }

            try {
                logStatus('detection-status', 'Running face detection...', 'info');
                
                // Draw current video frame to canvas
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                canvas.style.display = 'block';
                ctx.drawImage(video, 0, 0);

                // Detect faces
                const faces = await faceDetector.detect(video);
                
                logStatus('detection-status', `✅ Detected ${faces.length} face(s)`, 'success');
                
                // Draw face rectangles
                ctx.strokeStyle = '#00ff00';
                ctx.lineWidth = 3;
                faces.forEach((face, index) => {
                    const { x, y, width, height } = face.boundingBox;
                    ctx.strokeRect(x, y, width, height);
                    
                    // Add face number
                    ctx.fillStyle = '#00ff00';
                    ctx.font = '16px Arial';
                    ctx.fillText(`Face ${index + 1}`, x, y - 5);
                    
                    logStatus('detection-status', `Face ${index + 1}: x=${Math.round(x)}, y=${Math.round(y)}, w=${Math.round(width)}, h=${Math.round(height)}`, 'info');
                });

            } catch (error) {
                logStatus('detection-status', `❌ Face detection failed: ${error.message}`, 'error');
            }
        }

        async function testMediaPipe() {
            try {
                logStatus('mediapipe-status', 'Loading MediaPipe...', 'info');
                
                // Load MediaPipe script
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3/vision_bundle.js';
                
                script.onload = async () => {
                    try {
                        logStatus('mediapipe-status', '✅ MediaPipe script loaded', 'success');
                        
                        const vision = await window.FilesetResolver.forVisionTasks(
                            'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3/wasm'
                        );
                        
                        logStatus('mediapipe-status', '✅ MediaPipe vision initialized', 'success');
                        
                        mediapipeDetector = await window.FaceDetector.createFromOptions(vision, {
                            baseOptions: {
                                modelAssetPath: 'https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@0.10.3/wasm/face_detection_short_range.tflite',
                            },
                            runningMode: 'IMAGE',
                        });
                        
                        logStatus('mediapipe-status', '✅ MediaPipe FaceDetector created successfully', 'success');
                        
                    } catch (error) {
                        logStatus('mediapipe-status', `❌ MediaPipe initialization failed: ${error.message}`, 'error');
                    }
                };
                
                script.onerror = () => {
                    logStatus('mediapipe-status', '❌ Failed to load MediaPipe script', 'error');
                };
                
                document.head.appendChild(script);
                
            } catch (error) {
                logStatus('mediapipe-status', `❌ MediaPipe test failed: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
