import React, { useEffect, useRef, useState } from 'react';

interface BlazeFaceCanvasProps {
  containerId: string;
  width: number;
  height: number;
  onReady?: () => void; // Callback when BlazeFace is loaded and ready
}

/**
 * BlazeFace Canvas Component
 *
 * Uses TensorFlow.js BlazeFace model for accurate real-time face detection and blurring.
 * This implementation matches the working solution from test-blazeface-integration.html
 * with proper coordinate mapping and mirror effect handling.
 */
export default function BlazeFaceCanvas({ containerId, width, height, onReady }: BlazeFaceCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const rafRef = useRef<number | null>(null);
  const modelRef = useRef<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [faceCount, setFaceCount] = useState(0);

  useEffect(() => {
    console.log('[BlazeFaceCanvas] Starting initialization...', { containerId, width, height });

    const container = document.getElementById(containerId);
    if (!container) {
      console.error('[BlazeFaceCanvas] Container not found:', containerId);
      return;
    }

    const video: HTMLVideoElement | null = container.querySelector('video');
    if (!video) {
      console.error('[BlazeFaceCanvas] Video element not found in container');
      return;
    }

    const canvas = canvasRef.current;
    if (!canvas) {
      console.error('[BlazeFaceCanvas] Canvas ref not available');
      return;
    }

    // Set canvas size to match video dimensions when available
    const updateCanvasSize = () => {
      if (video.videoWidth && video.videoHeight) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        console.log('[BlazeFaceCanvas] Canvas resized to match video:', video.videoWidth, 'x', video.videoHeight);
      } else {
        // Fallback to provided dimensions
        canvas.width = width;
        canvas.height = height;
        console.log('[BlazeFaceCanvas] Canvas resized to provided dimensions:', width, 'x', height);
      }
    };

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('[BlazeFaceCanvas] Canvas context not available');
      return;
    }

    let isDetecting = true;

    // Helper function to load scripts
    const loadScript = (src: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = () => resolve();
        script.onerror = () => reject(new Error(`Failed to load ${src}`));
        document.head.appendChild(script);
      });
    };

    // Load TensorFlow.js and BlazeFace model - matching working test implementation
    const loadModel = async () => {
      try {
        console.log('[BlazeFaceCanvas] Loading TensorFlow.js...');

        // Load TensorFlow.js
        if (!(window as any).tf) {
          await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.20.0/dist/tf.min.js');
        }

        console.log('[BlazeFaceCanvas] Loading BlazeFace model...');

        // Load BlazeFace model
        if (!(window as any).blazeface) {
          await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js');
        }

        // Initialize BlazeFace model
        console.log('[BlazeFaceCanvas] Initializing BlazeFace model...');
        modelRef.current = await (window as any).blazeface.load();
        console.log('[BlazeFaceCanvas] ✅ BlazeFace model loaded successfully');

        setIsLoading(false);

        // Update canvas size once video is ready
        updateCanvasSize();

        // Notify parent that BlazeFace is ready
        if (onReady) {
          onReady();
        }

        // Start detection loop
        detectLoop();

      } catch (error) {
        console.error('[BlazeFaceCanvas] ❌ Failed to load model:', error);
        setIsLoading(false);
      }
    };

    const detectLoop = async () => {
      if (!isDetecting || !modelRef.current) return;

      try {
        // Check if video has valid dimensions
        if (!video.videoWidth || !video.videoHeight) {
          rafRef.current = requestAnimationFrame(detectLoop);
          return;
        }

        // Create tensor from video
        const tf = (window as any).tf;
        const tensor = tf.browser.fromPixels(video);

        // Detect faces with same confidence threshold as working test
        const predictions = await modelRef.current.estimateFaces(tensor, false, 0.6);
        tensor.dispose();

        // Clear canvas and draw the original video frame first
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        if (predictions.length > 0) {
          setFaceCount(predictions.length);

          // Process each detected face - REAL-TIME BLURRING like in test
          predictions.forEach((prediction: any, index: number) => {
            const [x1, y1] = prediction.topLeft;
            const [x2, y2] = prediction.bottomRight;

            // Fix coordinate order
            let minX = Math.min(x1, x2);
            let maxX = Math.max(x1, x2);
            const minY = Math.min(y1, y2);
            const maxY = Math.max(y1, y2);

            // Account for horizontal flip (mirror effect)
            const canvasWidth = canvas.width;
            const flippedMinX = canvasWidth - maxX;
            const flippedMaxX = canvasWidth - minX;
            minX = flippedMinX;
            maxX = flippedMaxX;

            // Calculate face dimensions
            const faceWidth = maxX - minX;
            const faceHeight = maxY - minY;

            if (faceWidth <= 0 || faceHeight <= 0) {
              return;
            }

            // Expand the bounding box for better coverage
            const centerX = (minX + maxX) / 2;
            const centerY = (minY + maxY) / 2;
            const expandedWidth = faceWidth * 1.5;
            const expandedHeight = faceHeight * 1.8;

            // Ensure positive radii
            const radiusX = Math.max(expandedWidth / 2, 10);
            const radiusY = Math.max(expandedHeight / 2, 10);

            // Apply REAL-TIME elliptical blur - this creates the live preview blur
            ctx.save();
            ctx.beginPath();
            ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, Math.PI * 2);
            ctx.clip();
            ctx.filter = 'blur(25px)'; // Stronger blur for better privacy
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            ctx.restore();

            // Add debug rectangle to show detection area (optional)
            if (__DEV__) {
              ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';
              ctx.lineWidth = 2;
              ctx.strokeRect(minX, minY, faceWidth, faceHeight);
            }
          });
        } else {
          setFaceCount(0);
        }

      } catch (error) {
        console.error('[BlazeFaceCanvas] Detection error:', error);
      }

      // Continue detection loop
      if (isDetecting) {
        rafRef.current = requestAnimationFrame(detectLoop);
      }
    };

    // Wait for video to be ready before starting
    const waitForVideoAndStart = () => {
      if (video.readyState >= 2) { // HAVE_CURRENT_DATA
        loadModel();
      } else {
        video.addEventListener('loadeddata', loadModel, { once: true });
      }
    };

    // Start the process
    waitForVideoAndStart();

    // Cleanup function
    return () => {
      isDetecting = false;
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };
  }, [containerId, width, height]);

  return (
    <>
      <canvas
        ref={canvasRef}
        style={{
          position: 'absolute',
          left: 0,
          top: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 15, // Higher z-index to be above video
          objectFit: 'cover',
          backgroundColor: 'transparent'
        }}
      />
      {/* Status indicator - matching working test implementation */}
      {isLoading && (
        <div style={{
          position: 'absolute',
          top: 10,
          left: 10,
          background: 'rgba(59, 130, 246, 0.9)',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '8px',
          fontSize: '12px',
          fontWeight: '600',
          zIndex: 20,
          border: '1px solid rgba(59, 130, 246, 0.3)'
        }}>
          Loading BlazeFace model...
        </div>
      )}
      {!isLoading && faceCount > 0 && (
        <div style={{
          position: 'absolute',
          top: 10,
          left: 10,
          background: 'rgba(16, 185, 129, 0.9)',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '8px',
          fontSize: '12px',
          fontWeight: '600',
          zIndex: 20,
          border: '1px solid rgba(16, 185, 129, 0.3)'
        }}>
          🛡️ Protecting {faceCount} face{faceCount > 1 ? 's' : ''}
        </div>
      )}
      {!isLoading && faceCount === 0 && (
        <div style={{
          position: 'absolute',
          top: 10,
          left: 10,
          background: 'rgba(107, 114, 128, 0.9)',
          color: 'white',
          padding: '8px 12px',
          borderRadius: '8px',
          fontSize: '12px',
          fontWeight: '600',
          zIndex: 20,
          border: '1px solid rgba(107, 114, 128, 0.3)'
        }}>
          👀 Looking for faces...
        </div>
      )}
    </>
  );
}
