import React, { useEffect, useRef, useState } from 'react';

interface BlazeFaceCanvasProps {
  containerId: string;
  width: number;
  height: number;
}

/**
 * BlazeFace Canvas Component
 * 
 * Uses TensorFlow.js BlazeFace model for accurate real-time face detection and blurring.
 * This implementation is based on the working solution from echo-camera-fixed.html
 * with proper coordinate mapping and mirror effect handling.
 */
export default function BlazeFaceCanvas({ containerId, width, height }: BlazeFaceCanvasProps) {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const rafRef = useRef<number | null>(null);
  const modelRef = useRef<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [faceCount, setFaceCount] = useState(0);

  useEffect(() => {
    console.log('[BlazeFaceCanvas] Starting initialization...', { containerId, width, height });

    const container = document.getElementById(containerId);
    if (!container) {
      console.error('[BlazeFaceCanvas] Container not found:', containerId);
      return;
    }

    const video: HTMLVideoElement | null = container.querySelector('video');
    if (!video) {
      console.error('[BlazeFaceCanvas] Video element not found in container');
      return;
    }

    const canvas = canvasRef.current;
    if (!canvas) {
      console.error('[BlazeFaceCanvas] Canvas ref not available');
      return;
    }

    // Set canvas size
    canvas.width = width;
    canvas.height = height;
    console.log('[BlazeFaceCanvas] Canvas resized to:', width, 'x', height);

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error('[BlazeFaceCanvas] Canvas context not available');
      return;
    }

    let isDetecting = true;

    // Load TensorFlow.js and BlazeFace model
    const loadModel = async () => {
      try {
        console.log('[BlazeFaceCanvas] Loading TensorFlow.js...');
        
        // Load TensorFlow.js
        if (!(window as any).tf) {
          await new Promise<void>((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.20.0/dist/tf.min.js';
            script.onload = () => resolve();
            script.onerror = () => reject(new Error('Failed to load TensorFlow.js'));
            document.head.appendChild(script);
          });
        }

        console.log('[BlazeFaceCanvas] Loading BlazeFace model...');
        
        // Load BlazeFace model
        if (!(window as any).blazeface) {
          await new Promise<void>((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';
            script.onload = () => resolve();
            script.onerror = () => reject(new Error('Failed to load BlazeFace'));
            document.head.appendChild(script);
          });
        }

        // Initialize BlazeFace model
        const tf = (window as any).tf;
        const blazeface = (window as any).blazeface;
        
        console.log('[BlazeFaceCanvas] Initializing BlazeFace model...');
        modelRef.current = await blazeface.load();
        console.log('[BlazeFaceCanvas] ✅ BlazeFace model loaded successfully');
        
        setIsLoading(false);
        
        // Start detection loop
        detectLoop();
        
      } catch (error) {
        console.error('[BlazeFaceCanvas] ❌ Failed to load model:', error);
        setIsLoading(false);
      }
    };

    const detectLoop = async () => {
      if (!isDetecting || !modelRef.current) return;

      try {
        // Create tensor from video
        const tf = (window as any).tf;
        const tensor = tf.browser.fromPixels(video);
        
        // Detect faces
        const predictions = await modelRef.current.estimateFaces(tensor, false, 0.6);
        tensor.dispose();

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        if (predictions.length > 0) {
          setFaceCount(predictions.length);
          
          // Process each detected face
          predictions.forEach((prediction: any, index: number) => {
            const [x1, y1] = prediction.topLeft;
            const [x2, y2] = prediction.bottomRight;

            // Fix coordinate order - BlazeFace might return them in different order
            let minX = Math.min(x1, x2);
            let maxX = Math.max(x1, x2);
            const minY = Math.min(y1, y2);
            const maxY = Math.max(y1, y2);
            
            // Account for horizontal flip (mirror effect) - flip X coordinates
            const canvasWidth = canvas.width;
            const flippedMinX = canvasWidth - maxX;
            const flippedMaxX = canvasWidth - minX;
            minX = flippedMinX;
            maxX = flippedMaxX;

            // Calculate face dimensions
            const faceWidth = maxX - minX;
            const faceHeight = maxY - minY;

            if (faceWidth <= 0 || faceHeight <= 0) {
              console.warn(`Invalid face dimensions for face ${index + 1}`);
              return;
            }

            // Expand the bounding box for better coverage
            const centerX = (minX + maxX) / 2;
            const centerY = (minY + maxY) / 2;
            const expandedWidth = faceWidth * 1.5;
            const expandedHeight = faceHeight * 1.8;

            // Ensure positive radii
            const radiusX = Math.max(expandedWidth / 2, 10);
            const radiusY = Math.max(expandedHeight / 2, 10);

            // Apply elliptical blur
            ctx.save();
            ctx.beginPath();
            ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, Math.PI * 2);
            ctx.clip();
            ctx.filter = 'blur(20px)';
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            ctx.restore();
          });
        } else {
          setFaceCount(0);
        }

      } catch (error) {
        console.error('[BlazeFaceCanvas] Detection error:', error);
      }

      // Continue detection loop
      if (isDetecting) {
        rafRef.current = requestAnimationFrame(detectLoop);
      }
    };

    // Start loading the model
    loadModel();

    // Cleanup function
    return () => {
      isDetecting = false;
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };
  }, [containerId, width, height]);

  return (
    <>
      <canvas
        ref={canvasRef}
        style={{ 
          position: 'absolute', 
          left: 0, 
          top: 0, 
          width, 
          height, 
          pointerEvents: 'none',
          zIndex: 10
        }}
      />
      {/* Status indicator */}
      {isLoading && (
        <div style={{
          position: 'absolute',
          top: 10,
          left: 10,
          background: 'rgba(0,0,0,0.7)',
          color: 'white',
          padding: '5px 10px',
          borderRadius: '5px',
          fontSize: '12px',
          zIndex: 20
        }}>
          Loading face detection...
        </div>
      )}
      {!isLoading && faceCount > 0 && (
        <div style={{
          position: 'absolute',
          top: 10,
          left: 10,
          background: 'rgba(0,128,0,0.8)',
          color: 'white',
          padding: '5px 10px',
          borderRadius: '5px',
          fontSize: '12px',
          zIndex: 20
        }}>
          🛡️ Protecting {faceCount} face{faceCount > 1 ? 's' : ''}
        </div>
      )}
    </>
  );
}
