{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        await processImageWithFaceBlur(photo.uri);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // Real face detection and blurring using browser APIs and CDN libraries\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        setProcessingProgress(60);\n\n        // Try multiple face detection approaches\n        let detectedFaces = [];\n\n        // Method 1: Try browser's native Face Detection API\n        try {\n          if ('FaceDetector' in window) {\n            console.log('[EchoCameraWeb] Using browser Face Detection API');\n            const faceDetector = new window.FaceDetector({\n              maxDetectedFaces: 10,\n              fastMode: false\n            });\n            const browserDetections = await faceDetector.detect(img);\n            detectedFaces = browserDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.boundingBox.x + detection.boundingBox.width / 2) / img.width,\n                yCenter: (detection.boundingBox.y + detection.boundingBox.height / 2) / img.height,\n                width: detection.boundingBox.width / img.width,\n                height: detection.boundingBox.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] Browser Face Detection API found ${detectedFaces.length} faces`);\n          } else {\n            throw new Error('Browser Face Detection API not available');\n          }\n        } catch (browserError) {\n          console.warn('[EchoCameraWeb] Browser face detection failed, trying face-api.js from CDN:', browserError);\n\n          // Method 2: Try loading face-api.js from CDN\n          try {\n            // Load face-api.js from CDN if not already loaded\n            if (!window.faceapi) {\n              await new Promise((resolve, reject) => {\n                const script = document.createElement('script');\n                script.src = 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js';\n                script.onload = resolve;\n                script.onerror = reject;\n                document.head.appendChild(script);\n              });\n            }\n            const faceapi = window.faceapi;\n\n            // Load models from CDN\n            await Promise.all([faceapi.nets.tinyFaceDetector.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'), faceapi.nets.faceLandmark68Net.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights')]);\n\n            // Detect faces\n            const faceDetections = await faceapi.detectAllFaces(img, new faceapi.TinyFaceDetectorOptions());\n            detectedFaces = faceDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.box.x + detection.box.width / 2) / img.width,\n                yCenter: (detection.box.y + detection.box.height / 2) / img.height,\n                width: detection.box.width / img.width,\n                height: detection.box.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] face-api.js found ${detectedFaces.length} faces`);\n          } catch (faceApiError) {\n            console.warn('[EchoCameraWeb] face-api.js also failed:', faceApiError);\n            detectedFaces = [];\n          }\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // Apply blurring to each detected face\n        if (detectedFaces.length > 0) {\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add some padding around the face\n            const padding = 0.2; // 20% padding\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] Blurring face ${index + 1} at (${paddedX}, ${paddedY}) size ${paddedWidth}x${paddedHeight}`);\n\n            // Get the face region image data\n            const faceImageData = ctx.getImageData(paddedX, paddedY, paddedWidth, paddedHeight);\n            const data = faceImageData.data;\n\n            // Apply pixelation blur effect\n            const pixelSize = Math.max(8, Math.min(paddedWidth, paddedHeight) / 20); // Adaptive pixel size\n            for (let y = 0; y < paddedHeight; y += pixelSize) {\n              for (let x = 0; x < paddedWidth; x += pixelSize) {\n                // Get the color of the top-left pixel in this block\n                const pixelIndex = (y * paddedWidth + x) * 4;\n                const r = data[pixelIndex];\n                const g = data[pixelIndex + 1];\n                const b = data[pixelIndex + 2];\n                const a = data[pixelIndex + 3];\n\n                // Apply this color to the entire block\n                for (let dy = 0; dy < pixelSize && y + dy < paddedHeight; dy++) {\n                  for (let dx = 0; dx < pixelSize && x + dx < paddedWidth; dx++) {\n                    const blockPixelIndex = ((y + dy) * paddedWidth + (x + dx)) * 4;\n                    data[blockPixelIndex] = r;\n                    data[blockPixelIndex + 1] = g;\n                    data[blockPixelIndex + 2] = b;\n                    data[blockPixelIndex + 3] = a;\n                  }\n                }\n              }\n            }\n\n            // Put the blurred face region back on the canvas\n            ctx.putImageData(faceImageData, paddedX, paddedY);\n          });\n        } else {\n          console.log('[EchoCameraWeb] No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] Processing complete:', result);\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 583,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 616,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 620,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 672,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 674,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 665,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 715,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 706,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 701,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1320, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [89, 4, 89, 2], [90, 4, 90, 2], [90, 10, 90, 8, "capturePhoto"], [90, 22, 90, 20], [90, 25, 90, 23], [90, 29, 90, 23, "useCallback"], [90, 47, 90, 34], [90, 49, 90, 35], [90, 61, 90, 47], [91, 6, 91, 4], [92, 6, 92, 4], [92, 12, 92, 10, "isDev"], [92, 17, 92, 15], [92, 20, 92, 18, "process"], [92, 27, 92, 25], [92, 28, 92, 26, "env"], [92, 31, 92, 29], [92, 32, 92, 30, "NODE_ENV"], [92, 40, 92, 38], [92, 45, 92, 43], [92, 58, 92, 56], [92, 62, 92, 60, "__DEV__"], [92, 69, 92, 67], [93, 6, 94, 4], [93, 10, 94, 8], [93, 11, 94, 9, "cameraRef"], [93, 20, 94, 18], [93, 21, 94, 19, "current"], [93, 28, 94, 26], [93, 32, 94, 30], [93, 33, 94, 31, "isDev"], [93, 38, 94, 36], [93, 40, 94, 38], [94, 8, 95, 6, "<PERSON><PERSON>"], [94, 22, 95, 11], [94, 23, 95, 12, "alert"], [94, 28, 95, 17], [94, 29, 95, 18], [94, 36, 95, 25], [94, 38, 95, 27], [94, 56, 95, 45], [94, 57, 95, 46], [95, 8, 96, 6], [96, 6, 97, 4], [97, 6, 98, 4], [97, 10, 98, 8], [98, 8, 99, 6, "setProcessingState"], [98, 26, 99, 24], [98, 27, 99, 25], [98, 38, 99, 36], [98, 39, 99, 37], [99, 8, 100, 6, "setProcessingProgress"], [99, 29, 100, 27], [99, 30, 100, 28], [99, 32, 100, 30], [99, 33, 100, 31], [100, 8, 101, 6], [101, 8, 102, 6], [102, 8, 103, 6], [103, 8, 104, 6], [103, 14, 104, 12], [103, 18, 104, 16, "Promise"], [103, 25, 104, 23], [103, 26, 104, 24, "resolve"], [103, 33, 104, 31], [103, 37, 104, 35, "setTimeout"], [103, 47, 104, 45], [103, 48, 104, 46, "resolve"], [103, 55, 104, 53], [103, 57, 104, 55], [103, 59, 104, 57], [103, 60, 104, 58], [103, 61, 104, 59], [104, 8, 105, 6], [105, 8, 106, 6], [105, 12, 106, 10, "photo"], [105, 17, 106, 15], [106, 8, 108, 6], [106, 12, 108, 10], [107, 10, 109, 8, "photo"], [107, 15, 109, 13], [107, 18, 109, 16], [107, 24, 109, 22, "cameraRef"], [107, 33, 109, 31], [107, 34, 109, 32, "current"], [107, 41, 109, 39], [107, 42, 109, 40, "takePictureAsync"], [107, 58, 109, 56], [107, 59, 109, 57], [108, 12, 110, 10, "quality"], [108, 19, 110, 17], [108, 21, 110, 19], [108, 24, 110, 22], [109, 12, 111, 10, "base64"], [109, 18, 111, 16], [109, 20, 111, 18], [109, 25, 111, 23], [110, 12, 112, 10, "skipProcessing"], [110, 26, 112, 24], [110, 28, 112, 26], [110, 32, 112, 30], [110, 33, 112, 32], [111, 10, 113, 8], [111, 11, 113, 9], [111, 12, 113, 10], [112, 8, 114, 6], [112, 9, 114, 7], [112, 10, 114, 8], [112, 17, 114, 15, "cameraError"], [112, 28, 114, 26], [112, 30, 114, 28], [113, 10, 115, 8, "console"], [113, 17, 115, 15], [113, 18, 115, 16, "log"], [113, 21, 115, 19], [113, 22, 115, 20], [113, 82, 115, 80], [113, 84, 115, 82, "cameraError"], [113, 95, 115, 93], [113, 96, 115, 94], [114, 10, 116, 8], [115, 10, 117, 8], [115, 14, 117, 12, "isDev"], [115, 19, 117, 17], [115, 21, 117, 19], [116, 12, 118, 10, "photo"], [116, 17, 118, 15], [116, 20, 118, 18], [117, 14, 119, 12, "uri"], [117, 17, 119, 15], [117, 19, 119, 17], [118, 12, 120, 10], [118, 13, 120, 11], [119, 10, 121, 8], [119, 11, 121, 9], [119, 17, 121, 15], [120, 12, 122, 10], [120, 18, 122, 16, "cameraError"], [120, 29, 122, 27], [121, 10, 123, 8], [122, 8, 124, 6], [123, 8, 125, 6], [123, 12, 125, 10], [123, 13, 125, 11, "photo"], [123, 18, 125, 16], [123, 20, 125, 18], [124, 10, 126, 8], [124, 16, 126, 14], [124, 20, 126, 18, "Error"], [124, 25, 126, 23], [124, 26, 126, 24], [124, 51, 126, 49], [124, 52, 126, 50], [125, 8, 127, 6], [126, 8, 128, 6, "console"], [126, 15, 128, 13], [126, 16, 128, 14, "log"], [126, 19, 128, 17], [126, 20, 128, 18], [126, 53, 128, 51], [126, 55, 128, 53, "photo"], [126, 60, 128, 58], [126, 61, 128, 59, "uri"], [126, 64, 128, 62], [126, 65, 128, 63], [127, 8, 129, 6, "setCapturedPhoto"], [127, 24, 129, 22], [127, 25, 129, 23, "photo"], [127, 30, 129, 28], [127, 31, 129, 29, "uri"], [127, 34, 129, 32], [127, 35, 129, 33], [128, 8, 130, 6, "setProcessingProgress"], [128, 29, 130, 27], [128, 30, 130, 28], [128, 32, 130, 30], [128, 33, 130, 31], [129, 8, 131, 6], [130, 8, 132, 6], [130, 14, 132, 12, "processImageWithFaceBlur"], [130, 38, 132, 36], [130, 39, 132, 37, "photo"], [130, 44, 132, 42], [130, 45, 132, 43, "uri"], [130, 48, 132, 46], [130, 49, 132, 47], [131, 6, 133, 4], [131, 7, 133, 5], [131, 8, 133, 6], [131, 15, 133, 13, "error"], [131, 20, 133, 18], [131, 22, 133, 20], [132, 8, 134, 6, "console"], [132, 15, 134, 13], [132, 16, 134, 14, "error"], [132, 21, 134, 19], [132, 22, 134, 20], [132, 54, 134, 52], [132, 56, 134, 54, "error"], [132, 61, 134, 59], [132, 62, 134, 60], [133, 8, 135, 6, "setErrorMessage"], [133, 23, 135, 21], [133, 24, 135, 22], [133, 68, 135, 66], [133, 69, 135, 67], [134, 8, 136, 6, "setProcessingState"], [134, 26, 136, 24], [134, 27, 136, 25], [134, 34, 136, 32], [134, 35, 136, 33], [135, 6, 137, 4], [136, 4, 138, 2], [136, 5, 138, 3], [136, 7, 138, 5], [136, 9, 138, 7], [136, 10, 138, 8], [137, 4, 139, 2], [138, 4, 140, 2], [138, 10, 140, 8, "processImageWithFaceBlur"], [138, 34, 140, 32], [138, 37, 140, 35], [138, 43, 140, 42, "photoUri"], [138, 51, 140, 58], [138, 55, 140, 63], [139, 6, 141, 4], [139, 10, 141, 8], [140, 8, 142, 6, "setProcessingState"], [140, 26, 142, 24], [140, 27, 142, 25], [140, 39, 142, 37], [140, 40, 142, 38], [141, 8, 143, 6, "setProcessingProgress"], [141, 29, 143, 27], [141, 30, 143, 28], [141, 32, 143, 30], [141, 33, 143, 31], [143, 8, 145, 6], [144, 8, 146, 6], [144, 14, 146, 12, "canvas"], [144, 20, 146, 18], [144, 23, 146, 21, "document"], [144, 31, 146, 29], [144, 32, 146, 30, "createElement"], [144, 45, 146, 43], [144, 46, 146, 44], [144, 54, 146, 52], [144, 55, 146, 53], [145, 8, 147, 6], [145, 14, 147, 12, "ctx"], [145, 17, 147, 15], [145, 20, 147, 18, "canvas"], [145, 26, 147, 24], [145, 27, 147, 25, "getContext"], [145, 37, 147, 35], [145, 38, 147, 36], [145, 42, 147, 40], [145, 43, 147, 41], [146, 8, 148, 6], [146, 12, 148, 10], [146, 13, 148, 11, "ctx"], [146, 16, 148, 14], [146, 18, 148, 16], [146, 24, 148, 22], [146, 28, 148, 26, "Error"], [146, 33, 148, 31], [146, 34, 148, 32], [146, 64, 148, 62], [146, 65, 148, 63], [148, 8, 150, 6], [149, 8, 151, 6], [149, 14, 151, 12, "img"], [149, 17, 151, 15], [149, 20, 151, 18], [149, 24, 151, 22, "Image"], [149, 29, 151, 27], [149, 30, 151, 28], [149, 31, 151, 29], [150, 8, 152, 6], [150, 14, 152, 12], [150, 18, 152, 16, "Promise"], [150, 25, 152, 23], [150, 26, 152, 24], [150, 27, 152, 25, "resolve"], [150, 34, 152, 32], [150, 36, 152, 34, "reject"], [150, 42, 152, 40], [150, 47, 152, 45], [151, 10, 153, 8, "img"], [151, 13, 153, 11], [151, 14, 153, 12, "onload"], [151, 20, 153, 18], [151, 23, 153, 21, "resolve"], [151, 30, 153, 28], [152, 10, 154, 8, "img"], [152, 13, 154, 11], [152, 14, 154, 12, "onerror"], [152, 21, 154, 19], [152, 24, 154, 22, "reject"], [152, 30, 154, 28], [153, 10, 155, 8, "img"], [153, 13, 155, 11], [153, 14, 155, 12, "src"], [153, 17, 155, 15], [153, 20, 155, 18, "photoUri"], [153, 28, 155, 26], [154, 8, 156, 6], [154, 9, 156, 7], [154, 10, 156, 8], [156, 8, 158, 6], [157, 8, 159, 6, "canvas"], [157, 14, 159, 12], [157, 15, 159, 13, "width"], [157, 20, 159, 18], [157, 23, 159, 21, "img"], [157, 26, 159, 24], [157, 27, 159, 25, "width"], [157, 32, 159, 30], [158, 8, 160, 6, "canvas"], [158, 14, 160, 12], [158, 15, 160, 13, "height"], [158, 21, 160, 19], [158, 24, 160, 22, "img"], [158, 27, 160, 25], [158, 28, 160, 26, "height"], [158, 34, 160, 32], [160, 8, 162, 6], [161, 8, 163, 6, "ctx"], [161, 11, 163, 9], [161, 12, 163, 10, "drawImage"], [161, 21, 163, 19], [161, 22, 163, 20, "img"], [161, 25, 163, 23], [161, 27, 163, 25], [161, 28, 163, 26], [161, 30, 163, 28], [161, 31, 163, 29], [161, 32, 163, 30], [162, 8, 165, 6, "setProcessingProgress"], [162, 29, 165, 27], [162, 30, 165, 28], [162, 32, 165, 30], [162, 33, 165, 31], [164, 8, 167, 6], [165, 8, 168, 6], [165, 12, 168, 10, "detectedFaces"], [165, 25, 168, 23], [165, 28, 168, 26], [165, 30, 168, 28], [167, 8, 170, 6], [168, 8, 171, 6], [168, 12, 171, 10], [169, 10, 172, 8], [169, 14, 172, 12], [169, 28, 172, 26], [169, 32, 172, 30, "window"], [169, 38, 172, 36], [169, 40, 172, 38], [170, 12, 173, 10, "console"], [170, 19, 173, 17], [170, 20, 173, 18, "log"], [170, 23, 173, 21], [170, 24, 173, 22], [170, 74, 173, 72], [170, 75, 173, 73], [171, 12, 174, 10], [171, 18, 174, 16, "faceDetector"], [171, 30, 174, 28], [171, 33, 174, 31], [171, 37, 174, 36, "window"], [171, 43, 174, 42], [171, 44, 174, 51, "FaceDetector"], [171, 56, 174, 63], [171, 57, 174, 64], [172, 14, 175, 12, "maxDetectedFaces"], [172, 30, 175, 28], [172, 32, 175, 30], [172, 34, 175, 32], [173, 14, 176, 12, "fastMode"], [173, 22, 176, 20], [173, 24, 176, 22], [174, 12, 177, 10], [174, 13, 177, 11], [174, 14, 177, 12], [175, 12, 179, 10], [175, 18, 179, 16, "browserDetections"], [175, 35, 179, 33], [175, 38, 179, 36], [175, 44, 179, 42, "faceDetector"], [175, 56, 179, 54], [175, 57, 179, 55, "detect"], [175, 63, 179, 61], [175, 64, 179, 62, "img"], [175, 67, 179, 65], [175, 68, 179, 66], [176, 12, 180, 10, "detectedFaces"], [176, 25, 180, 23], [176, 28, 180, 26, "browserDetections"], [176, 45, 180, 43], [176, 46, 180, 44, "map"], [176, 49, 180, 47], [176, 50, 180, 49, "detection"], [176, 59, 180, 63], [176, 64, 180, 69], [177, 14, 181, 12, "boundingBox"], [177, 25, 181, 23], [177, 27, 181, 25], [178, 16, 182, 14, "xCenter"], [178, 23, 182, 21], [178, 25, 182, 23], [178, 26, 182, 24, "detection"], [178, 35, 182, 33], [178, 36, 182, 34, "boundingBox"], [178, 47, 182, 45], [178, 48, 182, 46, "x"], [178, 49, 182, 47], [178, 52, 182, 50, "detection"], [178, 61, 182, 59], [178, 62, 182, 60, "boundingBox"], [178, 73, 182, 71], [178, 74, 182, 72, "width"], [178, 79, 182, 77], [178, 82, 182, 80], [178, 83, 182, 81], [178, 87, 182, 85, "img"], [178, 90, 182, 88], [178, 91, 182, 89, "width"], [178, 96, 182, 94], [179, 16, 183, 14, "yCenter"], [179, 23, 183, 21], [179, 25, 183, 23], [179, 26, 183, 24, "detection"], [179, 35, 183, 33], [179, 36, 183, 34, "boundingBox"], [179, 47, 183, 45], [179, 48, 183, 46, "y"], [179, 49, 183, 47], [179, 52, 183, 50, "detection"], [179, 61, 183, 59], [179, 62, 183, 60, "boundingBox"], [179, 73, 183, 71], [179, 74, 183, 72, "height"], [179, 80, 183, 78], [179, 83, 183, 81], [179, 84, 183, 82], [179, 88, 183, 86, "img"], [179, 91, 183, 89], [179, 92, 183, 90, "height"], [179, 98, 183, 96], [180, 16, 184, 14, "width"], [180, 21, 184, 19], [180, 23, 184, 21, "detection"], [180, 32, 184, 30], [180, 33, 184, 31, "boundingBox"], [180, 44, 184, 42], [180, 45, 184, 43, "width"], [180, 50, 184, 48], [180, 53, 184, 51, "img"], [180, 56, 184, 54], [180, 57, 184, 55, "width"], [180, 62, 184, 60], [181, 16, 185, 14, "height"], [181, 22, 185, 20], [181, 24, 185, 22, "detection"], [181, 33, 185, 31], [181, 34, 185, 32, "boundingBox"], [181, 45, 185, 43], [181, 46, 185, 44, "height"], [181, 52, 185, 50], [181, 55, 185, 53, "img"], [181, 58, 185, 56], [181, 59, 185, 57, "height"], [182, 14, 186, 12], [183, 12, 187, 10], [183, 13, 187, 11], [183, 14, 187, 12], [183, 15, 187, 13], [184, 12, 188, 10, "console"], [184, 19, 188, 17], [184, 20, 188, 18, "log"], [184, 23, 188, 21], [184, 24, 188, 22], [184, 76, 188, 74, "detectedFaces"], [184, 89, 188, 87], [184, 90, 188, 88, "length"], [184, 96, 188, 94], [184, 104, 188, 102], [184, 105, 188, 103], [185, 10, 189, 8], [185, 11, 189, 9], [185, 17, 189, 15], [186, 12, 190, 10], [186, 18, 190, 16], [186, 22, 190, 20, "Error"], [186, 27, 190, 25], [186, 28, 190, 26], [186, 70, 190, 68], [186, 71, 190, 69], [187, 10, 191, 8], [188, 8, 192, 6], [188, 9, 192, 7], [188, 10, 192, 8], [188, 17, 192, 15, "browserError"], [188, 29, 192, 27], [188, 31, 192, 29], [189, 10, 193, 8, "console"], [189, 17, 193, 15], [189, 18, 193, 16, "warn"], [189, 22, 193, 20], [189, 23, 193, 21], [189, 100, 193, 98], [189, 102, 193, 100, "browserError"], [189, 114, 193, 112], [189, 115, 193, 113], [191, 10, 195, 8], [192, 10, 196, 8], [192, 14, 196, 12], [193, 12, 197, 10], [194, 12, 198, 10], [194, 16, 198, 14], [194, 17, 198, 16, "window"], [194, 23, 198, 22], [194, 24, 198, 31, "<PERSON>ap<PERSON>"], [194, 31, 198, 38], [194, 33, 198, 40], [195, 14, 199, 12], [195, 20, 199, 18], [195, 24, 199, 22, "Promise"], [195, 31, 199, 29], [195, 32, 199, 30], [195, 33, 199, 31, "resolve"], [195, 40, 199, 38], [195, 42, 199, 40, "reject"], [195, 48, 199, 46], [195, 53, 199, 51], [196, 16, 200, 14], [196, 22, 200, 20, "script"], [196, 28, 200, 26], [196, 31, 200, 29, "document"], [196, 39, 200, 37], [196, 40, 200, 38, "createElement"], [196, 53, 200, 51], [196, 54, 200, 52], [196, 62, 200, 60], [196, 63, 200, 61], [197, 16, 201, 14, "script"], [197, 22, 201, 20], [197, 23, 201, 21, "src"], [197, 26, 201, 24], [197, 29, 201, 27], [197, 99, 201, 97], [198, 16, 202, 14, "script"], [198, 22, 202, 20], [198, 23, 202, 21, "onload"], [198, 29, 202, 27], [198, 32, 202, 30, "resolve"], [198, 39, 202, 37], [199, 16, 203, 14, "script"], [199, 22, 203, 20], [199, 23, 203, 21, "onerror"], [199, 30, 203, 28], [199, 33, 203, 31, "reject"], [199, 39, 203, 37], [200, 16, 204, 14, "document"], [200, 24, 204, 22], [200, 25, 204, 23, "head"], [200, 29, 204, 27], [200, 30, 204, 28, "append<PERSON><PERSON><PERSON>"], [200, 41, 204, 39], [200, 42, 204, 40, "script"], [200, 48, 204, 46], [200, 49, 204, 47], [201, 14, 205, 12], [201, 15, 205, 13], [201, 16, 205, 14], [202, 12, 206, 10], [203, 12, 208, 10], [203, 18, 208, 16, "<PERSON>ap<PERSON>"], [203, 25, 208, 23], [203, 28, 208, 27, "window"], [203, 34, 208, 33], [203, 35, 208, 42, "<PERSON>ap<PERSON>"], [203, 42, 208, 49], [205, 12, 210, 10], [206, 12, 211, 10], [206, 18, 211, 16, "Promise"], [206, 25, 211, 23], [206, 26, 211, 24, "all"], [206, 29, 211, 27], [206, 30, 211, 28], [206, 31, 212, 12, "<PERSON>ap<PERSON>"], [206, 38, 212, 19], [206, 39, 212, 20, "nets"], [206, 43, 212, 24], [206, 44, 212, 25, "tinyFaceDetector"], [206, 60, 212, 41], [206, 61, 212, 42, "loadFromUri"], [206, 72, 212, 53], [206, 73, 212, 54], [206, 130, 212, 111], [206, 131, 212, 112], [206, 133, 213, 12, "<PERSON>ap<PERSON>"], [206, 140, 213, 19], [206, 141, 213, 20, "nets"], [206, 145, 213, 24], [206, 146, 213, 25, "faceLandmark68Net"], [206, 163, 213, 42], [206, 164, 213, 43, "loadFromUri"], [206, 175, 213, 54], [206, 176, 213, 55], [206, 233, 213, 112], [206, 234, 213, 113], [206, 235, 214, 11], [206, 236, 214, 12], [208, 12, 216, 10], [209, 12, 217, 10], [209, 18, 217, 16, "faceDetections"], [209, 32, 217, 30], [209, 35, 217, 33], [209, 41, 217, 39, "<PERSON>ap<PERSON>"], [209, 48, 217, 46], [209, 49, 217, 47, "detectAllFaces"], [209, 63, 217, 61], [209, 64, 217, 62, "img"], [209, 67, 217, 65], [209, 69, 217, 67], [209, 73, 217, 71, "<PERSON>ap<PERSON>"], [209, 80, 217, 78], [209, 81, 217, 79, "TinyFaceDetectorOptions"], [209, 104, 217, 102], [209, 105, 217, 103], [209, 106, 217, 104], [209, 107, 217, 105], [210, 12, 219, 10, "detectedFaces"], [210, 25, 219, 23], [210, 28, 219, 26, "faceDetections"], [210, 42, 219, 40], [210, 43, 219, 41, "map"], [210, 46, 219, 44], [210, 47, 219, 46, "detection"], [210, 56, 219, 60], [210, 61, 219, 66], [211, 14, 220, 12, "boundingBox"], [211, 25, 220, 23], [211, 27, 220, 25], [212, 16, 221, 14, "xCenter"], [212, 23, 221, 21], [212, 25, 221, 23], [212, 26, 221, 24, "detection"], [212, 35, 221, 33], [212, 36, 221, 34, "box"], [212, 39, 221, 37], [212, 40, 221, 38, "x"], [212, 41, 221, 39], [212, 44, 221, 42, "detection"], [212, 53, 221, 51], [212, 54, 221, 52, "box"], [212, 57, 221, 55], [212, 58, 221, 56, "width"], [212, 63, 221, 61], [212, 66, 221, 64], [212, 67, 221, 65], [212, 71, 221, 69, "img"], [212, 74, 221, 72], [212, 75, 221, 73, "width"], [212, 80, 221, 78], [213, 16, 222, 14, "yCenter"], [213, 23, 222, 21], [213, 25, 222, 23], [213, 26, 222, 24, "detection"], [213, 35, 222, 33], [213, 36, 222, 34, "box"], [213, 39, 222, 37], [213, 40, 222, 38, "y"], [213, 41, 222, 39], [213, 44, 222, 42, "detection"], [213, 53, 222, 51], [213, 54, 222, 52, "box"], [213, 57, 222, 55], [213, 58, 222, 56, "height"], [213, 64, 222, 62], [213, 67, 222, 65], [213, 68, 222, 66], [213, 72, 222, 70, "img"], [213, 75, 222, 73], [213, 76, 222, 74, "height"], [213, 82, 222, 80], [214, 16, 223, 14, "width"], [214, 21, 223, 19], [214, 23, 223, 21, "detection"], [214, 32, 223, 30], [214, 33, 223, 31, "box"], [214, 36, 223, 34], [214, 37, 223, 35, "width"], [214, 42, 223, 40], [214, 45, 223, 43, "img"], [214, 48, 223, 46], [214, 49, 223, 47, "width"], [214, 54, 223, 52], [215, 16, 224, 14, "height"], [215, 22, 224, 20], [215, 24, 224, 22, "detection"], [215, 33, 224, 31], [215, 34, 224, 32, "box"], [215, 37, 224, 35], [215, 38, 224, 36, "height"], [215, 44, 224, 42], [215, 47, 224, 45, "img"], [215, 50, 224, 48], [215, 51, 224, 49, "height"], [216, 14, 225, 12], [217, 12, 226, 10], [217, 13, 226, 11], [217, 14, 226, 12], [217, 15, 226, 13], [218, 12, 228, 10, "console"], [218, 19, 228, 17], [218, 20, 228, 18, "log"], [218, 23, 228, 21], [218, 24, 228, 22], [218, 61, 228, 59, "detectedFaces"], [218, 74, 228, 72], [218, 75, 228, 73, "length"], [218, 81, 228, 79], [218, 89, 228, 87], [218, 90, 228, 88], [219, 10, 229, 8], [219, 11, 229, 9], [219, 12, 229, 10], [219, 19, 229, 17, "faceApiError"], [219, 31, 229, 29], [219, 33, 229, 31], [220, 12, 230, 10, "console"], [220, 19, 230, 17], [220, 20, 230, 18, "warn"], [220, 24, 230, 22], [220, 25, 230, 23], [220, 67, 230, 65], [220, 69, 230, 67, "faceApiError"], [220, 81, 230, 79], [220, 82, 230, 80], [221, 12, 231, 10, "detectedFaces"], [221, 25, 231, 23], [221, 28, 231, 26], [221, 30, 231, 28], [222, 10, 232, 8], [223, 8, 233, 6], [224, 8, 235, 6, "console"], [224, 15, 235, 13], [224, 16, 235, 14, "log"], [224, 19, 235, 17], [224, 20, 235, 18], [224, 72, 235, 70, "detectedFaces"], [224, 85, 235, 83], [224, 86, 235, 84, "length"], [224, 92, 235, 90], [224, 100, 235, 98], [224, 101, 235, 99], [225, 8, 236, 6], [225, 12, 236, 10, "detectedFaces"], [225, 25, 236, 23], [225, 26, 236, 24, "length"], [225, 32, 236, 30], [225, 35, 236, 33], [225, 36, 236, 34], [225, 38, 236, 36], [226, 10, 237, 8, "console"], [226, 17, 237, 15], [226, 18, 237, 16, "log"], [226, 21, 237, 19], [226, 22, 237, 20], [226, 66, 237, 64], [226, 68, 237, 66, "detectedFaces"], [226, 81, 237, 79], [226, 82, 237, 80, "map"], [226, 85, 237, 83], [226, 86, 237, 84], [226, 87, 237, 85, "face"], [226, 91, 237, 89], [226, 93, 237, 91, "i"], [226, 94, 237, 92], [226, 100, 237, 98], [227, 12, 238, 10, "faceNumber"], [227, 22, 238, 20], [227, 24, 238, 22, "i"], [227, 25, 238, 23], [227, 28, 238, 26], [227, 29, 238, 27], [228, 12, 239, 10, "centerX"], [228, 19, 239, 17], [228, 21, 239, 19, "face"], [228, 25, 239, 23], [228, 26, 239, 24, "boundingBox"], [228, 37, 239, 35], [228, 38, 239, 36, "xCenter"], [228, 45, 239, 43], [229, 12, 240, 10, "centerY"], [229, 19, 240, 17], [229, 21, 240, 19, "face"], [229, 25, 240, 23], [229, 26, 240, 24, "boundingBox"], [229, 37, 240, 35], [229, 38, 240, 36, "yCenter"], [229, 45, 240, 43], [230, 12, 241, 10, "width"], [230, 17, 241, 15], [230, 19, 241, 17, "face"], [230, 23, 241, 21], [230, 24, 241, 22, "boundingBox"], [230, 35, 241, 33], [230, 36, 241, 34, "width"], [230, 41, 241, 39], [231, 12, 242, 10, "height"], [231, 18, 242, 16], [231, 20, 242, 18, "face"], [231, 24, 242, 22], [231, 25, 242, 23, "boundingBox"], [231, 36, 242, 34], [231, 37, 242, 35, "height"], [232, 10, 243, 8], [232, 11, 243, 9], [232, 12, 243, 10], [232, 13, 243, 11], [232, 14, 243, 12], [233, 8, 244, 6], [233, 9, 244, 7], [233, 15, 244, 13], [234, 10, 245, 8, "console"], [234, 17, 245, 15], [234, 18, 245, 16, "log"], [234, 21, 245, 19], [234, 22, 245, 20], [234, 91, 245, 89], [234, 92, 245, 90], [235, 8, 246, 6], [236, 8, 248, 6, "setProcessingProgress"], [236, 29, 248, 27], [236, 30, 248, 28], [236, 32, 248, 30], [236, 33, 248, 31], [238, 8, 250, 6], [239, 8, 251, 6], [239, 12, 251, 10, "detectedFaces"], [239, 25, 251, 23], [239, 26, 251, 24, "length"], [239, 32, 251, 30], [239, 35, 251, 33], [239, 36, 251, 34], [239, 38, 251, 36], [240, 10, 252, 8, "detectedFaces"], [240, 23, 252, 21], [240, 24, 252, 22, "for<PERSON>ach"], [240, 31, 252, 29], [240, 32, 252, 30], [240, 33, 252, 31, "detection"], [240, 42, 252, 40], [240, 44, 252, 42, "index"], [240, 49, 252, 47], [240, 54, 252, 52], [241, 12, 253, 10], [241, 18, 253, 16, "bbox"], [241, 22, 253, 20], [241, 25, 253, 23, "detection"], [241, 34, 253, 32], [241, 35, 253, 33, "boundingBox"], [241, 46, 253, 44], [243, 12, 255, 10], [244, 12, 256, 10], [244, 18, 256, 16, "faceX"], [244, 23, 256, 21], [244, 26, 256, 24, "bbox"], [244, 30, 256, 28], [244, 31, 256, 29, "xCenter"], [244, 38, 256, 36], [244, 41, 256, 39, "img"], [244, 44, 256, 42], [244, 45, 256, 43, "width"], [244, 50, 256, 48], [244, 53, 256, 52, "bbox"], [244, 57, 256, 56], [244, 58, 256, 57, "width"], [244, 63, 256, 62], [244, 66, 256, 65, "img"], [244, 69, 256, 68], [244, 70, 256, 69, "width"], [244, 75, 256, 74], [244, 78, 256, 78], [244, 79, 256, 79], [245, 12, 257, 10], [245, 18, 257, 16, "faceY"], [245, 23, 257, 21], [245, 26, 257, 24, "bbox"], [245, 30, 257, 28], [245, 31, 257, 29, "yCenter"], [245, 38, 257, 36], [245, 41, 257, 39, "img"], [245, 44, 257, 42], [245, 45, 257, 43, "height"], [245, 51, 257, 49], [245, 54, 257, 53, "bbox"], [245, 58, 257, 57], [245, 59, 257, 58, "height"], [245, 65, 257, 64], [245, 68, 257, 67, "img"], [245, 71, 257, 70], [245, 72, 257, 71, "height"], [245, 78, 257, 77], [245, 81, 257, 81], [245, 82, 257, 82], [246, 12, 258, 10], [246, 18, 258, 16, "faceWidth"], [246, 27, 258, 25], [246, 30, 258, 28, "bbox"], [246, 34, 258, 32], [246, 35, 258, 33, "width"], [246, 40, 258, 38], [246, 43, 258, 41, "img"], [246, 46, 258, 44], [246, 47, 258, 45, "width"], [246, 52, 258, 50], [247, 12, 259, 10], [247, 18, 259, 16, "faceHeight"], [247, 28, 259, 26], [247, 31, 259, 29, "bbox"], [247, 35, 259, 33], [247, 36, 259, 34, "height"], [247, 42, 259, 40], [247, 45, 259, 43, "img"], [247, 48, 259, 46], [247, 49, 259, 47, "height"], [247, 55, 259, 53], [249, 12, 261, 10], [250, 12, 262, 10], [250, 18, 262, 16, "padding"], [250, 25, 262, 23], [250, 28, 262, 26], [250, 31, 262, 29], [250, 32, 262, 30], [250, 33, 262, 31], [251, 12, 263, 10], [251, 18, 263, 16, "paddedX"], [251, 25, 263, 23], [251, 28, 263, 26, "Math"], [251, 32, 263, 30], [251, 33, 263, 31, "max"], [251, 36, 263, 34], [251, 37, 263, 35], [251, 38, 263, 36], [251, 40, 263, 38, "faceX"], [251, 45, 263, 43], [251, 48, 263, 46, "faceWidth"], [251, 57, 263, 55], [251, 60, 263, 58, "padding"], [251, 67, 263, 65], [251, 68, 263, 66], [252, 12, 264, 10], [252, 18, 264, 16, "paddedY"], [252, 25, 264, 23], [252, 28, 264, 26, "Math"], [252, 32, 264, 30], [252, 33, 264, 31, "max"], [252, 36, 264, 34], [252, 37, 264, 35], [252, 38, 264, 36], [252, 40, 264, 38, "faceY"], [252, 45, 264, 43], [252, 48, 264, 46, "faceHeight"], [252, 58, 264, 56], [252, 61, 264, 59, "padding"], [252, 68, 264, 66], [252, 69, 264, 67], [253, 12, 265, 10], [253, 18, 265, 16, "<PERSON><PERSON><PERSON><PERSON>"], [253, 29, 265, 27], [253, 32, 265, 30, "Math"], [253, 36, 265, 34], [253, 37, 265, 35, "min"], [253, 40, 265, 38], [253, 41, 265, 39, "img"], [253, 44, 265, 42], [253, 45, 265, 43, "width"], [253, 50, 265, 48], [253, 53, 265, 51, "paddedX"], [253, 60, 265, 58], [253, 62, 265, 60, "faceWidth"], [253, 71, 265, 69], [253, 75, 265, 73], [253, 76, 265, 74], [253, 79, 265, 77], [253, 80, 265, 78], [253, 83, 265, 81, "padding"], [253, 90, 265, 88], [253, 91, 265, 89], [253, 92, 265, 90], [254, 12, 266, 10], [254, 18, 266, 16, "paddedHeight"], [254, 30, 266, 28], [254, 33, 266, 31, "Math"], [254, 37, 266, 35], [254, 38, 266, 36, "min"], [254, 41, 266, 39], [254, 42, 266, 40, "img"], [254, 45, 266, 43], [254, 46, 266, 44, "height"], [254, 52, 266, 50], [254, 55, 266, 53, "paddedY"], [254, 62, 266, 60], [254, 64, 266, 62, "faceHeight"], [254, 74, 266, 72], [254, 78, 266, 76], [254, 79, 266, 77], [254, 82, 266, 80], [254, 83, 266, 81], [254, 86, 266, 84, "padding"], [254, 93, 266, 91], [254, 94, 266, 92], [254, 95, 266, 93], [255, 12, 268, 10, "console"], [255, 19, 268, 17], [255, 20, 268, 18, "log"], [255, 23, 268, 21], [255, 24, 268, 22], [255, 57, 268, 55, "index"], [255, 62, 268, 60], [255, 65, 268, 63], [255, 66, 268, 64], [255, 74, 268, 72, "paddedX"], [255, 81, 268, 79], [255, 86, 268, 84, "paddedY"], [255, 93, 268, 91], [255, 103, 268, 101, "<PERSON><PERSON><PERSON><PERSON>"], [255, 114, 268, 112], [255, 118, 268, 116, "paddedHeight"], [255, 130, 268, 128], [255, 132, 268, 130], [255, 133, 268, 131], [257, 12, 270, 10], [258, 12, 271, 10], [258, 18, 271, 16, "faceImageData"], [258, 31, 271, 29], [258, 34, 271, 32, "ctx"], [258, 37, 271, 35], [258, 38, 271, 36, "getImageData"], [258, 50, 271, 48], [258, 51, 271, 49, "paddedX"], [258, 58, 271, 56], [258, 60, 271, 58, "paddedY"], [258, 67, 271, 65], [258, 69, 271, 67, "<PERSON><PERSON><PERSON><PERSON>"], [258, 80, 271, 78], [258, 82, 271, 80, "paddedHeight"], [258, 94, 271, 92], [258, 95, 271, 93], [259, 12, 272, 10], [259, 18, 272, 16, "data"], [259, 22, 272, 20], [259, 25, 272, 23, "faceImageData"], [259, 38, 272, 36], [259, 39, 272, 37, "data"], [259, 43, 272, 41], [261, 12, 274, 10], [262, 12, 275, 10], [262, 18, 275, 16, "pixelSize"], [262, 27, 275, 25], [262, 30, 275, 28, "Math"], [262, 34, 275, 32], [262, 35, 275, 33, "max"], [262, 38, 275, 36], [262, 39, 275, 37], [262, 40, 275, 38], [262, 42, 275, 40, "Math"], [262, 46, 275, 44], [262, 47, 275, 45, "min"], [262, 50, 275, 48], [262, 51, 275, 49, "<PERSON><PERSON><PERSON><PERSON>"], [262, 62, 275, 60], [262, 64, 275, 62, "paddedHeight"], [262, 76, 275, 74], [262, 77, 275, 75], [262, 80, 275, 78], [262, 82, 275, 80], [262, 83, 275, 81], [262, 84, 275, 82], [262, 85, 275, 83], [263, 12, 276, 10], [263, 17, 276, 15], [263, 21, 276, 19, "y"], [263, 22, 276, 20], [263, 25, 276, 23], [263, 26, 276, 24], [263, 28, 276, 26, "y"], [263, 29, 276, 27], [263, 32, 276, 30, "paddedHeight"], [263, 44, 276, 42], [263, 46, 276, 44, "y"], [263, 47, 276, 45], [263, 51, 276, 49, "pixelSize"], [263, 60, 276, 58], [263, 62, 276, 60], [264, 14, 277, 12], [264, 19, 277, 17], [264, 23, 277, 21, "x"], [264, 24, 277, 22], [264, 27, 277, 25], [264, 28, 277, 26], [264, 30, 277, 28, "x"], [264, 31, 277, 29], [264, 34, 277, 32, "<PERSON><PERSON><PERSON><PERSON>"], [264, 45, 277, 43], [264, 47, 277, 45, "x"], [264, 48, 277, 46], [264, 52, 277, 50, "pixelSize"], [264, 61, 277, 59], [264, 63, 277, 61], [265, 16, 278, 14], [266, 16, 279, 14], [266, 22, 279, 20, "pixelIndex"], [266, 32, 279, 30], [266, 35, 279, 33], [266, 36, 279, 34, "y"], [266, 37, 279, 35], [266, 40, 279, 38, "<PERSON><PERSON><PERSON><PERSON>"], [266, 51, 279, 49], [266, 54, 279, 52, "x"], [266, 55, 279, 53], [266, 59, 279, 57], [266, 60, 279, 58], [267, 16, 280, 14], [267, 22, 280, 20, "r"], [267, 23, 280, 21], [267, 26, 280, 24, "data"], [267, 30, 280, 28], [267, 31, 280, 29, "pixelIndex"], [267, 41, 280, 39], [267, 42, 280, 40], [268, 16, 281, 14], [268, 22, 281, 20, "g"], [268, 23, 281, 21], [268, 26, 281, 24, "data"], [268, 30, 281, 28], [268, 31, 281, 29, "pixelIndex"], [268, 41, 281, 39], [268, 44, 281, 42], [268, 45, 281, 43], [268, 46, 281, 44], [269, 16, 282, 14], [269, 22, 282, 20, "b"], [269, 23, 282, 21], [269, 26, 282, 24, "data"], [269, 30, 282, 28], [269, 31, 282, 29, "pixelIndex"], [269, 41, 282, 39], [269, 44, 282, 42], [269, 45, 282, 43], [269, 46, 282, 44], [270, 16, 283, 14], [270, 22, 283, 20, "a"], [270, 23, 283, 21], [270, 26, 283, 24, "data"], [270, 30, 283, 28], [270, 31, 283, 29, "pixelIndex"], [270, 41, 283, 39], [270, 44, 283, 42], [270, 45, 283, 43], [270, 46, 283, 44], [272, 16, 285, 14], [273, 16, 286, 14], [273, 21, 286, 19], [273, 25, 286, 23, "dy"], [273, 27, 286, 25], [273, 30, 286, 28], [273, 31, 286, 29], [273, 33, 286, 31, "dy"], [273, 35, 286, 33], [273, 38, 286, 36, "pixelSize"], [273, 47, 286, 45], [273, 51, 286, 49, "y"], [273, 52, 286, 50], [273, 55, 286, 53, "dy"], [273, 57, 286, 55], [273, 60, 286, 58, "paddedHeight"], [273, 72, 286, 70], [273, 74, 286, 72, "dy"], [273, 76, 286, 74], [273, 78, 286, 76], [273, 80, 286, 78], [274, 18, 287, 16], [274, 23, 287, 21], [274, 27, 287, 25, "dx"], [274, 29, 287, 27], [274, 32, 287, 30], [274, 33, 287, 31], [274, 35, 287, 33, "dx"], [274, 37, 287, 35], [274, 40, 287, 38, "pixelSize"], [274, 49, 287, 47], [274, 53, 287, 51, "x"], [274, 54, 287, 52], [274, 57, 287, 55, "dx"], [274, 59, 287, 57], [274, 62, 287, 60, "<PERSON><PERSON><PERSON><PERSON>"], [274, 73, 287, 71], [274, 75, 287, 73, "dx"], [274, 77, 287, 75], [274, 79, 287, 77], [274, 81, 287, 79], [275, 20, 288, 18], [275, 26, 288, 24, "blockPixelIndex"], [275, 41, 288, 39], [275, 44, 288, 42], [275, 45, 288, 43], [275, 46, 288, 44, "y"], [275, 47, 288, 45], [275, 50, 288, 48, "dy"], [275, 52, 288, 50], [275, 56, 288, 54, "<PERSON><PERSON><PERSON><PERSON>"], [275, 67, 288, 65], [275, 71, 288, 69, "x"], [275, 72, 288, 70], [275, 75, 288, 73, "dx"], [275, 77, 288, 75], [275, 78, 288, 76], [275, 82, 288, 80], [275, 83, 288, 81], [276, 20, 289, 18, "data"], [276, 24, 289, 22], [276, 25, 289, 23, "blockPixelIndex"], [276, 40, 289, 38], [276, 41, 289, 39], [276, 44, 289, 42, "r"], [276, 45, 289, 43], [277, 20, 290, 18, "data"], [277, 24, 290, 22], [277, 25, 290, 23, "blockPixelIndex"], [277, 40, 290, 38], [277, 43, 290, 41], [277, 44, 290, 42], [277, 45, 290, 43], [277, 48, 290, 46, "g"], [277, 49, 290, 47], [278, 20, 291, 18, "data"], [278, 24, 291, 22], [278, 25, 291, 23, "blockPixelIndex"], [278, 40, 291, 38], [278, 43, 291, 41], [278, 44, 291, 42], [278, 45, 291, 43], [278, 48, 291, 46, "b"], [278, 49, 291, 47], [279, 20, 292, 18, "data"], [279, 24, 292, 22], [279, 25, 292, 23, "blockPixelIndex"], [279, 40, 292, 38], [279, 43, 292, 41], [279, 44, 292, 42], [279, 45, 292, 43], [279, 48, 292, 46, "a"], [279, 49, 292, 47], [280, 18, 293, 16], [281, 16, 294, 14], [282, 14, 295, 12], [283, 12, 296, 10], [285, 12, 298, 10], [286, 12, 299, 10, "ctx"], [286, 15, 299, 13], [286, 16, 299, 14, "putImageData"], [286, 28, 299, 26], [286, 29, 299, 27, "faceImageData"], [286, 42, 299, 40], [286, 44, 299, 42, "paddedX"], [286, 51, 299, 49], [286, 53, 299, 51, "paddedY"], [286, 60, 299, 58], [286, 61, 299, 59], [287, 10, 300, 8], [287, 11, 300, 9], [287, 12, 300, 10], [288, 8, 301, 6], [288, 9, 301, 7], [288, 15, 301, 13], [289, 10, 302, 8, "console"], [289, 17, 302, 15], [289, 18, 302, 16, "log"], [289, 21, 302, 19], [289, 22, 302, 20], [289, 88, 302, 86], [289, 89, 302, 87], [290, 8, 303, 6], [291, 8, 305, 6, "setProcessingProgress"], [291, 29, 305, 27], [291, 30, 305, 28], [291, 32, 305, 30], [291, 33, 305, 31], [293, 8, 307, 6], [294, 8, 308, 6], [294, 14, 308, 12, "blurredImageBlob"], [294, 30, 308, 28], [294, 33, 308, 31], [294, 39, 308, 37], [294, 43, 308, 41, "Promise"], [294, 50, 308, 48], [294, 51, 308, 56, "resolve"], [294, 58, 308, 63], [294, 62, 308, 68], [295, 10, 309, 8, "canvas"], [295, 16, 309, 14], [295, 17, 309, 15, "toBlob"], [295, 23, 309, 21], [295, 24, 309, 23, "blob"], [295, 28, 309, 27], [295, 32, 309, 32, "resolve"], [295, 39, 309, 39], [295, 40, 309, 40, "blob"], [295, 44, 309, 45], [295, 45, 309, 46], [295, 47, 309, 48], [295, 59, 309, 60], [295, 61, 309, 62], [295, 64, 309, 65], [295, 65, 309, 66], [296, 8, 310, 6], [296, 9, 310, 7], [296, 10, 310, 8], [297, 8, 312, 6], [297, 14, 312, 12, "blurredImageUrl"], [297, 29, 312, 27], [297, 32, 312, 30, "URL"], [297, 35, 312, 33], [297, 36, 312, 34, "createObjectURL"], [297, 51, 312, 49], [297, 52, 312, 50, "blurredImageBlob"], [297, 68, 312, 66], [297, 69, 312, 67], [298, 8, 314, 6, "setProcessingProgress"], [298, 29, 314, 27], [298, 30, 314, 28], [298, 33, 314, 31], [298, 34, 314, 32], [300, 8, 316, 6], [301, 8, 317, 6], [301, 14, 317, 12, "completeProcessing"], [301, 32, 317, 30], [301, 33, 317, 31, "blurredImageUrl"], [301, 48, 317, 46], [301, 49, 317, 47], [302, 6, 319, 4], [302, 7, 319, 5], [302, 8, 319, 6], [302, 15, 319, 13, "error"], [302, 20, 319, 18], [302, 22, 319, 20], [303, 8, 320, 6, "console"], [303, 15, 320, 13], [303, 16, 320, 14, "error"], [303, 21, 320, 19], [303, 22, 320, 20], [303, 57, 320, 55], [303, 59, 320, 57, "error"], [303, 64, 320, 62], [303, 65, 320, 63], [304, 8, 321, 6, "setErrorMessage"], [304, 23, 321, 21], [304, 24, 321, 22], [304, 50, 321, 48], [304, 51, 321, 49], [305, 8, 322, 6, "setProcessingState"], [305, 26, 322, 24], [305, 27, 322, 25], [305, 34, 322, 32], [305, 35, 322, 33], [306, 6, 323, 4], [307, 4, 324, 2], [307, 5, 324, 3], [309, 4, 326, 2], [310, 4, 327, 2], [310, 10, 327, 8, "completeProcessing"], [310, 28, 327, 26], [310, 31, 327, 29], [310, 37, 327, 36, "blurredImageUrl"], [310, 52, 327, 59], [310, 56, 327, 64], [311, 6, 328, 4], [311, 10, 328, 8], [312, 8, 329, 6, "setProcessingState"], [312, 26, 329, 24], [312, 27, 329, 25], [312, 37, 329, 35], [312, 38, 329, 36], [314, 8, 331, 6], [315, 8, 332, 6], [315, 14, 332, 12, "timestamp"], [315, 23, 332, 21], [315, 26, 332, 24, "Date"], [315, 30, 332, 28], [315, 31, 332, 29, "now"], [315, 34, 332, 32], [315, 35, 332, 33], [315, 36, 332, 34], [316, 8, 333, 6], [316, 14, 333, 12, "result"], [316, 20, 333, 18], [316, 23, 333, 21], [317, 10, 334, 8, "imageUrl"], [317, 18, 334, 16], [317, 20, 334, 18, "blurredImageUrl"], [317, 35, 334, 33], [318, 10, 335, 8, "localUri"], [318, 18, 335, 16], [318, 20, 335, 18, "blurredImageUrl"], [318, 35, 335, 33], [319, 10, 336, 8, "challengeCode"], [319, 23, 336, 21], [319, 25, 336, 23, "challengeCode"], [319, 38, 336, 36], [319, 42, 336, 40], [319, 44, 336, 42], [320, 10, 337, 8, "timestamp"], [320, 19, 337, 17], [321, 10, 338, 8, "jobId"], [321, 15, 338, 13], [321, 17, 338, 15], [321, 27, 338, 25, "timestamp"], [321, 36, 338, 34], [321, 38, 338, 36], [322, 10, 339, 8, "status"], [322, 16, 339, 14], [322, 18, 339, 16], [323, 8, 340, 6], [323, 9, 340, 7], [324, 8, 342, 6, "console"], [324, 15, 342, 13], [324, 16, 342, 14, "log"], [324, 19, 342, 17], [324, 20, 342, 18], [324, 58, 342, 56], [324, 60, 342, 58, "result"], [324, 66, 342, 64], [324, 67, 342, 65], [326, 8, 344, 6], [327, 8, 345, 6, "onComplete"], [327, 18, 345, 16], [327, 19, 345, 17, "result"], [327, 25, 345, 23], [327, 26, 345, 24], [328, 6, 347, 4], [328, 7, 347, 5], [328, 8, 347, 6], [328, 15, 347, 13, "error"], [328, 20, 347, 18], [328, 22, 347, 20], [329, 8, 348, 6, "console"], [329, 15, 348, 13], [329, 16, 348, 14, "error"], [329, 21, 348, 19], [329, 22, 348, 20], [329, 57, 348, 55], [329, 59, 348, 57, "error"], [329, 64, 348, 62], [329, 65, 348, 63], [330, 8, 349, 6, "setErrorMessage"], [330, 23, 349, 21], [330, 24, 349, 22], [330, 56, 349, 54], [330, 57, 349, 55], [331, 8, 350, 6, "setProcessingState"], [331, 26, 350, 24], [331, 27, 350, 25], [331, 34, 350, 32], [331, 35, 350, 33], [332, 6, 351, 4], [333, 4, 352, 2], [333, 5, 352, 3], [335, 4, 354, 2], [336, 4, 355, 2], [336, 10, 355, 8, "triggerServerProcessing"], [336, 33, 355, 31], [336, 36, 355, 34], [336, 42, 355, 34, "triggerServerProcessing"], [336, 43, 355, 41, "privateImageUrl"], [336, 58, 355, 64], [336, 60, 355, 66, "timestamp"], [336, 69, 355, 83], [336, 74, 355, 88], [337, 6, 356, 4], [337, 10, 356, 8], [338, 8, 357, 6, "console"], [338, 15, 357, 13], [338, 16, 357, 14, "log"], [338, 19, 357, 17], [338, 20, 357, 18], [338, 74, 357, 72], [338, 76, 357, 74, "privateImageUrl"], [338, 91, 357, 89], [338, 92, 357, 90], [339, 8, 358, 6, "setProcessingState"], [339, 26, 358, 24], [339, 27, 358, 25], [339, 39, 358, 37], [339, 40, 358, 38], [340, 8, 359, 6, "setProcessingProgress"], [340, 29, 359, 27], [340, 30, 359, 28], [340, 32, 359, 30], [340, 33, 359, 31], [341, 8, 361, 6], [341, 14, 361, 12, "requestBody"], [341, 25, 361, 23], [341, 28, 361, 26], [342, 10, 362, 8, "imageUrl"], [342, 18, 362, 16], [342, 20, 362, 18, "privateImageUrl"], [342, 35, 362, 33], [343, 10, 363, 8, "userId"], [343, 16, 363, 14], [344, 10, 364, 8, "requestId"], [344, 19, 364, 17], [345, 10, 365, 8, "timestamp"], [345, 19, 365, 17], [346, 10, 366, 8, "platform"], [346, 18, 366, 16], [346, 20, 366, 18], [347, 8, 367, 6], [347, 9, 367, 7], [348, 8, 369, 6, "console"], [348, 15, 369, 13], [348, 16, 369, 14, "log"], [348, 19, 369, 17], [348, 20, 369, 18], [348, 65, 369, 63], [348, 67, 369, 65, "requestBody"], [348, 78, 369, 76], [348, 79, 369, 77], [350, 8, 371, 6], [351, 8, 372, 6], [351, 14, 372, 12, "response"], [351, 22, 372, 20], [351, 25, 372, 23], [351, 31, 372, 29, "fetch"], [351, 36, 372, 34], [351, 37, 372, 35], [351, 40, 372, 38, "API_BASE_URL"], [351, 52, 372, 50], [351, 72, 372, 70], [351, 74, 372, 72], [352, 10, 373, 8, "method"], [352, 16, 373, 14], [352, 18, 373, 16], [352, 24, 373, 22], [353, 10, 374, 8, "headers"], [353, 17, 374, 15], [353, 19, 374, 17], [354, 12, 375, 10], [354, 26, 375, 24], [354, 28, 375, 26], [354, 46, 375, 44], [355, 12, 376, 10], [355, 27, 376, 25], [355, 29, 376, 27], [355, 39, 376, 37], [355, 45, 376, 43, "getAuthToken"], [355, 57, 376, 55], [355, 58, 376, 56], [355, 59, 376, 57], [356, 10, 377, 8], [356, 11, 377, 9], [357, 10, 378, 8, "body"], [357, 14, 378, 12], [357, 16, 378, 14, "JSON"], [357, 20, 378, 18], [357, 21, 378, 19, "stringify"], [357, 30, 378, 28], [357, 31, 378, 29, "requestBody"], [357, 42, 378, 40], [358, 8, 379, 6], [358, 9, 379, 7], [358, 10, 379, 8], [359, 8, 381, 6], [359, 12, 381, 10], [359, 13, 381, 11, "response"], [359, 21, 381, 19], [359, 22, 381, 20, "ok"], [359, 24, 381, 22], [359, 26, 381, 24], [360, 10, 382, 8], [360, 16, 382, 14, "errorText"], [360, 25, 382, 23], [360, 28, 382, 26], [360, 34, 382, 32, "response"], [360, 42, 382, 40], [360, 43, 382, 41, "text"], [360, 47, 382, 45], [360, 48, 382, 46], [360, 49, 382, 47], [361, 10, 383, 8, "console"], [361, 17, 383, 15], [361, 18, 383, 16, "error"], [361, 23, 383, 21], [361, 24, 383, 22], [361, 68, 383, 66], [361, 70, 383, 68, "response"], [361, 78, 383, 76], [361, 79, 383, 77, "status"], [361, 85, 383, 83], [361, 87, 383, 85, "errorText"], [361, 96, 383, 94], [361, 97, 383, 95], [362, 10, 384, 8], [362, 16, 384, 14], [362, 20, 384, 18, "Error"], [362, 25, 384, 23], [362, 26, 384, 24], [362, 48, 384, 46, "response"], [362, 56, 384, 54], [362, 57, 384, 55, "status"], [362, 63, 384, 61], [362, 67, 384, 65, "response"], [362, 75, 384, 73], [362, 76, 384, 74, "statusText"], [362, 86, 384, 84], [362, 88, 384, 86], [362, 89, 384, 87], [363, 8, 385, 6], [364, 8, 387, 6], [364, 14, 387, 12, "result"], [364, 20, 387, 18], [364, 23, 387, 21], [364, 29, 387, 27, "response"], [364, 37, 387, 35], [364, 38, 387, 36, "json"], [364, 42, 387, 40], [364, 43, 387, 41], [364, 44, 387, 42], [365, 8, 388, 6, "console"], [365, 15, 388, 13], [365, 16, 388, 14, "log"], [365, 19, 388, 17], [365, 20, 388, 18], [365, 68, 388, 66], [365, 70, 388, 68, "result"], [365, 76, 388, 74], [365, 77, 388, 75], [366, 8, 390, 6], [366, 12, 390, 10], [366, 13, 390, 11, "result"], [366, 19, 390, 17], [366, 20, 390, 18, "jobId"], [366, 25, 390, 23], [366, 27, 390, 25], [367, 10, 391, 8], [367, 16, 391, 14], [367, 20, 391, 18, "Error"], [367, 25, 391, 23], [367, 26, 391, 24], [367, 70, 391, 68], [367, 71, 391, 69], [368, 8, 392, 6], [370, 8, 394, 6], [371, 8, 395, 6], [371, 14, 395, 12, "pollForCompletion"], [371, 31, 395, 29], [371, 32, 395, 30, "result"], [371, 38, 395, 36], [371, 39, 395, 37, "jobId"], [371, 44, 395, 42], [371, 46, 395, 44, "timestamp"], [371, 55, 395, 53], [371, 56, 395, 54], [372, 6, 396, 4], [372, 7, 396, 5], [372, 8, 396, 6], [372, 15, 396, 13, "error"], [372, 20, 396, 18], [372, 22, 396, 20], [373, 8, 397, 6, "console"], [373, 15, 397, 13], [373, 16, 397, 14, "error"], [373, 21, 397, 19], [373, 22, 397, 20], [373, 57, 397, 55], [373, 59, 397, 57, "error"], [373, 64, 397, 62], [373, 65, 397, 63], [374, 8, 398, 6, "setErrorMessage"], [374, 23, 398, 21], [374, 24, 398, 22], [374, 52, 398, 50, "error"], [374, 57, 398, 55], [374, 58, 398, 56, "message"], [374, 65, 398, 63], [374, 67, 398, 65], [374, 68, 398, 66], [375, 8, 399, 6, "setProcessingState"], [375, 26, 399, 24], [375, 27, 399, 25], [375, 34, 399, 32], [375, 35, 399, 33], [376, 6, 400, 4], [377, 4, 401, 2], [377, 5, 401, 3], [378, 4, 402, 2], [379, 4, 403, 2], [379, 10, 403, 8, "pollForCompletion"], [379, 27, 403, 25], [379, 30, 403, 28], [379, 36, 403, 28, "pollForCompletion"], [379, 37, 403, 35, "jobId"], [379, 42, 403, 48], [379, 44, 403, 50, "timestamp"], [379, 53, 403, 67], [379, 55, 403, 69, "attempts"], [379, 63, 403, 77], [379, 66, 403, 80], [379, 67, 403, 81], [379, 72, 403, 86], [380, 6, 404, 4], [380, 12, 404, 10, "MAX_ATTEMPTS"], [380, 24, 404, 22], [380, 27, 404, 25], [380, 29, 404, 27], [380, 30, 404, 28], [380, 31, 404, 29], [381, 6, 405, 4], [381, 12, 405, 10, "POLL_INTERVAL"], [381, 25, 405, 23], [381, 28, 405, 26], [381, 32, 405, 30], [381, 33, 405, 31], [381, 34, 405, 32], [383, 6, 407, 4, "console"], [383, 13, 407, 11], [383, 14, 407, 12, "log"], [383, 17, 407, 15], [383, 18, 407, 16], [383, 53, 407, 51, "attempts"], [383, 61, 407, 59], [383, 64, 407, 62], [383, 65, 407, 63], [383, 69, 407, 67, "MAX_ATTEMPTS"], [383, 81, 407, 79], [383, 93, 407, 91, "jobId"], [383, 98, 407, 96], [383, 100, 407, 98], [383, 101, 407, 99], [384, 6, 409, 4], [384, 10, 409, 8, "attempts"], [384, 18, 409, 16], [384, 22, 409, 20, "MAX_ATTEMPTS"], [384, 34, 409, 32], [384, 36, 409, 34], [385, 8, 410, 6, "console"], [385, 15, 410, 13], [385, 16, 410, 14, "error"], [385, 21, 410, 19], [385, 22, 410, 20], [385, 75, 410, 73], [385, 76, 410, 74], [386, 8, 411, 6, "setErrorMessage"], [386, 23, 411, 21], [386, 24, 411, 22], [386, 63, 411, 61], [386, 64, 411, 62], [387, 8, 412, 6, "setProcessingState"], [387, 26, 412, 24], [387, 27, 412, 25], [387, 34, 412, 32], [387, 35, 412, 33], [388, 8, 413, 6], [389, 6, 414, 4], [390, 6, 416, 4], [390, 10, 416, 8], [391, 8, 417, 6], [391, 14, 417, 12, "response"], [391, 22, 417, 20], [391, 25, 417, 23], [391, 31, 417, 29, "fetch"], [391, 36, 417, 34], [391, 37, 417, 35], [391, 40, 417, 38, "API_BASE_URL"], [391, 52, 417, 50], [391, 75, 417, 73, "jobId"], [391, 80, 417, 78], [391, 82, 417, 80], [391, 84, 417, 82], [392, 10, 418, 8, "headers"], [392, 17, 418, 15], [392, 19, 418, 17], [393, 12, 419, 10], [393, 27, 419, 25], [393, 29, 419, 27], [393, 39, 419, 37], [393, 45, 419, 43, "getAuthToken"], [393, 57, 419, 55], [393, 58, 419, 56], [393, 59, 419, 57], [394, 10, 420, 8], [395, 8, 421, 6], [395, 9, 421, 7], [395, 10, 421, 8], [396, 8, 423, 6], [396, 12, 423, 10], [396, 13, 423, 11, "response"], [396, 21, 423, 19], [396, 22, 423, 20, "ok"], [396, 24, 423, 22], [396, 26, 423, 24], [397, 10, 424, 8], [397, 16, 424, 14], [397, 20, 424, 18, "Error"], [397, 25, 424, 23], [397, 26, 424, 24], [397, 34, 424, 32, "response"], [397, 42, 424, 40], [397, 43, 424, 41, "status"], [397, 49, 424, 47], [397, 54, 424, 52, "response"], [397, 62, 424, 60], [397, 63, 424, 61, "statusText"], [397, 73, 424, 71], [397, 75, 424, 73], [397, 76, 424, 74], [398, 8, 425, 6], [399, 8, 427, 6], [399, 14, 427, 12, "status"], [399, 20, 427, 18], [399, 23, 427, 21], [399, 29, 427, 27, "response"], [399, 37, 427, 35], [399, 38, 427, 36, "json"], [399, 42, 427, 40], [399, 43, 427, 41], [399, 44, 427, 42], [400, 8, 428, 6, "console"], [400, 15, 428, 13], [400, 16, 428, 14, "log"], [400, 19, 428, 17], [400, 20, 428, 18], [400, 54, 428, 52], [400, 56, 428, 54, "status"], [400, 62, 428, 60], [400, 63, 428, 61], [401, 8, 430, 6], [401, 12, 430, 10, "status"], [401, 18, 430, 16], [401, 19, 430, 17, "status"], [401, 25, 430, 23], [401, 30, 430, 28], [401, 41, 430, 39], [401, 43, 430, 41], [402, 10, 431, 8, "console"], [402, 17, 431, 15], [402, 18, 431, 16, "log"], [402, 21, 431, 19], [402, 22, 431, 20], [402, 73, 431, 71], [402, 74, 431, 72], [403, 10, 432, 8, "setProcessingProgress"], [403, 31, 432, 29], [403, 32, 432, 30], [403, 35, 432, 33], [403, 36, 432, 34], [404, 10, 433, 8, "setProcessingState"], [404, 28, 433, 26], [404, 29, 433, 27], [404, 40, 433, 38], [404, 41, 433, 39], [405, 10, 434, 8], [406, 10, 435, 8], [406, 16, 435, 14, "result"], [406, 22, 435, 20], [406, 25, 435, 23], [407, 12, 436, 10, "imageUrl"], [407, 20, 436, 18], [407, 22, 436, 20, "status"], [407, 28, 436, 26], [407, 29, 436, 27, "publicUrl"], [407, 38, 436, 36], [408, 12, 436, 38], [409, 12, 437, 10, "localUri"], [409, 20, 437, 18], [409, 22, 437, 20, "capturedPhoto"], [409, 35, 437, 33], [409, 39, 437, 37, "status"], [409, 45, 437, 43], [409, 46, 437, 44, "publicUrl"], [409, 55, 437, 53], [410, 12, 437, 55], [411, 12, 438, 10, "challengeCode"], [411, 25, 438, 23], [411, 27, 438, 25, "challengeCode"], [411, 40, 438, 38], [411, 44, 438, 42], [411, 46, 438, 44], [412, 12, 439, 10, "timestamp"], [412, 21, 439, 19], [413, 12, 440, 10, "processingStatus"], [413, 28, 440, 26], [413, 30, 440, 28], [414, 10, 441, 8], [414, 11, 441, 9], [415, 10, 442, 8, "console"], [415, 17, 442, 15], [415, 18, 442, 16, "log"], [415, 21, 442, 19], [415, 22, 442, 20], [415, 57, 442, 55], [415, 59, 442, 57, "result"], [415, 65, 442, 63], [415, 66, 442, 64], [416, 10, 443, 8, "onComplete"], [416, 20, 443, 18], [416, 21, 443, 19, "result"], [416, 27, 443, 25], [416, 28, 443, 26], [417, 10, 444, 8], [418, 8, 445, 6], [418, 9, 445, 7], [418, 15, 445, 13], [418, 19, 445, 17, "status"], [418, 25, 445, 23], [418, 26, 445, 24, "status"], [418, 32, 445, 30], [418, 37, 445, 35], [418, 45, 445, 43], [418, 47, 445, 45], [419, 10, 446, 8, "console"], [419, 17, 446, 15], [419, 18, 446, 16, "error"], [419, 23, 446, 21], [419, 24, 446, 22], [419, 60, 446, 58], [419, 62, 446, 60, "status"], [419, 68, 446, 66], [419, 69, 446, 67, "error"], [419, 74, 446, 72], [419, 75, 446, 73], [420, 10, 447, 8], [420, 16, 447, 14], [420, 20, 447, 18, "Error"], [420, 25, 447, 23], [420, 26, 447, 24, "status"], [420, 32, 447, 30], [420, 33, 447, 31, "error"], [420, 38, 447, 36], [420, 42, 447, 40], [420, 61, 447, 59], [420, 62, 447, 60], [421, 8, 448, 6], [421, 9, 448, 7], [421, 15, 448, 13], [422, 10, 449, 8], [423, 10, 450, 8], [423, 16, 450, 14, "progressValue"], [423, 29, 450, 27], [423, 32, 450, 30], [423, 34, 450, 32], [423, 37, 450, 36, "attempts"], [423, 45, 450, 44], [423, 48, 450, 47, "MAX_ATTEMPTS"], [423, 60, 450, 59], [423, 63, 450, 63], [423, 65, 450, 65], [424, 10, 451, 8, "console"], [424, 17, 451, 15], [424, 18, 451, 16, "log"], [424, 21, 451, 19], [424, 22, 451, 20], [424, 71, 451, 69, "progressValue"], [424, 84, 451, 82], [424, 87, 451, 85], [424, 88, 451, 86], [425, 10, 452, 8, "setProcessingProgress"], [425, 31, 452, 29], [425, 32, 452, 30, "progressValue"], [425, 45, 452, 43], [425, 46, 452, 44], [426, 10, 454, 8, "setTimeout"], [426, 20, 454, 18], [426, 21, 454, 19], [426, 27, 454, 25], [427, 12, 455, 10, "pollForCompletion"], [427, 29, 455, 27], [427, 30, 455, 28, "jobId"], [427, 35, 455, 33], [427, 37, 455, 35, "timestamp"], [427, 46, 455, 44], [427, 48, 455, 46, "attempts"], [427, 56, 455, 54], [427, 59, 455, 57], [427, 60, 455, 58], [427, 61, 455, 59], [428, 10, 456, 8], [428, 11, 456, 9], [428, 13, 456, 11, "POLL_INTERVAL"], [428, 26, 456, 24], [428, 27, 456, 25], [429, 8, 457, 6], [430, 6, 458, 4], [430, 7, 458, 5], [430, 8, 458, 6], [430, 15, 458, 13, "error"], [430, 20, 458, 18], [430, 22, 458, 20], [431, 8, 459, 6, "console"], [431, 15, 459, 13], [431, 16, 459, 14, "error"], [431, 21, 459, 19], [431, 22, 459, 20], [431, 54, 459, 52], [431, 56, 459, 54, "error"], [431, 61, 459, 59], [431, 62, 459, 60], [432, 8, 460, 6, "setErrorMessage"], [432, 23, 460, 21], [432, 24, 460, 22], [432, 62, 460, 60, "error"], [432, 67, 460, 65], [432, 68, 460, 66, "message"], [432, 75, 460, 73], [432, 77, 460, 75], [432, 78, 460, 76], [433, 8, 461, 6, "setProcessingState"], [433, 26, 461, 24], [433, 27, 461, 25], [433, 34, 461, 32], [433, 35, 461, 33], [434, 6, 462, 4], [435, 4, 463, 2], [435, 5, 463, 3], [436, 4, 464, 2], [437, 4, 465, 2], [437, 10, 465, 8, "getAuthToken"], [437, 22, 465, 20], [437, 25, 465, 23], [437, 31, 465, 23, "getAuthToken"], [437, 32, 465, 23], [437, 37, 465, 52], [438, 6, 466, 4], [439, 6, 467, 4], [440, 6, 468, 4], [440, 13, 468, 11], [440, 30, 468, 28], [441, 4, 469, 2], [441, 5, 469, 3], [443, 4, 471, 2], [444, 4, 472, 2], [444, 10, 472, 8, "retryCapture"], [444, 22, 472, 20], [444, 25, 472, 23], [444, 29, 472, 23, "useCallback"], [444, 47, 472, 34], [444, 49, 472, 35], [444, 55, 472, 41], [445, 6, 473, 4, "console"], [445, 13, 473, 11], [445, 14, 473, 12, "log"], [445, 17, 473, 15], [445, 18, 473, 16], [445, 55, 473, 53], [445, 56, 473, 54], [446, 6, 474, 4, "setProcessingState"], [446, 24, 474, 22], [446, 25, 474, 23], [446, 31, 474, 29], [446, 32, 474, 30], [447, 6, 475, 4, "setErrorMessage"], [447, 21, 475, 19], [447, 22, 475, 20], [447, 24, 475, 22], [447, 25, 475, 23], [448, 6, 476, 4, "setCapturedPhoto"], [448, 22, 476, 20], [448, 23, 476, 21], [448, 25, 476, 23], [448, 26, 476, 24], [449, 6, 477, 4, "setProcessingProgress"], [449, 27, 477, 25], [449, 28, 477, 26], [449, 29, 477, 27], [449, 30, 477, 28], [450, 4, 478, 2], [450, 5, 478, 3], [450, 7, 478, 5], [450, 9, 478, 7], [450, 10, 478, 8], [451, 4, 479, 2], [452, 4, 480, 2], [452, 8, 480, 2, "useEffect"], [452, 24, 480, 11], [452, 26, 480, 12], [452, 32, 480, 18], [453, 6, 481, 4, "console"], [453, 13, 481, 11], [453, 14, 481, 12, "log"], [453, 17, 481, 15], [453, 18, 481, 16], [453, 53, 481, 51], [453, 55, 481, 53, "permission"], [453, 65, 481, 63], [453, 66, 481, 64], [454, 6, 482, 4], [454, 10, 482, 8, "permission"], [454, 20, 482, 18], [454, 22, 482, 20], [455, 8, 483, 6, "console"], [455, 15, 483, 13], [455, 16, 483, 14, "log"], [455, 19, 483, 17], [455, 20, 483, 18], [455, 57, 483, 55], [455, 59, 483, 57, "permission"], [455, 69, 483, 67], [455, 70, 483, 68, "granted"], [455, 77, 483, 75], [455, 78, 483, 76], [456, 6, 484, 4], [457, 4, 485, 2], [457, 5, 485, 3], [457, 7, 485, 5], [457, 8, 485, 6, "permission"], [457, 18, 485, 16], [457, 19, 485, 17], [457, 20, 485, 18], [458, 4, 486, 2], [459, 4, 487, 2], [459, 8, 487, 6], [459, 9, 487, 7, "permission"], [459, 19, 487, 17], [459, 21, 487, 19], [460, 6, 488, 4, "console"], [460, 13, 488, 11], [460, 14, 488, 12, "log"], [460, 17, 488, 15], [460, 18, 488, 16], [460, 67, 488, 65], [460, 68, 488, 66], [461, 6, 489, 4], [461, 26, 490, 6], [461, 30, 490, 6, "_jsxDevRuntime"], [461, 44, 490, 6], [461, 45, 490, 6, "jsxDEV"], [461, 51, 490, 6], [461, 53, 490, 7, "_View"], [461, 58, 490, 7], [461, 59, 490, 7, "default"], [461, 66, 490, 11], [462, 8, 490, 12, "style"], [462, 13, 490, 17], [462, 15, 490, 19, "styles"], [462, 21, 490, 25], [462, 22, 490, 26, "container"], [462, 31, 490, 36], [463, 8, 490, 36, "children"], [463, 16, 490, 36], [463, 32, 491, 8], [463, 36, 491, 8, "_jsxDevRuntime"], [463, 50, 491, 8], [463, 51, 491, 8, "jsxDEV"], [463, 57, 491, 8], [463, 59, 491, 9, "_ActivityIndicator"], [463, 77, 491, 9], [463, 78, 491, 9, "default"], [463, 85, 491, 26], [464, 10, 491, 27, "size"], [464, 14, 491, 31], [464, 16, 491, 32], [464, 23, 491, 39], [465, 10, 491, 40, "color"], [465, 15, 491, 45], [465, 17, 491, 46], [466, 8, 491, 55], [467, 10, 491, 55, "fileName"], [467, 18, 491, 55], [467, 20, 491, 55, "_jsxFileName"], [467, 32, 491, 55], [468, 10, 491, 55, "lineNumber"], [468, 20, 491, 55], [469, 10, 491, 55, "columnNumber"], [469, 22, 491, 55], [470, 8, 491, 55], [470, 15, 491, 57], [470, 16, 491, 58], [470, 31, 492, 8], [470, 35, 492, 8, "_jsxDevRuntime"], [470, 49, 492, 8], [470, 50, 492, 8, "jsxDEV"], [470, 56, 492, 8], [470, 58, 492, 9, "_Text"], [470, 63, 492, 9], [470, 64, 492, 9, "default"], [470, 71, 492, 13], [471, 10, 492, 14, "style"], [471, 15, 492, 19], [471, 17, 492, 21, "styles"], [471, 23, 492, 27], [471, 24, 492, 28, "loadingText"], [471, 35, 492, 40], [472, 10, 492, 40, "children"], [472, 18, 492, 40], [472, 20, 492, 41], [473, 8, 492, 58], [474, 10, 492, 58, "fileName"], [474, 18, 492, 58], [474, 20, 492, 58, "_jsxFileName"], [474, 32, 492, 58], [475, 10, 492, 58, "lineNumber"], [475, 20, 492, 58], [476, 10, 492, 58, "columnNumber"], [476, 22, 492, 58], [477, 8, 492, 58], [477, 15, 492, 64], [477, 16, 492, 65], [478, 6, 492, 65], [479, 8, 492, 65, "fileName"], [479, 16, 492, 65], [479, 18, 492, 65, "_jsxFileName"], [479, 30, 492, 65], [480, 8, 492, 65, "lineNumber"], [480, 18, 492, 65], [481, 8, 492, 65, "columnNumber"], [481, 20, 492, 65], [482, 6, 492, 65], [482, 13, 493, 12], [482, 14, 493, 13], [483, 4, 495, 2], [484, 4, 496, 2], [484, 8, 496, 6], [484, 9, 496, 7, "permission"], [484, 19, 496, 17], [484, 20, 496, 18, "granted"], [484, 27, 496, 25], [484, 29, 496, 27], [485, 6, 497, 4, "console"], [485, 13, 497, 11], [485, 14, 497, 12, "log"], [485, 17, 497, 15], [485, 18, 497, 16], [485, 93, 497, 91], [485, 94, 497, 92], [486, 6, 498, 4], [486, 26, 499, 6], [486, 30, 499, 6, "_jsxDevRuntime"], [486, 44, 499, 6], [486, 45, 499, 6, "jsxDEV"], [486, 51, 499, 6], [486, 53, 499, 7, "_View"], [486, 58, 499, 7], [486, 59, 499, 7, "default"], [486, 66, 499, 11], [487, 8, 499, 12, "style"], [487, 13, 499, 17], [487, 15, 499, 19, "styles"], [487, 21, 499, 25], [487, 22, 499, 26, "container"], [487, 31, 499, 36], [488, 8, 499, 36, "children"], [488, 16, 499, 36], [488, 31, 500, 8], [488, 35, 500, 8, "_jsxDevRuntime"], [488, 49, 500, 8], [488, 50, 500, 8, "jsxDEV"], [488, 56, 500, 8], [488, 58, 500, 9, "_View"], [488, 63, 500, 9], [488, 64, 500, 9, "default"], [488, 71, 500, 13], [489, 10, 500, 14, "style"], [489, 15, 500, 19], [489, 17, 500, 21, "styles"], [489, 23, 500, 27], [489, 24, 500, 28, "permissionContent"], [489, 41, 500, 46], [490, 10, 500, 46, "children"], [490, 18, 500, 46], [490, 34, 501, 10], [490, 38, 501, 10, "_jsxDevRuntime"], [490, 52, 501, 10], [490, 53, 501, 10, "jsxDEV"], [490, 59, 501, 10], [490, 61, 501, 11, "_lucideReactNative"], [490, 79, 501, 11], [490, 80, 501, 11, "Camera"], [490, 86, 501, 21], [491, 12, 501, 22, "size"], [491, 16, 501, 26], [491, 18, 501, 28], [491, 20, 501, 31], [492, 12, 501, 32, "color"], [492, 17, 501, 37], [492, 19, 501, 38], [493, 10, 501, 47], [494, 12, 501, 47, "fileName"], [494, 20, 501, 47], [494, 22, 501, 47, "_jsxFileName"], [494, 34, 501, 47], [495, 12, 501, 47, "lineNumber"], [495, 22, 501, 47], [496, 12, 501, 47, "columnNumber"], [496, 24, 501, 47], [497, 10, 501, 47], [497, 17, 501, 49], [497, 18, 501, 50], [497, 33, 502, 10], [497, 37, 502, 10, "_jsxDevRuntime"], [497, 51, 502, 10], [497, 52, 502, 10, "jsxDEV"], [497, 58, 502, 10], [497, 60, 502, 11, "_Text"], [497, 65, 502, 11], [497, 66, 502, 11, "default"], [497, 73, 502, 15], [498, 12, 502, 16, "style"], [498, 17, 502, 21], [498, 19, 502, 23, "styles"], [498, 25, 502, 29], [498, 26, 502, 30, "permissionTitle"], [498, 41, 502, 46], [499, 12, 502, 46, "children"], [499, 20, 502, 46], [499, 22, 502, 47], [500, 10, 502, 73], [501, 12, 502, 73, "fileName"], [501, 20, 502, 73], [501, 22, 502, 73, "_jsxFileName"], [501, 34, 502, 73], [502, 12, 502, 73, "lineNumber"], [502, 22, 502, 73], [503, 12, 502, 73, "columnNumber"], [503, 24, 502, 73], [504, 10, 502, 73], [504, 17, 502, 79], [504, 18, 502, 80], [504, 33, 503, 10], [504, 37, 503, 10, "_jsxDevRuntime"], [504, 51, 503, 10], [504, 52, 503, 10, "jsxDEV"], [504, 58, 503, 10], [504, 60, 503, 11, "_Text"], [504, 65, 503, 11], [504, 66, 503, 11, "default"], [504, 73, 503, 15], [505, 12, 503, 16, "style"], [505, 17, 503, 21], [505, 19, 503, 23, "styles"], [505, 25, 503, 29], [505, 26, 503, 30, "permissionDescription"], [505, 47, 503, 52], [506, 12, 503, 52, "children"], [506, 20, 503, 52], [506, 22, 503, 53], [507, 10, 506, 10], [508, 12, 506, 10, "fileName"], [508, 20, 506, 10], [508, 22, 506, 10, "_jsxFileName"], [508, 34, 506, 10], [509, 12, 506, 10, "lineNumber"], [509, 22, 506, 10], [510, 12, 506, 10, "columnNumber"], [510, 24, 506, 10], [511, 10, 506, 10], [511, 17, 506, 16], [511, 18, 506, 17], [511, 33, 507, 10], [511, 37, 507, 10, "_jsxDevRuntime"], [511, 51, 507, 10], [511, 52, 507, 10, "jsxDEV"], [511, 58, 507, 10], [511, 60, 507, 11, "_TouchableOpacity"], [511, 77, 507, 11], [511, 78, 507, 11, "default"], [511, 85, 507, 27], [512, 12, 507, 28, "onPress"], [512, 19, 507, 35], [512, 21, 507, 37, "requestPermission"], [512, 38, 507, 55], [513, 12, 507, 56, "style"], [513, 17, 507, 61], [513, 19, 507, 63, "styles"], [513, 25, 507, 69], [513, 26, 507, 70, "primaryButton"], [513, 39, 507, 84], [514, 12, 507, 84, "children"], [514, 20, 507, 84], [514, 35, 508, 12], [514, 39, 508, 12, "_jsxDevRuntime"], [514, 53, 508, 12], [514, 54, 508, 12, "jsxDEV"], [514, 60, 508, 12], [514, 62, 508, 13, "_Text"], [514, 67, 508, 13], [514, 68, 508, 13, "default"], [514, 75, 508, 17], [515, 14, 508, 18, "style"], [515, 19, 508, 23], [515, 21, 508, 25, "styles"], [515, 27, 508, 31], [515, 28, 508, 32, "primaryButtonText"], [515, 45, 508, 50], [516, 14, 508, 50, "children"], [516, 22, 508, 50], [516, 24, 508, 51], [517, 12, 508, 67], [518, 14, 508, 67, "fileName"], [518, 22, 508, 67], [518, 24, 508, 67, "_jsxFileName"], [518, 36, 508, 67], [519, 14, 508, 67, "lineNumber"], [519, 24, 508, 67], [520, 14, 508, 67, "columnNumber"], [520, 26, 508, 67], [521, 12, 508, 67], [521, 19, 508, 73], [522, 10, 508, 74], [523, 12, 508, 74, "fileName"], [523, 20, 508, 74], [523, 22, 508, 74, "_jsxFileName"], [523, 34, 508, 74], [524, 12, 508, 74, "lineNumber"], [524, 22, 508, 74], [525, 12, 508, 74, "columnNumber"], [525, 24, 508, 74], [526, 10, 508, 74], [526, 17, 509, 28], [526, 18, 509, 29], [526, 33, 510, 10], [526, 37, 510, 10, "_jsxDevRuntime"], [526, 51, 510, 10], [526, 52, 510, 10, "jsxDEV"], [526, 58, 510, 10], [526, 60, 510, 11, "_TouchableOpacity"], [526, 77, 510, 11], [526, 78, 510, 11, "default"], [526, 85, 510, 27], [527, 12, 510, 28, "onPress"], [527, 19, 510, 35], [527, 21, 510, 37, "onCancel"], [527, 29, 510, 46], [528, 12, 510, 47, "style"], [528, 17, 510, 52], [528, 19, 510, 54, "styles"], [528, 25, 510, 60], [528, 26, 510, 61, "secondaryButton"], [528, 41, 510, 77], [529, 12, 510, 77, "children"], [529, 20, 510, 77], [529, 35, 511, 12], [529, 39, 511, 12, "_jsxDevRuntime"], [529, 53, 511, 12], [529, 54, 511, 12, "jsxDEV"], [529, 60, 511, 12], [529, 62, 511, 13, "_Text"], [529, 67, 511, 13], [529, 68, 511, 13, "default"], [529, 75, 511, 17], [530, 14, 511, 18, "style"], [530, 19, 511, 23], [530, 21, 511, 25, "styles"], [530, 27, 511, 31], [530, 28, 511, 32, "secondaryButtonText"], [530, 47, 511, 52], [531, 14, 511, 52, "children"], [531, 22, 511, 52], [531, 24, 511, 53], [532, 12, 511, 59], [533, 14, 511, 59, "fileName"], [533, 22, 511, 59], [533, 24, 511, 59, "_jsxFileName"], [533, 36, 511, 59], [534, 14, 511, 59, "lineNumber"], [534, 24, 511, 59], [535, 14, 511, 59, "columnNumber"], [535, 26, 511, 59], [536, 12, 511, 59], [536, 19, 511, 65], [537, 10, 511, 66], [538, 12, 511, 66, "fileName"], [538, 20, 511, 66], [538, 22, 511, 66, "_jsxFileName"], [538, 34, 511, 66], [539, 12, 511, 66, "lineNumber"], [539, 22, 511, 66], [540, 12, 511, 66, "columnNumber"], [540, 24, 511, 66], [541, 10, 511, 66], [541, 17, 512, 28], [541, 18, 512, 29], [542, 8, 512, 29], [543, 10, 512, 29, "fileName"], [543, 18, 512, 29], [543, 20, 512, 29, "_jsxFileName"], [543, 32, 512, 29], [544, 10, 512, 29, "lineNumber"], [544, 20, 512, 29], [545, 10, 512, 29, "columnNumber"], [545, 22, 512, 29], [546, 8, 512, 29], [546, 15, 513, 14], [547, 6, 513, 15], [548, 8, 513, 15, "fileName"], [548, 16, 513, 15], [548, 18, 513, 15, "_jsxFileName"], [548, 30, 513, 15], [549, 8, 513, 15, "lineNumber"], [549, 18, 513, 15], [550, 8, 513, 15, "columnNumber"], [550, 20, 513, 15], [551, 6, 513, 15], [551, 13, 514, 12], [551, 14, 514, 13], [552, 4, 516, 2], [553, 4, 517, 2], [554, 4, 518, 2, "console"], [554, 11, 518, 9], [554, 12, 518, 10, "log"], [554, 15, 518, 13], [554, 16, 518, 14], [554, 55, 518, 53], [554, 56, 518, 54], [555, 4, 520, 2], [555, 24, 521, 4], [555, 28, 521, 4, "_jsxDevRuntime"], [555, 42, 521, 4], [555, 43, 521, 4, "jsxDEV"], [555, 49, 521, 4], [555, 51, 521, 5, "_View"], [555, 56, 521, 5], [555, 57, 521, 5, "default"], [555, 64, 521, 9], [556, 6, 521, 10, "style"], [556, 11, 521, 15], [556, 13, 521, 17, "styles"], [556, 19, 521, 23], [556, 20, 521, 24, "container"], [556, 29, 521, 34], [557, 6, 521, 34, "children"], [557, 14, 521, 34], [557, 30, 523, 6], [557, 34, 523, 6, "_jsxDevRuntime"], [557, 48, 523, 6], [557, 49, 523, 6, "jsxDEV"], [557, 55, 523, 6], [557, 57, 523, 7, "_View"], [557, 62, 523, 7], [557, 63, 523, 7, "default"], [557, 70, 523, 11], [558, 8, 523, 12, "style"], [558, 13, 523, 17], [558, 15, 523, 19, "styles"], [558, 21, 523, 25], [558, 22, 523, 26, "cameraContainer"], [558, 37, 523, 42], [559, 8, 523, 43, "id"], [559, 10, 523, 45], [559, 12, 523, 46], [559, 29, 523, 63], [560, 8, 523, 63, "children"], [560, 16, 523, 63], [560, 32, 524, 8], [560, 36, 524, 8, "_jsxDevRuntime"], [560, 50, 524, 8], [560, 51, 524, 8, "jsxDEV"], [560, 57, 524, 8], [560, 59, 524, 9, "_expoCamera"], [560, 70, 524, 9], [560, 71, 524, 9, "CameraView"], [560, 81, 524, 19], [561, 10, 525, 10, "ref"], [561, 13, 525, 13], [561, 15, 525, 15, "cameraRef"], [561, 24, 525, 25], [562, 10, 526, 10, "style"], [562, 15, 526, 15], [562, 17, 526, 17], [562, 18, 526, 18, "styles"], [562, 24, 526, 24], [562, 25, 526, 25, "camera"], [562, 31, 526, 31], [562, 33, 526, 33], [563, 12, 526, 35, "backgroundColor"], [563, 27, 526, 50], [563, 29, 526, 52], [564, 10, 526, 62], [564, 11, 526, 63], [564, 12, 526, 65], [565, 10, 527, 10, "facing"], [565, 16, 527, 16], [565, 18, 527, 17], [565, 24, 527, 23], [566, 10, 528, 10, "onLayout"], [566, 18, 528, 18], [566, 20, 528, 21, "e"], [566, 21, 528, 22], [566, 25, 528, 27], [567, 12, 529, 12, "console"], [567, 19, 529, 19], [567, 20, 529, 20, "log"], [567, 23, 529, 23], [567, 24, 529, 24], [567, 56, 529, 56], [567, 58, 529, 58, "e"], [567, 59, 529, 59], [567, 60, 529, 60, "nativeEvent"], [567, 71, 529, 71], [567, 72, 529, 72, "layout"], [567, 78, 529, 78], [567, 79, 529, 79], [568, 12, 530, 12, "setViewSize"], [568, 23, 530, 23], [568, 24, 530, 24], [569, 14, 530, 26, "width"], [569, 19, 530, 31], [569, 21, 530, 33, "e"], [569, 22, 530, 34], [569, 23, 530, 35, "nativeEvent"], [569, 34, 530, 46], [569, 35, 530, 47, "layout"], [569, 41, 530, 53], [569, 42, 530, 54, "width"], [569, 47, 530, 59], [570, 14, 530, 61, "height"], [570, 20, 530, 67], [570, 22, 530, 69, "e"], [570, 23, 530, 70], [570, 24, 530, 71, "nativeEvent"], [570, 35, 530, 82], [570, 36, 530, 83, "layout"], [570, 42, 530, 89], [570, 43, 530, 90, "height"], [571, 12, 530, 97], [571, 13, 530, 98], [571, 14, 530, 99], [572, 10, 531, 10], [572, 11, 531, 12], [573, 10, 532, 10, "onCameraReady"], [573, 23, 532, 23], [573, 25, 532, 25, "onCameraReady"], [573, 26, 532, 25], [573, 31, 532, 31], [574, 12, 533, 12, "console"], [574, 19, 533, 19], [574, 20, 533, 20, "log"], [574, 23, 533, 23], [574, 24, 533, 24], [574, 55, 533, 55], [574, 56, 533, 56], [575, 12, 534, 12, "setIsCameraReady"], [575, 28, 534, 28], [575, 29, 534, 29], [575, 33, 534, 33], [575, 34, 534, 34], [575, 35, 534, 35], [575, 36, 534, 36], [576, 10, 535, 10], [576, 11, 535, 12], [577, 10, 536, 10, "onMountError"], [577, 22, 536, 22], [577, 24, 536, 25, "error"], [577, 29, 536, 30], [577, 33, 536, 35], [578, 12, 537, 12, "console"], [578, 19, 537, 19], [578, 20, 537, 20, "error"], [578, 25, 537, 25], [578, 26, 537, 26], [578, 63, 537, 63], [578, 65, 537, 65, "error"], [578, 70, 537, 70], [578, 71, 537, 71], [579, 12, 538, 12, "setErrorMessage"], [579, 27, 538, 27], [579, 28, 538, 28], [579, 57, 538, 57], [579, 58, 538, 58], [580, 12, 539, 12, "setProcessingState"], [580, 30, 539, 30], [580, 31, 539, 31], [580, 38, 539, 38], [580, 39, 539, 39], [581, 10, 540, 10], [582, 8, 540, 12], [583, 10, 540, 12, "fileName"], [583, 18, 540, 12], [583, 20, 540, 12, "_jsxFileName"], [583, 32, 540, 12], [584, 10, 540, 12, "lineNumber"], [584, 20, 540, 12], [585, 10, 540, 12, "columnNumber"], [585, 22, 540, 12], [586, 8, 540, 12], [586, 15, 541, 9], [586, 16, 541, 10], [586, 18, 543, 9], [586, 19, 543, 10, "isCameraReady"], [586, 32, 543, 23], [586, 49, 544, 10], [586, 53, 544, 10, "_jsxDevRuntime"], [586, 67, 544, 10], [586, 68, 544, 10, "jsxDEV"], [586, 74, 544, 10], [586, 76, 544, 11, "_View"], [586, 81, 544, 11], [586, 82, 544, 11, "default"], [586, 89, 544, 15], [587, 10, 544, 16, "style"], [587, 15, 544, 21], [587, 17, 544, 23], [587, 18, 544, 24, "StyleSheet"], [587, 37, 544, 34], [587, 38, 544, 35, "absoluteFill"], [587, 50, 544, 47], [587, 52, 544, 49], [588, 12, 544, 51, "backgroundColor"], [588, 27, 544, 66], [588, 29, 544, 68], [588, 49, 544, 88], [589, 12, 544, 90, "justifyContent"], [589, 26, 544, 104], [589, 28, 544, 106], [589, 36, 544, 114], [590, 12, 544, 116, "alignItems"], [590, 22, 544, 126], [590, 24, 544, 128], [590, 32, 544, 136], [591, 12, 544, 138, "zIndex"], [591, 18, 544, 144], [591, 20, 544, 146], [592, 10, 544, 151], [592, 11, 544, 152], [592, 12, 544, 154], [593, 10, 544, 154, "children"], [593, 18, 544, 154], [593, 33, 545, 12], [593, 37, 545, 12, "_jsxDevRuntime"], [593, 51, 545, 12], [593, 52, 545, 12, "jsxDEV"], [593, 58, 545, 12], [593, 60, 545, 13, "_View"], [593, 65, 545, 13], [593, 66, 545, 13, "default"], [593, 73, 545, 17], [594, 12, 545, 18, "style"], [594, 17, 545, 23], [594, 19, 545, 25], [595, 14, 545, 27, "backgroundColor"], [595, 29, 545, 42], [595, 31, 545, 44], [595, 51, 545, 64], [596, 14, 545, 66, "padding"], [596, 21, 545, 73], [596, 23, 545, 75], [596, 25, 545, 77], [597, 14, 545, 79, "borderRadius"], [597, 26, 545, 91], [597, 28, 545, 93], [597, 30, 545, 95], [598, 14, 545, 97, "alignItems"], [598, 24, 545, 107], [598, 26, 545, 109], [599, 12, 545, 118], [599, 13, 545, 120], [600, 12, 545, 120, "children"], [600, 20, 545, 120], [600, 36, 546, 14], [600, 40, 546, 14, "_jsxDevRuntime"], [600, 54, 546, 14], [600, 55, 546, 14, "jsxDEV"], [600, 61, 546, 14], [600, 63, 546, 15, "_ActivityIndicator"], [600, 81, 546, 15], [600, 82, 546, 15, "default"], [600, 89, 546, 32], [601, 14, 546, 33, "size"], [601, 18, 546, 37], [601, 20, 546, 38], [601, 27, 546, 45], [602, 14, 546, 46, "color"], [602, 19, 546, 51], [602, 21, 546, 52], [602, 30, 546, 61], [603, 14, 546, 62, "style"], [603, 19, 546, 67], [603, 21, 546, 69], [604, 16, 546, 71, "marginBottom"], [604, 28, 546, 83], [604, 30, 546, 85], [605, 14, 546, 88], [606, 12, 546, 90], [607, 14, 546, 90, "fileName"], [607, 22, 546, 90], [607, 24, 546, 90, "_jsxFileName"], [607, 36, 546, 90], [608, 14, 546, 90, "lineNumber"], [608, 24, 546, 90], [609, 14, 546, 90, "columnNumber"], [609, 26, 546, 90], [610, 12, 546, 90], [610, 19, 546, 92], [610, 20, 546, 93], [610, 35, 547, 14], [610, 39, 547, 14, "_jsxDevRuntime"], [610, 53, 547, 14], [610, 54, 547, 14, "jsxDEV"], [610, 60, 547, 14], [610, 62, 547, 15, "_Text"], [610, 67, 547, 15], [610, 68, 547, 15, "default"], [610, 75, 547, 19], [611, 14, 547, 20, "style"], [611, 19, 547, 25], [611, 21, 547, 27], [612, 16, 547, 29, "color"], [612, 21, 547, 34], [612, 23, 547, 36], [612, 29, 547, 42], [613, 16, 547, 44, "fontSize"], [613, 24, 547, 52], [613, 26, 547, 54], [613, 28, 547, 56], [614, 16, 547, 58, "fontWeight"], [614, 26, 547, 68], [614, 28, 547, 70], [615, 14, 547, 76], [615, 15, 547, 78], [616, 14, 547, 78, "children"], [616, 22, 547, 78], [616, 24, 547, 79], [617, 12, 547, 101], [618, 14, 547, 101, "fileName"], [618, 22, 547, 101], [618, 24, 547, 101, "_jsxFileName"], [618, 36, 547, 101], [619, 14, 547, 101, "lineNumber"], [619, 24, 547, 101], [620, 14, 547, 101, "columnNumber"], [620, 26, 547, 101], [621, 12, 547, 101], [621, 19, 547, 107], [621, 20, 547, 108], [621, 35, 548, 14], [621, 39, 548, 14, "_jsxDevRuntime"], [621, 53, 548, 14], [621, 54, 548, 14, "jsxDEV"], [621, 60, 548, 14], [621, 62, 548, 15, "_Text"], [621, 67, 548, 15], [621, 68, 548, 15, "default"], [621, 75, 548, 19], [622, 14, 548, 20, "style"], [622, 19, 548, 25], [622, 21, 548, 27], [623, 16, 548, 29, "color"], [623, 21, 548, 34], [623, 23, 548, 36], [623, 32, 548, 45], [624, 16, 548, 47, "fontSize"], [624, 24, 548, 55], [624, 26, 548, 57], [624, 28, 548, 59], [625, 16, 548, 61, "marginTop"], [625, 25, 548, 70], [625, 27, 548, 72], [626, 14, 548, 74], [626, 15, 548, 76], [627, 14, 548, 76, "children"], [627, 22, 548, 76], [627, 24, 548, 77], [628, 12, 548, 88], [629, 14, 548, 88, "fileName"], [629, 22, 548, 88], [629, 24, 548, 88, "_jsxFileName"], [629, 36, 548, 88], [630, 14, 548, 88, "lineNumber"], [630, 24, 548, 88], [631, 14, 548, 88, "columnNumber"], [631, 26, 548, 88], [632, 12, 548, 88], [632, 19, 548, 94], [632, 20, 548, 95], [633, 10, 548, 95], [634, 12, 548, 95, "fileName"], [634, 20, 548, 95], [634, 22, 548, 95, "_jsxFileName"], [634, 34, 548, 95], [635, 12, 548, 95, "lineNumber"], [635, 22, 548, 95], [636, 12, 548, 95, "columnNumber"], [636, 24, 548, 95], [637, 10, 548, 95], [637, 17, 549, 18], [638, 8, 549, 19], [639, 10, 549, 19, "fileName"], [639, 18, 549, 19], [639, 20, 549, 19, "_jsxFileName"], [639, 32, 549, 19], [640, 10, 549, 19, "lineNumber"], [640, 20, 549, 19], [641, 10, 549, 19, "columnNumber"], [641, 22, 549, 19], [642, 8, 549, 19], [642, 15, 550, 16], [642, 16, 551, 9], [642, 18, 554, 9, "isCameraReady"], [642, 31, 554, 22], [642, 35, 554, 26, "previewBlurEnabled"], [642, 53, 554, 44], [642, 57, 554, 48, "viewSize"], [642, 65, 554, 56], [642, 66, 554, 57, "width"], [642, 71, 554, 62], [642, 74, 554, 65], [642, 75, 554, 66], [642, 92, 555, 10], [642, 96, 555, 10, "_jsxDevRuntime"], [642, 110, 555, 10], [642, 111, 555, 10, "jsxDEV"], [642, 117, 555, 10], [642, 119, 555, 10, "_jsxDevRuntime"], [642, 133, 555, 10], [642, 134, 555, 10, "Fragment"], [642, 142, 555, 10], [643, 10, 555, 10, "children"], [643, 18, 555, 10], [643, 34, 557, 12], [643, 38, 557, 12, "_jsxDevRuntime"], [643, 52, 557, 12], [643, 53, 557, 12, "jsxDEV"], [643, 59, 557, 12], [643, 61, 557, 13, "_LiveFaceCanvas"], [643, 76, 557, 13], [643, 77, 557, 13, "default"], [643, 84, 557, 27], [644, 12, 557, 28, "containerId"], [644, 23, 557, 39], [644, 25, 557, 40], [644, 42, 557, 57], [645, 12, 557, 58, "width"], [645, 17, 557, 63], [645, 19, 557, 65, "viewSize"], [645, 27, 557, 73], [645, 28, 557, 74, "width"], [645, 33, 557, 80], [646, 12, 557, 81, "height"], [646, 18, 557, 87], [646, 20, 557, 89, "viewSize"], [646, 28, 557, 97], [646, 29, 557, 98, "height"], [647, 10, 557, 105], [648, 12, 557, 105, "fileName"], [648, 20, 557, 105], [648, 22, 557, 105, "_jsxFileName"], [648, 34, 557, 105], [649, 12, 557, 105, "lineNumber"], [649, 22, 557, 105], [650, 12, 557, 105, "columnNumber"], [650, 24, 557, 105], [651, 10, 557, 105], [651, 17, 557, 107], [651, 18, 557, 108], [651, 33, 558, 12], [651, 37, 558, 12, "_jsxDevRuntime"], [651, 51, 558, 12], [651, 52, 558, 12, "jsxDEV"], [651, 58, 558, 12], [651, 60, 558, 13, "_View"], [651, 65, 558, 13], [651, 66, 558, 13, "default"], [651, 73, 558, 17], [652, 12, 558, 18, "style"], [652, 17, 558, 23], [652, 19, 558, 25], [652, 20, 558, 26, "StyleSheet"], [652, 39, 558, 36], [652, 40, 558, 37, "absoluteFill"], [652, 52, 558, 49], [652, 54, 558, 51], [653, 14, 558, 53, "pointerEvents"], [653, 27, 558, 66], [653, 29, 558, 68], [654, 12, 558, 75], [654, 13, 558, 76], [654, 14, 558, 78], [655, 12, 558, 78, "children"], [655, 20, 558, 78], [655, 36, 560, 12], [655, 40, 560, 12, "_jsxDevRuntime"], [655, 54, 560, 12], [655, 55, 560, 12, "jsxDEV"], [655, 61, 560, 12], [655, 63, 560, 13, "_expoBlur"], [655, 72, 560, 13], [655, 73, 560, 13, "BlurView"], [655, 81, 560, 21], [656, 14, 560, 22, "intensity"], [656, 23, 560, 31], [656, 25, 560, 33], [656, 27, 560, 36], [657, 14, 560, 37, "tint"], [657, 18, 560, 41], [657, 20, 560, 42], [657, 26, 560, 48], [658, 14, 560, 49, "style"], [658, 19, 560, 54], [658, 21, 560, 56], [658, 22, 560, 57, "styles"], [658, 28, 560, 63], [658, 29, 560, 64, "blurZone"], [658, 37, 560, 72], [658, 39, 560, 74], [659, 16, 561, 14, "left"], [659, 20, 561, 18], [659, 22, 561, 20], [659, 23, 561, 21], [660, 16, 562, 14, "top"], [660, 19, 562, 17], [660, 21, 562, 19, "viewSize"], [660, 29, 562, 27], [660, 30, 562, 28, "height"], [660, 36, 562, 34], [660, 39, 562, 37], [660, 42, 562, 40], [661, 16, 563, 14, "width"], [661, 21, 563, 19], [661, 23, 563, 21, "viewSize"], [661, 31, 563, 29], [661, 32, 563, 30, "width"], [661, 37, 563, 35], [662, 16, 564, 14, "height"], [662, 22, 564, 20], [662, 24, 564, 22, "viewSize"], [662, 32, 564, 30], [662, 33, 564, 31, "height"], [662, 39, 564, 37], [662, 42, 564, 40], [662, 46, 564, 44], [663, 16, 565, 14, "borderRadius"], [663, 28, 565, 26], [663, 30, 565, 28], [664, 14, 566, 12], [664, 15, 566, 13], [665, 12, 566, 15], [666, 14, 566, 15, "fileName"], [666, 22, 566, 15], [666, 24, 566, 15, "_jsxFileName"], [666, 36, 566, 15], [667, 14, 566, 15, "lineNumber"], [667, 24, 566, 15], [668, 14, 566, 15, "columnNumber"], [668, 26, 566, 15], [669, 12, 566, 15], [669, 19, 566, 17], [669, 20, 566, 18], [669, 35, 568, 12], [669, 39, 568, 12, "_jsxDevRuntime"], [669, 53, 568, 12], [669, 54, 568, 12, "jsxDEV"], [669, 60, 568, 12], [669, 62, 568, 13, "_expoBlur"], [669, 71, 568, 13], [669, 72, 568, 13, "BlurView"], [669, 80, 568, 21], [670, 14, 568, 22, "intensity"], [670, 23, 568, 31], [670, 25, 568, 33], [670, 27, 568, 36], [671, 14, 568, 37, "tint"], [671, 18, 568, 41], [671, 20, 568, 42], [671, 26, 568, 48], [672, 14, 568, 49, "style"], [672, 19, 568, 54], [672, 21, 568, 56], [672, 22, 568, 57, "styles"], [672, 28, 568, 63], [672, 29, 568, 64, "blurZone"], [672, 37, 568, 72], [672, 39, 568, 74], [673, 16, 569, 14, "left"], [673, 20, 569, 18], [673, 22, 569, 20], [673, 23, 569, 21], [674, 16, 570, 14, "top"], [674, 19, 570, 17], [674, 21, 570, 19], [674, 22, 570, 20], [675, 16, 571, 14, "width"], [675, 21, 571, 19], [675, 23, 571, 21, "viewSize"], [675, 31, 571, 29], [675, 32, 571, 30, "width"], [675, 37, 571, 35], [676, 16, 572, 14, "height"], [676, 22, 572, 20], [676, 24, 572, 22, "viewSize"], [676, 32, 572, 30], [676, 33, 572, 31, "height"], [676, 39, 572, 37], [676, 42, 572, 40], [676, 45, 572, 43], [677, 16, 573, 14, "borderRadius"], [677, 28, 573, 26], [677, 30, 573, 28], [678, 14, 574, 12], [678, 15, 574, 13], [679, 12, 574, 15], [680, 14, 574, 15, "fileName"], [680, 22, 574, 15], [680, 24, 574, 15, "_jsxFileName"], [680, 36, 574, 15], [681, 14, 574, 15, "lineNumber"], [681, 24, 574, 15], [682, 14, 574, 15, "columnNumber"], [682, 26, 574, 15], [683, 12, 574, 15], [683, 19, 574, 17], [683, 20, 574, 18], [683, 35, 576, 12], [683, 39, 576, 12, "_jsxDevRuntime"], [683, 53, 576, 12], [683, 54, 576, 12, "jsxDEV"], [683, 60, 576, 12], [683, 62, 576, 13, "_expoBlur"], [683, 71, 576, 13], [683, 72, 576, 13, "BlurView"], [683, 80, 576, 21], [684, 14, 576, 22, "intensity"], [684, 23, 576, 31], [684, 25, 576, 33], [684, 27, 576, 36], [685, 14, 576, 37, "tint"], [685, 18, 576, 41], [685, 20, 576, 42], [685, 26, 576, 48], [686, 14, 576, 49, "style"], [686, 19, 576, 54], [686, 21, 576, 56], [686, 22, 576, 57, "styles"], [686, 28, 576, 63], [686, 29, 576, 64, "blurZone"], [686, 37, 576, 72], [686, 39, 576, 74], [687, 16, 577, 14, "left"], [687, 20, 577, 18], [687, 22, 577, 20, "viewSize"], [687, 30, 577, 28], [687, 31, 577, 29, "width"], [687, 36, 577, 34], [687, 39, 577, 37], [687, 42, 577, 40], [687, 45, 577, 44, "viewSize"], [687, 53, 577, 52], [687, 54, 577, 53, "width"], [687, 59, 577, 58], [687, 62, 577, 61], [687, 66, 577, 66], [688, 16, 578, 14, "top"], [688, 19, 578, 17], [688, 21, 578, 19, "viewSize"], [688, 29, 578, 27], [688, 30, 578, 28, "height"], [688, 36, 578, 34], [688, 39, 578, 37], [688, 43, 578, 41], [688, 46, 578, 45, "viewSize"], [688, 54, 578, 53], [688, 55, 578, 54, "width"], [688, 60, 578, 59], [688, 63, 578, 62], [688, 67, 578, 67], [689, 16, 579, 14, "width"], [689, 21, 579, 19], [689, 23, 579, 21, "viewSize"], [689, 31, 579, 29], [689, 32, 579, 30, "width"], [689, 37, 579, 35], [689, 40, 579, 38], [689, 43, 579, 41], [690, 16, 580, 14, "height"], [690, 22, 580, 20], [690, 24, 580, 22, "viewSize"], [690, 32, 580, 30], [690, 33, 580, 31, "width"], [690, 38, 580, 36], [690, 41, 580, 39], [690, 44, 580, 42], [691, 16, 581, 14, "borderRadius"], [691, 28, 581, 26], [691, 30, 581, 29, "viewSize"], [691, 38, 581, 37], [691, 39, 581, 38, "width"], [691, 44, 581, 43], [691, 47, 581, 46], [691, 50, 581, 49], [691, 53, 581, 53], [692, 14, 582, 12], [692, 15, 582, 13], [693, 12, 582, 15], [694, 14, 582, 15, "fileName"], [694, 22, 582, 15], [694, 24, 582, 15, "_jsxFileName"], [694, 36, 582, 15], [695, 14, 582, 15, "lineNumber"], [695, 24, 582, 15], [696, 14, 582, 15, "columnNumber"], [696, 26, 582, 15], [697, 12, 582, 15], [697, 19, 582, 17], [697, 20, 582, 18], [697, 35, 583, 12], [697, 39, 583, 12, "_jsxDevRuntime"], [697, 53, 583, 12], [697, 54, 583, 12, "jsxDEV"], [697, 60, 583, 12], [697, 62, 583, 13, "_expoBlur"], [697, 71, 583, 13], [697, 72, 583, 13, "BlurView"], [697, 80, 583, 21], [698, 14, 583, 22, "intensity"], [698, 23, 583, 31], [698, 25, 583, 33], [698, 27, 583, 36], [699, 14, 583, 37, "tint"], [699, 18, 583, 41], [699, 20, 583, 42], [699, 26, 583, 48], [700, 14, 583, 49, "style"], [700, 19, 583, 54], [700, 21, 583, 56], [700, 22, 583, 57, "styles"], [700, 28, 583, 63], [700, 29, 583, 64, "blurZone"], [700, 37, 583, 72], [700, 39, 583, 74], [701, 16, 584, 14, "left"], [701, 20, 584, 18], [701, 22, 584, 20, "viewSize"], [701, 30, 584, 28], [701, 31, 584, 29, "width"], [701, 36, 584, 34], [701, 39, 584, 37], [701, 42, 584, 40], [701, 45, 584, 44, "viewSize"], [701, 53, 584, 52], [701, 54, 584, 53, "width"], [701, 59, 584, 58], [701, 62, 584, 61], [701, 66, 584, 66], [702, 16, 585, 14, "top"], [702, 19, 585, 17], [702, 21, 585, 19, "viewSize"], [702, 29, 585, 27], [702, 30, 585, 28, "height"], [702, 36, 585, 34], [702, 39, 585, 37], [702, 42, 585, 40], [702, 45, 585, 44, "viewSize"], [702, 53, 585, 52], [702, 54, 585, 53, "width"], [702, 59, 585, 58], [702, 62, 585, 61], [702, 66, 585, 66], [703, 16, 586, 14, "width"], [703, 21, 586, 19], [703, 23, 586, 21, "viewSize"], [703, 31, 586, 29], [703, 32, 586, 30, "width"], [703, 37, 586, 35], [703, 40, 586, 38], [703, 43, 586, 41], [704, 16, 587, 14, "height"], [704, 22, 587, 20], [704, 24, 587, 22, "viewSize"], [704, 32, 587, 30], [704, 33, 587, 31, "width"], [704, 38, 587, 36], [704, 41, 587, 39], [704, 44, 587, 42], [705, 16, 588, 14, "borderRadius"], [705, 28, 588, 26], [705, 30, 588, 29, "viewSize"], [705, 38, 588, 37], [705, 39, 588, 38, "width"], [705, 44, 588, 43], [705, 47, 588, 46], [705, 50, 588, 49], [705, 53, 588, 53], [706, 14, 589, 12], [706, 15, 589, 13], [707, 12, 589, 15], [708, 14, 589, 15, "fileName"], [708, 22, 589, 15], [708, 24, 589, 15, "_jsxFileName"], [708, 36, 589, 15], [709, 14, 589, 15, "lineNumber"], [709, 24, 589, 15], [710, 14, 589, 15, "columnNumber"], [710, 26, 589, 15], [711, 12, 589, 15], [711, 19, 589, 17], [711, 20, 589, 18], [711, 35, 590, 12], [711, 39, 590, 12, "_jsxDevRuntime"], [711, 53, 590, 12], [711, 54, 590, 12, "jsxDEV"], [711, 60, 590, 12], [711, 62, 590, 13, "_expoBlur"], [711, 71, 590, 13], [711, 72, 590, 13, "BlurView"], [711, 80, 590, 21], [712, 14, 590, 22, "intensity"], [712, 23, 590, 31], [712, 25, 590, 33], [712, 27, 590, 36], [713, 14, 590, 37, "tint"], [713, 18, 590, 41], [713, 20, 590, 42], [713, 26, 590, 48], [714, 14, 590, 49, "style"], [714, 19, 590, 54], [714, 21, 590, 56], [714, 22, 590, 57, "styles"], [714, 28, 590, 63], [714, 29, 590, 64, "blurZone"], [714, 37, 590, 72], [714, 39, 590, 74], [715, 16, 591, 14, "left"], [715, 20, 591, 18], [715, 22, 591, 20, "viewSize"], [715, 30, 591, 28], [715, 31, 591, 29, "width"], [715, 36, 591, 34], [715, 39, 591, 37], [715, 42, 591, 40], [715, 45, 591, 44, "viewSize"], [715, 53, 591, 52], [715, 54, 591, 53, "width"], [715, 59, 591, 58], [715, 62, 591, 61], [715, 66, 591, 66], [716, 16, 592, 14, "top"], [716, 19, 592, 17], [716, 21, 592, 19, "viewSize"], [716, 29, 592, 27], [716, 30, 592, 28, "height"], [716, 36, 592, 34], [716, 39, 592, 37], [716, 42, 592, 40], [716, 45, 592, 44, "viewSize"], [716, 53, 592, 52], [716, 54, 592, 53, "width"], [716, 59, 592, 58], [716, 62, 592, 61], [716, 66, 592, 66], [717, 16, 593, 14, "width"], [717, 21, 593, 19], [717, 23, 593, 21, "viewSize"], [717, 31, 593, 29], [717, 32, 593, 30, "width"], [717, 37, 593, 35], [717, 40, 593, 38], [717, 43, 593, 41], [718, 16, 594, 14, "height"], [718, 22, 594, 20], [718, 24, 594, 22, "viewSize"], [718, 32, 594, 30], [718, 33, 594, 31, "width"], [718, 38, 594, 36], [718, 41, 594, 39], [718, 44, 594, 42], [719, 16, 595, 14, "borderRadius"], [719, 28, 595, 26], [719, 30, 595, 29, "viewSize"], [719, 38, 595, 37], [719, 39, 595, 38, "width"], [719, 44, 595, 43], [719, 47, 595, 46], [719, 50, 595, 49], [719, 53, 595, 53], [720, 14, 596, 12], [720, 15, 596, 13], [721, 12, 596, 15], [722, 14, 596, 15, "fileName"], [722, 22, 596, 15], [722, 24, 596, 15, "_jsxFileName"], [722, 36, 596, 15], [723, 14, 596, 15, "lineNumber"], [723, 24, 596, 15], [724, 14, 596, 15, "columnNumber"], [724, 26, 596, 15], [725, 12, 596, 15], [725, 19, 596, 17], [725, 20, 596, 18], [725, 22, 598, 13, "__DEV__"], [725, 29, 598, 20], [725, 46, 599, 14], [725, 50, 599, 14, "_jsxDevRuntime"], [725, 64, 599, 14], [725, 65, 599, 14, "jsxDEV"], [725, 71, 599, 14], [725, 73, 599, 15, "_View"], [725, 78, 599, 15], [725, 79, 599, 15, "default"], [725, 86, 599, 19], [726, 14, 599, 20, "style"], [726, 19, 599, 25], [726, 21, 599, 27, "styles"], [726, 27, 599, 33], [726, 28, 599, 34, "previewChip"], [726, 39, 599, 46], [727, 14, 599, 46, "children"], [727, 22, 599, 46], [727, 37, 600, 16], [727, 41, 600, 16, "_jsxDevRuntime"], [727, 55, 600, 16], [727, 56, 600, 16, "jsxDEV"], [727, 62, 600, 16], [727, 64, 600, 17, "_Text"], [727, 69, 600, 17], [727, 70, 600, 17, "default"], [727, 77, 600, 21], [728, 16, 600, 22, "style"], [728, 21, 600, 27], [728, 23, 600, 29, "styles"], [728, 29, 600, 35], [728, 30, 600, 36, "previewChipText"], [728, 45, 600, 52], [729, 16, 600, 52, "children"], [729, 24, 600, 52], [729, 26, 600, 53], [730, 14, 600, 73], [731, 16, 600, 73, "fileName"], [731, 24, 600, 73], [731, 26, 600, 73, "_jsxFileName"], [731, 38, 600, 73], [732, 16, 600, 73, "lineNumber"], [732, 26, 600, 73], [733, 16, 600, 73, "columnNumber"], [733, 28, 600, 73], [734, 14, 600, 73], [734, 21, 600, 79], [735, 12, 600, 80], [736, 14, 600, 80, "fileName"], [736, 22, 600, 80], [736, 24, 600, 80, "_jsxFileName"], [736, 36, 600, 80], [737, 14, 600, 80, "lineNumber"], [737, 24, 600, 80], [738, 14, 600, 80, "columnNumber"], [738, 26, 600, 80], [739, 12, 600, 80], [739, 19, 601, 20], [739, 20, 602, 13], [740, 10, 602, 13], [741, 12, 602, 13, "fileName"], [741, 20, 602, 13], [741, 22, 602, 13, "_jsxFileName"], [741, 34, 602, 13], [742, 12, 602, 13, "lineNumber"], [742, 22, 602, 13], [743, 12, 602, 13, "columnNumber"], [743, 24, 602, 13], [744, 10, 602, 13], [744, 17, 603, 18], [744, 18, 603, 19], [745, 8, 603, 19], [745, 23, 604, 12], [745, 24, 605, 9], [745, 26, 607, 9, "isCameraReady"], [745, 39, 607, 22], [745, 56, 608, 10], [745, 60, 608, 10, "_jsxDevRuntime"], [745, 74, 608, 10], [745, 75, 608, 10, "jsxDEV"], [745, 81, 608, 10], [745, 83, 608, 10, "_jsxDevRuntime"], [745, 97, 608, 10], [745, 98, 608, 10, "Fragment"], [745, 106, 608, 10], [746, 10, 608, 10, "children"], [746, 18, 608, 10], [746, 34, 610, 12], [746, 38, 610, 12, "_jsxDevRuntime"], [746, 52, 610, 12], [746, 53, 610, 12, "jsxDEV"], [746, 59, 610, 12], [746, 61, 610, 13, "_View"], [746, 66, 610, 13], [746, 67, 610, 13, "default"], [746, 74, 610, 17], [747, 12, 610, 18, "style"], [747, 17, 610, 23], [747, 19, 610, 25, "styles"], [747, 25, 610, 31], [747, 26, 610, 32, "headerOverlay"], [747, 39, 610, 46], [748, 12, 610, 46, "children"], [748, 20, 610, 46], [748, 35, 611, 14], [748, 39, 611, 14, "_jsxDevRuntime"], [748, 53, 611, 14], [748, 54, 611, 14, "jsxDEV"], [748, 60, 611, 14], [748, 62, 611, 15, "_View"], [748, 67, 611, 15], [748, 68, 611, 15, "default"], [748, 75, 611, 19], [749, 14, 611, 20, "style"], [749, 19, 611, 25], [749, 21, 611, 27, "styles"], [749, 27, 611, 33], [749, 28, 611, 34, "headerContent"], [749, 41, 611, 48], [750, 14, 611, 48, "children"], [750, 22, 611, 48], [750, 38, 612, 16], [750, 42, 612, 16, "_jsxDevRuntime"], [750, 56, 612, 16], [750, 57, 612, 16, "jsxDEV"], [750, 63, 612, 16], [750, 65, 612, 17, "_View"], [750, 70, 612, 17], [750, 71, 612, 17, "default"], [750, 78, 612, 21], [751, 16, 612, 22, "style"], [751, 21, 612, 27], [751, 23, 612, 29, "styles"], [751, 29, 612, 35], [751, 30, 612, 36, "headerLeft"], [751, 40, 612, 47], [752, 16, 612, 47, "children"], [752, 24, 612, 47], [752, 40, 613, 18], [752, 44, 613, 18, "_jsxDevRuntime"], [752, 58, 613, 18], [752, 59, 613, 18, "jsxDEV"], [752, 65, 613, 18], [752, 67, 613, 19, "_Text"], [752, 72, 613, 19], [752, 73, 613, 19, "default"], [752, 80, 613, 23], [753, 18, 613, 24, "style"], [753, 23, 613, 29], [753, 25, 613, 31, "styles"], [753, 31, 613, 37], [753, 32, 613, 38, "headerTitle"], [753, 43, 613, 50], [754, 18, 613, 50, "children"], [754, 26, 613, 50], [754, 28, 613, 51], [755, 16, 613, 62], [756, 18, 613, 62, "fileName"], [756, 26, 613, 62], [756, 28, 613, 62, "_jsxFileName"], [756, 40, 613, 62], [757, 18, 613, 62, "lineNumber"], [757, 28, 613, 62], [758, 18, 613, 62, "columnNumber"], [758, 30, 613, 62], [759, 16, 613, 62], [759, 23, 613, 68], [759, 24, 613, 69], [759, 39, 614, 18], [759, 43, 614, 18, "_jsxDevRuntime"], [759, 57, 614, 18], [759, 58, 614, 18, "jsxDEV"], [759, 64, 614, 18], [759, 66, 614, 19, "_View"], [759, 71, 614, 19], [759, 72, 614, 19, "default"], [759, 79, 614, 23], [760, 18, 614, 24, "style"], [760, 23, 614, 29], [760, 25, 614, 31, "styles"], [760, 31, 614, 37], [760, 32, 614, 38, "subtitleRow"], [760, 43, 614, 50], [761, 18, 614, 50, "children"], [761, 26, 614, 50], [761, 42, 615, 20], [761, 46, 615, 20, "_jsxDevRuntime"], [761, 60, 615, 20], [761, 61, 615, 20, "jsxDEV"], [761, 67, 615, 20], [761, 69, 615, 21, "_Text"], [761, 74, 615, 21], [761, 75, 615, 21, "default"], [761, 82, 615, 25], [762, 20, 615, 26, "style"], [762, 25, 615, 31], [762, 27, 615, 33, "styles"], [762, 33, 615, 39], [762, 34, 615, 40, "webIcon"], [762, 41, 615, 48], [763, 20, 615, 48, "children"], [763, 28, 615, 48], [763, 30, 615, 49], [764, 18, 615, 51], [765, 20, 615, 51, "fileName"], [765, 28, 615, 51], [765, 30, 615, 51, "_jsxFileName"], [765, 42, 615, 51], [766, 20, 615, 51, "lineNumber"], [766, 30, 615, 51], [767, 20, 615, 51, "columnNumber"], [767, 32, 615, 51], [768, 18, 615, 51], [768, 25, 615, 57], [768, 26, 615, 58], [768, 41, 616, 20], [768, 45, 616, 20, "_jsxDevRuntime"], [768, 59, 616, 20], [768, 60, 616, 20, "jsxDEV"], [768, 66, 616, 20], [768, 68, 616, 21, "_Text"], [768, 73, 616, 21], [768, 74, 616, 21, "default"], [768, 81, 616, 25], [769, 20, 616, 26, "style"], [769, 25, 616, 31], [769, 27, 616, 33, "styles"], [769, 33, 616, 39], [769, 34, 616, 40, "headerSubtitle"], [769, 48, 616, 55], [770, 20, 616, 55, "children"], [770, 28, 616, 55], [770, 30, 616, 56], [771, 18, 616, 71], [772, 20, 616, 71, "fileName"], [772, 28, 616, 71], [772, 30, 616, 71, "_jsxFileName"], [772, 42, 616, 71], [773, 20, 616, 71, "lineNumber"], [773, 30, 616, 71], [774, 20, 616, 71, "columnNumber"], [774, 32, 616, 71], [775, 18, 616, 71], [775, 25, 616, 77], [775, 26, 616, 78], [776, 16, 616, 78], [777, 18, 616, 78, "fileName"], [777, 26, 616, 78], [777, 28, 616, 78, "_jsxFileName"], [777, 40, 616, 78], [778, 18, 616, 78, "lineNumber"], [778, 28, 616, 78], [779, 18, 616, 78, "columnNumber"], [779, 30, 616, 78], [780, 16, 616, 78], [780, 23, 617, 24], [780, 24, 617, 25], [780, 26, 618, 19, "challengeCode"], [780, 39, 618, 32], [780, 56, 619, 20], [780, 60, 619, 20, "_jsxDevRuntime"], [780, 74, 619, 20], [780, 75, 619, 20, "jsxDEV"], [780, 81, 619, 20], [780, 83, 619, 21, "_View"], [780, 88, 619, 21], [780, 89, 619, 21, "default"], [780, 96, 619, 25], [781, 18, 619, 26, "style"], [781, 23, 619, 31], [781, 25, 619, 33, "styles"], [781, 31, 619, 39], [781, 32, 619, 40, "challengeRow"], [781, 44, 619, 53], [782, 18, 619, 53, "children"], [782, 26, 619, 53], [782, 42, 620, 22], [782, 46, 620, 22, "_jsxDevRuntime"], [782, 60, 620, 22], [782, 61, 620, 22, "jsxDEV"], [782, 67, 620, 22], [782, 69, 620, 23, "_lucideReactNative"], [782, 87, 620, 23], [782, 88, 620, 23, "Shield"], [782, 94, 620, 29], [783, 20, 620, 30, "size"], [783, 24, 620, 34], [783, 26, 620, 36], [783, 28, 620, 39], [784, 20, 620, 40, "color"], [784, 25, 620, 45], [784, 27, 620, 46], [785, 18, 620, 52], [786, 20, 620, 52, "fileName"], [786, 28, 620, 52], [786, 30, 620, 52, "_jsxFileName"], [786, 42, 620, 52], [787, 20, 620, 52, "lineNumber"], [787, 30, 620, 52], [788, 20, 620, 52, "columnNumber"], [788, 32, 620, 52], [789, 18, 620, 52], [789, 25, 620, 54], [789, 26, 620, 55], [789, 41, 621, 22], [789, 45, 621, 22, "_jsxDevRuntime"], [789, 59, 621, 22], [789, 60, 621, 22, "jsxDEV"], [789, 66, 621, 22], [789, 68, 621, 23, "_Text"], [789, 73, 621, 23], [789, 74, 621, 23, "default"], [789, 81, 621, 27], [790, 20, 621, 28, "style"], [790, 25, 621, 33], [790, 27, 621, 35, "styles"], [790, 33, 621, 41], [790, 34, 621, 42, "challengeCode"], [790, 47, 621, 56], [791, 20, 621, 56, "children"], [791, 28, 621, 56], [791, 30, 621, 58, "challengeCode"], [792, 18, 621, 71], [793, 20, 621, 71, "fileName"], [793, 28, 621, 71], [793, 30, 621, 71, "_jsxFileName"], [793, 42, 621, 71], [794, 20, 621, 71, "lineNumber"], [794, 30, 621, 71], [795, 20, 621, 71, "columnNumber"], [795, 32, 621, 71], [796, 18, 621, 71], [796, 25, 621, 78], [796, 26, 621, 79], [797, 16, 621, 79], [798, 18, 621, 79, "fileName"], [798, 26, 621, 79], [798, 28, 621, 79, "_jsxFileName"], [798, 40, 621, 79], [799, 18, 621, 79, "lineNumber"], [799, 28, 621, 79], [800, 18, 621, 79, "columnNumber"], [800, 30, 621, 79], [801, 16, 621, 79], [801, 23, 622, 26], [801, 24, 623, 19], [802, 14, 623, 19], [803, 16, 623, 19, "fileName"], [803, 24, 623, 19], [803, 26, 623, 19, "_jsxFileName"], [803, 38, 623, 19], [804, 16, 623, 19, "lineNumber"], [804, 26, 623, 19], [805, 16, 623, 19, "columnNumber"], [805, 28, 623, 19], [806, 14, 623, 19], [806, 21, 624, 22], [806, 22, 624, 23], [806, 37, 625, 16], [806, 41, 625, 16, "_jsxDevRuntime"], [806, 55, 625, 16], [806, 56, 625, 16, "jsxDEV"], [806, 62, 625, 16], [806, 64, 625, 17, "_TouchableOpacity"], [806, 81, 625, 17], [806, 82, 625, 17, "default"], [806, 89, 625, 33], [807, 16, 625, 34, "onPress"], [807, 23, 625, 41], [807, 25, 625, 43, "onCancel"], [807, 33, 625, 52], [808, 16, 625, 53, "style"], [808, 21, 625, 58], [808, 23, 625, 60, "styles"], [808, 29, 625, 66], [808, 30, 625, 67, "closeButton"], [808, 41, 625, 79], [809, 16, 625, 79, "children"], [809, 24, 625, 79], [809, 39, 626, 18], [809, 43, 626, 18, "_jsxDevRuntime"], [809, 57, 626, 18], [809, 58, 626, 18, "jsxDEV"], [809, 64, 626, 18], [809, 66, 626, 19, "_lucideReactNative"], [809, 84, 626, 19], [809, 85, 626, 19, "X"], [809, 86, 626, 20], [810, 18, 626, 21, "size"], [810, 22, 626, 25], [810, 24, 626, 27], [810, 26, 626, 30], [811, 18, 626, 31, "color"], [811, 23, 626, 36], [811, 25, 626, 37], [812, 16, 626, 43], [813, 18, 626, 43, "fileName"], [813, 26, 626, 43], [813, 28, 626, 43, "_jsxFileName"], [813, 40, 626, 43], [814, 18, 626, 43, "lineNumber"], [814, 28, 626, 43], [815, 18, 626, 43, "columnNumber"], [815, 30, 626, 43], [816, 16, 626, 43], [816, 23, 626, 45], [817, 14, 626, 46], [818, 16, 626, 46, "fileName"], [818, 24, 626, 46], [818, 26, 626, 46, "_jsxFileName"], [818, 38, 626, 46], [819, 16, 626, 46, "lineNumber"], [819, 26, 626, 46], [820, 16, 626, 46, "columnNumber"], [820, 28, 626, 46], [821, 14, 626, 46], [821, 21, 627, 34], [821, 22, 627, 35], [822, 12, 627, 35], [823, 14, 627, 35, "fileName"], [823, 22, 627, 35], [823, 24, 627, 35, "_jsxFileName"], [823, 36, 627, 35], [824, 14, 627, 35, "lineNumber"], [824, 24, 627, 35], [825, 14, 627, 35, "columnNumber"], [825, 26, 627, 35], [826, 12, 627, 35], [826, 19, 628, 20], [827, 10, 628, 21], [828, 12, 628, 21, "fileName"], [828, 20, 628, 21], [828, 22, 628, 21, "_jsxFileName"], [828, 34, 628, 21], [829, 12, 628, 21, "lineNumber"], [829, 22, 628, 21], [830, 12, 628, 21, "columnNumber"], [830, 24, 628, 21], [831, 10, 628, 21], [831, 17, 629, 18], [831, 18, 629, 19], [831, 33, 631, 12], [831, 37, 631, 12, "_jsxDevRuntime"], [831, 51, 631, 12], [831, 52, 631, 12, "jsxDEV"], [831, 58, 631, 12], [831, 60, 631, 13, "_View"], [831, 65, 631, 13], [831, 66, 631, 13, "default"], [831, 73, 631, 17], [832, 12, 631, 18, "style"], [832, 17, 631, 23], [832, 19, 631, 25, "styles"], [832, 25, 631, 31], [832, 26, 631, 32, "privacyNotice"], [832, 39, 631, 46], [833, 12, 631, 46, "children"], [833, 20, 631, 46], [833, 36, 632, 14], [833, 40, 632, 14, "_jsxDevRuntime"], [833, 54, 632, 14], [833, 55, 632, 14, "jsxDEV"], [833, 61, 632, 14], [833, 63, 632, 15, "_lucideReactNative"], [833, 81, 632, 15], [833, 82, 632, 15, "Shield"], [833, 88, 632, 21], [834, 14, 632, 22, "size"], [834, 18, 632, 26], [834, 20, 632, 28], [834, 22, 632, 31], [835, 14, 632, 32, "color"], [835, 19, 632, 37], [835, 21, 632, 38], [836, 12, 632, 47], [837, 14, 632, 47, "fileName"], [837, 22, 632, 47], [837, 24, 632, 47, "_jsxFileName"], [837, 36, 632, 47], [838, 14, 632, 47, "lineNumber"], [838, 24, 632, 47], [839, 14, 632, 47, "columnNumber"], [839, 26, 632, 47], [840, 12, 632, 47], [840, 19, 632, 49], [840, 20, 632, 50], [840, 35, 633, 14], [840, 39, 633, 14, "_jsxDevRuntime"], [840, 53, 633, 14], [840, 54, 633, 14, "jsxDEV"], [840, 60, 633, 14], [840, 62, 633, 15, "_Text"], [840, 67, 633, 15], [840, 68, 633, 15, "default"], [840, 75, 633, 19], [841, 14, 633, 20, "style"], [841, 19, 633, 25], [841, 21, 633, 27, "styles"], [841, 27, 633, 33], [841, 28, 633, 34, "privacyText"], [841, 39, 633, 46], [842, 14, 633, 46, "children"], [842, 22, 633, 46], [842, 24, 633, 47], [843, 12, 635, 14], [844, 14, 635, 14, "fileName"], [844, 22, 635, 14], [844, 24, 635, 14, "_jsxFileName"], [844, 36, 635, 14], [845, 14, 635, 14, "lineNumber"], [845, 24, 635, 14], [846, 14, 635, 14, "columnNumber"], [846, 26, 635, 14], [847, 12, 635, 14], [847, 19, 635, 20], [847, 20, 635, 21], [848, 10, 635, 21], [849, 12, 635, 21, "fileName"], [849, 20, 635, 21], [849, 22, 635, 21, "_jsxFileName"], [849, 34, 635, 21], [850, 12, 635, 21, "lineNumber"], [850, 22, 635, 21], [851, 12, 635, 21, "columnNumber"], [851, 24, 635, 21], [852, 10, 635, 21], [852, 17, 636, 18], [852, 18, 636, 19], [852, 33, 638, 12], [852, 37, 638, 12, "_jsxDevRuntime"], [852, 51, 638, 12], [852, 52, 638, 12, "jsxDEV"], [852, 58, 638, 12], [852, 60, 638, 13, "_View"], [852, 65, 638, 13], [852, 66, 638, 13, "default"], [852, 73, 638, 17], [853, 12, 638, 18, "style"], [853, 17, 638, 23], [853, 19, 638, 25, "styles"], [853, 25, 638, 31], [853, 26, 638, 32, "footer<PERSON><PERSON><PERSON>"], [853, 39, 638, 46], [854, 12, 638, 46, "children"], [854, 20, 638, 46], [854, 36, 639, 14], [854, 40, 639, 14, "_jsxDevRuntime"], [854, 54, 639, 14], [854, 55, 639, 14, "jsxDEV"], [854, 61, 639, 14], [854, 63, 639, 15, "_Text"], [854, 68, 639, 15], [854, 69, 639, 15, "default"], [854, 76, 639, 19], [855, 14, 639, 20, "style"], [855, 19, 639, 25], [855, 21, 639, 27, "styles"], [855, 27, 639, 33], [855, 28, 639, 34, "instruction"], [855, 39, 639, 46], [856, 14, 639, 46, "children"], [856, 22, 639, 46], [856, 24, 639, 47], [857, 12, 641, 14], [858, 14, 641, 14, "fileName"], [858, 22, 641, 14], [858, 24, 641, 14, "_jsxFileName"], [858, 36, 641, 14], [859, 14, 641, 14, "lineNumber"], [859, 24, 641, 14], [860, 14, 641, 14, "columnNumber"], [860, 26, 641, 14], [861, 12, 641, 14], [861, 19, 641, 20], [861, 20, 641, 21], [861, 35, 643, 14], [861, 39, 643, 14, "_jsxDevRuntime"], [861, 53, 643, 14], [861, 54, 643, 14, "jsxDEV"], [861, 60, 643, 14], [861, 62, 643, 15, "_TouchableOpacity"], [861, 79, 643, 15], [861, 80, 643, 15, "default"], [861, 87, 643, 31], [862, 14, 644, 16, "onPress"], [862, 21, 644, 23], [862, 23, 644, 25, "capturePhoto"], [862, 35, 644, 38], [863, 14, 645, 16, "disabled"], [863, 22, 645, 24], [863, 24, 645, 26, "processingState"], [863, 39, 645, 41], [863, 44, 645, 46], [863, 50, 645, 52], [863, 54, 645, 56], [863, 55, 645, 57, "isCameraReady"], [863, 68, 645, 71], [864, 14, 646, 16, "style"], [864, 19, 646, 21], [864, 21, 646, 23], [864, 22, 647, 18, "styles"], [864, 28, 647, 24], [864, 29, 647, 25, "shutterButton"], [864, 42, 647, 38], [864, 44, 648, 18, "processingState"], [864, 59, 648, 33], [864, 64, 648, 38], [864, 70, 648, 44], [864, 74, 648, 48, "styles"], [864, 80, 648, 54], [864, 81, 648, 55, "shutterButtonDisabled"], [864, 102, 648, 76], [864, 103, 649, 18], [865, 14, 649, 18, "children"], [865, 22, 649, 18], [865, 24, 651, 17, "processingState"], [865, 39, 651, 32], [865, 44, 651, 37], [865, 50, 651, 43], [865, 66, 652, 18], [865, 70, 652, 18, "_jsxDevRuntime"], [865, 84, 652, 18], [865, 85, 652, 18, "jsxDEV"], [865, 91, 652, 18], [865, 93, 652, 19, "_View"], [865, 98, 652, 19], [865, 99, 652, 19, "default"], [865, 106, 652, 23], [866, 16, 652, 24, "style"], [866, 21, 652, 29], [866, 23, 652, 31, "styles"], [866, 29, 652, 37], [866, 30, 652, 38, "shutterInner"], [867, 14, 652, 51], [868, 16, 652, 51, "fileName"], [868, 24, 652, 51], [868, 26, 652, 51, "_jsxFileName"], [868, 38, 652, 51], [869, 16, 652, 51, "lineNumber"], [869, 26, 652, 51], [870, 16, 652, 51, "columnNumber"], [870, 28, 652, 51], [871, 14, 652, 51], [871, 21, 652, 53], [871, 22, 652, 54], [871, 38, 654, 18], [871, 42, 654, 18, "_jsxDevRuntime"], [871, 56, 654, 18], [871, 57, 654, 18, "jsxDEV"], [871, 63, 654, 18], [871, 65, 654, 19, "_ActivityIndicator"], [871, 83, 654, 19], [871, 84, 654, 19, "default"], [871, 91, 654, 36], [872, 16, 654, 37, "size"], [872, 20, 654, 41], [872, 22, 654, 42], [872, 29, 654, 49], [873, 16, 654, 50, "color"], [873, 21, 654, 55], [873, 23, 654, 56], [874, 14, 654, 65], [875, 16, 654, 65, "fileName"], [875, 24, 654, 65], [875, 26, 654, 65, "_jsxFileName"], [875, 38, 654, 65], [876, 16, 654, 65, "lineNumber"], [876, 26, 654, 65], [877, 16, 654, 65, "columnNumber"], [877, 28, 654, 65], [878, 14, 654, 65], [878, 21, 654, 67], [879, 12, 655, 17], [880, 14, 655, 17, "fileName"], [880, 22, 655, 17], [880, 24, 655, 17, "_jsxFileName"], [880, 36, 655, 17], [881, 14, 655, 17, "lineNumber"], [881, 24, 655, 17], [882, 14, 655, 17, "columnNumber"], [882, 26, 655, 17], [883, 12, 655, 17], [883, 19, 656, 32], [883, 20, 656, 33], [883, 35, 657, 14], [883, 39, 657, 14, "_jsxDevRuntime"], [883, 53, 657, 14], [883, 54, 657, 14, "jsxDEV"], [883, 60, 657, 14], [883, 62, 657, 15, "_Text"], [883, 67, 657, 15], [883, 68, 657, 15, "default"], [883, 75, 657, 19], [884, 14, 657, 20, "style"], [884, 19, 657, 25], [884, 21, 657, 27, "styles"], [884, 27, 657, 33], [884, 28, 657, 34, "privacyNote"], [884, 39, 657, 46], [885, 14, 657, 46, "children"], [885, 22, 657, 46], [885, 24, 657, 47], [886, 12, 659, 14], [887, 14, 659, 14, "fileName"], [887, 22, 659, 14], [887, 24, 659, 14, "_jsxFileName"], [887, 36, 659, 14], [888, 14, 659, 14, "lineNumber"], [888, 24, 659, 14], [889, 14, 659, 14, "columnNumber"], [889, 26, 659, 14], [890, 12, 659, 14], [890, 19, 659, 20], [890, 20, 659, 21], [891, 10, 659, 21], [892, 12, 659, 21, "fileName"], [892, 20, 659, 21], [892, 22, 659, 21, "_jsxFileName"], [892, 34, 659, 21], [893, 12, 659, 21, "lineNumber"], [893, 22, 659, 21], [894, 12, 659, 21, "columnNumber"], [894, 24, 659, 21], [895, 10, 659, 21], [895, 17, 660, 18], [895, 18, 660, 19], [896, 8, 660, 19], [896, 23, 661, 12], [896, 24, 662, 9], [897, 6, 662, 9], [898, 8, 662, 9, "fileName"], [898, 16, 662, 9], [898, 18, 662, 9, "_jsxFileName"], [898, 30, 662, 9], [899, 8, 662, 9, "lineNumber"], [899, 18, 662, 9], [900, 8, 662, 9, "columnNumber"], [900, 20, 662, 9], [901, 6, 662, 9], [901, 13, 663, 12], [901, 14, 663, 13], [901, 29, 665, 6], [901, 33, 665, 6, "_jsxDevRuntime"], [901, 47, 665, 6], [901, 48, 665, 6, "jsxDEV"], [901, 54, 665, 6], [901, 56, 665, 7, "_Modal"], [901, 62, 665, 7], [901, 63, 665, 7, "default"], [901, 70, 665, 12], [902, 8, 666, 8, "visible"], [902, 15, 666, 15], [902, 17, 666, 17, "processingState"], [902, 32, 666, 32], [902, 37, 666, 37], [902, 43, 666, 43], [902, 47, 666, 47, "processingState"], [902, 62, 666, 62], [902, 67, 666, 67], [902, 74, 666, 75], [903, 8, 667, 8, "transparent"], [903, 19, 667, 19], [904, 8, 668, 8, "animationType"], [904, 21, 668, 21], [904, 23, 668, 22], [904, 29, 668, 28], [905, 8, 668, 28, "children"], [905, 16, 668, 28], [905, 31, 670, 8], [905, 35, 670, 8, "_jsxDevRuntime"], [905, 49, 670, 8], [905, 50, 670, 8, "jsxDEV"], [905, 56, 670, 8], [905, 58, 670, 9, "_View"], [905, 63, 670, 9], [905, 64, 670, 9, "default"], [905, 71, 670, 13], [906, 10, 670, 14, "style"], [906, 15, 670, 19], [906, 17, 670, 21, "styles"], [906, 23, 670, 27], [906, 24, 670, 28, "processingModal"], [906, 39, 670, 44], [907, 10, 670, 44, "children"], [907, 18, 670, 44], [907, 33, 671, 10], [907, 37, 671, 10, "_jsxDevRuntime"], [907, 51, 671, 10], [907, 52, 671, 10, "jsxDEV"], [907, 58, 671, 10], [907, 60, 671, 11, "_View"], [907, 65, 671, 11], [907, 66, 671, 11, "default"], [907, 73, 671, 15], [908, 12, 671, 16, "style"], [908, 17, 671, 21], [908, 19, 671, 23, "styles"], [908, 25, 671, 29], [908, 26, 671, 30, "processingContent"], [908, 43, 671, 48], [909, 12, 671, 48, "children"], [909, 20, 671, 48], [909, 36, 672, 12], [909, 40, 672, 12, "_jsxDevRuntime"], [909, 54, 672, 12], [909, 55, 672, 12, "jsxDEV"], [909, 61, 672, 12], [909, 63, 672, 13, "_ActivityIndicator"], [909, 81, 672, 13], [909, 82, 672, 13, "default"], [909, 89, 672, 30], [910, 14, 672, 31, "size"], [910, 18, 672, 35], [910, 20, 672, 36], [910, 27, 672, 43], [911, 14, 672, 44, "color"], [911, 19, 672, 49], [911, 21, 672, 50], [912, 12, 672, 59], [913, 14, 672, 59, "fileName"], [913, 22, 672, 59], [913, 24, 672, 59, "_jsxFileName"], [913, 36, 672, 59], [914, 14, 672, 59, "lineNumber"], [914, 24, 672, 59], [915, 14, 672, 59, "columnNumber"], [915, 26, 672, 59], [916, 12, 672, 59], [916, 19, 672, 61], [916, 20, 672, 62], [916, 35, 674, 12], [916, 39, 674, 12, "_jsxDevRuntime"], [916, 53, 674, 12], [916, 54, 674, 12, "jsxDEV"], [916, 60, 674, 12], [916, 62, 674, 13, "_Text"], [916, 67, 674, 13], [916, 68, 674, 13, "default"], [916, 75, 674, 17], [917, 14, 674, 18, "style"], [917, 19, 674, 23], [917, 21, 674, 25, "styles"], [917, 27, 674, 31], [917, 28, 674, 32, "processingTitle"], [917, 43, 674, 48], [918, 14, 674, 48, "children"], [918, 22, 674, 48], [918, 25, 675, 15, "processingState"], [918, 40, 675, 30], [918, 45, 675, 35], [918, 56, 675, 46], [918, 60, 675, 50], [918, 80, 675, 70], [918, 82, 676, 15, "processingState"], [918, 97, 676, 30], [918, 102, 676, 35], [918, 113, 676, 46], [918, 117, 676, 50], [918, 146, 676, 79], [918, 148, 677, 15, "processingState"], [918, 163, 677, 30], [918, 168, 677, 35], [918, 180, 677, 47], [918, 184, 677, 51], [918, 216, 677, 83], [918, 218, 678, 15, "processingState"], [918, 233, 678, 30], [918, 238, 678, 35], [918, 249, 678, 46], [918, 253, 678, 50], [918, 275, 678, 72], [919, 12, 678, 72], [920, 14, 678, 72, "fileName"], [920, 22, 678, 72], [920, 24, 678, 72, "_jsxFileName"], [920, 36, 678, 72], [921, 14, 678, 72, "lineNumber"], [921, 24, 678, 72], [922, 14, 678, 72, "columnNumber"], [922, 26, 678, 72], [923, 12, 678, 72], [923, 19, 679, 18], [923, 20, 679, 19], [923, 35, 680, 12], [923, 39, 680, 12, "_jsxDevRuntime"], [923, 53, 680, 12], [923, 54, 680, 12, "jsxDEV"], [923, 60, 680, 12], [923, 62, 680, 13, "_View"], [923, 67, 680, 13], [923, 68, 680, 13, "default"], [923, 75, 680, 17], [924, 14, 680, 18, "style"], [924, 19, 680, 23], [924, 21, 680, 25, "styles"], [924, 27, 680, 31], [924, 28, 680, 32, "progressBar"], [924, 39, 680, 44], [925, 14, 680, 44, "children"], [925, 22, 680, 44], [925, 37, 681, 14], [925, 41, 681, 14, "_jsxDevRuntime"], [925, 55, 681, 14], [925, 56, 681, 14, "jsxDEV"], [925, 62, 681, 14], [925, 64, 681, 15, "_View"], [925, 69, 681, 15], [925, 70, 681, 15, "default"], [925, 77, 681, 19], [926, 16, 682, 16, "style"], [926, 21, 682, 21], [926, 23, 682, 23], [926, 24, 683, 18, "styles"], [926, 30, 683, 24], [926, 31, 683, 25, "progressFill"], [926, 43, 683, 37], [926, 45, 684, 18], [927, 18, 684, 20, "width"], [927, 23, 684, 25], [927, 25, 684, 27], [927, 28, 684, 30, "processingProgress"], [927, 46, 684, 48], [928, 16, 684, 52], [928, 17, 684, 53], [929, 14, 685, 18], [930, 16, 685, 18, "fileName"], [930, 24, 685, 18], [930, 26, 685, 18, "_jsxFileName"], [930, 38, 685, 18], [931, 16, 685, 18, "lineNumber"], [931, 26, 685, 18], [932, 16, 685, 18, "columnNumber"], [932, 28, 685, 18], [933, 14, 685, 18], [933, 21, 686, 15], [934, 12, 686, 16], [935, 14, 686, 16, "fileName"], [935, 22, 686, 16], [935, 24, 686, 16, "_jsxFileName"], [935, 36, 686, 16], [936, 14, 686, 16, "lineNumber"], [936, 24, 686, 16], [937, 14, 686, 16, "columnNumber"], [937, 26, 686, 16], [938, 12, 686, 16], [938, 19, 687, 18], [938, 20, 687, 19], [938, 35, 688, 12], [938, 39, 688, 12, "_jsxDevRuntime"], [938, 53, 688, 12], [938, 54, 688, 12, "jsxDEV"], [938, 60, 688, 12], [938, 62, 688, 13, "_Text"], [938, 67, 688, 13], [938, 68, 688, 13, "default"], [938, 75, 688, 17], [939, 14, 688, 18, "style"], [939, 19, 688, 23], [939, 21, 688, 25, "styles"], [939, 27, 688, 31], [939, 28, 688, 32, "processingDescription"], [939, 49, 688, 54], [940, 14, 688, 54, "children"], [940, 22, 688, 54], [940, 25, 689, 15, "processingState"], [940, 40, 689, 30], [940, 45, 689, 35], [940, 56, 689, 46], [940, 60, 689, 50], [940, 89, 689, 79], [940, 91, 690, 15, "processingState"], [940, 106, 690, 30], [940, 111, 690, 35], [940, 122, 690, 46], [940, 126, 690, 50], [940, 164, 690, 88], [940, 166, 691, 15, "processingState"], [940, 181, 691, 30], [940, 186, 691, 35], [940, 198, 691, 47], [940, 202, 691, 51], [940, 247, 691, 96], [940, 249, 692, 15, "processingState"], [940, 264, 692, 30], [940, 269, 692, 35], [940, 280, 692, 46], [940, 284, 692, 50], [940, 325, 692, 91], [941, 12, 692, 91], [942, 14, 692, 91, "fileName"], [942, 22, 692, 91], [942, 24, 692, 91, "_jsxFileName"], [942, 36, 692, 91], [943, 14, 692, 91, "lineNumber"], [943, 24, 692, 91], [944, 14, 692, 91, "columnNumber"], [944, 26, 692, 91], [945, 12, 692, 91], [945, 19, 693, 18], [945, 20, 693, 19], [945, 22, 694, 13, "processingState"], [945, 37, 694, 28], [945, 42, 694, 33], [945, 53, 694, 44], [945, 70, 695, 14], [945, 74, 695, 14, "_jsxDevRuntime"], [945, 88, 695, 14], [945, 89, 695, 14, "jsxDEV"], [945, 95, 695, 14], [945, 97, 695, 15, "_lucideReactNative"], [945, 115, 695, 15], [945, 116, 695, 15, "CheckCircle"], [945, 127, 695, 26], [946, 14, 695, 27, "size"], [946, 18, 695, 31], [946, 20, 695, 33], [946, 22, 695, 36], [947, 14, 695, 37, "color"], [947, 19, 695, 42], [947, 21, 695, 43], [947, 30, 695, 52], [948, 14, 695, 53, "style"], [948, 19, 695, 58], [948, 21, 695, 60, "styles"], [948, 27, 695, 66], [948, 28, 695, 67, "successIcon"], [949, 12, 695, 79], [950, 14, 695, 79, "fileName"], [950, 22, 695, 79], [950, 24, 695, 79, "_jsxFileName"], [950, 36, 695, 79], [951, 14, 695, 79, "lineNumber"], [951, 24, 695, 79], [952, 14, 695, 79, "columnNumber"], [952, 26, 695, 79], [953, 12, 695, 79], [953, 19, 695, 81], [953, 20, 696, 13], [954, 10, 696, 13], [955, 12, 696, 13, "fileName"], [955, 20, 696, 13], [955, 22, 696, 13, "_jsxFileName"], [955, 34, 696, 13], [956, 12, 696, 13, "lineNumber"], [956, 22, 696, 13], [957, 12, 696, 13, "columnNumber"], [957, 24, 696, 13], [958, 10, 696, 13], [958, 17, 697, 16], [959, 8, 697, 17], [960, 10, 697, 17, "fileName"], [960, 18, 697, 17], [960, 20, 697, 17, "_jsxFileName"], [960, 32, 697, 17], [961, 10, 697, 17, "lineNumber"], [961, 20, 697, 17], [962, 10, 697, 17, "columnNumber"], [962, 22, 697, 17], [963, 8, 697, 17], [963, 15, 698, 14], [964, 6, 698, 15], [965, 8, 698, 15, "fileName"], [965, 16, 698, 15], [965, 18, 698, 15, "_jsxFileName"], [965, 30, 698, 15], [966, 8, 698, 15, "lineNumber"], [966, 18, 698, 15], [967, 8, 698, 15, "columnNumber"], [967, 20, 698, 15], [968, 6, 698, 15], [968, 13, 699, 13], [968, 14, 699, 14], [968, 29, 701, 6], [968, 33, 701, 6, "_jsxDevRuntime"], [968, 47, 701, 6], [968, 48, 701, 6, "jsxDEV"], [968, 54, 701, 6], [968, 56, 701, 7, "_Modal"], [968, 62, 701, 7], [968, 63, 701, 7, "default"], [968, 70, 701, 12], [969, 8, 702, 8, "visible"], [969, 15, 702, 15], [969, 17, 702, 17, "processingState"], [969, 32, 702, 32], [969, 37, 702, 37], [969, 44, 702, 45], [970, 8, 703, 8, "transparent"], [970, 19, 703, 19], [971, 8, 704, 8, "animationType"], [971, 21, 704, 21], [971, 23, 704, 22], [971, 29, 704, 28], [972, 8, 704, 28, "children"], [972, 16, 704, 28], [972, 31, 706, 8], [972, 35, 706, 8, "_jsxDevRuntime"], [972, 49, 706, 8], [972, 50, 706, 8, "jsxDEV"], [972, 56, 706, 8], [972, 58, 706, 9, "_View"], [972, 63, 706, 9], [972, 64, 706, 9, "default"], [972, 71, 706, 13], [973, 10, 706, 14, "style"], [973, 15, 706, 19], [973, 17, 706, 21, "styles"], [973, 23, 706, 27], [973, 24, 706, 28, "processingModal"], [973, 39, 706, 44], [974, 10, 706, 44, "children"], [974, 18, 706, 44], [974, 33, 707, 10], [974, 37, 707, 10, "_jsxDevRuntime"], [974, 51, 707, 10], [974, 52, 707, 10, "jsxDEV"], [974, 58, 707, 10], [974, 60, 707, 11, "_View"], [974, 65, 707, 11], [974, 66, 707, 11, "default"], [974, 73, 707, 15], [975, 12, 707, 16, "style"], [975, 17, 707, 21], [975, 19, 707, 23, "styles"], [975, 25, 707, 29], [975, 26, 707, 30, "errorContent"], [975, 38, 707, 43], [976, 12, 707, 43, "children"], [976, 20, 707, 43], [976, 36, 708, 12], [976, 40, 708, 12, "_jsxDevRuntime"], [976, 54, 708, 12], [976, 55, 708, 12, "jsxDEV"], [976, 61, 708, 12], [976, 63, 708, 13, "_lucideReactNative"], [976, 81, 708, 13], [976, 82, 708, 13, "X"], [976, 83, 708, 14], [977, 14, 708, 15, "size"], [977, 18, 708, 19], [977, 20, 708, 21], [977, 22, 708, 24], [978, 14, 708, 25, "color"], [978, 19, 708, 30], [978, 21, 708, 31], [979, 12, 708, 40], [980, 14, 708, 40, "fileName"], [980, 22, 708, 40], [980, 24, 708, 40, "_jsxFileName"], [980, 36, 708, 40], [981, 14, 708, 40, "lineNumber"], [981, 24, 708, 40], [982, 14, 708, 40, "columnNumber"], [982, 26, 708, 40], [983, 12, 708, 40], [983, 19, 708, 42], [983, 20, 708, 43], [983, 35, 709, 12], [983, 39, 709, 12, "_jsxDevRuntime"], [983, 53, 709, 12], [983, 54, 709, 12, "jsxDEV"], [983, 60, 709, 12], [983, 62, 709, 13, "_Text"], [983, 67, 709, 13], [983, 68, 709, 13, "default"], [983, 75, 709, 17], [984, 14, 709, 18, "style"], [984, 19, 709, 23], [984, 21, 709, 25, "styles"], [984, 27, 709, 31], [984, 28, 709, 32, "errorTitle"], [984, 38, 709, 43], [985, 14, 709, 43, "children"], [985, 22, 709, 43], [985, 24, 709, 44], [986, 12, 709, 61], [987, 14, 709, 61, "fileName"], [987, 22, 709, 61], [987, 24, 709, 61, "_jsxFileName"], [987, 36, 709, 61], [988, 14, 709, 61, "lineNumber"], [988, 24, 709, 61], [989, 14, 709, 61, "columnNumber"], [989, 26, 709, 61], [990, 12, 709, 61], [990, 19, 709, 67], [990, 20, 709, 68], [990, 35, 710, 12], [990, 39, 710, 12, "_jsxDevRuntime"], [990, 53, 710, 12], [990, 54, 710, 12, "jsxDEV"], [990, 60, 710, 12], [990, 62, 710, 13, "_Text"], [990, 67, 710, 13], [990, 68, 710, 13, "default"], [990, 75, 710, 17], [991, 14, 710, 18, "style"], [991, 19, 710, 23], [991, 21, 710, 25, "styles"], [991, 27, 710, 31], [991, 28, 710, 32, "errorMessage"], [991, 40, 710, 45], [992, 14, 710, 45, "children"], [992, 22, 710, 45], [992, 24, 710, 47, "errorMessage"], [993, 12, 710, 59], [994, 14, 710, 59, "fileName"], [994, 22, 710, 59], [994, 24, 710, 59, "_jsxFileName"], [994, 36, 710, 59], [995, 14, 710, 59, "lineNumber"], [995, 24, 710, 59], [996, 14, 710, 59, "columnNumber"], [996, 26, 710, 59], [997, 12, 710, 59], [997, 19, 710, 66], [997, 20, 710, 67], [997, 35, 711, 12], [997, 39, 711, 12, "_jsxDevRuntime"], [997, 53, 711, 12], [997, 54, 711, 12, "jsxDEV"], [997, 60, 711, 12], [997, 62, 711, 13, "_TouchableOpacity"], [997, 79, 711, 13], [997, 80, 711, 13, "default"], [997, 87, 711, 29], [998, 14, 712, 14, "onPress"], [998, 21, 712, 21], [998, 23, 712, 23, "retryCapture"], [998, 35, 712, 36], [999, 14, 713, 14, "style"], [999, 19, 713, 19], [999, 21, 713, 21, "styles"], [999, 27, 713, 27], [999, 28, 713, 28, "primaryButton"], [999, 41, 713, 42], [1000, 14, 713, 42, "children"], [1000, 22, 713, 42], [1000, 37, 715, 14], [1000, 41, 715, 14, "_jsxDevRuntime"], [1000, 55, 715, 14], [1000, 56, 715, 14, "jsxDEV"], [1000, 62, 715, 14], [1000, 64, 715, 15, "_Text"], [1000, 69, 715, 15], [1000, 70, 715, 15, "default"], [1000, 77, 715, 19], [1001, 16, 715, 20, "style"], [1001, 21, 715, 25], [1001, 23, 715, 27, "styles"], [1001, 29, 715, 33], [1001, 30, 715, 34, "primaryButtonText"], [1001, 47, 715, 52], [1002, 16, 715, 52, "children"], [1002, 24, 715, 52], [1002, 26, 715, 53], [1003, 14, 715, 62], [1004, 16, 715, 62, "fileName"], [1004, 24, 715, 62], [1004, 26, 715, 62, "_jsxFileName"], [1004, 38, 715, 62], [1005, 16, 715, 62, "lineNumber"], [1005, 26, 715, 62], [1006, 16, 715, 62, "columnNumber"], [1006, 28, 715, 62], [1007, 14, 715, 62], [1007, 21, 715, 68], [1008, 12, 715, 69], [1009, 14, 715, 69, "fileName"], [1009, 22, 715, 69], [1009, 24, 715, 69, "_jsxFileName"], [1009, 36, 715, 69], [1010, 14, 715, 69, "lineNumber"], [1010, 24, 715, 69], [1011, 14, 715, 69, "columnNumber"], [1011, 26, 715, 69], [1012, 12, 715, 69], [1012, 19, 716, 30], [1012, 20, 716, 31], [1012, 35, 717, 12], [1012, 39, 717, 12, "_jsxDevRuntime"], [1012, 53, 717, 12], [1012, 54, 717, 12, "jsxDEV"], [1012, 60, 717, 12], [1012, 62, 717, 13, "_TouchableOpacity"], [1012, 79, 717, 13], [1012, 80, 717, 13, "default"], [1012, 87, 717, 29], [1013, 14, 718, 14, "onPress"], [1013, 21, 718, 21], [1013, 23, 718, 23, "onCancel"], [1013, 31, 718, 32], [1014, 14, 719, 14, "style"], [1014, 19, 719, 19], [1014, 21, 719, 21, "styles"], [1014, 27, 719, 27], [1014, 28, 719, 28, "secondaryButton"], [1014, 43, 719, 44], [1015, 14, 719, 44, "children"], [1015, 22, 719, 44], [1015, 37, 721, 14], [1015, 41, 721, 14, "_jsxDevRuntime"], [1015, 55, 721, 14], [1015, 56, 721, 14, "jsxDEV"], [1015, 62, 721, 14], [1015, 64, 721, 15, "_Text"], [1015, 69, 721, 15], [1015, 70, 721, 15, "default"], [1015, 77, 721, 19], [1016, 16, 721, 20, "style"], [1016, 21, 721, 25], [1016, 23, 721, 27, "styles"], [1016, 29, 721, 33], [1016, 30, 721, 34, "secondaryButtonText"], [1016, 49, 721, 54], [1017, 16, 721, 54, "children"], [1017, 24, 721, 54], [1017, 26, 721, 55], [1018, 14, 721, 61], [1019, 16, 721, 61, "fileName"], [1019, 24, 721, 61], [1019, 26, 721, 61, "_jsxFileName"], [1019, 38, 721, 61], [1020, 16, 721, 61, "lineNumber"], [1020, 26, 721, 61], [1021, 16, 721, 61, "columnNumber"], [1021, 28, 721, 61], [1022, 14, 721, 61], [1022, 21, 721, 67], [1023, 12, 721, 68], [1024, 14, 721, 68, "fileName"], [1024, 22, 721, 68], [1024, 24, 721, 68, "_jsxFileName"], [1024, 36, 721, 68], [1025, 14, 721, 68, "lineNumber"], [1025, 24, 721, 68], [1026, 14, 721, 68, "columnNumber"], [1026, 26, 721, 68], [1027, 12, 721, 68], [1027, 19, 722, 30], [1027, 20, 722, 31], [1028, 10, 722, 31], [1029, 12, 722, 31, "fileName"], [1029, 20, 722, 31], [1029, 22, 722, 31, "_jsxFileName"], [1029, 34, 722, 31], [1030, 12, 722, 31, "lineNumber"], [1030, 22, 722, 31], [1031, 12, 722, 31, "columnNumber"], [1031, 24, 722, 31], [1032, 10, 722, 31], [1032, 17, 723, 16], [1033, 8, 723, 17], [1034, 10, 723, 17, "fileName"], [1034, 18, 723, 17], [1034, 20, 723, 17, "_jsxFileName"], [1034, 32, 723, 17], [1035, 10, 723, 17, "lineNumber"], [1035, 20, 723, 17], [1036, 10, 723, 17, "columnNumber"], [1036, 22, 723, 17], [1037, 8, 723, 17], [1037, 15, 724, 14], [1038, 6, 724, 15], [1039, 8, 724, 15, "fileName"], [1039, 16, 724, 15], [1039, 18, 724, 15, "_jsxFileName"], [1039, 30, 724, 15], [1040, 8, 724, 15, "lineNumber"], [1040, 18, 724, 15], [1041, 8, 724, 15, "columnNumber"], [1041, 20, 724, 15], [1042, 6, 724, 15], [1042, 13, 725, 13], [1042, 14, 725, 14], [1043, 4, 725, 14], [1044, 6, 725, 14, "fileName"], [1044, 14, 725, 14], [1044, 16, 725, 14, "_jsxFileName"], [1044, 28, 725, 14], [1045, 6, 725, 14, "lineNumber"], [1045, 16, 725, 14], [1046, 6, 725, 14, "columnNumber"], [1046, 18, 725, 14], [1047, 4, 725, 14], [1047, 11, 726, 10], [1047, 12, 726, 11], [1048, 2, 728, 0], [1049, 2, 728, 1, "_s"], [1049, 4, 728, 1], [1049, 5, 51, 24, "EchoCameraWeb"], [1049, 18, 51, 37], [1050, 4, 51, 37], [1050, 12, 58, 42, "useCameraPermissions"], [1050, 44, 58, 62], [1050, 46, 72, 19, "useUpload"], [1050, 64, 72, 28], [1051, 2, 72, 28], [1052, 2, 72, 28, "_c"], [1052, 4, 72, 28], [1052, 7, 51, 24, "EchoCameraWeb"], [1052, 20, 51, 37], [1053, 2, 729, 0], [1053, 8, 729, 6, "styles"], [1053, 14, 729, 12], [1053, 17, 729, 15, "StyleSheet"], [1053, 36, 729, 25], [1053, 37, 729, 26, "create"], [1053, 43, 729, 32], [1053, 44, 729, 33], [1054, 4, 730, 2, "container"], [1054, 13, 730, 11], [1054, 15, 730, 13], [1055, 6, 731, 4, "flex"], [1055, 10, 731, 8], [1055, 12, 731, 10], [1055, 13, 731, 11], [1056, 6, 732, 4, "backgroundColor"], [1056, 21, 732, 19], [1056, 23, 732, 21], [1057, 4, 733, 2], [1057, 5, 733, 3], [1058, 4, 734, 2, "cameraContainer"], [1058, 19, 734, 17], [1058, 21, 734, 19], [1059, 6, 735, 4, "flex"], [1059, 10, 735, 8], [1059, 12, 735, 10], [1059, 13, 735, 11], [1060, 6, 736, 4, "max<PERSON><PERSON><PERSON>"], [1060, 14, 736, 12], [1060, 16, 736, 14], [1060, 19, 736, 17], [1061, 6, 737, 4, "alignSelf"], [1061, 15, 737, 13], [1061, 17, 737, 15], [1061, 25, 737, 23], [1062, 6, 738, 4, "width"], [1062, 11, 738, 9], [1062, 13, 738, 11], [1063, 4, 739, 2], [1063, 5, 739, 3], [1064, 4, 740, 2, "camera"], [1064, 10, 740, 8], [1064, 12, 740, 10], [1065, 6, 741, 4, "flex"], [1065, 10, 741, 8], [1065, 12, 741, 10], [1066, 4, 742, 2], [1066, 5, 742, 3], [1067, 4, 743, 2, "headerOverlay"], [1067, 17, 743, 15], [1067, 19, 743, 17], [1068, 6, 744, 4, "position"], [1068, 14, 744, 12], [1068, 16, 744, 14], [1068, 26, 744, 24], [1069, 6, 745, 4, "top"], [1069, 9, 745, 7], [1069, 11, 745, 9], [1069, 12, 745, 10], [1070, 6, 746, 4, "left"], [1070, 10, 746, 8], [1070, 12, 746, 10], [1070, 13, 746, 11], [1071, 6, 747, 4, "right"], [1071, 11, 747, 9], [1071, 13, 747, 11], [1071, 14, 747, 12], [1072, 6, 748, 4, "backgroundColor"], [1072, 21, 748, 19], [1072, 23, 748, 21], [1072, 36, 748, 34], [1073, 6, 749, 4, "paddingTop"], [1073, 16, 749, 14], [1073, 18, 749, 16], [1073, 20, 749, 18], [1074, 6, 750, 4, "paddingHorizontal"], [1074, 23, 750, 21], [1074, 25, 750, 23], [1074, 27, 750, 25], [1075, 6, 751, 4, "paddingBottom"], [1075, 19, 751, 17], [1075, 21, 751, 19], [1076, 4, 752, 2], [1076, 5, 752, 3], [1077, 4, 753, 2, "headerContent"], [1077, 17, 753, 15], [1077, 19, 753, 17], [1078, 6, 754, 4, "flexDirection"], [1078, 19, 754, 17], [1078, 21, 754, 19], [1078, 26, 754, 24], [1079, 6, 755, 4, "justifyContent"], [1079, 20, 755, 18], [1079, 22, 755, 20], [1079, 37, 755, 35], [1080, 6, 756, 4, "alignItems"], [1080, 16, 756, 14], [1080, 18, 756, 16], [1081, 4, 757, 2], [1081, 5, 757, 3], [1082, 4, 758, 2, "headerLeft"], [1082, 14, 758, 12], [1082, 16, 758, 14], [1083, 6, 759, 4, "flex"], [1083, 10, 759, 8], [1083, 12, 759, 10], [1084, 4, 760, 2], [1084, 5, 760, 3], [1085, 4, 761, 2, "headerTitle"], [1085, 15, 761, 13], [1085, 17, 761, 15], [1086, 6, 762, 4, "fontSize"], [1086, 14, 762, 12], [1086, 16, 762, 14], [1086, 18, 762, 16], [1087, 6, 763, 4, "fontWeight"], [1087, 16, 763, 14], [1087, 18, 763, 16], [1087, 23, 763, 21], [1088, 6, 764, 4, "color"], [1088, 11, 764, 9], [1088, 13, 764, 11], [1088, 19, 764, 17], [1089, 6, 765, 4, "marginBottom"], [1089, 18, 765, 16], [1089, 20, 765, 18], [1090, 4, 766, 2], [1090, 5, 766, 3], [1091, 4, 767, 2, "subtitleRow"], [1091, 15, 767, 13], [1091, 17, 767, 15], [1092, 6, 768, 4, "flexDirection"], [1092, 19, 768, 17], [1092, 21, 768, 19], [1092, 26, 768, 24], [1093, 6, 769, 4, "alignItems"], [1093, 16, 769, 14], [1093, 18, 769, 16], [1093, 26, 769, 24], [1094, 6, 770, 4, "marginBottom"], [1094, 18, 770, 16], [1094, 20, 770, 18], [1095, 4, 771, 2], [1095, 5, 771, 3], [1096, 4, 772, 2, "webIcon"], [1096, 11, 772, 9], [1096, 13, 772, 11], [1097, 6, 773, 4, "fontSize"], [1097, 14, 773, 12], [1097, 16, 773, 14], [1097, 18, 773, 16], [1098, 6, 774, 4, "marginRight"], [1098, 17, 774, 15], [1098, 19, 774, 17], [1099, 4, 775, 2], [1099, 5, 775, 3], [1100, 4, 776, 2, "headerSubtitle"], [1100, 18, 776, 16], [1100, 20, 776, 18], [1101, 6, 777, 4, "fontSize"], [1101, 14, 777, 12], [1101, 16, 777, 14], [1101, 18, 777, 16], [1102, 6, 778, 4, "color"], [1102, 11, 778, 9], [1102, 13, 778, 11], [1102, 19, 778, 17], [1103, 6, 779, 4, "opacity"], [1103, 13, 779, 11], [1103, 15, 779, 13], [1104, 4, 780, 2], [1104, 5, 780, 3], [1105, 4, 781, 2, "challengeRow"], [1105, 16, 781, 14], [1105, 18, 781, 16], [1106, 6, 782, 4, "flexDirection"], [1106, 19, 782, 17], [1106, 21, 782, 19], [1106, 26, 782, 24], [1107, 6, 783, 4, "alignItems"], [1107, 16, 783, 14], [1107, 18, 783, 16], [1108, 4, 784, 2], [1108, 5, 784, 3], [1109, 4, 785, 2, "challengeCode"], [1109, 17, 785, 15], [1109, 19, 785, 17], [1110, 6, 786, 4, "fontSize"], [1110, 14, 786, 12], [1110, 16, 786, 14], [1110, 18, 786, 16], [1111, 6, 787, 4, "color"], [1111, 11, 787, 9], [1111, 13, 787, 11], [1111, 19, 787, 17], [1112, 6, 788, 4, "marginLeft"], [1112, 16, 788, 14], [1112, 18, 788, 16], [1112, 19, 788, 17], [1113, 6, 789, 4, "fontFamily"], [1113, 16, 789, 14], [1113, 18, 789, 16], [1114, 4, 790, 2], [1114, 5, 790, 3], [1115, 4, 791, 2, "closeButton"], [1115, 15, 791, 13], [1115, 17, 791, 15], [1116, 6, 792, 4, "padding"], [1116, 13, 792, 11], [1116, 15, 792, 13], [1117, 4, 793, 2], [1117, 5, 793, 3], [1118, 4, 794, 2, "privacyNotice"], [1118, 17, 794, 15], [1118, 19, 794, 17], [1119, 6, 795, 4, "position"], [1119, 14, 795, 12], [1119, 16, 795, 14], [1119, 26, 795, 24], [1120, 6, 796, 4, "top"], [1120, 9, 796, 7], [1120, 11, 796, 9], [1120, 14, 796, 12], [1121, 6, 797, 4, "left"], [1121, 10, 797, 8], [1121, 12, 797, 10], [1121, 14, 797, 12], [1122, 6, 798, 4, "right"], [1122, 11, 798, 9], [1122, 13, 798, 11], [1122, 15, 798, 13], [1123, 6, 799, 4, "backgroundColor"], [1123, 21, 799, 19], [1123, 23, 799, 21], [1123, 48, 799, 46], [1124, 6, 800, 4, "borderRadius"], [1124, 18, 800, 16], [1124, 20, 800, 18], [1124, 21, 800, 19], [1125, 6, 801, 4, "padding"], [1125, 13, 801, 11], [1125, 15, 801, 13], [1125, 17, 801, 15], [1126, 6, 802, 4, "flexDirection"], [1126, 19, 802, 17], [1126, 21, 802, 19], [1126, 26, 802, 24], [1127, 6, 803, 4, "alignItems"], [1127, 16, 803, 14], [1127, 18, 803, 16], [1128, 4, 804, 2], [1128, 5, 804, 3], [1129, 4, 805, 2, "privacyText"], [1129, 15, 805, 13], [1129, 17, 805, 15], [1130, 6, 806, 4, "color"], [1130, 11, 806, 9], [1130, 13, 806, 11], [1130, 19, 806, 17], [1131, 6, 807, 4, "fontSize"], [1131, 14, 807, 12], [1131, 16, 807, 14], [1131, 18, 807, 16], [1132, 6, 808, 4, "marginLeft"], [1132, 16, 808, 14], [1132, 18, 808, 16], [1132, 19, 808, 17], [1133, 6, 809, 4, "flex"], [1133, 10, 809, 8], [1133, 12, 809, 10], [1134, 4, 810, 2], [1134, 5, 810, 3], [1135, 4, 811, 2, "footer<PERSON><PERSON><PERSON>"], [1135, 17, 811, 15], [1135, 19, 811, 17], [1136, 6, 812, 4, "position"], [1136, 14, 812, 12], [1136, 16, 812, 14], [1136, 26, 812, 24], [1137, 6, 813, 4, "bottom"], [1137, 12, 813, 10], [1137, 14, 813, 12], [1137, 15, 813, 13], [1138, 6, 814, 4, "left"], [1138, 10, 814, 8], [1138, 12, 814, 10], [1138, 13, 814, 11], [1139, 6, 815, 4, "right"], [1139, 11, 815, 9], [1139, 13, 815, 11], [1139, 14, 815, 12], [1140, 6, 816, 4, "backgroundColor"], [1140, 21, 816, 19], [1140, 23, 816, 21], [1140, 36, 816, 34], [1141, 6, 817, 4, "paddingBottom"], [1141, 19, 817, 17], [1141, 21, 817, 19], [1141, 23, 817, 21], [1142, 6, 818, 4, "paddingTop"], [1142, 16, 818, 14], [1142, 18, 818, 16], [1142, 20, 818, 18], [1143, 6, 819, 4, "alignItems"], [1143, 16, 819, 14], [1143, 18, 819, 16], [1144, 4, 820, 2], [1144, 5, 820, 3], [1145, 4, 821, 2, "instruction"], [1145, 15, 821, 13], [1145, 17, 821, 15], [1146, 6, 822, 4, "fontSize"], [1146, 14, 822, 12], [1146, 16, 822, 14], [1146, 18, 822, 16], [1147, 6, 823, 4, "color"], [1147, 11, 823, 9], [1147, 13, 823, 11], [1147, 19, 823, 17], [1148, 6, 824, 4, "marginBottom"], [1148, 18, 824, 16], [1148, 20, 824, 18], [1149, 4, 825, 2], [1149, 5, 825, 3], [1150, 4, 826, 2, "shutterButton"], [1150, 17, 826, 15], [1150, 19, 826, 17], [1151, 6, 827, 4, "width"], [1151, 11, 827, 9], [1151, 13, 827, 11], [1151, 15, 827, 13], [1152, 6, 828, 4, "height"], [1152, 12, 828, 10], [1152, 14, 828, 12], [1152, 16, 828, 14], [1153, 6, 829, 4, "borderRadius"], [1153, 18, 829, 16], [1153, 20, 829, 18], [1153, 22, 829, 20], [1154, 6, 830, 4, "backgroundColor"], [1154, 21, 830, 19], [1154, 23, 830, 21], [1154, 29, 830, 27], [1155, 6, 831, 4, "justifyContent"], [1155, 20, 831, 18], [1155, 22, 831, 20], [1155, 30, 831, 28], [1156, 6, 832, 4, "alignItems"], [1156, 16, 832, 14], [1156, 18, 832, 16], [1156, 26, 832, 24], [1157, 6, 833, 4, "marginBottom"], [1157, 18, 833, 16], [1157, 20, 833, 18], [1157, 22, 833, 20], [1158, 6, 834, 4], [1158, 9, 834, 7, "Platform"], [1158, 26, 834, 15], [1158, 27, 834, 16, "select"], [1158, 33, 834, 22], [1158, 34, 834, 23], [1159, 8, 835, 6, "ios"], [1159, 11, 835, 9], [1159, 13, 835, 11], [1160, 10, 836, 8, "shadowColor"], [1160, 21, 836, 19], [1160, 23, 836, 21], [1160, 32, 836, 30], [1161, 10, 837, 8, "shadowOffset"], [1161, 22, 837, 20], [1161, 24, 837, 22], [1162, 12, 837, 24, "width"], [1162, 17, 837, 29], [1162, 19, 837, 31], [1162, 20, 837, 32], [1163, 12, 837, 34, "height"], [1163, 18, 837, 40], [1163, 20, 837, 42], [1164, 10, 837, 44], [1164, 11, 837, 45], [1165, 10, 838, 8, "shadowOpacity"], [1165, 23, 838, 21], [1165, 25, 838, 23], [1165, 28, 838, 26], [1166, 10, 839, 8, "shadowRadius"], [1166, 22, 839, 20], [1166, 24, 839, 22], [1167, 8, 840, 6], [1167, 9, 840, 7], [1168, 8, 841, 6, "android"], [1168, 15, 841, 13], [1168, 17, 841, 15], [1169, 10, 842, 8, "elevation"], [1169, 19, 842, 17], [1169, 21, 842, 19], [1170, 8, 843, 6], [1170, 9, 843, 7], [1171, 8, 844, 6, "web"], [1171, 11, 844, 9], [1171, 13, 844, 11], [1172, 10, 845, 8, "boxShadow"], [1172, 19, 845, 17], [1172, 21, 845, 19], [1173, 8, 846, 6], [1174, 6, 847, 4], [1174, 7, 847, 5], [1175, 4, 848, 2], [1175, 5, 848, 3], [1176, 4, 849, 2, "shutterButtonDisabled"], [1176, 25, 849, 23], [1176, 27, 849, 25], [1177, 6, 850, 4, "opacity"], [1177, 13, 850, 11], [1177, 15, 850, 13], [1178, 4, 851, 2], [1178, 5, 851, 3], [1179, 4, 852, 2, "shutterInner"], [1179, 16, 852, 14], [1179, 18, 852, 16], [1180, 6, 853, 4, "width"], [1180, 11, 853, 9], [1180, 13, 853, 11], [1180, 15, 853, 13], [1181, 6, 854, 4, "height"], [1181, 12, 854, 10], [1181, 14, 854, 12], [1181, 16, 854, 14], [1182, 6, 855, 4, "borderRadius"], [1182, 18, 855, 16], [1182, 20, 855, 18], [1182, 22, 855, 20], [1183, 6, 856, 4, "backgroundColor"], [1183, 21, 856, 19], [1183, 23, 856, 21], [1183, 29, 856, 27], [1184, 6, 857, 4, "borderWidth"], [1184, 17, 857, 15], [1184, 19, 857, 17], [1184, 20, 857, 18], [1185, 6, 858, 4, "borderColor"], [1185, 17, 858, 15], [1185, 19, 858, 17], [1186, 4, 859, 2], [1186, 5, 859, 3], [1187, 4, 860, 2, "privacyNote"], [1187, 15, 860, 13], [1187, 17, 860, 15], [1188, 6, 861, 4, "fontSize"], [1188, 14, 861, 12], [1188, 16, 861, 14], [1188, 18, 861, 16], [1189, 6, 862, 4, "color"], [1189, 11, 862, 9], [1189, 13, 862, 11], [1190, 4, 863, 2], [1190, 5, 863, 3], [1191, 4, 864, 2, "processingModal"], [1191, 19, 864, 17], [1191, 21, 864, 19], [1192, 6, 865, 4, "flex"], [1192, 10, 865, 8], [1192, 12, 865, 10], [1192, 13, 865, 11], [1193, 6, 866, 4, "backgroundColor"], [1193, 21, 866, 19], [1193, 23, 866, 21], [1193, 43, 866, 41], [1194, 6, 867, 4, "justifyContent"], [1194, 20, 867, 18], [1194, 22, 867, 20], [1194, 30, 867, 28], [1195, 6, 868, 4, "alignItems"], [1195, 16, 868, 14], [1195, 18, 868, 16], [1196, 4, 869, 2], [1196, 5, 869, 3], [1197, 4, 870, 2, "processingContent"], [1197, 21, 870, 19], [1197, 23, 870, 21], [1198, 6, 871, 4, "backgroundColor"], [1198, 21, 871, 19], [1198, 23, 871, 21], [1198, 29, 871, 27], [1199, 6, 872, 4, "borderRadius"], [1199, 18, 872, 16], [1199, 20, 872, 18], [1199, 22, 872, 20], [1200, 6, 873, 4, "padding"], [1200, 13, 873, 11], [1200, 15, 873, 13], [1200, 17, 873, 15], [1201, 6, 874, 4, "width"], [1201, 11, 874, 9], [1201, 13, 874, 11], [1201, 18, 874, 16], [1202, 6, 875, 4, "max<PERSON><PERSON><PERSON>"], [1202, 14, 875, 12], [1202, 16, 875, 14], [1202, 19, 875, 17], [1203, 6, 876, 4, "alignItems"], [1203, 16, 876, 14], [1203, 18, 876, 16], [1204, 4, 877, 2], [1204, 5, 877, 3], [1205, 4, 878, 2, "processingTitle"], [1205, 19, 878, 17], [1205, 21, 878, 19], [1206, 6, 879, 4, "fontSize"], [1206, 14, 879, 12], [1206, 16, 879, 14], [1206, 18, 879, 16], [1207, 6, 880, 4, "fontWeight"], [1207, 16, 880, 14], [1207, 18, 880, 16], [1207, 23, 880, 21], [1208, 6, 881, 4, "color"], [1208, 11, 881, 9], [1208, 13, 881, 11], [1208, 22, 881, 20], [1209, 6, 882, 4, "marginTop"], [1209, 15, 882, 13], [1209, 17, 882, 15], [1209, 19, 882, 17], [1210, 6, 883, 4, "marginBottom"], [1210, 18, 883, 16], [1210, 20, 883, 18], [1211, 4, 884, 2], [1211, 5, 884, 3], [1212, 4, 885, 2, "progressBar"], [1212, 15, 885, 13], [1212, 17, 885, 15], [1213, 6, 886, 4, "width"], [1213, 11, 886, 9], [1213, 13, 886, 11], [1213, 19, 886, 17], [1214, 6, 887, 4, "height"], [1214, 12, 887, 10], [1214, 14, 887, 12], [1214, 15, 887, 13], [1215, 6, 888, 4, "backgroundColor"], [1215, 21, 888, 19], [1215, 23, 888, 21], [1215, 32, 888, 30], [1216, 6, 889, 4, "borderRadius"], [1216, 18, 889, 16], [1216, 20, 889, 18], [1216, 21, 889, 19], [1217, 6, 890, 4, "overflow"], [1217, 14, 890, 12], [1217, 16, 890, 14], [1217, 24, 890, 22], [1218, 6, 891, 4, "marginBottom"], [1218, 18, 891, 16], [1218, 20, 891, 18], [1219, 4, 892, 2], [1219, 5, 892, 3], [1220, 4, 893, 2, "progressFill"], [1220, 16, 893, 14], [1220, 18, 893, 16], [1221, 6, 894, 4, "height"], [1221, 12, 894, 10], [1221, 14, 894, 12], [1221, 20, 894, 18], [1222, 6, 895, 4, "backgroundColor"], [1222, 21, 895, 19], [1222, 23, 895, 21], [1222, 32, 895, 30], [1223, 6, 896, 4, "borderRadius"], [1223, 18, 896, 16], [1223, 20, 896, 18], [1224, 4, 897, 2], [1224, 5, 897, 3], [1225, 4, 898, 2, "processingDescription"], [1225, 25, 898, 23], [1225, 27, 898, 25], [1226, 6, 899, 4, "fontSize"], [1226, 14, 899, 12], [1226, 16, 899, 14], [1226, 18, 899, 16], [1227, 6, 900, 4, "color"], [1227, 11, 900, 9], [1227, 13, 900, 11], [1227, 22, 900, 20], [1228, 6, 901, 4, "textAlign"], [1228, 15, 901, 13], [1228, 17, 901, 15], [1229, 4, 902, 2], [1229, 5, 902, 3], [1230, 4, 903, 2, "successIcon"], [1230, 15, 903, 13], [1230, 17, 903, 15], [1231, 6, 904, 4, "marginTop"], [1231, 15, 904, 13], [1231, 17, 904, 15], [1232, 4, 905, 2], [1232, 5, 905, 3], [1233, 4, 906, 2, "errorContent"], [1233, 16, 906, 14], [1233, 18, 906, 16], [1234, 6, 907, 4, "backgroundColor"], [1234, 21, 907, 19], [1234, 23, 907, 21], [1234, 29, 907, 27], [1235, 6, 908, 4, "borderRadius"], [1235, 18, 908, 16], [1235, 20, 908, 18], [1235, 22, 908, 20], [1236, 6, 909, 4, "padding"], [1236, 13, 909, 11], [1236, 15, 909, 13], [1236, 17, 909, 15], [1237, 6, 910, 4, "width"], [1237, 11, 910, 9], [1237, 13, 910, 11], [1237, 18, 910, 16], [1238, 6, 911, 4, "max<PERSON><PERSON><PERSON>"], [1238, 14, 911, 12], [1238, 16, 911, 14], [1238, 19, 911, 17], [1239, 6, 912, 4, "alignItems"], [1239, 16, 912, 14], [1239, 18, 912, 16], [1240, 4, 913, 2], [1240, 5, 913, 3], [1241, 4, 914, 2, "errorTitle"], [1241, 14, 914, 12], [1241, 16, 914, 14], [1242, 6, 915, 4, "fontSize"], [1242, 14, 915, 12], [1242, 16, 915, 14], [1242, 18, 915, 16], [1243, 6, 916, 4, "fontWeight"], [1243, 16, 916, 14], [1243, 18, 916, 16], [1243, 23, 916, 21], [1244, 6, 917, 4, "color"], [1244, 11, 917, 9], [1244, 13, 917, 11], [1244, 22, 917, 20], [1245, 6, 918, 4, "marginTop"], [1245, 15, 918, 13], [1245, 17, 918, 15], [1245, 19, 918, 17], [1246, 6, 919, 4, "marginBottom"], [1246, 18, 919, 16], [1246, 20, 919, 18], [1247, 4, 920, 2], [1247, 5, 920, 3], [1248, 4, 921, 2, "errorMessage"], [1248, 16, 921, 14], [1248, 18, 921, 16], [1249, 6, 922, 4, "fontSize"], [1249, 14, 922, 12], [1249, 16, 922, 14], [1249, 18, 922, 16], [1250, 6, 923, 4, "color"], [1250, 11, 923, 9], [1250, 13, 923, 11], [1250, 22, 923, 20], [1251, 6, 924, 4, "textAlign"], [1251, 15, 924, 13], [1251, 17, 924, 15], [1251, 25, 924, 23], [1252, 6, 925, 4, "marginBottom"], [1252, 18, 925, 16], [1252, 20, 925, 18], [1253, 4, 926, 2], [1253, 5, 926, 3], [1254, 4, 927, 2, "primaryButton"], [1254, 17, 927, 15], [1254, 19, 927, 17], [1255, 6, 928, 4, "backgroundColor"], [1255, 21, 928, 19], [1255, 23, 928, 21], [1255, 32, 928, 30], [1256, 6, 929, 4, "paddingHorizontal"], [1256, 23, 929, 21], [1256, 25, 929, 23], [1256, 27, 929, 25], [1257, 6, 930, 4, "paddingVertical"], [1257, 21, 930, 19], [1257, 23, 930, 21], [1257, 25, 930, 23], [1258, 6, 931, 4, "borderRadius"], [1258, 18, 931, 16], [1258, 20, 931, 18], [1258, 21, 931, 19], [1259, 6, 932, 4, "marginTop"], [1259, 15, 932, 13], [1259, 17, 932, 15], [1260, 4, 933, 2], [1260, 5, 933, 3], [1261, 4, 934, 2, "primaryButtonText"], [1261, 21, 934, 19], [1261, 23, 934, 21], [1262, 6, 935, 4, "color"], [1262, 11, 935, 9], [1262, 13, 935, 11], [1262, 19, 935, 17], [1263, 6, 936, 4, "fontSize"], [1263, 14, 936, 12], [1263, 16, 936, 14], [1263, 18, 936, 16], [1264, 6, 937, 4, "fontWeight"], [1264, 16, 937, 14], [1264, 18, 937, 16], [1265, 4, 938, 2], [1265, 5, 938, 3], [1266, 4, 939, 2, "secondaryButton"], [1266, 19, 939, 17], [1266, 21, 939, 19], [1267, 6, 940, 4, "paddingHorizontal"], [1267, 23, 940, 21], [1267, 25, 940, 23], [1267, 27, 940, 25], [1268, 6, 941, 4, "paddingVertical"], [1268, 21, 941, 19], [1268, 23, 941, 21], [1268, 25, 941, 23], [1269, 6, 942, 4, "marginTop"], [1269, 15, 942, 13], [1269, 17, 942, 15], [1270, 4, 943, 2], [1270, 5, 943, 3], [1271, 4, 944, 2, "secondaryButtonText"], [1271, 23, 944, 21], [1271, 25, 944, 23], [1272, 6, 945, 4, "color"], [1272, 11, 945, 9], [1272, 13, 945, 11], [1272, 22, 945, 20], [1273, 6, 946, 4, "fontSize"], [1273, 14, 946, 12], [1273, 16, 946, 14], [1274, 4, 947, 2], [1274, 5, 947, 3], [1275, 4, 948, 2, "permissionContent"], [1275, 21, 948, 19], [1275, 23, 948, 21], [1276, 6, 949, 4, "flex"], [1276, 10, 949, 8], [1276, 12, 949, 10], [1276, 13, 949, 11], [1277, 6, 950, 4, "justifyContent"], [1277, 20, 950, 18], [1277, 22, 950, 20], [1277, 30, 950, 28], [1278, 6, 951, 4, "alignItems"], [1278, 16, 951, 14], [1278, 18, 951, 16], [1278, 26, 951, 24], [1279, 6, 952, 4, "padding"], [1279, 13, 952, 11], [1279, 15, 952, 13], [1280, 4, 953, 2], [1280, 5, 953, 3], [1281, 4, 954, 2, "permissionTitle"], [1281, 19, 954, 17], [1281, 21, 954, 19], [1282, 6, 955, 4, "fontSize"], [1282, 14, 955, 12], [1282, 16, 955, 14], [1282, 18, 955, 16], [1283, 6, 956, 4, "fontWeight"], [1283, 16, 956, 14], [1283, 18, 956, 16], [1283, 23, 956, 21], [1284, 6, 957, 4, "color"], [1284, 11, 957, 9], [1284, 13, 957, 11], [1284, 22, 957, 20], [1285, 6, 958, 4, "marginTop"], [1285, 15, 958, 13], [1285, 17, 958, 15], [1285, 19, 958, 17], [1286, 6, 959, 4, "marginBottom"], [1286, 18, 959, 16], [1286, 20, 959, 18], [1287, 4, 960, 2], [1287, 5, 960, 3], [1288, 4, 961, 2, "permissionDescription"], [1288, 25, 961, 23], [1288, 27, 961, 25], [1289, 6, 962, 4, "fontSize"], [1289, 14, 962, 12], [1289, 16, 962, 14], [1289, 18, 962, 16], [1290, 6, 963, 4, "color"], [1290, 11, 963, 9], [1290, 13, 963, 11], [1290, 22, 963, 20], [1291, 6, 964, 4, "textAlign"], [1291, 15, 964, 13], [1291, 17, 964, 15], [1291, 25, 964, 23], [1292, 6, 965, 4, "marginBottom"], [1292, 18, 965, 16], [1292, 20, 965, 18], [1293, 4, 966, 2], [1293, 5, 966, 3], [1294, 4, 967, 2, "loadingText"], [1294, 15, 967, 13], [1294, 17, 967, 15], [1295, 6, 968, 4, "color"], [1295, 11, 968, 9], [1295, 13, 968, 11], [1295, 22, 968, 20], [1296, 6, 969, 4, "marginTop"], [1296, 15, 969, 13], [1296, 17, 969, 15], [1297, 4, 970, 2], [1297, 5, 970, 3], [1298, 4, 971, 2], [1299, 4, 972, 2, "blurZone"], [1299, 12, 972, 10], [1299, 14, 972, 12], [1300, 6, 973, 4, "position"], [1300, 14, 973, 12], [1300, 16, 973, 14], [1300, 26, 973, 24], [1301, 6, 974, 4, "overflow"], [1301, 14, 974, 12], [1301, 16, 974, 14], [1302, 4, 975, 2], [1302, 5, 975, 3], [1303, 4, 976, 2, "previewChip"], [1303, 15, 976, 13], [1303, 17, 976, 15], [1304, 6, 977, 4, "position"], [1304, 14, 977, 12], [1304, 16, 977, 14], [1304, 26, 977, 24], [1305, 6, 978, 4, "top"], [1305, 9, 978, 7], [1305, 11, 978, 9], [1305, 12, 978, 10], [1306, 6, 979, 4, "right"], [1306, 11, 979, 9], [1306, 13, 979, 11], [1306, 14, 979, 12], [1307, 6, 980, 4, "backgroundColor"], [1307, 21, 980, 19], [1307, 23, 980, 21], [1307, 40, 980, 38], [1308, 6, 981, 4, "paddingHorizontal"], [1308, 23, 981, 21], [1308, 25, 981, 23], [1308, 27, 981, 25], [1309, 6, 982, 4, "paddingVertical"], [1309, 21, 982, 19], [1309, 23, 982, 21], [1309, 24, 982, 22], [1310, 6, 983, 4, "borderRadius"], [1310, 18, 983, 16], [1310, 20, 983, 18], [1311, 4, 984, 2], [1311, 5, 984, 3], [1312, 4, 985, 2, "previewChipText"], [1312, 19, 985, 17], [1312, 21, 985, 19], [1313, 6, 986, 4, "color"], [1313, 11, 986, 9], [1313, 13, 986, 11], [1313, 19, 986, 17], [1314, 6, 987, 4, "fontSize"], [1314, 14, 987, 12], [1314, 16, 987, 14], [1314, 18, 987, 16], [1315, 6, 988, 4, "fontWeight"], [1315, 16, 988, 14], [1315, 18, 988, 16], [1316, 4, 989, 2], [1317, 2, 990, 0], [1317, 3, 990, 1], [1317, 4, 990, 2], [1318, 2, 990, 3], [1318, 6, 990, 3, "_c"], [1318, 8, 990, 3], [1319, 2, 990, 3, "$RefreshReg$"], [1319, 14, 990, 3], [1319, 15, 990, 3, "_c"], [1319, 17, 990, 3], [1320, 0, 990, 3], [1320, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "capturePhoto", "Promise$argument_0", "processImageWithFaceBlur", "browserDetections.map$argument_0", "faceDetections.map$argument_0", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;mCGE;wBCc,kCD;GHkC;mCKE;wBDY;OCI;gDCwB;YDO;8BDY;aCM;6CEc;YFO;oFGW;UHM;8BIS;SJgD;uDDQ;sBMC,wBN;OCC;GLc;6BWG;GXyB;kCYG;GZ8C;4BaE;mBCmD;SDE;GbO;uBeE;GfI;mCgBG;GhBM;YCE;GDK;oBiB2C;WjBG;yBkBC;WlBG;wBmBC;WnBI;CD4L"}}, "type": "js/module"}]}