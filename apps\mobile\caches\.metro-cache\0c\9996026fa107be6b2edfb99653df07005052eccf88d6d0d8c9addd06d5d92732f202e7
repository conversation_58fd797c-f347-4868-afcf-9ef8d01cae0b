{"dependencies": [{"name": "./Coordinates", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "sIVxZvNHFmYZfO7p7LGJvl+4EqA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.rotate = void 0;\n  var _Coordinates = require(_dependencyMap[0], \"./Coordinates\");\n  const _worklet_16224485897640_init_data = {\n    code: \"function TransformsJs1(tr,origin,rotation){const{canvas2Polar,polar2Canvas}=this.__closure;const{radius:radius,theta:theta}=canvas2Polar(tr,origin);return polar2Canvas({radius:radius,theta:theta+rotation},origin);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\renderer\\\\processors\\\\math\\\\Transforms.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"TransformsJs1\\\",\\\"tr\\\",\\\"origin\\\",\\\"rotation\\\",\\\"canvas2Polar\\\",\\\"polar2Canvas\\\",\\\"__closure\\\",\\\"radius\\\",\\\"theta\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/renderer/processors/math/Transforms.js\\\"],\\\"mappings\\\":\\\"AACsB,QAAC,CAAAA,aAAUA,CAAEC,EAAA,CAAAC,MAAa,CAAAC,QAAA,QAAAC,YAAA,CAAAC,YAAA,OAAAC,SAAA,CAG9C,KAAM,CACJC,MAAM,CAANA,MAAM,CACNC,KAAA,CAAAA,KACF,CAAC,CAAGJ,YAAY,CAACH,EAAE,CAAEC,MAAM,CAAC,CAC5B,MAAO,CAAAG,YAAY,CAAC,CAClBE,MAAM,CAANA,MAAM,CACNC,KAAK,CAAEA,KAAK,CAAGL,QACjB,CAAC,CAAED,MAAM,CAAC,CACZ\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const rotate = exports.rotate = function () {\n    const _e = [new global.Error(), -3, -27];\n    const TransformsJs1 = function (tr, origin, rotation) {\n      const {\n        radius,\n        theta\n      } = (0, _Coordinates.canvas2Polar)(tr, origin);\n      return (0, _Coordinates.polar2Canvas)({\n        radius,\n        theta: theta + rotation\n      }, origin);\n    };\n    TransformsJs1.__closure = {\n      canvas2Polar: _Coordinates.canvas2Polar,\n      polar2Canvas: _Coordinates.polar2Canvas\n    };\n    TransformsJs1.__workletHash = 16224485897640;\n    TransformsJs1.__initData = _worklet_16224485897640_init_data;\n    TransformsJs1.__stackDetails = _e;\n    return TransformsJs1;\n  }();\n});", "lineCount": 34, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Coordinates"], [6, 18, 1, 0], [6, 21, 1, 0, "require"], [6, 28, 1, 0], [6, 29, 1, 0, "_dependencyMap"], [6, 43, 1, 0], [7, 2, 1, 59], [7, 8, 1, 59, "_worklet_16224485897640_init_data"], [7, 41, 1, 59], [8, 4, 1, 59, "code"], [8, 8, 1, 59], [9, 4, 1, 59, "location"], [9, 12, 1, 59], [10, 4, 1, 59, "sourceMap"], [10, 13, 1, 59], [11, 4, 1, 59, "version"], [11, 11, 1, 59], [12, 2, 1, 59], [13, 2, 2, 7], [13, 8, 2, 13, "rotate"], [13, 14, 2, 19], [13, 17, 2, 19, "exports"], [13, 24, 2, 19], [13, 25, 2, 19, "rotate"], [13, 31, 2, 19], [13, 34, 2, 22], [14, 4, 2, 22], [14, 10, 2, 22, "_e"], [14, 12, 2, 22], [14, 20, 2, 22, "global"], [14, 26, 2, 22], [14, 27, 2, 22, "Error"], [14, 32, 2, 22], [15, 4, 2, 22], [15, 10, 2, 22, "TransformsJs1"], [15, 23, 2, 22], [15, 35, 2, 22, "TransformsJs1"], [15, 36, 2, 23, "tr"], [15, 38, 2, 25], [15, 40, 2, 27, "origin"], [15, 46, 2, 33], [15, 48, 2, 35, "rotation"], [15, 56, 2, 43], [15, 58, 2, 48], [16, 6, 5, 2], [16, 12, 5, 8], [17, 8, 6, 4, "radius"], [17, 14, 6, 10], [18, 8, 7, 4, "theta"], [19, 6, 8, 2], [19, 7, 8, 3], [19, 10, 8, 6], [19, 14, 8, 6, "canvas2Polar"], [19, 39, 8, 18], [19, 41, 8, 19, "tr"], [19, 43, 8, 21], [19, 45, 8, 23, "origin"], [19, 51, 8, 29], [19, 52, 8, 30], [20, 6, 9, 2], [20, 13, 9, 9], [20, 17, 9, 9, "polar2Canvas"], [20, 42, 9, 21], [20, 44, 9, 22], [21, 8, 10, 4, "radius"], [21, 14, 10, 10], [22, 8, 11, 4, "theta"], [22, 13, 11, 9], [22, 15, 11, 11, "theta"], [22, 20, 11, 16], [22, 23, 11, 19, "rotation"], [23, 6, 12, 2], [23, 7, 12, 3], [23, 9, 12, 5, "origin"], [23, 15, 12, 11], [23, 16, 12, 12], [24, 4, 13, 0], [24, 5, 13, 1], [25, 4, 13, 1, "TransformsJs1"], [25, 17, 13, 1], [25, 18, 13, 1, "__closure"], [25, 27, 13, 1], [26, 6, 13, 1, "canvas2Polar"], [26, 18, 13, 1], [26, 20, 8, 6, "canvas2Polar"], [26, 45, 8, 18], [27, 6, 8, 18, "polar2Canvas"], [27, 18, 8, 18], [27, 20, 9, 9, "polar2Canvas"], [28, 4, 9, 21], [29, 4, 9, 21, "TransformsJs1"], [29, 17, 9, 21], [29, 18, 9, 21, "__workletHash"], [29, 31, 9, 21], [30, 4, 9, 21, "TransformsJs1"], [30, 17, 9, 21], [30, 18, 9, 21, "__initData"], [30, 28, 9, 21], [30, 31, 9, 21, "_worklet_16224485897640_init_data"], [30, 64, 9, 21], [31, 4, 9, 21, "TransformsJs1"], [31, 17, 9, 21], [31, 18, 9, 21, "__stackDetails"], [31, 32, 9, 21], [31, 35, 9, 21, "_e"], [31, 37, 9, 21], [32, 4, 9, 21], [32, 11, 9, 21, "TransformsJs1"], [32, 24, 9, 21], [33, 2, 9, 21], [33, 3, 2, 22], [33, 5, 13, 1], [34, 0, 13, 2], [34, 3]], "functionMap": {"names": ["<global>", "rotate"], "mappings": "AAA;sBCC;CDW"}}, "type": "js/module"}]}