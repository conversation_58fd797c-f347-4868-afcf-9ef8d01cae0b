{"dependencies": [{"name": "../types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 41, "index": 41}}], "key": "SiqkZ9nARqNkdXfcIWbBgsKp5Yo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkParagraphStyle = void 0;\n  var _types = require(_dependencyMap[0], \"../types\");\n  class JsiSkParagraphStyle {\n    static toParagraphStyle(ck, value) {\n      var _value$disableHinting, _value$ellipsis, _value$heightMultipli, _value$maxLines, _value$replaceTabChar, _ps$strutStyle, _value$strutStyle$fon, _value$strutStyle, _value$strutStyle$fon2, _value$strutStyle2, _value$strutStyle$hei, _value$strutStyle3, _value$strutStyle$lea, _value$strutStyle4, _value$strutStyle$for, _value$strutStyle5, _ps$strutStyle$fontSt, _value$strutStyle6, _value$strutStyle7, _value$strutStyle8, _value$strutStyle$hal, _value$strutStyle9, _value$strutStyle$str, _value$strutStyle10;\n      // Seems like we need to provide the textStyle.color value, otherwise\n      // the constructor crashes.\n      const ps = new ck.ParagraphStyle({\n        textStyle: {\n          color: ck.BLACK\n        }\n      });\n      ps.disableHinting = (_value$disableHinting = value.disableHinting) !== null && _value$disableHinting !== void 0 ? _value$disableHinting : ps.disableHinting;\n      ps.ellipsis = (_value$ellipsis = value.ellipsis) !== null && _value$ellipsis !== void 0 ? _value$ellipsis : ps.ellipsis;\n      ps.heightMultiplier = (_value$heightMultipli = value.heightMultiplier) !== null && _value$heightMultipli !== void 0 ? _value$heightMultipli : ps.heightMultiplier;\n      ps.maxLines = (_value$maxLines = value.maxLines) !== null && _value$maxLines !== void 0 ? _value$maxLines : ps.maxLines;\n      ps.replaceTabCharacters = (_value$replaceTabChar = value.replaceTabCharacters) !== null && _value$replaceTabChar !== void 0 ? _value$replaceTabChar : ps.replaceTabCharacters;\n      ps.textAlign = value.textAlign !== undefined ? {\n        value: value.textAlign\n      } : ps.textAlign;\n      ps.textDirection = value.textDirection !== undefined ? {\n        value: value.textDirection === _types.TextDirection.LTR ? 1 : 0\n      } : ps.textDirection;\n      ps.textHeightBehavior = value.textHeightBehavior !== undefined ? {\n        value: value.textHeightBehavior\n      } : ps.textHeightBehavior;\n      ps.strutStyle = (_ps$strutStyle = ps.strutStyle) !== null && _ps$strutStyle !== void 0 ? _ps$strutStyle : {};\n      ps.strutStyle.fontFamilies = (_value$strutStyle$fon = (_value$strutStyle = value.strutStyle) === null || _value$strutStyle === void 0 ? void 0 : _value$strutStyle.fontFamilies) !== null && _value$strutStyle$fon !== void 0 ? _value$strutStyle$fon : ps.strutStyle.fontFamilies;\n      ps.strutStyle.fontSize = (_value$strutStyle$fon2 = (_value$strutStyle2 = value.strutStyle) === null || _value$strutStyle2 === void 0 ? void 0 : _value$strutStyle2.fontSize) !== null && _value$strutStyle$fon2 !== void 0 ? _value$strutStyle$fon2 : ps.strutStyle.fontSize;\n      ps.strutStyle.heightMultiplier = (_value$strutStyle$hei = (_value$strutStyle3 = value.strutStyle) === null || _value$strutStyle3 === void 0 ? void 0 : _value$strutStyle3.heightMultiplier) !== null && _value$strutStyle$hei !== void 0 ? _value$strutStyle$hei : ps.strutStyle.heightMultiplier;\n      ps.strutStyle.leading = (_value$strutStyle$lea = (_value$strutStyle4 = value.strutStyle) === null || _value$strutStyle4 === void 0 ? void 0 : _value$strutStyle4.leading) !== null && _value$strutStyle$lea !== void 0 ? _value$strutStyle$lea : ps.strutStyle.leading;\n      ps.strutStyle.forceStrutHeight = (_value$strutStyle$for = (_value$strutStyle5 = value.strutStyle) === null || _value$strutStyle5 === void 0 ? void 0 : _value$strutStyle5.forceStrutHeight) !== null && _value$strutStyle$for !== void 0 ? _value$strutStyle$for : ps.strutStyle.forceStrutHeight;\n      ps.strutStyle.fontStyle = (_ps$strutStyle$fontSt = ps.strutStyle.fontStyle) !== null && _ps$strutStyle$fontSt !== void 0 ? _ps$strutStyle$fontSt : {};\n      ps.strutStyle.fontStyle.slant = ((_value$strutStyle6 = value.strutStyle) === null || _value$strutStyle6 === void 0 || (_value$strutStyle6 = _value$strutStyle6.fontStyle) === null || _value$strutStyle6 === void 0 ? void 0 : _value$strutStyle6.slant) !== undefined ? {\n        value: value.strutStyle.fontStyle.slant\n      } : ps.strutStyle.fontStyle.slant;\n      ps.strutStyle.fontStyle.width = ((_value$strutStyle7 = value.strutStyle) === null || _value$strutStyle7 === void 0 || (_value$strutStyle7 = _value$strutStyle7.fontStyle) === null || _value$strutStyle7 === void 0 ? void 0 : _value$strutStyle7.width) !== undefined ? {\n        value: value.strutStyle.fontStyle.width\n      } : ps.strutStyle.fontStyle.width;\n      ps.strutStyle.fontStyle.weight = ((_value$strutStyle8 = value.strutStyle) === null || _value$strutStyle8 === void 0 || (_value$strutStyle8 = _value$strutStyle8.fontStyle) === null || _value$strutStyle8 === void 0 ? void 0 : _value$strutStyle8.weight) !== undefined ? {\n        value: value.strutStyle.fontStyle.weight\n      } : ps.strutStyle.fontStyle.weight;\n      ps.strutStyle.halfLeading = (_value$strutStyle$hal = (_value$strutStyle9 = value.strutStyle) === null || _value$strutStyle9 === void 0 ? void 0 : _value$strutStyle9.halfLeading) !== null && _value$strutStyle$hal !== void 0 ? _value$strutStyle$hal : ps.strutStyle.halfLeading;\n      ps.strutStyle.strutEnabled = (_value$strutStyle$str = (_value$strutStyle10 = value.strutStyle) === null || _value$strutStyle10 === void 0 ? void 0 : _value$strutStyle10.strutEnabled) !== null && _value$strutStyle$str !== void 0 ? _value$strutStyle$str : ps.strutStyle.strutEnabled;\n      return ps;\n    }\n  }\n  exports.JsiSkParagraphStyle = JsiSkParagraphStyle;\n});", "lineCount": 53, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_types"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 2, 7], [7, 8, 2, 13, "JsiSkParagraphStyle"], [7, 27, 2, 32], [7, 28, 2, 33], [8, 4, 3, 2], [8, 11, 3, 9, "toParagraphStyle"], [8, 27, 3, 25, "toParagraphStyle"], [8, 28, 3, 26, "ck"], [8, 30, 3, 28], [8, 32, 3, 30, "value"], [8, 37, 3, 35], [8, 39, 3, 37], [9, 6, 4, 4], [9, 10, 4, 8, "_value$disableHinting"], [9, 31, 4, 29], [9, 33, 4, 31, "_value$ellipsis"], [9, 48, 4, 46], [9, 50, 4, 48, "_value$heightMultipli"], [9, 71, 4, 69], [9, 73, 4, 71, "_value$maxLines"], [9, 88, 4, 86], [9, 90, 4, 88, "_value$replaceTabChar"], [9, 111, 4, 109], [9, 113, 4, 111, "_ps$strutStyle"], [9, 127, 4, 125], [9, 129, 4, 127, "_value$strutStyle$fon"], [9, 150, 4, 148], [9, 152, 4, 150, "_value$strutStyle"], [9, 169, 4, 167], [9, 171, 4, 169, "_value$strutStyle$fon2"], [9, 193, 4, 191], [9, 195, 4, 193, "_value$strutStyle2"], [9, 213, 4, 211], [9, 215, 4, 213, "_value$strutStyle$hei"], [9, 236, 4, 234], [9, 238, 4, 236, "_value$strutStyle3"], [9, 256, 4, 254], [9, 258, 4, 256, "_value$strutStyle$lea"], [9, 279, 4, 277], [9, 281, 4, 279, "_value$strutStyle4"], [9, 299, 4, 297], [9, 301, 4, 299, "_value$strutStyle$for"], [9, 322, 4, 320], [9, 324, 4, 322, "_value$strutStyle5"], [9, 342, 4, 340], [9, 344, 4, 342, "_ps$strutStyle$fontSt"], [9, 365, 4, 363], [9, 367, 4, 365, "_value$strutStyle6"], [9, 385, 4, 383], [9, 387, 4, 385, "_value$strutStyle7"], [9, 405, 4, 403], [9, 407, 4, 405, "_value$strutStyle8"], [9, 425, 4, 423], [9, 427, 4, 425, "_value$strutStyle$hal"], [9, 448, 4, 446], [9, 450, 4, 448, "_value$strutStyle9"], [9, 468, 4, 466], [9, 470, 4, 468, "_value$strutStyle$str"], [9, 491, 4, 489], [9, 493, 4, 491, "_value$strutStyle10"], [9, 512, 4, 510], [10, 6, 5, 4], [11, 6, 6, 4], [12, 6, 7, 4], [12, 12, 7, 10, "ps"], [12, 14, 7, 12], [12, 17, 7, 15], [12, 21, 7, 19, "ck"], [12, 23, 7, 21], [12, 24, 7, 22, "ParagraphStyle"], [12, 38, 7, 36], [12, 39, 7, 37], [13, 8, 8, 6, "textStyle"], [13, 17, 8, 15], [13, 19, 8, 17], [14, 10, 9, 8, "color"], [14, 15, 9, 13], [14, 17, 9, 15, "ck"], [14, 19, 9, 17], [14, 20, 9, 18, "BLACK"], [15, 8, 10, 6], [16, 6, 11, 4], [16, 7, 11, 5], [16, 8, 11, 6], [17, 6, 12, 4, "ps"], [17, 8, 12, 6], [17, 9, 12, 7, "disableHinting"], [17, 23, 12, 21], [17, 26, 12, 24], [17, 27, 12, 25, "_value$disableHinting"], [17, 48, 12, 46], [17, 51, 12, 49, "value"], [17, 56, 12, 54], [17, 57, 12, 55, "disableHinting"], [17, 71, 12, 69], [17, 77, 12, 75], [17, 81, 12, 79], [17, 85, 12, 83, "_value$disableHinting"], [17, 106, 12, 104], [17, 111, 12, 109], [17, 116, 12, 114], [17, 117, 12, 115], [17, 120, 12, 118, "_value$disableHinting"], [17, 141, 12, 139], [17, 144, 12, 142, "ps"], [17, 146, 12, 144], [17, 147, 12, 145, "disableHinting"], [17, 161, 12, 159], [18, 6, 13, 4, "ps"], [18, 8, 13, 6], [18, 9, 13, 7, "ellipsis"], [18, 17, 13, 15], [18, 20, 13, 18], [18, 21, 13, 19, "_value$ellipsis"], [18, 36, 13, 34], [18, 39, 13, 37, "value"], [18, 44, 13, 42], [18, 45, 13, 43, "ellipsis"], [18, 53, 13, 51], [18, 59, 13, 57], [18, 63, 13, 61], [18, 67, 13, 65, "_value$ellipsis"], [18, 82, 13, 80], [18, 87, 13, 85], [18, 92, 13, 90], [18, 93, 13, 91], [18, 96, 13, 94, "_value$ellipsis"], [18, 111, 13, 109], [18, 114, 13, 112, "ps"], [18, 116, 13, 114], [18, 117, 13, 115, "ellipsis"], [18, 125, 13, 123], [19, 6, 14, 4, "ps"], [19, 8, 14, 6], [19, 9, 14, 7, "heightMultiplier"], [19, 25, 14, 23], [19, 28, 14, 26], [19, 29, 14, 27, "_value$heightMultipli"], [19, 50, 14, 48], [19, 53, 14, 51, "value"], [19, 58, 14, 56], [19, 59, 14, 57, "heightMultiplier"], [19, 75, 14, 73], [19, 81, 14, 79], [19, 85, 14, 83], [19, 89, 14, 87, "_value$heightMultipli"], [19, 110, 14, 108], [19, 115, 14, 113], [19, 120, 14, 118], [19, 121, 14, 119], [19, 124, 14, 122, "_value$heightMultipli"], [19, 145, 14, 143], [19, 148, 14, 146, "ps"], [19, 150, 14, 148], [19, 151, 14, 149, "heightMultiplier"], [19, 167, 14, 165], [20, 6, 15, 4, "ps"], [20, 8, 15, 6], [20, 9, 15, 7, "maxLines"], [20, 17, 15, 15], [20, 20, 15, 18], [20, 21, 15, 19, "_value$maxLines"], [20, 36, 15, 34], [20, 39, 15, 37, "value"], [20, 44, 15, 42], [20, 45, 15, 43, "maxLines"], [20, 53, 15, 51], [20, 59, 15, 57], [20, 63, 15, 61], [20, 67, 15, 65, "_value$maxLines"], [20, 82, 15, 80], [20, 87, 15, 85], [20, 92, 15, 90], [20, 93, 15, 91], [20, 96, 15, 94, "_value$maxLines"], [20, 111, 15, 109], [20, 114, 15, 112, "ps"], [20, 116, 15, 114], [20, 117, 15, 115, "maxLines"], [20, 125, 15, 123], [21, 6, 16, 4, "ps"], [21, 8, 16, 6], [21, 9, 16, 7, "replaceTabCharacters"], [21, 29, 16, 27], [21, 32, 16, 30], [21, 33, 16, 31, "_value$replaceTabChar"], [21, 54, 16, 52], [21, 57, 16, 55, "value"], [21, 62, 16, 60], [21, 63, 16, 61, "replaceTabCharacters"], [21, 83, 16, 81], [21, 89, 16, 87], [21, 93, 16, 91], [21, 97, 16, 95, "_value$replaceTabChar"], [21, 118, 16, 116], [21, 123, 16, 121], [21, 128, 16, 126], [21, 129, 16, 127], [21, 132, 16, 130, "_value$replaceTabChar"], [21, 153, 16, 151], [21, 156, 16, 154, "ps"], [21, 158, 16, 156], [21, 159, 16, 157, "replaceTabCharacters"], [21, 179, 16, 177], [22, 6, 17, 4, "ps"], [22, 8, 17, 6], [22, 9, 17, 7, "textAlign"], [22, 18, 17, 16], [22, 21, 17, 19, "value"], [22, 26, 17, 24], [22, 27, 17, 25, "textAlign"], [22, 36, 17, 34], [22, 41, 17, 39, "undefined"], [22, 50, 17, 48], [22, 53, 17, 51], [23, 8, 18, 6, "value"], [23, 13, 18, 11], [23, 15, 18, 13, "value"], [23, 20, 18, 18], [23, 21, 18, 19, "textAlign"], [24, 6, 19, 4], [24, 7, 19, 5], [24, 10, 19, 8, "ps"], [24, 12, 19, 10], [24, 13, 19, 11, "textAlign"], [24, 22, 19, 20], [25, 6, 20, 4, "ps"], [25, 8, 20, 6], [25, 9, 20, 7, "textDirection"], [25, 22, 20, 20], [25, 25, 20, 23, "value"], [25, 30, 20, 28], [25, 31, 20, 29, "textDirection"], [25, 44, 20, 42], [25, 49, 20, 47, "undefined"], [25, 58, 20, 56], [25, 61, 20, 59], [26, 8, 21, 6, "value"], [26, 13, 21, 11], [26, 15, 21, 13, "value"], [26, 20, 21, 18], [26, 21, 21, 19, "textDirection"], [26, 34, 21, 32], [26, 39, 21, 37, "TextDirection"], [26, 59, 21, 50], [26, 60, 21, 51, "LTR"], [26, 63, 21, 54], [26, 66, 21, 57], [26, 67, 21, 58], [26, 70, 21, 61], [27, 6, 22, 4], [27, 7, 22, 5], [27, 10, 22, 8, "ps"], [27, 12, 22, 10], [27, 13, 22, 11, "textDirection"], [27, 26, 22, 24], [28, 6, 23, 4, "ps"], [28, 8, 23, 6], [28, 9, 23, 7, "textHeightBehavior"], [28, 27, 23, 25], [28, 30, 23, 28, "value"], [28, 35, 23, 33], [28, 36, 23, 34, "textHeightBehavior"], [28, 54, 23, 52], [28, 59, 23, 57, "undefined"], [28, 68, 23, 66], [28, 71, 23, 69], [29, 8, 24, 6, "value"], [29, 13, 24, 11], [29, 15, 24, 13, "value"], [29, 20, 24, 18], [29, 21, 24, 19, "textHeightBehavior"], [30, 6, 25, 4], [30, 7, 25, 5], [30, 10, 25, 8, "ps"], [30, 12, 25, 10], [30, 13, 25, 11, "textHeightBehavior"], [30, 31, 25, 29], [31, 6, 26, 4, "ps"], [31, 8, 26, 6], [31, 9, 26, 7, "strutStyle"], [31, 19, 26, 17], [31, 22, 26, 20], [31, 23, 26, 21, "_ps$strutStyle"], [31, 37, 26, 35], [31, 40, 26, 38, "ps"], [31, 42, 26, 40], [31, 43, 26, 41, "strutStyle"], [31, 53, 26, 51], [31, 59, 26, 57], [31, 63, 26, 61], [31, 67, 26, 65, "_ps$strutStyle"], [31, 81, 26, 79], [31, 86, 26, 84], [31, 91, 26, 89], [31, 92, 26, 90], [31, 95, 26, 93, "_ps$strutStyle"], [31, 109, 26, 107], [31, 112, 26, 110], [31, 113, 26, 111], [31, 114, 26, 112], [32, 6, 27, 4, "ps"], [32, 8, 27, 6], [32, 9, 27, 7, "strutStyle"], [32, 19, 27, 17], [32, 20, 27, 18, "fontFamilies"], [32, 32, 27, 30], [32, 35, 27, 33], [32, 36, 27, 34, "_value$strutStyle$fon"], [32, 57, 27, 55], [32, 60, 27, 58], [32, 61, 27, 59, "_value$strutStyle"], [32, 78, 27, 76], [32, 81, 27, 79, "value"], [32, 86, 27, 84], [32, 87, 27, 85, "strutStyle"], [32, 97, 27, 95], [32, 103, 27, 101], [32, 107, 27, 105], [32, 111, 27, 109, "_value$strutStyle"], [32, 128, 27, 126], [32, 133, 27, 131], [32, 138, 27, 136], [32, 139, 27, 137], [32, 142, 27, 140], [32, 147, 27, 145], [32, 148, 27, 146], [32, 151, 27, 149, "_value$strutStyle"], [32, 168, 27, 166], [32, 169, 27, 167, "fontFamilies"], [32, 181, 27, 179], [32, 187, 27, 185], [32, 191, 27, 189], [32, 195, 27, 193, "_value$strutStyle$fon"], [32, 216, 27, 214], [32, 221, 27, 219], [32, 226, 27, 224], [32, 227, 27, 225], [32, 230, 27, 228, "_value$strutStyle$fon"], [32, 251, 27, 249], [32, 254, 27, 252, "ps"], [32, 256, 27, 254], [32, 257, 27, 255, "strutStyle"], [32, 267, 27, 265], [32, 268, 27, 266, "fontFamilies"], [32, 280, 27, 278], [33, 6, 28, 4, "ps"], [33, 8, 28, 6], [33, 9, 28, 7, "strutStyle"], [33, 19, 28, 17], [33, 20, 28, 18, "fontSize"], [33, 28, 28, 26], [33, 31, 28, 29], [33, 32, 28, 30, "_value$strutStyle$fon2"], [33, 54, 28, 52], [33, 57, 28, 55], [33, 58, 28, 56, "_value$strutStyle2"], [33, 76, 28, 74], [33, 79, 28, 77, "value"], [33, 84, 28, 82], [33, 85, 28, 83, "strutStyle"], [33, 95, 28, 93], [33, 101, 28, 99], [33, 105, 28, 103], [33, 109, 28, 107, "_value$strutStyle2"], [33, 127, 28, 125], [33, 132, 28, 130], [33, 137, 28, 135], [33, 138, 28, 136], [33, 141, 28, 139], [33, 146, 28, 144], [33, 147, 28, 145], [33, 150, 28, 148, "_value$strutStyle2"], [33, 168, 28, 166], [33, 169, 28, 167, "fontSize"], [33, 177, 28, 175], [33, 183, 28, 181], [33, 187, 28, 185], [33, 191, 28, 189, "_value$strutStyle$fon2"], [33, 213, 28, 211], [33, 218, 28, 216], [33, 223, 28, 221], [33, 224, 28, 222], [33, 227, 28, 225, "_value$strutStyle$fon2"], [33, 249, 28, 247], [33, 252, 28, 250, "ps"], [33, 254, 28, 252], [33, 255, 28, 253, "strutStyle"], [33, 265, 28, 263], [33, 266, 28, 264, "fontSize"], [33, 274, 28, 272], [34, 6, 29, 4, "ps"], [34, 8, 29, 6], [34, 9, 29, 7, "strutStyle"], [34, 19, 29, 17], [34, 20, 29, 18, "heightMultiplier"], [34, 36, 29, 34], [34, 39, 29, 37], [34, 40, 29, 38, "_value$strutStyle$hei"], [34, 61, 29, 59], [34, 64, 29, 62], [34, 65, 29, 63, "_value$strutStyle3"], [34, 83, 29, 81], [34, 86, 29, 84, "value"], [34, 91, 29, 89], [34, 92, 29, 90, "strutStyle"], [34, 102, 29, 100], [34, 108, 29, 106], [34, 112, 29, 110], [34, 116, 29, 114, "_value$strutStyle3"], [34, 134, 29, 132], [34, 139, 29, 137], [34, 144, 29, 142], [34, 145, 29, 143], [34, 148, 29, 146], [34, 153, 29, 151], [34, 154, 29, 152], [34, 157, 29, 155, "_value$strutStyle3"], [34, 175, 29, 173], [34, 176, 29, 174, "heightMultiplier"], [34, 192, 29, 190], [34, 198, 29, 196], [34, 202, 29, 200], [34, 206, 29, 204, "_value$strutStyle$hei"], [34, 227, 29, 225], [34, 232, 29, 230], [34, 237, 29, 235], [34, 238, 29, 236], [34, 241, 29, 239, "_value$strutStyle$hei"], [34, 262, 29, 260], [34, 265, 29, 263, "ps"], [34, 267, 29, 265], [34, 268, 29, 266, "strutStyle"], [34, 278, 29, 276], [34, 279, 29, 277, "heightMultiplier"], [34, 295, 29, 293], [35, 6, 30, 4, "ps"], [35, 8, 30, 6], [35, 9, 30, 7, "strutStyle"], [35, 19, 30, 17], [35, 20, 30, 18, "leading"], [35, 27, 30, 25], [35, 30, 30, 28], [35, 31, 30, 29, "_value$strutStyle$lea"], [35, 52, 30, 50], [35, 55, 30, 53], [35, 56, 30, 54, "_value$strutStyle4"], [35, 74, 30, 72], [35, 77, 30, 75, "value"], [35, 82, 30, 80], [35, 83, 30, 81, "strutStyle"], [35, 93, 30, 91], [35, 99, 30, 97], [35, 103, 30, 101], [35, 107, 30, 105, "_value$strutStyle4"], [35, 125, 30, 123], [35, 130, 30, 128], [35, 135, 30, 133], [35, 136, 30, 134], [35, 139, 30, 137], [35, 144, 30, 142], [35, 145, 30, 143], [35, 148, 30, 146, "_value$strutStyle4"], [35, 166, 30, 164], [35, 167, 30, 165, "leading"], [35, 174, 30, 172], [35, 180, 30, 178], [35, 184, 30, 182], [35, 188, 30, 186, "_value$strutStyle$lea"], [35, 209, 30, 207], [35, 214, 30, 212], [35, 219, 30, 217], [35, 220, 30, 218], [35, 223, 30, 221, "_value$strutStyle$lea"], [35, 244, 30, 242], [35, 247, 30, 245, "ps"], [35, 249, 30, 247], [35, 250, 30, 248, "strutStyle"], [35, 260, 30, 258], [35, 261, 30, 259, "leading"], [35, 268, 30, 266], [36, 6, 31, 4, "ps"], [36, 8, 31, 6], [36, 9, 31, 7, "strutStyle"], [36, 19, 31, 17], [36, 20, 31, 18, "forceStrutHeight"], [36, 36, 31, 34], [36, 39, 31, 37], [36, 40, 31, 38, "_value$strutStyle$for"], [36, 61, 31, 59], [36, 64, 31, 62], [36, 65, 31, 63, "_value$strutStyle5"], [36, 83, 31, 81], [36, 86, 31, 84, "value"], [36, 91, 31, 89], [36, 92, 31, 90, "strutStyle"], [36, 102, 31, 100], [36, 108, 31, 106], [36, 112, 31, 110], [36, 116, 31, 114, "_value$strutStyle5"], [36, 134, 31, 132], [36, 139, 31, 137], [36, 144, 31, 142], [36, 145, 31, 143], [36, 148, 31, 146], [36, 153, 31, 151], [36, 154, 31, 152], [36, 157, 31, 155, "_value$strutStyle5"], [36, 175, 31, 173], [36, 176, 31, 174, "forceStrutHeight"], [36, 192, 31, 190], [36, 198, 31, 196], [36, 202, 31, 200], [36, 206, 31, 204, "_value$strutStyle$for"], [36, 227, 31, 225], [36, 232, 31, 230], [36, 237, 31, 235], [36, 238, 31, 236], [36, 241, 31, 239, "_value$strutStyle$for"], [36, 262, 31, 260], [36, 265, 31, 263, "ps"], [36, 267, 31, 265], [36, 268, 31, 266, "strutStyle"], [36, 278, 31, 276], [36, 279, 31, 277, "forceStrutHeight"], [36, 295, 31, 293], [37, 6, 32, 4, "ps"], [37, 8, 32, 6], [37, 9, 32, 7, "strutStyle"], [37, 19, 32, 17], [37, 20, 32, 18, "fontStyle"], [37, 29, 32, 27], [37, 32, 32, 30], [37, 33, 32, 31, "_ps$strutStyle$fontSt"], [37, 54, 32, 52], [37, 57, 32, 55, "ps"], [37, 59, 32, 57], [37, 60, 32, 58, "strutStyle"], [37, 70, 32, 68], [37, 71, 32, 69, "fontStyle"], [37, 80, 32, 78], [37, 86, 32, 84], [37, 90, 32, 88], [37, 94, 32, 92, "_ps$strutStyle$fontSt"], [37, 115, 32, 113], [37, 120, 32, 118], [37, 125, 32, 123], [37, 126, 32, 124], [37, 129, 32, 127, "_ps$strutStyle$fontSt"], [37, 150, 32, 148], [37, 153, 32, 151], [37, 154, 32, 152], [37, 155, 32, 153], [38, 6, 33, 4, "ps"], [38, 8, 33, 6], [38, 9, 33, 7, "strutStyle"], [38, 19, 33, 17], [38, 20, 33, 18, "fontStyle"], [38, 29, 33, 27], [38, 30, 33, 28, "slant"], [38, 35, 33, 33], [38, 38, 33, 36], [38, 39, 33, 37], [38, 40, 33, 38, "_value$strutStyle6"], [38, 58, 33, 56], [38, 61, 33, 59, "value"], [38, 66, 33, 64], [38, 67, 33, 65, "strutStyle"], [38, 77, 33, 75], [38, 83, 33, 81], [38, 87, 33, 85], [38, 91, 33, 89, "_value$strutStyle6"], [38, 109, 33, 107], [38, 114, 33, 112], [38, 119, 33, 117], [38, 120, 33, 118], [38, 124, 33, 122], [38, 125, 33, 123, "_value$strutStyle6"], [38, 143, 33, 141], [38, 146, 33, 144, "_value$strutStyle6"], [38, 164, 33, 162], [38, 165, 33, 163, "fontStyle"], [38, 174, 33, 172], [38, 180, 33, 178], [38, 184, 33, 182], [38, 188, 33, 186, "_value$strutStyle6"], [38, 206, 33, 204], [38, 211, 33, 209], [38, 216, 33, 214], [38, 217, 33, 215], [38, 220, 33, 218], [38, 225, 33, 223], [38, 226, 33, 224], [38, 229, 33, 227, "_value$strutStyle6"], [38, 247, 33, 245], [38, 248, 33, 246, "slant"], [38, 253, 33, 251], [38, 259, 33, 257, "undefined"], [38, 268, 33, 266], [38, 271, 33, 269], [39, 8, 34, 6, "value"], [39, 13, 34, 11], [39, 15, 34, 13, "value"], [39, 20, 34, 18], [39, 21, 34, 19, "strutStyle"], [39, 31, 34, 29], [39, 32, 34, 30, "fontStyle"], [39, 41, 34, 39], [39, 42, 34, 40, "slant"], [40, 6, 35, 4], [40, 7, 35, 5], [40, 10, 35, 8, "ps"], [40, 12, 35, 10], [40, 13, 35, 11, "strutStyle"], [40, 23, 35, 21], [40, 24, 35, 22, "fontStyle"], [40, 33, 35, 31], [40, 34, 35, 32, "slant"], [40, 39, 35, 37], [41, 6, 36, 4, "ps"], [41, 8, 36, 6], [41, 9, 36, 7, "strutStyle"], [41, 19, 36, 17], [41, 20, 36, 18, "fontStyle"], [41, 29, 36, 27], [41, 30, 36, 28, "width"], [41, 35, 36, 33], [41, 38, 36, 36], [41, 39, 36, 37], [41, 40, 36, 38, "_value$strutStyle7"], [41, 58, 36, 56], [41, 61, 36, 59, "value"], [41, 66, 36, 64], [41, 67, 36, 65, "strutStyle"], [41, 77, 36, 75], [41, 83, 36, 81], [41, 87, 36, 85], [41, 91, 36, 89, "_value$strutStyle7"], [41, 109, 36, 107], [41, 114, 36, 112], [41, 119, 36, 117], [41, 120, 36, 118], [41, 124, 36, 122], [41, 125, 36, 123, "_value$strutStyle7"], [41, 143, 36, 141], [41, 146, 36, 144, "_value$strutStyle7"], [41, 164, 36, 162], [41, 165, 36, 163, "fontStyle"], [41, 174, 36, 172], [41, 180, 36, 178], [41, 184, 36, 182], [41, 188, 36, 186, "_value$strutStyle7"], [41, 206, 36, 204], [41, 211, 36, 209], [41, 216, 36, 214], [41, 217, 36, 215], [41, 220, 36, 218], [41, 225, 36, 223], [41, 226, 36, 224], [41, 229, 36, 227, "_value$strutStyle7"], [41, 247, 36, 245], [41, 248, 36, 246, "width"], [41, 253, 36, 251], [41, 259, 36, 257, "undefined"], [41, 268, 36, 266], [41, 271, 36, 269], [42, 8, 37, 6, "value"], [42, 13, 37, 11], [42, 15, 37, 13, "value"], [42, 20, 37, 18], [42, 21, 37, 19, "strutStyle"], [42, 31, 37, 29], [42, 32, 37, 30, "fontStyle"], [42, 41, 37, 39], [42, 42, 37, 40, "width"], [43, 6, 38, 4], [43, 7, 38, 5], [43, 10, 38, 8, "ps"], [43, 12, 38, 10], [43, 13, 38, 11, "strutStyle"], [43, 23, 38, 21], [43, 24, 38, 22, "fontStyle"], [43, 33, 38, 31], [43, 34, 38, 32, "width"], [43, 39, 38, 37], [44, 6, 39, 4, "ps"], [44, 8, 39, 6], [44, 9, 39, 7, "strutStyle"], [44, 19, 39, 17], [44, 20, 39, 18, "fontStyle"], [44, 29, 39, 27], [44, 30, 39, 28, "weight"], [44, 36, 39, 34], [44, 39, 39, 37], [44, 40, 39, 38], [44, 41, 39, 39, "_value$strutStyle8"], [44, 59, 39, 57], [44, 62, 39, 60, "value"], [44, 67, 39, 65], [44, 68, 39, 66, "strutStyle"], [44, 78, 39, 76], [44, 84, 39, 82], [44, 88, 39, 86], [44, 92, 39, 90, "_value$strutStyle8"], [44, 110, 39, 108], [44, 115, 39, 113], [44, 120, 39, 118], [44, 121, 39, 119], [44, 125, 39, 123], [44, 126, 39, 124, "_value$strutStyle8"], [44, 144, 39, 142], [44, 147, 39, 145, "_value$strutStyle8"], [44, 165, 39, 163], [44, 166, 39, 164, "fontStyle"], [44, 175, 39, 173], [44, 181, 39, 179], [44, 185, 39, 183], [44, 189, 39, 187, "_value$strutStyle8"], [44, 207, 39, 205], [44, 212, 39, 210], [44, 217, 39, 215], [44, 218, 39, 216], [44, 221, 39, 219], [44, 226, 39, 224], [44, 227, 39, 225], [44, 230, 39, 228, "_value$strutStyle8"], [44, 248, 39, 246], [44, 249, 39, 247, "weight"], [44, 255, 39, 253], [44, 261, 39, 259, "undefined"], [44, 270, 39, 268], [44, 273, 39, 271], [45, 8, 40, 6, "value"], [45, 13, 40, 11], [45, 15, 40, 13, "value"], [45, 20, 40, 18], [45, 21, 40, 19, "strutStyle"], [45, 31, 40, 29], [45, 32, 40, 30, "fontStyle"], [45, 41, 40, 39], [45, 42, 40, 40, "weight"], [46, 6, 41, 4], [46, 7, 41, 5], [46, 10, 41, 8, "ps"], [46, 12, 41, 10], [46, 13, 41, 11, "strutStyle"], [46, 23, 41, 21], [46, 24, 41, 22, "fontStyle"], [46, 33, 41, 31], [46, 34, 41, 32, "weight"], [46, 40, 41, 38], [47, 6, 42, 4, "ps"], [47, 8, 42, 6], [47, 9, 42, 7, "strutStyle"], [47, 19, 42, 17], [47, 20, 42, 18, "halfLeading"], [47, 31, 42, 29], [47, 34, 42, 32], [47, 35, 42, 33, "_value$strutStyle$hal"], [47, 56, 42, 54], [47, 59, 42, 57], [47, 60, 42, 58, "_value$strutStyle9"], [47, 78, 42, 76], [47, 81, 42, 79, "value"], [47, 86, 42, 84], [47, 87, 42, 85, "strutStyle"], [47, 97, 42, 95], [47, 103, 42, 101], [47, 107, 42, 105], [47, 111, 42, 109, "_value$strutStyle9"], [47, 129, 42, 127], [47, 134, 42, 132], [47, 139, 42, 137], [47, 140, 42, 138], [47, 143, 42, 141], [47, 148, 42, 146], [47, 149, 42, 147], [47, 152, 42, 150, "_value$strutStyle9"], [47, 170, 42, 168], [47, 171, 42, 169, "halfLeading"], [47, 182, 42, 180], [47, 188, 42, 186], [47, 192, 42, 190], [47, 196, 42, 194, "_value$strutStyle$hal"], [47, 217, 42, 215], [47, 222, 42, 220], [47, 227, 42, 225], [47, 228, 42, 226], [47, 231, 42, 229, "_value$strutStyle$hal"], [47, 252, 42, 250], [47, 255, 42, 253, "ps"], [47, 257, 42, 255], [47, 258, 42, 256, "strutStyle"], [47, 268, 42, 266], [47, 269, 42, 267, "halfLeading"], [47, 280, 42, 278], [48, 6, 43, 4, "ps"], [48, 8, 43, 6], [48, 9, 43, 7, "strutStyle"], [48, 19, 43, 17], [48, 20, 43, 18, "strutEnabled"], [48, 32, 43, 30], [48, 35, 43, 33], [48, 36, 43, 34, "_value$strutStyle$str"], [48, 57, 43, 55], [48, 60, 43, 58], [48, 61, 43, 59, "_value$strutStyle10"], [48, 80, 43, 78], [48, 83, 43, 81, "value"], [48, 88, 43, 86], [48, 89, 43, 87, "strutStyle"], [48, 99, 43, 97], [48, 105, 43, 103], [48, 109, 43, 107], [48, 113, 43, 111, "_value$strutStyle10"], [48, 132, 43, 130], [48, 137, 43, 135], [48, 142, 43, 140], [48, 143, 43, 141], [48, 146, 43, 144], [48, 151, 43, 149], [48, 152, 43, 150], [48, 155, 43, 153, "_value$strutStyle10"], [48, 174, 43, 172], [48, 175, 43, 173, "strutEnabled"], [48, 187, 43, 185], [48, 193, 43, 191], [48, 197, 43, 195], [48, 201, 43, 199, "_value$strutStyle$str"], [48, 222, 43, 220], [48, 227, 43, 225], [48, 232, 43, 230], [48, 233, 43, 231], [48, 236, 43, 234, "_value$strutStyle$str"], [48, 257, 43, 255], [48, 260, 43, 258, "ps"], [48, 262, 43, 260], [48, 263, 43, 261, "strutStyle"], [48, 273, 43, 271], [48, 274, 43, 272, "strutEnabled"], [48, 286, 43, 284], [49, 6, 44, 4], [49, 13, 44, 11, "ps"], [49, 15, 44, 13], [50, 4, 45, 2], [51, 2, 46, 0], [52, 2, 46, 1, "exports"], [52, 9, 46, 1], [52, 10, 46, 1, "JsiSkParagraphStyle"], [52, 29, 46, 1], [52, 32, 46, 1, "JsiSkParagraphStyle"], [52, 51, 46, 1], [53, 0, 46, 1], [53, 3]], "functionMap": {"names": ["<global>", "JsiSkParagraphStyle", "toParagraphStyle"], "mappings": "AAA;OCC;ECC;GD0C;CDC"}}, "type": "js/module"}]}