{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.OpacityMatrix = exports.ColorMatrix = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  const ColorMatrix = props => {\n    return /*#__PURE__*/_react.default.createElement(\"skMatrixColorFilter\", props);\n  };\n  exports.ColorMatrix = ColorMatrix;\n  const OpacityMatrix = opacity => [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, opacity, 0];\n  exports.OpacityMatrix = OpacityMatrix;\n});", "lineCount": 14, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireDefault"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 2, 7], [8, 8, 2, 13, "ColorMatrix"], [8, 19, 2, 24], [8, 22, 2, 27, "props"], [8, 27, 2, 32], [8, 31, 2, 36], [9, 4, 3, 2], [9, 11, 3, 9], [9, 24, 3, 22, "React"], [9, 38, 3, 27], [9, 39, 3, 28, "createElement"], [9, 52, 3, 41], [9, 53, 3, 42], [9, 74, 3, 63], [9, 76, 3, 65, "props"], [9, 81, 3, 70], [9, 82, 3, 71], [10, 2, 4, 0], [10, 3, 4, 1], [11, 2, 4, 2, "exports"], [11, 9, 4, 2], [11, 10, 4, 2, "ColorMatrix"], [11, 21, 4, 2], [11, 24, 4, 2, "ColorMatrix"], [11, 35, 4, 2], [12, 2, 5, 7], [12, 8, 5, 13, "OpacityMatrix"], [12, 21, 5, 26], [12, 24, 5, 29, "opacity"], [12, 31, 5, 36], [12, 35, 5, 40], [12, 36, 5, 41], [12, 37, 5, 42], [12, 39, 5, 44], [12, 40, 5, 45], [12, 42, 5, 47], [12, 43, 5, 48], [12, 45, 5, 50], [12, 46, 5, 51], [12, 48, 5, 53], [12, 49, 5, 54], [12, 51, 5, 56], [12, 52, 5, 57], [12, 54, 5, 59], [12, 55, 5, 60], [12, 57, 5, 62], [12, 58, 5, 63], [12, 60, 5, 65], [12, 61, 5, 66], [12, 63, 5, 68], [12, 64, 5, 69], [12, 66, 5, 71], [12, 67, 5, 72], [12, 69, 5, 74], [12, 70, 5, 75], [12, 72, 5, 77], [12, 73, 5, 78], [12, 75, 5, 80], [12, 76, 5, 81], [12, 78, 5, 83], [12, 79, 5, 84], [12, 81, 5, 86], [12, 82, 5, 87], [12, 84, 5, 89], [12, 85, 5, 90], [12, 87, 5, 92], [12, 88, 5, 93], [12, 90, 5, 95, "opacity"], [12, 97, 5, 102], [12, 99, 5, 104], [12, 100, 5, 105], [12, 101, 5, 106], [13, 2, 5, 107, "exports"], [13, 9, 5, 107], [13, 10, 5, 107, "OpacityMatrix"], [13, 23, 5, 107], [13, 26, 5, 107, "OpacityMatrix"], [13, 39, 5, 107], [14, 0, 5, 107], [14, 3]], "functionMap": {"names": ["<global>", "ColorMatrix", "OpacityMatrix"], "mappings": "AAA;2BCC;CDE;6BEC,6EF"}}, "type": "js/module"}]}