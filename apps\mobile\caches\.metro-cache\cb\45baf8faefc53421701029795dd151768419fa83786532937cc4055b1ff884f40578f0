{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 43, "index": 43}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../../skia", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 44}, "end": {"line": 2, "column": 34, "index": 78}}], "key": "+q0qwmVtgReRJ1JJKJleyyIYxCs=", "exportNames": ["*"]}}, {"name": "./interpolators", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 79}, "end": {"line": 3, "column": 47, "index": 126}}], "key": "ggIB+SYYgEIkY308CzIkIyVPsho=", "exportNames": ["*"]}}, {"name": "./ReanimatedProxy", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 127}, "end": {"line": 4, "column": 36, "index": 163}}], "key": "9/hMKtQD5ZrdSt9i8tblq1v6X3Y=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useRectBuffer = exports.useRSXformBuffer = exports.usePointBuffer = exports.useColorBuffer = void 0;\n  var _react = require(_dependencyMap[1], \"react\");\n  var _skia = require(_dependencyMap[2], \"../../skia\");\n  var _interpolators = require(_dependencyMap[3], \"./interpolators\");\n  var _ReanimatedProxy = _interopRequireDefault(require(_dependencyMap[4], \"./ReanimatedProxy\"));\n  const useBufferValue = (size, bufferInitializer) => {\n    return (0, _react.useMemo)(() => _ReanimatedProxy.default.makeMutable(new Array(size).fill(0).map(bufferInitializer)),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [size]);\n  };\n  const _worklet_12959593067005_init_data = {\n    code: \"function buffersJs1(){const{values,modifier,notifyChange}=this.__closure;values.value.forEach(function(val,index){modifier(val,index);});notifyChange(values);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\external\\\\reanimated\\\\buffers.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"buffersJs1\\\",\\\"values\\\",\\\"modifier\\\",\\\"notifyChange\\\",\\\"__closure\\\",\\\"value\\\",\\\"forEach\\\",\\\"val\\\",\\\"index\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/external/reanimated/buffers.js\\\"],\\\"mappings\\\":\\\"AAcmC,SAAAA,UAAMA,CAAA,QAAAC,MAAA,CAAAC,QAAA,CAAAC,YAAA,OAAAC,SAAA,CAGrCH,MAAM,CAACI,KAAK,CAACC,OAAO,CAAC,SAACC,GAAG,CAAEC,KAAK,CAAK,CACnCN,QAAQ,CAACK,GAAG,CAAEC,KAAK,CAAC,CACtB,CAAC,CAAC,CACFL,YAAY,CAACF,MAAM,CAAC,CACtB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const useBuffer = (size, bufferInitializer, modifier) => {\n    var _mod$__closure;\n    const values = useBufferValue(size, bufferInitializer);\n    const mod = modifier;\n    const deps = [size, ...Object.values((_mod$__closure = mod.__closure) !== null && _mod$__closure !== void 0 ? _mod$__closure : {})];\n    const mapperId = _ReanimatedProxy.default.startMapper(function () {\n      const _e = [new global.Error(), -4, -27];\n      const buffersJs1 = function () {\n        values.value.forEach((val, index) => {\n          modifier(val, index);\n        });\n        (0, _interpolators.notifyChange)(values);\n      };\n      buffersJs1.__closure = {\n        values,\n        modifier,\n        notifyChange: _interpolators.notifyChange\n      };\n      buffersJs1.__workletHash = 12959593067005;\n      buffersJs1.__initData = _worklet_12959593067005_init_data;\n      buffersJs1.__stackDetails = _e;\n      return buffersJs1;\n    }(), deps);\n    (0, _react.useEffect)(() => {\n      return () => {\n        _ReanimatedProxy.default.stopMapper(mapperId);\n      };\n    }, [mapperId]);\n    return values;\n  };\n  const useRectBuffer = (size, modifier) => useBuffer(size, () => _skia.Skia.XYWHRect(0, 0, 0, 0), modifier);\n\n  // Usage for RSXform Buffer\n  exports.useRectBuffer = useRectBuffer;\n  const useRSXformBuffer = (size, modifier) => useBuffer(size, () => _skia.Skia.RSXform(1, 0, 0, 0), modifier);\n\n  // Usage for Point Buffer\n  exports.useRSXformBuffer = useRSXformBuffer;\n  const usePointBuffer = (size, modifier) => useBuffer(size, () => _skia.Skia.Point(0, 0), modifier);\n\n  // Usage for Color Buffer\n  exports.usePointBuffer = usePointBuffer;\n  const useColorBuffer = (size, modifier) => useBuffer(size, () => _skia.Skia.Color(\"black\"), modifier);\n  exports.useColorBuffer = useColorBuffer;\n});", "lineCount": 66, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "require"], [7, 22, 1, 0], [7, 23, 1, 0, "_dependencyMap"], [7, 37, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_skia"], [8, 11, 2, 0], [8, 14, 2, 0, "require"], [8, 21, 2, 0], [8, 22, 2, 0, "_dependencyMap"], [8, 36, 2, 0], [9, 2, 3, 0], [9, 6, 3, 0, "_interpolators"], [9, 20, 3, 0], [9, 23, 3, 0, "require"], [9, 30, 3, 0], [9, 31, 3, 0, "_dependencyMap"], [9, 45, 3, 0], [10, 2, 4, 0], [10, 6, 4, 0, "_ReanimatedProxy"], [10, 22, 4, 0], [10, 25, 4, 0, "_interopRequireDefault"], [10, 47, 4, 0], [10, 48, 4, 0, "require"], [10, 55, 4, 0], [10, 56, 4, 0, "_dependencyMap"], [10, 70, 4, 0], [11, 2, 5, 0], [11, 8, 5, 6, "useBufferValue"], [11, 22, 5, 20], [11, 25, 5, 23, "useBufferValue"], [11, 26, 5, 24, "size"], [11, 30, 5, 28], [11, 32, 5, 30, "bufferInitializer"], [11, 49, 5, 47], [11, 54, 5, 52], [12, 4, 6, 2], [12, 11, 6, 9], [12, 15, 6, 9, "useMemo"], [12, 29, 6, 16], [12, 31, 6, 17], [12, 37, 6, 23, "<PERSON><PERSON>"], [12, 61, 6, 26], [12, 62, 6, 27, "makeMutable"], [12, 73, 6, 38], [12, 74, 6, 39], [12, 78, 6, 43, "Array"], [12, 83, 6, 48], [12, 84, 6, 49, "size"], [12, 88, 6, 53], [12, 89, 6, 54], [12, 90, 6, 55, "fill"], [12, 94, 6, 59], [12, 95, 6, 60], [12, 96, 6, 61], [12, 97, 6, 62], [12, 98, 6, 63, "map"], [12, 101, 6, 66], [12, 102, 6, 67, "bufferInitializer"], [12, 119, 6, 84], [12, 120, 6, 85], [12, 121, 6, 86], [13, 4, 7, 2], [14, 4, 8, 2], [14, 5, 8, 3, "size"], [14, 9, 8, 7], [14, 10, 8, 8], [14, 11, 8, 9], [15, 2, 9, 0], [15, 3, 9, 1], [16, 2, 9, 2], [16, 8, 9, 2, "_worklet_12959593067005_init_data"], [16, 41, 9, 2], [17, 4, 9, 2, "code"], [17, 8, 9, 2], [18, 4, 9, 2, "location"], [18, 12, 9, 2], [19, 4, 9, 2, "sourceMap"], [19, 13, 9, 2], [20, 4, 9, 2, "version"], [20, 11, 9, 2], [21, 2, 9, 2], [22, 2, 10, 0], [22, 8, 10, 6, "useBuffer"], [22, 17, 10, 15], [22, 20, 10, 18, "useBuffer"], [22, 21, 10, 19, "size"], [22, 25, 10, 23], [22, 27, 10, 25, "bufferInitializer"], [22, 44, 10, 42], [22, 46, 10, 44, "modifier"], [22, 54, 10, 52], [22, 59, 10, 57], [23, 4, 11, 2], [23, 8, 11, 6, "_mod$__closure"], [23, 22, 11, 20], [24, 4, 12, 2], [24, 10, 12, 8, "values"], [24, 16, 12, 14], [24, 19, 12, 17, "useBufferValue"], [24, 33, 12, 31], [24, 34, 12, 32, "size"], [24, 38, 12, 36], [24, 40, 12, 38, "bufferInitializer"], [24, 57, 12, 55], [24, 58, 12, 56], [25, 4, 13, 2], [25, 10, 13, 8, "mod"], [25, 13, 13, 11], [25, 16, 13, 14, "modifier"], [25, 24, 13, 22], [26, 4, 14, 2], [26, 10, 14, 8, "deps"], [26, 14, 14, 12], [26, 17, 14, 15], [26, 18, 14, 16, "size"], [26, 22, 14, 20], [26, 24, 14, 22], [26, 27, 14, 25, "Object"], [26, 33, 14, 31], [26, 34, 14, 32, "values"], [26, 40, 14, 38], [26, 41, 14, 39], [26, 42, 14, 40, "_mod$__closure"], [26, 56, 14, 54], [26, 59, 14, 57, "mod"], [26, 62, 14, 60], [26, 63, 14, 61, "__closure"], [26, 72, 14, 70], [26, 78, 14, 76], [26, 82, 14, 80], [26, 86, 14, 84, "_mod$__closure"], [26, 100, 14, 98], [26, 105, 14, 103], [26, 110, 14, 108], [26, 111, 14, 109], [26, 114, 14, 112, "_mod$__closure"], [26, 128, 14, 126], [26, 131, 14, 129], [26, 132, 14, 130], [26, 133, 14, 131], [26, 134, 14, 132], [26, 135, 14, 133], [27, 4, 15, 2], [27, 10, 15, 8, "mapperId"], [27, 18, 15, 16], [27, 21, 15, 19, "<PERSON><PERSON>"], [27, 45, 15, 22], [27, 46, 15, 23, "startMapper"], [27, 57, 15, 34], [27, 58, 15, 35], [28, 6, 15, 35], [28, 12, 15, 35, "_e"], [28, 14, 15, 35], [28, 22, 15, 35, "global"], [28, 28, 15, 35], [28, 29, 15, 35, "Error"], [28, 34, 15, 35], [29, 6, 15, 35], [29, 12, 15, 35, "buffersJs1"], [29, 22, 15, 35], [29, 34, 15, 35, "buffersJs1"], [29, 35, 15, 35], [29, 37, 15, 41], [30, 8, 18, 4, "values"], [30, 14, 18, 10], [30, 15, 18, 11, "value"], [30, 20, 18, 16], [30, 21, 18, 17, "for<PERSON>ach"], [30, 28, 18, 24], [30, 29, 18, 25], [30, 30, 18, 26, "val"], [30, 33, 18, 29], [30, 35, 18, 31, "index"], [30, 40, 18, 36], [30, 45, 18, 41], [31, 10, 19, 6, "modifier"], [31, 18, 19, 14], [31, 19, 19, 15, "val"], [31, 22, 19, 18], [31, 24, 19, 20, "index"], [31, 29, 19, 25], [31, 30, 19, 26], [32, 8, 20, 4], [32, 9, 20, 5], [32, 10, 20, 6], [33, 8, 21, 4], [33, 12, 21, 4, "notify<PERSON><PERSON><PERSON>"], [33, 39, 21, 16], [33, 41, 21, 17, "values"], [33, 47, 21, 23], [33, 48, 21, 24], [34, 6, 22, 2], [34, 7, 22, 3], [35, 6, 22, 3, "buffersJs1"], [35, 16, 22, 3], [35, 17, 22, 3, "__closure"], [35, 26, 22, 3], [36, 8, 22, 3, "values"], [36, 14, 22, 3], [37, 8, 22, 3, "modifier"], [37, 16, 22, 3], [38, 8, 22, 3, "notify<PERSON><PERSON><PERSON>"], [38, 20, 22, 3], [38, 22, 21, 4, "notify<PERSON><PERSON><PERSON>"], [39, 6, 21, 16], [40, 6, 21, 16, "buffersJs1"], [40, 16, 21, 16], [40, 17, 21, 16, "__workletHash"], [40, 30, 21, 16], [41, 6, 21, 16, "buffersJs1"], [41, 16, 21, 16], [41, 17, 21, 16, "__initData"], [41, 27, 21, 16], [41, 30, 21, 16, "_worklet_12959593067005_init_data"], [41, 63, 21, 16], [42, 6, 21, 16, "buffersJs1"], [42, 16, 21, 16], [42, 17, 21, 16, "__stackDetails"], [42, 31, 21, 16], [42, 34, 21, 16, "_e"], [42, 36, 21, 16], [43, 6, 21, 16], [43, 13, 21, 16, "buffersJs1"], [43, 23, 21, 16], [44, 4, 21, 16], [44, 5, 15, 35], [44, 9, 22, 5, "deps"], [44, 13, 22, 9], [44, 14, 22, 10], [45, 4, 23, 2], [45, 8, 23, 2, "useEffect"], [45, 24, 23, 11], [45, 26, 23, 12], [45, 32, 23, 18], [46, 6, 24, 4], [46, 13, 24, 11], [46, 19, 24, 17], [47, 8, 25, 6, "<PERSON><PERSON>"], [47, 32, 25, 9], [47, 33, 25, 10, "stopMapper"], [47, 43, 25, 20], [47, 44, 25, 21, "mapperId"], [47, 52, 25, 29], [47, 53, 25, 30], [48, 6, 26, 4], [48, 7, 26, 5], [49, 4, 27, 2], [49, 5, 27, 3], [49, 7, 27, 5], [49, 8, 27, 6, "mapperId"], [49, 16, 27, 14], [49, 17, 27, 15], [49, 18, 27, 16], [50, 4, 28, 2], [50, 11, 28, 9, "values"], [50, 17, 28, 15], [51, 2, 29, 0], [51, 3, 29, 1], [52, 2, 30, 7], [52, 8, 30, 13, "useRectBuffer"], [52, 21, 30, 26], [52, 24, 30, 29, "useRectBuffer"], [52, 25, 30, 30, "size"], [52, 29, 30, 34], [52, 31, 30, 36, "modifier"], [52, 39, 30, 44], [52, 44, 30, 49, "useBuffer"], [52, 53, 30, 58], [52, 54, 30, 59, "size"], [52, 58, 30, 63], [52, 60, 30, 65], [52, 66, 30, 71, "Skia"], [52, 76, 30, 75], [52, 77, 30, 76, "XYWHRect"], [52, 85, 30, 84], [52, 86, 30, 85], [52, 87, 30, 86], [52, 89, 30, 88], [52, 90, 30, 89], [52, 92, 30, 91], [52, 93, 30, 92], [52, 95, 30, 94], [52, 96, 30, 95], [52, 97, 30, 96], [52, 99, 30, 98, "modifier"], [52, 107, 30, 106], [52, 108, 30, 107], [54, 2, 32, 0], [55, 2, 32, 0, "exports"], [55, 9, 32, 0], [55, 10, 32, 0, "useRectBuffer"], [55, 23, 32, 0], [55, 26, 32, 0, "useRectBuffer"], [55, 39, 32, 0], [56, 2, 33, 7], [56, 8, 33, 13, "useRSXformBuffer"], [56, 24, 33, 29], [56, 27, 33, 32, "useRSXformBuffer"], [56, 28, 33, 33, "size"], [56, 32, 33, 37], [56, 34, 33, 39, "modifier"], [56, 42, 33, 47], [56, 47, 33, 52, "useBuffer"], [56, 56, 33, 61], [56, 57, 33, 62, "size"], [56, 61, 33, 66], [56, 63, 33, 68], [56, 69, 33, 74, "Skia"], [56, 79, 33, 78], [56, 80, 33, 79, "RSXform"], [56, 87, 33, 86], [56, 88, 33, 87], [56, 89, 33, 88], [56, 91, 33, 90], [56, 92, 33, 91], [56, 94, 33, 93], [56, 95, 33, 94], [56, 97, 33, 96], [56, 98, 33, 97], [56, 99, 33, 98], [56, 101, 33, 100, "modifier"], [56, 109, 33, 108], [56, 110, 33, 109], [58, 2, 35, 0], [59, 2, 35, 0, "exports"], [59, 9, 35, 0], [59, 10, 35, 0, "useRSXformBuffer"], [59, 26, 35, 0], [59, 29, 35, 0, "useRSXformBuffer"], [59, 45, 35, 0], [60, 2, 36, 7], [60, 8, 36, 13, "usePointBuffer"], [60, 22, 36, 27], [60, 25, 36, 30, "usePointBuffer"], [60, 26, 36, 31, "size"], [60, 30, 36, 35], [60, 32, 36, 37, "modifier"], [60, 40, 36, 45], [60, 45, 36, 50, "useBuffer"], [60, 54, 36, 59], [60, 55, 36, 60, "size"], [60, 59, 36, 64], [60, 61, 36, 66], [60, 67, 36, 72, "Skia"], [60, 77, 36, 76], [60, 78, 36, 77, "Point"], [60, 83, 36, 82], [60, 84, 36, 83], [60, 85, 36, 84], [60, 87, 36, 86], [60, 88, 36, 87], [60, 89, 36, 88], [60, 91, 36, 90, "modifier"], [60, 99, 36, 98], [60, 100, 36, 99], [62, 2, 38, 0], [63, 2, 38, 0, "exports"], [63, 9, 38, 0], [63, 10, 38, 0, "usePointBuffer"], [63, 24, 38, 0], [63, 27, 38, 0, "usePointBuffer"], [63, 41, 38, 0], [64, 2, 39, 7], [64, 8, 39, 13, "useColorBuffer"], [64, 22, 39, 27], [64, 25, 39, 30, "useColorBuffer"], [64, 26, 39, 31, "size"], [64, 30, 39, 35], [64, 32, 39, 37, "modifier"], [64, 40, 39, 45], [64, 45, 39, 50, "useBuffer"], [64, 54, 39, 59], [64, 55, 39, 60, "size"], [64, 59, 39, 64], [64, 61, 39, 66], [64, 67, 39, 72, "Skia"], [64, 77, 39, 76], [64, 78, 39, 77, "Color"], [64, 83, 39, 82], [64, 84, 39, 83], [64, 91, 39, 90], [64, 92, 39, 91], [64, 94, 39, 93, "modifier"], [64, 102, 39, 101], [64, 103, 39, 102], [65, 2, 39, 103, "exports"], [65, 9, 39, 103], [65, 10, 39, 103, "useColorBuffer"], [65, 24, 39, 103], [65, 27, 39, 103, "useColorBuffer"], [65, 41, 39, 103], [66, 0, 39, 103], [66, 3]], "functionMap": {"names": ["<global>", "useBufferValue", "useMemo$argument_0", "useBuffer", "Rea.startMapper$argument_0", "values.value.forEach$argument_0", "useEffect$argument_0", "<anonymous>", "useRectBuffer", "useBuffer$argument_1", "useRSXformBuffer", "usePointBuffer", "useColorBuffer"], "mappings": "AAA;uBCI;iBCC,qED;CDG;kBGC;mCCK;yBCG;KDE;GDE;YGC;WCC;KDE;GHC;CHE;6BQC,oCC,+BD,WR;gCUG,oCD,8BC,WV;8BWG,oCF,sBE,WX;8BYG,oCH,yBG,WZ"}}, "type": "js/module"}]}