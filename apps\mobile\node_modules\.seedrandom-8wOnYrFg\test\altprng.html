<html>
<head>
<link rel="stylesheet" href="lib/qunit.css">
</head>
<body>
<div id="qunit"></div>
<div id="qunit-fixture"></div>
<script src="../lib/xor4096.min.js"></script>
<script src="lib/qunit.js"></script>
<script>
QUnit.module("Alternative PRNG Test");

QUnit.test("Verify that we can use xor4096", function(assert) {
// Use xor4096 for <PERSON>'s xorgens-4096 algorithm.
var xorgen = new xor4096('hello.');

// By default provides 32 bits of randomness in a float.
assert.equal(xorgen(), 0.9798525865189731);

// Use "double" to get 56 bits of randomness.
assert.equal(xorgen.double(), 0.03583478477375346);

// Use "int32" to get a 32 bit (signed) integer.
assert.equal(xorgen.int32(), 1341429986);

});
</script>
</html>


