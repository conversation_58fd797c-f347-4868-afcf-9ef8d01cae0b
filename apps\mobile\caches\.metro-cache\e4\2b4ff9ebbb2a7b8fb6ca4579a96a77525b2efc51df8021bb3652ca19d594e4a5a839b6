{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 40, "index": 642}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkPoint = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class JsiSkPoint extends _Host.BaseHostObject {\n    static fromValue(point) {\n      if (point instanceof JsiSkPoint) {\n        return point.ref;\n      }\n      return new Float32Array([point.x, point.y]);\n    }\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"Point\");\n      _defineProperty(this, \"dispose\", () => {\n        // Float32Array\n      });\n    }\n    get x() {\n      return this.ref[0];\n    }\n    get y() {\n      return this.ref[1];\n    }\n  }\n  exports.JsiSkPoint = JsiSkPoint;\n});", "lineCount": 50, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_Host"], [6, 11, 4, 0], [6, 14, 4, 0, "require"], [6, 21, 4, 0], [6, 22, 4, 0, "_dependencyMap"], [6, 36, 4, 0], [7, 2, 1, 0], [7, 11, 1, 9, "_defineProperty"], [7, 26, 1, 24, "_defineProperty"], [7, 27, 1, 25, "e"], [7, 28, 1, 26], [7, 30, 1, 28, "r"], [7, 31, 1, 29], [7, 33, 1, 31, "t"], [7, 34, 1, 32], [7, 36, 1, 34], [8, 4, 1, 36], [8, 11, 1, 43], [8, 12, 1, 44, "r"], [8, 13, 1, 45], [8, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 30, 1, 62], [8, 31, 1, 63, "r"], [8, 32, 1, 64], [8, 33, 1, 65], [8, 38, 1, 70, "e"], [8, 39, 1, 71], [8, 42, 1, 74, "Object"], [8, 48, 1, 80], [8, 49, 1, 81, "defineProperty"], [8, 63, 1, 95], [8, 64, 1, 96, "e"], [8, 65, 1, 97], [8, 67, 1, 99, "r"], [8, 68, 1, 100], [8, 70, 1, 102], [9, 6, 1, 104, "value"], [9, 11, 1, 109], [9, 13, 1, 111, "t"], [9, 14, 1, 112], [10, 6, 1, 114, "enumerable"], [10, 16, 1, 124], [10, 18, 1, 126], [10, 19, 1, 127], [10, 20, 1, 128], [11, 6, 1, 130, "configurable"], [11, 18, 1, 142], [11, 20, 1, 144], [11, 21, 1, 145], [11, 22, 1, 146], [12, 6, 1, 148, "writable"], [12, 14, 1, 156], [12, 16, 1, 158], [12, 17, 1, 159], [13, 4, 1, 161], [13, 5, 1, 162], [13, 6, 1, 163], [13, 9, 1, 166, "e"], [13, 10, 1, 167], [13, 11, 1, 168, "r"], [13, 12, 1, 169], [13, 13, 1, 170], [13, 16, 1, 173, "t"], [13, 17, 1, 174], [13, 19, 1, 176, "e"], [13, 20, 1, 177], [14, 2, 1, 179], [15, 2, 2, 0], [15, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [15, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [15, 26, 2, 24, "t"], [15, 27, 2, 25], [15, 29, 2, 27], [16, 4, 2, 29], [16, 8, 2, 33, "i"], [16, 9, 2, 34], [16, 12, 2, 37, "_toPrimitive"], [16, 24, 2, 49], [16, 25, 2, 50, "t"], [16, 26, 2, 51], [16, 28, 2, 53], [16, 36, 2, 61], [16, 37, 2, 62], [17, 4, 2, 64], [17, 11, 2, 71], [17, 19, 2, 79], [17, 23, 2, 83], [17, 30, 2, 90, "i"], [17, 31, 2, 91], [17, 34, 2, 94, "i"], [17, 35, 2, 95], [17, 38, 2, 98, "i"], [17, 39, 2, 99], [17, 42, 2, 102], [17, 44, 2, 104], [18, 2, 2, 106], [19, 2, 3, 0], [19, 11, 3, 9, "_toPrimitive"], [19, 23, 3, 21, "_toPrimitive"], [19, 24, 3, 22, "t"], [19, 25, 3, 23], [19, 27, 3, 25, "r"], [19, 28, 3, 26], [19, 30, 3, 28], [20, 4, 3, 30], [20, 8, 3, 34], [20, 16, 3, 42], [20, 20, 3, 46], [20, 27, 3, 53, "t"], [20, 28, 3, 54], [20, 32, 3, 58], [20, 33, 3, 59, "t"], [20, 34, 3, 60], [20, 36, 3, 62], [20, 43, 3, 69, "t"], [20, 44, 3, 70], [21, 4, 3, 72], [21, 8, 3, 76, "e"], [21, 9, 3, 77], [21, 12, 3, 80, "t"], [21, 13, 3, 81], [21, 14, 3, 82, "Symbol"], [21, 20, 3, 88], [21, 21, 3, 89, "toPrimitive"], [21, 32, 3, 100], [21, 33, 3, 101], [22, 4, 3, 103], [22, 8, 3, 107], [22, 13, 3, 112], [22, 14, 3, 113], [22, 19, 3, 118, "e"], [22, 20, 3, 119], [22, 22, 3, 121], [23, 6, 3, 123], [23, 10, 3, 127, "i"], [23, 11, 3, 128], [23, 14, 3, 131, "e"], [23, 15, 3, 132], [23, 16, 3, 133, "call"], [23, 20, 3, 137], [23, 21, 3, 138, "t"], [23, 22, 3, 139], [23, 24, 3, 141, "r"], [23, 25, 3, 142], [23, 29, 3, 146], [23, 38, 3, 155], [23, 39, 3, 156], [24, 6, 3, 158], [24, 10, 3, 162], [24, 18, 3, 170], [24, 22, 3, 174], [24, 29, 3, 181, "i"], [24, 30, 3, 182], [24, 32, 3, 184], [24, 39, 3, 191, "i"], [24, 40, 3, 192], [25, 6, 3, 194], [25, 12, 3, 200], [25, 16, 3, 204, "TypeError"], [25, 25, 3, 213], [25, 26, 3, 214], [25, 72, 3, 260], [25, 73, 3, 261], [26, 4, 3, 263], [27, 4, 3, 265], [27, 11, 3, 272], [27, 12, 3, 273], [27, 20, 3, 281], [27, 25, 3, 286, "r"], [27, 26, 3, 287], [27, 29, 3, 290, "String"], [27, 35, 3, 296], [27, 38, 3, 299, "Number"], [27, 44, 3, 305], [27, 46, 3, 307, "t"], [27, 47, 3, 308], [27, 48, 3, 309], [28, 2, 3, 311], [29, 2, 5, 7], [29, 8, 5, 13, "JsiSkPoint"], [29, 18, 5, 23], [29, 27, 5, 32, "BaseHostObject"], [29, 47, 5, 46], [29, 48, 5, 47], [30, 4, 6, 2], [30, 11, 6, 9, "fromValue"], [30, 20, 6, 18, "fromValue"], [30, 21, 6, 19, "point"], [30, 26, 6, 24], [30, 28, 6, 26], [31, 6, 7, 4], [31, 10, 7, 8, "point"], [31, 15, 7, 13], [31, 27, 7, 25, "JsiSkPoint"], [31, 37, 7, 35], [31, 39, 7, 37], [32, 8, 8, 6], [32, 15, 8, 13, "point"], [32, 20, 8, 18], [32, 21, 8, 19, "ref"], [32, 24, 8, 22], [33, 6, 9, 4], [34, 6, 10, 4], [34, 13, 10, 11], [34, 17, 10, 15, "Float32Array"], [34, 29, 10, 27], [34, 30, 10, 28], [34, 31, 10, 29, "point"], [34, 36, 10, 34], [34, 37, 10, 35, "x"], [34, 38, 10, 36], [34, 40, 10, 38, "point"], [34, 45, 10, 43], [34, 46, 10, 44, "y"], [34, 47, 10, 45], [34, 48, 10, 46], [34, 49, 10, 47], [35, 4, 11, 2], [36, 4, 12, 2, "constructor"], [36, 15, 12, 13, "constructor"], [36, 16, 12, 14, "CanvasKit"], [36, 25, 12, 23], [36, 27, 12, 25, "ref"], [36, 30, 12, 28], [36, 32, 12, 30], [37, 6, 13, 4], [37, 11, 13, 9], [37, 12, 13, 10, "CanvasKit"], [37, 21, 13, 19], [37, 23, 13, 21, "ref"], [37, 26, 13, 24], [37, 28, 13, 26], [37, 35, 13, 33], [37, 36, 13, 34], [38, 6, 14, 4, "_defineProperty"], [38, 21, 14, 19], [38, 22, 14, 20], [38, 26, 14, 24], [38, 28, 14, 26], [38, 37, 14, 35], [38, 39, 14, 37], [38, 45, 14, 43], [39, 8, 15, 6], [40, 6, 15, 6], [40, 7, 16, 5], [40, 8, 16, 6], [41, 4, 17, 2], [42, 4, 18, 2], [42, 8, 18, 6, "x"], [42, 9, 18, 7, "x"], [42, 10, 18, 7], [42, 12, 18, 10], [43, 6, 19, 4], [43, 13, 19, 11], [43, 17, 19, 15], [43, 18, 19, 16, "ref"], [43, 21, 19, 19], [43, 22, 19, 20], [43, 23, 19, 21], [43, 24, 19, 22], [44, 4, 20, 2], [45, 4, 21, 2], [45, 8, 21, 6, "y"], [45, 9, 21, 7, "y"], [45, 10, 21, 7], [45, 12, 21, 10], [46, 6, 22, 4], [46, 13, 22, 11], [46, 17, 22, 15], [46, 18, 22, 16, "ref"], [46, 21, 22, 19], [46, 22, 22, 20], [46, 23, 22, 21], [46, 24, 22, 22], [47, 4, 23, 2], [48, 2, 24, 0], [49, 2, 24, 1, "exports"], [49, 9, 24, 1], [49, 10, 24, 1, "JsiSkPoint"], [49, 20, 24, 1], [49, 23, 24, 1, "JsiSkPoint"], [49, 33, 24, 1], [50, 0, 24, 1], [50, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "JsiSkPoint", "fromValue", "constructor", "_defineProperty$argument_2", "get__x", "get__y"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGE;ECC;GDK;EEC;qCCE;KDE;GFC;EIC;GJE;EKC;GLE;CHC"}}, "type": "js/module"}]}