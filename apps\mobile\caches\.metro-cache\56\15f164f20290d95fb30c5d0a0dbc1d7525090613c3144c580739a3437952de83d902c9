{"dependencies": [{"name": "../utils.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 61}, "end": {"line": 2, "column": 42, "index": 103}}], "key": "V6Cd8v/K/LtdxP2wf1WD5HCNhqM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _utils = require(_dependencyMap[0], \"../utils.web\");\n  function sizeFromAngle(width, height, angle) {\n    const radians = angle * Math.PI / 180;\n    let c = Math.cos(radians);\n    let s = Math.sin(radians);\n    if (s < 0) {\n      s = -s;\n    }\n    if (c < 0) {\n      c = -c;\n    }\n    return {\n      width: height * s + width * c,\n      height: height * c + width * s\n    };\n  }\n  var _default = (canvas, degrees) => {\n    const {\n      width,\n      height\n    } = sizeFromAngle(canvas.width, canvas.height, degrees);\n    const result = document.createElement('canvas');\n    result.width = width;\n    result.height = height;\n    const context = (0, _utils.getContext)(result);\n\n    // Set the origin to the center of the image\n    context.translate(result.width / 2, result.height / 2);\n\n    // Rotate the canvas around the origin\n    const radians = degrees * Math.PI / 180;\n    context.rotate(radians);\n\n    // Draw the image\n    context.drawImage(canvas, -canvas.width / 2, -canvas.height / 2, canvas.width, canvas.height);\n    return result;\n  };\n  exports.default = _default;\n});", "lineCount": 44, "map": [[6, 2, 2, 0], [6, 6, 2, 0, "_utils"], [6, 12, 2, 0], [6, 15, 2, 0, "require"], [6, 22, 2, 0], [6, 23, 2, 0, "_dependencyMap"], [6, 37, 2, 0], [7, 2, 4, 0], [7, 11, 4, 9, "sizeFromAngle"], [7, 24, 4, 22, "sizeFromAngle"], [7, 25, 5, 2, "width"], [7, 30, 5, 15], [7, 32, 6, 2, "height"], [7, 38, 6, 16], [7, 40, 7, 2, "angle"], [7, 45, 7, 15], [7, 47, 8, 37], [8, 4, 9, 2], [8, 10, 9, 8, "radians"], [8, 17, 9, 15], [8, 20, 9, 19, "angle"], [8, 25, 9, 24], [8, 28, 9, 27, "Math"], [8, 32, 9, 31], [8, 33, 9, 32, "PI"], [8, 35, 9, 34], [8, 38, 9, 38], [8, 41, 9, 41], [9, 4, 10, 2], [9, 8, 10, 6, "c"], [9, 9, 10, 7], [9, 12, 10, 10, "Math"], [9, 16, 10, 14], [9, 17, 10, 15, "cos"], [9, 20, 10, 18], [9, 21, 10, 19, "radians"], [9, 28, 10, 26], [9, 29, 10, 27], [10, 4, 11, 2], [10, 8, 11, 6, "s"], [10, 9, 11, 7], [10, 12, 11, 10, "Math"], [10, 16, 11, 14], [10, 17, 11, 15, "sin"], [10, 20, 11, 18], [10, 21, 11, 19, "radians"], [10, 28, 11, 26], [10, 29, 11, 27], [11, 4, 12, 2], [11, 8, 12, 6, "s"], [11, 9, 12, 7], [11, 12, 12, 10], [11, 13, 12, 11], [11, 15, 12, 13], [12, 6, 13, 4, "s"], [12, 7, 13, 5], [12, 10, 13, 8], [12, 11, 13, 9, "s"], [12, 12, 13, 10], [13, 4, 14, 2], [14, 4, 15, 2], [14, 8, 15, 6, "c"], [14, 9, 15, 7], [14, 12, 15, 10], [14, 13, 15, 11], [14, 15, 15, 13], [15, 6, 16, 4, "c"], [15, 7, 16, 5], [15, 10, 16, 8], [15, 11, 16, 9, "c"], [15, 12, 16, 10], [16, 4, 17, 2], [17, 4, 18, 2], [17, 11, 18, 9], [18, 6, 18, 11, "width"], [18, 11, 18, 16], [18, 13, 18, 18, "height"], [18, 19, 18, 24], [18, 22, 18, 27, "s"], [18, 23, 18, 28], [18, 26, 18, 31, "width"], [18, 31, 18, 36], [18, 34, 18, 39, "c"], [18, 35, 18, 40], [19, 6, 18, 42, "height"], [19, 12, 18, 48], [19, 14, 18, 50, "height"], [19, 20, 18, 56], [19, 23, 18, 59, "c"], [19, 24, 18, 60], [19, 27, 18, 63, "width"], [19, 32, 18, 68], [19, 35, 18, 71, "s"], [20, 4, 18, 73], [20, 5, 18, 74], [21, 2, 19, 0], [22, 2, 19, 1], [22, 6, 19, 1, "_default"], [22, 14, 19, 1], [22, 17, 21, 15, "_default"], [22, 18, 21, 16, "canvas"], [22, 24, 21, 41], [22, 26, 21, 43, "degrees"], [22, 33, 21, 74], [22, 38, 21, 79], [23, 4, 22, 2], [23, 10, 22, 8], [24, 6, 22, 10, "width"], [24, 11, 22, 15], [25, 6, 22, 17, "height"], [26, 4, 22, 24], [26, 5, 22, 25], [26, 8, 22, 28, "sizeFromAngle"], [26, 21, 22, 41], [26, 22, 22, 42, "canvas"], [26, 28, 22, 48], [26, 29, 22, 49, "width"], [26, 34, 22, 54], [26, 36, 22, 56, "canvas"], [26, 42, 22, 62], [26, 43, 22, 63, "height"], [26, 49, 22, 69], [26, 51, 22, 71, "degrees"], [26, 58, 22, 78], [26, 59, 22, 79], [27, 4, 24, 2], [27, 10, 24, 8, "result"], [27, 16, 24, 14], [27, 19, 24, 17, "document"], [27, 27, 24, 25], [27, 28, 24, 26, "createElement"], [27, 41, 24, 39], [27, 42, 24, 40], [27, 50, 24, 48], [27, 51, 24, 49], [28, 4, 25, 2, "result"], [28, 10, 25, 8], [28, 11, 25, 9, "width"], [28, 16, 25, 14], [28, 19, 25, 17, "width"], [28, 24, 25, 22], [29, 4, 26, 2, "result"], [29, 10, 26, 8], [29, 11, 26, 9, "height"], [29, 17, 26, 15], [29, 20, 26, 18, "height"], [29, 26, 26, 24], [30, 4, 28, 2], [30, 10, 28, 8, "context"], [30, 17, 28, 15], [30, 20, 28, 18], [30, 24, 28, 18, "getContext"], [30, 41, 28, 28], [30, 43, 28, 29, "result"], [30, 49, 28, 35], [30, 50, 28, 36], [32, 4, 30, 2], [33, 4, 31, 2, "context"], [33, 11, 31, 9], [33, 12, 31, 10, "translate"], [33, 21, 31, 19], [33, 22, 31, 20, "result"], [33, 28, 31, 26], [33, 29, 31, 27, "width"], [33, 34, 31, 32], [33, 37, 31, 35], [33, 38, 31, 36], [33, 40, 31, 38, "result"], [33, 46, 31, 44], [33, 47, 31, 45, "height"], [33, 53, 31, 51], [33, 56, 31, 54], [33, 57, 31, 55], [33, 58, 31, 56], [35, 4, 33, 2], [36, 4, 34, 2], [36, 10, 34, 8, "radians"], [36, 17, 34, 15], [36, 20, 34, 19, "degrees"], [36, 27, 34, 26], [36, 30, 34, 29, "Math"], [36, 34, 34, 33], [36, 35, 34, 34, "PI"], [36, 37, 34, 36], [36, 40, 34, 40], [36, 43, 34, 43], [37, 4, 35, 2, "context"], [37, 11, 35, 9], [37, 12, 35, 10, "rotate"], [37, 18, 35, 16], [37, 19, 35, 17, "radians"], [37, 26, 35, 24], [37, 27, 35, 25], [39, 4, 37, 2], [40, 4, 38, 2, "context"], [40, 11, 38, 9], [40, 12, 38, 10, "drawImage"], [40, 21, 38, 19], [40, 22, 38, 20, "canvas"], [40, 28, 38, 26], [40, 30, 38, 28], [40, 31, 38, 29, "canvas"], [40, 37, 38, 35], [40, 38, 38, 36, "width"], [40, 43, 38, 41], [40, 46, 38, 44], [40, 47, 38, 45], [40, 49, 38, 47], [40, 50, 38, 48, "canvas"], [40, 56, 38, 54], [40, 57, 38, 55, "height"], [40, 63, 38, 61], [40, 66, 38, 64], [40, 67, 38, 65], [40, 69, 38, 67, "canvas"], [40, 75, 38, 73], [40, 76, 38, 74, "width"], [40, 81, 38, 79], [40, 83, 38, 81, "canvas"], [40, 89, 38, 87], [40, 90, 38, 88, "height"], [40, 96, 38, 94], [40, 97, 38, 95], [41, 4, 40, 2], [41, 11, 40, 9, "result"], [41, 17, 40, 15], [42, 2, 41, 0], [42, 3, 41, 1], [43, 2, 41, 1, "exports"], [43, 9, 41, 1], [43, 10, 41, 1, "default"], [43, 17, 41, 1], [43, 20, 41, 1, "_default"], [43, 28, 41, 1], [44, 0, 41, 1], [44, 3]], "functionMap": {"names": ["<global>", "sizeFromAngle", "default"], "mappings": "AAA;ACG;CDe;eEE;CFoB"}}, "type": "js/module"}]}