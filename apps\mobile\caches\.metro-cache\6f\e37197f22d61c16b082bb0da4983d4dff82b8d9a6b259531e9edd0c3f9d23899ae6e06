{"dependencies": [{"name": "expo", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 33, "index": 33}}], "key": "PWvtvXU7MaET6Yd1Gn8oQOXJQ8A=", "exportNames": ["*"]}}, {"name": "../ImageManipulator.types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 35}, "end": {"line": 3, "column": 81, "index": 116}}], "key": "we/+JaAMOU0m3RKMiFhb4NAarVM=", "exportNames": ["*"]}}, {"name": "./utils.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 117}, "end": {"line": 4, "column": 49, "index": 166}}], "key": "EUegP4QQ6V+YZMXwkMptx1Qg28Y=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _expo = require(_dependencyMap[0], \"expo\");\n  var _ImageManipulator = require(_dependencyMap[1], \"../ImageManipulator.types\");\n  var _utils = require(_dependencyMap[2], \"./utils.web\");\n  class ImageManipulatorImageRef extends _expo.SharedRef {\n    nativeRefType = 'image';\n    constructor(uri, canvas) {\n      super();\n      this.uri = uri;\n      this.canvas = canvas;\n    }\n    get width() {\n      return this.canvas.width;\n    }\n    get height() {\n      return this.canvas.height;\n    }\n    async saveAsync(options = {\n      base64: false\n    }) {\n      return new Promise((resolve, reject) => {\n        this.canvas.toBlob(async blob => {\n          if (!blob) {\n            return reject(new Error(`Unable to save image: ${this.uri}`));\n          }\n          const base64 = options.base64 ? await (0, _utils.blobToBase64String)(blob) : undefined;\n          const uri = URL.createObjectURL(blob);\n          resolve({\n            uri,\n            width: this.width,\n            height: this.height,\n            base64\n          });\n        }, `image/${options.format ?? _ImageManipulator.SaveFormat.JPEG}`, options.compress);\n      });\n    }\n  }\n  exports.default = ImageManipulatorImageRef;\n});", "lineCount": 43, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_expo"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 3, 0], [7, 6, 3, 0, "_ImageManipulator"], [7, 23, 3, 0], [7, 26, 3, 0, "require"], [7, 33, 3, 0], [7, 34, 3, 0, "_dependencyMap"], [7, 48, 3, 0], [8, 2, 4, 0], [8, 6, 4, 0, "_utils"], [8, 12, 4, 0], [8, 15, 4, 0, "require"], [8, 22, 4, 0], [8, 23, 4, 0, "_dependencyMap"], [8, 37, 4, 0], [9, 2, 6, 15], [9, 8, 6, 21, "ImageManipulatorImageRef"], [9, 32, 6, 45], [9, 41, 6, 54, "SharedRef"], [9, 56, 6, 63], [9, 57, 6, 73], [10, 4, 7, 11, "nativeRefType"], [10, 17, 7, 24], [10, 20, 7, 35], [10, 27, 7, 42], [11, 4, 12, 2, "constructor"], [11, 15, 12, 13, "constructor"], [11, 16, 12, 14, "uri"], [11, 19, 12, 25], [11, 21, 12, 27, "canvas"], [11, 27, 12, 52], [11, 29, 12, 54], [12, 6, 13, 4], [12, 11, 13, 9], [12, 12, 13, 10], [12, 13, 13, 11], [13, 6, 14, 4], [13, 10, 14, 8], [13, 11, 14, 9, "uri"], [13, 14, 14, 12], [13, 17, 14, 15, "uri"], [13, 20, 14, 18], [14, 6, 15, 4], [14, 10, 15, 8], [14, 11, 15, 9, "canvas"], [14, 17, 15, 15], [14, 20, 15, 18, "canvas"], [14, 26, 15, 24], [15, 4, 16, 2], [16, 4, 18, 2], [16, 8, 18, 6, "width"], [16, 13, 18, 11, "width"], [16, 14, 18, 11], [16, 16, 18, 14], [17, 6, 19, 4], [17, 13, 19, 11], [17, 17, 19, 15], [17, 18, 19, 16, "canvas"], [17, 24, 19, 22], [17, 25, 19, 23, "width"], [17, 30, 19, 28], [18, 4, 20, 2], [19, 4, 22, 2], [19, 8, 22, 6, "height"], [19, 14, 22, 12, "height"], [19, 15, 22, 12], [19, 17, 22, 15], [20, 6, 23, 4], [20, 13, 23, 11], [20, 17, 23, 15], [20, 18, 23, 16, "canvas"], [20, 24, 23, 22], [20, 25, 23, 23, "height"], [20, 31, 23, 29], [21, 4, 24, 2], [22, 4, 26, 2], [22, 10, 26, 8, "saveAsync"], [22, 19, 26, 17, "saveAsync"], [22, 20, 26, 18, "options"], [22, 27, 26, 38], [22, 30, 26, 41], [23, 6, 26, 43, "base64"], [23, 12, 26, 49], [23, 14, 26, 51], [24, 4, 26, 57], [24, 5, 26, 58], [24, 7, 26, 82], [25, 6, 27, 4], [25, 13, 27, 11], [25, 17, 27, 15, "Promise"], [25, 24, 27, 22], [25, 25, 27, 23], [25, 26, 27, 24, "resolve"], [25, 33, 27, 31], [25, 35, 27, 33, "reject"], [25, 41, 27, 39], [25, 46, 27, 44], [26, 8, 28, 6], [26, 12, 28, 10], [26, 13, 28, 11, "canvas"], [26, 19, 28, 17], [26, 20, 28, 18, "toBlob"], [26, 26, 28, 24], [26, 27, 29, 8], [26, 33, 29, 15, "blob"], [26, 37, 29, 19], [26, 41, 29, 24], [27, 10, 30, 10], [27, 14, 30, 14], [27, 15, 30, 15, "blob"], [27, 19, 30, 19], [27, 21, 30, 21], [28, 12, 31, 12], [28, 19, 31, 19, "reject"], [28, 25, 31, 25], [28, 26, 31, 26], [28, 30, 31, 30, "Error"], [28, 35, 31, 35], [28, 36, 31, 36], [28, 61, 31, 61], [28, 65, 31, 65], [28, 66, 31, 66, "uri"], [28, 69, 31, 69], [28, 71, 31, 71], [28, 72, 31, 72], [28, 73, 31, 73], [29, 10, 32, 10], [30, 10, 33, 10], [30, 16, 33, 16, "base64"], [30, 22, 33, 22], [30, 25, 33, 25, "options"], [30, 32, 33, 32], [30, 33, 33, 33, "base64"], [30, 39, 33, 39], [30, 42, 33, 42], [30, 48, 33, 48], [30, 52, 33, 48, "blobToBase64String"], [30, 77, 33, 66], [30, 79, 33, 67, "blob"], [30, 83, 33, 71], [30, 84, 33, 72], [30, 87, 33, 75, "undefined"], [30, 96, 33, 84], [31, 10, 34, 10], [31, 16, 34, 16, "uri"], [31, 19, 34, 19], [31, 22, 34, 22, "URL"], [31, 25, 34, 25], [31, 26, 34, 26, "createObjectURL"], [31, 41, 34, 41], [31, 42, 34, 42, "blob"], [31, 46, 34, 46], [31, 47, 34, 47], [32, 10, 36, 10, "resolve"], [32, 17, 36, 17], [32, 18, 36, 18], [33, 12, 37, 12, "uri"], [33, 15, 37, 15], [34, 12, 38, 12, "width"], [34, 17, 38, 17], [34, 19, 38, 19], [34, 23, 38, 23], [34, 24, 38, 24, "width"], [34, 29, 38, 29], [35, 12, 39, 12, "height"], [35, 18, 39, 18], [35, 20, 39, 20], [35, 24, 39, 24], [35, 25, 39, 25, "height"], [35, 31, 39, 31], [36, 12, 40, 12, "base64"], [37, 10, 41, 10], [37, 11, 41, 11], [37, 12, 41, 12], [38, 8, 42, 8], [38, 9, 42, 9], [38, 11, 43, 8], [38, 20, 43, 17, "options"], [38, 27, 43, 24], [38, 28, 43, 25, "format"], [38, 34, 43, 31], [38, 38, 43, 35, "SaveFormat"], [38, 66, 43, 45], [38, 67, 43, 46, "JPEG"], [38, 71, 43, 50], [38, 73, 43, 52], [38, 75, 44, 8, "options"], [38, 82, 44, 15], [38, 83, 44, 16, "compress"], [38, 91, 45, 6], [38, 92, 45, 7], [39, 6, 46, 4], [39, 7, 46, 5], [39, 8, 46, 6], [40, 4, 47, 2], [41, 2, 48, 0], [42, 2, 48, 1, "exports"], [42, 9, 48, 1], [42, 10, 48, 1, "default"], [42, 17, 48, 1], [42, 20, 48, 1, "ImageManipulatorImageRef"], [42, 44, 48, 1], [43, 0, 48, 1], [43, 3]], "functionMap": {"names": ["<global>", "ImageManipulatorImageRef", "constructor", "get__width", "get__height", "saveAsync", "Promise$argument_0", "canvas.toBlob$argument_0"], "mappings": "AAA;eCK;ECM;GDI;EEE;GFE;EGE;GHE;EIE;uBCC;QCE;SDa;KDI;GJC;CDC"}}, "type": "js/module"}]}