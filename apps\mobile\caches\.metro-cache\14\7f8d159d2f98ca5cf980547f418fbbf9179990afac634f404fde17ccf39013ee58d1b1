{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 6, "column": 0, "index": 667}, "end": {"line": 6, "column": 45, "index": 712}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkShader", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 7, "column": 0, "index": 713}, "end": {"line": 7, "column": 44, "index": 757}}], "key": "qmH0e2X2qhdhK7DOZEHi4mFPbz8=", "exportNames": ["*"]}}, {"name": "./JsiSkMatrix", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 8, "column": 0, "index": 758}, "end": {"line": 8, "column": 44, "index": 802}}], "key": "aOVfjZgmz4R2ci39pV6HZujK8og=", "exportNames": ["*"]}}, {"name": "./JsiSkRect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 9, "column": 0, "index": 803}, "end": {"line": 9, "column": 40, "index": 843}}], "key": "VBkFjQz9GOtB0AbNPoXYbn3D5z0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkPicture = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkShader = require(_dependencyMap[1], \"./JsiSkShader\");\n  var _JsiSkMatrix = require(_dependencyMap[2], \"./JsiSkMatrix\");\n  var _JsiSkRect = require(_dependencyMap[3], \"./JsiSkRect\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  // TODO: suggest to rename SkPicture to Picture for consistency\n\n  class JsiSkPicture extends _Host.HostObject {\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"Picture\");\n      _defineProperty(this, \"dispose\", () => {\n        this.ref.delete();\n      });\n    }\n    makeShader(tmx, tmy, mode, localMatrix, tileRect) {\n      return new _JsiSkShader.JsiSkShader(this.CanvasKit, this.ref.makeShader((0, _Host.getEnum)(this.CanvasKit, \"TileMode\", tmx), (0, _Host.getEnum)(this.CanvasKit, \"TileMode\", tmy), (0, _Host.getEnum)(this.CanvasKit, \"FilterMode\", mode), localMatrix ? _JsiSkMatrix.JsiSkMatrix.fromValue(localMatrix) : undefined, tileRect ? _JsiSkRect.JsiSkRect.fromValue(this.CanvasKit, tileRect) : undefined));\n    }\n    serialize() {\n      return this.ref.serialize();\n    }\n  }\n  exports.JsiSkPicture = JsiSkPicture;\n});", "lineCount": 49, "map": [[6, 2, 6, 0], [6, 6, 6, 0, "_Host"], [6, 11, 6, 0], [6, 14, 6, 0, "require"], [6, 21, 6, 0], [6, 22, 6, 0, "_dependencyMap"], [6, 36, 6, 0], [7, 2, 7, 0], [7, 6, 7, 0, "_JsiSkShader"], [7, 18, 7, 0], [7, 21, 7, 0, "require"], [7, 28, 7, 0], [7, 29, 7, 0, "_dependencyMap"], [7, 43, 7, 0], [8, 2, 8, 0], [8, 6, 8, 0, "_JsiSkMatrix"], [8, 18, 8, 0], [8, 21, 8, 0, "require"], [8, 28, 8, 0], [8, 29, 8, 0, "_dependencyMap"], [8, 43, 8, 0], [9, 2, 9, 0], [9, 6, 9, 0, "_JsiSkRect"], [9, 16, 9, 0], [9, 19, 9, 0, "require"], [9, 26, 9, 0], [9, 27, 9, 0, "_dependencyMap"], [9, 41, 9, 0], [10, 2, 1, 0], [10, 11, 1, 9, "_defineProperty"], [10, 26, 1, 24, "_defineProperty"], [10, 27, 1, 25, "e"], [10, 28, 1, 26], [10, 30, 1, 28, "r"], [10, 31, 1, 29], [10, 33, 1, 31, "t"], [10, 34, 1, 32], [10, 36, 1, 34], [11, 4, 1, 36], [11, 11, 1, 43], [11, 12, 1, 44, "r"], [11, 13, 1, 45], [11, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, 30, 1, 62], [11, 31, 1, 63, "r"], [11, 32, 1, 64], [11, 33, 1, 65], [11, 38, 1, 70, "e"], [11, 39, 1, 71], [11, 42, 1, 74, "Object"], [11, 48, 1, 80], [11, 49, 1, 81, "defineProperty"], [11, 63, 1, 95], [11, 64, 1, 96, "e"], [11, 65, 1, 97], [11, 67, 1, 99, "r"], [11, 68, 1, 100], [11, 70, 1, 102], [12, 6, 1, 104, "value"], [12, 11, 1, 109], [12, 13, 1, 111, "t"], [12, 14, 1, 112], [13, 6, 1, 114, "enumerable"], [13, 16, 1, 124], [13, 18, 1, 126], [13, 19, 1, 127], [13, 20, 1, 128], [14, 6, 1, 130, "configurable"], [14, 18, 1, 142], [14, 20, 1, 144], [14, 21, 1, 145], [14, 22, 1, 146], [15, 6, 1, 148, "writable"], [15, 14, 1, 156], [15, 16, 1, 158], [15, 17, 1, 159], [16, 4, 1, 161], [16, 5, 1, 162], [16, 6, 1, 163], [16, 9, 1, 166, "e"], [16, 10, 1, 167], [16, 11, 1, 168, "r"], [16, 12, 1, 169], [16, 13, 1, 170], [16, 16, 1, 173, "t"], [16, 17, 1, 174], [16, 19, 1, 176, "e"], [16, 20, 1, 177], [17, 2, 1, 179], [18, 2, 2, 0], [18, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [18, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [18, 26, 2, 24, "t"], [18, 27, 2, 25], [18, 29, 2, 27], [19, 4, 2, 29], [19, 8, 2, 33, "i"], [19, 9, 2, 34], [19, 12, 2, 37, "_toPrimitive"], [19, 24, 2, 49], [19, 25, 2, 50, "t"], [19, 26, 2, 51], [19, 28, 2, 53], [19, 36, 2, 61], [19, 37, 2, 62], [20, 4, 2, 64], [20, 11, 2, 71], [20, 19, 2, 79], [20, 23, 2, 83], [20, 30, 2, 90, "i"], [20, 31, 2, 91], [20, 34, 2, 94, "i"], [20, 35, 2, 95], [20, 38, 2, 98, "i"], [20, 39, 2, 99], [20, 42, 2, 102], [20, 44, 2, 104], [21, 2, 2, 106], [22, 2, 3, 0], [22, 11, 3, 9, "_toPrimitive"], [22, 23, 3, 21, "_toPrimitive"], [22, 24, 3, 22, "t"], [22, 25, 3, 23], [22, 27, 3, 25, "r"], [22, 28, 3, 26], [22, 30, 3, 28], [23, 4, 3, 30], [23, 8, 3, 34], [23, 16, 3, 42], [23, 20, 3, 46], [23, 27, 3, 53, "t"], [23, 28, 3, 54], [23, 32, 3, 58], [23, 33, 3, 59, "t"], [23, 34, 3, 60], [23, 36, 3, 62], [23, 43, 3, 69, "t"], [23, 44, 3, 70], [24, 4, 3, 72], [24, 8, 3, 76, "e"], [24, 9, 3, 77], [24, 12, 3, 80, "t"], [24, 13, 3, 81], [24, 14, 3, 82, "Symbol"], [24, 20, 3, 88], [24, 21, 3, 89, "toPrimitive"], [24, 32, 3, 100], [24, 33, 3, 101], [25, 4, 3, 103], [25, 8, 3, 107], [25, 13, 3, 112], [25, 14, 3, 113], [25, 19, 3, 118, "e"], [25, 20, 3, 119], [25, 22, 3, 121], [26, 6, 3, 123], [26, 10, 3, 127, "i"], [26, 11, 3, 128], [26, 14, 3, 131, "e"], [26, 15, 3, 132], [26, 16, 3, 133, "call"], [26, 20, 3, 137], [26, 21, 3, 138, "t"], [26, 22, 3, 139], [26, 24, 3, 141, "r"], [26, 25, 3, 142], [26, 29, 3, 146], [26, 38, 3, 155], [26, 39, 3, 156], [27, 6, 3, 158], [27, 10, 3, 162], [27, 18, 3, 170], [27, 22, 3, 174], [27, 29, 3, 181, "i"], [27, 30, 3, 182], [27, 32, 3, 184], [27, 39, 3, 191, "i"], [27, 40, 3, 192], [28, 6, 3, 194], [28, 12, 3, 200], [28, 16, 3, 204, "TypeError"], [28, 25, 3, 213], [28, 26, 3, 214], [28, 72, 3, 260], [28, 73, 3, 261], [29, 4, 3, 263], [30, 4, 3, 265], [30, 11, 3, 272], [30, 12, 3, 273], [30, 20, 3, 281], [30, 25, 3, 286, "r"], [30, 26, 3, 287], [30, 29, 3, 290, "String"], [30, 35, 3, 296], [30, 38, 3, 299, "Number"], [30, 44, 3, 305], [30, 46, 3, 307, "t"], [30, 47, 3, 308], [30, 48, 3, 309], [31, 2, 3, 311], [32, 2, 4, 0], [34, 2, 10, 7], [34, 8, 10, 13, "JsiSkPicture"], [34, 20, 10, 25], [34, 29, 10, 34, "HostObject"], [34, 45, 10, 44], [34, 46, 10, 45], [35, 4, 11, 2, "constructor"], [35, 15, 11, 13, "constructor"], [35, 16, 11, 14, "CanvasKit"], [35, 25, 11, 23], [35, 27, 11, 25, "ref"], [35, 30, 11, 28], [35, 32, 11, 30], [36, 6, 12, 4], [36, 11, 12, 9], [36, 12, 12, 10, "CanvasKit"], [36, 21, 12, 19], [36, 23, 12, 21, "ref"], [36, 26, 12, 24], [36, 28, 12, 26], [36, 37, 12, 35], [36, 38, 12, 36], [37, 6, 13, 4, "_defineProperty"], [37, 21, 13, 19], [37, 22, 13, 20], [37, 26, 13, 24], [37, 28, 13, 26], [37, 37, 13, 35], [37, 39, 13, 37], [37, 45, 13, 43], [38, 8, 14, 6], [38, 12, 14, 10], [38, 13, 14, 11, "ref"], [38, 16, 14, 14], [38, 17, 14, 15, "delete"], [38, 23, 14, 21], [38, 24, 14, 22], [38, 25, 14, 23], [39, 6, 15, 4], [39, 7, 15, 5], [39, 8, 15, 6], [40, 4, 16, 2], [41, 4, 17, 2, "<PERSON><PERSON><PERSON><PERSON>"], [41, 14, 17, 12, "<PERSON><PERSON><PERSON><PERSON>"], [41, 15, 17, 13, "tmx"], [41, 18, 17, 16], [41, 20, 17, 18, "tmy"], [41, 23, 17, 21], [41, 25, 17, 23, "mode"], [41, 29, 17, 27], [41, 31, 17, 29, "localMatrix"], [41, 42, 17, 40], [41, 44, 17, 42, "tileRect"], [41, 52, 17, 50], [41, 54, 17, 52], [42, 6, 18, 4], [42, 13, 18, 11], [42, 17, 18, 15, "JsiSkShader"], [42, 41, 18, 26], [42, 42, 18, 27], [42, 46, 18, 31], [42, 47, 18, 32, "CanvasKit"], [42, 56, 18, 41], [42, 58, 18, 43], [42, 62, 18, 47], [42, 63, 18, 48, "ref"], [42, 66, 18, 51], [42, 67, 18, 52, "<PERSON><PERSON><PERSON><PERSON>"], [42, 77, 18, 62], [42, 78, 18, 63], [42, 82, 18, 63, "getEnum"], [42, 95, 18, 70], [42, 97, 18, 71], [42, 101, 18, 75], [42, 102, 18, 76, "CanvasKit"], [42, 111, 18, 85], [42, 113, 18, 87], [42, 123, 18, 97], [42, 125, 18, 99, "tmx"], [42, 128, 18, 102], [42, 129, 18, 103], [42, 131, 18, 105], [42, 135, 18, 105, "getEnum"], [42, 148, 18, 112], [42, 150, 18, 113], [42, 154, 18, 117], [42, 155, 18, 118, "CanvasKit"], [42, 164, 18, 127], [42, 166, 18, 129], [42, 176, 18, 139], [42, 178, 18, 141, "tmy"], [42, 181, 18, 144], [42, 182, 18, 145], [42, 184, 18, 147], [42, 188, 18, 147, "getEnum"], [42, 201, 18, 154], [42, 203, 18, 155], [42, 207, 18, 159], [42, 208, 18, 160, "CanvasKit"], [42, 217, 18, 169], [42, 219, 18, 171], [42, 231, 18, 183], [42, 233, 18, 185, "mode"], [42, 237, 18, 189], [42, 238, 18, 190], [42, 240, 18, 192, "localMatrix"], [42, 251, 18, 203], [42, 254, 18, 206, "JsiSkMatrix"], [42, 278, 18, 217], [42, 279, 18, 218, "fromValue"], [42, 288, 18, 227], [42, 289, 18, 228, "localMatrix"], [42, 300, 18, 239], [42, 301, 18, 240], [42, 304, 18, 243, "undefined"], [42, 313, 18, 252], [42, 315, 18, 254, "tileRect"], [42, 323, 18, 262], [42, 326, 18, 265, "JsiSkRect"], [42, 346, 18, 274], [42, 347, 18, 275, "fromValue"], [42, 356, 18, 284], [42, 357, 18, 285], [42, 361, 18, 289], [42, 362, 18, 290, "CanvasKit"], [42, 371, 18, 299], [42, 373, 18, 301, "tileRect"], [42, 381, 18, 309], [42, 382, 18, 310], [42, 385, 18, 313, "undefined"], [42, 394, 18, 322], [42, 395, 18, 323], [42, 396, 18, 324], [43, 4, 19, 2], [44, 4, 20, 2, "serialize"], [44, 13, 20, 11, "serialize"], [44, 14, 20, 11], [44, 16, 20, 14], [45, 6, 21, 4], [45, 13, 21, 11], [45, 17, 21, 15], [45, 18, 21, 16, "ref"], [45, 21, 21, 19], [45, 22, 21, 20, "serialize"], [45, 31, 21, 29], [45, 32, 21, 30], [45, 33, 21, 31], [46, 4, 22, 2], [47, 2, 23, 0], [48, 2, 23, 1, "exports"], [48, 9, 23, 1], [48, 10, 23, 1, "JsiSkPicture"], [48, 22, 23, 1], [48, 25, 23, 1, "JsiSkPicture"], [48, 37, 23, 1], [49, 0, 23, 1], [49, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "JsiSkPicture", "constructor", "_defineProperty$argument_2", "<PERSON><PERSON><PERSON><PERSON>", "serialize"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGO;ECC;qCCE;KDE;GDC;EGC;GHE;EIC;GJE;CHC"}}, "type": "js/module"}]}