{"dependencies": [{"name": "../../Platform", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 42, "index": 42}}], "key": "AXHAxFjlDdeq1JxYZnWn+aHYhYU=", "exportNames": ["*"]}}, {"name": "../Skia", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 43}, "end": {"line": 2, "column": 31, "index": 74}}], "key": "5eRJ3Y/mp/EEiynYa3WwzXcSMXc=", "exportNames": ["*"]}}, {"name": "./Data", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 75}, "end": {"line": 3, "column": 36, "index": 111}}], "key": "0fS75tjqpJdM3ThONBfvzOQKEI0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useImage = exports.makeImageFromView = void 0;\n  var _Platform = require(_dependencyMap[0], \"../../Platform\");\n  var _Skia = require(_dependencyMap[1], \"../Skia\");\n  var _Data = require(_dependencyMap[2], \"./Data\");\n  const imgFactory = _Skia.Skia.Image.MakeImageFromEncoded.bind(_Skia.Skia.Image);\n\n  /**\n   * Returns a Skia Image object\n   * */\n  const useImage = (source, onError) => (0, _Data.useRawData)(source, imgFactory, onError);\n\n  /**\n   * Creates an image from a given view reference. NOTE: This method has different implementations\n   * on web/native. On web, the callback is called with the view ref and the callback is expected to\n   * return a promise that resolves to a Skia Image object. On native, the view ref is used to\n   * find the view tag and the Skia Image object is created from the view tag. This means that on web\n   * you will need to implement the logic to create the image from the view ref yourself.\n   * @param viewRef Ref to the view we're creating an image from\n   * @returns A promise that resolves to a Skia Image object or rejects\n   * with an error id the view tag is invalid.\n   */\n  exports.useImage = useImage;\n  const makeImageFromView = (viewRef, callback = null) => {\n    // In web implementation we just delegate the work to the provided callback\n    if (_Platform.Platform.OS === \"web\") {\n      if (callback) {\n        return callback(viewRef);\n      } else {\n        Promise.reject(new Error(\"Callback is required on web in the makeImageFromView function.\"));\n      }\n    }\n    const viewTag = _Platform.Platform.findNodeHandle(viewRef.current);\n    if (viewTag !== null && viewTag !== 0) {\n      return _Skia.Skia.Image.MakeImageFromViewTag(viewTag);\n    }\n    return Promise.reject(new Error(\"Invalid view tag\"));\n  };\n  exports.makeImageFromView = makeImageFromView;\n});", "lineCount": 43, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Platform"], [6, 15, 1, 0], [6, 18, 1, 0, "require"], [6, 25, 1, 0], [6, 26, 1, 0, "_dependencyMap"], [6, 40, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_Skia"], [7, 11, 2, 0], [7, 14, 2, 0, "require"], [7, 21, 2, 0], [7, 22, 2, 0, "_dependencyMap"], [7, 36, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_Data"], [8, 11, 3, 0], [8, 14, 3, 0, "require"], [8, 21, 3, 0], [8, 22, 3, 0, "_dependencyMap"], [8, 36, 3, 0], [9, 2, 4, 0], [9, 8, 4, 6, "imgFactory"], [9, 18, 4, 16], [9, 21, 4, 19, "Skia"], [9, 31, 4, 23], [9, 32, 4, 24, "Image"], [9, 37, 4, 29], [9, 38, 4, 30, "MakeImageFromEncoded"], [9, 58, 4, 50], [9, 59, 4, 51, "bind"], [9, 63, 4, 55], [9, 64, 4, 56, "Skia"], [9, 74, 4, 60], [9, 75, 4, 61, "Image"], [9, 80, 4, 66], [9, 81, 4, 67], [11, 2, 6, 0], [12, 0, 7, 0], [13, 0, 8, 0], [14, 2, 9, 7], [14, 8, 9, 13, "useImage"], [14, 16, 9, 21], [14, 19, 9, 24, "useImage"], [14, 20, 9, 25, "source"], [14, 26, 9, 31], [14, 28, 9, 33, "onError"], [14, 35, 9, 40], [14, 40, 9, 45], [14, 44, 9, 45, "useRawData"], [14, 60, 9, 55], [14, 62, 9, 56, "source"], [14, 68, 9, 62], [14, 70, 9, 64, "imgFactory"], [14, 80, 9, 74], [14, 82, 9, 76, "onError"], [14, 89, 9, 83], [14, 90, 9, 84], [16, 2, 11, 0], [17, 0, 12, 0], [18, 0, 13, 0], [19, 0, 14, 0], [20, 0, 15, 0], [21, 0, 16, 0], [22, 0, 17, 0], [23, 0, 18, 0], [24, 0, 19, 0], [25, 0, 20, 0], [26, 2, 11, 0, "exports"], [26, 9, 11, 0], [26, 10, 11, 0, "useImage"], [26, 18, 11, 0], [26, 21, 11, 0, "useImage"], [26, 29, 11, 0], [27, 2, 21, 7], [27, 8, 21, 13, "makeImageFromView"], [27, 25, 21, 30], [27, 28, 21, 33, "makeImageFromView"], [27, 29, 21, 34, "viewRef"], [27, 36, 21, 41], [27, 38, 21, 43, "callback"], [27, 46, 21, 51], [27, 49, 21, 54], [27, 53, 21, 58], [27, 58, 21, 63], [28, 4, 22, 2], [29, 4, 23, 2], [29, 8, 23, 6, "Platform"], [29, 26, 23, 14], [29, 27, 23, 15, "OS"], [29, 29, 23, 17], [29, 34, 23, 22], [29, 39, 23, 27], [29, 41, 23, 29], [30, 6, 24, 4], [30, 10, 24, 8, "callback"], [30, 18, 24, 16], [30, 20, 24, 18], [31, 8, 25, 6], [31, 15, 25, 13, "callback"], [31, 23, 25, 21], [31, 24, 25, 22, "viewRef"], [31, 31, 25, 29], [31, 32, 25, 30], [32, 6, 26, 4], [32, 7, 26, 5], [32, 13, 26, 11], [33, 8, 27, 6, "Promise"], [33, 15, 27, 13], [33, 16, 27, 14, "reject"], [33, 22, 27, 20], [33, 23, 27, 21], [33, 27, 27, 25, "Error"], [33, 32, 27, 30], [33, 33, 27, 31], [33, 97, 27, 95], [33, 98, 27, 96], [33, 99, 27, 97], [34, 6, 28, 4], [35, 4, 29, 2], [36, 4, 30, 2], [36, 10, 30, 8, "viewTag"], [36, 17, 30, 15], [36, 20, 30, 18, "Platform"], [36, 38, 30, 26], [36, 39, 30, 27, "findNodeHandle"], [36, 53, 30, 41], [36, 54, 30, 42, "viewRef"], [36, 61, 30, 49], [36, 62, 30, 50, "current"], [36, 69, 30, 57], [36, 70, 30, 58], [37, 4, 31, 2], [37, 8, 31, 6, "viewTag"], [37, 15, 31, 13], [37, 20, 31, 18], [37, 24, 31, 22], [37, 28, 31, 26, "viewTag"], [37, 35, 31, 33], [37, 40, 31, 38], [37, 41, 31, 39], [37, 43, 31, 41], [38, 6, 32, 4], [38, 13, 32, 11, "Skia"], [38, 23, 32, 15], [38, 24, 32, 16, "Image"], [38, 29, 32, 21], [38, 30, 32, 22, "MakeImageFromViewTag"], [38, 50, 32, 42], [38, 51, 32, 43, "viewTag"], [38, 58, 32, 50], [38, 59, 32, 51], [39, 4, 33, 2], [40, 4, 34, 2], [40, 11, 34, 9, "Promise"], [40, 18, 34, 16], [40, 19, 34, 17, "reject"], [40, 25, 34, 23], [40, 26, 34, 24], [40, 30, 34, 28, "Error"], [40, 35, 34, 33], [40, 36, 34, 34], [40, 54, 34, 52], [40, 55, 34, 53], [40, 56, 34, 54], [41, 2, 35, 0], [41, 3, 35, 1], [42, 2, 35, 2, "exports"], [42, 9, 35, 2], [42, 10, 35, 2, "makeImageFromView"], [42, 27, 35, 2], [42, 30, 35, 2, "makeImageFromView"], [42, 47, 35, 2], [43, 0, 35, 2], [43, 3]], "functionMap": {"names": ["<global>", "useImage", "makeImageFromView"], "mappings": "AAA;wBCQ,4DD;iCEY;CFc"}}, "type": "js/module"}]}