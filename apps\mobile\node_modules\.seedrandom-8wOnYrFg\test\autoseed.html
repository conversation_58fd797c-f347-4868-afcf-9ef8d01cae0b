<html>
<head>
<link rel="stylesheet" href="lib/qunit.css">
</head>
<body>
<div id="qunit"></div>
<div id="qunit-fixture"></div>
<script src="lib/qunit.js"></script>
<script src="../seedrandom.js"></script>
<script>
QUnit.module("Simple Test");

QUnit.test("Check that we can reproduce a seed", function(assert) {
  var seed;
  var time = new Date().getTime();
  var seediter = 50;
  for (var k = 0; k < seediter; ++k) {
    seed = Math.seedrandom();
  }
  var seedtime = (new Date().getTime() - time) / seediter;

  time = new Date().getTime();
  var vals = [];
  var iters = 1000;
  var j;
  for (j = 0; j < iters; ++j) {
    var saw = Math.random();
    vals.push(saw);
  }
  time = new Date().getTime() - time;
  var errors = 0;
  Math.seedrandom(seed);
  for (j = 0; j < vals.length; ++j) {
    var saw = vals[j];
    var got = Math.random();
    assert.equal(saw, got, saw + " vs " + got);
  }

  assert.ok(true, '' +
     'Seeding took ' + seedtime + ' ms per seedrandom' +
     ' in ' + time + ' ms for ' + iters +
     ' calls, ' + (time / iters) + ' ms per random()' + '');
});
</script>
</body>
</html>
