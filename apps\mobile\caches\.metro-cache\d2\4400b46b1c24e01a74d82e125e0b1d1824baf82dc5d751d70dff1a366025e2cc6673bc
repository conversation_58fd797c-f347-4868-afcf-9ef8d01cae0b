{"dependencies": [{"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 47, "index": 47}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.blobToBase64String = blobToBase64String;\n  exports.getContext = getContext;\n  exports.loadImageAsync = loadImageAsync;\n  var _expoModulesCore = require(_dependencyMap[0], \"expo-modules-core\");\n  function getContext(canvas) {\n    const ctx = canvas.getContext('2d');\n    if (!ctx) {\n      throw new _expoModulesCore.CodedError('ERR_IMAGE_MANIPULATOR', 'Failed to create canvas context');\n    }\n    return ctx;\n  }\n  async function blobToBase64String(blob) {\n    const dataURL = await new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onloadend = () => resolve(reader.result);\n      reader.onerror = () => reject(new Error(`Unable to convert blob to base64 string: ${reader.error}`));\n      reader.readAsDataURL(blob);\n    });\n    return dataURL.replace(/^data:image\\/\\w+;base64,/, '');\n  }\n  function loadImageAsync(uri) {\n    return new Promise((resolve, reject) => {\n      const imageSource = new Image();\n      imageSource.crossOrigin = 'anonymous';\n      const canvas = document.createElement('canvas');\n      imageSource.onload = () => {\n        canvas.width = imageSource.naturalWidth;\n        canvas.height = imageSource.naturalHeight;\n        const context = getContext(canvas);\n        context.drawImage(imageSource, 0, 0, imageSource.naturalWidth, imageSource.naturalHeight);\n        resolve(canvas);\n      };\n      imageSource.onerror = () => reject(canvas);\n      imageSource.src = uri;\n    });\n  }\n});", "lineCount": 41, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_expoModulesCore"], [8, 22, 1, 0], [8, 25, 1, 0, "require"], [8, 32, 1, 0], [8, 33, 1, 0, "_dependencyMap"], [8, 47, 1, 0], [9, 2, 3, 7], [9, 11, 3, 16, "getContext"], [9, 21, 3, 26, "getContext"], [9, 22, 3, 27, "canvas"], [9, 28, 3, 52], [9, 30, 3, 80], [10, 4, 4, 2], [10, 10, 4, 8, "ctx"], [10, 13, 4, 11], [10, 16, 4, 14, "canvas"], [10, 22, 4, 20], [10, 23, 4, 21, "getContext"], [10, 33, 4, 31], [10, 34, 4, 32], [10, 38, 4, 36], [10, 39, 4, 37], [11, 4, 5, 2], [11, 8, 5, 6], [11, 9, 5, 7, "ctx"], [11, 12, 5, 10], [11, 14, 5, 12], [12, 6, 6, 4], [12, 12, 6, 10], [12, 16, 6, 14, "CodedError"], [12, 43, 6, 24], [12, 44, 6, 25], [12, 67, 6, 48], [12, 69, 6, 50], [12, 102, 6, 83], [12, 103, 6, 84], [13, 4, 7, 2], [14, 4, 8, 2], [14, 11, 8, 9, "ctx"], [14, 14, 8, 12], [15, 2, 9, 0], [16, 2, 11, 7], [16, 17, 11, 22, "blobToBase64String"], [16, 35, 11, 40, "blobToBase64String"], [16, 36, 11, 41, "blob"], [16, 40, 11, 51], [16, 42, 11, 70], [17, 4, 12, 2], [17, 10, 12, 8, "dataURL"], [17, 17, 12, 15], [17, 20, 12, 18], [17, 26, 12, 24], [17, 30, 12, 28, "Promise"], [17, 37, 12, 35], [17, 38, 12, 44], [17, 39, 12, 45, "resolve"], [17, 46, 12, 52], [17, 48, 12, 54, "reject"], [17, 54, 12, 60], [17, 59, 12, 65], [18, 6, 13, 4], [18, 12, 13, 10, "reader"], [18, 18, 13, 16], [18, 21, 13, 19], [18, 25, 13, 23, "FileReader"], [18, 35, 13, 33], [18, 36, 13, 34], [18, 37, 13, 35], [19, 6, 14, 4, "reader"], [19, 12, 14, 10], [19, 13, 14, 11, "onloadend"], [19, 22, 14, 20], [19, 25, 14, 23], [19, 31, 14, 29, "resolve"], [19, 38, 14, 36], [19, 39, 14, 37, "reader"], [19, 45, 14, 43], [19, 46, 14, 44, "result"], [19, 52, 14, 60], [19, 53, 14, 61], [20, 6, 15, 4, "reader"], [20, 12, 15, 10], [20, 13, 15, 11, "onerror"], [20, 20, 15, 18], [20, 23, 15, 21], [20, 29, 16, 6, "reject"], [20, 35, 16, 12], [20, 36, 16, 13], [20, 40, 16, 17, "Error"], [20, 45, 16, 22], [20, 46, 16, 23], [20, 90, 16, 67, "reader"], [20, 96, 16, 73], [20, 97, 16, 74, "error"], [20, 102, 16, 79], [20, 104, 16, 81], [20, 105, 16, 82], [20, 106, 16, 83], [21, 6, 17, 4, "reader"], [21, 12, 17, 10], [21, 13, 17, 11, "readAsDataURL"], [21, 26, 17, 24], [21, 27, 17, 25, "blob"], [21, 31, 17, 29], [21, 32, 17, 30], [22, 4, 18, 2], [22, 5, 18, 3], [22, 6, 18, 4], [23, 4, 19, 2], [23, 11, 19, 9, "dataURL"], [23, 18, 19, 16], [23, 19, 19, 17, "replace"], [23, 26, 19, 24], [23, 27, 19, 25], [23, 53, 19, 51], [23, 55, 19, 53], [23, 57, 19, 55], [23, 58, 19, 56], [24, 2, 20, 0], [25, 2, 22, 7], [25, 11, 22, 16, "loadImageAsync"], [25, 25, 22, 30, "loadImageAsync"], [25, 26, 22, 31, "uri"], [25, 29, 22, 42], [25, 31, 22, 72], [26, 4, 23, 2], [26, 11, 23, 9], [26, 15, 23, 13, "Promise"], [26, 22, 23, 20], [26, 23, 23, 21], [26, 24, 23, 22, "resolve"], [26, 31, 23, 29], [26, 33, 23, 31, "reject"], [26, 39, 23, 37], [26, 44, 23, 42], [27, 6, 24, 4], [27, 12, 24, 10, "imageSource"], [27, 23, 24, 21], [27, 26, 24, 24], [27, 30, 24, 28, "Image"], [27, 35, 24, 33], [27, 36, 24, 34], [27, 37, 24, 35], [28, 6, 25, 4, "imageSource"], [28, 17, 25, 15], [28, 18, 25, 16, "crossOrigin"], [28, 29, 25, 27], [28, 32, 25, 30], [28, 43, 25, 41], [29, 6, 26, 4], [29, 12, 26, 10, "canvas"], [29, 18, 26, 16], [29, 21, 26, 19, "document"], [29, 29, 26, 27], [29, 30, 26, 28, "createElement"], [29, 43, 26, 41], [29, 44, 26, 42], [29, 52, 26, 50], [29, 53, 26, 51], [30, 6, 27, 4, "imageSource"], [30, 17, 27, 15], [30, 18, 27, 16, "onload"], [30, 24, 27, 22], [30, 27, 27, 25], [30, 33, 27, 31], [31, 8, 28, 6, "canvas"], [31, 14, 28, 12], [31, 15, 28, 13, "width"], [31, 20, 28, 18], [31, 23, 28, 21, "imageSource"], [31, 34, 28, 32], [31, 35, 28, 33, "naturalWidth"], [31, 47, 28, 45], [32, 8, 29, 6, "canvas"], [32, 14, 29, 12], [32, 15, 29, 13, "height"], [32, 21, 29, 19], [32, 24, 29, 22, "imageSource"], [32, 35, 29, 33], [32, 36, 29, 34, "naturalHeight"], [32, 49, 29, 47], [33, 8, 31, 6], [33, 14, 31, 12, "context"], [33, 21, 31, 19], [33, 24, 31, 22, "getContext"], [33, 34, 31, 32], [33, 35, 31, 33, "canvas"], [33, 41, 31, 39], [33, 42, 31, 40], [34, 8, 32, 6, "context"], [34, 15, 32, 13], [34, 16, 32, 14, "drawImage"], [34, 25, 32, 23], [34, 26, 32, 24, "imageSource"], [34, 37, 32, 35], [34, 39, 32, 37], [34, 40, 32, 38], [34, 42, 32, 40], [34, 43, 32, 41], [34, 45, 32, 43, "imageSource"], [34, 56, 32, 54], [34, 57, 32, 55, "naturalWidth"], [34, 69, 32, 67], [34, 71, 32, 69, "imageSource"], [34, 82, 32, 80], [34, 83, 32, 81, "naturalHeight"], [34, 96, 32, 94], [34, 97, 32, 95], [35, 8, 34, 6, "resolve"], [35, 15, 34, 13], [35, 16, 34, 14, "canvas"], [35, 22, 34, 20], [35, 23, 34, 21], [36, 6, 35, 4], [36, 7, 35, 5], [37, 6, 36, 4, "imageSource"], [37, 17, 36, 15], [37, 18, 36, 16, "onerror"], [37, 25, 36, 23], [37, 28, 36, 26], [37, 34, 36, 32, "reject"], [37, 40, 36, 38], [37, 41, 36, 39, "canvas"], [37, 47, 36, 45], [37, 48, 36, 46], [38, 6, 37, 4, "imageSource"], [38, 17, 37, 15], [38, 18, 37, 16, "src"], [38, 21, 37, 19], [38, 24, 37, 22, "uri"], [38, 27, 37, 25], [39, 4, 38, 2], [39, 5, 38, 3], [39, 6, 38, 4], [40, 2, 39, 0], [41, 0, 39, 1], [41, 3]], "functionMap": {"names": ["<global>", "getContext", "blobToBase64String", "Promise$argument_0", "reader.onloadend", "reader.onerror", "loadImageAsync", "imageSource.onload", "imageSource.onerror"], "mappings": "AAA;OCE;CDM;OEE;4CCC;uBCE,sCD;qBEC;mFFC;GDE;CFE;OME;qBHC;yBII;KJQ;0BKC,oBL;GGE;CNC"}}, "type": "js/module"}]}