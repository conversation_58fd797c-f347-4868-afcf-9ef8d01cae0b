{"dependencies": [{"name": "../Skia", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "5eRJ3Y/mp/EEiynYa3WwzXcSMXc=", "exportNames": ["*"]}}, {"name": "./Data", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 36, "index": 68}}], "key": "0fS75tjqpJdM3ThONBfvzOQKEI0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.useAnimatedImage = void 0;\n  var _Skia = require(_dependencyMap[0], \"../Skia\");\n  var _Data = require(_dependencyMap[1], \"./Data\");\n  const animatedImgFactory = _Skia.Skia.AnimatedImage.MakeAnimatedImageFromEncoded.bind(_Skia.Skia.AnimatedImage);\n\n  /**\n   * Returns a Skia Animated Image object\n   * */\n  const useAnimatedImage = (source, onError) => (0, _Data.useRawData)(source, animatedImgFactory, onError);\n  exports.useAnimatedImage = useAnimatedImage;\n});", "lineCount": 15, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Skia"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_Data"], [7, 11, 2, 0], [7, 14, 2, 0, "require"], [7, 21, 2, 0], [7, 22, 2, 0, "_dependencyMap"], [7, 36, 2, 0], [8, 2, 3, 0], [8, 8, 3, 6, "animatedImgFactory"], [8, 26, 3, 24], [8, 29, 3, 27, "Skia"], [8, 39, 3, 31], [8, 40, 3, 32, "AnimatedImage"], [8, 53, 3, 45], [8, 54, 3, 46, "MakeAnimatedImageFromEncoded"], [8, 82, 3, 74], [8, 83, 3, 75, "bind"], [8, 87, 3, 79], [8, 88, 3, 80, "Skia"], [8, 98, 3, 84], [8, 99, 3, 85, "AnimatedImage"], [8, 112, 3, 98], [8, 113, 3, 99], [10, 2, 5, 0], [11, 0, 6, 0], [12, 0, 7, 0], [13, 2, 8, 7], [13, 8, 8, 13, "useAnimatedImage"], [13, 24, 8, 29], [13, 27, 8, 32, "useAnimatedImage"], [13, 28, 8, 33, "source"], [13, 34, 8, 39], [13, 36, 8, 41, "onError"], [13, 43, 8, 48], [13, 48, 8, 53], [13, 52, 8, 53, "useRawData"], [13, 68, 8, 63], [13, 70, 8, 64, "source"], [13, 76, 8, 70], [13, 78, 8, 72, "animatedImgFactory"], [13, 96, 8, 90], [13, 98, 8, 92, "onError"], [13, 105, 8, 99], [13, 106, 8, 100], [14, 2, 8, 101, "exports"], [14, 9, 8, 101], [14, 10, 8, 101, "useAnimatedImage"], [14, 26, 8, 101], [14, 29, 8, 101, "useAnimatedImage"], [14, 45, 8, 101], [15, 0, 8, 101], [15, 3]], "functionMap": {"names": ["<global>", "useAnimatedImage"], "mappings": "AAA;gCCO,oED"}}, "type": "js/module"}]}