{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces with lower confidence threshold to catch more faces\n        const predictions = await model.estimateFaces(tensor, false, 0.7); // Lower threshold from default 0.9\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sensitive face detection criteria\n          if (analysis.skinRatio > 0.15 &&\n          // Lower skin ratio threshold\n          analysis.hasVariation && analysis.brightness > 0.15 &&\n          // Lower brightness threshold\n          analysis.brightness < 0.9) {\n            // Higher max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const detectFacesAggressive = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🚨 Running aggressive face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 6; // Larger blocks for aggressive detection\n\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // More overlap\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // Very relaxed criteria - catch anything that might be a face\n          if (analysis.skinRatio > 0.08 &&\n          // Very low skin ratio\n          analysis.brightness > 0.1 &&\n          // Very low brightness threshold\n          analysis.brightness < 0.95) {\n            // High max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize / img.width,\n                height: blockSize / img.height\n              },\n              confidence: 0.4 // Lower confidence for aggressive detection\n            });\n          }\n        }\n      }\n\n      // Merge overlapping detections\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🚨 Aggressive detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 5); // Allow more faces in aggressive mode\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions:`, {\n          original: {\n            x,\n            y,\n            width,\n            height\n          },\n          canvas: {\n            width: canvasWidth,\n            height: canvasHeight\n          },\n          clamped: {\n            x: clampedX,\n            y: clampedY,\n            width: clampedWidth,\n            height: clampedHeight\n          }\n        });\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          console.log('[EchoCameraWeb] 🔄 Loading TensorFlow.js and BlazeFace...');\n          await loadTensorFlowFaceDetection();\n          console.log('[EchoCameraWeb] ✅ TensorFlow.js loaded, starting face detection...');\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n          console.warn('[EchoCameraWeb] ❌ TensorFlow error details:', {\n            message: tensorFlowError.message,\n            stack: tensorFlowError.stack\n          });\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n\n        // Strategy 3: If still no faces found, use aggressive detection\n        if (detectedFaces.length === 0) {\n          console.log('[EchoCameraWeb] 🔍 No faces found, trying aggressive detection...');\n          detectedFaces = detectFacesAggressive(img, ctx);\n          console.log(`[EchoCameraWeb] 🔍 Aggressive detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected by any method');\n          console.log('[EchoCameraWeb] 🛡️ Applying privacy-first fallback: center region blur');\n\n          // Privacy-first fallback: blur the center region where faces are most likely\n          const centerX = img.width * 0.3;\n          const centerY = img.height * 0.2;\n          const centerWidth = img.width * 0.4;\n          const centerHeight = img.height * 0.6;\n          detectedFaces = [{\n            boundingBox: {\n              xCenter: 0.5,\n              yCenter: 0.5,\n              width: 0.4,\n              height: 0.6\n            },\n            confidence: 0.3\n          }];\n          console.log('[EchoCameraWeb] 🛡️ Applied privacy fallback - center region will be blurred');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n            console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Raw detection data for face ${index + 1}:`, {\n              bbox,\n              imageSize: {\n                width: img.width,\n                height: img.height\n              }\n            });\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n            console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Calculated face coordinates:`, {\n              faceX,\n              faceY,\n              faceWidth,\n              faceHeight,\n              isValid: faceX >= 0 && faceY >= 0 && faceWidth > 0 && faceHeight > 0\n            });\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n\n        // CRITICAL: Update the captured photo state with the blurred version\n        setCapturedPhoto(blurredImageUrl);\n        console.log('[EchoCameraWeb] 🔄 Updated capturedPhoto state with blurred image');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 899,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 900,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 898,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 909,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 911,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 916,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 915,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 919,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 918,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 908,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 907,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 932,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 954,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 955,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 956,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 953,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 952,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 984,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 991,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1008,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1007,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 966,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1021,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1023,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1024,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1022,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1028,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1029,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1027,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1020,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1034,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1033,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1019,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1018,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1040,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1041,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1039,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1060,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1051,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1065,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1046,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 931,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1080,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1082,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1089,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1088,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1096,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1103,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1079,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1078,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1073,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1116,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1117,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1118,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1123,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1119,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1129,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1125,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1115,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1114,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1109,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 929,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1744, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 69, 132, 67], [129, 72, 132, 70], [129, 73, 132, 71], [129, 74, 132, 72], [129, 75, 132, 73], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [180, 10, 187, 42], [181, 10, 188, 12, "analysis"], [181, 18, 188, 20], [181, 19, 188, 21, "hasVariation"], [181, 31, 188, 33], [181, 35, 189, 12, "analysis"], [181, 43, 189, 20], [181, 44, 189, 21, "brightness"], [181, 54, 189, 31], [181, 57, 189, 34], [181, 61, 189, 38], [182, 10, 189, 43], [183, 10, 190, 12, "analysis"], [183, 18, 190, 20], [183, 19, 190, 21, "brightness"], [183, 29, 190, 31], [183, 32, 190, 34], [183, 35, 190, 37], [183, 37, 190, 39], [184, 12, 190, 43], [186, 12, 192, 10, "faces"], [186, 17, 192, 15], [186, 18, 192, 16, "push"], [186, 22, 192, 20], [186, 23, 192, 21], [187, 14, 193, 12, "boundingBox"], [187, 25, 193, 23], [187, 27, 193, 25], [188, 16, 194, 14, "xCenter"], [188, 23, 194, 21], [188, 25, 194, 23], [188, 26, 194, 24, "x"], [188, 27, 194, 25], [188, 30, 194, 28, "blockSize"], [188, 39, 194, 37], [188, 42, 194, 40], [188, 43, 194, 41], [188, 47, 194, 45, "img"], [188, 50, 194, 48], [188, 51, 194, 49, "width"], [188, 56, 194, 54], [189, 16, 195, 14, "yCenter"], [189, 23, 195, 21], [189, 25, 195, 23], [189, 26, 195, 24, "y"], [189, 27, 195, 25], [189, 30, 195, 28, "blockSize"], [189, 39, 195, 37], [189, 42, 195, 40], [189, 43, 195, 41], [189, 47, 195, 45, "img"], [189, 50, 195, 48], [189, 51, 195, 49, "height"], [189, 57, 195, 55], [190, 16, 196, 14, "width"], [190, 21, 196, 19], [190, 23, 196, 22, "blockSize"], [190, 32, 196, 31], [190, 35, 196, 34], [190, 38, 196, 37], [190, 41, 196, 41, "img"], [190, 44, 196, 44], [190, 45, 196, 45, "width"], [190, 50, 196, 50], [191, 16, 197, 14, "height"], [191, 22, 197, 20], [191, 24, 197, 23, "blockSize"], [191, 33, 197, 32], [191, 36, 197, 35], [191, 39, 197, 38], [191, 42, 197, 42, "img"], [191, 45, 197, 45], [191, 46, 197, 46, "height"], [192, 14, 198, 12], [192, 15, 198, 13], [193, 14, 199, 12, "confidence"], [193, 24, 199, 22], [193, 26, 199, 24, "analysis"], [193, 34, 199, 32], [193, 35, 199, 33, "skinRatio"], [193, 44, 199, 42], [193, 47, 199, 45, "analysis"], [193, 55, 199, 53], [193, 56, 199, 54, "variation"], [194, 12, 200, 10], [194, 13, 200, 11], [194, 14, 200, 12], [195, 12, 202, 10, "console"], [195, 19, 202, 17], [195, 20, 202, 18, "log"], [195, 23, 202, 21], [195, 24, 202, 22], [195, 71, 202, 69, "Math"], [195, 75, 202, 73], [195, 76, 202, 74, "round"], [195, 81, 202, 79], [195, 82, 202, 80, "x"], [195, 83, 202, 81], [195, 84, 202, 82], [195, 89, 202, 87, "Math"], [195, 93, 202, 91], [195, 94, 202, 92, "round"], [195, 99, 202, 97], [195, 100, 202, 98, "y"], [195, 101, 202, 99], [195, 102, 202, 100], [195, 115, 202, 113], [195, 116, 202, 114, "analysis"], [195, 124, 202, 122], [195, 125, 202, 123, "skinRatio"], [195, 134, 202, 132], [195, 137, 202, 135], [195, 140, 202, 138], [195, 142, 202, 140, "toFixed"], [195, 149, 202, 147], [195, 150, 202, 148], [195, 151, 202, 149], [195, 152, 202, 150], [195, 169, 202, 167, "analysis"], [195, 177, 202, 175], [195, 178, 202, 176, "variation"], [195, 187, 202, 185], [195, 188, 202, 186, "toFixed"], [195, 195, 202, 193], [195, 196, 202, 194], [195, 197, 202, 195], [195, 198, 202, 196], [195, 215, 202, 213, "analysis"], [195, 223, 202, 221], [195, 224, 202, 222, "brightness"], [195, 234, 202, 232], [195, 235, 202, 233, "toFixed"], [195, 242, 202, 240], [195, 243, 202, 241], [195, 244, 202, 242], [195, 245, 202, 243], [195, 247, 202, 245], [195, 248, 202, 246], [196, 10, 203, 8], [197, 8, 204, 6], [198, 6, 205, 4], [200, 6, 207, 4], [201, 6, 208, 4, "faces"], [201, 11, 208, 9], [201, 12, 208, 10, "sort"], [201, 16, 208, 14], [201, 17, 208, 15], [201, 18, 208, 16, "a"], [201, 19, 208, 17], [201, 21, 208, 19, "b"], [201, 22, 208, 20], [201, 27, 208, 25], [201, 28, 208, 26, "b"], [201, 29, 208, 27], [201, 30, 208, 28, "confidence"], [201, 40, 208, 38], [201, 44, 208, 42], [201, 45, 208, 43], [201, 50, 208, 48, "a"], [201, 51, 208, 49], [201, 52, 208, 50, "confidence"], [201, 62, 208, 60], [201, 66, 208, 64], [201, 67, 208, 65], [201, 68, 208, 66], [201, 69, 208, 67], [202, 6, 209, 4], [202, 12, 209, 10, "mergedFaces"], [202, 23, 209, 21], [202, 26, 209, 24, "mergeFaceDetections"], [202, 45, 209, 43], [202, 46, 209, 44, "faces"], [202, 51, 209, 49], [202, 52, 209, 50], [203, 6, 211, 4, "console"], [203, 13, 211, 11], [203, 14, 211, 12, "log"], [203, 17, 211, 15], [203, 18, 211, 16], [203, 61, 211, 59, "faces"], [203, 66, 211, 64], [203, 67, 211, 65, "length"], [203, 73, 211, 71], [203, 90, 211, 88, "mergedFaces"], [203, 101, 211, 99], [203, 102, 211, 100, "length"], [203, 108, 211, 106], [203, 123, 211, 121], [203, 124, 211, 122], [204, 6, 212, 4], [204, 13, 212, 11, "mergedFaces"], [204, 24, 212, 22], [204, 25, 212, 23, "slice"], [204, 30, 212, 28], [204, 31, 212, 29], [204, 32, 212, 30], [204, 34, 212, 32], [204, 35, 212, 33], [204, 36, 212, 34], [204, 37, 212, 35], [204, 38, 212, 36], [205, 4, 213, 2], [205, 5, 213, 3], [206, 4, 215, 2], [206, 10, 215, 8, "detectFacesAggressive"], [206, 31, 215, 29], [206, 34, 215, 32, "detectFacesAggressive"], [206, 35, 215, 33, "img"], [206, 38, 215, 54], [206, 40, 215, 56, "ctx"], [206, 43, 215, 85], [206, 48, 215, 90], [207, 6, 216, 4, "console"], [207, 13, 216, 11], [207, 14, 216, 12, "log"], [207, 17, 216, 15], [207, 18, 216, 16], [207, 75, 216, 73], [207, 76, 216, 74], [209, 6, 218, 4], [210, 6, 219, 4], [210, 12, 219, 10, "imageData"], [210, 21, 219, 19], [210, 24, 219, 22, "ctx"], [210, 27, 219, 25], [210, 28, 219, 26, "getImageData"], [210, 40, 219, 38], [210, 41, 219, 39], [210, 42, 219, 40], [210, 44, 219, 42], [210, 45, 219, 43], [210, 47, 219, 45, "img"], [210, 50, 219, 48], [210, 51, 219, 49, "width"], [210, 56, 219, 54], [210, 58, 219, 56, "img"], [210, 61, 219, 59], [210, 62, 219, 60, "height"], [210, 68, 219, 66], [210, 69, 219, 67], [211, 6, 220, 4], [211, 12, 220, 10, "data"], [211, 16, 220, 14], [211, 19, 220, 17, "imageData"], [211, 28, 220, 26], [211, 29, 220, 27, "data"], [211, 33, 220, 31], [212, 6, 222, 4], [212, 12, 222, 10, "faces"], [212, 17, 222, 15], [212, 20, 222, 18], [212, 22, 222, 20], [213, 6, 223, 4], [213, 12, 223, 10, "blockSize"], [213, 21, 223, 19], [213, 24, 223, 22, "Math"], [213, 28, 223, 26], [213, 29, 223, 27, "min"], [213, 32, 223, 30], [213, 33, 223, 31, "img"], [213, 36, 223, 34], [213, 37, 223, 35, "width"], [213, 42, 223, 40], [213, 44, 223, 42, "img"], [213, 47, 223, 45], [213, 48, 223, 46, "height"], [213, 54, 223, 52], [213, 55, 223, 53], [213, 58, 223, 56], [213, 59, 223, 57], [213, 60, 223, 58], [213, 61, 223, 59], [215, 6, 225, 4], [215, 11, 225, 9], [215, 15, 225, 13, "y"], [215, 16, 225, 14], [215, 19, 225, 17], [215, 20, 225, 18], [215, 22, 225, 20, "y"], [215, 23, 225, 21], [215, 26, 225, 24, "img"], [215, 29, 225, 27], [215, 30, 225, 28, "height"], [215, 36, 225, 34], [215, 39, 225, 37, "blockSize"], [215, 48, 225, 46], [215, 50, 225, 48, "y"], [215, 51, 225, 49], [215, 55, 225, 53, "blockSize"], [215, 64, 225, 62], [215, 67, 225, 65], [215, 68, 225, 66], [215, 70, 225, 68], [216, 8, 225, 70], [217, 8, 226, 6], [217, 13, 226, 11], [217, 17, 226, 15, "x"], [217, 18, 226, 16], [217, 21, 226, 19], [217, 22, 226, 20], [217, 24, 226, 22, "x"], [217, 25, 226, 23], [217, 28, 226, 26, "img"], [217, 31, 226, 29], [217, 32, 226, 30, "width"], [217, 37, 226, 35], [217, 40, 226, 38, "blockSize"], [217, 49, 226, 47], [217, 51, 226, 49, "x"], [217, 52, 226, 50], [217, 56, 226, 54, "blockSize"], [217, 65, 226, 63], [217, 68, 226, 66], [217, 69, 226, 67], [217, 71, 226, 69], [218, 10, 227, 8], [218, 16, 227, 14, "analysis"], [218, 24, 227, 22], [218, 27, 227, 25, "analyzeRegionForFace"], [218, 47, 227, 45], [218, 48, 227, 46, "data"], [218, 52, 227, 50], [218, 54, 227, 52, "x"], [218, 55, 227, 53], [218, 57, 227, 55, "y"], [218, 58, 227, 56], [218, 60, 227, 58, "blockSize"], [218, 69, 227, 67], [218, 71, 227, 69, "img"], [218, 74, 227, 72], [218, 75, 227, 73, "width"], [218, 80, 227, 78], [218, 82, 227, 80, "img"], [218, 85, 227, 83], [218, 86, 227, 84, "height"], [218, 92, 227, 90], [218, 93, 227, 91], [220, 10, 229, 8], [221, 10, 230, 8], [221, 14, 230, 12, "analysis"], [221, 22, 230, 20], [221, 23, 230, 21, "skinRatio"], [221, 32, 230, 30], [221, 35, 230, 33], [221, 39, 230, 37], [222, 10, 230, 42], [223, 10, 231, 12, "analysis"], [223, 18, 231, 20], [223, 19, 231, 21, "brightness"], [223, 29, 231, 31], [223, 32, 231, 34], [223, 35, 231, 37], [224, 10, 231, 42], [225, 10, 232, 12, "analysis"], [225, 18, 232, 20], [225, 19, 232, 21, "brightness"], [225, 29, 232, 31], [225, 32, 232, 34], [225, 36, 232, 38], [225, 38, 232, 40], [226, 12, 232, 43], [228, 12, 234, 10, "faces"], [228, 17, 234, 15], [228, 18, 234, 16, "push"], [228, 22, 234, 20], [228, 23, 234, 21], [229, 14, 235, 12, "boundingBox"], [229, 25, 235, 23], [229, 27, 235, 25], [230, 16, 236, 14, "xCenter"], [230, 23, 236, 21], [230, 25, 236, 23], [230, 26, 236, 24, "x"], [230, 27, 236, 25], [230, 30, 236, 28, "blockSize"], [230, 39, 236, 37], [230, 42, 236, 40], [230, 43, 236, 41], [230, 47, 236, 45, "img"], [230, 50, 236, 48], [230, 51, 236, 49, "width"], [230, 56, 236, 54], [231, 16, 237, 14, "yCenter"], [231, 23, 237, 21], [231, 25, 237, 23], [231, 26, 237, 24, "y"], [231, 27, 237, 25], [231, 30, 237, 28, "blockSize"], [231, 39, 237, 37], [231, 42, 237, 40], [231, 43, 237, 41], [231, 47, 237, 45, "img"], [231, 50, 237, 48], [231, 51, 237, 49, "height"], [231, 57, 237, 55], [232, 16, 238, 14, "width"], [232, 21, 238, 19], [232, 23, 238, 21, "blockSize"], [232, 32, 238, 30], [232, 35, 238, 33, "img"], [232, 38, 238, 36], [232, 39, 238, 37, "width"], [232, 44, 238, 42], [233, 16, 239, 14, "height"], [233, 22, 239, 20], [233, 24, 239, 22, "blockSize"], [233, 33, 239, 31], [233, 36, 239, 34, "img"], [233, 39, 239, 37], [233, 40, 239, 38, "height"], [234, 14, 240, 12], [234, 15, 240, 13], [235, 14, 241, 12, "confidence"], [235, 24, 241, 22], [235, 26, 241, 24], [235, 29, 241, 27], [235, 30, 241, 28], [236, 12, 242, 10], [236, 13, 242, 11], [236, 14, 242, 12], [237, 10, 243, 8], [238, 8, 244, 6], [239, 6, 245, 4], [241, 6, 247, 4], [242, 6, 248, 4], [242, 12, 248, 10, "mergedFaces"], [242, 23, 248, 21], [242, 26, 248, 24, "mergeFaceDetections"], [242, 45, 248, 43], [242, 46, 248, 44, "faces"], [242, 51, 248, 49], [242, 52, 248, 50], [243, 6, 249, 4, "console"], [243, 13, 249, 11], [243, 14, 249, 12, "log"], [243, 17, 249, 15], [243, 18, 249, 16], [243, 62, 249, 60, "faces"], [243, 67, 249, 65], [243, 68, 249, 66, "length"], [243, 74, 249, 72], [243, 91, 249, 89, "mergedFaces"], [243, 102, 249, 100], [243, 103, 249, 101, "length"], [243, 109, 249, 107], [243, 124, 249, 122], [243, 125, 249, 123], [244, 6, 250, 4], [244, 13, 250, 11, "mergedFaces"], [244, 24, 250, 22], [244, 25, 250, 23, "slice"], [244, 30, 250, 28], [244, 31, 250, 29], [244, 32, 250, 30], [244, 34, 250, 32], [244, 35, 250, 33], [244, 36, 250, 34], [244, 37, 250, 35], [244, 38, 250, 36], [245, 4, 251, 2], [245, 5, 251, 3], [246, 4, 253, 2], [246, 10, 253, 8, "analyzeRegionForFace"], [246, 30, 253, 28], [246, 33, 253, 31, "analyzeRegionForFace"], [246, 34, 253, 32, "data"], [246, 38, 253, 55], [246, 40, 253, 57, "startX"], [246, 46, 253, 71], [246, 48, 253, 73, "startY"], [246, 54, 253, 87], [246, 56, 253, 89, "size"], [246, 60, 253, 101], [246, 62, 253, 103, "imageWidth"], [246, 72, 253, 121], [246, 74, 253, 123, "imageHeight"], [246, 85, 253, 142], [246, 90, 253, 147], [247, 6, 254, 4], [247, 10, 254, 8, "skinPixels"], [247, 20, 254, 18], [247, 23, 254, 21], [247, 24, 254, 22], [248, 6, 255, 4], [248, 10, 255, 8, "totalPixels"], [248, 21, 255, 19], [248, 24, 255, 22], [248, 25, 255, 23], [249, 6, 256, 4], [249, 10, 256, 8, "totalBrightness"], [249, 25, 256, 23], [249, 28, 256, 26], [249, 29, 256, 27], [250, 6, 257, 4], [250, 10, 257, 8, "colorVariations"], [250, 25, 257, 23], [250, 28, 257, 26], [250, 29, 257, 27], [251, 6, 258, 4], [251, 10, 258, 8, "prevR"], [251, 15, 258, 13], [251, 18, 258, 16], [251, 19, 258, 17], [252, 8, 258, 19, "prevG"], [252, 13, 258, 24], [252, 16, 258, 27], [252, 17, 258, 28], [253, 8, 258, 30, "prevB"], [253, 13, 258, 35], [253, 16, 258, 38], [253, 17, 258, 39], [254, 6, 260, 4], [254, 11, 260, 9], [254, 15, 260, 13, "y"], [254, 16, 260, 14], [254, 19, 260, 17, "startY"], [254, 25, 260, 23], [254, 27, 260, 25, "y"], [254, 28, 260, 26], [254, 31, 260, 29, "startY"], [254, 37, 260, 35], [254, 40, 260, 38, "size"], [254, 44, 260, 42], [254, 48, 260, 46, "y"], [254, 49, 260, 47], [254, 52, 260, 50, "imageHeight"], [254, 63, 260, 61], [254, 65, 260, 63, "y"], [254, 66, 260, 64], [254, 68, 260, 66], [254, 70, 260, 68], [255, 8, 261, 6], [255, 13, 261, 11], [255, 17, 261, 15, "x"], [255, 18, 261, 16], [255, 21, 261, 19, "startX"], [255, 27, 261, 25], [255, 29, 261, 27, "x"], [255, 30, 261, 28], [255, 33, 261, 31, "startX"], [255, 39, 261, 37], [255, 42, 261, 40, "size"], [255, 46, 261, 44], [255, 50, 261, 48, "x"], [255, 51, 261, 49], [255, 54, 261, 52, "imageWidth"], [255, 64, 261, 62], [255, 66, 261, 64, "x"], [255, 67, 261, 65], [255, 69, 261, 67], [255, 71, 261, 69], [256, 10, 262, 8], [256, 16, 262, 14, "index"], [256, 21, 262, 19], [256, 24, 262, 22], [256, 25, 262, 23, "y"], [256, 26, 262, 24], [256, 29, 262, 27, "imageWidth"], [256, 39, 262, 37], [256, 42, 262, 40, "x"], [256, 43, 262, 41], [256, 47, 262, 45], [256, 48, 262, 46], [257, 10, 263, 8], [257, 16, 263, 14, "r"], [257, 17, 263, 15], [257, 20, 263, 18, "data"], [257, 24, 263, 22], [257, 25, 263, 23, "index"], [257, 30, 263, 28], [257, 31, 263, 29], [258, 10, 264, 8], [258, 16, 264, 14, "g"], [258, 17, 264, 15], [258, 20, 264, 18, "data"], [258, 24, 264, 22], [258, 25, 264, 23, "index"], [258, 30, 264, 28], [258, 33, 264, 31], [258, 34, 264, 32], [258, 35, 264, 33], [259, 10, 265, 8], [259, 16, 265, 14, "b"], [259, 17, 265, 15], [259, 20, 265, 18, "data"], [259, 24, 265, 22], [259, 25, 265, 23, "index"], [259, 30, 265, 28], [259, 33, 265, 31], [259, 34, 265, 32], [259, 35, 265, 33], [261, 10, 267, 8], [262, 10, 268, 8], [262, 14, 268, 12, "isSkinTone"], [262, 24, 268, 22], [262, 25, 268, 23, "r"], [262, 26, 268, 24], [262, 28, 268, 26, "g"], [262, 29, 268, 27], [262, 31, 268, 29, "b"], [262, 32, 268, 30], [262, 33, 268, 31], [262, 35, 268, 33], [263, 12, 269, 10, "skinPixels"], [263, 22, 269, 20], [263, 24, 269, 22], [264, 10, 270, 8], [266, 10, 272, 8], [267, 10, 273, 8], [267, 16, 273, 14, "brightness"], [267, 26, 273, 24], [267, 29, 273, 27], [267, 30, 273, 28, "r"], [267, 31, 273, 29], [267, 34, 273, 32, "g"], [267, 35, 273, 33], [267, 38, 273, 36, "b"], [267, 39, 273, 37], [267, 44, 273, 42], [267, 45, 273, 43], [267, 48, 273, 46], [267, 51, 273, 49], [267, 52, 273, 50], [268, 10, 274, 8, "totalBrightness"], [268, 25, 274, 23], [268, 29, 274, 27, "brightness"], [268, 39, 274, 37], [270, 10, 276, 8], [271, 10, 277, 8], [271, 14, 277, 12, "totalPixels"], [271, 25, 277, 23], [271, 28, 277, 26], [271, 29, 277, 27], [271, 31, 277, 29], [272, 12, 278, 10], [272, 18, 278, 16, "colorDiff"], [272, 27, 278, 25], [272, 30, 278, 28, "Math"], [272, 34, 278, 32], [272, 35, 278, 33, "abs"], [272, 38, 278, 36], [272, 39, 278, 37, "r"], [272, 40, 278, 38], [272, 43, 278, 41, "prevR"], [272, 48, 278, 46], [272, 49, 278, 47], [272, 52, 278, 50, "Math"], [272, 56, 278, 54], [272, 57, 278, 55, "abs"], [272, 60, 278, 58], [272, 61, 278, 59, "g"], [272, 62, 278, 60], [272, 65, 278, 63, "prevG"], [272, 70, 278, 68], [272, 71, 278, 69], [272, 74, 278, 72, "Math"], [272, 78, 278, 76], [272, 79, 278, 77, "abs"], [272, 82, 278, 80], [272, 83, 278, 81, "b"], [272, 84, 278, 82], [272, 87, 278, 85, "prevB"], [272, 92, 278, 90], [272, 93, 278, 91], [273, 12, 279, 10], [273, 16, 279, 14, "colorDiff"], [273, 25, 279, 23], [273, 28, 279, 26], [273, 30, 279, 28], [273, 32, 279, 30], [274, 14, 279, 32], [275, 14, 280, 12, "colorVariations"], [275, 29, 280, 27], [275, 31, 280, 29], [276, 12, 281, 10], [277, 10, 282, 8], [278, 10, 284, 8, "prevR"], [278, 15, 284, 13], [278, 18, 284, 16, "r"], [278, 19, 284, 17], [279, 10, 284, 19, "prevG"], [279, 15, 284, 24], [279, 18, 284, 27, "g"], [279, 19, 284, 28], [280, 10, 284, 30, "prevB"], [280, 15, 284, 35], [280, 18, 284, 38, "b"], [280, 19, 284, 39], [281, 10, 285, 8, "totalPixels"], [281, 21, 285, 19], [281, 23, 285, 21], [282, 8, 286, 6], [283, 6, 287, 4], [284, 6, 289, 4], [284, 13, 289, 11], [285, 8, 290, 6, "skinRatio"], [285, 17, 290, 15], [285, 19, 290, 17, "skinPixels"], [285, 29, 290, 27], [285, 32, 290, 30, "totalPixels"], [285, 43, 290, 41], [286, 8, 291, 6, "brightness"], [286, 18, 291, 16], [286, 20, 291, 18, "totalBrightness"], [286, 35, 291, 33], [286, 38, 291, 36, "totalPixels"], [286, 49, 291, 47], [287, 8, 292, 6, "variation"], [287, 17, 292, 15], [287, 19, 292, 17, "colorVariations"], [287, 34, 292, 32], [287, 37, 292, 35, "totalPixels"], [287, 48, 292, 46], [288, 8, 293, 6, "hasVariation"], [288, 20, 293, 18], [288, 22, 293, 20, "colorVariations"], [288, 37, 293, 35], [288, 40, 293, 38, "totalPixels"], [288, 51, 293, 49], [288, 54, 293, 52], [288, 57, 293, 55], [288, 58, 293, 56], [289, 6, 294, 4], [289, 7, 294, 5], [290, 4, 295, 2], [290, 5, 295, 3], [291, 4, 297, 2], [291, 10, 297, 8, "isSkinTone"], [291, 20, 297, 18], [291, 23, 297, 21, "isSkinTone"], [291, 24, 297, 22, "r"], [291, 25, 297, 31], [291, 27, 297, 33, "g"], [291, 28, 297, 42], [291, 30, 297, 44, "b"], [291, 31, 297, 53], [291, 36, 297, 58], [292, 6, 298, 4], [293, 6, 299, 4], [293, 13, 300, 6, "r"], [293, 14, 300, 7], [293, 17, 300, 10], [293, 19, 300, 12], [293, 23, 300, 16, "g"], [293, 24, 300, 17], [293, 27, 300, 20], [293, 29, 300, 22], [293, 33, 300, 26, "b"], [293, 34, 300, 27], [293, 37, 300, 30], [293, 39, 300, 32], [293, 43, 301, 6, "r"], [293, 44, 301, 7], [293, 47, 301, 10, "g"], [293, 48, 301, 11], [293, 52, 301, 15, "r"], [293, 53, 301, 16], [293, 56, 301, 19, "b"], [293, 57, 301, 20], [293, 61, 302, 6, "Math"], [293, 65, 302, 10], [293, 66, 302, 11, "abs"], [293, 69, 302, 14], [293, 70, 302, 15, "r"], [293, 71, 302, 16], [293, 74, 302, 19, "g"], [293, 75, 302, 20], [293, 76, 302, 21], [293, 79, 302, 24], [293, 81, 302, 26], [293, 85, 303, 6, "Math"], [293, 89, 303, 10], [293, 90, 303, 11, "max"], [293, 93, 303, 14], [293, 94, 303, 15, "r"], [293, 95, 303, 16], [293, 97, 303, 18, "g"], [293, 98, 303, 19], [293, 100, 303, 21, "b"], [293, 101, 303, 22], [293, 102, 303, 23], [293, 105, 303, 26, "Math"], [293, 109, 303, 30], [293, 110, 303, 31, "min"], [293, 113, 303, 34], [293, 114, 303, 35, "r"], [293, 115, 303, 36], [293, 117, 303, 38, "g"], [293, 118, 303, 39], [293, 120, 303, 41, "b"], [293, 121, 303, 42], [293, 122, 303, 43], [293, 125, 303, 46], [293, 127, 303, 48], [294, 4, 305, 2], [294, 5, 305, 3], [295, 4, 307, 2], [295, 10, 307, 8, "mergeFaceDetections"], [295, 29, 307, 27], [295, 32, 307, 31, "faces"], [295, 37, 307, 43], [295, 41, 307, 48], [296, 6, 308, 4], [296, 10, 308, 8, "faces"], [296, 15, 308, 13], [296, 16, 308, 14, "length"], [296, 22, 308, 20], [296, 26, 308, 24], [296, 27, 308, 25], [296, 29, 308, 27], [296, 36, 308, 34, "faces"], [296, 41, 308, 39], [297, 6, 310, 4], [297, 12, 310, 10, "merged"], [297, 18, 310, 16], [297, 21, 310, 19], [297, 23, 310, 21], [298, 6, 311, 4], [298, 12, 311, 10, "used"], [298, 16, 311, 14], [298, 19, 311, 17], [298, 23, 311, 21, "Set"], [298, 26, 311, 24], [298, 27, 311, 25], [298, 28, 311, 26], [299, 6, 313, 4], [299, 11, 313, 9], [299, 15, 313, 13, "i"], [299, 16, 313, 14], [299, 19, 313, 17], [299, 20, 313, 18], [299, 22, 313, 20, "i"], [299, 23, 313, 21], [299, 26, 313, 24, "faces"], [299, 31, 313, 29], [299, 32, 313, 30, "length"], [299, 38, 313, 36], [299, 40, 313, 38, "i"], [299, 41, 313, 39], [299, 43, 313, 41], [299, 45, 313, 43], [300, 8, 314, 6], [300, 12, 314, 10, "used"], [300, 16, 314, 14], [300, 17, 314, 15, "has"], [300, 20, 314, 18], [300, 21, 314, 19, "i"], [300, 22, 314, 20], [300, 23, 314, 21], [300, 25, 314, 23], [301, 8, 316, 6], [301, 12, 316, 10, "currentFace"], [301, 23, 316, 21], [301, 26, 316, 24, "faces"], [301, 31, 316, 29], [301, 32, 316, 30, "i"], [301, 33, 316, 31], [301, 34, 316, 32], [302, 8, 317, 6, "used"], [302, 12, 317, 10], [302, 13, 317, 11, "add"], [302, 16, 317, 14], [302, 17, 317, 15, "i"], [302, 18, 317, 16], [302, 19, 317, 17], [304, 8, 319, 6], [305, 8, 320, 6], [305, 13, 320, 11], [305, 17, 320, 15, "j"], [305, 18, 320, 16], [305, 21, 320, 19, "i"], [305, 22, 320, 20], [305, 25, 320, 23], [305, 26, 320, 24], [305, 28, 320, 26, "j"], [305, 29, 320, 27], [305, 32, 320, 30, "faces"], [305, 37, 320, 35], [305, 38, 320, 36, "length"], [305, 44, 320, 42], [305, 46, 320, 44, "j"], [305, 47, 320, 45], [305, 49, 320, 47], [305, 51, 320, 49], [306, 10, 321, 8], [306, 14, 321, 12, "used"], [306, 18, 321, 16], [306, 19, 321, 17, "has"], [306, 22, 321, 20], [306, 23, 321, 21, "j"], [306, 24, 321, 22], [306, 25, 321, 23], [306, 27, 321, 25], [307, 10, 323, 8], [307, 16, 323, 14, "overlap"], [307, 23, 323, 21], [307, 26, 323, 24, "calculateOverlap"], [307, 42, 323, 40], [307, 43, 323, 41, "currentFace"], [307, 54, 323, 52], [307, 55, 323, 53, "boundingBox"], [307, 66, 323, 64], [307, 68, 323, 66, "faces"], [307, 73, 323, 71], [307, 74, 323, 72, "j"], [307, 75, 323, 73], [307, 76, 323, 74], [307, 77, 323, 75, "boundingBox"], [307, 88, 323, 86], [307, 89, 323, 87], [308, 10, 324, 8], [308, 14, 324, 12, "overlap"], [308, 21, 324, 19], [308, 24, 324, 22], [308, 27, 324, 25], [308, 29, 324, 27], [309, 12, 324, 29], [310, 12, 325, 10], [311, 12, 326, 10, "currentFace"], [311, 23, 326, 21], [311, 26, 326, 24, "mergeTwoFaces"], [311, 39, 326, 37], [311, 40, 326, 38, "currentFace"], [311, 51, 326, 49], [311, 53, 326, 51, "faces"], [311, 58, 326, 56], [311, 59, 326, 57, "j"], [311, 60, 326, 58], [311, 61, 326, 59], [311, 62, 326, 60], [312, 12, 327, 10, "used"], [312, 16, 327, 14], [312, 17, 327, 15, "add"], [312, 20, 327, 18], [312, 21, 327, 19, "j"], [312, 22, 327, 20], [312, 23, 327, 21], [313, 10, 328, 8], [314, 8, 329, 6], [315, 8, 331, 6, "merged"], [315, 14, 331, 12], [315, 15, 331, 13, "push"], [315, 19, 331, 17], [315, 20, 331, 18, "currentFace"], [315, 31, 331, 29], [315, 32, 331, 30], [316, 6, 332, 4], [317, 6, 334, 4], [317, 13, 334, 11, "merged"], [317, 19, 334, 17], [318, 4, 335, 2], [318, 5, 335, 3], [319, 4, 337, 2], [319, 10, 337, 8, "calculateOverlap"], [319, 26, 337, 24], [319, 29, 337, 27, "calculateOverlap"], [319, 30, 337, 28, "box1"], [319, 34, 337, 37], [319, 36, 337, 39, "box2"], [319, 40, 337, 48], [319, 45, 337, 53], [320, 6, 338, 4], [320, 12, 338, 10, "x1"], [320, 14, 338, 12], [320, 17, 338, 15, "Math"], [320, 21, 338, 19], [320, 22, 338, 20, "max"], [320, 25, 338, 23], [320, 26, 338, 24, "box1"], [320, 30, 338, 28], [320, 31, 338, 29, "xCenter"], [320, 38, 338, 36], [320, 41, 338, 39, "box1"], [320, 45, 338, 43], [320, 46, 338, 44, "width"], [320, 51, 338, 49], [320, 54, 338, 50], [320, 55, 338, 51], [320, 57, 338, 53, "box2"], [320, 61, 338, 57], [320, 62, 338, 58, "xCenter"], [320, 69, 338, 65], [320, 72, 338, 68, "box2"], [320, 76, 338, 72], [320, 77, 338, 73, "width"], [320, 82, 338, 78], [320, 85, 338, 79], [320, 86, 338, 80], [320, 87, 338, 81], [321, 6, 339, 4], [321, 12, 339, 10, "y1"], [321, 14, 339, 12], [321, 17, 339, 15, "Math"], [321, 21, 339, 19], [321, 22, 339, 20, "max"], [321, 25, 339, 23], [321, 26, 339, 24, "box1"], [321, 30, 339, 28], [321, 31, 339, 29, "yCenter"], [321, 38, 339, 36], [321, 41, 339, 39, "box1"], [321, 45, 339, 43], [321, 46, 339, 44, "height"], [321, 52, 339, 50], [321, 55, 339, 51], [321, 56, 339, 52], [321, 58, 339, 54, "box2"], [321, 62, 339, 58], [321, 63, 339, 59, "yCenter"], [321, 70, 339, 66], [321, 73, 339, 69, "box2"], [321, 77, 339, 73], [321, 78, 339, 74, "height"], [321, 84, 339, 80], [321, 87, 339, 81], [321, 88, 339, 82], [321, 89, 339, 83], [322, 6, 340, 4], [322, 12, 340, 10, "x2"], [322, 14, 340, 12], [322, 17, 340, 15, "Math"], [322, 21, 340, 19], [322, 22, 340, 20, "min"], [322, 25, 340, 23], [322, 26, 340, 24, "box1"], [322, 30, 340, 28], [322, 31, 340, 29, "xCenter"], [322, 38, 340, 36], [322, 41, 340, 39, "box1"], [322, 45, 340, 43], [322, 46, 340, 44, "width"], [322, 51, 340, 49], [322, 54, 340, 50], [322, 55, 340, 51], [322, 57, 340, 53, "box2"], [322, 61, 340, 57], [322, 62, 340, 58, "xCenter"], [322, 69, 340, 65], [322, 72, 340, 68, "box2"], [322, 76, 340, 72], [322, 77, 340, 73, "width"], [322, 82, 340, 78], [322, 85, 340, 79], [322, 86, 340, 80], [322, 87, 340, 81], [323, 6, 341, 4], [323, 12, 341, 10, "y2"], [323, 14, 341, 12], [323, 17, 341, 15, "Math"], [323, 21, 341, 19], [323, 22, 341, 20, "min"], [323, 25, 341, 23], [323, 26, 341, 24, "box1"], [323, 30, 341, 28], [323, 31, 341, 29, "yCenter"], [323, 38, 341, 36], [323, 41, 341, 39, "box1"], [323, 45, 341, 43], [323, 46, 341, 44, "height"], [323, 52, 341, 50], [323, 55, 341, 51], [323, 56, 341, 52], [323, 58, 341, 54, "box2"], [323, 62, 341, 58], [323, 63, 341, 59, "yCenter"], [323, 70, 341, 66], [323, 73, 341, 69, "box2"], [323, 77, 341, 73], [323, 78, 341, 74, "height"], [323, 84, 341, 80], [323, 87, 341, 81], [323, 88, 341, 82], [323, 89, 341, 83], [324, 6, 343, 4], [324, 10, 343, 8, "x2"], [324, 12, 343, 10], [324, 16, 343, 14, "x1"], [324, 18, 343, 16], [324, 22, 343, 20, "y2"], [324, 24, 343, 22], [324, 28, 343, 26, "y1"], [324, 30, 343, 28], [324, 32, 343, 30], [324, 39, 343, 37], [324, 40, 343, 38], [325, 6, 345, 4], [325, 12, 345, 10, "overlapArea"], [325, 23, 345, 21], [325, 26, 345, 24], [325, 27, 345, 25, "x2"], [325, 29, 345, 27], [325, 32, 345, 30, "x1"], [325, 34, 345, 32], [325, 39, 345, 37, "y2"], [325, 41, 345, 39], [325, 44, 345, 42, "y1"], [325, 46, 345, 44], [325, 47, 345, 45], [326, 6, 346, 4], [326, 12, 346, 10, "box1Area"], [326, 20, 346, 18], [326, 23, 346, 21, "box1"], [326, 27, 346, 25], [326, 28, 346, 26, "width"], [326, 33, 346, 31], [326, 36, 346, 34, "box1"], [326, 40, 346, 38], [326, 41, 346, 39, "height"], [326, 47, 346, 45], [327, 6, 347, 4], [327, 12, 347, 10, "box2Area"], [327, 20, 347, 18], [327, 23, 347, 21, "box2"], [327, 27, 347, 25], [327, 28, 347, 26, "width"], [327, 33, 347, 31], [327, 36, 347, 34, "box2"], [327, 40, 347, 38], [327, 41, 347, 39, "height"], [327, 47, 347, 45], [328, 6, 349, 4], [328, 13, 349, 11, "overlapArea"], [328, 24, 349, 22], [328, 27, 349, 25, "Math"], [328, 31, 349, 29], [328, 32, 349, 30, "min"], [328, 35, 349, 33], [328, 36, 349, 34, "box1Area"], [328, 44, 349, 42], [328, 46, 349, 44, "box2Area"], [328, 54, 349, 52], [328, 55, 349, 53], [329, 4, 350, 2], [329, 5, 350, 3], [330, 4, 352, 2], [330, 10, 352, 8, "mergeTwoFaces"], [330, 23, 352, 21], [330, 26, 352, 24, "mergeTwoFaces"], [330, 27, 352, 25, "face1"], [330, 32, 352, 35], [330, 34, 352, 37, "face2"], [330, 39, 352, 47], [330, 44, 352, 52], [331, 6, 353, 4], [331, 12, 353, 10, "box1"], [331, 16, 353, 14], [331, 19, 353, 17, "face1"], [331, 24, 353, 22], [331, 25, 353, 23, "boundingBox"], [331, 36, 353, 34], [332, 6, 354, 4], [332, 12, 354, 10, "box2"], [332, 16, 354, 14], [332, 19, 354, 17, "face2"], [332, 24, 354, 22], [332, 25, 354, 23, "boundingBox"], [332, 36, 354, 34], [333, 6, 356, 4], [333, 12, 356, 10, "left"], [333, 16, 356, 14], [333, 19, 356, 17, "Math"], [333, 23, 356, 21], [333, 24, 356, 22, "min"], [333, 27, 356, 25], [333, 28, 356, 26, "box1"], [333, 32, 356, 30], [333, 33, 356, 31, "xCenter"], [333, 40, 356, 38], [333, 43, 356, 41, "box1"], [333, 47, 356, 45], [333, 48, 356, 46, "width"], [333, 53, 356, 51], [333, 56, 356, 52], [333, 57, 356, 53], [333, 59, 356, 55, "box2"], [333, 63, 356, 59], [333, 64, 356, 60, "xCenter"], [333, 71, 356, 67], [333, 74, 356, 70, "box2"], [333, 78, 356, 74], [333, 79, 356, 75, "width"], [333, 84, 356, 80], [333, 87, 356, 81], [333, 88, 356, 82], [333, 89, 356, 83], [334, 6, 357, 4], [334, 12, 357, 10, "right"], [334, 17, 357, 15], [334, 20, 357, 18, "Math"], [334, 24, 357, 22], [334, 25, 357, 23, "max"], [334, 28, 357, 26], [334, 29, 357, 27, "box1"], [334, 33, 357, 31], [334, 34, 357, 32, "xCenter"], [334, 41, 357, 39], [334, 44, 357, 42, "box1"], [334, 48, 357, 46], [334, 49, 357, 47, "width"], [334, 54, 357, 52], [334, 57, 357, 53], [334, 58, 357, 54], [334, 60, 357, 56, "box2"], [334, 64, 357, 60], [334, 65, 357, 61, "xCenter"], [334, 72, 357, 68], [334, 75, 357, 71, "box2"], [334, 79, 357, 75], [334, 80, 357, 76, "width"], [334, 85, 357, 81], [334, 88, 357, 82], [334, 89, 357, 83], [334, 90, 357, 84], [335, 6, 358, 4], [335, 12, 358, 10, "top"], [335, 15, 358, 13], [335, 18, 358, 16, "Math"], [335, 22, 358, 20], [335, 23, 358, 21, "min"], [335, 26, 358, 24], [335, 27, 358, 25, "box1"], [335, 31, 358, 29], [335, 32, 358, 30, "yCenter"], [335, 39, 358, 37], [335, 42, 358, 40, "box1"], [335, 46, 358, 44], [335, 47, 358, 45, "height"], [335, 53, 358, 51], [335, 56, 358, 52], [335, 57, 358, 53], [335, 59, 358, 55, "box2"], [335, 63, 358, 59], [335, 64, 358, 60, "yCenter"], [335, 71, 358, 67], [335, 74, 358, 70, "box2"], [335, 78, 358, 74], [335, 79, 358, 75, "height"], [335, 85, 358, 81], [335, 88, 358, 82], [335, 89, 358, 83], [335, 90, 358, 84], [336, 6, 359, 4], [336, 12, 359, 10, "bottom"], [336, 18, 359, 16], [336, 21, 359, 19, "Math"], [336, 25, 359, 23], [336, 26, 359, 24, "max"], [336, 29, 359, 27], [336, 30, 359, 28, "box1"], [336, 34, 359, 32], [336, 35, 359, 33, "yCenter"], [336, 42, 359, 40], [336, 45, 359, 43, "box1"], [336, 49, 359, 47], [336, 50, 359, 48, "height"], [336, 56, 359, 54], [336, 59, 359, 55], [336, 60, 359, 56], [336, 62, 359, 58, "box2"], [336, 66, 359, 62], [336, 67, 359, 63, "yCenter"], [336, 74, 359, 70], [336, 77, 359, 73, "box2"], [336, 81, 359, 77], [336, 82, 359, 78, "height"], [336, 88, 359, 84], [336, 91, 359, 85], [336, 92, 359, 86], [336, 93, 359, 87], [337, 6, 361, 4], [337, 13, 361, 11], [338, 8, 362, 6, "boundingBox"], [338, 19, 362, 17], [338, 21, 362, 19], [339, 10, 363, 8, "xCenter"], [339, 17, 363, 15], [339, 19, 363, 17], [339, 20, 363, 18, "left"], [339, 24, 363, 22], [339, 27, 363, 25, "right"], [339, 32, 363, 30], [339, 36, 363, 34], [339, 37, 363, 35], [340, 10, 364, 8, "yCenter"], [340, 17, 364, 15], [340, 19, 364, 17], [340, 20, 364, 18, "top"], [340, 23, 364, 21], [340, 26, 364, 24, "bottom"], [340, 32, 364, 30], [340, 36, 364, 34], [340, 37, 364, 35], [341, 10, 365, 8, "width"], [341, 15, 365, 13], [341, 17, 365, 15, "right"], [341, 22, 365, 20], [341, 25, 365, 23, "left"], [341, 29, 365, 27], [342, 10, 366, 8, "height"], [342, 16, 366, 14], [342, 18, 366, 16, "bottom"], [342, 24, 366, 22], [342, 27, 366, 25, "top"], [343, 8, 367, 6], [344, 6, 368, 4], [344, 7, 368, 5], [345, 4, 369, 2], [345, 5, 369, 3], [347, 4, 371, 2], [348, 4, 372, 2], [348, 10, 372, 8, "applyStrongBlur"], [348, 25, 372, 23], [348, 28, 372, 26, "applyStrongBlur"], [348, 29, 372, 27, "ctx"], [348, 32, 372, 56], [348, 34, 372, 58, "x"], [348, 35, 372, 67], [348, 37, 372, 69, "y"], [348, 38, 372, 78], [348, 40, 372, 80, "width"], [348, 45, 372, 93], [348, 47, 372, 95, "height"], [348, 53, 372, 109], [348, 58, 372, 114], [349, 6, 373, 4], [350, 6, 374, 4], [350, 12, 374, 10, "canvasWidth"], [350, 23, 374, 21], [350, 26, 374, 24, "ctx"], [350, 29, 374, 27], [350, 30, 374, 28, "canvas"], [350, 36, 374, 34], [350, 37, 374, 35, "width"], [350, 42, 374, 40], [351, 6, 375, 4], [351, 12, 375, 10, "canvasHeight"], [351, 24, 375, 22], [351, 27, 375, 25, "ctx"], [351, 30, 375, 28], [351, 31, 375, 29, "canvas"], [351, 37, 375, 35], [351, 38, 375, 36, "height"], [351, 44, 375, 42], [352, 6, 377, 4], [352, 12, 377, 10, "clampedX"], [352, 20, 377, 18], [352, 23, 377, 21, "Math"], [352, 27, 377, 25], [352, 28, 377, 26, "max"], [352, 31, 377, 29], [352, 32, 377, 30], [352, 33, 377, 31], [352, 35, 377, 33, "Math"], [352, 39, 377, 37], [352, 40, 377, 38, "min"], [352, 43, 377, 41], [352, 44, 377, 42, "Math"], [352, 48, 377, 46], [352, 49, 377, 47, "floor"], [352, 54, 377, 52], [352, 55, 377, 53, "x"], [352, 56, 377, 54], [352, 57, 377, 55], [352, 59, 377, 57, "canvasWidth"], [352, 70, 377, 68], [352, 73, 377, 71], [352, 74, 377, 72], [352, 75, 377, 73], [352, 76, 377, 74], [353, 6, 378, 4], [353, 12, 378, 10, "clampedY"], [353, 20, 378, 18], [353, 23, 378, 21, "Math"], [353, 27, 378, 25], [353, 28, 378, 26, "max"], [353, 31, 378, 29], [353, 32, 378, 30], [353, 33, 378, 31], [353, 35, 378, 33, "Math"], [353, 39, 378, 37], [353, 40, 378, 38, "min"], [353, 43, 378, 41], [353, 44, 378, 42, "Math"], [353, 48, 378, 46], [353, 49, 378, 47, "floor"], [353, 54, 378, 52], [353, 55, 378, 53, "y"], [353, 56, 378, 54], [353, 57, 378, 55], [353, 59, 378, 57, "canvasHeight"], [353, 71, 378, 69], [353, 74, 378, 72], [353, 75, 378, 73], [353, 76, 378, 74], [353, 77, 378, 75], [354, 6, 379, 4], [354, 12, 379, 10, "<PERSON><PERSON><PERSON><PERSON>"], [354, 24, 379, 22], [354, 27, 379, 25, "Math"], [354, 31, 379, 29], [354, 32, 379, 30, "min"], [354, 35, 379, 33], [354, 36, 379, 34, "Math"], [354, 40, 379, 38], [354, 41, 379, 39, "floor"], [354, 46, 379, 44], [354, 47, 379, 45, "width"], [354, 52, 379, 50], [354, 53, 379, 51], [354, 55, 379, 53, "canvasWidth"], [354, 66, 379, 64], [354, 69, 379, 67, "clampedX"], [354, 77, 379, 75], [354, 78, 379, 76], [355, 6, 380, 4], [355, 12, 380, 10, "clampedHeight"], [355, 25, 380, 23], [355, 28, 380, 26, "Math"], [355, 32, 380, 30], [355, 33, 380, 31, "min"], [355, 36, 380, 34], [355, 37, 380, 35, "Math"], [355, 41, 380, 39], [355, 42, 380, 40, "floor"], [355, 47, 380, 45], [355, 48, 380, 46, "height"], [355, 54, 380, 52], [355, 55, 380, 53], [355, 57, 380, 55, "canvasHeight"], [355, 69, 380, 67], [355, 72, 380, 70, "clampedY"], [355, 80, 380, 78], [355, 81, 380, 79], [356, 6, 382, 4], [356, 10, 382, 8, "<PERSON><PERSON><PERSON><PERSON>"], [356, 22, 382, 20], [356, 26, 382, 24], [356, 27, 382, 25], [356, 31, 382, 29, "clampedHeight"], [356, 44, 382, 42], [356, 48, 382, 46], [356, 49, 382, 47], [356, 51, 382, 49], [357, 8, 383, 6, "console"], [357, 15, 383, 13], [357, 16, 383, 14, "warn"], [357, 20, 383, 18], [357, 21, 383, 19], [357, 73, 383, 71], [357, 75, 383, 73], [358, 10, 384, 8, "original"], [358, 18, 384, 16], [358, 20, 384, 18], [359, 12, 384, 20, "x"], [359, 13, 384, 21], [360, 12, 384, 23, "y"], [360, 13, 384, 24], [361, 12, 384, 26, "width"], [361, 17, 384, 31], [362, 12, 384, 33, "height"], [363, 10, 384, 40], [363, 11, 384, 41], [364, 10, 385, 8, "canvas"], [364, 16, 385, 14], [364, 18, 385, 16], [365, 12, 385, 18, "width"], [365, 17, 385, 23], [365, 19, 385, 25, "canvasWidth"], [365, 30, 385, 36], [366, 12, 385, 38, "height"], [366, 18, 385, 44], [366, 20, 385, 46, "canvasHeight"], [367, 10, 385, 59], [367, 11, 385, 60], [368, 10, 386, 8, "clamped"], [368, 17, 386, 15], [368, 19, 386, 17], [369, 12, 386, 19, "x"], [369, 13, 386, 20], [369, 15, 386, 22, "clampedX"], [369, 23, 386, 30], [370, 12, 386, 32, "y"], [370, 13, 386, 33], [370, 15, 386, 35, "clampedY"], [370, 23, 386, 43], [371, 12, 386, 45, "width"], [371, 17, 386, 50], [371, 19, 386, 52, "<PERSON><PERSON><PERSON><PERSON>"], [371, 31, 386, 64], [372, 12, 386, 66, "height"], [372, 18, 386, 72], [372, 20, 386, 74, "clampedHeight"], [373, 10, 386, 88], [374, 8, 387, 6], [374, 9, 387, 7], [374, 10, 387, 8], [375, 8, 388, 6], [376, 6, 389, 4], [378, 6, 391, 4], [379, 6, 392, 4], [379, 12, 392, 10, "imageData"], [379, 21, 392, 19], [379, 24, 392, 22, "ctx"], [379, 27, 392, 25], [379, 28, 392, 26, "getImageData"], [379, 40, 392, 38], [379, 41, 392, 39, "clampedX"], [379, 49, 392, 47], [379, 51, 392, 49, "clampedY"], [379, 59, 392, 57], [379, 61, 392, 59, "<PERSON><PERSON><PERSON><PERSON>"], [379, 73, 392, 71], [379, 75, 392, 73, "clampedHeight"], [379, 88, 392, 86], [379, 89, 392, 87], [380, 6, 393, 4], [380, 12, 393, 10, "data"], [380, 16, 393, 14], [380, 19, 393, 17, "imageData"], [380, 28, 393, 26], [380, 29, 393, 27, "data"], [380, 33, 393, 31], [382, 6, 395, 4], [383, 6, 396, 4], [383, 12, 396, 10, "pixelSize"], [383, 21, 396, 19], [383, 24, 396, 22, "Math"], [383, 28, 396, 26], [383, 29, 396, 27, "max"], [383, 32, 396, 30], [383, 33, 396, 31], [383, 35, 396, 33], [383, 37, 396, 35, "Math"], [383, 41, 396, 39], [383, 42, 396, 40, "min"], [383, 45, 396, 43], [383, 46, 396, 44, "<PERSON><PERSON><PERSON><PERSON>"], [383, 58, 396, 56], [383, 60, 396, 58, "clampedHeight"], [383, 73, 396, 71], [383, 74, 396, 72], [383, 77, 396, 75], [383, 78, 396, 76], [383, 79, 396, 77], [384, 6, 398, 4], [384, 11, 398, 9], [384, 15, 398, 13, "py"], [384, 17, 398, 15], [384, 20, 398, 18], [384, 21, 398, 19], [384, 23, 398, 21, "py"], [384, 25, 398, 23], [384, 28, 398, 26, "clampedHeight"], [384, 41, 398, 39], [384, 43, 398, 41, "py"], [384, 45, 398, 43], [384, 49, 398, 47, "pixelSize"], [384, 58, 398, 56], [384, 60, 398, 58], [385, 8, 399, 6], [385, 13, 399, 11], [385, 17, 399, 15, "px"], [385, 19, 399, 17], [385, 22, 399, 20], [385, 23, 399, 21], [385, 25, 399, 23, "px"], [385, 27, 399, 25], [385, 30, 399, 28, "<PERSON><PERSON><PERSON><PERSON>"], [385, 42, 399, 40], [385, 44, 399, 42, "px"], [385, 46, 399, 44], [385, 50, 399, 48, "pixelSize"], [385, 59, 399, 57], [385, 61, 399, 59], [386, 10, 400, 8], [387, 10, 401, 8], [387, 14, 401, 12, "r"], [387, 15, 401, 13], [387, 18, 401, 16], [387, 19, 401, 17], [388, 12, 401, 19, "g"], [388, 13, 401, 20], [388, 16, 401, 23], [388, 17, 401, 24], [389, 12, 401, 26, "b"], [389, 13, 401, 27], [389, 16, 401, 30], [389, 17, 401, 31], [390, 12, 401, 33, "count"], [390, 17, 401, 38], [390, 20, 401, 41], [390, 21, 401, 42], [391, 10, 403, 8], [391, 15, 403, 13], [391, 19, 403, 17, "dy"], [391, 21, 403, 19], [391, 24, 403, 22], [391, 25, 403, 23], [391, 27, 403, 25, "dy"], [391, 29, 403, 27], [391, 32, 403, 30, "pixelSize"], [391, 41, 403, 39], [391, 45, 403, 43, "py"], [391, 47, 403, 45], [391, 50, 403, 48, "dy"], [391, 52, 403, 50], [391, 55, 403, 53, "clampedHeight"], [391, 68, 403, 66], [391, 70, 403, 68, "dy"], [391, 72, 403, 70], [391, 74, 403, 72], [391, 76, 403, 74], [392, 12, 404, 10], [392, 17, 404, 15], [392, 21, 404, 19, "dx"], [392, 23, 404, 21], [392, 26, 404, 24], [392, 27, 404, 25], [392, 29, 404, 27, "dx"], [392, 31, 404, 29], [392, 34, 404, 32, "pixelSize"], [392, 43, 404, 41], [392, 47, 404, 45, "px"], [392, 49, 404, 47], [392, 52, 404, 50, "dx"], [392, 54, 404, 52], [392, 57, 404, 55, "<PERSON><PERSON><PERSON><PERSON>"], [392, 69, 404, 67], [392, 71, 404, 69, "dx"], [392, 73, 404, 71], [392, 75, 404, 73], [392, 77, 404, 75], [393, 14, 405, 12], [393, 20, 405, 18, "index"], [393, 25, 405, 23], [393, 28, 405, 26], [393, 29, 405, 27], [393, 30, 405, 28, "py"], [393, 32, 405, 30], [393, 35, 405, 33, "dy"], [393, 37, 405, 35], [393, 41, 405, 39, "<PERSON><PERSON><PERSON><PERSON>"], [393, 53, 405, 51], [393, 57, 405, 55, "px"], [393, 59, 405, 57], [393, 62, 405, 60, "dx"], [393, 64, 405, 62], [393, 65, 405, 63], [393, 69, 405, 67], [393, 70, 405, 68], [394, 14, 406, 12, "r"], [394, 15, 406, 13], [394, 19, 406, 17, "data"], [394, 23, 406, 21], [394, 24, 406, 22, "index"], [394, 29, 406, 27], [394, 30, 406, 28], [395, 14, 407, 12, "g"], [395, 15, 407, 13], [395, 19, 407, 17, "data"], [395, 23, 407, 21], [395, 24, 407, 22, "index"], [395, 29, 407, 27], [395, 32, 407, 30], [395, 33, 407, 31], [395, 34, 407, 32], [396, 14, 408, 12, "b"], [396, 15, 408, 13], [396, 19, 408, 17, "data"], [396, 23, 408, 21], [396, 24, 408, 22, "index"], [396, 29, 408, 27], [396, 32, 408, 30], [396, 33, 408, 31], [396, 34, 408, 32], [397, 14, 409, 12, "count"], [397, 19, 409, 17], [397, 21, 409, 19], [398, 12, 410, 10], [399, 10, 411, 8], [400, 10, 413, 8], [400, 14, 413, 12, "count"], [400, 19, 413, 17], [400, 22, 413, 20], [400, 23, 413, 21], [400, 25, 413, 23], [401, 12, 414, 10, "r"], [401, 13, 414, 11], [401, 16, 414, 14, "Math"], [401, 20, 414, 18], [401, 21, 414, 19, "floor"], [401, 26, 414, 24], [401, 27, 414, 25, "r"], [401, 28, 414, 26], [401, 31, 414, 29, "count"], [401, 36, 414, 34], [401, 37, 414, 35], [402, 12, 415, 10, "g"], [402, 13, 415, 11], [402, 16, 415, 14, "Math"], [402, 20, 415, 18], [402, 21, 415, 19, "floor"], [402, 26, 415, 24], [402, 27, 415, 25, "g"], [402, 28, 415, 26], [402, 31, 415, 29, "count"], [402, 36, 415, 34], [402, 37, 415, 35], [403, 12, 416, 10, "b"], [403, 13, 416, 11], [403, 16, 416, 14, "Math"], [403, 20, 416, 18], [403, 21, 416, 19, "floor"], [403, 26, 416, 24], [403, 27, 416, 25, "b"], [403, 28, 416, 26], [403, 31, 416, 29, "count"], [403, 36, 416, 34], [403, 37, 416, 35], [405, 12, 418, 10], [406, 12, 419, 10], [406, 17, 419, 15], [406, 21, 419, 19, "dy"], [406, 23, 419, 21], [406, 26, 419, 24], [406, 27, 419, 25], [406, 29, 419, 27, "dy"], [406, 31, 419, 29], [406, 34, 419, 32, "pixelSize"], [406, 43, 419, 41], [406, 47, 419, 45, "py"], [406, 49, 419, 47], [406, 52, 419, 50, "dy"], [406, 54, 419, 52], [406, 57, 419, 55, "clampedHeight"], [406, 70, 419, 68], [406, 72, 419, 70, "dy"], [406, 74, 419, 72], [406, 76, 419, 74], [406, 78, 419, 76], [407, 14, 420, 12], [407, 19, 420, 17], [407, 23, 420, 21, "dx"], [407, 25, 420, 23], [407, 28, 420, 26], [407, 29, 420, 27], [407, 31, 420, 29, "dx"], [407, 33, 420, 31], [407, 36, 420, 34, "pixelSize"], [407, 45, 420, 43], [407, 49, 420, 47, "px"], [407, 51, 420, 49], [407, 54, 420, 52, "dx"], [407, 56, 420, 54], [407, 59, 420, 57, "<PERSON><PERSON><PERSON><PERSON>"], [407, 71, 420, 69], [407, 73, 420, 71, "dx"], [407, 75, 420, 73], [407, 77, 420, 75], [407, 79, 420, 77], [408, 16, 421, 14], [408, 22, 421, 20, "index"], [408, 27, 421, 25], [408, 30, 421, 28], [408, 31, 421, 29], [408, 32, 421, 30, "py"], [408, 34, 421, 32], [408, 37, 421, 35, "dy"], [408, 39, 421, 37], [408, 43, 421, 41, "<PERSON><PERSON><PERSON><PERSON>"], [408, 55, 421, 53], [408, 59, 421, 57, "px"], [408, 61, 421, 59], [408, 64, 421, 62, "dx"], [408, 66, 421, 64], [408, 67, 421, 65], [408, 71, 421, 69], [408, 72, 421, 70], [409, 16, 422, 14, "data"], [409, 20, 422, 18], [409, 21, 422, 19, "index"], [409, 26, 422, 24], [409, 27, 422, 25], [409, 30, 422, 28, "r"], [409, 31, 422, 29], [410, 16, 423, 14, "data"], [410, 20, 423, 18], [410, 21, 423, 19, "index"], [410, 26, 423, 24], [410, 29, 423, 27], [410, 30, 423, 28], [410, 31, 423, 29], [410, 34, 423, 32, "g"], [410, 35, 423, 33], [411, 16, 424, 14, "data"], [411, 20, 424, 18], [411, 21, 424, 19, "index"], [411, 26, 424, 24], [411, 29, 424, 27], [411, 30, 424, 28], [411, 31, 424, 29], [411, 34, 424, 32, "b"], [411, 35, 424, 33], [412, 16, 425, 14], [413, 14, 426, 12], [414, 12, 427, 10], [415, 10, 428, 8], [416, 8, 429, 6], [417, 6, 430, 4], [419, 6, 432, 4], [420, 6, 433, 4], [420, 11, 433, 9], [420, 15, 433, 13, "i"], [420, 16, 433, 14], [420, 19, 433, 17], [420, 20, 433, 18], [420, 22, 433, 20, "i"], [420, 23, 433, 21], [420, 26, 433, 24], [420, 27, 433, 25], [420, 29, 433, 27, "i"], [420, 30, 433, 28], [420, 32, 433, 30], [420, 34, 433, 32], [421, 8, 434, 6, "applySimpleBlur"], [421, 23, 434, 21], [421, 24, 434, 22, "data"], [421, 28, 434, 26], [421, 30, 434, 28, "<PERSON><PERSON><PERSON><PERSON>"], [421, 42, 434, 40], [421, 44, 434, 42, "clampedHeight"], [421, 57, 434, 55], [421, 58, 434, 56], [422, 6, 435, 4], [424, 6, 437, 4], [425, 6, 438, 4, "ctx"], [425, 9, 438, 7], [425, 10, 438, 8, "putImageData"], [425, 22, 438, 20], [425, 23, 438, 21, "imageData"], [425, 32, 438, 30], [425, 34, 438, 32, "clampedX"], [425, 42, 438, 40], [425, 44, 438, 42, "clampedY"], [425, 52, 438, 50], [425, 53, 438, 51], [427, 6, 440, 4], [428, 6, 441, 4, "ctx"], [428, 9, 441, 7], [428, 10, 441, 8, "fillStyle"], [428, 19, 441, 17], [428, 22, 441, 20], [428, 48, 441, 46], [429, 6, 442, 4, "ctx"], [429, 9, 442, 7], [429, 10, 442, 8, "fillRect"], [429, 18, 442, 16], [429, 19, 442, 17, "clampedX"], [429, 27, 442, 25], [429, 29, 442, 27, "clampedY"], [429, 37, 442, 35], [429, 39, 442, 37, "<PERSON><PERSON><PERSON><PERSON>"], [429, 51, 442, 49], [429, 53, 442, 51, "clampedHeight"], [429, 66, 442, 64], [429, 67, 442, 65], [430, 4, 443, 2], [430, 5, 443, 3], [431, 4, 445, 2], [431, 10, 445, 8, "applySimpleBlur"], [431, 25, 445, 23], [431, 28, 445, 26, "applySimpleBlur"], [431, 29, 445, 27, "data"], [431, 33, 445, 50], [431, 35, 445, 52, "width"], [431, 40, 445, 65], [431, 42, 445, 67, "height"], [431, 48, 445, 81], [431, 53, 445, 86], [432, 6, 446, 4], [432, 12, 446, 10, "original"], [432, 20, 446, 18], [432, 23, 446, 21], [432, 27, 446, 25, "Uint8ClampedArray"], [432, 44, 446, 42], [432, 45, 446, 43, "data"], [432, 49, 446, 47], [432, 50, 446, 48], [433, 6, 448, 4], [433, 11, 448, 9], [433, 15, 448, 13, "y"], [433, 16, 448, 14], [433, 19, 448, 17], [433, 20, 448, 18], [433, 22, 448, 20, "y"], [433, 23, 448, 21], [433, 26, 448, 24, "height"], [433, 32, 448, 30], [433, 35, 448, 33], [433, 36, 448, 34], [433, 38, 448, 36, "y"], [433, 39, 448, 37], [433, 41, 448, 39], [433, 43, 448, 41], [434, 8, 449, 6], [434, 13, 449, 11], [434, 17, 449, 15, "x"], [434, 18, 449, 16], [434, 21, 449, 19], [434, 22, 449, 20], [434, 24, 449, 22, "x"], [434, 25, 449, 23], [434, 28, 449, 26, "width"], [434, 33, 449, 31], [434, 36, 449, 34], [434, 37, 449, 35], [434, 39, 449, 37, "x"], [434, 40, 449, 38], [434, 42, 449, 40], [434, 44, 449, 42], [435, 10, 450, 8], [435, 16, 450, 14, "index"], [435, 21, 450, 19], [435, 24, 450, 22], [435, 25, 450, 23, "y"], [435, 26, 450, 24], [435, 29, 450, 27, "width"], [435, 34, 450, 32], [435, 37, 450, 35, "x"], [435, 38, 450, 36], [435, 42, 450, 40], [435, 43, 450, 41], [437, 10, 452, 8], [438, 10, 453, 8], [438, 14, 453, 12, "r"], [438, 15, 453, 13], [438, 18, 453, 16], [438, 19, 453, 17], [439, 12, 453, 19, "g"], [439, 13, 453, 20], [439, 16, 453, 23], [439, 17, 453, 24], [440, 12, 453, 26, "b"], [440, 13, 453, 27], [440, 16, 453, 30], [440, 17, 453, 31], [441, 10, 454, 8], [441, 15, 454, 13], [441, 19, 454, 17, "dy"], [441, 21, 454, 19], [441, 24, 454, 22], [441, 25, 454, 23], [441, 26, 454, 24], [441, 28, 454, 26, "dy"], [441, 30, 454, 28], [441, 34, 454, 32], [441, 35, 454, 33], [441, 37, 454, 35, "dy"], [441, 39, 454, 37], [441, 41, 454, 39], [441, 43, 454, 41], [442, 12, 455, 10], [442, 17, 455, 15], [442, 21, 455, 19, "dx"], [442, 23, 455, 21], [442, 26, 455, 24], [442, 27, 455, 25], [442, 28, 455, 26], [442, 30, 455, 28, "dx"], [442, 32, 455, 30], [442, 36, 455, 34], [442, 37, 455, 35], [442, 39, 455, 37, "dx"], [442, 41, 455, 39], [442, 43, 455, 41], [442, 45, 455, 43], [443, 14, 456, 12], [443, 20, 456, 18, "neighborIndex"], [443, 33, 456, 31], [443, 36, 456, 34], [443, 37, 456, 35], [443, 38, 456, 36, "y"], [443, 39, 456, 37], [443, 42, 456, 40, "dy"], [443, 44, 456, 42], [443, 48, 456, 46, "width"], [443, 53, 456, 51], [443, 57, 456, 55, "x"], [443, 58, 456, 56], [443, 61, 456, 59, "dx"], [443, 63, 456, 61], [443, 64, 456, 62], [443, 68, 456, 66], [443, 69, 456, 67], [444, 14, 457, 12, "r"], [444, 15, 457, 13], [444, 19, 457, 17, "original"], [444, 27, 457, 25], [444, 28, 457, 26, "neighborIndex"], [444, 41, 457, 39], [444, 42, 457, 40], [445, 14, 458, 12, "g"], [445, 15, 458, 13], [445, 19, 458, 17, "original"], [445, 27, 458, 25], [445, 28, 458, 26, "neighborIndex"], [445, 41, 458, 39], [445, 44, 458, 42], [445, 45, 458, 43], [445, 46, 458, 44], [446, 14, 459, 12, "b"], [446, 15, 459, 13], [446, 19, 459, 17, "original"], [446, 27, 459, 25], [446, 28, 459, 26, "neighborIndex"], [446, 41, 459, 39], [446, 44, 459, 42], [446, 45, 459, 43], [446, 46, 459, 44], [447, 12, 460, 10], [448, 10, 461, 8], [449, 10, 463, 8, "data"], [449, 14, 463, 12], [449, 15, 463, 13, "index"], [449, 20, 463, 18], [449, 21, 463, 19], [449, 24, 463, 22, "r"], [449, 25, 463, 23], [449, 28, 463, 26], [449, 29, 463, 27], [450, 10, 464, 8, "data"], [450, 14, 464, 12], [450, 15, 464, 13, "index"], [450, 20, 464, 18], [450, 23, 464, 21], [450, 24, 464, 22], [450, 25, 464, 23], [450, 28, 464, 26, "g"], [450, 29, 464, 27], [450, 32, 464, 30], [450, 33, 464, 31], [451, 10, 465, 8, "data"], [451, 14, 465, 12], [451, 15, 465, 13, "index"], [451, 20, 465, 18], [451, 23, 465, 21], [451, 24, 465, 22], [451, 25, 465, 23], [451, 28, 465, 26, "b"], [451, 29, 465, 27], [451, 32, 465, 30], [451, 33, 465, 31], [452, 8, 466, 6], [453, 6, 467, 4], [454, 4, 468, 2], [454, 5, 468, 3], [455, 4, 470, 2], [455, 10, 470, 8, "applyFallbackFaceBlur"], [455, 31, 470, 29], [455, 34, 470, 32, "applyFallbackFaceBlur"], [455, 35, 470, 33, "ctx"], [455, 38, 470, 62], [455, 40, 470, 64, "imgWidth"], [455, 48, 470, 80], [455, 50, 470, 82, "imgHeight"], [455, 59, 470, 99], [455, 64, 470, 104], [456, 6, 471, 4, "console"], [456, 13, 471, 11], [456, 14, 471, 12, "log"], [456, 17, 471, 15], [456, 18, 471, 16], [456, 90, 471, 88], [456, 91, 471, 89], [458, 6, 473, 4], [459, 6, 474, 4], [459, 12, 474, 10, "areas"], [459, 17, 474, 15], [459, 20, 474, 18], [460, 6, 475, 6], [461, 6, 476, 6], [462, 8, 476, 8, "x"], [462, 9, 476, 9], [462, 11, 476, 11, "imgWidth"], [462, 19, 476, 19], [462, 22, 476, 22], [462, 26, 476, 26], [463, 8, 476, 28, "y"], [463, 9, 476, 29], [463, 11, 476, 31, "imgHeight"], [463, 20, 476, 40], [463, 23, 476, 43], [463, 27, 476, 47], [464, 8, 476, 49, "w"], [464, 9, 476, 50], [464, 11, 476, 52, "imgWidth"], [464, 19, 476, 60], [464, 22, 476, 63], [464, 25, 476, 66], [465, 8, 476, 68, "h"], [465, 9, 476, 69], [465, 11, 476, 71, "imgHeight"], [465, 20, 476, 80], [465, 23, 476, 83], [466, 6, 476, 87], [466, 7, 476, 88], [467, 6, 477, 6], [468, 6, 478, 6], [469, 8, 478, 8, "x"], [469, 9, 478, 9], [469, 11, 478, 11, "imgWidth"], [469, 19, 478, 19], [469, 22, 478, 22], [469, 25, 478, 25], [470, 8, 478, 27, "y"], [470, 9, 478, 28], [470, 11, 478, 30, "imgHeight"], [470, 20, 478, 39], [470, 23, 478, 42], [470, 26, 478, 45], [471, 8, 478, 47, "w"], [471, 9, 478, 48], [471, 11, 478, 50, "imgWidth"], [471, 19, 478, 58], [471, 22, 478, 61], [471, 26, 478, 65], [472, 8, 478, 67, "h"], [472, 9, 478, 68], [472, 11, 478, 70, "imgHeight"], [472, 20, 478, 79], [472, 23, 478, 82], [473, 6, 478, 86], [473, 7, 478, 87], [474, 6, 479, 6], [475, 6, 480, 6], [476, 8, 480, 8, "x"], [476, 9, 480, 9], [476, 11, 480, 11, "imgWidth"], [476, 19, 480, 19], [476, 22, 480, 22], [476, 26, 480, 26], [477, 8, 480, 28, "y"], [477, 9, 480, 29], [477, 11, 480, 31, "imgHeight"], [477, 20, 480, 40], [477, 23, 480, 43], [477, 26, 480, 46], [478, 8, 480, 48, "w"], [478, 9, 480, 49], [478, 11, 480, 51, "imgWidth"], [478, 19, 480, 59], [478, 22, 480, 62], [478, 26, 480, 66], [479, 8, 480, 68, "h"], [479, 9, 480, 69], [479, 11, 480, 71, "imgHeight"], [479, 20, 480, 80], [479, 23, 480, 83], [480, 6, 480, 87], [480, 7, 480, 88], [480, 8, 481, 5], [481, 6, 483, 4, "areas"], [481, 11, 483, 9], [481, 12, 483, 10, "for<PERSON>ach"], [481, 19, 483, 17], [481, 20, 483, 18], [481, 21, 483, 19, "area"], [481, 25, 483, 23], [481, 27, 483, 25, "index"], [481, 32, 483, 30], [481, 37, 483, 35], [482, 8, 484, 6, "console"], [482, 15, 484, 13], [482, 16, 484, 14, "log"], [482, 19, 484, 17], [482, 20, 484, 18], [482, 65, 484, 63, "index"], [482, 70, 484, 68], [482, 73, 484, 71], [482, 74, 484, 72], [482, 77, 484, 75], [482, 79, 484, 77, "area"], [482, 83, 484, 81], [482, 84, 484, 82], [483, 8, 485, 6, "applyStrongBlur"], [483, 23, 485, 21], [483, 24, 485, 22, "ctx"], [483, 27, 485, 25], [483, 29, 485, 27, "area"], [483, 33, 485, 31], [483, 34, 485, 32, "x"], [483, 35, 485, 33], [483, 37, 485, 35, "area"], [483, 41, 485, 39], [483, 42, 485, 40, "y"], [483, 43, 485, 41], [483, 45, 485, 43, "area"], [483, 49, 485, 47], [483, 50, 485, 48, "w"], [483, 51, 485, 49], [483, 53, 485, 51, "area"], [483, 57, 485, 55], [483, 58, 485, 56, "h"], [483, 59, 485, 57], [483, 60, 485, 58], [484, 6, 486, 4], [484, 7, 486, 5], [484, 8, 486, 6], [485, 4, 487, 2], [485, 5, 487, 3], [487, 4, 489, 2], [488, 4, 490, 2], [488, 10, 490, 8, "capturePhoto"], [488, 22, 490, 20], [488, 25, 490, 23], [488, 29, 490, 23, "useCallback"], [488, 47, 490, 34], [488, 49, 490, 35], [488, 61, 490, 47], [489, 6, 491, 4], [490, 6, 492, 4], [490, 12, 492, 10, "isDev"], [490, 17, 492, 15], [490, 20, 492, 18, "process"], [490, 27, 492, 25], [490, 28, 492, 26, "env"], [490, 31, 492, 29], [490, 32, 492, 30, "NODE_ENV"], [490, 40, 492, 38], [490, 45, 492, 43], [490, 58, 492, 56], [490, 62, 492, 60, "__DEV__"], [490, 69, 492, 67], [491, 6, 494, 4], [491, 10, 494, 8], [491, 11, 494, 9, "cameraRef"], [491, 20, 494, 18], [491, 21, 494, 19, "current"], [491, 28, 494, 26], [491, 32, 494, 30], [491, 33, 494, 31, "isDev"], [491, 38, 494, 36], [491, 40, 494, 38], [492, 8, 495, 6, "<PERSON><PERSON>"], [492, 22, 495, 11], [492, 23, 495, 12, "alert"], [492, 28, 495, 17], [492, 29, 495, 18], [492, 36, 495, 25], [492, 38, 495, 27], [492, 56, 495, 45], [492, 57, 495, 46], [493, 8, 496, 6], [494, 6, 497, 4], [495, 6, 498, 4], [495, 10, 498, 8], [496, 8, 499, 6, "setProcessingState"], [496, 26, 499, 24], [496, 27, 499, 25], [496, 38, 499, 36], [496, 39, 499, 37], [497, 8, 500, 6, "setProcessingProgress"], [497, 29, 500, 27], [497, 30, 500, 28], [497, 32, 500, 30], [497, 33, 500, 31], [498, 8, 501, 6], [499, 8, 502, 6], [500, 8, 503, 6], [501, 8, 504, 6], [501, 14, 504, 12], [501, 18, 504, 16, "Promise"], [501, 25, 504, 23], [501, 26, 504, 24, "resolve"], [501, 33, 504, 31], [501, 37, 504, 35, "setTimeout"], [501, 47, 504, 45], [501, 48, 504, 46, "resolve"], [501, 55, 504, 53], [501, 57, 504, 55], [501, 59, 504, 57], [501, 60, 504, 58], [501, 61, 504, 59], [502, 8, 505, 6], [503, 8, 506, 6], [503, 12, 506, 10, "photo"], [503, 17, 506, 15], [504, 8, 508, 6], [504, 12, 508, 10], [505, 10, 509, 8, "photo"], [505, 15, 509, 13], [505, 18, 509, 16], [505, 24, 509, 22, "cameraRef"], [505, 33, 509, 31], [505, 34, 509, 32, "current"], [505, 41, 509, 39], [505, 42, 509, 40, "takePictureAsync"], [505, 58, 509, 56], [505, 59, 509, 57], [506, 12, 510, 10, "quality"], [506, 19, 510, 17], [506, 21, 510, 19], [506, 24, 510, 22], [507, 12, 511, 10, "base64"], [507, 18, 511, 16], [507, 20, 511, 18], [507, 25, 511, 23], [508, 12, 512, 10, "skipProcessing"], [508, 26, 512, 24], [508, 28, 512, 26], [508, 32, 512, 30], [508, 33, 512, 32], [509, 10, 513, 8], [509, 11, 513, 9], [509, 12, 513, 10], [510, 8, 514, 6], [510, 9, 514, 7], [510, 10, 514, 8], [510, 17, 514, 15, "cameraError"], [510, 28, 514, 26], [510, 30, 514, 28], [511, 10, 515, 8, "console"], [511, 17, 515, 15], [511, 18, 515, 16, "log"], [511, 21, 515, 19], [511, 22, 515, 20], [511, 82, 515, 80], [511, 84, 515, 82, "cameraError"], [511, 95, 515, 93], [511, 96, 515, 94], [512, 10, 516, 8], [513, 10, 517, 8], [513, 14, 517, 12, "isDev"], [513, 19, 517, 17], [513, 21, 517, 19], [514, 12, 518, 10, "photo"], [514, 17, 518, 15], [514, 20, 518, 18], [515, 14, 519, 12, "uri"], [515, 17, 519, 15], [515, 19, 519, 17], [516, 12, 520, 10], [516, 13, 520, 11], [517, 10, 521, 8], [517, 11, 521, 9], [517, 17, 521, 15], [518, 12, 522, 10], [518, 18, 522, 16, "cameraError"], [518, 29, 522, 27], [519, 10, 523, 8], [520, 8, 524, 6], [521, 8, 525, 6], [521, 12, 525, 10], [521, 13, 525, 11, "photo"], [521, 18, 525, 16], [521, 20, 525, 18], [522, 10, 526, 8], [522, 16, 526, 14], [522, 20, 526, 18, "Error"], [522, 25, 526, 23], [522, 26, 526, 24], [522, 51, 526, 49], [522, 52, 526, 50], [523, 8, 527, 6], [524, 8, 528, 6, "console"], [524, 15, 528, 13], [524, 16, 528, 14, "log"], [524, 19, 528, 17], [524, 20, 528, 18], [524, 56, 528, 54], [524, 58, 528, 56, "photo"], [524, 63, 528, 61], [524, 64, 528, 62, "uri"], [524, 67, 528, 65], [524, 68, 528, 66], [525, 8, 529, 6, "setCapturedPhoto"], [525, 24, 529, 22], [525, 25, 529, 23, "photo"], [525, 30, 529, 28], [525, 31, 529, 29, "uri"], [525, 34, 529, 32], [525, 35, 529, 33], [526, 8, 530, 6, "setProcessingProgress"], [526, 29, 530, 27], [526, 30, 530, 28], [526, 32, 530, 30], [526, 33, 530, 31], [527, 8, 531, 6], [528, 8, 532, 6, "console"], [528, 15, 532, 13], [528, 16, 532, 14, "log"], [528, 19, 532, 17], [528, 20, 532, 18], [528, 73, 532, 71], [528, 74, 532, 72], [529, 8, 533, 6], [529, 14, 533, 12, "processImageWithFaceBlur"], [529, 38, 533, 36], [529, 39, 533, 37, "photo"], [529, 44, 533, 42], [529, 45, 533, 43, "uri"], [529, 48, 533, 46], [529, 49, 533, 47], [530, 8, 534, 6, "console"], [530, 15, 534, 13], [530, 16, 534, 14, "log"], [530, 19, 534, 17], [530, 20, 534, 18], [530, 71, 534, 69], [530, 72, 534, 70], [531, 6, 535, 4], [531, 7, 535, 5], [531, 8, 535, 6], [531, 15, 535, 13, "error"], [531, 20, 535, 18], [531, 22, 535, 20], [532, 8, 536, 6, "console"], [532, 15, 536, 13], [532, 16, 536, 14, "error"], [532, 21, 536, 19], [532, 22, 536, 20], [532, 54, 536, 52], [532, 56, 536, 54, "error"], [532, 61, 536, 59], [532, 62, 536, 60], [533, 8, 537, 6, "setErrorMessage"], [533, 23, 537, 21], [533, 24, 537, 22], [533, 68, 537, 66], [533, 69, 537, 67], [534, 8, 538, 6, "setProcessingState"], [534, 26, 538, 24], [534, 27, 538, 25], [534, 34, 538, 32], [534, 35, 538, 33], [535, 6, 539, 4], [536, 4, 540, 2], [536, 5, 540, 3], [536, 7, 540, 5], [536, 9, 540, 7], [536, 10, 540, 8], [537, 4, 541, 2], [538, 4, 542, 2], [538, 10, 542, 8, "processImageWithFaceBlur"], [538, 34, 542, 32], [538, 37, 542, 35], [538, 43, 542, 42, "photoUri"], [538, 51, 542, 58], [538, 55, 542, 63], [539, 6, 543, 4], [539, 10, 543, 8], [540, 8, 544, 6, "console"], [540, 15, 544, 13], [540, 16, 544, 14, "log"], [540, 19, 544, 17], [540, 20, 544, 18], [540, 84, 544, 82], [540, 85, 544, 83], [541, 8, 545, 6, "setProcessingState"], [541, 26, 545, 24], [541, 27, 545, 25], [541, 39, 545, 37], [541, 40, 545, 38], [542, 8, 546, 6, "setProcessingProgress"], [542, 29, 546, 27], [542, 30, 546, 28], [542, 32, 546, 30], [542, 33, 546, 31], [544, 8, 548, 6], [545, 8, 549, 6], [545, 14, 549, 12, "canvas"], [545, 20, 549, 18], [545, 23, 549, 21, "document"], [545, 31, 549, 29], [545, 32, 549, 30, "createElement"], [545, 45, 549, 43], [545, 46, 549, 44], [545, 54, 549, 52], [545, 55, 549, 53], [546, 8, 550, 6], [546, 14, 550, 12, "ctx"], [546, 17, 550, 15], [546, 20, 550, 18, "canvas"], [546, 26, 550, 24], [546, 27, 550, 25, "getContext"], [546, 37, 550, 35], [546, 38, 550, 36], [546, 42, 550, 40], [546, 43, 550, 41], [547, 8, 551, 6], [547, 12, 551, 10], [547, 13, 551, 11, "ctx"], [547, 16, 551, 14], [547, 18, 551, 16], [547, 24, 551, 22], [547, 28, 551, 26, "Error"], [547, 33, 551, 31], [547, 34, 551, 32], [547, 64, 551, 62], [547, 65, 551, 63], [549, 8, 553, 6], [550, 8, 554, 6], [550, 14, 554, 12, "img"], [550, 17, 554, 15], [550, 20, 554, 18], [550, 24, 554, 22, "Image"], [550, 29, 554, 27], [550, 30, 554, 28], [550, 31, 554, 29], [551, 8, 555, 6], [551, 14, 555, 12], [551, 18, 555, 16, "Promise"], [551, 25, 555, 23], [551, 26, 555, 24], [551, 27, 555, 25, "resolve"], [551, 34, 555, 32], [551, 36, 555, 34, "reject"], [551, 42, 555, 40], [551, 47, 555, 45], [552, 10, 556, 8, "img"], [552, 13, 556, 11], [552, 14, 556, 12, "onload"], [552, 20, 556, 18], [552, 23, 556, 21, "resolve"], [552, 30, 556, 28], [553, 10, 557, 8, "img"], [553, 13, 557, 11], [553, 14, 557, 12, "onerror"], [553, 21, 557, 19], [553, 24, 557, 22, "reject"], [553, 30, 557, 28], [554, 10, 558, 8, "img"], [554, 13, 558, 11], [554, 14, 558, 12, "src"], [554, 17, 558, 15], [554, 20, 558, 18, "photoUri"], [554, 28, 558, 26], [555, 8, 559, 6], [555, 9, 559, 7], [555, 10, 559, 8], [557, 8, 561, 6], [558, 8, 562, 6, "canvas"], [558, 14, 562, 12], [558, 15, 562, 13, "width"], [558, 20, 562, 18], [558, 23, 562, 21, "img"], [558, 26, 562, 24], [558, 27, 562, 25, "width"], [558, 32, 562, 30], [559, 8, 563, 6, "canvas"], [559, 14, 563, 12], [559, 15, 563, 13, "height"], [559, 21, 563, 19], [559, 24, 563, 22, "img"], [559, 27, 563, 25], [559, 28, 563, 26, "height"], [559, 34, 563, 32], [560, 8, 564, 6, "console"], [560, 15, 564, 13], [560, 16, 564, 14, "log"], [560, 19, 564, 17], [560, 20, 564, 18], [560, 54, 564, 52], [560, 56, 564, 54], [561, 10, 564, 56, "width"], [561, 15, 564, 61], [561, 17, 564, 63, "img"], [561, 20, 564, 66], [561, 21, 564, 67, "width"], [561, 26, 564, 72], [562, 10, 564, 74, "height"], [562, 16, 564, 80], [562, 18, 564, 82, "img"], [562, 21, 564, 85], [562, 22, 564, 86, "height"], [563, 8, 564, 93], [563, 9, 564, 94], [563, 10, 564, 95], [565, 8, 566, 6], [566, 8, 567, 6, "ctx"], [566, 11, 567, 9], [566, 12, 567, 10, "drawImage"], [566, 21, 567, 19], [566, 22, 567, 20, "img"], [566, 25, 567, 23], [566, 27, 567, 25], [566, 28, 567, 26], [566, 30, 567, 28], [566, 31, 567, 29], [566, 32, 567, 30], [567, 8, 568, 6, "console"], [567, 15, 568, 13], [567, 16, 568, 14, "log"], [567, 19, 568, 17], [567, 20, 568, 18], [567, 72, 568, 70], [567, 73, 568, 71], [568, 8, 570, 6, "setProcessingProgress"], [568, 29, 570, 27], [568, 30, 570, 28], [568, 32, 570, 30], [568, 33, 570, 31], [570, 8, 572, 6], [571, 8, 573, 6], [571, 12, 573, 10, "detectedFaces"], [571, 25, 573, 23], [571, 28, 573, 26], [571, 30, 573, 28], [572, 8, 575, 6, "console"], [572, 15, 575, 13], [572, 16, 575, 14, "log"], [572, 19, 575, 17], [572, 20, 575, 18], [572, 81, 575, 79], [572, 82, 575, 80], [574, 8, 577, 6], [575, 8, 578, 6], [575, 12, 578, 10], [576, 10, 579, 8, "console"], [576, 17, 579, 15], [576, 18, 579, 16, "log"], [576, 21, 579, 19], [576, 22, 579, 20], [576, 81, 579, 79], [576, 82, 579, 80], [577, 10, 580, 8], [577, 16, 580, 14, "loadTensorFlowFaceDetection"], [577, 43, 580, 41], [577, 44, 580, 42], [577, 45, 580, 43], [578, 10, 581, 8, "console"], [578, 17, 581, 15], [578, 18, 581, 16, "log"], [578, 21, 581, 19], [578, 22, 581, 20], [578, 90, 581, 88], [578, 91, 581, 89], [579, 10, 582, 8, "detectedFaces"], [579, 23, 582, 21], [579, 26, 582, 24], [579, 32, 582, 30, "detectFacesWithTensorFlow"], [579, 57, 582, 55], [579, 58, 582, 56, "img"], [579, 61, 582, 59], [579, 62, 582, 60], [580, 10, 583, 8, "console"], [580, 17, 583, 15], [580, 18, 583, 16, "log"], [580, 21, 583, 19], [580, 22, 583, 20], [580, 70, 583, 68, "detectedFaces"], [580, 83, 583, 81], [580, 84, 583, 82, "length"], [580, 90, 583, 88], [580, 98, 583, 96], [580, 99, 583, 97], [581, 8, 584, 6], [581, 9, 584, 7], [581, 10, 584, 8], [581, 17, 584, 15, "tensorFlowError"], [581, 32, 584, 30], [581, 34, 584, 32], [582, 10, 585, 8, "console"], [582, 17, 585, 15], [582, 18, 585, 16, "warn"], [582, 22, 585, 20], [582, 23, 585, 21], [582, 61, 585, 59], [582, 63, 585, 61, "tensorFlowError"], [582, 78, 585, 76], [582, 79, 585, 77], [583, 10, 586, 8, "console"], [583, 17, 586, 15], [583, 18, 586, 16, "warn"], [583, 22, 586, 20], [583, 23, 586, 21], [583, 68, 586, 66], [583, 70, 586, 68], [584, 12, 587, 10, "message"], [584, 19, 587, 17], [584, 21, 587, 19, "tensorFlowError"], [584, 36, 587, 34], [584, 37, 587, 35, "message"], [584, 44, 587, 42], [585, 12, 588, 10, "stack"], [585, 17, 588, 15], [585, 19, 588, 17, "tensorFlowError"], [585, 34, 588, 32], [585, 35, 588, 33, "stack"], [586, 10, 589, 8], [586, 11, 589, 9], [586, 12, 589, 10], [588, 10, 591, 8], [589, 10, 592, 8, "console"], [589, 17, 592, 15], [589, 18, 592, 16, "log"], [589, 21, 592, 19], [589, 22, 592, 20], [589, 86, 592, 84], [589, 87, 592, 85], [590, 10, 593, 8, "detectedFaces"], [590, 23, 593, 21], [590, 26, 593, 24, "detectFacesHeuristic"], [590, 46, 593, 44], [590, 47, 593, 45, "img"], [590, 50, 593, 48], [590, 52, 593, 50, "ctx"], [590, 55, 593, 53], [590, 56, 593, 54], [591, 10, 594, 8, "console"], [591, 17, 594, 15], [591, 18, 594, 16, "log"], [591, 21, 594, 19], [591, 22, 594, 20], [591, 70, 594, 68, "detectedFaces"], [591, 83, 594, 81], [591, 84, 594, 82, "length"], [591, 90, 594, 88], [591, 98, 594, 96], [591, 99, 594, 97], [592, 8, 595, 6], [594, 8, 597, 6], [595, 8, 598, 6], [595, 12, 598, 10, "detectedFaces"], [595, 25, 598, 23], [595, 26, 598, 24, "length"], [595, 32, 598, 30], [595, 37, 598, 35], [595, 38, 598, 36], [595, 40, 598, 38], [596, 10, 599, 8, "console"], [596, 17, 599, 15], [596, 18, 599, 16, "log"], [596, 21, 599, 19], [596, 22, 599, 20], [596, 89, 599, 87], [596, 90, 599, 88], [597, 10, 600, 8, "detectedFaces"], [597, 23, 600, 21], [597, 26, 600, 24, "detectFacesAggressive"], [597, 47, 600, 45], [597, 48, 600, 46, "img"], [597, 51, 600, 49], [597, 53, 600, 51, "ctx"], [597, 56, 600, 54], [597, 57, 600, 55], [598, 10, 601, 8, "console"], [598, 17, 601, 15], [598, 18, 601, 16, "log"], [598, 21, 601, 19], [598, 22, 601, 20], [598, 71, 601, 69, "detectedFaces"], [598, 84, 601, 82], [598, 85, 601, 83, "length"], [598, 91, 601, 89], [598, 99, 601, 97], [598, 100, 601, 98], [599, 8, 602, 6], [600, 8, 604, 6, "console"], [600, 15, 604, 13], [600, 16, 604, 14, "log"], [600, 19, 604, 17], [600, 20, 604, 18], [600, 72, 604, 70, "detectedFaces"], [600, 85, 604, 83], [600, 86, 604, 84, "length"], [600, 92, 604, 90], [600, 100, 604, 98], [600, 101, 604, 99], [601, 8, 605, 6], [601, 12, 605, 10, "detectedFaces"], [601, 25, 605, 23], [601, 26, 605, 24, "length"], [601, 32, 605, 30], [601, 35, 605, 33], [601, 36, 605, 34], [601, 38, 605, 36], [602, 10, 606, 8, "console"], [602, 17, 606, 15], [602, 18, 606, 16, "log"], [602, 21, 606, 19], [602, 22, 606, 20], [602, 66, 606, 64], [602, 68, 606, 66, "detectedFaces"], [602, 81, 606, 79], [602, 82, 606, 80, "map"], [602, 85, 606, 83], [602, 86, 606, 84], [602, 87, 606, 85, "face"], [602, 91, 606, 89], [602, 93, 606, 91, "i"], [602, 94, 606, 92], [602, 100, 606, 98], [603, 12, 607, 10, "faceNumber"], [603, 22, 607, 20], [603, 24, 607, 22, "i"], [603, 25, 607, 23], [603, 28, 607, 26], [603, 29, 607, 27], [604, 12, 608, 10, "centerX"], [604, 19, 608, 17], [604, 21, 608, 19, "face"], [604, 25, 608, 23], [604, 26, 608, 24, "boundingBox"], [604, 37, 608, 35], [604, 38, 608, 36, "xCenter"], [604, 45, 608, 43], [605, 12, 609, 10, "centerY"], [605, 19, 609, 17], [605, 21, 609, 19, "face"], [605, 25, 609, 23], [605, 26, 609, 24, "boundingBox"], [605, 37, 609, 35], [605, 38, 609, 36, "yCenter"], [605, 45, 609, 43], [606, 12, 610, 10, "width"], [606, 17, 610, 15], [606, 19, 610, 17, "face"], [606, 23, 610, 21], [606, 24, 610, 22, "boundingBox"], [606, 35, 610, 33], [606, 36, 610, 34, "width"], [606, 41, 610, 39], [607, 12, 611, 10, "height"], [607, 18, 611, 16], [607, 20, 611, 18, "face"], [607, 24, 611, 22], [607, 25, 611, 23, "boundingBox"], [607, 36, 611, 34], [607, 37, 611, 35, "height"], [608, 10, 612, 8], [608, 11, 612, 9], [608, 12, 612, 10], [608, 13, 612, 11], [608, 14, 612, 12], [609, 8, 613, 6], [609, 9, 613, 7], [609, 15, 613, 13], [610, 10, 614, 8, "console"], [610, 17, 614, 15], [610, 18, 614, 16, "log"], [610, 21, 614, 19], [610, 22, 614, 20], [610, 74, 614, 72], [610, 75, 614, 73], [611, 10, 615, 8, "console"], [611, 17, 615, 15], [611, 18, 615, 16, "log"], [611, 21, 615, 19], [611, 22, 615, 20], [611, 95, 615, 93], [611, 96, 615, 94], [613, 10, 617, 8], [614, 10, 618, 8], [614, 16, 618, 14, "centerX"], [614, 23, 618, 21], [614, 26, 618, 24, "img"], [614, 29, 618, 27], [614, 30, 618, 28, "width"], [614, 35, 618, 33], [614, 38, 618, 36], [614, 41, 618, 39], [615, 10, 619, 8], [615, 16, 619, 14, "centerY"], [615, 23, 619, 21], [615, 26, 619, 24, "img"], [615, 29, 619, 27], [615, 30, 619, 28, "height"], [615, 36, 619, 34], [615, 39, 619, 37], [615, 42, 619, 40], [616, 10, 620, 8], [616, 16, 620, 14, "centerWidth"], [616, 27, 620, 25], [616, 30, 620, 28, "img"], [616, 33, 620, 31], [616, 34, 620, 32, "width"], [616, 39, 620, 37], [616, 42, 620, 40], [616, 45, 620, 43], [617, 10, 621, 8], [617, 16, 621, 14, "centerHeight"], [617, 28, 621, 26], [617, 31, 621, 29, "img"], [617, 34, 621, 32], [617, 35, 621, 33, "height"], [617, 41, 621, 39], [617, 44, 621, 42], [617, 47, 621, 45], [618, 10, 623, 8, "detectedFaces"], [618, 23, 623, 21], [618, 26, 623, 24], [618, 27, 623, 25], [619, 12, 624, 10, "boundingBox"], [619, 23, 624, 21], [619, 25, 624, 23], [620, 14, 625, 12, "xCenter"], [620, 21, 625, 19], [620, 23, 625, 21], [620, 26, 625, 24], [621, 14, 626, 12, "yCenter"], [621, 21, 626, 19], [621, 23, 626, 21], [621, 26, 626, 24], [622, 14, 627, 12, "width"], [622, 19, 627, 17], [622, 21, 627, 19], [622, 24, 627, 22], [623, 14, 628, 12, "height"], [623, 20, 628, 18], [623, 22, 628, 20], [624, 12, 629, 10], [624, 13, 629, 11], [625, 12, 630, 10, "confidence"], [625, 22, 630, 20], [625, 24, 630, 22], [626, 10, 631, 8], [626, 11, 631, 9], [626, 12, 631, 10], [627, 10, 633, 8, "console"], [627, 17, 633, 15], [627, 18, 633, 16, "log"], [627, 21, 633, 19], [627, 22, 633, 20], [627, 100, 633, 98], [627, 101, 633, 99], [628, 8, 634, 6], [629, 8, 636, 6, "setProcessingProgress"], [629, 29, 636, 27], [629, 30, 636, 28], [629, 32, 636, 30], [629, 33, 636, 31], [631, 8, 638, 6], [632, 8, 639, 6], [632, 12, 639, 10, "detectedFaces"], [632, 25, 639, 23], [632, 26, 639, 24, "length"], [632, 32, 639, 30], [632, 35, 639, 33], [632, 36, 639, 34], [632, 38, 639, 36], [633, 10, 640, 8, "console"], [633, 17, 640, 15], [633, 18, 640, 16, "log"], [633, 21, 640, 19], [633, 22, 640, 20], [633, 61, 640, 59, "detectedFaces"], [633, 74, 640, 72], [633, 75, 640, 73, "length"], [633, 81, 640, 79], [633, 101, 640, 99], [633, 102, 640, 100], [634, 10, 642, 8, "detectedFaces"], [634, 23, 642, 21], [634, 24, 642, 22, "for<PERSON>ach"], [634, 31, 642, 29], [634, 32, 642, 30], [634, 33, 642, 31, "detection"], [634, 42, 642, 40], [634, 44, 642, 42, "index"], [634, 49, 642, 47], [634, 54, 642, 52], [635, 12, 643, 10], [635, 18, 643, 16, "bbox"], [635, 22, 643, 20], [635, 25, 643, 23, "detection"], [635, 34, 643, 32], [635, 35, 643, 33, "boundingBox"], [635, 46, 643, 44], [636, 12, 645, 10, "console"], [636, 19, 645, 17], [636, 20, 645, 18, "log"], [636, 23, 645, 21], [636, 24, 645, 22], [636, 85, 645, 83, "index"], [636, 90, 645, 88], [636, 93, 645, 91], [636, 94, 645, 92], [636, 97, 645, 95], [636, 99, 645, 97], [637, 14, 646, 12, "bbox"], [637, 18, 646, 16], [638, 14, 647, 12, "imageSize"], [638, 23, 647, 21], [638, 25, 647, 23], [639, 16, 647, 25, "width"], [639, 21, 647, 30], [639, 23, 647, 32, "img"], [639, 26, 647, 35], [639, 27, 647, 36, "width"], [639, 32, 647, 41], [640, 16, 647, 43, "height"], [640, 22, 647, 49], [640, 24, 647, 51, "img"], [640, 27, 647, 54], [640, 28, 647, 55, "height"], [641, 14, 647, 62], [642, 12, 648, 10], [642, 13, 648, 11], [642, 14, 648, 12], [644, 12, 650, 10], [645, 12, 651, 10], [645, 18, 651, 16, "faceX"], [645, 23, 651, 21], [645, 26, 651, 24, "bbox"], [645, 30, 651, 28], [645, 31, 651, 29, "xCenter"], [645, 38, 651, 36], [645, 41, 651, 39, "img"], [645, 44, 651, 42], [645, 45, 651, 43, "width"], [645, 50, 651, 48], [645, 53, 651, 52, "bbox"], [645, 57, 651, 56], [645, 58, 651, 57, "width"], [645, 63, 651, 62], [645, 66, 651, 65, "img"], [645, 69, 651, 68], [645, 70, 651, 69, "width"], [645, 75, 651, 74], [645, 78, 651, 78], [645, 79, 651, 79], [646, 12, 652, 10], [646, 18, 652, 16, "faceY"], [646, 23, 652, 21], [646, 26, 652, 24, "bbox"], [646, 30, 652, 28], [646, 31, 652, 29, "yCenter"], [646, 38, 652, 36], [646, 41, 652, 39, "img"], [646, 44, 652, 42], [646, 45, 652, 43, "height"], [646, 51, 652, 49], [646, 54, 652, 53, "bbox"], [646, 58, 652, 57], [646, 59, 652, 58, "height"], [646, 65, 652, 64], [646, 68, 652, 67, "img"], [646, 71, 652, 70], [646, 72, 652, 71, "height"], [646, 78, 652, 77], [646, 81, 652, 81], [646, 82, 652, 82], [647, 12, 653, 10], [647, 18, 653, 16, "faceWidth"], [647, 27, 653, 25], [647, 30, 653, 28, "bbox"], [647, 34, 653, 32], [647, 35, 653, 33, "width"], [647, 40, 653, 38], [647, 43, 653, 41, "img"], [647, 46, 653, 44], [647, 47, 653, 45, "width"], [647, 52, 653, 50], [648, 12, 654, 10], [648, 18, 654, 16, "faceHeight"], [648, 28, 654, 26], [648, 31, 654, 29, "bbox"], [648, 35, 654, 33], [648, 36, 654, 34, "height"], [648, 42, 654, 40], [648, 45, 654, 43, "img"], [648, 48, 654, 46], [648, 49, 654, 47, "height"], [648, 55, 654, 53], [649, 12, 656, 10, "console"], [649, 19, 656, 17], [649, 20, 656, 18, "log"], [649, 23, 656, 21], [649, 24, 656, 22], [649, 84, 656, 82], [649, 86, 656, 84], [650, 14, 657, 12, "faceX"], [650, 19, 657, 17], [651, 14, 657, 19, "faceY"], [651, 19, 657, 24], [652, 14, 657, 26, "faceWidth"], [652, 23, 657, 35], [653, 14, 657, 37, "faceHeight"], [653, 24, 657, 47], [654, 14, 658, 12, "<PERSON><PERSON><PERSON><PERSON>"], [654, 21, 658, 19], [654, 23, 658, 21, "faceX"], [654, 28, 658, 26], [654, 32, 658, 30], [654, 33, 658, 31], [654, 37, 658, 35, "faceY"], [654, 42, 658, 40], [654, 46, 658, 44], [654, 47, 658, 45], [654, 51, 658, 49, "faceWidth"], [654, 60, 658, 58], [654, 63, 658, 61], [654, 64, 658, 62], [654, 68, 658, 66, "faceHeight"], [654, 78, 658, 76], [654, 81, 658, 79], [655, 12, 659, 10], [655, 13, 659, 11], [655, 14, 659, 12], [657, 12, 661, 10], [658, 12, 662, 10], [658, 18, 662, 16, "padding"], [658, 25, 662, 23], [658, 28, 662, 26], [658, 31, 662, 29], [659, 12, 663, 10], [659, 18, 663, 16, "paddedX"], [659, 25, 663, 23], [659, 28, 663, 26, "Math"], [659, 32, 663, 30], [659, 33, 663, 31, "max"], [659, 36, 663, 34], [659, 37, 663, 35], [659, 38, 663, 36], [659, 40, 663, 38, "faceX"], [659, 45, 663, 43], [659, 48, 663, 46, "faceWidth"], [659, 57, 663, 55], [659, 60, 663, 58, "padding"], [659, 67, 663, 65], [659, 68, 663, 66], [660, 12, 664, 10], [660, 18, 664, 16, "paddedY"], [660, 25, 664, 23], [660, 28, 664, 26, "Math"], [660, 32, 664, 30], [660, 33, 664, 31, "max"], [660, 36, 664, 34], [660, 37, 664, 35], [660, 38, 664, 36], [660, 40, 664, 38, "faceY"], [660, 45, 664, 43], [660, 48, 664, 46, "faceHeight"], [660, 58, 664, 56], [660, 61, 664, 59, "padding"], [660, 68, 664, 66], [660, 69, 664, 67], [661, 12, 665, 10], [661, 18, 665, 16, "<PERSON><PERSON><PERSON><PERSON>"], [661, 29, 665, 27], [661, 32, 665, 30, "Math"], [661, 36, 665, 34], [661, 37, 665, 35, "min"], [661, 40, 665, 38], [661, 41, 665, 39, "img"], [661, 44, 665, 42], [661, 45, 665, 43, "width"], [661, 50, 665, 48], [661, 53, 665, 51, "paddedX"], [661, 60, 665, 58], [661, 62, 665, 60, "faceWidth"], [661, 71, 665, 69], [661, 75, 665, 73], [661, 76, 665, 74], [661, 79, 665, 77], [661, 80, 665, 78], [661, 83, 665, 81, "padding"], [661, 90, 665, 88], [661, 91, 665, 89], [661, 92, 665, 90], [662, 12, 666, 10], [662, 18, 666, 16, "paddedHeight"], [662, 30, 666, 28], [662, 33, 666, 31, "Math"], [662, 37, 666, 35], [662, 38, 666, 36, "min"], [662, 41, 666, 39], [662, 42, 666, 40, "img"], [662, 45, 666, 43], [662, 46, 666, 44, "height"], [662, 52, 666, 50], [662, 55, 666, 53, "paddedY"], [662, 62, 666, 60], [662, 64, 666, 62, "faceHeight"], [662, 74, 666, 72], [662, 78, 666, 76], [662, 79, 666, 77], [662, 82, 666, 80], [662, 83, 666, 81], [662, 86, 666, 84, "padding"], [662, 93, 666, 91], [662, 94, 666, 92], [662, 95, 666, 93], [663, 12, 668, 10, "console"], [663, 19, 668, 17], [663, 20, 668, 18, "log"], [663, 23, 668, 21], [663, 24, 668, 22], [663, 60, 668, 58, "index"], [663, 65, 668, 63], [663, 68, 668, 66], [663, 69, 668, 67], [663, 72, 668, 70], [663, 74, 668, 72], [664, 14, 669, 12, "original"], [664, 22, 669, 20], [664, 24, 669, 22], [665, 16, 669, 24, "x"], [665, 17, 669, 25], [665, 19, 669, 27, "Math"], [665, 23, 669, 31], [665, 24, 669, 32, "round"], [665, 29, 669, 37], [665, 30, 669, 38, "faceX"], [665, 35, 669, 43], [665, 36, 669, 44], [666, 16, 669, 46, "y"], [666, 17, 669, 47], [666, 19, 669, 49, "Math"], [666, 23, 669, 53], [666, 24, 669, 54, "round"], [666, 29, 669, 59], [666, 30, 669, 60, "faceY"], [666, 35, 669, 65], [666, 36, 669, 66], [667, 16, 669, 68, "w"], [667, 17, 669, 69], [667, 19, 669, 71, "Math"], [667, 23, 669, 75], [667, 24, 669, 76, "round"], [667, 29, 669, 81], [667, 30, 669, 82, "faceWidth"], [667, 39, 669, 91], [667, 40, 669, 92], [668, 16, 669, 94, "h"], [668, 17, 669, 95], [668, 19, 669, 97, "Math"], [668, 23, 669, 101], [668, 24, 669, 102, "round"], [668, 29, 669, 107], [668, 30, 669, 108, "faceHeight"], [668, 40, 669, 118], [669, 14, 669, 120], [669, 15, 669, 121], [670, 14, 670, 12, "padded"], [670, 20, 670, 18], [670, 22, 670, 20], [671, 16, 670, 22, "x"], [671, 17, 670, 23], [671, 19, 670, 25, "Math"], [671, 23, 670, 29], [671, 24, 670, 30, "round"], [671, 29, 670, 35], [671, 30, 670, 36, "paddedX"], [671, 37, 670, 43], [671, 38, 670, 44], [672, 16, 670, 46, "y"], [672, 17, 670, 47], [672, 19, 670, 49, "Math"], [672, 23, 670, 53], [672, 24, 670, 54, "round"], [672, 29, 670, 59], [672, 30, 670, 60, "paddedY"], [672, 37, 670, 67], [672, 38, 670, 68], [673, 16, 670, 70, "w"], [673, 17, 670, 71], [673, 19, 670, 73, "Math"], [673, 23, 670, 77], [673, 24, 670, 78, "round"], [673, 29, 670, 83], [673, 30, 670, 84, "<PERSON><PERSON><PERSON><PERSON>"], [673, 41, 670, 95], [673, 42, 670, 96], [674, 16, 670, 98, "h"], [674, 17, 670, 99], [674, 19, 670, 101, "Math"], [674, 23, 670, 105], [674, 24, 670, 106, "round"], [674, 29, 670, 111], [674, 30, 670, 112, "paddedHeight"], [674, 42, 670, 124], [675, 14, 670, 126], [676, 12, 671, 10], [676, 13, 671, 11], [676, 14, 671, 12], [678, 12, 673, 10], [679, 12, 674, 10, "console"], [679, 19, 674, 17], [679, 20, 674, 18, "log"], [679, 23, 674, 21], [679, 24, 674, 22], [679, 70, 674, 68], [679, 72, 674, 70], [680, 14, 675, 12, "width"], [680, 19, 675, 17], [680, 21, 675, 19, "canvas"], [680, 27, 675, 25], [680, 28, 675, 26, "width"], [680, 33, 675, 31], [681, 14, 676, 12, "height"], [681, 20, 676, 18], [681, 22, 676, 20, "canvas"], [681, 28, 676, 26], [681, 29, 676, 27, "height"], [681, 35, 676, 33], [682, 14, 677, 12, "contextValid"], [682, 26, 677, 24], [682, 28, 677, 26], [682, 29, 677, 27], [682, 30, 677, 28, "ctx"], [683, 12, 678, 10], [683, 13, 678, 11], [683, 14, 678, 12], [685, 12, 680, 10], [686, 12, 681, 10, "applyStrongBlur"], [686, 27, 681, 25], [686, 28, 681, 26, "ctx"], [686, 31, 681, 29], [686, 33, 681, 31, "paddedX"], [686, 40, 681, 38], [686, 42, 681, 40, "paddedY"], [686, 49, 681, 47], [686, 51, 681, 49, "<PERSON><PERSON><PERSON><PERSON>"], [686, 62, 681, 60], [686, 64, 681, 62, "paddedHeight"], [686, 76, 681, 74], [686, 77, 681, 75], [688, 12, 683, 10], [689, 12, 684, 10, "console"], [689, 19, 684, 17], [689, 20, 684, 18, "log"], [689, 23, 684, 21], [689, 24, 684, 22], [689, 102, 684, 100], [689, 103, 684, 101], [691, 12, 686, 10], [692, 12, 687, 10], [692, 18, 687, 16, "testImageData"], [692, 31, 687, 29], [692, 34, 687, 32, "ctx"], [692, 37, 687, 35], [692, 38, 687, 36, "getImageData"], [692, 50, 687, 48], [692, 51, 687, 49, "paddedX"], [692, 58, 687, 56], [692, 61, 687, 59], [692, 63, 687, 61], [692, 65, 687, 63, "paddedY"], [692, 72, 687, 70], [692, 75, 687, 73], [692, 77, 687, 75], [692, 79, 687, 77], [692, 81, 687, 79], [692, 83, 687, 81], [692, 85, 687, 83], [692, 86, 687, 84], [693, 12, 688, 10, "console"], [693, 19, 688, 17], [693, 20, 688, 18, "log"], [693, 23, 688, 21], [693, 24, 688, 22], [693, 70, 688, 68], [693, 72, 688, 70], [694, 14, 689, 12, "firstPixel"], [694, 24, 689, 22], [694, 26, 689, 24], [694, 27, 689, 25, "testImageData"], [694, 40, 689, 38], [694, 41, 689, 39, "data"], [694, 45, 689, 43], [694, 46, 689, 44], [694, 47, 689, 45], [694, 48, 689, 46], [694, 50, 689, 48, "testImageData"], [694, 63, 689, 61], [694, 64, 689, 62, "data"], [694, 68, 689, 66], [694, 69, 689, 67], [694, 70, 689, 68], [694, 71, 689, 69], [694, 73, 689, 71, "testImageData"], [694, 86, 689, 84], [694, 87, 689, 85, "data"], [694, 91, 689, 89], [694, 92, 689, 90], [694, 93, 689, 91], [694, 94, 689, 92], [694, 95, 689, 93], [695, 14, 690, 12, "secondPixel"], [695, 25, 690, 23], [695, 27, 690, 25], [695, 28, 690, 26, "testImageData"], [695, 41, 690, 39], [695, 42, 690, 40, "data"], [695, 46, 690, 44], [695, 47, 690, 45], [695, 48, 690, 46], [695, 49, 690, 47], [695, 51, 690, 49, "testImageData"], [695, 64, 690, 62], [695, 65, 690, 63, "data"], [695, 69, 690, 67], [695, 70, 690, 68], [695, 71, 690, 69], [695, 72, 690, 70], [695, 74, 690, 72, "testImageData"], [695, 87, 690, 85], [695, 88, 690, 86, "data"], [695, 92, 690, 90], [695, 93, 690, 91], [695, 94, 690, 92], [695, 95, 690, 93], [696, 12, 691, 10], [696, 13, 691, 11], [696, 14, 691, 12], [697, 12, 693, 10, "console"], [697, 19, 693, 17], [697, 20, 693, 18, "log"], [697, 23, 693, 21], [697, 24, 693, 22], [697, 50, 693, 48, "index"], [697, 55, 693, 53], [697, 58, 693, 56], [697, 59, 693, 57], [697, 79, 693, 77], [697, 80, 693, 78], [698, 10, 694, 8], [698, 11, 694, 9], [698, 12, 694, 10], [699, 10, 696, 8, "console"], [699, 17, 696, 15], [699, 18, 696, 16, "log"], [699, 21, 696, 19], [699, 22, 696, 20], [699, 48, 696, 46, "detectedFaces"], [699, 61, 696, 59], [699, 62, 696, 60, "length"], [699, 68, 696, 66], [699, 104, 696, 102], [699, 105, 696, 103], [700, 8, 697, 6], [700, 9, 697, 7], [700, 15, 697, 13], [701, 10, 698, 8, "console"], [701, 17, 698, 15], [701, 18, 698, 16, "log"], [701, 21, 698, 19], [701, 22, 698, 20], [701, 109, 698, 107], [701, 110, 698, 108], [702, 10, 699, 8], [703, 10, 700, 8, "applyFallbackFaceBlur"], [703, 31, 700, 29], [703, 32, 700, 30, "ctx"], [703, 35, 700, 33], [703, 37, 700, 35, "img"], [703, 40, 700, 38], [703, 41, 700, 39, "width"], [703, 46, 700, 44], [703, 48, 700, 46, "img"], [703, 51, 700, 49], [703, 52, 700, 50, "height"], [703, 58, 700, 56], [703, 59, 700, 57], [704, 8, 701, 6], [705, 8, 703, 6, "setProcessingProgress"], [705, 29, 703, 27], [705, 30, 703, 28], [705, 32, 703, 30], [705, 33, 703, 31], [707, 8, 705, 6], [708, 8, 706, 6, "console"], [708, 15, 706, 13], [708, 16, 706, 14, "log"], [708, 19, 706, 17], [708, 20, 706, 18], [708, 85, 706, 83], [708, 86, 706, 84], [709, 8, 707, 6], [709, 14, 707, 12, "blurredImageBlob"], [709, 30, 707, 28], [709, 33, 707, 31], [709, 39, 707, 37], [709, 43, 707, 41, "Promise"], [709, 50, 707, 48], [709, 51, 707, 56, "resolve"], [709, 58, 707, 63], [709, 62, 707, 68], [710, 10, 708, 8, "canvas"], [710, 16, 708, 14], [710, 17, 708, 15, "toBlob"], [710, 23, 708, 21], [710, 24, 708, 23, "blob"], [710, 28, 708, 27], [710, 32, 708, 32, "resolve"], [710, 39, 708, 39], [710, 40, 708, 40, "blob"], [710, 44, 708, 45], [710, 45, 708, 46], [710, 47, 708, 48], [710, 59, 708, 60], [710, 61, 708, 62], [710, 64, 708, 65], [710, 65, 708, 66], [711, 8, 709, 6], [711, 9, 709, 7], [711, 10, 709, 8], [712, 8, 711, 6], [712, 14, 711, 12, "blurredImageUrl"], [712, 29, 711, 27], [712, 32, 711, 30, "URL"], [712, 35, 711, 33], [712, 36, 711, 34, "createObjectURL"], [712, 51, 711, 49], [712, 52, 711, 50, "blurredImageBlob"], [712, 68, 711, 66], [712, 69, 711, 67], [713, 8, 712, 6, "console"], [713, 15, 712, 13], [713, 16, 712, 14, "log"], [713, 19, 712, 17], [713, 20, 712, 18], [713, 66, 712, 64], [713, 68, 712, 66, "blurredImageUrl"], [713, 83, 712, 81], [713, 84, 712, 82, "substring"], [713, 93, 712, 91], [713, 94, 712, 92], [713, 95, 712, 93], [713, 97, 712, 95], [713, 99, 712, 97], [713, 100, 712, 98], [713, 103, 712, 101], [713, 108, 712, 106], [713, 109, 712, 107], [715, 8, 714, 6], [716, 8, 715, 6, "setCapturedPhoto"], [716, 24, 715, 22], [716, 25, 715, 23, "blurredImageUrl"], [716, 40, 715, 38], [716, 41, 715, 39], [717, 8, 716, 6, "console"], [717, 15, 716, 13], [717, 16, 716, 14, "log"], [717, 19, 716, 17], [717, 20, 716, 18], [717, 87, 716, 85], [717, 88, 716, 86], [718, 8, 718, 6, "setProcessingProgress"], [718, 29, 718, 27], [718, 30, 718, 28], [718, 33, 718, 31], [718, 34, 718, 32], [720, 8, 720, 6], [721, 8, 721, 6], [721, 14, 721, 12, "completeProcessing"], [721, 32, 721, 30], [721, 33, 721, 31, "blurredImageUrl"], [721, 48, 721, 46], [721, 49, 721, 47], [722, 6, 723, 4], [722, 7, 723, 5], [722, 8, 723, 6], [722, 15, 723, 13, "error"], [722, 20, 723, 18], [722, 22, 723, 20], [723, 8, 724, 6, "console"], [723, 15, 724, 13], [723, 16, 724, 14, "error"], [723, 21, 724, 19], [723, 22, 724, 20], [723, 57, 724, 55], [723, 59, 724, 57, "error"], [723, 64, 724, 62], [723, 65, 724, 63], [724, 8, 725, 6, "setErrorMessage"], [724, 23, 725, 21], [724, 24, 725, 22], [724, 50, 725, 48], [724, 51, 725, 49], [725, 8, 726, 6, "setProcessingState"], [725, 26, 726, 24], [725, 27, 726, 25], [725, 34, 726, 32], [725, 35, 726, 33], [726, 6, 727, 4], [727, 4, 728, 2], [727, 5, 728, 3], [729, 4, 730, 2], [730, 4, 731, 2], [730, 10, 731, 8, "completeProcessing"], [730, 28, 731, 26], [730, 31, 731, 29], [730, 37, 731, 36, "blurredImageUrl"], [730, 52, 731, 59], [730, 56, 731, 64], [731, 6, 732, 4], [731, 10, 732, 8], [732, 8, 733, 6, "setProcessingState"], [732, 26, 733, 24], [732, 27, 733, 25], [732, 37, 733, 35], [732, 38, 733, 36], [734, 8, 735, 6], [735, 8, 736, 6], [735, 14, 736, 12, "timestamp"], [735, 23, 736, 21], [735, 26, 736, 24, "Date"], [735, 30, 736, 28], [735, 31, 736, 29, "now"], [735, 34, 736, 32], [735, 35, 736, 33], [735, 36, 736, 34], [736, 8, 737, 6], [736, 14, 737, 12, "result"], [736, 20, 737, 18], [736, 23, 737, 21], [737, 10, 738, 8, "imageUrl"], [737, 18, 738, 16], [737, 20, 738, 18, "blurredImageUrl"], [737, 35, 738, 33], [738, 10, 739, 8, "localUri"], [738, 18, 739, 16], [738, 20, 739, 18, "blurredImageUrl"], [738, 35, 739, 33], [739, 10, 740, 8, "challengeCode"], [739, 23, 740, 21], [739, 25, 740, 23, "challengeCode"], [739, 38, 740, 36], [739, 42, 740, 40], [739, 44, 740, 42], [740, 10, 741, 8, "timestamp"], [740, 19, 741, 17], [741, 10, 742, 8, "jobId"], [741, 15, 742, 13], [741, 17, 742, 15], [741, 27, 742, 25, "timestamp"], [741, 36, 742, 34], [741, 38, 742, 36], [742, 10, 743, 8, "status"], [742, 16, 743, 14], [742, 18, 743, 16], [743, 8, 744, 6], [743, 9, 744, 7], [744, 8, 746, 6, "console"], [744, 15, 746, 13], [744, 16, 746, 14, "log"], [744, 19, 746, 17], [744, 20, 746, 18], [744, 100, 746, 98], [744, 102, 746, 100], [745, 10, 747, 8, "imageUrl"], [745, 18, 747, 16], [745, 20, 747, 18, "blurredImageUrl"], [745, 35, 747, 33], [745, 36, 747, 34, "substring"], [745, 45, 747, 43], [745, 46, 747, 44], [745, 47, 747, 45], [745, 49, 747, 47], [745, 51, 747, 49], [745, 52, 747, 50], [745, 55, 747, 53], [745, 60, 747, 58], [746, 10, 748, 8, "timestamp"], [746, 19, 748, 17], [747, 10, 749, 8, "jobId"], [747, 15, 749, 13], [747, 17, 749, 15, "result"], [747, 23, 749, 21], [747, 24, 749, 22, "jobId"], [748, 8, 750, 6], [748, 9, 750, 7], [748, 10, 750, 8], [750, 8, 752, 6], [751, 8, 753, 6, "onComplete"], [751, 18, 753, 16], [751, 19, 753, 17, "result"], [751, 25, 753, 23], [751, 26, 753, 24], [752, 6, 755, 4], [752, 7, 755, 5], [752, 8, 755, 6], [752, 15, 755, 13, "error"], [752, 20, 755, 18], [752, 22, 755, 20], [753, 8, 756, 6, "console"], [753, 15, 756, 13], [753, 16, 756, 14, "error"], [753, 21, 756, 19], [753, 22, 756, 20], [753, 57, 756, 55], [753, 59, 756, 57, "error"], [753, 64, 756, 62], [753, 65, 756, 63], [754, 8, 757, 6, "setErrorMessage"], [754, 23, 757, 21], [754, 24, 757, 22], [754, 56, 757, 54], [754, 57, 757, 55], [755, 8, 758, 6, "setProcessingState"], [755, 26, 758, 24], [755, 27, 758, 25], [755, 34, 758, 32], [755, 35, 758, 33], [756, 6, 759, 4], [757, 4, 760, 2], [757, 5, 760, 3], [759, 4, 762, 2], [760, 4, 763, 2], [760, 10, 763, 8, "triggerServerProcessing"], [760, 33, 763, 31], [760, 36, 763, 34], [760, 42, 763, 34, "triggerServerProcessing"], [760, 43, 763, 41, "privateImageUrl"], [760, 58, 763, 64], [760, 60, 763, 66, "timestamp"], [760, 69, 763, 83], [760, 74, 763, 88], [761, 6, 764, 4], [761, 10, 764, 8], [762, 8, 765, 6, "console"], [762, 15, 765, 13], [762, 16, 765, 14, "log"], [762, 19, 765, 17], [762, 20, 765, 18], [762, 74, 765, 72], [762, 76, 765, 74, "privateImageUrl"], [762, 91, 765, 89], [762, 92, 765, 90], [763, 8, 766, 6, "setProcessingState"], [763, 26, 766, 24], [763, 27, 766, 25], [763, 39, 766, 37], [763, 40, 766, 38], [764, 8, 767, 6, "setProcessingProgress"], [764, 29, 767, 27], [764, 30, 767, 28], [764, 32, 767, 30], [764, 33, 767, 31], [765, 8, 769, 6], [765, 14, 769, 12, "requestBody"], [765, 25, 769, 23], [765, 28, 769, 26], [766, 10, 770, 8, "imageUrl"], [766, 18, 770, 16], [766, 20, 770, 18, "privateImageUrl"], [766, 35, 770, 33], [767, 10, 771, 8, "userId"], [767, 16, 771, 14], [768, 10, 772, 8, "requestId"], [768, 19, 772, 17], [769, 10, 773, 8, "timestamp"], [769, 19, 773, 17], [770, 10, 774, 8, "platform"], [770, 18, 774, 16], [770, 20, 774, 18], [771, 8, 775, 6], [771, 9, 775, 7], [772, 8, 777, 6, "console"], [772, 15, 777, 13], [772, 16, 777, 14, "log"], [772, 19, 777, 17], [772, 20, 777, 18], [772, 65, 777, 63], [772, 67, 777, 65, "requestBody"], [772, 78, 777, 76], [772, 79, 777, 77], [774, 8, 779, 6], [775, 8, 780, 6], [775, 14, 780, 12, "response"], [775, 22, 780, 20], [775, 25, 780, 23], [775, 31, 780, 29, "fetch"], [775, 36, 780, 34], [775, 37, 780, 35], [775, 40, 780, 38, "API_BASE_URL"], [775, 52, 780, 50], [775, 72, 780, 70], [775, 74, 780, 72], [776, 10, 781, 8, "method"], [776, 16, 781, 14], [776, 18, 781, 16], [776, 24, 781, 22], [777, 10, 782, 8, "headers"], [777, 17, 782, 15], [777, 19, 782, 17], [778, 12, 783, 10], [778, 26, 783, 24], [778, 28, 783, 26], [778, 46, 783, 44], [779, 12, 784, 10], [779, 27, 784, 25], [779, 29, 784, 27], [779, 39, 784, 37], [779, 45, 784, 43, "getAuthToken"], [779, 57, 784, 55], [779, 58, 784, 56], [779, 59, 784, 57], [780, 10, 785, 8], [780, 11, 785, 9], [781, 10, 786, 8, "body"], [781, 14, 786, 12], [781, 16, 786, 14, "JSON"], [781, 20, 786, 18], [781, 21, 786, 19, "stringify"], [781, 30, 786, 28], [781, 31, 786, 29, "requestBody"], [781, 42, 786, 40], [782, 8, 787, 6], [782, 9, 787, 7], [782, 10, 787, 8], [783, 8, 789, 6], [783, 12, 789, 10], [783, 13, 789, 11, "response"], [783, 21, 789, 19], [783, 22, 789, 20, "ok"], [783, 24, 789, 22], [783, 26, 789, 24], [784, 10, 790, 8], [784, 16, 790, 14, "errorText"], [784, 25, 790, 23], [784, 28, 790, 26], [784, 34, 790, 32, "response"], [784, 42, 790, 40], [784, 43, 790, 41, "text"], [784, 47, 790, 45], [784, 48, 790, 46], [784, 49, 790, 47], [785, 10, 791, 8, "console"], [785, 17, 791, 15], [785, 18, 791, 16, "error"], [785, 23, 791, 21], [785, 24, 791, 22], [785, 68, 791, 66], [785, 70, 791, 68, "response"], [785, 78, 791, 76], [785, 79, 791, 77, "status"], [785, 85, 791, 83], [785, 87, 791, 85, "errorText"], [785, 96, 791, 94], [785, 97, 791, 95], [786, 10, 792, 8], [786, 16, 792, 14], [786, 20, 792, 18, "Error"], [786, 25, 792, 23], [786, 26, 792, 24], [786, 48, 792, 46, "response"], [786, 56, 792, 54], [786, 57, 792, 55, "status"], [786, 63, 792, 61], [786, 67, 792, 65, "response"], [786, 75, 792, 73], [786, 76, 792, 74, "statusText"], [786, 86, 792, 84], [786, 88, 792, 86], [786, 89, 792, 87], [787, 8, 793, 6], [788, 8, 795, 6], [788, 14, 795, 12, "result"], [788, 20, 795, 18], [788, 23, 795, 21], [788, 29, 795, 27, "response"], [788, 37, 795, 35], [788, 38, 795, 36, "json"], [788, 42, 795, 40], [788, 43, 795, 41], [788, 44, 795, 42], [789, 8, 796, 6, "console"], [789, 15, 796, 13], [789, 16, 796, 14, "log"], [789, 19, 796, 17], [789, 20, 796, 18], [789, 68, 796, 66], [789, 70, 796, 68, "result"], [789, 76, 796, 74], [789, 77, 796, 75], [790, 8, 798, 6], [790, 12, 798, 10], [790, 13, 798, 11, "result"], [790, 19, 798, 17], [790, 20, 798, 18, "jobId"], [790, 25, 798, 23], [790, 27, 798, 25], [791, 10, 799, 8], [791, 16, 799, 14], [791, 20, 799, 18, "Error"], [791, 25, 799, 23], [791, 26, 799, 24], [791, 70, 799, 68], [791, 71, 799, 69], [792, 8, 800, 6], [794, 8, 802, 6], [795, 8, 803, 6], [795, 14, 803, 12, "pollForCompletion"], [795, 31, 803, 29], [795, 32, 803, 30, "result"], [795, 38, 803, 36], [795, 39, 803, 37, "jobId"], [795, 44, 803, 42], [795, 46, 803, 44, "timestamp"], [795, 55, 803, 53], [795, 56, 803, 54], [796, 6, 804, 4], [796, 7, 804, 5], [796, 8, 804, 6], [796, 15, 804, 13, "error"], [796, 20, 804, 18], [796, 22, 804, 20], [797, 8, 805, 6, "console"], [797, 15, 805, 13], [797, 16, 805, 14, "error"], [797, 21, 805, 19], [797, 22, 805, 20], [797, 57, 805, 55], [797, 59, 805, 57, "error"], [797, 64, 805, 62], [797, 65, 805, 63], [798, 8, 806, 6, "setErrorMessage"], [798, 23, 806, 21], [798, 24, 806, 22], [798, 52, 806, 50, "error"], [798, 57, 806, 55], [798, 58, 806, 56, "message"], [798, 65, 806, 63], [798, 67, 806, 65], [798, 68, 806, 66], [799, 8, 807, 6, "setProcessingState"], [799, 26, 807, 24], [799, 27, 807, 25], [799, 34, 807, 32], [799, 35, 807, 33], [800, 6, 808, 4], [801, 4, 809, 2], [801, 5, 809, 3], [802, 4, 810, 2], [803, 4, 811, 2], [803, 10, 811, 8, "pollForCompletion"], [803, 27, 811, 25], [803, 30, 811, 28], [803, 36, 811, 28, "pollForCompletion"], [803, 37, 811, 35, "jobId"], [803, 42, 811, 48], [803, 44, 811, 50, "timestamp"], [803, 53, 811, 67], [803, 55, 811, 69, "attempts"], [803, 63, 811, 77], [803, 66, 811, 80], [803, 67, 811, 81], [803, 72, 811, 86], [804, 6, 812, 4], [804, 12, 812, 10, "MAX_ATTEMPTS"], [804, 24, 812, 22], [804, 27, 812, 25], [804, 29, 812, 27], [804, 30, 812, 28], [804, 31, 812, 29], [805, 6, 813, 4], [805, 12, 813, 10, "POLL_INTERVAL"], [805, 25, 813, 23], [805, 28, 813, 26], [805, 32, 813, 30], [805, 33, 813, 31], [805, 34, 813, 32], [807, 6, 815, 4, "console"], [807, 13, 815, 11], [807, 14, 815, 12, "log"], [807, 17, 815, 15], [807, 18, 815, 16], [807, 53, 815, 51, "attempts"], [807, 61, 815, 59], [807, 64, 815, 62], [807, 65, 815, 63], [807, 69, 815, 67, "MAX_ATTEMPTS"], [807, 81, 815, 79], [807, 93, 815, 91, "jobId"], [807, 98, 815, 96], [807, 100, 815, 98], [807, 101, 815, 99], [808, 6, 817, 4], [808, 10, 817, 8, "attempts"], [808, 18, 817, 16], [808, 22, 817, 20, "MAX_ATTEMPTS"], [808, 34, 817, 32], [808, 36, 817, 34], [809, 8, 818, 6, "console"], [809, 15, 818, 13], [809, 16, 818, 14, "error"], [809, 21, 818, 19], [809, 22, 818, 20], [809, 75, 818, 73], [809, 76, 818, 74], [810, 8, 819, 6, "setErrorMessage"], [810, 23, 819, 21], [810, 24, 819, 22], [810, 63, 819, 61], [810, 64, 819, 62], [811, 8, 820, 6, "setProcessingState"], [811, 26, 820, 24], [811, 27, 820, 25], [811, 34, 820, 32], [811, 35, 820, 33], [812, 8, 821, 6], [813, 6, 822, 4], [814, 6, 824, 4], [814, 10, 824, 8], [815, 8, 825, 6], [815, 14, 825, 12, "response"], [815, 22, 825, 20], [815, 25, 825, 23], [815, 31, 825, 29, "fetch"], [815, 36, 825, 34], [815, 37, 825, 35], [815, 40, 825, 38, "API_BASE_URL"], [815, 52, 825, 50], [815, 75, 825, 73, "jobId"], [815, 80, 825, 78], [815, 82, 825, 80], [815, 84, 825, 82], [816, 10, 826, 8, "headers"], [816, 17, 826, 15], [816, 19, 826, 17], [817, 12, 827, 10], [817, 27, 827, 25], [817, 29, 827, 27], [817, 39, 827, 37], [817, 45, 827, 43, "getAuthToken"], [817, 57, 827, 55], [817, 58, 827, 56], [817, 59, 827, 57], [818, 10, 828, 8], [819, 8, 829, 6], [819, 9, 829, 7], [819, 10, 829, 8], [820, 8, 831, 6], [820, 12, 831, 10], [820, 13, 831, 11, "response"], [820, 21, 831, 19], [820, 22, 831, 20, "ok"], [820, 24, 831, 22], [820, 26, 831, 24], [821, 10, 832, 8], [821, 16, 832, 14], [821, 20, 832, 18, "Error"], [821, 25, 832, 23], [821, 26, 832, 24], [821, 34, 832, 32, "response"], [821, 42, 832, 40], [821, 43, 832, 41, "status"], [821, 49, 832, 47], [821, 54, 832, 52, "response"], [821, 62, 832, 60], [821, 63, 832, 61, "statusText"], [821, 73, 832, 71], [821, 75, 832, 73], [821, 76, 832, 74], [822, 8, 833, 6], [823, 8, 835, 6], [823, 14, 835, 12, "status"], [823, 20, 835, 18], [823, 23, 835, 21], [823, 29, 835, 27, "response"], [823, 37, 835, 35], [823, 38, 835, 36, "json"], [823, 42, 835, 40], [823, 43, 835, 41], [823, 44, 835, 42], [824, 8, 836, 6, "console"], [824, 15, 836, 13], [824, 16, 836, 14, "log"], [824, 19, 836, 17], [824, 20, 836, 18], [824, 54, 836, 52], [824, 56, 836, 54, "status"], [824, 62, 836, 60], [824, 63, 836, 61], [825, 8, 838, 6], [825, 12, 838, 10, "status"], [825, 18, 838, 16], [825, 19, 838, 17, "status"], [825, 25, 838, 23], [825, 30, 838, 28], [825, 41, 838, 39], [825, 43, 838, 41], [826, 10, 839, 8, "console"], [826, 17, 839, 15], [826, 18, 839, 16, "log"], [826, 21, 839, 19], [826, 22, 839, 20], [826, 73, 839, 71], [826, 74, 839, 72], [827, 10, 840, 8, "setProcessingProgress"], [827, 31, 840, 29], [827, 32, 840, 30], [827, 35, 840, 33], [827, 36, 840, 34], [828, 10, 841, 8, "setProcessingState"], [828, 28, 841, 26], [828, 29, 841, 27], [828, 40, 841, 38], [828, 41, 841, 39], [829, 10, 842, 8], [830, 10, 843, 8], [830, 16, 843, 14, "result"], [830, 22, 843, 20], [830, 25, 843, 23], [831, 12, 844, 10, "imageUrl"], [831, 20, 844, 18], [831, 22, 844, 20, "status"], [831, 28, 844, 26], [831, 29, 844, 27, "publicUrl"], [831, 38, 844, 36], [832, 12, 844, 38], [833, 12, 845, 10, "localUri"], [833, 20, 845, 18], [833, 22, 845, 20, "capturedPhoto"], [833, 35, 845, 33], [833, 39, 845, 37, "status"], [833, 45, 845, 43], [833, 46, 845, 44, "publicUrl"], [833, 55, 845, 53], [834, 12, 845, 55], [835, 12, 846, 10, "challengeCode"], [835, 25, 846, 23], [835, 27, 846, 25, "challengeCode"], [835, 40, 846, 38], [835, 44, 846, 42], [835, 46, 846, 44], [836, 12, 847, 10, "timestamp"], [836, 21, 847, 19], [837, 12, 848, 10, "processingStatus"], [837, 28, 848, 26], [837, 30, 848, 28], [838, 10, 849, 8], [838, 11, 849, 9], [839, 10, 850, 8, "console"], [839, 17, 850, 15], [839, 18, 850, 16, "log"], [839, 21, 850, 19], [839, 22, 850, 20], [839, 57, 850, 55], [839, 59, 850, 57, "result"], [839, 65, 850, 63], [839, 66, 850, 64], [840, 10, 851, 8, "onComplete"], [840, 20, 851, 18], [840, 21, 851, 19, "result"], [840, 27, 851, 25], [840, 28, 851, 26], [841, 10, 852, 8], [842, 8, 853, 6], [842, 9, 853, 7], [842, 15, 853, 13], [842, 19, 853, 17, "status"], [842, 25, 853, 23], [842, 26, 853, 24, "status"], [842, 32, 853, 30], [842, 37, 853, 35], [842, 45, 853, 43], [842, 47, 853, 45], [843, 10, 854, 8, "console"], [843, 17, 854, 15], [843, 18, 854, 16, "error"], [843, 23, 854, 21], [843, 24, 854, 22], [843, 60, 854, 58], [843, 62, 854, 60, "status"], [843, 68, 854, 66], [843, 69, 854, 67, "error"], [843, 74, 854, 72], [843, 75, 854, 73], [844, 10, 855, 8], [844, 16, 855, 14], [844, 20, 855, 18, "Error"], [844, 25, 855, 23], [844, 26, 855, 24, "status"], [844, 32, 855, 30], [844, 33, 855, 31, "error"], [844, 38, 855, 36], [844, 42, 855, 40], [844, 61, 855, 59], [844, 62, 855, 60], [845, 8, 856, 6], [845, 9, 856, 7], [845, 15, 856, 13], [846, 10, 857, 8], [847, 10, 858, 8], [847, 16, 858, 14, "progressValue"], [847, 29, 858, 27], [847, 32, 858, 30], [847, 34, 858, 32], [847, 37, 858, 36, "attempts"], [847, 45, 858, 44], [847, 48, 858, 47, "MAX_ATTEMPTS"], [847, 60, 858, 59], [847, 63, 858, 63], [847, 65, 858, 65], [848, 10, 859, 8, "console"], [848, 17, 859, 15], [848, 18, 859, 16, "log"], [848, 21, 859, 19], [848, 22, 859, 20], [848, 71, 859, 69, "progressValue"], [848, 84, 859, 82], [848, 87, 859, 85], [848, 88, 859, 86], [849, 10, 860, 8, "setProcessingProgress"], [849, 31, 860, 29], [849, 32, 860, 30, "progressValue"], [849, 45, 860, 43], [849, 46, 860, 44], [850, 10, 862, 8, "setTimeout"], [850, 20, 862, 18], [850, 21, 862, 19], [850, 27, 862, 25], [851, 12, 863, 10, "pollForCompletion"], [851, 29, 863, 27], [851, 30, 863, 28, "jobId"], [851, 35, 863, 33], [851, 37, 863, 35, "timestamp"], [851, 46, 863, 44], [851, 48, 863, 46, "attempts"], [851, 56, 863, 54], [851, 59, 863, 57], [851, 60, 863, 58], [851, 61, 863, 59], [852, 10, 864, 8], [852, 11, 864, 9], [852, 13, 864, 11, "POLL_INTERVAL"], [852, 26, 864, 24], [852, 27, 864, 25], [853, 8, 865, 6], [854, 6, 866, 4], [854, 7, 866, 5], [854, 8, 866, 6], [854, 15, 866, 13, "error"], [854, 20, 866, 18], [854, 22, 866, 20], [855, 8, 867, 6, "console"], [855, 15, 867, 13], [855, 16, 867, 14, "error"], [855, 21, 867, 19], [855, 22, 867, 20], [855, 54, 867, 52], [855, 56, 867, 54, "error"], [855, 61, 867, 59], [855, 62, 867, 60], [856, 8, 868, 6, "setErrorMessage"], [856, 23, 868, 21], [856, 24, 868, 22], [856, 62, 868, 60, "error"], [856, 67, 868, 65], [856, 68, 868, 66, "message"], [856, 75, 868, 73], [856, 77, 868, 75], [856, 78, 868, 76], [857, 8, 869, 6, "setProcessingState"], [857, 26, 869, 24], [857, 27, 869, 25], [857, 34, 869, 32], [857, 35, 869, 33], [858, 6, 870, 4], [859, 4, 871, 2], [859, 5, 871, 3], [860, 4, 872, 2], [861, 4, 873, 2], [861, 10, 873, 8, "getAuthToken"], [861, 22, 873, 20], [861, 25, 873, 23], [861, 31, 873, 23, "getAuthToken"], [861, 32, 873, 23], [861, 37, 873, 52], [862, 6, 874, 4], [863, 6, 875, 4], [864, 6, 876, 4], [864, 13, 876, 11], [864, 30, 876, 28], [865, 4, 877, 2], [865, 5, 877, 3], [867, 4, 879, 2], [868, 4, 880, 2], [868, 10, 880, 8, "retryCapture"], [868, 22, 880, 20], [868, 25, 880, 23], [868, 29, 880, 23, "useCallback"], [868, 47, 880, 34], [868, 49, 880, 35], [868, 55, 880, 41], [869, 6, 881, 4, "console"], [869, 13, 881, 11], [869, 14, 881, 12, "log"], [869, 17, 881, 15], [869, 18, 881, 16], [869, 55, 881, 53], [869, 56, 881, 54], [870, 6, 882, 4, "setProcessingState"], [870, 24, 882, 22], [870, 25, 882, 23], [870, 31, 882, 29], [870, 32, 882, 30], [871, 6, 883, 4, "setErrorMessage"], [871, 21, 883, 19], [871, 22, 883, 20], [871, 24, 883, 22], [871, 25, 883, 23], [872, 6, 884, 4, "setCapturedPhoto"], [872, 22, 884, 20], [872, 23, 884, 21], [872, 25, 884, 23], [872, 26, 884, 24], [873, 6, 885, 4, "setProcessingProgress"], [873, 27, 885, 25], [873, 28, 885, 26], [873, 29, 885, 27], [873, 30, 885, 28], [874, 4, 886, 2], [874, 5, 886, 3], [874, 7, 886, 5], [874, 9, 886, 7], [874, 10, 886, 8], [875, 4, 887, 2], [876, 4, 888, 2], [876, 8, 888, 2, "useEffect"], [876, 24, 888, 11], [876, 26, 888, 12], [876, 32, 888, 18], [877, 6, 889, 4, "console"], [877, 13, 889, 11], [877, 14, 889, 12, "log"], [877, 17, 889, 15], [877, 18, 889, 16], [877, 53, 889, 51], [877, 55, 889, 53, "permission"], [877, 65, 889, 63], [877, 66, 889, 64], [878, 6, 890, 4], [878, 10, 890, 8, "permission"], [878, 20, 890, 18], [878, 22, 890, 20], [879, 8, 891, 6, "console"], [879, 15, 891, 13], [879, 16, 891, 14, "log"], [879, 19, 891, 17], [879, 20, 891, 18], [879, 57, 891, 55], [879, 59, 891, 57, "permission"], [879, 69, 891, 67], [879, 70, 891, 68, "granted"], [879, 77, 891, 75], [879, 78, 891, 76], [880, 6, 892, 4], [881, 4, 893, 2], [881, 5, 893, 3], [881, 7, 893, 5], [881, 8, 893, 6, "permission"], [881, 18, 893, 16], [881, 19, 893, 17], [881, 20, 893, 18], [882, 4, 894, 2], [883, 4, 895, 2], [883, 8, 895, 6], [883, 9, 895, 7, "permission"], [883, 19, 895, 17], [883, 21, 895, 19], [884, 6, 896, 4, "console"], [884, 13, 896, 11], [884, 14, 896, 12, "log"], [884, 17, 896, 15], [884, 18, 896, 16], [884, 67, 896, 65], [884, 68, 896, 66], [885, 6, 897, 4], [885, 26, 898, 6], [885, 30, 898, 6, "_jsxDevRuntime"], [885, 44, 898, 6], [885, 45, 898, 6, "jsxDEV"], [885, 51, 898, 6], [885, 53, 898, 7, "_View"], [885, 58, 898, 7], [885, 59, 898, 7, "default"], [885, 66, 898, 11], [886, 8, 898, 12, "style"], [886, 13, 898, 17], [886, 15, 898, 19, "styles"], [886, 21, 898, 25], [886, 22, 898, 26, "container"], [886, 31, 898, 36], [887, 8, 898, 36, "children"], [887, 16, 898, 36], [887, 32, 899, 8], [887, 36, 899, 8, "_jsxDevRuntime"], [887, 50, 899, 8], [887, 51, 899, 8, "jsxDEV"], [887, 57, 899, 8], [887, 59, 899, 9, "_ActivityIndicator"], [887, 77, 899, 9], [887, 78, 899, 9, "default"], [887, 85, 899, 26], [888, 10, 899, 27, "size"], [888, 14, 899, 31], [888, 16, 899, 32], [888, 23, 899, 39], [889, 10, 899, 40, "color"], [889, 15, 899, 45], [889, 17, 899, 46], [890, 8, 899, 55], [891, 10, 899, 55, "fileName"], [891, 18, 899, 55], [891, 20, 899, 55, "_jsxFileName"], [891, 32, 899, 55], [892, 10, 899, 55, "lineNumber"], [892, 20, 899, 55], [893, 10, 899, 55, "columnNumber"], [893, 22, 899, 55], [894, 8, 899, 55], [894, 15, 899, 57], [894, 16, 899, 58], [894, 31, 900, 8], [894, 35, 900, 8, "_jsxDevRuntime"], [894, 49, 900, 8], [894, 50, 900, 8, "jsxDEV"], [894, 56, 900, 8], [894, 58, 900, 9, "_Text"], [894, 63, 900, 9], [894, 64, 900, 9, "default"], [894, 71, 900, 13], [895, 10, 900, 14, "style"], [895, 15, 900, 19], [895, 17, 900, 21, "styles"], [895, 23, 900, 27], [895, 24, 900, 28, "loadingText"], [895, 35, 900, 40], [896, 10, 900, 40, "children"], [896, 18, 900, 40], [896, 20, 900, 41], [897, 8, 900, 58], [898, 10, 900, 58, "fileName"], [898, 18, 900, 58], [898, 20, 900, 58, "_jsxFileName"], [898, 32, 900, 58], [899, 10, 900, 58, "lineNumber"], [899, 20, 900, 58], [900, 10, 900, 58, "columnNumber"], [900, 22, 900, 58], [901, 8, 900, 58], [901, 15, 900, 64], [901, 16, 900, 65], [902, 6, 900, 65], [903, 8, 900, 65, "fileName"], [903, 16, 900, 65], [903, 18, 900, 65, "_jsxFileName"], [903, 30, 900, 65], [904, 8, 900, 65, "lineNumber"], [904, 18, 900, 65], [905, 8, 900, 65, "columnNumber"], [905, 20, 900, 65], [906, 6, 900, 65], [906, 13, 901, 12], [906, 14, 901, 13], [907, 4, 903, 2], [908, 4, 904, 2], [908, 8, 904, 6], [908, 9, 904, 7, "permission"], [908, 19, 904, 17], [908, 20, 904, 18, "granted"], [908, 27, 904, 25], [908, 29, 904, 27], [909, 6, 905, 4, "console"], [909, 13, 905, 11], [909, 14, 905, 12, "log"], [909, 17, 905, 15], [909, 18, 905, 16], [909, 93, 905, 91], [909, 94, 905, 92], [910, 6, 906, 4], [910, 26, 907, 6], [910, 30, 907, 6, "_jsxDevRuntime"], [910, 44, 907, 6], [910, 45, 907, 6, "jsxDEV"], [910, 51, 907, 6], [910, 53, 907, 7, "_View"], [910, 58, 907, 7], [910, 59, 907, 7, "default"], [910, 66, 907, 11], [911, 8, 907, 12, "style"], [911, 13, 907, 17], [911, 15, 907, 19, "styles"], [911, 21, 907, 25], [911, 22, 907, 26, "container"], [911, 31, 907, 36], [912, 8, 907, 36, "children"], [912, 16, 907, 36], [912, 31, 908, 8], [912, 35, 908, 8, "_jsxDevRuntime"], [912, 49, 908, 8], [912, 50, 908, 8, "jsxDEV"], [912, 56, 908, 8], [912, 58, 908, 9, "_View"], [912, 63, 908, 9], [912, 64, 908, 9, "default"], [912, 71, 908, 13], [913, 10, 908, 14, "style"], [913, 15, 908, 19], [913, 17, 908, 21, "styles"], [913, 23, 908, 27], [913, 24, 908, 28, "permissionContent"], [913, 41, 908, 46], [914, 10, 908, 46, "children"], [914, 18, 908, 46], [914, 34, 909, 10], [914, 38, 909, 10, "_jsxDevRuntime"], [914, 52, 909, 10], [914, 53, 909, 10, "jsxDEV"], [914, 59, 909, 10], [914, 61, 909, 11, "_lucideReactNative"], [914, 79, 909, 11], [914, 80, 909, 11, "Camera"], [914, 86, 909, 21], [915, 12, 909, 22, "size"], [915, 16, 909, 26], [915, 18, 909, 28], [915, 20, 909, 31], [916, 12, 909, 32, "color"], [916, 17, 909, 37], [916, 19, 909, 38], [917, 10, 909, 47], [918, 12, 909, 47, "fileName"], [918, 20, 909, 47], [918, 22, 909, 47, "_jsxFileName"], [918, 34, 909, 47], [919, 12, 909, 47, "lineNumber"], [919, 22, 909, 47], [920, 12, 909, 47, "columnNumber"], [920, 24, 909, 47], [921, 10, 909, 47], [921, 17, 909, 49], [921, 18, 909, 50], [921, 33, 910, 10], [921, 37, 910, 10, "_jsxDevRuntime"], [921, 51, 910, 10], [921, 52, 910, 10, "jsxDEV"], [921, 58, 910, 10], [921, 60, 910, 11, "_Text"], [921, 65, 910, 11], [921, 66, 910, 11, "default"], [921, 73, 910, 15], [922, 12, 910, 16, "style"], [922, 17, 910, 21], [922, 19, 910, 23, "styles"], [922, 25, 910, 29], [922, 26, 910, 30, "permissionTitle"], [922, 41, 910, 46], [923, 12, 910, 46, "children"], [923, 20, 910, 46], [923, 22, 910, 47], [924, 10, 910, 73], [925, 12, 910, 73, "fileName"], [925, 20, 910, 73], [925, 22, 910, 73, "_jsxFileName"], [925, 34, 910, 73], [926, 12, 910, 73, "lineNumber"], [926, 22, 910, 73], [927, 12, 910, 73, "columnNumber"], [927, 24, 910, 73], [928, 10, 910, 73], [928, 17, 910, 79], [928, 18, 910, 80], [928, 33, 911, 10], [928, 37, 911, 10, "_jsxDevRuntime"], [928, 51, 911, 10], [928, 52, 911, 10, "jsxDEV"], [928, 58, 911, 10], [928, 60, 911, 11, "_Text"], [928, 65, 911, 11], [928, 66, 911, 11, "default"], [928, 73, 911, 15], [929, 12, 911, 16, "style"], [929, 17, 911, 21], [929, 19, 911, 23, "styles"], [929, 25, 911, 29], [929, 26, 911, 30, "permissionDescription"], [929, 47, 911, 52], [930, 12, 911, 52, "children"], [930, 20, 911, 52], [930, 22, 911, 53], [931, 10, 914, 10], [932, 12, 914, 10, "fileName"], [932, 20, 914, 10], [932, 22, 914, 10, "_jsxFileName"], [932, 34, 914, 10], [933, 12, 914, 10, "lineNumber"], [933, 22, 914, 10], [934, 12, 914, 10, "columnNumber"], [934, 24, 914, 10], [935, 10, 914, 10], [935, 17, 914, 16], [935, 18, 914, 17], [935, 33, 915, 10], [935, 37, 915, 10, "_jsxDevRuntime"], [935, 51, 915, 10], [935, 52, 915, 10, "jsxDEV"], [935, 58, 915, 10], [935, 60, 915, 11, "_TouchableOpacity"], [935, 77, 915, 11], [935, 78, 915, 11, "default"], [935, 85, 915, 27], [936, 12, 915, 28, "onPress"], [936, 19, 915, 35], [936, 21, 915, 37, "requestPermission"], [936, 38, 915, 55], [937, 12, 915, 56, "style"], [937, 17, 915, 61], [937, 19, 915, 63, "styles"], [937, 25, 915, 69], [937, 26, 915, 70, "primaryButton"], [937, 39, 915, 84], [938, 12, 915, 84, "children"], [938, 20, 915, 84], [938, 35, 916, 12], [938, 39, 916, 12, "_jsxDevRuntime"], [938, 53, 916, 12], [938, 54, 916, 12, "jsxDEV"], [938, 60, 916, 12], [938, 62, 916, 13, "_Text"], [938, 67, 916, 13], [938, 68, 916, 13, "default"], [938, 75, 916, 17], [939, 14, 916, 18, "style"], [939, 19, 916, 23], [939, 21, 916, 25, "styles"], [939, 27, 916, 31], [939, 28, 916, 32, "primaryButtonText"], [939, 45, 916, 50], [940, 14, 916, 50, "children"], [940, 22, 916, 50], [940, 24, 916, 51], [941, 12, 916, 67], [942, 14, 916, 67, "fileName"], [942, 22, 916, 67], [942, 24, 916, 67, "_jsxFileName"], [942, 36, 916, 67], [943, 14, 916, 67, "lineNumber"], [943, 24, 916, 67], [944, 14, 916, 67, "columnNumber"], [944, 26, 916, 67], [945, 12, 916, 67], [945, 19, 916, 73], [946, 10, 916, 74], [947, 12, 916, 74, "fileName"], [947, 20, 916, 74], [947, 22, 916, 74, "_jsxFileName"], [947, 34, 916, 74], [948, 12, 916, 74, "lineNumber"], [948, 22, 916, 74], [949, 12, 916, 74, "columnNumber"], [949, 24, 916, 74], [950, 10, 916, 74], [950, 17, 917, 28], [950, 18, 917, 29], [950, 33, 918, 10], [950, 37, 918, 10, "_jsxDevRuntime"], [950, 51, 918, 10], [950, 52, 918, 10, "jsxDEV"], [950, 58, 918, 10], [950, 60, 918, 11, "_TouchableOpacity"], [950, 77, 918, 11], [950, 78, 918, 11, "default"], [950, 85, 918, 27], [951, 12, 918, 28, "onPress"], [951, 19, 918, 35], [951, 21, 918, 37, "onCancel"], [951, 29, 918, 46], [952, 12, 918, 47, "style"], [952, 17, 918, 52], [952, 19, 918, 54, "styles"], [952, 25, 918, 60], [952, 26, 918, 61, "secondaryButton"], [952, 41, 918, 77], [953, 12, 918, 77, "children"], [953, 20, 918, 77], [953, 35, 919, 12], [953, 39, 919, 12, "_jsxDevRuntime"], [953, 53, 919, 12], [953, 54, 919, 12, "jsxDEV"], [953, 60, 919, 12], [953, 62, 919, 13, "_Text"], [953, 67, 919, 13], [953, 68, 919, 13, "default"], [953, 75, 919, 17], [954, 14, 919, 18, "style"], [954, 19, 919, 23], [954, 21, 919, 25, "styles"], [954, 27, 919, 31], [954, 28, 919, 32, "secondaryButtonText"], [954, 47, 919, 52], [955, 14, 919, 52, "children"], [955, 22, 919, 52], [955, 24, 919, 53], [956, 12, 919, 59], [957, 14, 919, 59, "fileName"], [957, 22, 919, 59], [957, 24, 919, 59, "_jsxFileName"], [957, 36, 919, 59], [958, 14, 919, 59, "lineNumber"], [958, 24, 919, 59], [959, 14, 919, 59, "columnNumber"], [959, 26, 919, 59], [960, 12, 919, 59], [960, 19, 919, 65], [961, 10, 919, 66], [962, 12, 919, 66, "fileName"], [962, 20, 919, 66], [962, 22, 919, 66, "_jsxFileName"], [962, 34, 919, 66], [963, 12, 919, 66, "lineNumber"], [963, 22, 919, 66], [964, 12, 919, 66, "columnNumber"], [964, 24, 919, 66], [965, 10, 919, 66], [965, 17, 920, 28], [965, 18, 920, 29], [966, 8, 920, 29], [967, 10, 920, 29, "fileName"], [967, 18, 920, 29], [967, 20, 920, 29, "_jsxFileName"], [967, 32, 920, 29], [968, 10, 920, 29, "lineNumber"], [968, 20, 920, 29], [969, 10, 920, 29, "columnNumber"], [969, 22, 920, 29], [970, 8, 920, 29], [970, 15, 921, 14], [971, 6, 921, 15], [972, 8, 921, 15, "fileName"], [972, 16, 921, 15], [972, 18, 921, 15, "_jsxFileName"], [972, 30, 921, 15], [973, 8, 921, 15, "lineNumber"], [973, 18, 921, 15], [974, 8, 921, 15, "columnNumber"], [974, 20, 921, 15], [975, 6, 921, 15], [975, 13, 922, 12], [975, 14, 922, 13], [976, 4, 924, 2], [977, 4, 925, 2], [978, 4, 926, 2, "console"], [978, 11, 926, 9], [978, 12, 926, 10, "log"], [978, 15, 926, 13], [978, 16, 926, 14], [978, 55, 926, 53], [978, 56, 926, 54], [979, 4, 928, 2], [979, 24, 929, 4], [979, 28, 929, 4, "_jsxDevRuntime"], [979, 42, 929, 4], [979, 43, 929, 4, "jsxDEV"], [979, 49, 929, 4], [979, 51, 929, 5, "_View"], [979, 56, 929, 5], [979, 57, 929, 5, "default"], [979, 64, 929, 9], [980, 6, 929, 10, "style"], [980, 11, 929, 15], [980, 13, 929, 17, "styles"], [980, 19, 929, 23], [980, 20, 929, 24, "container"], [980, 29, 929, 34], [981, 6, 929, 34, "children"], [981, 14, 929, 34], [981, 30, 931, 6], [981, 34, 931, 6, "_jsxDevRuntime"], [981, 48, 931, 6], [981, 49, 931, 6, "jsxDEV"], [981, 55, 931, 6], [981, 57, 931, 7, "_View"], [981, 62, 931, 7], [981, 63, 931, 7, "default"], [981, 70, 931, 11], [982, 8, 931, 12, "style"], [982, 13, 931, 17], [982, 15, 931, 19, "styles"], [982, 21, 931, 25], [982, 22, 931, 26, "cameraContainer"], [982, 37, 931, 42], [983, 8, 931, 43, "id"], [983, 10, 931, 45], [983, 12, 931, 46], [983, 29, 931, 63], [984, 8, 931, 63, "children"], [984, 16, 931, 63], [984, 32, 932, 8], [984, 36, 932, 8, "_jsxDevRuntime"], [984, 50, 932, 8], [984, 51, 932, 8, "jsxDEV"], [984, 57, 932, 8], [984, 59, 932, 9, "_expoCamera"], [984, 70, 932, 9], [984, 71, 932, 9, "CameraView"], [984, 81, 932, 19], [985, 10, 933, 10, "ref"], [985, 13, 933, 13], [985, 15, 933, 15, "cameraRef"], [985, 24, 933, 25], [986, 10, 934, 10, "style"], [986, 15, 934, 15], [986, 17, 934, 17], [986, 18, 934, 18, "styles"], [986, 24, 934, 24], [986, 25, 934, 25, "camera"], [986, 31, 934, 31], [986, 33, 934, 33], [987, 12, 934, 35, "backgroundColor"], [987, 27, 934, 50], [987, 29, 934, 52], [988, 10, 934, 62], [988, 11, 934, 63], [988, 12, 934, 65], [989, 10, 935, 10, "facing"], [989, 16, 935, 16], [989, 18, 935, 17], [989, 24, 935, 23], [990, 10, 936, 10, "onLayout"], [990, 18, 936, 18], [990, 20, 936, 21, "e"], [990, 21, 936, 22], [990, 25, 936, 27], [991, 12, 937, 12, "console"], [991, 19, 937, 19], [991, 20, 937, 20, "log"], [991, 23, 937, 23], [991, 24, 937, 24], [991, 56, 937, 56], [991, 58, 937, 58, "e"], [991, 59, 937, 59], [991, 60, 937, 60, "nativeEvent"], [991, 71, 937, 71], [991, 72, 937, 72, "layout"], [991, 78, 937, 78], [991, 79, 937, 79], [992, 12, 938, 12, "setViewSize"], [992, 23, 938, 23], [992, 24, 938, 24], [993, 14, 938, 26, "width"], [993, 19, 938, 31], [993, 21, 938, 33, "e"], [993, 22, 938, 34], [993, 23, 938, 35, "nativeEvent"], [993, 34, 938, 46], [993, 35, 938, 47, "layout"], [993, 41, 938, 53], [993, 42, 938, 54, "width"], [993, 47, 938, 59], [994, 14, 938, 61, "height"], [994, 20, 938, 67], [994, 22, 938, 69, "e"], [994, 23, 938, 70], [994, 24, 938, 71, "nativeEvent"], [994, 35, 938, 82], [994, 36, 938, 83, "layout"], [994, 42, 938, 89], [994, 43, 938, 90, "height"], [995, 12, 938, 97], [995, 13, 938, 98], [995, 14, 938, 99], [996, 10, 939, 10], [996, 11, 939, 12], [997, 10, 940, 10, "onCameraReady"], [997, 23, 940, 23], [997, 25, 940, 25, "onCameraReady"], [997, 26, 940, 25], [997, 31, 940, 31], [998, 12, 941, 12, "console"], [998, 19, 941, 19], [998, 20, 941, 20, "log"], [998, 23, 941, 23], [998, 24, 941, 24], [998, 55, 941, 55], [998, 56, 941, 56], [999, 12, 942, 12, "setIsCameraReady"], [999, 28, 942, 28], [999, 29, 942, 29], [999, 33, 942, 33], [999, 34, 942, 34], [999, 35, 942, 35], [999, 36, 942, 36], [1000, 10, 943, 10], [1000, 11, 943, 12], [1001, 10, 944, 10, "onMountError"], [1001, 22, 944, 22], [1001, 24, 944, 25, "error"], [1001, 29, 944, 30], [1001, 33, 944, 35], [1002, 12, 945, 12, "console"], [1002, 19, 945, 19], [1002, 20, 945, 20, "error"], [1002, 25, 945, 25], [1002, 26, 945, 26], [1002, 63, 945, 63], [1002, 65, 945, 65, "error"], [1002, 70, 945, 70], [1002, 71, 945, 71], [1003, 12, 946, 12, "setErrorMessage"], [1003, 27, 946, 27], [1003, 28, 946, 28], [1003, 57, 946, 57], [1003, 58, 946, 58], [1004, 12, 947, 12, "setProcessingState"], [1004, 30, 947, 30], [1004, 31, 947, 31], [1004, 38, 947, 38], [1004, 39, 947, 39], [1005, 10, 948, 10], [1006, 8, 948, 12], [1007, 10, 948, 12, "fileName"], [1007, 18, 948, 12], [1007, 20, 948, 12, "_jsxFileName"], [1007, 32, 948, 12], [1008, 10, 948, 12, "lineNumber"], [1008, 20, 948, 12], [1009, 10, 948, 12, "columnNumber"], [1009, 22, 948, 12], [1010, 8, 948, 12], [1010, 15, 949, 9], [1010, 16, 949, 10], [1010, 18, 951, 9], [1010, 19, 951, 10, "isCameraReady"], [1010, 32, 951, 23], [1010, 49, 952, 10], [1010, 53, 952, 10, "_jsxDevRuntime"], [1010, 67, 952, 10], [1010, 68, 952, 10, "jsxDEV"], [1010, 74, 952, 10], [1010, 76, 952, 11, "_View"], [1010, 81, 952, 11], [1010, 82, 952, 11, "default"], [1010, 89, 952, 15], [1011, 10, 952, 16, "style"], [1011, 15, 952, 21], [1011, 17, 952, 23], [1011, 18, 952, 24, "StyleSheet"], [1011, 37, 952, 34], [1011, 38, 952, 35, "absoluteFill"], [1011, 50, 952, 47], [1011, 52, 952, 49], [1012, 12, 952, 51, "backgroundColor"], [1012, 27, 952, 66], [1012, 29, 952, 68], [1012, 49, 952, 88], [1013, 12, 952, 90, "justifyContent"], [1013, 26, 952, 104], [1013, 28, 952, 106], [1013, 36, 952, 114], [1014, 12, 952, 116, "alignItems"], [1014, 22, 952, 126], [1014, 24, 952, 128], [1014, 32, 952, 136], [1015, 12, 952, 138, "zIndex"], [1015, 18, 952, 144], [1015, 20, 952, 146], [1016, 10, 952, 151], [1016, 11, 952, 152], [1016, 12, 952, 154], [1017, 10, 952, 154, "children"], [1017, 18, 952, 154], [1017, 33, 953, 12], [1017, 37, 953, 12, "_jsxDevRuntime"], [1017, 51, 953, 12], [1017, 52, 953, 12, "jsxDEV"], [1017, 58, 953, 12], [1017, 60, 953, 13, "_View"], [1017, 65, 953, 13], [1017, 66, 953, 13, "default"], [1017, 73, 953, 17], [1018, 12, 953, 18, "style"], [1018, 17, 953, 23], [1018, 19, 953, 25], [1019, 14, 953, 27, "backgroundColor"], [1019, 29, 953, 42], [1019, 31, 953, 44], [1019, 51, 953, 64], [1020, 14, 953, 66, "padding"], [1020, 21, 953, 73], [1020, 23, 953, 75], [1020, 25, 953, 77], [1021, 14, 953, 79, "borderRadius"], [1021, 26, 953, 91], [1021, 28, 953, 93], [1021, 30, 953, 95], [1022, 14, 953, 97, "alignItems"], [1022, 24, 953, 107], [1022, 26, 953, 109], [1023, 12, 953, 118], [1023, 13, 953, 120], [1024, 12, 953, 120, "children"], [1024, 20, 953, 120], [1024, 36, 954, 14], [1024, 40, 954, 14, "_jsxDevRuntime"], [1024, 54, 954, 14], [1024, 55, 954, 14, "jsxDEV"], [1024, 61, 954, 14], [1024, 63, 954, 15, "_ActivityIndicator"], [1024, 81, 954, 15], [1024, 82, 954, 15, "default"], [1024, 89, 954, 32], [1025, 14, 954, 33, "size"], [1025, 18, 954, 37], [1025, 20, 954, 38], [1025, 27, 954, 45], [1026, 14, 954, 46, "color"], [1026, 19, 954, 51], [1026, 21, 954, 52], [1026, 30, 954, 61], [1027, 14, 954, 62, "style"], [1027, 19, 954, 67], [1027, 21, 954, 69], [1028, 16, 954, 71, "marginBottom"], [1028, 28, 954, 83], [1028, 30, 954, 85], [1029, 14, 954, 88], [1030, 12, 954, 90], [1031, 14, 954, 90, "fileName"], [1031, 22, 954, 90], [1031, 24, 954, 90, "_jsxFileName"], [1031, 36, 954, 90], [1032, 14, 954, 90, "lineNumber"], [1032, 24, 954, 90], [1033, 14, 954, 90, "columnNumber"], [1033, 26, 954, 90], [1034, 12, 954, 90], [1034, 19, 954, 92], [1034, 20, 954, 93], [1034, 35, 955, 14], [1034, 39, 955, 14, "_jsxDevRuntime"], [1034, 53, 955, 14], [1034, 54, 955, 14, "jsxDEV"], [1034, 60, 955, 14], [1034, 62, 955, 15, "_Text"], [1034, 67, 955, 15], [1034, 68, 955, 15, "default"], [1034, 75, 955, 19], [1035, 14, 955, 20, "style"], [1035, 19, 955, 25], [1035, 21, 955, 27], [1036, 16, 955, 29, "color"], [1036, 21, 955, 34], [1036, 23, 955, 36], [1036, 29, 955, 42], [1037, 16, 955, 44, "fontSize"], [1037, 24, 955, 52], [1037, 26, 955, 54], [1037, 28, 955, 56], [1038, 16, 955, 58, "fontWeight"], [1038, 26, 955, 68], [1038, 28, 955, 70], [1039, 14, 955, 76], [1039, 15, 955, 78], [1040, 14, 955, 78, "children"], [1040, 22, 955, 78], [1040, 24, 955, 79], [1041, 12, 955, 101], [1042, 14, 955, 101, "fileName"], [1042, 22, 955, 101], [1042, 24, 955, 101, "_jsxFileName"], [1042, 36, 955, 101], [1043, 14, 955, 101, "lineNumber"], [1043, 24, 955, 101], [1044, 14, 955, 101, "columnNumber"], [1044, 26, 955, 101], [1045, 12, 955, 101], [1045, 19, 955, 107], [1045, 20, 955, 108], [1045, 35, 956, 14], [1045, 39, 956, 14, "_jsxDevRuntime"], [1045, 53, 956, 14], [1045, 54, 956, 14, "jsxDEV"], [1045, 60, 956, 14], [1045, 62, 956, 15, "_Text"], [1045, 67, 956, 15], [1045, 68, 956, 15, "default"], [1045, 75, 956, 19], [1046, 14, 956, 20, "style"], [1046, 19, 956, 25], [1046, 21, 956, 27], [1047, 16, 956, 29, "color"], [1047, 21, 956, 34], [1047, 23, 956, 36], [1047, 32, 956, 45], [1048, 16, 956, 47, "fontSize"], [1048, 24, 956, 55], [1048, 26, 956, 57], [1048, 28, 956, 59], [1049, 16, 956, 61, "marginTop"], [1049, 25, 956, 70], [1049, 27, 956, 72], [1050, 14, 956, 74], [1050, 15, 956, 76], [1051, 14, 956, 76, "children"], [1051, 22, 956, 76], [1051, 24, 956, 77], [1052, 12, 956, 88], [1053, 14, 956, 88, "fileName"], [1053, 22, 956, 88], [1053, 24, 956, 88, "_jsxFileName"], [1053, 36, 956, 88], [1054, 14, 956, 88, "lineNumber"], [1054, 24, 956, 88], [1055, 14, 956, 88, "columnNumber"], [1055, 26, 956, 88], [1056, 12, 956, 88], [1056, 19, 956, 94], [1056, 20, 956, 95], [1057, 10, 956, 95], [1058, 12, 956, 95, "fileName"], [1058, 20, 956, 95], [1058, 22, 956, 95, "_jsxFileName"], [1058, 34, 956, 95], [1059, 12, 956, 95, "lineNumber"], [1059, 22, 956, 95], [1060, 12, 956, 95, "columnNumber"], [1060, 24, 956, 95], [1061, 10, 956, 95], [1061, 17, 957, 18], [1062, 8, 957, 19], [1063, 10, 957, 19, "fileName"], [1063, 18, 957, 19], [1063, 20, 957, 19, "_jsxFileName"], [1063, 32, 957, 19], [1064, 10, 957, 19, "lineNumber"], [1064, 20, 957, 19], [1065, 10, 957, 19, "columnNumber"], [1065, 22, 957, 19], [1066, 8, 957, 19], [1066, 15, 958, 16], [1066, 16, 959, 9], [1066, 18, 962, 9, "isCameraReady"], [1066, 31, 962, 22], [1066, 35, 962, 26, "previewBlurEnabled"], [1066, 53, 962, 44], [1066, 57, 962, 48, "viewSize"], [1066, 65, 962, 56], [1066, 66, 962, 57, "width"], [1066, 71, 962, 62], [1066, 74, 962, 65], [1066, 75, 962, 66], [1066, 92, 963, 10], [1066, 96, 963, 10, "_jsxDevRuntime"], [1066, 110, 963, 10], [1066, 111, 963, 10, "jsxDEV"], [1066, 117, 963, 10], [1066, 119, 963, 10, "_jsxDevRuntime"], [1066, 133, 963, 10], [1066, 134, 963, 10, "Fragment"], [1066, 142, 963, 10], [1067, 10, 963, 10, "children"], [1067, 18, 963, 10], [1067, 34, 965, 12], [1067, 38, 965, 12, "_jsxDevRuntime"], [1067, 52, 965, 12], [1067, 53, 965, 12, "jsxDEV"], [1067, 59, 965, 12], [1067, 61, 965, 13, "_LiveFaceCanvas"], [1067, 76, 965, 13], [1067, 77, 965, 13, "default"], [1067, 84, 965, 27], [1068, 12, 965, 28, "containerId"], [1068, 23, 965, 39], [1068, 25, 965, 40], [1068, 42, 965, 57], [1069, 12, 965, 58, "width"], [1069, 17, 965, 63], [1069, 19, 965, 65, "viewSize"], [1069, 27, 965, 73], [1069, 28, 965, 74, "width"], [1069, 33, 965, 80], [1070, 12, 965, 81, "height"], [1070, 18, 965, 87], [1070, 20, 965, 89, "viewSize"], [1070, 28, 965, 97], [1070, 29, 965, 98, "height"], [1071, 10, 965, 105], [1072, 12, 965, 105, "fileName"], [1072, 20, 965, 105], [1072, 22, 965, 105, "_jsxFileName"], [1072, 34, 965, 105], [1073, 12, 965, 105, "lineNumber"], [1073, 22, 965, 105], [1074, 12, 965, 105, "columnNumber"], [1074, 24, 965, 105], [1075, 10, 965, 105], [1075, 17, 965, 107], [1075, 18, 965, 108], [1075, 33, 966, 12], [1075, 37, 966, 12, "_jsxDevRuntime"], [1075, 51, 966, 12], [1075, 52, 966, 12, "jsxDEV"], [1075, 58, 966, 12], [1075, 60, 966, 13, "_View"], [1075, 65, 966, 13], [1075, 66, 966, 13, "default"], [1075, 73, 966, 17], [1076, 12, 966, 18, "style"], [1076, 17, 966, 23], [1076, 19, 966, 25], [1076, 20, 966, 26, "StyleSheet"], [1076, 39, 966, 36], [1076, 40, 966, 37, "absoluteFill"], [1076, 52, 966, 49], [1076, 54, 966, 51], [1077, 14, 966, 53, "pointerEvents"], [1077, 27, 966, 66], [1077, 29, 966, 68], [1078, 12, 966, 75], [1078, 13, 966, 76], [1078, 14, 966, 78], [1079, 12, 966, 78, "children"], [1079, 20, 966, 78], [1079, 36, 968, 12], [1079, 40, 968, 12, "_jsxDevRuntime"], [1079, 54, 968, 12], [1079, 55, 968, 12, "jsxDEV"], [1079, 61, 968, 12], [1079, 63, 968, 13, "_expoBlur"], [1079, 72, 968, 13], [1079, 73, 968, 13, "BlurView"], [1079, 81, 968, 21], [1080, 14, 968, 22, "intensity"], [1080, 23, 968, 31], [1080, 25, 968, 33], [1080, 27, 968, 36], [1081, 14, 968, 37, "tint"], [1081, 18, 968, 41], [1081, 20, 968, 42], [1081, 26, 968, 48], [1082, 14, 968, 49, "style"], [1082, 19, 968, 54], [1082, 21, 968, 56], [1082, 22, 968, 57, "styles"], [1082, 28, 968, 63], [1082, 29, 968, 64, "blurZone"], [1082, 37, 968, 72], [1082, 39, 968, 74], [1083, 16, 969, 14, "left"], [1083, 20, 969, 18], [1083, 22, 969, 20], [1083, 23, 969, 21], [1084, 16, 970, 14, "top"], [1084, 19, 970, 17], [1084, 21, 970, 19, "viewSize"], [1084, 29, 970, 27], [1084, 30, 970, 28, "height"], [1084, 36, 970, 34], [1084, 39, 970, 37], [1084, 42, 970, 40], [1085, 16, 971, 14, "width"], [1085, 21, 971, 19], [1085, 23, 971, 21, "viewSize"], [1085, 31, 971, 29], [1085, 32, 971, 30, "width"], [1085, 37, 971, 35], [1086, 16, 972, 14, "height"], [1086, 22, 972, 20], [1086, 24, 972, 22, "viewSize"], [1086, 32, 972, 30], [1086, 33, 972, 31, "height"], [1086, 39, 972, 37], [1086, 42, 972, 40], [1086, 46, 972, 44], [1087, 16, 973, 14, "borderRadius"], [1087, 28, 973, 26], [1087, 30, 973, 28], [1088, 14, 974, 12], [1088, 15, 974, 13], [1089, 12, 974, 15], [1090, 14, 974, 15, "fileName"], [1090, 22, 974, 15], [1090, 24, 974, 15, "_jsxFileName"], [1090, 36, 974, 15], [1091, 14, 974, 15, "lineNumber"], [1091, 24, 974, 15], [1092, 14, 974, 15, "columnNumber"], [1092, 26, 974, 15], [1093, 12, 974, 15], [1093, 19, 974, 17], [1093, 20, 974, 18], [1093, 35, 976, 12], [1093, 39, 976, 12, "_jsxDevRuntime"], [1093, 53, 976, 12], [1093, 54, 976, 12, "jsxDEV"], [1093, 60, 976, 12], [1093, 62, 976, 13, "_expoBlur"], [1093, 71, 976, 13], [1093, 72, 976, 13, "BlurView"], [1093, 80, 976, 21], [1094, 14, 976, 22, "intensity"], [1094, 23, 976, 31], [1094, 25, 976, 33], [1094, 27, 976, 36], [1095, 14, 976, 37, "tint"], [1095, 18, 976, 41], [1095, 20, 976, 42], [1095, 26, 976, 48], [1096, 14, 976, 49, "style"], [1096, 19, 976, 54], [1096, 21, 976, 56], [1096, 22, 976, 57, "styles"], [1096, 28, 976, 63], [1096, 29, 976, 64, "blurZone"], [1096, 37, 976, 72], [1096, 39, 976, 74], [1097, 16, 977, 14, "left"], [1097, 20, 977, 18], [1097, 22, 977, 20], [1097, 23, 977, 21], [1098, 16, 978, 14, "top"], [1098, 19, 978, 17], [1098, 21, 978, 19], [1098, 22, 978, 20], [1099, 16, 979, 14, "width"], [1099, 21, 979, 19], [1099, 23, 979, 21, "viewSize"], [1099, 31, 979, 29], [1099, 32, 979, 30, "width"], [1099, 37, 979, 35], [1100, 16, 980, 14, "height"], [1100, 22, 980, 20], [1100, 24, 980, 22, "viewSize"], [1100, 32, 980, 30], [1100, 33, 980, 31, "height"], [1100, 39, 980, 37], [1100, 42, 980, 40], [1100, 45, 980, 43], [1101, 16, 981, 14, "borderRadius"], [1101, 28, 981, 26], [1101, 30, 981, 28], [1102, 14, 982, 12], [1102, 15, 982, 13], [1103, 12, 982, 15], [1104, 14, 982, 15, "fileName"], [1104, 22, 982, 15], [1104, 24, 982, 15, "_jsxFileName"], [1104, 36, 982, 15], [1105, 14, 982, 15, "lineNumber"], [1105, 24, 982, 15], [1106, 14, 982, 15, "columnNumber"], [1106, 26, 982, 15], [1107, 12, 982, 15], [1107, 19, 982, 17], [1107, 20, 982, 18], [1107, 35, 984, 12], [1107, 39, 984, 12, "_jsxDevRuntime"], [1107, 53, 984, 12], [1107, 54, 984, 12, "jsxDEV"], [1107, 60, 984, 12], [1107, 62, 984, 13, "_expoBlur"], [1107, 71, 984, 13], [1107, 72, 984, 13, "BlurView"], [1107, 80, 984, 21], [1108, 14, 984, 22, "intensity"], [1108, 23, 984, 31], [1108, 25, 984, 33], [1108, 27, 984, 36], [1109, 14, 984, 37, "tint"], [1109, 18, 984, 41], [1109, 20, 984, 42], [1109, 26, 984, 48], [1110, 14, 984, 49, "style"], [1110, 19, 984, 54], [1110, 21, 984, 56], [1110, 22, 984, 57, "styles"], [1110, 28, 984, 63], [1110, 29, 984, 64, "blurZone"], [1110, 37, 984, 72], [1110, 39, 984, 74], [1111, 16, 985, 14, "left"], [1111, 20, 985, 18], [1111, 22, 985, 20, "viewSize"], [1111, 30, 985, 28], [1111, 31, 985, 29, "width"], [1111, 36, 985, 34], [1111, 39, 985, 37], [1111, 42, 985, 40], [1111, 45, 985, 44, "viewSize"], [1111, 53, 985, 52], [1111, 54, 985, 53, "width"], [1111, 59, 985, 58], [1111, 62, 985, 61], [1111, 66, 985, 66], [1112, 16, 986, 14, "top"], [1112, 19, 986, 17], [1112, 21, 986, 19, "viewSize"], [1112, 29, 986, 27], [1112, 30, 986, 28, "height"], [1112, 36, 986, 34], [1112, 39, 986, 37], [1112, 43, 986, 41], [1112, 46, 986, 45, "viewSize"], [1112, 54, 986, 53], [1112, 55, 986, 54, "width"], [1112, 60, 986, 59], [1112, 63, 986, 62], [1112, 67, 986, 67], [1113, 16, 987, 14, "width"], [1113, 21, 987, 19], [1113, 23, 987, 21, "viewSize"], [1113, 31, 987, 29], [1113, 32, 987, 30, "width"], [1113, 37, 987, 35], [1113, 40, 987, 38], [1113, 43, 987, 41], [1114, 16, 988, 14, "height"], [1114, 22, 988, 20], [1114, 24, 988, 22, "viewSize"], [1114, 32, 988, 30], [1114, 33, 988, 31, "width"], [1114, 38, 988, 36], [1114, 41, 988, 39], [1114, 44, 988, 42], [1115, 16, 989, 14, "borderRadius"], [1115, 28, 989, 26], [1115, 30, 989, 29, "viewSize"], [1115, 38, 989, 37], [1115, 39, 989, 38, "width"], [1115, 44, 989, 43], [1115, 47, 989, 46], [1115, 50, 989, 49], [1115, 53, 989, 53], [1116, 14, 990, 12], [1116, 15, 990, 13], [1117, 12, 990, 15], [1118, 14, 990, 15, "fileName"], [1118, 22, 990, 15], [1118, 24, 990, 15, "_jsxFileName"], [1118, 36, 990, 15], [1119, 14, 990, 15, "lineNumber"], [1119, 24, 990, 15], [1120, 14, 990, 15, "columnNumber"], [1120, 26, 990, 15], [1121, 12, 990, 15], [1121, 19, 990, 17], [1121, 20, 990, 18], [1121, 35, 991, 12], [1121, 39, 991, 12, "_jsxDevRuntime"], [1121, 53, 991, 12], [1121, 54, 991, 12, "jsxDEV"], [1121, 60, 991, 12], [1121, 62, 991, 13, "_expoBlur"], [1121, 71, 991, 13], [1121, 72, 991, 13, "BlurView"], [1121, 80, 991, 21], [1122, 14, 991, 22, "intensity"], [1122, 23, 991, 31], [1122, 25, 991, 33], [1122, 27, 991, 36], [1123, 14, 991, 37, "tint"], [1123, 18, 991, 41], [1123, 20, 991, 42], [1123, 26, 991, 48], [1124, 14, 991, 49, "style"], [1124, 19, 991, 54], [1124, 21, 991, 56], [1124, 22, 991, 57, "styles"], [1124, 28, 991, 63], [1124, 29, 991, 64, "blurZone"], [1124, 37, 991, 72], [1124, 39, 991, 74], [1125, 16, 992, 14, "left"], [1125, 20, 992, 18], [1125, 22, 992, 20, "viewSize"], [1125, 30, 992, 28], [1125, 31, 992, 29, "width"], [1125, 36, 992, 34], [1125, 39, 992, 37], [1125, 42, 992, 40], [1125, 45, 992, 44, "viewSize"], [1125, 53, 992, 52], [1125, 54, 992, 53, "width"], [1125, 59, 992, 58], [1125, 62, 992, 61], [1125, 66, 992, 66], [1126, 16, 993, 14, "top"], [1126, 19, 993, 17], [1126, 21, 993, 19, "viewSize"], [1126, 29, 993, 27], [1126, 30, 993, 28, "height"], [1126, 36, 993, 34], [1126, 39, 993, 37], [1126, 42, 993, 40], [1126, 45, 993, 44, "viewSize"], [1126, 53, 993, 52], [1126, 54, 993, 53, "width"], [1126, 59, 993, 58], [1126, 62, 993, 61], [1126, 66, 993, 66], [1127, 16, 994, 14, "width"], [1127, 21, 994, 19], [1127, 23, 994, 21, "viewSize"], [1127, 31, 994, 29], [1127, 32, 994, 30, "width"], [1127, 37, 994, 35], [1127, 40, 994, 38], [1127, 43, 994, 41], [1128, 16, 995, 14, "height"], [1128, 22, 995, 20], [1128, 24, 995, 22, "viewSize"], [1128, 32, 995, 30], [1128, 33, 995, 31, "width"], [1128, 38, 995, 36], [1128, 41, 995, 39], [1128, 44, 995, 42], [1129, 16, 996, 14, "borderRadius"], [1129, 28, 996, 26], [1129, 30, 996, 29, "viewSize"], [1129, 38, 996, 37], [1129, 39, 996, 38, "width"], [1129, 44, 996, 43], [1129, 47, 996, 46], [1129, 50, 996, 49], [1129, 53, 996, 53], [1130, 14, 997, 12], [1130, 15, 997, 13], [1131, 12, 997, 15], [1132, 14, 997, 15, "fileName"], [1132, 22, 997, 15], [1132, 24, 997, 15, "_jsxFileName"], [1132, 36, 997, 15], [1133, 14, 997, 15, "lineNumber"], [1133, 24, 997, 15], [1134, 14, 997, 15, "columnNumber"], [1134, 26, 997, 15], [1135, 12, 997, 15], [1135, 19, 997, 17], [1135, 20, 997, 18], [1135, 35, 998, 12], [1135, 39, 998, 12, "_jsxDevRuntime"], [1135, 53, 998, 12], [1135, 54, 998, 12, "jsxDEV"], [1135, 60, 998, 12], [1135, 62, 998, 13, "_expoBlur"], [1135, 71, 998, 13], [1135, 72, 998, 13, "BlurView"], [1135, 80, 998, 21], [1136, 14, 998, 22, "intensity"], [1136, 23, 998, 31], [1136, 25, 998, 33], [1136, 27, 998, 36], [1137, 14, 998, 37, "tint"], [1137, 18, 998, 41], [1137, 20, 998, 42], [1137, 26, 998, 48], [1138, 14, 998, 49, "style"], [1138, 19, 998, 54], [1138, 21, 998, 56], [1138, 22, 998, 57, "styles"], [1138, 28, 998, 63], [1138, 29, 998, 64, "blurZone"], [1138, 37, 998, 72], [1138, 39, 998, 74], [1139, 16, 999, 14, "left"], [1139, 20, 999, 18], [1139, 22, 999, 20, "viewSize"], [1139, 30, 999, 28], [1139, 31, 999, 29, "width"], [1139, 36, 999, 34], [1139, 39, 999, 37], [1139, 42, 999, 40], [1139, 45, 999, 44, "viewSize"], [1139, 53, 999, 52], [1139, 54, 999, 53, "width"], [1139, 59, 999, 58], [1139, 62, 999, 61], [1139, 66, 999, 66], [1140, 16, 1000, 14, "top"], [1140, 19, 1000, 17], [1140, 21, 1000, 19, "viewSize"], [1140, 29, 1000, 27], [1140, 30, 1000, 28, "height"], [1140, 36, 1000, 34], [1140, 39, 1000, 37], [1140, 42, 1000, 40], [1140, 45, 1000, 44, "viewSize"], [1140, 53, 1000, 52], [1140, 54, 1000, 53, "width"], [1140, 59, 1000, 58], [1140, 62, 1000, 61], [1140, 66, 1000, 66], [1141, 16, 1001, 14, "width"], [1141, 21, 1001, 19], [1141, 23, 1001, 21, "viewSize"], [1141, 31, 1001, 29], [1141, 32, 1001, 30, "width"], [1141, 37, 1001, 35], [1141, 40, 1001, 38], [1141, 43, 1001, 41], [1142, 16, 1002, 14, "height"], [1142, 22, 1002, 20], [1142, 24, 1002, 22, "viewSize"], [1142, 32, 1002, 30], [1142, 33, 1002, 31, "width"], [1142, 38, 1002, 36], [1142, 41, 1002, 39], [1142, 44, 1002, 42], [1143, 16, 1003, 14, "borderRadius"], [1143, 28, 1003, 26], [1143, 30, 1003, 29, "viewSize"], [1143, 38, 1003, 37], [1143, 39, 1003, 38, "width"], [1143, 44, 1003, 43], [1143, 47, 1003, 46], [1143, 50, 1003, 49], [1143, 53, 1003, 53], [1144, 14, 1004, 12], [1144, 15, 1004, 13], [1145, 12, 1004, 15], [1146, 14, 1004, 15, "fileName"], [1146, 22, 1004, 15], [1146, 24, 1004, 15, "_jsxFileName"], [1146, 36, 1004, 15], [1147, 14, 1004, 15, "lineNumber"], [1147, 24, 1004, 15], [1148, 14, 1004, 15, "columnNumber"], [1148, 26, 1004, 15], [1149, 12, 1004, 15], [1149, 19, 1004, 17], [1149, 20, 1004, 18], [1149, 22, 1006, 13, "__DEV__"], [1149, 29, 1006, 20], [1149, 46, 1007, 14], [1149, 50, 1007, 14, "_jsxDevRuntime"], [1149, 64, 1007, 14], [1149, 65, 1007, 14, "jsxDEV"], [1149, 71, 1007, 14], [1149, 73, 1007, 15, "_View"], [1149, 78, 1007, 15], [1149, 79, 1007, 15, "default"], [1149, 86, 1007, 19], [1150, 14, 1007, 20, "style"], [1150, 19, 1007, 25], [1150, 21, 1007, 27, "styles"], [1150, 27, 1007, 33], [1150, 28, 1007, 34, "previewChip"], [1150, 39, 1007, 46], [1151, 14, 1007, 46, "children"], [1151, 22, 1007, 46], [1151, 37, 1008, 16], [1151, 41, 1008, 16, "_jsxDevRuntime"], [1151, 55, 1008, 16], [1151, 56, 1008, 16, "jsxDEV"], [1151, 62, 1008, 16], [1151, 64, 1008, 17, "_Text"], [1151, 69, 1008, 17], [1151, 70, 1008, 17, "default"], [1151, 77, 1008, 21], [1152, 16, 1008, 22, "style"], [1152, 21, 1008, 27], [1152, 23, 1008, 29, "styles"], [1152, 29, 1008, 35], [1152, 30, 1008, 36, "previewChipText"], [1152, 45, 1008, 52], [1153, 16, 1008, 52, "children"], [1153, 24, 1008, 52], [1153, 26, 1008, 53], [1154, 14, 1008, 73], [1155, 16, 1008, 73, "fileName"], [1155, 24, 1008, 73], [1155, 26, 1008, 73, "_jsxFileName"], [1155, 38, 1008, 73], [1156, 16, 1008, 73, "lineNumber"], [1156, 26, 1008, 73], [1157, 16, 1008, 73, "columnNumber"], [1157, 28, 1008, 73], [1158, 14, 1008, 73], [1158, 21, 1008, 79], [1159, 12, 1008, 80], [1160, 14, 1008, 80, "fileName"], [1160, 22, 1008, 80], [1160, 24, 1008, 80, "_jsxFileName"], [1160, 36, 1008, 80], [1161, 14, 1008, 80, "lineNumber"], [1161, 24, 1008, 80], [1162, 14, 1008, 80, "columnNumber"], [1162, 26, 1008, 80], [1163, 12, 1008, 80], [1163, 19, 1009, 20], [1163, 20, 1010, 13], [1164, 10, 1010, 13], [1165, 12, 1010, 13, "fileName"], [1165, 20, 1010, 13], [1165, 22, 1010, 13, "_jsxFileName"], [1165, 34, 1010, 13], [1166, 12, 1010, 13, "lineNumber"], [1166, 22, 1010, 13], [1167, 12, 1010, 13, "columnNumber"], [1167, 24, 1010, 13], [1168, 10, 1010, 13], [1168, 17, 1011, 18], [1168, 18, 1011, 19], [1169, 8, 1011, 19], [1169, 23, 1012, 12], [1169, 24, 1013, 9], [1169, 26, 1015, 9, "isCameraReady"], [1169, 39, 1015, 22], [1169, 56, 1016, 10], [1169, 60, 1016, 10, "_jsxDevRuntime"], [1169, 74, 1016, 10], [1169, 75, 1016, 10, "jsxDEV"], [1169, 81, 1016, 10], [1169, 83, 1016, 10, "_jsxDevRuntime"], [1169, 97, 1016, 10], [1169, 98, 1016, 10, "Fragment"], [1169, 106, 1016, 10], [1170, 10, 1016, 10, "children"], [1170, 18, 1016, 10], [1170, 34, 1018, 12], [1170, 38, 1018, 12, "_jsxDevRuntime"], [1170, 52, 1018, 12], [1170, 53, 1018, 12, "jsxDEV"], [1170, 59, 1018, 12], [1170, 61, 1018, 13, "_View"], [1170, 66, 1018, 13], [1170, 67, 1018, 13, "default"], [1170, 74, 1018, 17], [1171, 12, 1018, 18, "style"], [1171, 17, 1018, 23], [1171, 19, 1018, 25, "styles"], [1171, 25, 1018, 31], [1171, 26, 1018, 32, "headerOverlay"], [1171, 39, 1018, 46], [1172, 12, 1018, 46, "children"], [1172, 20, 1018, 46], [1172, 35, 1019, 14], [1172, 39, 1019, 14, "_jsxDevRuntime"], [1172, 53, 1019, 14], [1172, 54, 1019, 14, "jsxDEV"], [1172, 60, 1019, 14], [1172, 62, 1019, 15, "_View"], [1172, 67, 1019, 15], [1172, 68, 1019, 15, "default"], [1172, 75, 1019, 19], [1173, 14, 1019, 20, "style"], [1173, 19, 1019, 25], [1173, 21, 1019, 27, "styles"], [1173, 27, 1019, 33], [1173, 28, 1019, 34, "headerContent"], [1173, 41, 1019, 48], [1174, 14, 1019, 48, "children"], [1174, 22, 1019, 48], [1174, 38, 1020, 16], [1174, 42, 1020, 16, "_jsxDevRuntime"], [1174, 56, 1020, 16], [1174, 57, 1020, 16, "jsxDEV"], [1174, 63, 1020, 16], [1174, 65, 1020, 17, "_View"], [1174, 70, 1020, 17], [1174, 71, 1020, 17, "default"], [1174, 78, 1020, 21], [1175, 16, 1020, 22, "style"], [1175, 21, 1020, 27], [1175, 23, 1020, 29, "styles"], [1175, 29, 1020, 35], [1175, 30, 1020, 36, "headerLeft"], [1175, 40, 1020, 47], [1176, 16, 1020, 47, "children"], [1176, 24, 1020, 47], [1176, 40, 1021, 18], [1176, 44, 1021, 18, "_jsxDevRuntime"], [1176, 58, 1021, 18], [1176, 59, 1021, 18, "jsxDEV"], [1176, 65, 1021, 18], [1176, 67, 1021, 19, "_Text"], [1176, 72, 1021, 19], [1176, 73, 1021, 19, "default"], [1176, 80, 1021, 23], [1177, 18, 1021, 24, "style"], [1177, 23, 1021, 29], [1177, 25, 1021, 31, "styles"], [1177, 31, 1021, 37], [1177, 32, 1021, 38, "headerTitle"], [1177, 43, 1021, 50], [1178, 18, 1021, 50, "children"], [1178, 26, 1021, 50], [1178, 28, 1021, 51], [1179, 16, 1021, 62], [1180, 18, 1021, 62, "fileName"], [1180, 26, 1021, 62], [1180, 28, 1021, 62, "_jsxFileName"], [1180, 40, 1021, 62], [1181, 18, 1021, 62, "lineNumber"], [1181, 28, 1021, 62], [1182, 18, 1021, 62, "columnNumber"], [1182, 30, 1021, 62], [1183, 16, 1021, 62], [1183, 23, 1021, 68], [1183, 24, 1021, 69], [1183, 39, 1022, 18], [1183, 43, 1022, 18, "_jsxDevRuntime"], [1183, 57, 1022, 18], [1183, 58, 1022, 18, "jsxDEV"], [1183, 64, 1022, 18], [1183, 66, 1022, 19, "_View"], [1183, 71, 1022, 19], [1183, 72, 1022, 19, "default"], [1183, 79, 1022, 23], [1184, 18, 1022, 24, "style"], [1184, 23, 1022, 29], [1184, 25, 1022, 31, "styles"], [1184, 31, 1022, 37], [1184, 32, 1022, 38, "subtitleRow"], [1184, 43, 1022, 50], [1185, 18, 1022, 50, "children"], [1185, 26, 1022, 50], [1185, 42, 1023, 20], [1185, 46, 1023, 20, "_jsxDevRuntime"], [1185, 60, 1023, 20], [1185, 61, 1023, 20, "jsxDEV"], [1185, 67, 1023, 20], [1185, 69, 1023, 21, "_Text"], [1185, 74, 1023, 21], [1185, 75, 1023, 21, "default"], [1185, 82, 1023, 25], [1186, 20, 1023, 26, "style"], [1186, 25, 1023, 31], [1186, 27, 1023, 33, "styles"], [1186, 33, 1023, 39], [1186, 34, 1023, 40, "webIcon"], [1186, 41, 1023, 48], [1187, 20, 1023, 48, "children"], [1187, 28, 1023, 48], [1187, 30, 1023, 49], [1188, 18, 1023, 51], [1189, 20, 1023, 51, "fileName"], [1189, 28, 1023, 51], [1189, 30, 1023, 51, "_jsxFileName"], [1189, 42, 1023, 51], [1190, 20, 1023, 51, "lineNumber"], [1190, 30, 1023, 51], [1191, 20, 1023, 51, "columnNumber"], [1191, 32, 1023, 51], [1192, 18, 1023, 51], [1192, 25, 1023, 57], [1192, 26, 1023, 58], [1192, 41, 1024, 20], [1192, 45, 1024, 20, "_jsxDevRuntime"], [1192, 59, 1024, 20], [1192, 60, 1024, 20, "jsxDEV"], [1192, 66, 1024, 20], [1192, 68, 1024, 21, "_Text"], [1192, 73, 1024, 21], [1192, 74, 1024, 21, "default"], [1192, 81, 1024, 25], [1193, 20, 1024, 26, "style"], [1193, 25, 1024, 31], [1193, 27, 1024, 33, "styles"], [1193, 33, 1024, 39], [1193, 34, 1024, 40, "headerSubtitle"], [1193, 48, 1024, 55], [1194, 20, 1024, 55, "children"], [1194, 28, 1024, 55], [1194, 30, 1024, 56], [1195, 18, 1024, 71], [1196, 20, 1024, 71, "fileName"], [1196, 28, 1024, 71], [1196, 30, 1024, 71, "_jsxFileName"], [1196, 42, 1024, 71], [1197, 20, 1024, 71, "lineNumber"], [1197, 30, 1024, 71], [1198, 20, 1024, 71, "columnNumber"], [1198, 32, 1024, 71], [1199, 18, 1024, 71], [1199, 25, 1024, 77], [1199, 26, 1024, 78], [1200, 16, 1024, 78], [1201, 18, 1024, 78, "fileName"], [1201, 26, 1024, 78], [1201, 28, 1024, 78, "_jsxFileName"], [1201, 40, 1024, 78], [1202, 18, 1024, 78, "lineNumber"], [1202, 28, 1024, 78], [1203, 18, 1024, 78, "columnNumber"], [1203, 30, 1024, 78], [1204, 16, 1024, 78], [1204, 23, 1025, 24], [1204, 24, 1025, 25], [1204, 26, 1026, 19, "challengeCode"], [1204, 39, 1026, 32], [1204, 56, 1027, 20], [1204, 60, 1027, 20, "_jsxDevRuntime"], [1204, 74, 1027, 20], [1204, 75, 1027, 20, "jsxDEV"], [1204, 81, 1027, 20], [1204, 83, 1027, 21, "_View"], [1204, 88, 1027, 21], [1204, 89, 1027, 21, "default"], [1204, 96, 1027, 25], [1205, 18, 1027, 26, "style"], [1205, 23, 1027, 31], [1205, 25, 1027, 33, "styles"], [1205, 31, 1027, 39], [1205, 32, 1027, 40, "challengeRow"], [1205, 44, 1027, 53], [1206, 18, 1027, 53, "children"], [1206, 26, 1027, 53], [1206, 42, 1028, 22], [1206, 46, 1028, 22, "_jsxDevRuntime"], [1206, 60, 1028, 22], [1206, 61, 1028, 22, "jsxDEV"], [1206, 67, 1028, 22], [1206, 69, 1028, 23, "_lucideReactNative"], [1206, 87, 1028, 23], [1206, 88, 1028, 23, "Shield"], [1206, 94, 1028, 29], [1207, 20, 1028, 30, "size"], [1207, 24, 1028, 34], [1207, 26, 1028, 36], [1207, 28, 1028, 39], [1208, 20, 1028, 40, "color"], [1208, 25, 1028, 45], [1208, 27, 1028, 46], [1209, 18, 1028, 52], [1210, 20, 1028, 52, "fileName"], [1210, 28, 1028, 52], [1210, 30, 1028, 52, "_jsxFileName"], [1210, 42, 1028, 52], [1211, 20, 1028, 52, "lineNumber"], [1211, 30, 1028, 52], [1212, 20, 1028, 52, "columnNumber"], [1212, 32, 1028, 52], [1213, 18, 1028, 52], [1213, 25, 1028, 54], [1213, 26, 1028, 55], [1213, 41, 1029, 22], [1213, 45, 1029, 22, "_jsxDevRuntime"], [1213, 59, 1029, 22], [1213, 60, 1029, 22, "jsxDEV"], [1213, 66, 1029, 22], [1213, 68, 1029, 23, "_Text"], [1213, 73, 1029, 23], [1213, 74, 1029, 23, "default"], [1213, 81, 1029, 27], [1214, 20, 1029, 28, "style"], [1214, 25, 1029, 33], [1214, 27, 1029, 35, "styles"], [1214, 33, 1029, 41], [1214, 34, 1029, 42, "challengeCode"], [1214, 47, 1029, 56], [1215, 20, 1029, 56, "children"], [1215, 28, 1029, 56], [1215, 30, 1029, 58, "challengeCode"], [1216, 18, 1029, 71], [1217, 20, 1029, 71, "fileName"], [1217, 28, 1029, 71], [1217, 30, 1029, 71, "_jsxFileName"], [1217, 42, 1029, 71], [1218, 20, 1029, 71, "lineNumber"], [1218, 30, 1029, 71], [1219, 20, 1029, 71, "columnNumber"], [1219, 32, 1029, 71], [1220, 18, 1029, 71], [1220, 25, 1029, 78], [1220, 26, 1029, 79], [1221, 16, 1029, 79], [1222, 18, 1029, 79, "fileName"], [1222, 26, 1029, 79], [1222, 28, 1029, 79, "_jsxFileName"], [1222, 40, 1029, 79], [1223, 18, 1029, 79, "lineNumber"], [1223, 28, 1029, 79], [1224, 18, 1029, 79, "columnNumber"], [1224, 30, 1029, 79], [1225, 16, 1029, 79], [1225, 23, 1030, 26], [1225, 24, 1031, 19], [1226, 14, 1031, 19], [1227, 16, 1031, 19, "fileName"], [1227, 24, 1031, 19], [1227, 26, 1031, 19, "_jsxFileName"], [1227, 38, 1031, 19], [1228, 16, 1031, 19, "lineNumber"], [1228, 26, 1031, 19], [1229, 16, 1031, 19, "columnNumber"], [1229, 28, 1031, 19], [1230, 14, 1031, 19], [1230, 21, 1032, 22], [1230, 22, 1032, 23], [1230, 37, 1033, 16], [1230, 41, 1033, 16, "_jsxDevRuntime"], [1230, 55, 1033, 16], [1230, 56, 1033, 16, "jsxDEV"], [1230, 62, 1033, 16], [1230, 64, 1033, 17, "_TouchableOpacity"], [1230, 81, 1033, 17], [1230, 82, 1033, 17, "default"], [1230, 89, 1033, 33], [1231, 16, 1033, 34, "onPress"], [1231, 23, 1033, 41], [1231, 25, 1033, 43, "onCancel"], [1231, 33, 1033, 52], [1232, 16, 1033, 53, "style"], [1232, 21, 1033, 58], [1232, 23, 1033, 60, "styles"], [1232, 29, 1033, 66], [1232, 30, 1033, 67, "closeButton"], [1232, 41, 1033, 79], [1233, 16, 1033, 79, "children"], [1233, 24, 1033, 79], [1233, 39, 1034, 18], [1233, 43, 1034, 18, "_jsxDevRuntime"], [1233, 57, 1034, 18], [1233, 58, 1034, 18, "jsxDEV"], [1233, 64, 1034, 18], [1233, 66, 1034, 19, "_lucideReactNative"], [1233, 84, 1034, 19], [1233, 85, 1034, 19, "X"], [1233, 86, 1034, 20], [1234, 18, 1034, 21, "size"], [1234, 22, 1034, 25], [1234, 24, 1034, 27], [1234, 26, 1034, 30], [1235, 18, 1034, 31, "color"], [1235, 23, 1034, 36], [1235, 25, 1034, 37], [1236, 16, 1034, 43], [1237, 18, 1034, 43, "fileName"], [1237, 26, 1034, 43], [1237, 28, 1034, 43, "_jsxFileName"], [1237, 40, 1034, 43], [1238, 18, 1034, 43, "lineNumber"], [1238, 28, 1034, 43], [1239, 18, 1034, 43, "columnNumber"], [1239, 30, 1034, 43], [1240, 16, 1034, 43], [1240, 23, 1034, 45], [1241, 14, 1034, 46], [1242, 16, 1034, 46, "fileName"], [1242, 24, 1034, 46], [1242, 26, 1034, 46, "_jsxFileName"], [1242, 38, 1034, 46], [1243, 16, 1034, 46, "lineNumber"], [1243, 26, 1034, 46], [1244, 16, 1034, 46, "columnNumber"], [1244, 28, 1034, 46], [1245, 14, 1034, 46], [1245, 21, 1035, 34], [1245, 22, 1035, 35], [1246, 12, 1035, 35], [1247, 14, 1035, 35, "fileName"], [1247, 22, 1035, 35], [1247, 24, 1035, 35, "_jsxFileName"], [1247, 36, 1035, 35], [1248, 14, 1035, 35, "lineNumber"], [1248, 24, 1035, 35], [1249, 14, 1035, 35, "columnNumber"], [1249, 26, 1035, 35], [1250, 12, 1035, 35], [1250, 19, 1036, 20], [1251, 10, 1036, 21], [1252, 12, 1036, 21, "fileName"], [1252, 20, 1036, 21], [1252, 22, 1036, 21, "_jsxFileName"], [1252, 34, 1036, 21], [1253, 12, 1036, 21, "lineNumber"], [1253, 22, 1036, 21], [1254, 12, 1036, 21, "columnNumber"], [1254, 24, 1036, 21], [1255, 10, 1036, 21], [1255, 17, 1037, 18], [1255, 18, 1037, 19], [1255, 33, 1039, 12], [1255, 37, 1039, 12, "_jsxDevRuntime"], [1255, 51, 1039, 12], [1255, 52, 1039, 12, "jsxDEV"], [1255, 58, 1039, 12], [1255, 60, 1039, 13, "_View"], [1255, 65, 1039, 13], [1255, 66, 1039, 13, "default"], [1255, 73, 1039, 17], [1256, 12, 1039, 18, "style"], [1256, 17, 1039, 23], [1256, 19, 1039, 25, "styles"], [1256, 25, 1039, 31], [1256, 26, 1039, 32, "privacyNotice"], [1256, 39, 1039, 46], [1257, 12, 1039, 46, "children"], [1257, 20, 1039, 46], [1257, 36, 1040, 14], [1257, 40, 1040, 14, "_jsxDevRuntime"], [1257, 54, 1040, 14], [1257, 55, 1040, 14, "jsxDEV"], [1257, 61, 1040, 14], [1257, 63, 1040, 15, "_lucideReactNative"], [1257, 81, 1040, 15], [1257, 82, 1040, 15, "Shield"], [1257, 88, 1040, 21], [1258, 14, 1040, 22, "size"], [1258, 18, 1040, 26], [1258, 20, 1040, 28], [1258, 22, 1040, 31], [1259, 14, 1040, 32, "color"], [1259, 19, 1040, 37], [1259, 21, 1040, 38], [1260, 12, 1040, 47], [1261, 14, 1040, 47, "fileName"], [1261, 22, 1040, 47], [1261, 24, 1040, 47, "_jsxFileName"], [1261, 36, 1040, 47], [1262, 14, 1040, 47, "lineNumber"], [1262, 24, 1040, 47], [1263, 14, 1040, 47, "columnNumber"], [1263, 26, 1040, 47], [1264, 12, 1040, 47], [1264, 19, 1040, 49], [1264, 20, 1040, 50], [1264, 35, 1041, 14], [1264, 39, 1041, 14, "_jsxDevRuntime"], [1264, 53, 1041, 14], [1264, 54, 1041, 14, "jsxDEV"], [1264, 60, 1041, 14], [1264, 62, 1041, 15, "_Text"], [1264, 67, 1041, 15], [1264, 68, 1041, 15, "default"], [1264, 75, 1041, 19], [1265, 14, 1041, 20, "style"], [1265, 19, 1041, 25], [1265, 21, 1041, 27, "styles"], [1265, 27, 1041, 33], [1265, 28, 1041, 34, "privacyText"], [1265, 39, 1041, 46], [1266, 14, 1041, 46, "children"], [1266, 22, 1041, 46], [1266, 24, 1041, 47], [1267, 12, 1043, 14], [1268, 14, 1043, 14, "fileName"], [1268, 22, 1043, 14], [1268, 24, 1043, 14, "_jsxFileName"], [1268, 36, 1043, 14], [1269, 14, 1043, 14, "lineNumber"], [1269, 24, 1043, 14], [1270, 14, 1043, 14, "columnNumber"], [1270, 26, 1043, 14], [1271, 12, 1043, 14], [1271, 19, 1043, 20], [1271, 20, 1043, 21], [1272, 10, 1043, 21], [1273, 12, 1043, 21, "fileName"], [1273, 20, 1043, 21], [1273, 22, 1043, 21, "_jsxFileName"], [1273, 34, 1043, 21], [1274, 12, 1043, 21, "lineNumber"], [1274, 22, 1043, 21], [1275, 12, 1043, 21, "columnNumber"], [1275, 24, 1043, 21], [1276, 10, 1043, 21], [1276, 17, 1044, 18], [1276, 18, 1044, 19], [1276, 33, 1046, 12], [1276, 37, 1046, 12, "_jsxDevRuntime"], [1276, 51, 1046, 12], [1276, 52, 1046, 12, "jsxDEV"], [1276, 58, 1046, 12], [1276, 60, 1046, 13, "_View"], [1276, 65, 1046, 13], [1276, 66, 1046, 13, "default"], [1276, 73, 1046, 17], [1277, 12, 1046, 18, "style"], [1277, 17, 1046, 23], [1277, 19, 1046, 25, "styles"], [1277, 25, 1046, 31], [1277, 26, 1046, 32, "footer<PERSON><PERSON><PERSON>"], [1277, 39, 1046, 46], [1278, 12, 1046, 46, "children"], [1278, 20, 1046, 46], [1278, 36, 1047, 14], [1278, 40, 1047, 14, "_jsxDevRuntime"], [1278, 54, 1047, 14], [1278, 55, 1047, 14, "jsxDEV"], [1278, 61, 1047, 14], [1278, 63, 1047, 15, "_Text"], [1278, 68, 1047, 15], [1278, 69, 1047, 15, "default"], [1278, 76, 1047, 19], [1279, 14, 1047, 20, "style"], [1279, 19, 1047, 25], [1279, 21, 1047, 27, "styles"], [1279, 27, 1047, 33], [1279, 28, 1047, 34, "instruction"], [1279, 39, 1047, 46], [1280, 14, 1047, 46, "children"], [1280, 22, 1047, 46], [1280, 24, 1047, 47], [1281, 12, 1049, 14], [1282, 14, 1049, 14, "fileName"], [1282, 22, 1049, 14], [1282, 24, 1049, 14, "_jsxFileName"], [1282, 36, 1049, 14], [1283, 14, 1049, 14, "lineNumber"], [1283, 24, 1049, 14], [1284, 14, 1049, 14, "columnNumber"], [1284, 26, 1049, 14], [1285, 12, 1049, 14], [1285, 19, 1049, 20], [1285, 20, 1049, 21], [1285, 35, 1051, 14], [1285, 39, 1051, 14, "_jsxDevRuntime"], [1285, 53, 1051, 14], [1285, 54, 1051, 14, "jsxDEV"], [1285, 60, 1051, 14], [1285, 62, 1051, 15, "_TouchableOpacity"], [1285, 79, 1051, 15], [1285, 80, 1051, 15, "default"], [1285, 87, 1051, 31], [1286, 14, 1052, 16, "onPress"], [1286, 21, 1052, 23], [1286, 23, 1052, 25, "capturePhoto"], [1286, 35, 1052, 38], [1287, 14, 1053, 16, "disabled"], [1287, 22, 1053, 24], [1287, 24, 1053, 26, "processingState"], [1287, 39, 1053, 41], [1287, 44, 1053, 46], [1287, 50, 1053, 52], [1287, 54, 1053, 56], [1287, 55, 1053, 57, "isCameraReady"], [1287, 68, 1053, 71], [1288, 14, 1054, 16, "style"], [1288, 19, 1054, 21], [1288, 21, 1054, 23], [1288, 22, 1055, 18, "styles"], [1288, 28, 1055, 24], [1288, 29, 1055, 25, "shutterButton"], [1288, 42, 1055, 38], [1288, 44, 1056, 18, "processingState"], [1288, 59, 1056, 33], [1288, 64, 1056, 38], [1288, 70, 1056, 44], [1288, 74, 1056, 48, "styles"], [1288, 80, 1056, 54], [1288, 81, 1056, 55, "shutterButtonDisabled"], [1288, 102, 1056, 76], [1288, 103, 1057, 18], [1289, 14, 1057, 18, "children"], [1289, 22, 1057, 18], [1289, 24, 1059, 17, "processingState"], [1289, 39, 1059, 32], [1289, 44, 1059, 37], [1289, 50, 1059, 43], [1289, 66, 1060, 18], [1289, 70, 1060, 18, "_jsxDevRuntime"], [1289, 84, 1060, 18], [1289, 85, 1060, 18, "jsxDEV"], [1289, 91, 1060, 18], [1289, 93, 1060, 19, "_View"], [1289, 98, 1060, 19], [1289, 99, 1060, 19, "default"], [1289, 106, 1060, 23], [1290, 16, 1060, 24, "style"], [1290, 21, 1060, 29], [1290, 23, 1060, 31, "styles"], [1290, 29, 1060, 37], [1290, 30, 1060, 38, "shutterInner"], [1291, 14, 1060, 51], [1292, 16, 1060, 51, "fileName"], [1292, 24, 1060, 51], [1292, 26, 1060, 51, "_jsxFileName"], [1292, 38, 1060, 51], [1293, 16, 1060, 51, "lineNumber"], [1293, 26, 1060, 51], [1294, 16, 1060, 51, "columnNumber"], [1294, 28, 1060, 51], [1295, 14, 1060, 51], [1295, 21, 1060, 53], [1295, 22, 1060, 54], [1295, 38, 1062, 18], [1295, 42, 1062, 18, "_jsxDevRuntime"], [1295, 56, 1062, 18], [1295, 57, 1062, 18, "jsxDEV"], [1295, 63, 1062, 18], [1295, 65, 1062, 19, "_ActivityIndicator"], [1295, 83, 1062, 19], [1295, 84, 1062, 19, "default"], [1295, 91, 1062, 36], [1296, 16, 1062, 37, "size"], [1296, 20, 1062, 41], [1296, 22, 1062, 42], [1296, 29, 1062, 49], [1297, 16, 1062, 50, "color"], [1297, 21, 1062, 55], [1297, 23, 1062, 56], [1298, 14, 1062, 65], [1299, 16, 1062, 65, "fileName"], [1299, 24, 1062, 65], [1299, 26, 1062, 65, "_jsxFileName"], [1299, 38, 1062, 65], [1300, 16, 1062, 65, "lineNumber"], [1300, 26, 1062, 65], [1301, 16, 1062, 65, "columnNumber"], [1301, 28, 1062, 65], [1302, 14, 1062, 65], [1302, 21, 1062, 67], [1303, 12, 1063, 17], [1304, 14, 1063, 17, "fileName"], [1304, 22, 1063, 17], [1304, 24, 1063, 17, "_jsxFileName"], [1304, 36, 1063, 17], [1305, 14, 1063, 17, "lineNumber"], [1305, 24, 1063, 17], [1306, 14, 1063, 17, "columnNumber"], [1306, 26, 1063, 17], [1307, 12, 1063, 17], [1307, 19, 1064, 32], [1307, 20, 1064, 33], [1307, 35, 1065, 14], [1307, 39, 1065, 14, "_jsxDevRuntime"], [1307, 53, 1065, 14], [1307, 54, 1065, 14, "jsxDEV"], [1307, 60, 1065, 14], [1307, 62, 1065, 15, "_Text"], [1307, 67, 1065, 15], [1307, 68, 1065, 15, "default"], [1307, 75, 1065, 19], [1308, 14, 1065, 20, "style"], [1308, 19, 1065, 25], [1308, 21, 1065, 27, "styles"], [1308, 27, 1065, 33], [1308, 28, 1065, 34, "privacyNote"], [1308, 39, 1065, 46], [1309, 14, 1065, 46, "children"], [1309, 22, 1065, 46], [1309, 24, 1065, 47], [1310, 12, 1067, 14], [1311, 14, 1067, 14, "fileName"], [1311, 22, 1067, 14], [1311, 24, 1067, 14, "_jsxFileName"], [1311, 36, 1067, 14], [1312, 14, 1067, 14, "lineNumber"], [1312, 24, 1067, 14], [1313, 14, 1067, 14, "columnNumber"], [1313, 26, 1067, 14], [1314, 12, 1067, 14], [1314, 19, 1067, 20], [1314, 20, 1067, 21], [1315, 10, 1067, 21], [1316, 12, 1067, 21, "fileName"], [1316, 20, 1067, 21], [1316, 22, 1067, 21, "_jsxFileName"], [1316, 34, 1067, 21], [1317, 12, 1067, 21, "lineNumber"], [1317, 22, 1067, 21], [1318, 12, 1067, 21, "columnNumber"], [1318, 24, 1067, 21], [1319, 10, 1067, 21], [1319, 17, 1068, 18], [1319, 18, 1068, 19], [1320, 8, 1068, 19], [1320, 23, 1069, 12], [1320, 24, 1070, 9], [1321, 6, 1070, 9], [1322, 8, 1070, 9, "fileName"], [1322, 16, 1070, 9], [1322, 18, 1070, 9, "_jsxFileName"], [1322, 30, 1070, 9], [1323, 8, 1070, 9, "lineNumber"], [1323, 18, 1070, 9], [1324, 8, 1070, 9, "columnNumber"], [1324, 20, 1070, 9], [1325, 6, 1070, 9], [1325, 13, 1071, 12], [1325, 14, 1071, 13], [1325, 29, 1073, 6], [1325, 33, 1073, 6, "_jsxDevRuntime"], [1325, 47, 1073, 6], [1325, 48, 1073, 6, "jsxDEV"], [1325, 54, 1073, 6], [1325, 56, 1073, 7, "_Modal"], [1325, 62, 1073, 7], [1325, 63, 1073, 7, "default"], [1325, 70, 1073, 12], [1326, 8, 1074, 8, "visible"], [1326, 15, 1074, 15], [1326, 17, 1074, 17, "processingState"], [1326, 32, 1074, 32], [1326, 37, 1074, 37], [1326, 43, 1074, 43], [1326, 47, 1074, 47, "processingState"], [1326, 62, 1074, 62], [1326, 67, 1074, 67], [1326, 74, 1074, 75], [1327, 8, 1075, 8, "transparent"], [1327, 19, 1075, 19], [1328, 8, 1076, 8, "animationType"], [1328, 21, 1076, 21], [1328, 23, 1076, 22], [1328, 29, 1076, 28], [1329, 8, 1076, 28, "children"], [1329, 16, 1076, 28], [1329, 31, 1078, 8], [1329, 35, 1078, 8, "_jsxDevRuntime"], [1329, 49, 1078, 8], [1329, 50, 1078, 8, "jsxDEV"], [1329, 56, 1078, 8], [1329, 58, 1078, 9, "_View"], [1329, 63, 1078, 9], [1329, 64, 1078, 9, "default"], [1329, 71, 1078, 13], [1330, 10, 1078, 14, "style"], [1330, 15, 1078, 19], [1330, 17, 1078, 21, "styles"], [1330, 23, 1078, 27], [1330, 24, 1078, 28, "processingModal"], [1330, 39, 1078, 44], [1331, 10, 1078, 44, "children"], [1331, 18, 1078, 44], [1331, 33, 1079, 10], [1331, 37, 1079, 10, "_jsxDevRuntime"], [1331, 51, 1079, 10], [1331, 52, 1079, 10, "jsxDEV"], [1331, 58, 1079, 10], [1331, 60, 1079, 11, "_View"], [1331, 65, 1079, 11], [1331, 66, 1079, 11, "default"], [1331, 73, 1079, 15], [1332, 12, 1079, 16, "style"], [1332, 17, 1079, 21], [1332, 19, 1079, 23, "styles"], [1332, 25, 1079, 29], [1332, 26, 1079, 30, "processingContent"], [1332, 43, 1079, 48], [1333, 12, 1079, 48, "children"], [1333, 20, 1079, 48], [1333, 36, 1080, 12], [1333, 40, 1080, 12, "_jsxDevRuntime"], [1333, 54, 1080, 12], [1333, 55, 1080, 12, "jsxDEV"], [1333, 61, 1080, 12], [1333, 63, 1080, 13, "_ActivityIndicator"], [1333, 81, 1080, 13], [1333, 82, 1080, 13, "default"], [1333, 89, 1080, 30], [1334, 14, 1080, 31, "size"], [1334, 18, 1080, 35], [1334, 20, 1080, 36], [1334, 27, 1080, 43], [1335, 14, 1080, 44, "color"], [1335, 19, 1080, 49], [1335, 21, 1080, 50], [1336, 12, 1080, 59], [1337, 14, 1080, 59, "fileName"], [1337, 22, 1080, 59], [1337, 24, 1080, 59, "_jsxFileName"], [1337, 36, 1080, 59], [1338, 14, 1080, 59, "lineNumber"], [1338, 24, 1080, 59], [1339, 14, 1080, 59, "columnNumber"], [1339, 26, 1080, 59], [1340, 12, 1080, 59], [1340, 19, 1080, 61], [1340, 20, 1080, 62], [1340, 35, 1082, 12], [1340, 39, 1082, 12, "_jsxDevRuntime"], [1340, 53, 1082, 12], [1340, 54, 1082, 12, "jsxDEV"], [1340, 60, 1082, 12], [1340, 62, 1082, 13, "_Text"], [1340, 67, 1082, 13], [1340, 68, 1082, 13, "default"], [1340, 75, 1082, 17], [1341, 14, 1082, 18, "style"], [1341, 19, 1082, 23], [1341, 21, 1082, 25, "styles"], [1341, 27, 1082, 31], [1341, 28, 1082, 32, "processingTitle"], [1341, 43, 1082, 48], [1342, 14, 1082, 48, "children"], [1342, 22, 1082, 48], [1342, 25, 1083, 15, "processingState"], [1342, 40, 1083, 30], [1342, 45, 1083, 35], [1342, 56, 1083, 46], [1342, 60, 1083, 50], [1342, 80, 1083, 70], [1342, 82, 1084, 15, "processingState"], [1342, 97, 1084, 30], [1342, 102, 1084, 35], [1342, 113, 1084, 46], [1342, 117, 1084, 50], [1342, 146, 1084, 79], [1342, 148, 1085, 15, "processingState"], [1342, 163, 1085, 30], [1342, 168, 1085, 35], [1342, 180, 1085, 47], [1342, 184, 1085, 51], [1342, 216, 1085, 83], [1342, 218, 1086, 15, "processingState"], [1342, 233, 1086, 30], [1342, 238, 1086, 35], [1342, 249, 1086, 46], [1342, 253, 1086, 50], [1342, 275, 1086, 72], [1343, 12, 1086, 72], [1344, 14, 1086, 72, "fileName"], [1344, 22, 1086, 72], [1344, 24, 1086, 72, "_jsxFileName"], [1344, 36, 1086, 72], [1345, 14, 1086, 72, "lineNumber"], [1345, 24, 1086, 72], [1346, 14, 1086, 72, "columnNumber"], [1346, 26, 1086, 72], [1347, 12, 1086, 72], [1347, 19, 1087, 18], [1347, 20, 1087, 19], [1347, 35, 1088, 12], [1347, 39, 1088, 12, "_jsxDevRuntime"], [1347, 53, 1088, 12], [1347, 54, 1088, 12, "jsxDEV"], [1347, 60, 1088, 12], [1347, 62, 1088, 13, "_View"], [1347, 67, 1088, 13], [1347, 68, 1088, 13, "default"], [1347, 75, 1088, 17], [1348, 14, 1088, 18, "style"], [1348, 19, 1088, 23], [1348, 21, 1088, 25, "styles"], [1348, 27, 1088, 31], [1348, 28, 1088, 32, "progressBar"], [1348, 39, 1088, 44], [1349, 14, 1088, 44, "children"], [1349, 22, 1088, 44], [1349, 37, 1089, 14], [1349, 41, 1089, 14, "_jsxDevRuntime"], [1349, 55, 1089, 14], [1349, 56, 1089, 14, "jsxDEV"], [1349, 62, 1089, 14], [1349, 64, 1089, 15, "_View"], [1349, 69, 1089, 15], [1349, 70, 1089, 15, "default"], [1349, 77, 1089, 19], [1350, 16, 1090, 16, "style"], [1350, 21, 1090, 21], [1350, 23, 1090, 23], [1350, 24, 1091, 18, "styles"], [1350, 30, 1091, 24], [1350, 31, 1091, 25, "progressFill"], [1350, 43, 1091, 37], [1350, 45, 1092, 18], [1351, 18, 1092, 20, "width"], [1351, 23, 1092, 25], [1351, 25, 1092, 27], [1351, 28, 1092, 30, "processingProgress"], [1351, 46, 1092, 48], [1352, 16, 1092, 52], [1352, 17, 1092, 53], [1353, 14, 1093, 18], [1354, 16, 1093, 18, "fileName"], [1354, 24, 1093, 18], [1354, 26, 1093, 18, "_jsxFileName"], [1354, 38, 1093, 18], [1355, 16, 1093, 18, "lineNumber"], [1355, 26, 1093, 18], [1356, 16, 1093, 18, "columnNumber"], [1356, 28, 1093, 18], [1357, 14, 1093, 18], [1357, 21, 1094, 15], [1358, 12, 1094, 16], [1359, 14, 1094, 16, "fileName"], [1359, 22, 1094, 16], [1359, 24, 1094, 16, "_jsxFileName"], [1359, 36, 1094, 16], [1360, 14, 1094, 16, "lineNumber"], [1360, 24, 1094, 16], [1361, 14, 1094, 16, "columnNumber"], [1361, 26, 1094, 16], [1362, 12, 1094, 16], [1362, 19, 1095, 18], [1362, 20, 1095, 19], [1362, 35, 1096, 12], [1362, 39, 1096, 12, "_jsxDevRuntime"], [1362, 53, 1096, 12], [1362, 54, 1096, 12, "jsxDEV"], [1362, 60, 1096, 12], [1362, 62, 1096, 13, "_Text"], [1362, 67, 1096, 13], [1362, 68, 1096, 13, "default"], [1362, 75, 1096, 17], [1363, 14, 1096, 18, "style"], [1363, 19, 1096, 23], [1363, 21, 1096, 25, "styles"], [1363, 27, 1096, 31], [1363, 28, 1096, 32, "processingDescription"], [1363, 49, 1096, 54], [1364, 14, 1096, 54, "children"], [1364, 22, 1096, 54], [1364, 25, 1097, 15, "processingState"], [1364, 40, 1097, 30], [1364, 45, 1097, 35], [1364, 56, 1097, 46], [1364, 60, 1097, 50], [1364, 89, 1097, 79], [1364, 91, 1098, 15, "processingState"], [1364, 106, 1098, 30], [1364, 111, 1098, 35], [1364, 122, 1098, 46], [1364, 126, 1098, 50], [1364, 164, 1098, 88], [1364, 166, 1099, 15, "processingState"], [1364, 181, 1099, 30], [1364, 186, 1099, 35], [1364, 198, 1099, 47], [1364, 202, 1099, 51], [1364, 247, 1099, 96], [1364, 249, 1100, 15, "processingState"], [1364, 264, 1100, 30], [1364, 269, 1100, 35], [1364, 280, 1100, 46], [1364, 284, 1100, 50], [1364, 325, 1100, 91], [1365, 12, 1100, 91], [1366, 14, 1100, 91, "fileName"], [1366, 22, 1100, 91], [1366, 24, 1100, 91, "_jsxFileName"], [1366, 36, 1100, 91], [1367, 14, 1100, 91, "lineNumber"], [1367, 24, 1100, 91], [1368, 14, 1100, 91, "columnNumber"], [1368, 26, 1100, 91], [1369, 12, 1100, 91], [1369, 19, 1101, 18], [1369, 20, 1101, 19], [1369, 22, 1102, 13, "processingState"], [1369, 37, 1102, 28], [1369, 42, 1102, 33], [1369, 53, 1102, 44], [1369, 70, 1103, 14], [1369, 74, 1103, 14, "_jsxDevRuntime"], [1369, 88, 1103, 14], [1369, 89, 1103, 14, "jsxDEV"], [1369, 95, 1103, 14], [1369, 97, 1103, 15, "_lucideReactNative"], [1369, 115, 1103, 15], [1369, 116, 1103, 15, "CheckCircle"], [1369, 127, 1103, 26], [1370, 14, 1103, 27, "size"], [1370, 18, 1103, 31], [1370, 20, 1103, 33], [1370, 22, 1103, 36], [1371, 14, 1103, 37, "color"], [1371, 19, 1103, 42], [1371, 21, 1103, 43], [1371, 30, 1103, 52], [1372, 14, 1103, 53, "style"], [1372, 19, 1103, 58], [1372, 21, 1103, 60, "styles"], [1372, 27, 1103, 66], [1372, 28, 1103, 67, "successIcon"], [1373, 12, 1103, 79], [1374, 14, 1103, 79, "fileName"], [1374, 22, 1103, 79], [1374, 24, 1103, 79, "_jsxFileName"], [1374, 36, 1103, 79], [1375, 14, 1103, 79, "lineNumber"], [1375, 24, 1103, 79], [1376, 14, 1103, 79, "columnNumber"], [1376, 26, 1103, 79], [1377, 12, 1103, 79], [1377, 19, 1103, 81], [1377, 20, 1104, 13], [1378, 10, 1104, 13], [1379, 12, 1104, 13, "fileName"], [1379, 20, 1104, 13], [1379, 22, 1104, 13, "_jsxFileName"], [1379, 34, 1104, 13], [1380, 12, 1104, 13, "lineNumber"], [1380, 22, 1104, 13], [1381, 12, 1104, 13, "columnNumber"], [1381, 24, 1104, 13], [1382, 10, 1104, 13], [1382, 17, 1105, 16], [1383, 8, 1105, 17], [1384, 10, 1105, 17, "fileName"], [1384, 18, 1105, 17], [1384, 20, 1105, 17, "_jsxFileName"], [1384, 32, 1105, 17], [1385, 10, 1105, 17, "lineNumber"], [1385, 20, 1105, 17], [1386, 10, 1105, 17, "columnNumber"], [1386, 22, 1105, 17], [1387, 8, 1105, 17], [1387, 15, 1106, 14], [1388, 6, 1106, 15], [1389, 8, 1106, 15, "fileName"], [1389, 16, 1106, 15], [1389, 18, 1106, 15, "_jsxFileName"], [1389, 30, 1106, 15], [1390, 8, 1106, 15, "lineNumber"], [1390, 18, 1106, 15], [1391, 8, 1106, 15, "columnNumber"], [1391, 20, 1106, 15], [1392, 6, 1106, 15], [1392, 13, 1107, 13], [1392, 14, 1107, 14], [1392, 29, 1109, 6], [1392, 33, 1109, 6, "_jsxDevRuntime"], [1392, 47, 1109, 6], [1392, 48, 1109, 6, "jsxDEV"], [1392, 54, 1109, 6], [1392, 56, 1109, 7, "_Modal"], [1392, 62, 1109, 7], [1392, 63, 1109, 7, "default"], [1392, 70, 1109, 12], [1393, 8, 1110, 8, "visible"], [1393, 15, 1110, 15], [1393, 17, 1110, 17, "processingState"], [1393, 32, 1110, 32], [1393, 37, 1110, 37], [1393, 44, 1110, 45], [1394, 8, 1111, 8, "transparent"], [1394, 19, 1111, 19], [1395, 8, 1112, 8, "animationType"], [1395, 21, 1112, 21], [1395, 23, 1112, 22], [1395, 29, 1112, 28], [1396, 8, 1112, 28, "children"], [1396, 16, 1112, 28], [1396, 31, 1114, 8], [1396, 35, 1114, 8, "_jsxDevRuntime"], [1396, 49, 1114, 8], [1396, 50, 1114, 8, "jsxDEV"], [1396, 56, 1114, 8], [1396, 58, 1114, 9, "_View"], [1396, 63, 1114, 9], [1396, 64, 1114, 9, "default"], [1396, 71, 1114, 13], [1397, 10, 1114, 14, "style"], [1397, 15, 1114, 19], [1397, 17, 1114, 21, "styles"], [1397, 23, 1114, 27], [1397, 24, 1114, 28, "processingModal"], [1397, 39, 1114, 44], [1398, 10, 1114, 44, "children"], [1398, 18, 1114, 44], [1398, 33, 1115, 10], [1398, 37, 1115, 10, "_jsxDevRuntime"], [1398, 51, 1115, 10], [1398, 52, 1115, 10, "jsxDEV"], [1398, 58, 1115, 10], [1398, 60, 1115, 11, "_View"], [1398, 65, 1115, 11], [1398, 66, 1115, 11, "default"], [1398, 73, 1115, 15], [1399, 12, 1115, 16, "style"], [1399, 17, 1115, 21], [1399, 19, 1115, 23, "styles"], [1399, 25, 1115, 29], [1399, 26, 1115, 30, "errorContent"], [1399, 38, 1115, 43], [1400, 12, 1115, 43, "children"], [1400, 20, 1115, 43], [1400, 36, 1116, 12], [1400, 40, 1116, 12, "_jsxDevRuntime"], [1400, 54, 1116, 12], [1400, 55, 1116, 12, "jsxDEV"], [1400, 61, 1116, 12], [1400, 63, 1116, 13, "_lucideReactNative"], [1400, 81, 1116, 13], [1400, 82, 1116, 13, "X"], [1400, 83, 1116, 14], [1401, 14, 1116, 15, "size"], [1401, 18, 1116, 19], [1401, 20, 1116, 21], [1401, 22, 1116, 24], [1402, 14, 1116, 25, "color"], [1402, 19, 1116, 30], [1402, 21, 1116, 31], [1403, 12, 1116, 40], [1404, 14, 1116, 40, "fileName"], [1404, 22, 1116, 40], [1404, 24, 1116, 40, "_jsxFileName"], [1404, 36, 1116, 40], [1405, 14, 1116, 40, "lineNumber"], [1405, 24, 1116, 40], [1406, 14, 1116, 40, "columnNumber"], [1406, 26, 1116, 40], [1407, 12, 1116, 40], [1407, 19, 1116, 42], [1407, 20, 1116, 43], [1407, 35, 1117, 12], [1407, 39, 1117, 12, "_jsxDevRuntime"], [1407, 53, 1117, 12], [1407, 54, 1117, 12, "jsxDEV"], [1407, 60, 1117, 12], [1407, 62, 1117, 13, "_Text"], [1407, 67, 1117, 13], [1407, 68, 1117, 13, "default"], [1407, 75, 1117, 17], [1408, 14, 1117, 18, "style"], [1408, 19, 1117, 23], [1408, 21, 1117, 25, "styles"], [1408, 27, 1117, 31], [1408, 28, 1117, 32, "errorTitle"], [1408, 38, 1117, 43], [1409, 14, 1117, 43, "children"], [1409, 22, 1117, 43], [1409, 24, 1117, 44], [1410, 12, 1117, 61], [1411, 14, 1117, 61, "fileName"], [1411, 22, 1117, 61], [1411, 24, 1117, 61, "_jsxFileName"], [1411, 36, 1117, 61], [1412, 14, 1117, 61, "lineNumber"], [1412, 24, 1117, 61], [1413, 14, 1117, 61, "columnNumber"], [1413, 26, 1117, 61], [1414, 12, 1117, 61], [1414, 19, 1117, 67], [1414, 20, 1117, 68], [1414, 35, 1118, 12], [1414, 39, 1118, 12, "_jsxDevRuntime"], [1414, 53, 1118, 12], [1414, 54, 1118, 12, "jsxDEV"], [1414, 60, 1118, 12], [1414, 62, 1118, 13, "_Text"], [1414, 67, 1118, 13], [1414, 68, 1118, 13, "default"], [1414, 75, 1118, 17], [1415, 14, 1118, 18, "style"], [1415, 19, 1118, 23], [1415, 21, 1118, 25, "styles"], [1415, 27, 1118, 31], [1415, 28, 1118, 32, "errorMessage"], [1415, 40, 1118, 45], [1416, 14, 1118, 45, "children"], [1416, 22, 1118, 45], [1416, 24, 1118, 47, "errorMessage"], [1417, 12, 1118, 59], [1418, 14, 1118, 59, "fileName"], [1418, 22, 1118, 59], [1418, 24, 1118, 59, "_jsxFileName"], [1418, 36, 1118, 59], [1419, 14, 1118, 59, "lineNumber"], [1419, 24, 1118, 59], [1420, 14, 1118, 59, "columnNumber"], [1420, 26, 1118, 59], [1421, 12, 1118, 59], [1421, 19, 1118, 66], [1421, 20, 1118, 67], [1421, 35, 1119, 12], [1421, 39, 1119, 12, "_jsxDevRuntime"], [1421, 53, 1119, 12], [1421, 54, 1119, 12, "jsxDEV"], [1421, 60, 1119, 12], [1421, 62, 1119, 13, "_TouchableOpacity"], [1421, 79, 1119, 13], [1421, 80, 1119, 13, "default"], [1421, 87, 1119, 29], [1422, 14, 1120, 14, "onPress"], [1422, 21, 1120, 21], [1422, 23, 1120, 23, "retryCapture"], [1422, 35, 1120, 36], [1423, 14, 1121, 14, "style"], [1423, 19, 1121, 19], [1423, 21, 1121, 21, "styles"], [1423, 27, 1121, 27], [1423, 28, 1121, 28, "primaryButton"], [1423, 41, 1121, 42], [1424, 14, 1121, 42, "children"], [1424, 22, 1121, 42], [1424, 37, 1123, 14], [1424, 41, 1123, 14, "_jsxDevRuntime"], [1424, 55, 1123, 14], [1424, 56, 1123, 14, "jsxDEV"], [1424, 62, 1123, 14], [1424, 64, 1123, 15, "_Text"], [1424, 69, 1123, 15], [1424, 70, 1123, 15, "default"], [1424, 77, 1123, 19], [1425, 16, 1123, 20, "style"], [1425, 21, 1123, 25], [1425, 23, 1123, 27, "styles"], [1425, 29, 1123, 33], [1425, 30, 1123, 34, "primaryButtonText"], [1425, 47, 1123, 52], [1426, 16, 1123, 52, "children"], [1426, 24, 1123, 52], [1426, 26, 1123, 53], [1427, 14, 1123, 62], [1428, 16, 1123, 62, "fileName"], [1428, 24, 1123, 62], [1428, 26, 1123, 62, "_jsxFileName"], [1428, 38, 1123, 62], [1429, 16, 1123, 62, "lineNumber"], [1429, 26, 1123, 62], [1430, 16, 1123, 62, "columnNumber"], [1430, 28, 1123, 62], [1431, 14, 1123, 62], [1431, 21, 1123, 68], [1432, 12, 1123, 69], [1433, 14, 1123, 69, "fileName"], [1433, 22, 1123, 69], [1433, 24, 1123, 69, "_jsxFileName"], [1433, 36, 1123, 69], [1434, 14, 1123, 69, "lineNumber"], [1434, 24, 1123, 69], [1435, 14, 1123, 69, "columnNumber"], [1435, 26, 1123, 69], [1436, 12, 1123, 69], [1436, 19, 1124, 30], [1436, 20, 1124, 31], [1436, 35, 1125, 12], [1436, 39, 1125, 12, "_jsxDevRuntime"], [1436, 53, 1125, 12], [1436, 54, 1125, 12, "jsxDEV"], [1436, 60, 1125, 12], [1436, 62, 1125, 13, "_TouchableOpacity"], [1436, 79, 1125, 13], [1436, 80, 1125, 13, "default"], [1436, 87, 1125, 29], [1437, 14, 1126, 14, "onPress"], [1437, 21, 1126, 21], [1437, 23, 1126, 23, "onCancel"], [1437, 31, 1126, 32], [1438, 14, 1127, 14, "style"], [1438, 19, 1127, 19], [1438, 21, 1127, 21, "styles"], [1438, 27, 1127, 27], [1438, 28, 1127, 28, "secondaryButton"], [1438, 43, 1127, 44], [1439, 14, 1127, 44, "children"], [1439, 22, 1127, 44], [1439, 37, 1129, 14], [1439, 41, 1129, 14, "_jsxDevRuntime"], [1439, 55, 1129, 14], [1439, 56, 1129, 14, "jsxDEV"], [1439, 62, 1129, 14], [1439, 64, 1129, 15, "_Text"], [1439, 69, 1129, 15], [1439, 70, 1129, 15, "default"], [1439, 77, 1129, 19], [1440, 16, 1129, 20, "style"], [1440, 21, 1129, 25], [1440, 23, 1129, 27, "styles"], [1440, 29, 1129, 33], [1440, 30, 1129, 34, "secondaryButtonText"], [1440, 49, 1129, 54], [1441, 16, 1129, 54, "children"], [1441, 24, 1129, 54], [1441, 26, 1129, 55], [1442, 14, 1129, 61], [1443, 16, 1129, 61, "fileName"], [1443, 24, 1129, 61], [1443, 26, 1129, 61, "_jsxFileName"], [1443, 38, 1129, 61], [1444, 16, 1129, 61, "lineNumber"], [1444, 26, 1129, 61], [1445, 16, 1129, 61, "columnNumber"], [1445, 28, 1129, 61], [1446, 14, 1129, 61], [1446, 21, 1129, 67], [1447, 12, 1129, 68], [1448, 14, 1129, 68, "fileName"], [1448, 22, 1129, 68], [1448, 24, 1129, 68, "_jsxFileName"], [1448, 36, 1129, 68], [1449, 14, 1129, 68, "lineNumber"], [1449, 24, 1129, 68], [1450, 14, 1129, 68, "columnNumber"], [1450, 26, 1129, 68], [1451, 12, 1129, 68], [1451, 19, 1130, 30], [1451, 20, 1130, 31], [1452, 10, 1130, 31], [1453, 12, 1130, 31, "fileName"], [1453, 20, 1130, 31], [1453, 22, 1130, 31, "_jsxFileName"], [1453, 34, 1130, 31], [1454, 12, 1130, 31, "lineNumber"], [1454, 22, 1130, 31], [1455, 12, 1130, 31, "columnNumber"], [1455, 24, 1130, 31], [1456, 10, 1130, 31], [1456, 17, 1131, 16], [1457, 8, 1131, 17], [1458, 10, 1131, 17, "fileName"], [1458, 18, 1131, 17], [1458, 20, 1131, 17, "_jsxFileName"], [1458, 32, 1131, 17], [1459, 10, 1131, 17, "lineNumber"], [1459, 20, 1131, 17], [1460, 10, 1131, 17, "columnNumber"], [1460, 22, 1131, 17], [1461, 8, 1131, 17], [1461, 15, 1132, 14], [1462, 6, 1132, 15], [1463, 8, 1132, 15, "fileName"], [1463, 16, 1132, 15], [1463, 18, 1132, 15, "_jsxFileName"], [1463, 30, 1132, 15], [1464, 8, 1132, 15, "lineNumber"], [1464, 18, 1132, 15], [1465, 8, 1132, 15, "columnNumber"], [1465, 20, 1132, 15], [1466, 6, 1132, 15], [1466, 13, 1133, 13], [1466, 14, 1133, 14], [1467, 4, 1133, 14], [1468, 6, 1133, 14, "fileName"], [1468, 14, 1133, 14], [1468, 16, 1133, 14, "_jsxFileName"], [1468, 28, 1133, 14], [1469, 6, 1133, 14, "lineNumber"], [1469, 16, 1133, 14], [1470, 6, 1133, 14, "columnNumber"], [1470, 18, 1133, 14], [1471, 4, 1133, 14], [1471, 11, 1134, 10], [1471, 12, 1134, 11], [1472, 2, 1136, 0], [1473, 2, 1136, 1, "_s"], [1473, 4, 1136, 1], [1473, 5, 51, 24, "EchoCameraWeb"], [1473, 18, 51, 37], [1474, 4, 51, 37], [1474, 12, 58, 42, "useCameraPermissions"], [1474, 44, 58, 62], [1474, 46, 72, 19, "useUpload"], [1474, 64, 72, 28], [1475, 2, 72, 28], [1476, 2, 72, 28, "_c"], [1476, 4, 72, 28], [1476, 7, 51, 24, "EchoCameraWeb"], [1476, 20, 51, 37], [1477, 2, 1137, 0], [1477, 8, 1137, 6, "styles"], [1477, 14, 1137, 12], [1477, 17, 1137, 15, "StyleSheet"], [1477, 36, 1137, 25], [1477, 37, 1137, 26, "create"], [1477, 43, 1137, 32], [1477, 44, 1137, 33], [1478, 4, 1138, 2, "container"], [1478, 13, 1138, 11], [1478, 15, 1138, 13], [1479, 6, 1139, 4, "flex"], [1479, 10, 1139, 8], [1479, 12, 1139, 10], [1479, 13, 1139, 11], [1480, 6, 1140, 4, "backgroundColor"], [1480, 21, 1140, 19], [1480, 23, 1140, 21], [1481, 4, 1141, 2], [1481, 5, 1141, 3], [1482, 4, 1142, 2, "cameraContainer"], [1482, 19, 1142, 17], [1482, 21, 1142, 19], [1483, 6, 1143, 4, "flex"], [1483, 10, 1143, 8], [1483, 12, 1143, 10], [1483, 13, 1143, 11], [1484, 6, 1144, 4, "max<PERSON><PERSON><PERSON>"], [1484, 14, 1144, 12], [1484, 16, 1144, 14], [1484, 19, 1144, 17], [1485, 6, 1145, 4, "alignSelf"], [1485, 15, 1145, 13], [1485, 17, 1145, 15], [1485, 25, 1145, 23], [1486, 6, 1146, 4, "width"], [1486, 11, 1146, 9], [1486, 13, 1146, 11], [1487, 4, 1147, 2], [1487, 5, 1147, 3], [1488, 4, 1148, 2, "camera"], [1488, 10, 1148, 8], [1488, 12, 1148, 10], [1489, 6, 1149, 4, "flex"], [1489, 10, 1149, 8], [1489, 12, 1149, 10], [1490, 4, 1150, 2], [1490, 5, 1150, 3], [1491, 4, 1151, 2, "headerOverlay"], [1491, 17, 1151, 15], [1491, 19, 1151, 17], [1492, 6, 1152, 4, "position"], [1492, 14, 1152, 12], [1492, 16, 1152, 14], [1492, 26, 1152, 24], [1493, 6, 1153, 4, "top"], [1493, 9, 1153, 7], [1493, 11, 1153, 9], [1493, 12, 1153, 10], [1494, 6, 1154, 4, "left"], [1494, 10, 1154, 8], [1494, 12, 1154, 10], [1494, 13, 1154, 11], [1495, 6, 1155, 4, "right"], [1495, 11, 1155, 9], [1495, 13, 1155, 11], [1495, 14, 1155, 12], [1496, 6, 1156, 4, "backgroundColor"], [1496, 21, 1156, 19], [1496, 23, 1156, 21], [1496, 36, 1156, 34], [1497, 6, 1157, 4, "paddingTop"], [1497, 16, 1157, 14], [1497, 18, 1157, 16], [1497, 20, 1157, 18], [1498, 6, 1158, 4, "paddingHorizontal"], [1498, 23, 1158, 21], [1498, 25, 1158, 23], [1498, 27, 1158, 25], [1499, 6, 1159, 4, "paddingBottom"], [1499, 19, 1159, 17], [1499, 21, 1159, 19], [1500, 4, 1160, 2], [1500, 5, 1160, 3], [1501, 4, 1161, 2, "headerContent"], [1501, 17, 1161, 15], [1501, 19, 1161, 17], [1502, 6, 1162, 4, "flexDirection"], [1502, 19, 1162, 17], [1502, 21, 1162, 19], [1502, 26, 1162, 24], [1503, 6, 1163, 4, "justifyContent"], [1503, 20, 1163, 18], [1503, 22, 1163, 20], [1503, 37, 1163, 35], [1504, 6, 1164, 4, "alignItems"], [1504, 16, 1164, 14], [1504, 18, 1164, 16], [1505, 4, 1165, 2], [1505, 5, 1165, 3], [1506, 4, 1166, 2, "headerLeft"], [1506, 14, 1166, 12], [1506, 16, 1166, 14], [1507, 6, 1167, 4, "flex"], [1507, 10, 1167, 8], [1507, 12, 1167, 10], [1508, 4, 1168, 2], [1508, 5, 1168, 3], [1509, 4, 1169, 2, "headerTitle"], [1509, 15, 1169, 13], [1509, 17, 1169, 15], [1510, 6, 1170, 4, "fontSize"], [1510, 14, 1170, 12], [1510, 16, 1170, 14], [1510, 18, 1170, 16], [1511, 6, 1171, 4, "fontWeight"], [1511, 16, 1171, 14], [1511, 18, 1171, 16], [1511, 23, 1171, 21], [1512, 6, 1172, 4, "color"], [1512, 11, 1172, 9], [1512, 13, 1172, 11], [1512, 19, 1172, 17], [1513, 6, 1173, 4, "marginBottom"], [1513, 18, 1173, 16], [1513, 20, 1173, 18], [1514, 4, 1174, 2], [1514, 5, 1174, 3], [1515, 4, 1175, 2, "subtitleRow"], [1515, 15, 1175, 13], [1515, 17, 1175, 15], [1516, 6, 1176, 4, "flexDirection"], [1516, 19, 1176, 17], [1516, 21, 1176, 19], [1516, 26, 1176, 24], [1517, 6, 1177, 4, "alignItems"], [1517, 16, 1177, 14], [1517, 18, 1177, 16], [1517, 26, 1177, 24], [1518, 6, 1178, 4, "marginBottom"], [1518, 18, 1178, 16], [1518, 20, 1178, 18], [1519, 4, 1179, 2], [1519, 5, 1179, 3], [1520, 4, 1180, 2, "webIcon"], [1520, 11, 1180, 9], [1520, 13, 1180, 11], [1521, 6, 1181, 4, "fontSize"], [1521, 14, 1181, 12], [1521, 16, 1181, 14], [1521, 18, 1181, 16], [1522, 6, 1182, 4, "marginRight"], [1522, 17, 1182, 15], [1522, 19, 1182, 17], [1523, 4, 1183, 2], [1523, 5, 1183, 3], [1524, 4, 1184, 2, "headerSubtitle"], [1524, 18, 1184, 16], [1524, 20, 1184, 18], [1525, 6, 1185, 4, "fontSize"], [1525, 14, 1185, 12], [1525, 16, 1185, 14], [1525, 18, 1185, 16], [1526, 6, 1186, 4, "color"], [1526, 11, 1186, 9], [1526, 13, 1186, 11], [1526, 19, 1186, 17], [1527, 6, 1187, 4, "opacity"], [1527, 13, 1187, 11], [1527, 15, 1187, 13], [1528, 4, 1188, 2], [1528, 5, 1188, 3], [1529, 4, 1189, 2, "challengeRow"], [1529, 16, 1189, 14], [1529, 18, 1189, 16], [1530, 6, 1190, 4, "flexDirection"], [1530, 19, 1190, 17], [1530, 21, 1190, 19], [1530, 26, 1190, 24], [1531, 6, 1191, 4, "alignItems"], [1531, 16, 1191, 14], [1531, 18, 1191, 16], [1532, 4, 1192, 2], [1532, 5, 1192, 3], [1533, 4, 1193, 2, "challengeCode"], [1533, 17, 1193, 15], [1533, 19, 1193, 17], [1534, 6, 1194, 4, "fontSize"], [1534, 14, 1194, 12], [1534, 16, 1194, 14], [1534, 18, 1194, 16], [1535, 6, 1195, 4, "color"], [1535, 11, 1195, 9], [1535, 13, 1195, 11], [1535, 19, 1195, 17], [1536, 6, 1196, 4, "marginLeft"], [1536, 16, 1196, 14], [1536, 18, 1196, 16], [1536, 19, 1196, 17], [1537, 6, 1197, 4, "fontFamily"], [1537, 16, 1197, 14], [1537, 18, 1197, 16], [1538, 4, 1198, 2], [1538, 5, 1198, 3], [1539, 4, 1199, 2, "closeButton"], [1539, 15, 1199, 13], [1539, 17, 1199, 15], [1540, 6, 1200, 4, "padding"], [1540, 13, 1200, 11], [1540, 15, 1200, 13], [1541, 4, 1201, 2], [1541, 5, 1201, 3], [1542, 4, 1202, 2, "privacyNotice"], [1542, 17, 1202, 15], [1542, 19, 1202, 17], [1543, 6, 1203, 4, "position"], [1543, 14, 1203, 12], [1543, 16, 1203, 14], [1543, 26, 1203, 24], [1544, 6, 1204, 4, "top"], [1544, 9, 1204, 7], [1544, 11, 1204, 9], [1544, 14, 1204, 12], [1545, 6, 1205, 4, "left"], [1545, 10, 1205, 8], [1545, 12, 1205, 10], [1545, 14, 1205, 12], [1546, 6, 1206, 4, "right"], [1546, 11, 1206, 9], [1546, 13, 1206, 11], [1546, 15, 1206, 13], [1547, 6, 1207, 4, "backgroundColor"], [1547, 21, 1207, 19], [1547, 23, 1207, 21], [1547, 48, 1207, 46], [1548, 6, 1208, 4, "borderRadius"], [1548, 18, 1208, 16], [1548, 20, 1208, 18], [1548, 21, 1208, 19], [1549, 6, 1209, 4, "padding"], [1549, 13, 1209, 11], [1549, 15, 1209, 13], [1549, 17, 1209, 15], [1550, 6, 1210, 4, "flexDirection"], [1550, 19, 1210, 17], [1550, 21, 1210, 19], [1550, 26, 1210, 24], [1551, 6, 1211, 4, "alignItems"], [1551, 16, 1211, 14], [1551, 18, 1211, 16], [1552, 4, 1212, 2], [1552, 5, 1212, 3], [1553, 4, 1213, 2, "privacyText"], [1553, 15, 1213, 13], [1553, 17, 1213, 15], [1554, 6, 1214, 4, "color"], [1554, 11, 1214, 9], [1554, 13, 1214, 11], [1554, 19, 1214, 17], [1555, 6, 1215, 4, "fontSize"], [1555, 14, 1215, 12], [1555, 16, 1215, 14], [1555, 18, 1215, 16], [1556, 6, 1216, 4, "marginLeft"], [1556, 16, 1216, 14], [1556, 18, 1216, 16], [1556, 19, 1216, 17], [1557, 6, 1217, 4, "flex"], [1557, 10, 1217, 8], [1557, 12, 1217, 10], [1558, 4, 1218, 2], [1558, 5, 1218, 3], [1559, 4, 1219, 2, "footer<PERSON><PERSON><PERSON>"], [1559, 17, 1219, 15], [1559, 19, 1219, 17], [1560, 6, 1220, 4, "position"], [1560, 14, 1220, 12], [1560, 16, 1220, 14], [1560, 26, 1220, 24], [1561, 6, 1221, 4, "bottom"], [1561, 12, 1221, 10], [1561, 14, 1221, 12], [1561, 15, 1221, 13], [1562, 6, 1222, 4, "left"], [1562, 10, 1222, 8], [1562, 12, 1222, 10], [1562, 13, 1222, 11], [1563, 6, 1223, 4, "right"], [1563, 11, 1223, 9], [1563, 13, 1223, 11], [1563, 14, 1223, 12], [1564, 6, 1224, 4, "backgroundColor"], [1564, 21, 1224, 19], [1564, 23, 1224, 21], [1564, 36, 1224, 34], [1565, 6, 1225, 4, "paddingBottom"], [1565, 19, 1225, 17], [1565, 21, 1225, 19], [1565, 23, 1225, 21], [1566, 6, 1226, 4, "paddingTop"], [1566, 16, 1226, 14], [1566, 18, 1226, 16], [1566, 20, 1226, 18], [1567, 6, 1227, 4, "alignItems"], [1567, 16, 1227, 14], [1567, 18, 1227, 16], [1568, 4, 1228, 2], [1568, 5, 1228, 3], [1569, 4, 1229, 2, "instruction"], [1569, 15, 1229, 13], [1569, 17, 1229, 15], [1570, 6, 1230, 4, "fontSize"], [1570, 14, 1230, 12], [1570, 16, 1230, 14], [1570, 18, 1230, 16], [1571, 6, 1231, 4, "color"], [1571, 11, 1231, 9], [1571, 13, 1231, 11], [1571, 19, 1231, 17], [1572, 6, 1232, 4, "marginBottom"], [1572, 18, 1232, 16], [1572, 20, 1232, 18], [1573, 4, 1233, 2], [1573, 5, 1233, 3], [1574, 4, 1234, 2, "shutterButton"], [1574, 17, 1234, 15], [1574, 19, 1234, 17], [1575, 6, 1235, 4, "width"], [1575, 11, 1235, 9], [1575, 13, 1235, 11], [1575, 15, 1235, 13], [1576, 6, 1236, 4, "height"], [1576, 12, 1236, 10], [1576, 14, 1236, 12], [1576, 16, 1236, 14], [1577, 6, 1237, 4, "borderRadius"], [1577, 18, 1237, 16], [1577, 20, 1237, 18], [1577, 22, 1237, 20], [1578, 6, 1238, 4, "backgroundColor"], [1578, 21, 1238, 19], [1578, 23, 1238, 21], [1578, 29, 1238, 27], [1579, 6, 1239, 4, "justifyContent"], [1579, 20, 1239, 18], [1579, 22, 1239, 20], [1579, 30, 1239, 28], [1580, 6, 1240, 4, "alignItems"], [1580, 16, 1240, 14], [1580, 18, 1240, 16], [1580, 26, 1240, 24], [1581, 6, 1241, 4, "marginBottom"], [1581, 18, 1241, 16], [1581, 20, 1241, 18], [1581, 22, 1241, 20], [1582, 6, 1242, 4], [1582, 9, 1242, 7, "Platform"], [1582, 26, 1242, 15], [1582, 27, 1242, 16, "select"], [1582, 33, 1242, 22], [1582, 34, 1242, 23], [1583, 8, 1243, 6, "ios"], [1583, 11, 1243, 9], [1583, 13, 1243, 11], [1584, 10, 1244, 8, "shadowColor"], [1584, 21, 1244, 19], [1584, 23, 1244, 21], [1584, 32, 1244, 30], [1585, 10, 1245, 8, "shadowOffset"], [1585, 22, 1245, 20], [1585, 24, 1245, 22], [1586, 12, 1245, 24, "width"], [1586, 17, 1245, 29], [1586, 19, 1245, 31], [1586, 20, 1245, 32], [1587, 12, 1245, 34, "height"], [1587, 18, 1245, 40], [1587, 20, 1245, 42], [1588, 10, 1245, 44], [1588, 11, 1245, 45], [1589, 10, 1246, 8, "shadowOpacity"], [1589, 23, 1246, 21], [1589, 25, 1246, 23], [1589, 28, 1246, 26], [1590, 10, 1247, 8, "shadowRadius"], [1590, 22, 1247, 20], [1590, 24, 1247, 22], [1591, 8, 1248, 6], [1591, 9, 1248, 7], [1592, 8, 1249, 6, "android"], [1592, 15, 1249, 13], [1592, 17, 1249, 15], [1593, 10, 1250, 8, "elevation"], [1593, 19, 1250, 17], [1593, 21, 1250, 19], [1594, 8, 1251, 6], [1594, 9, 1251, 7], [1595, 8, 1252, 6, "web"], [1595, 11, 1252, 9], [1595, 13, 1252, 11], [1596, 10, 1253, 8, "boxShadow"], [1596, 19, 1253, 17], [1596, 21, 1253, 19], [1597, 8, 1254, 6], [1598, 6, 1255, 4], [1598, 7, 1255, 5], [1599, 4, 1256, 2], [1599, 5, 1256, 3], [1600, 4, 1257, 2, "shutterButtonDisabled"], [1600, 25, 1257, 23], [1600, 27, 1257, 25], [1601, 6, 1258, 4, "opacity"], [1601, 13, 1258, 11], [1601, 15, 1258, 13], [1602, 4, 1259, 2], [1602, 5, 1259, 3], [1603, 4, 1260, 2, "shutterInner"], [1603, 16, 1260, 14], [1603, 18, 1260, 16], [1604, 6, 1261, 4, "width"], [1604, 11, 1261, 9], [1604, 13, 1261, 11], [1604, 15, 1261, 13], [1605, 6, 1262, 4, "height"], [1605, 12, 1262, 10], [1605, 14, 1262, 12], [1605, 16, 1262, 14], [1606, 6, 1263, 4, "borderRadius"], [1606, 18, 1263, 16], [1606, 20, 1263, 18], [1606, 22, 1263, 20], [1607, 6, 1264, 4, "backgroundColor"], [1607, 21, 1264, 19], [1607, 23, 1264, 21], [1607, 29, 1264, 27], [1608, 6, 1265, 4, "borderWidth"], [1608, 17, 1265, 15], [1608, 19, 1265, 17], [1608, 20, 1265, 18], [1609, 6, 1266, 4, "borderColor"], [1609, 17, 1266, 15], [1609, 19, 1266, 17], [1610, 4, 1267, 2], [1610, 5, 1267, 3], [1611, 4, 1268, 2, "privacyNote"], [1611, 15, 1268, 13], [1611, 17, 1268, 15], [1612, 6, 1269, 4, "fontSize"], [1612, 14, 1269, 12], [1612, 16, 1269, 14], [1612, 18, 1269, 16], [1613, 6, 1270, 4, "color"], [1613, 11, 1270, 9], [1613, 13, 1270, 11], [1614, 4, 1271, 2], [1614, 5, 1271, 3], [1615, 4, 1272, 2, "processingModal"], [1615, 19, 1272, 17], [1615, 21, 1272, 19], [1616, 6, 1273, 4, "flex"], [1616, 10, 1273, 8], [1616, 12, 1273, 10], [1616, 13, 1273, 11], [1617, 6, 1274, 4, "backgroundColor"], [1617, 21, 1274, 19], [1617, 23, 1274, 21], [1617, 43, 1274, 41], [1618, 6, 1275, 4, "justifyContent"], [1618, 20, 1275, 18], [1618, 22, 1275, 20], [1618, 30, 1275, 28], [1619, 6, 1276, 4, "alignItems"], [1619, 16, 1276, 14], [1619, 18, 1276, 16], [1620, 4, 1277, 2], [1620, 5, 1277, 3], [1621, 4, 1278, 2, "processingContent"], [1621, 21, 1278, 19], [1621, 23, 1278, 21], [1622, 6, 1279, 4, "backgroundColor"], [1622, 21, 1279, 19], [1622, 23, 1279, 21], [1622, 29, 1279, 27], [1623, 6, 1280, 4, "borderRadius"], [1623, 18, 1280, 16], [1623, 20, 1280, 18], [1623, 22, 1280, 20], [1624, 6, 1281, 4, "padding"], [1624, 13, 1281, 11], [1624, 15, 1281, 13], [1624, 17, 1281, 15], [1625, 6, 1282, 4, "width"], [1625, 11, 1282, 9], [1625, 13, 1282, 11], [1625, 18, 1282, 16], [1626, 6, 1283, 4, "max<PERSON><PERSON><PERSON>"], [1626, 14, 1283, 12], [1626, 16, 1283, 14], [1626, 19, 1283, 17], [1627, 6, 1284, 4, "alignItems"], [1627, 16, 1284, 14], [1627, 18, 1284, 16], [1628, 4, 1285, 2], [1628, 5, 1285, 3], [1629, 4, 1286, 2, "processingTitle"], [1629, 19, 1286, 17], [1629, 21, 1286, 19], [1630, 6, 1287, 4, "fontSize"], [1630, 14, 1287, 12], [1630, 16, 1287, 14], [1630, 18, 1287, 16], [1631, 6, 1288, 4, "fontWeight"], [1631, 16, 1288, 14], [1631, 18, 1288, 16], [1631, 23, 1288, 21], [1632, 6, 1289, 4, "color"], [1632, 11, 1289, 9], [1632, 13, 1289, 11], [1632, 22, 1289, 20], [1633, 6, 1290, 4, "marginTop"], [1633, 15, 1290, 13], [1633, 17, 1290, 15], [1633, 19, 1290, 17], [1634, 6, 1291, 4, "marginBottom"], [1634, 18, 1291, 16], [1634, 20, 1291, 18], [1635, 4, 1292, 2], [1635, 5, 1292, 3], [1636, 4, 1293, 2, "progressBar"], [1636, 15, 1293, 13], [1636, 17, 1293, 15], [1637, 6, 1294, 4, "width"], [1637, 11, 1294, 9], [1637, 13, 1294, 11], [1637, 19, 1294, 17], [1638, 6, 1295, 4, "height"], [1638, 12, 1295, 10], [1638, 14, 1295, 12], [1638, 15, 1295, 13], [1639, 6, 1296, 4, "backgroundColor"], [1639, 21, 1296, 19], [1639, 23, 1296, 21], [1639, 32, 1296, 30], [1640, 6, 1297, 4, "borderRadius"], [1640, 18, 1297, 16], [1640, 20, 1297, 18], [1640, 21, 1297, 19], [1641, 6, 1298, 4, "overflow"], [1641, 14, 1298, 12], [1641, 16, 1298, 14], [1641, 24, 1298, 22], [1642, 6, 1299, 4, "marginBottom"], [1642, 18, 1299, 16], [1642, 20, 1299, 18], [1643, 4, 1300, 2], [1643, 5, 1300, 3], [1644, 4, 1301, 2, "progressFill"], [1644, 16, 1301, 14], [1644, 18, 1301, 16], [1645, 6, 1302, 4, "height"], [1645, 12, 1302, 10], [1645, 14, 1302, 12], [1645, 20, 1302, 18], [1646, 6, 1303, 4, "backgroundColor"], [1646, 21, 1303, 19], [1646, 23, 1303, 21], [1646, 32, 1303, 30], [1647, 6, 1304, 4, "borderRadius"], [1647, 18, 1304, 16], [1647, 20, 1304, 18], [1648, 4, 1305, 2], [1648, 5, 1305, 3], [1649, 4, 1306, 2, "processingDescription"], [1649, 25, 1306, 23], [1649, 27, 1306, 25], [1650, 6, 1307, 4, "fontSize"], [1650, 14, 1307, 12], [1650, 16, 1307, 14], [1650, 18, 1307, 16], [1651, 6, 1308, 4, "color"], [1651, 11, 1308, 9], [1651, 13, 1308, 11], [1651, 22, 1308, 20], [1652, 6, 1309, 4, "textAlign"], [1652, 15, 1309, 13], [1652, 17, 1309, 15], [1653, 4, 1310, 2], [1653, 5, 1310, 3], [1654, 4, 1311, 2, "successIcon"], [1654, 15, 1311, 13], [1654, 17, 1311, 15], [1655, 6, 1312, 4, "marginTop"], [1655, 15, 1312, 13], [1655, 17, 1312, 15], [1656, 4, 1313, 2], [1656, 5, 1313, 3], [1657, 4, 1314, 2, "errorContent"], [1657, 16, 1314, 14], [1657, 18, 1314, 16], [1658, 6, 1315, 4, "backgroundColor"], [1658, 21, 1315, 19], [1658, 23, 1315, 21], [1658, 29, 1315, 27], [1659, 6, 1316, 4, "borderRadius"], [1659, 18, 1316, 16], [1659, 20, 1316, 18], [1659, 22, 1316, 20], [1660, 6, 1317, 4, "padding"], [1660, 13, 1317, 11], [1660, 15, 1317, 13], [1660, 17, 1317, 15], [1661, 6, 1318, 4, "width"], [1661, 11, 1318, 9], [1661, 13, 1318, 11], [1661, 18, 1318, 16], [1662, 6, 1319, 4, "max<PERSON><PERSON><PERSON>"], [1662, 14, 1319, 12], [1662, 16, 1319, 14], [1662, 19, 1319, 17], [1663, 6, 1320, 4, "alignItems"], [1663, 16, 1320, 14], [1663, 18, 1320, 16], [1664, 4, 1321, 2], [1664, 5, 1321, 3], [1665, 4, 1322, 2, "errorTitle"], [1665, 14, 1322, 12], [1665, 16, 1322, 14], [1666, 6, 1323, 4, "fontSize"], [1666, 14, 1323, 12], [1666, 16, 1323, 14], [1666, 18, 1323, 16], [1667, 6, 1324, 4, "fontWeight"], [1667, 16, 1324, 14], [1667, 18, 1324, 16], [1667, 23, 1324, 21], [1668, 6, 1325, 4, "color"], [1668, 11, 1325, 9], [1668, 13, 1325, 11], [1668, 22, 1325, 20], [1669, 6, 1326, 4, "marginTop"], [1669, 15, 1326, 13], [1669, 17, 1326, 15], [1669, 19, 1326, 17], [1670, 6, 1327, 4, "marginBottom"], [1670, 18, 1327, 16], [1670, 20, 1327, 18], [1671, 4, 1328, 2], [1671, 5, 1328, 3], [1672, 4, 1329, 2, "errorMessage"], [1672, 16, 1329, 14], [1672, 18, 1329, 16], [1673, 6, 1330, 4, "fontSize"], [1673, 14, 1330, 12], [1673, 16, 1330, 14], [1673, 18, 1330, 16], [1674, 6, 1331, 4, "color"], [1674, 11, 1331, 9], [1674, 13, 1331, 11], [1674, 22, 1331, 20], [1675, 6, 1332, 4, "textAlign"], [1675, 15, 1332, 13], [1675, 17, 1332, 15], [1675, 25, 1332, 23], [1676, 6, 1333, 4, "marginBottom"], [1676, 18, 1333, 16], [1676, 20, 1333, 18], [1677, 4, 1334, 2], [1677, 5, 1334, 3], [1678, 4, 1335, 2, "primaryButton"], [1678, 17, 1335, 15], [1678, 19, 1335, 17], [1679, 6, 1336, 4, "backgroundColor"], [1679, 21, 1336, 19], [1679, 23, 1336, 21], [1679, 32, 1336, 30], [1680, 6, 1337, 4, "paddingHorizontal"], [1680, 23, 1337, 21], [1680, 25, 1337, 23], [1680, 27, 1337, 25], [1681, 6, 1338, 4, "paddingVertical"], [1681, 21, 1338, 19], [1681, 23, 1338, 21], [1681, 25, 1338, 23], [1682, 6, 1339, 4, "borderRadius"], [1682, 18, 1339, 16], [1682, 20, 1339, 18], [1682, 21, 1339, 19], [1683, 6, 1340, 4, "marginTop"], [1683, 15, 1340, 13], [1683, 17, 1340, 15], [1684, 4, 1341, 2], [1684, 5, 1341, 3], [1685, 4, 1342, 2, "primaryButtonText"], [1685, 21, 1342, 19], [1685, 23, 1342, 21], [1686, 6, 1343, 4, "color"], [1686, 11, 1343, 9], [1686, 13, 1343, 11], [1686, 19, 1343, 17], [1687, 6, 1344, 4, "fontSize"], [1687, 14, 1344, 12], [1687, 16, 1344, 14], [1687, 18, 1344, 16], [1688, 6, 1345, 4, "fontWeight"], [1688, 16, 1345, 14], [1688, 18, 1345, 16], [1689, 4, 1346, 2], [1689, 5, 1346, 3], [1690, 4, 1347, 2, "secondaryButton"], [1690, 19, 1347, 17], [1690, 21, 1347, 19], [1691, 6, 1348, 4, "paddingHorizontal"], [1691, 23, 1348, 21], [1691, 25, 1348, 23], [1691, 27, 1348, 25], [1692, 6, 1349, 4, "paddingVertical"], [1692, 21, 1349, 19], [1692, 23, 1349, 21], [1692, 25, 1349, 23], [1693, 6, 1350, 4, "marginTop"], [1693, 15, 1350, 13], [1693, 17, 1350, 15], [1694, 4, 1351, 2], [1694, 5, 1351, 3], [1695, 4, 1352, 2, "secondaryButtonText"], [1695, 23, 1352, 21], [1695, 25, 1352, 23], [1696, 6, 1353, 4, "color"], [1696, 11, 1353, 9], [1696, 13, 1353, 11], [1696, 22, 1353, 20], [1697, 6, 1354, 4, "fontSize"], [1697, 14, 1354, 12], [1697, 16, 1354, 14], [1698, 4, 1355, 2], [1698, 5, 1355, 3], [1699, 4, 1356, 2, "permissionContent"], [1699, 21, 1356, 19], [1699, 23, 1356, 21], [1700, 6, 1357, 4, "flex"], [1700, 10, 1357, 8], [1700, 12, 1357, 10], [1700, 13, 1357, 11], [1701, 6, 1358, 4, "justifyContent"], [1701, 20, 1358, 18], [1701, 22, 1358, 20], [1701, 30, 1358, 28], [1702, 6, 1359, 4, "alignItems"], [1702, 16, 1359, 14], [1702, 18, 1359, 16], [1702, 26, 1359, 24], [1703, 6, 1360, 4, "padding"], [1703, 13, 1360, 11], [1703, 15, 1360, 13], [1704, 4, 1361, 2], [1704, 5, 1361, 3], [1705, 4, 1362, 2, "permissionTitle"], [1705, 19, 1362, 17], [1705, 21, 1362, 19], [1706, 6, 1363, 4, "fontSize"], [1706, 14, 1363, 12], [1706, 16, 1363, 14], [1706, 18, 1363, 16], [1707, 6, 1364, 4, "fontWeight"], [1707, 16, 1364, 14], [1707, 18, 1364, 16], [1707, 23, 1364, 21], [1708, 6, 1365, 4, "color"], [1708, 11, 1365, 9], [1708, 13, 1365, 11], [1708, 22, 1365, 20], [1709, 6, 1366, 4, "marginTop"], [1709, 15, 1366, 13], [1709, 17, 1366, 15], [1709, 19, 1366, 17], [1710, 6, 1367, 4, "marginBottom"], [1710, 18, 1367, 16], [1710, 20, 1367, 18], [1711, 4, 1368, 2], [1711, 5, 1368, 3], [1712, 4, 1369, 2, "permissionDescription"], [1712, 25, 1369, 23], [1712, 27, 1369, 25], [1713, 6, 1370, 4, "fontSize"], [1713, 14, 1370, 12], [1713, 16, 1370, 14], [1713, 18, 1370, 16], [1714, 6, 1371, 4, "color"], [1714, 11, 1371, 9], [1714, 13, 1371, 11], [1714, 22, 1371, 20], [1715, 6, 1372, 4, "textAlign"], [1715, 15, 1372, 13], [1715, 17, 1372, 15], [1715, 25, 1372, 23], [1716, 6, 1373, 4, "marginBottom"], [1716, 18, 1373, 16], [1716, 20, 1373, 18], [1717, 4, 1374, 2], [1717, 5, 1374, 3], [1718, 4, 1375, 2, "loadingText"], [1718, 15, 1375, 13], [1718, 17, 1375, 15], [1719, 6, 1376, 4, "color"], [1719, 11, 1376, 9], [1719, 13, 1376, 11], [1719, 22, 1376, 20], [1720, 6, 1377, 4, "marginTop"], [1720, 15, 1377, 13], [1720, 17, 1377, 15], [1721, 4, 1378, 2], [1721, 5, 1378, 3], [1722, 4, 1379, 2], [1723, 4, 1380, 2, "blurZone"], [1723, 12, 1380, 10], [1723, 14, 1380, 12], [1724, 6, 1381, 4, "position"], [1724, 14, 1381, 12], [1724, 16, 1381, 14], [1724, 26, 1381, 24], [1725, 6, 1382, 4, "overflow"], [1725, 14, 1382, 12], [1725, 16, 1382, 14], [1726, 4, 1383, 2], [1726, 5, 1383, 3], [1727, 4, 1384, 2, "previewChip"], [1727, 15, 1384, 13], [1727, 17, 1384, 15], [1728, 6, 1385, 4, "position"], [1728, 14, 1385, 12], [1728, 16, 1385, 14], [1728, 26, 1385, 24], [1729, 6, 1386, 4, "top"], [1729, 9, 1386, 7], [1729, 11, 1386, 9], [1729, 12, 1386, 10], [1730, 6, 1387, 4, "right"], [1730, 11, 1387, 9], [1730, 13, 1387, 11], [1730, 14, 1387, 12], [1731, 6, 1388, 4, "backgroundColor"], [1731, 21, 1388, 19], [1731, 23, 1388, 21], [1731, 40, 1388, 38], [1732, 6, 1389, 4, "paddingHorizontal"], [1732, 23, 1389, 21], [1732, 25, 1389, 23], [1732, 27, 1389, 25], [1733, 6, 1390, 4, "paddingVertical"], [1733, 21, 1390, 19], [1733, 23, 1390, 21], [1733, 24, 1390, 22], [1734, 6, 1391, 4, "borderRadius"], [1734, 18, 1391, 16], [1734, 20, 1391, 18], [1735, 4, 1392, 2], [1735, 5, 1392, 3], [1736, 4, 1393, 2, "previewChipText"], [1736, 19, 1393, 17], [1736, 21, 1393, 19], [1737, 6, 1394, 4, "color"], [1737, 11, 1394, 9], [1737, 13, 1394, 11], [1737, 19, 1394, 17], [1738, 6, 1395, 4, "fontSize"], [1738, 14, 1395, 12], [1738, 16, 1395, 14], [1738, 18, 1395, 16], [1739, 6, 1396, 4, "fontWeight"], [1739, 16, 1396, 14], [1739, 18, 1396, 16], [1740, 4, 1397, 2], [1741, 2, 1398, 0], [1741, 3, 1398, 1], [1741, 4, 1398, 2], [1742, 2, 1398, 3], [1742, 6, 1398, 3, "_c"], [1742, 8, 1398, 3], [1743, 2, 1398, 3, "$RefreshReg$"], [1743, 14, 1398, 3], [1743, 15, 1398, 3, "_c"], [1743, 17, 1398, 3], [1744, 0, 1398, 3], [1744, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "detectFacesAggressive", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;gCSE;GToC;+BUE;GV0C;qBWE;GXQ;8BYE;GZ4B;2BaE;Gba;wBcE;GdiB;0BeG;GfuE;0BgBE;GhBuB;gCiBE;kBCa;KDG;GjBC;mCmBG;wBfc,kCe;GnBoC;mCoBE;wBhBa;OgBI;oFC+C;UDM;8BE8B;SFoD;uDhBa;sBmBC,wBnB;OgBC;GpBmB;6BwBG;GxB6B;kCyBG;GzB8C;4B0BE;mBCmD;SDE;G1BO;uB4BE;G5BI;mC6BG;G7BM;YCE;GDK;oB8B2C;W9BG;yB+BC;W/BG;wBgCC;WhCI;CD4L"}}, "type": "js/module"}]}