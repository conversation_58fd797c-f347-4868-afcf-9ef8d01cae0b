{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces with lower confidence threshold to catch more faces\n        const predictions = await model.estimateFaces(tensor, false, 0.7); // Lower threshold from default 0.9\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sensitive face detection criteria\n          if (analysis.skinRatio > 0.15 &&\n          // Lower skin ratio threshold\n          analysis.hasVariation && analysis.brightness > 0.15 &&\n          // Lower brightness threshold\n          analysis.brightness < 0.9) {\n            // Higher max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const detectFacesAggressive = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🚨 Running aggressive face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 6; // Larger blocks for aggressive detection\n\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // More overlap\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // Very relaxed criteria - catch anything that might be a face\n          if (analysis.skinRatio > 0.08 &&\n          // Very low skin ratio\n          analysis.brightness > 0.1 &&\n          // Very low brightness threshold\n          analysis.brightness < 0.95) {\n            // High max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize / img.width,\n                height: blockSize / img.height\n              },\n              confidence: 0.4 // Lower confidence for aggressive detection\n            });\n          }\n        }\n      }\n\n      // Merge overlapping detections\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🚨 Aggressive detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 5); // Allow more faces in aggressive mode\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions`);\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          console.log('[EchoCameraWeb] 🔄 Loading TensorFlow.js and BlazeFace...');\n          await loadTensorFlowFaceDetection();\n          console.log('[EchoCameraWeb] ✅ TensorFlow.js loaded, starting face detection...');\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n          console.warn('[EchoCameraWeb] ❌ TensorFlow error details:', {\n            message: tensorFlowError.message,\n            stack: tensorFlowError.stack\n          });\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n\n        // Strategy 3: If still no faces found, use aggressive detection\n        if (detectedFaces.length === 0) {\n          console.log('[EchoCameraWeb] 🔍 No faces found, trying aggressive detection...');\n          detectedFaces = detectFacesAggressive(img, ctx);\n          console.log(`[EchoCameraWeb] 🔍 Aggressive detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n\n        // CRITICAL: Update the captured photo state with the blurred version\n        setCapturedPhoto(blurredImageUrl);\n        console.log('[EchoCameraWeb] 🔄 Updated capturedPhoto state with blurred image');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 866,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 867,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 865,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 877,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 883,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 882,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 885,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 875,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 874,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 899,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 921,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 922,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 923,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 920,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 919,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 932,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 935,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 943,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 951,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 975,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 974,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 933,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 988,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 990,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 991,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 989,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 995,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 996,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 994,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 987,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1001,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1000,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 986,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 985,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1007,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1008,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1006,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1014,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1027,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1029,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1018,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1032,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1013,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 898,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1047,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1049,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1056,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1055,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1063,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1070,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1046,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1045,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1040,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1083,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1084,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1085,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1090,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1086,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1096,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1092,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1082,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1081,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1076,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 896,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1696, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 69, 132, 67], [129, 72, 132, 70], [129, 73, 132, 71], [129, 74, 132, 72], [129, 75, 132, 73], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [180, 10, 187, 42], [181, 10, 188, 12, "analysis"], [181, 18, 188, 20], [181, 19, 188, 21, "hasVariation"], [181, 31, 188, 33], [181, 35, 189, 12, "analysis"], [181, 43, 189, 20], [181, 44, 189, 21, "brightness"], [181, 54, 189, 31], [181, 57, 189, 34], [181, 61, 189, 38], [182, 10, 189, 43], [183, 10, 190, 12, "analysis"], [183, 18, 190, 20], [183, 19, 190, 21, "brightness"], [183, 29, 190, 31], [183, 32, 190, 34], [183, 35, 190, 37], [183, 37, 190, 39], [184, 12, 190, 43], [186, 12, 192, 10, "faces"], [186, 17, 192, 15], [186, 18, 192, 16, "push"], [186, 22, 192, 20], [186, 23, 192, 21], [187, 14, 193, 12, "boundingBox"], [187, 25, 193, 23], [187, 27, 193, 25], [188, 16, 194, 14, "xCenter"], [188, 23, 194, 21], [188, 25, 194, 23], [188, 26, 194, 24, "x"], [188, 27, 194, 25], [188, 30, 194, 28, "blockSize"], [188, 39, 194, 37], [188, 42, 194, 40], [188, 43, 194, 41], [188, 47, 194, 45, "img"], [188, 50, 194, 48], [188, 51, 194, 49, "width"], [188, 56, 194, 54], [189, 16, 195, 14, "yCenter"], [189, 23, 195, 21], [189, 25, 195, 23], [189, 26, 195, 24, "y"], [189, 27, 195, 25], [189, 30, 195, 28, "blockSize"], [189, 39, 195, 37], [189, 42, 195, 40], [189, 43, 195, 41], [189, 47, 195, 45, "img"], [189, 50, 195, 48], [189, 51, 195, 49, "height"], [189, 57, 195, 55], [190, 16, 196, 14, "width"], [190, 21, 196, 19], [190, 23, 196, 22, "blockSize"], [190, 32, 196, 31], [190, 35, 196, 34], [190, 38, 196, 37], [190, 41, 196, 41, "img"], [190, 44, 196, 44], [190, 45, 196, 45, "width"], [190, 50, 196, 50], [191, 16, 197, 14, "height"], [191, 22, 197, 20], [191, 24, 197, 23, "blockSize"], [191, 33, 197, 32], [191, 36, 197, 35], [191, 39, 197, 38], [191, 42, 197, 42, "img"], [191, 45, 197, 45], [191, 46, 197, 46, "height"], [192, 14, 198, 12], [192, 15, 198, 13], [193, 14, 199, 12, "confidence"], [193, 24, 199, 22], [193, 26, 199, 24, "analysis"], [193, 34, 199, 32], [193, 35, 199, 33, "skinRatio"], [193, 44, 199, 42], [193, 47, 199, 45, "analysis"], [193, 55, 199, 53], [193, 56, 199, 54, "variation"], [194, 12, 200, 10], [194, 13, 200, 11], [194, 14, 200, 12], [195, 12, 202, 10, "console"], [195, 19, 202, 17], [195, 20, 202, 18, "log"], [195, 23, 202, 21], [195, 24, 202, 22], [195, 71, 202, 69, "Math"], [195, 75, 202, 73], [195, 76, 202, 74, "round"], [195, 81, 202, 79], [195, 82, 202, 80, "x"], [195, 83, 202, 81], [195, 84, 202, 82], [195, 89, 202, 87, "Math"], [195, 93, 202, 91], [195, 94, 202, 92, "round"], [195, 99, 202, 97], [195, 100, 202, 98, "y"], [195, 101, 202, 99], [195, 102, 202, 100], [195, 115, 202, 113], [195, 116, 202, 114, "analysis"], [195, 124, 202, 122], [195, 125, 202, 123, "skinRatio"], [195, 134, 202, 132], [195, 137, 202, 135], [195, 140, 202, 138], [195, 142, 202, 140, "toFixed"], [195, 149, 202, 147], [195, 150, 202, 148], [195, 151, 202, 149], [195, 152, 202, 150], [195, 169, 202, 167, "analysis"], [195, 177, 202, 175], [195, 178, 202, 176, "variation"], [195, 187, 202, 185], [195, 188, 202, 186, "toFixed"], [195, 195, 202, 193], [195, 196, 202, 194], [195, 197, 202, 195], [195, 198, 202, 196], [195, 215, 202, 213, "analysis"], [195, 223, 202, 221], [195, 224, 202, 222, "brightness"], [195, 234, 202, 232], [195, 235, 202, 233, "toFixed"], [195, 242, 202, 240], [195, 243, 202, 241], [195, 244, 202, 242], [195, 245, 202, 243], [195, 247, 202, 245], [195, 248, 202, 246], [196, 10, 203, 8], [197, 8, 204, 6], [198, 6, 205, 4], [200, 6, 207, 4], [201, 6, 208, 4, "faces"], [201, 11, 208, 9], [201, 12, 208, 10, "sort"], [201, 16, 208, 14], [201, 17, 208, 15], [201, 18, 208, 16, "a"], [201, 19, 208, 17], [201, 21, 208, 19, "b"], [201, 22, 208, 20], [201, 27, 208, 25], [201, 28, 208, 26, "b"], [201, 29, 208, 27], [201, 30, 208, 28, "confidence"], [201, 40, 208, 38], [201, 44, 208, 42], [201, 45, 208, 43], [201, 50, 208, 48, "a"], [201, 51, 208, 49], [201, 52, 208, 50, "confidence"], [201, 62, 208, 60], [201, 66, 208, 64], [201, 67, 208, 65], [201, 68, 208, 66], [201, 69, 208, 67], [202, 6, 209, 4], [202, 12, 209, 10, "mergedFaces"], [202, 23, 209, 21], [202, 26, 209, 24, "mergeFaceDetections"], [202, 45, 209, 43], [202, 46, 209, 44, "faces"], [202, 51, 209, 49], [202, 52, 209, 50], [203, 6, 211, 4, "console"], [203, 13, 211, 11], [203, 14, 211, 12, "log"], [203, 17, 211, 15], [203, 18, 211, 16], [203, 61, 211, 59, "faces"], [203, 66, 211, 64], [203, 67, 211, 65, "length"], [203, 73, 211, 71], [203, 90, 211, 88, "mergedFaces"], [203, 101, 211, 99], [203, 102, 211, 100, "length"], [203, 108, 211, 106], [203, 123, 211, 121], [203, 124, 211, 122], [204, 6, 212, 4], [204, 13, 212, 11, "mergedFaces"], [204, 24, 212, 22], [204, 25, 212, 23, "slice"], [204, 30, 212, 28], [204, 31, 212, 29], [204, 32, 212, 30], [204, 34, 212, 32], [204, 35, 212, 33], [204, 36, 212, 34], [204, 37, 212, 35], [204, 38, 212, 36], [205, 4, 213, 2], [205, 5, 213, 3], [206, 4, 215, 2], [206, 10, 215, 8, "detectFacesAggressive"], [206, 31, 215, 29], [206, 34, 215, 32, "detectFacesAggressive"], [206, 35, 215, 33, "img"], [206, 38, 215, 54], [206, 40, 215, 56, "ctx"], [206, 43, 215, 85], [206, 48, 215, 90], [207, 6, 216, 4, "console"], [207, 13, 216, 11], [207, 14, 216, 12, "log"], [207, 17, 216, 15], [207, 18, 216, 16], [207, 75, 216, 73], [207, 76, 216, 74], [209, 6, 218, 4], [210, 6, 219, 4], [210, 12, 219, 10, "imageData"], [210, 21, 219, 19], [210, 24, 219, 22, "ctx"], [210, 27, 219, 25], [210, 28, 219, 26, "getImageData"], [210, 40, 219, 38], [210, 41, 219, 39], [210, 42, 219, 40], [210, 44, 219, 42], [210, 45, 219, 43], [210, 47, 219, 45, "img"], [210, 50, 219, 48], [210, 51, 219, 49, "width"], [210, 56, 219, 54], [210, 58, 219, 56, "img"], [210, 61, 219, 59], [210, 62, 219, 60, "height"], [210, 68, 219, 66], [210, 69, 219, 67], [211, 6, 220, 4], [211, 12, 220, 10, "data"], [211, 16, 220, 14], [211, 19, 220, 17, "imageData"], [211, 28, 220, 26], [211, 29, 220, 27, "data"], [211, 33, 220, 31], [212, 6, 222, 4], [212, 12, 222, 10, "faces"], [212, 17, 222, 15], [212, 20, 222, 18], [212, 22, 222, 20], [213, 6, 223, 4], [213, 12, 223, 10, "blockSize"], [213, 21, 223, 19], [213, 24, 223, 22, "Math"], [213, 28, 223, 26], [213, 29, 223, 27, "min"], [213, 32, 223, 30], [213, 33, 223, 31, "img"], [213, 36, 223, 34], [213, 37, 223, 35, "width"], [213, 42, 223, 40], [213, 44, 223, 42, "img"], [213, 47, 223, 45], [213, 48, 223, 46, "height"], [213, 54, 223, 52], [213, 55, 223, 53], [213, 58, 223, 56], [213, 59, 223, 57], [213, 60, 223, 58], [213, 61, 223, 59], [215, 6, 225, 4], [215, 11, 225, 9], [215, 15, 225, 13, "y"], [215, 16, 225, 14], [215, 19, 225, 17], [215, 20, 225, 18], [215, 22, 225, 20, "y"], [215, 23, 225, 21], [215, 26, 225, 24, "img"], [215, 29, 225, 27], [215, 30, 225, 28, "height"], [215, 36, 225, 34], [215, 39, 225, 37, "blockSize"], [215, 48, 225, 46], [215, 50, 225, 48, "y"], [215, 51, 225, 49], [215, 55, 225, 53, "blockSize"], [215, 64, 225, 62], [215, 67, 225, 65], [215, 68, 225, 66], [215, 70, 225, 68], [216, 8, 225, 70], [217, 8, 226, 6], [217, 13, 226, 11], [217, 17, 226, 15, "x"], [217, 18, 226, 16], [217, 21, 226, 19], [217, 22, 226, 20], [217, 24, 226, 22, "x"], [217, 25, 226, 23], [217, 28, 226, 26, "img"], [217, 31, 226, 29], [217, 32, 226, 30, "width"], [217, 37, 226, 35], [217, 40, 226, 38, "blockSize"], [217, 49, 226, 47], [217, 51, 226, 49, "x"], [217, 52, 226, 50], [217, 56, 226, 54, "blockSize"], [217, 65, 226, 63], [217, 68, 226, 66], [217, 69, 226, 67], [217, 71, 226, 69], [218, 10, 227, 8], [218, 16, 227, 14, "analysis"], [218, 24, 227, 22], [218, 27, 227, 25, "analyzeRegionForFace"], [218, 47, 227, 45], [218, 48, 227, 46, "data"], [218, 52, 227, 50], [218, 54, 227, 52, "x"], [218, 55, 227, 53], [218, 57, 227, 55, "y"], [218, 58, 227, 56], [218, 60, 227, 58, "blockSize"], [218, 69, 227, 67], [218, 71, 227, 69, "img"], [218, 74, 227, 72], [218, 75, 227, 73, "width"], [218, 80, 227, 78], [218, 82, 227, 80, "img"], [218, 85, 227, 83], [218, 86, 227, 84, "height"], [218, 92, 227, 90], [218, 93, 227, 91], [220, 10, 229, 8], [221, 10, 230, 8], [221, 14, 230, 12, "analysis"], [221, 22, 230, 20], [221, 23, 230, 21, "skinRatio"], [221, 32, 230, 30], [221, 35, 230, 33], [221, 39, 230, 37], [222, 10, 230, 42], [223, 10, 231, 12, "analysis"], [223, 18, 231, 20], [223, 19, 231, 21, "brightness"], [223, 29, 231, 31], [223, 32, 231, 34], [223, 35, 231, 37], [224, 10, 231, 42], [225, 10, 232, 12, "analysis"], [225, 18, 232, 20], [225, 19, 232, 21, "brightness"], [225, 29, 232, 31], [225, 32, 232, 34], [225, 36, 232, 38], [225, 38, 232, 40], [226, 12, 232, 43], [228, 12, 234, 10, "faces"], [228, 17, 234, 15], [228, 18, 234, 16, "push"], [228, 22, 234, 20], [228, 23, 234, 21], [229, 14, 235, 12, "boundingBox"], [229, 25, 235, 23], [229, 27, 235, 25], [230, 16, 236, 14, "xCenter"], [230, 23, 236, 21], [230, 25, 236, 23], [230, 26, 236, 24, "x"], [230, 27, 236, 25], [230, 30, 236, 28, "blockSize"], [230, 39, 236, 37], [230, 42, 236, 40], [230, 43, 236, 41], [230, 47, 236, 45, "img"], [230, 50, 236, 48], [230, 51, 236, 49, "width"], [230, 56, 236, 54], [231, 16, 237, 14, "yCenter"], [231, 23, 237, 21], [231, 25, 237, 23], [231, 26, 237, 24, "y"], [231, 27, 237, 25], [231, 30, 237, 28, "blockSize"], [231, 39, 237, 37], [231, 42, 237, 40], [231, 43, 237, 41], [231, 47, 237, 45, "img"], [231, 50, 237, 48], [231, 51, 237, 49, "height"], [231, 57, 237, 55], [232, 16, 238, 14, "width"], [232, 21, 238, 19], [232, 23, 238, 21, "blockSize"], [232, 32, 238, 30], [232, 35, 238, 33, "img"], [232, 38, 238, 36], [232, 39, 238, 37, "width"], [232, 44, 238, 42], [233, 16, 239, 14, "height"], [233, 22, 239, 20], [233, 24, 239, 22, "blockSize"], [233, 33, 239, 31], [233, 36, 239, 34, "img"], [233, 39, 239, 37], [233, 40, 239, 38, "height"], [234, 14, 240, 12], [234, 15, 240, 13], [235, 14, 241, 12, "confidence"], [235, 24, 241, 22], [235, 26, 241, 24], [235, 29, 241, 27], [235, 30, 241, 28], [236, 12, 242, 10], [236, 13, 242, 11], [236, 14, 242, 12], [237, 10, 243, 8], [238, 8, 244, 6], [239, 6, 245, 4], [241, 6, 247, 4], [242, 6, 248, 4], [242, 12, 248, 10, "mergedFaces"], [242, 23, 248, 21], [242, 26, 248, 24, "mergeFaceDetections"], [242, 45, 248, 43], [242, 46, 248, 44, "faces"], [242, 51, 248, 49], [242, 52, 248, 50], [243, 6, 249, 4, "console"], [243, 13, 249, 11], [243, 14, 249, 12, "log"], [243, 17, 249, 15], [243, 18, 249, 16], [243, 62, 249, 60, "faces"], [243, 67, 249, 65], [243, 68, 249, 66, "length"], [243, 74, 249, 72], [243, 91, 249, 89, "mergedFaces"], [243, 102, 249, 100], [243, 103, 249, 101, "length"], [243, 109, 249, 107], [243, 124, 249, 122], [243, 125, 249, 123], [244, 6, 250, 4], [244, 13, 250, 11, "mergedFaces"], [244, 24, 250, 22], [244, 25, 250, 23, "slice"], [244, 30, 250, 28], [244, 31, 250, 29], [244, 32, 250, 30], [244, 34, 250, 32], [244, 35, 250, 33], [244, 36, 250, 34], [244, 37, 250, 35], [244, 38, 250, 36], [245, 4, 251, 2], [245, 5, 251, 3], [246, 4, 253, 2], [246, 10, 253, 8, "analyzeRegionForFace"], [246, 30, 253, 28], [246, 33, 253, 31, "analyzeRegionForFace"], [246, 34, 253, 32, "data"], [246, 38, 253, 55], [246, 40, 253, 57, "startX"], [246, 46, 253, 71], [246, 48, 253, 73, "startY"], [246, 54, 253, 87], [246, 56, 253, 89, "size"], [246, 60, 253, 101], [246, 62, 253, 103, "imageWidth"], [246, 72, 253, 121], [246, 74, 253, 123, "imageHeight"], [246, 85, 253, 142], [246, 90, 253, 147], [247, 6, 254, 4], [247, 10, 254, 8, "skinPixels"], [247, 20, 254, 18], [247, 23, 254, 21], [247, 24, 254, 22], [248, 6, 255, 4], [248, 10, 255, 8, "totalPixels"], [248, 21, 255, 19], [248, 24, 255, 22], [248, 25, 255, 23], [249, 6, 256, 4], [249, 10, 256, 8, "totalBrightness"], [249, 25, 256, 23], [249, 28, 256, 26], [249, 29, 256, 27], [250, 6, 257, 4], [250, 10, 257, 8, "colorVariations"], [250, 25, 257, 23], [250, 28, 257, 26], [250, 29, 257, 27], [251, 6, 258, 4], [251, 10, 258, 8, "prevR"], [251, 15, 258, 13], [251, 18, 258, 16], [251, 19, 258, 17], [252, 8, 258, 19, "prevG"], [252, 13, 258, 24], [252, 16, 258, 27], [252, 17, 258, 28], [253, 8, 258, 30, "prevB"], [253, 13, 258, 35], [253, 16, 258, 38], [253, 17, 258, 39], [254, 6, 260, 4], [254, 11, 260, 9], [254, 15, 260, 13, "y"], [254, 16, 260, 14], [254, 19, 260, 17, "startY"], [254, 25, 260, 23], [254, 27, 260, 25, "y"], [254, 28, 260, 26], [254, 31, 260, 29, "startY"], [254, 37, 260, 35], [254, 40, 260, 38, "size"], [254, 44, 260, 42], [254, 48, 260, 46, "y"], [254, 49, 260, 47], [254, 52, 260, 50, "imageHeight"], [254, 63, 260, 61], [254, 65, 260, 63, "y"], [254, 66, 260, 64], [254, 68, 260, 66], [254, 70, 260, 68], [255, 8, 261, 6], [255, 13, 261, 11], [255, 17, 261, 15, "x"], [255, 18, 261, 16], [255, 21, 261, 19, "startX"], [255, 27, 261, 25], [255, 29, 261, 27, "x"], [255, 30, 261, 28], [255, 33, 261, 31, "startX"], [255, 39, 261, 37], [255, 42, 261, 40, "size"], [255, 46, 261, 44], [255, 50, 261, 48, "x"], [255, 51, 261, 49], [255, 54, 261, 52, "imageWidth"], [255, 64, 261, 62], [255, 66, 261, 64, "x"], [255, 67, 261, 65], [255, 69, 261, 67], [255, 71, 261, 69], [256, 10, 262, 8], [256, 16, 262, 14, "index"], [256, 21, 262, 19], [256, 24, 262, 22], [256, 25, 262, 23, "y"], [256, 26, 262, 24], [256, 29, 262, 27, "imageWidth"], [256, 39, 262, 37], [256, 42, 262, 40, "x"], [256, 43, 262, 41], [256, 47, 262, 45], [256, 48, 262, 46], [257, 10, 263, 8], [257, 16, 263, 14, "r"], [257, 17, 263, 15], [257, 20, 263, 18, "data"], [257, 24, 263, 22], [257, 25, 263, 23, "index"], [257, 30, 263, 28], [257, 31, 263, 29], [258, 10, 264, 8], [258, 16, 264, 14, "g"], [258, 17, 264, 15], [258, 20, 264, 18, "data"], [258, 24, 264, 22], [258, 25, 264, 23, "index"], [258, 30, 264, 28], [258, 33, 264, 31], [258, 34, 264, 32], [258, 35, 264, 33], [259, 10, 265, 8], [259, 16, 265, 14, "b"], [259, 17, 265, 15], [259, 20, 265, 18, "data"], [259, 24, 265, 22], [259, 25, 265, 23, "index"], [259, 30, 265, 28], [259, 33, 265, 31], [259, 34, 265, 32], [259, 35, 265, 33], [261, 10, 267, 8], [262, 10, 268, 8], [262, 14, 268, 12, "isSkinTone"], [262, 24, 268, 22], [262, 25, 268, 23, "r"], [262, 26, 268, 24], [262, 28, 268, 26, "g"], [262, 29, 268, 27], [262, 31, 268, 29, "b"], [262, 32, 268, 30], [262, 33, 268, 31], [262, 35, 268, 33], [263, 12, 269, 10, "skinPixels"], [263, 22, 269, 20], [263, 24, 269, 22], [264, 10, 270, 8], [266, 10, 272, 8], [267, 10, 273, 8], [267, 16, 273, 14, "brightness"], [267, 26, 273, 24], [267, 29, 273, 27], [267, 30, 273, 28, "r"], [267, 31, 273, 29], [267, 34, 273, 32, "g"], [267, 35, 273, 33], [267, 38, 273, 36, "b"], [267, 39, 273, 37], [267, 44, 273, 42], [267, 45, 273, 43], [267, 48, 273, 46], [267, 51, 273, 49], [267, 52, 273, 50], [268, 10, 274, 8, "totalBrightness"], [268, 25, 274, 23], [268, 29, 274, 27, "brightness"], [268, 39, 274, 37], [270, 10, 276, 8], [271, 10, 277, 8], [271, 14, 277, 12, "totalPixels"], [271, 25, 277, 23], [271, 28, 277, 26], [271, 29, 277, 27], [271, 31, 277, 29], [272, 12, 278, 10], [272, 18, 278, 16, "colorDiff"], [272, 27, 278, 25], [272, 30, 278, 28, "Math"], [272, 34, 278, 32], [272, 35, 278, 33, "abs"], [272, 38, 278, 36], [272, 39, 278, 37, "r"], [272, 40, 278, 38], [272, 43, 278, 41, "prevR"], [272, 48, 278, 46], [272, 49, 278, 47], [272, 52, 278, 50, "Math"], [272, 56, 278, 54], [272, 57, 278, 55, "abs"], [272, 60, 278, 58], [272, 61, 278, 59, "g"], [272, 62, 278, 60], [272, 65, 278, 63, "prevG"], [272, 70, 278, 68], [272, 71, 278, 69], [272, 74, 278, 72, "Math"], [272, 78, 278, 76], [272, 79, 278, 77, "abs"], [272, 82, 278, 80], [272, 83, 278, 81, "b"], [272, 84, 278, 82], [272, 87, 278, 85, "prevB"], [272, 92, 278, 90], [272, 93, 278, 91], [273, 12, 279, 10], [273, 16, 279, 14, "colorDiff"], [273, 25, 279, 23], [273, 28, 279, 26], [273, 30, 279, 28], [273, 32, 279, 30], [274, 14, 279, 32], [275, 14, 280, 12, "colorVariations"], [275, 29, 280, 27], [275, 31, 280, 29], [276, 12, 281, 10], [277, 10, 282, 8], [278, 10, 284, 8, "prevR"], [278, 15, 284, 13], [278, 18, 284, 16, "r"], [278, 19, 284, 17], [279, 10, 284, 19, "prevG"], [279, 15, 284, 24], [279, 18, 284, 27, "g"], [279, 19, 284, 28], [280, 10, 284, 30, "prevB"], [280, 15, 284, 35], [280, 18, 284, 38, "b"], [280, 19, 284, 39], [281, 10, 285, 8, "totalPixels"], [281, 21, 285, 19], [281, 23, 285, 21], [282, 8, 286, 6], [283, 6, 287, 4], [284, 6, 289, 4], [284, 13, 289, 11], [285, 8, 290, 6, "skinRatio"], [285, 17, 290, 15], [285, 19, 290, 17, "skinPixels"], [285, 29, 290, 27], [285, 32, 290, 30, "totalPixels"], [285, 43, 290, 41], [286, 8, 291, 6, "brightness"], [286, 18, 291, 16], [286, 20, 291, 18, "totalBrightness"], [286, 35, 291, 33], [286, 38, 291, 36, "totalPixels"], [286, 49, 291, 47], [287, 8, 292, 6, "variation"], [287, 17, 292, 15], [287, 19, 292, 17, "colorVariations"], [287, 34, 292, 32], [287, 37, 292, 35, "totalPixels"], [287, 48, 292, 46], [288, 8, 293, 6, "hasVariation"], [288, 20, 293, 18], [288, 22, 293, 20, "colorVariations"], [288, 37, 293, 35], [288, 40, 293, 38, "totalPixels"], [288, 51, 293, 49], [288, 54, 293, 52], [288, 57, 293, 55], [288, 58, 293, 56], [289, 6, 294, 4], [289, 7, 294, 5], [290, 4, 295, 2], [290, 5, 295, 3], [291, 4, 297, 2], [291, 10, 297, 8, "isSkinTone"], [291, 20, 297, 18], [291, 23, 297, 21, "isSkinTone"], [291, 24, 297, 22, "r"], [291, 25, 297, 31], [291, 27, 297, 33, "g"], [291, 28, 297, 42], [291, 30, 297, 44, "b"], [291, 31, 297, 53], [291, 36, 297, 58], [292, 6, 298, 4], [293, 6, 299, 4], [293, 13, 300, 6, "r"], [293, 14, 300, 7], [293, 17, 300, 10], [293, 19, 300, 12], [293, 23, 300, 16, "g"], [293, 24, 300, 17], [293, 27, 300, 20], [293, 29, 300, 22], [293, 33, 300, 26, "b"], [293, 34, 300, 27], [293, 37, 300, 30], [293, 39, 300, 32], [293, 43, 301, 6, "r"], [293, 44, 301, 7], [293, 47, 301, 10, "g"], [293, 48, 301, 11], [293, 52, 301, 15, "r"], [293, 53, 301, 16], [293, 56, 301, 19, "b"], [293, 57, 301, 20], [293, 61, 302, 6, "Math"], [293, 65, 302, 10], [293, 66, 302, 11, "abs"], [293, 69, 302, 14], [293, 70, 302, 15, "r"], [293, 71, 302, 16], [293, 74, 302, 19, "g"], [293, 75, 302, 20], [293, 76, 302, 21], [293, 79, 302, 24], [293, 81, 302, 26], [293, 85, 303, 6, "Math"], [293, 89, 303, 10], [293, 90, 303, 11, "max"], [293, 93, 303, 14], [293, 94, 303, 15, "r"], [293, 95, 303, 16], [293, 97, 303, 18, "g"], [293, 98, 303, 19], [293, 100, 303, 21, "b"], [293, 101, 303, 22], [293, 102, 303, 23], [293, 105, 303, 26, "Math"], [293, 109, 303, 30], [293, 110, 303, 31, "min"], [293, 113, 303, 34], [293, 114, 303, 35, "r"], [293, 115, 303, 36], [293, 117, 303, 38, "g"], [293, 118, 303, 39], [293, 120, 303, 41, "b"], [293, 121, 303, 42], [293, 122, 303, 43], [293, 125, 303, 46], [293, 127, 303, 48], [294, 4, 305, 2], [294, 5, 305, 3], [295, 4, 307, 2], [295, 10, 307, 8, "mergeFaceDetections"], [295, 29, 307, 27], [295, 32, 307, 31, "faces"], [295, 37, 307, 43], [295, 41, 307, 48], [296, 6, 308, 4], [296, 10, 308, 8, "faces"], [296, 15, 308, 13], [296, 16, 308, 14, "length"], [296, 22, 308, 20], [296, 26, 308, 24], [296, 27, 308, 25], [296, 29, 308, 27], [296, 36, 308, 34, "faces"], [296, 41, 308, 39], [297, 6, 310, 4], [297, 12, 310, 10, "merged"], [297, 18, 310, 16], [297, 21, 310, 19], [297, 23, 310, 21], [298, 6, 311, 4], [298, 12, 311, 10, "used"], [298, 16, 311, 14], [298, 19, 311, 17], [298, 23, 311, 21, "Set"], [298, 26, 311, 24], [298, 27, 311, 25], [298, 28, 311, 26], [299, 6, 313, 4], [299, 11, 313, 9], [299, 15, 313, 13, "i"], [299, 16, 313, 14], [299, 19, 313, 17], [299, 20, 313, 18], [299, 22, 313, 20, "i"], [299, 23, 313, 21], [299, 26, 313, 24, "faces"], [299, 31, 313, 29], [299, 32, 313, 30, "length"], [299, 38, 313, 36], [299, 40, 313, 38, "i"], [299, 41, 313, 39], [299, 43, 313, 41], [299, 45, 313, 43], [300, 8, 314, 6], [300, 12, 314, 10, "used"], [300, 16, 314, 14], [300, 17, 314, 15, "has"], [300, 20, 314, 18], [300, 21, 314, 19, "i"], [300, 22, 314, 20], [300, 23, 314, 21], [300, 25, 314, 23], [301, 8, 316, 6], [301, 12, 316, 10, "currentFace"], [301, 23, 316, 21], [301, 26, 316, 24, "faces"], [301, 31, 316, 29], [301, 32, 316, 30, "i"], [301, 33, 316, 31], [301, 34, 316, 32], [302, 8, 317, 6, "used"], [302, 12, 317, 10], [302, 13, 317, 11, "add"], [302, 16, 317, 14], [302, 17, 317, 15, "i"], [302, 18, 317, 16], [302, 19, 317, 17], [304, 8, 319, 6], [305, 8, 320, 6], [305, 13, 320, 11], [305, 17, 320, 15, "j"], [305, 18, 320, 16], [305, 21, 320, 19, "i"], [305, 22, 320, 20], [305, 25, 320, 23], [305, 26, 320, 24], [305, 28, 320, 26, "j"], [305, 29, 320, 27], [305, 32, 320, 30, "faces"], [305, 37, 320, 35], [305, 38, 320, 36, "length"], [305, 44, 320, 42], [305, 46, 320, 44, "j"], [305, 47, 320, 45], [305, 49, 320, 47], [305, 51, 320, 49], [306, 10, 321, 8], [306, 14, 321, 12, "used"], [306, 18, 321, 16], [306, 19, 321, 17, "has"], [306, 22, 321, 20], [306, 23, 321, 21, "j"], [306, 24, 321, 22], [306, 25, 321, 23], [306, 27, 321, 25], [307, 10, 323, 8], [307, 16, 323, 14, "overlap"], [307, 23, 323, 21], [307, 26, 323, 24, "calculateOverlap"], [307, 42, 323, 40], [307, 43, 323, 41, "currentFace"], [307, 54, 323, 52], [307, 55, 323, 53, "boundingBox"], [307, 66, 323, 64], [307, 68, 323, 66, "faces"], [307, 73, 323, 71], [307, 74, 323, 72, "j"], [307, 75, 323, 73], [307, 76, 323, 74], [307, 77, 323, 75, "boundingBox"], [307, 88, 323, 86], [307, 89, 323, 87], [308, 10, 324, 8], [308, 14, 324, 12, "overlap"], [308, 21, 324, 19], [308, 24, 324, 22], [308, 27, 324, 25], [308, 29, 324, 27], [309, 12, 324, 29], [310, 12, 325, 10], [311, 12, 326, 10, "currentFace"], [311, 23, 326, 21], [311, 26, 326, 24, "mergeTwoFaces"], [311, 39, 326, 37], [311, 40, 326, 38, "currentFace"], [311, 51, 326, 49], [311, 53, 326, 51, "faces"], [311, 58, 326, 56], [311, 59, 326, 57, "j"], [311, 60, 326, 58], [311, 61, 326, 59], [311, 62, 326, 60], [312, 12, 327, 10, "used"], [312, 16, 327, 14], [312, 17, 327, 15, "add"], [312, 20, 327, 18], [312, 21, 327, 19, "j"], [312, 22, 327, 20], [312, 23, 327, 21], [313, 10, 328, 8], [314, 8, 329, 6], [315, 8, 331, 6, "merged"], [315, 14, 331, 12], [315, 15, 331, 13, "push"], [315, 19, 331, 17], [315, 20, 331, 18, "currentFace"], [315, 31, 331, 29], [315, 32, 331, 30], [316, 6, 332, 4], [317, 6, 334, 4], [317, 13, 334, 11, "merged"], [317, 19, 334, 17], [318, 4, 335, 2], [318, 5, 335, 3], [319, 4, 337, 2], [319, 10, 337, 8, "calculateOverlap"], [319, 26, 337, 24], [319, 29, 337, 27, "calculateOverlap"], [319, 30, 337, 28, "box1"], [319, 34, 337, 37], [319, 36, 337, 39, "box2"], [319, 40, 337, 48], [319, 45, 337, 53], [320, 6, 338, 4], [320, 12, 338, 10, "x1"], [320, 14, 338, 12], [320, 17, 338, 15, "Math"], [320, 21, 338, 19], [320, 22, 338, 20, "max"], [320, 25, 338, 23], [320, 26, 338, 24, "box1"], [320, 30, 338, 28], [320, 31, 338, 29, "xCenter"], [320, 38, 338, 36], [320, 41, 338, 39, "box1"], [320, 45, 338, 43], [320, 46, 338, 44, "width"], [320, 51, 338, 49], [320, 54, 338, 50], [320, 55, 338, 51], [320, 57, 338, 53, "box2"], [320, 61, 338, 57], [320, 62, 338, 58, "xCenter"], [320, 69, 338, 65], [320, 72, 338, 68, "box2"], [320, 76, 338, 72], [320, 77, 338, 73, "width"], [320, 82, 338, 78], [320, 85, 338, 79], [320, 86, 338, 80], [320, 87, 338, 81], [321, 6, 339, 4], [321, 12, 339, 10, "y1"], [321, 14, 339, 12], [321, 17, 339, 15, "Math"], [321, 21, 339, 19], [321, 22, 339, 20, "max"], [321, 25, 339, 23], [321, 26, 339, 24, "box1"], [321, 30, 339, 28], [321, 31, 339, 29, "yCenter"], [321, 38, 339, 36], [321, 41, 339, 39, "box1"], [321, 45, 339, 43], [321, 46, 339, 44, "height"], [321, 52, 339, 50], [321, 55, 339, 51], [321, 56, 339, 52], [321, 58, 339, 54, "box2"], [321, 62, 339, 58], [321, 63, 339, 59, "yCenter"], [321, 70, 339, 66], [321, 73, 339, 69, "box2"], [321, 77, 339, 73], [321, 78, 339, 74, "height"], [321, 84, 339, 80], [321, 87, 339, 81], [321, 88, 339, 82], [321, 89, 339, 83], [322, 6, 340, 4], [322, 12, 340, 10, "x2"], [322, 14, 340, 12], [322, 17, 340, 15, "Math"], [322, 21, 340, 19], [322, 22, 340, 20, "min"], [322, 25, 340, 23], [322, 26, 340, 24, "box1"], [322, 30, 340, 28], [322, 31, 340, 29, "xCenter"], [322, 38, 340, 36], [322, 41, 340, 39, "box1"], [322, 45, 340, 43], [322, 46, 340, 44, "width"], [322, 51, 340, 49], [322, 54, 340, 50], [322, 55, 340, 51], [322, 57, 340, 53, "box2"], [322, 61, 340, 57], [322, 62, 340, 58, "xCenter"], [322, 69, 340, 65], [322, 72, 340, 68, "box2"], [322, 76, 340, 72], [322, 77, 340, 73, "width"], [322, 82, 340, 78], [322, 85, 340, 79], [322, 86, 340, 80], [322, 87, 340, 81], [323, 6, 341, 4], [323, 12, 341, 10, "y2"], [323, 14, 341, 12], [323, 17, 341, 15, "Math"], [323, 21, 341, 19], [323, 22, 341, 20, "min"], [323, 25, 341, 23], [323, 26, 341, 24, "box1"], [323, 30, 341, 28], [323, 31, 341, 29, "yCenter"], [323, 38, 341, 36], [323, 41, 341, 39, "box1"], [323, 45, 341, 43], [323, 46, 341, 44, "height"], [323, 52, 341, 50], [323, 55, 341, 51], [323, 56, 341, 52], [323, 58, 341, 54, "box2"], [323, 62, 341, 58], [323, 63, 341, 59, "yCenter"], [323, 70, 341, 66], [323, 73, 341, 69, "box2"], [323, 77, 341, 73], [323, 78, 341, 74, "height"], [323, 84, 341, 80], [323, 87, 341, 81], [323, 88, 341, 82], [323, 89, 341, 83], [324, 6, 343, 4], [324, 10, 343, 8, "x2"], [324, 12, 343, 10], [324, 16, 343, 14, "x1"], [324, 18, 343, 16], [324, 22, 343, 20, "y2"], [324, 24, 343, 22], [324, 28, 343, 26, "y1"], [324, 30, 343, 28], [324, 32, 343, 30], [324, 39, 343, 37], [324, 40, 343, 38], [325, 6, 345, 4], [325, 12, 345, 10, "overlapArea"], [325, 23, 345, 21], [325, 26, 345, 24], [325, 27, 345, 25, "x2"], [325, 29, 345, 27], [325, 32, 345, 30, "x1"], [325, 34, 345, 32], [325, 39, 345, 37, "y2"], [325, 41, 345, 39], [325, 44, 345, 42, "y1"], [325, 46, 345, 44], [325, 47, 345, 45], [326, 6, 346, 4], [326, 12, 346, 10, "box1Area"], [326, 20, 346, 18], [326, 23, 346, 21, "box1"], [326, 27, 346, 25], [326, 28, 346, 26, "width"], [326, 33, 346, 31], [326, 36, 346, 34, "box1"], [326, 40, 346, 38], [326, 41, 346, 39, "height"], [326, 47, 346, 45], [327, 6, 347, 4], [327, 12, 347, 10, "box2Area"], [327, 20, 347, 18], [327, 23, 347, 21, "box2"], [327, 27, 347, 25], [327, 28, 347, 26, "width"], [327, 33, 347, 31], [327, 36, 347, 34, "box2"], [327, 40, 347, 38], [327, 41, 347, 39, "height"], [327, 47, 347, 45], [328, 6, 349, 4], [328, 13, 349, 11, "overlapArea"], [328, 24, 349, 22], [328, 27, 349, 25, "Math"], [328, 31, 349, 29], [328, 32, 349, 30, "min"], [328, 35, 349, 33], [328, 36, 349, 34, "box1Area"], [328, 44, 349, 42], [328, 46, 349, 44, "box2Area"], [328, 54, 349, 52], [328, 55, 349, 53], [329, 4, 350, 2], [329, 5, 350, 3], [330, 4, 352, 2], [330, 10, 352, 8, "mergeTwoFaces"], [330, 23, 352, 21], [330, 26, 352, 24, "mergeTwoFaces"], [330, 27, 352, 25, "face1"], [330, 32, 352, 35], [330, 34, 352, 37, "face2"], [330, 39, 352, 47], [330, 44, 352, 52], [331, 6, 353, 4], [331, 12, 353, 10, "box1"], [331, 16, 353, 14], [331, 19, 353, 17, "face1"], [331, 24, 353, 22], [331, 25, 353, 23, "boundingBox"], [331, 36, 353, 34], [332, 6, 354, 4], [332, 12, 354, 10, "box2"], [332, 16, 354, 14], [332, 19, 354, 17, "face2"], [332, 24, 354, 22], [332, 25, 354, 23, "boundingBox"], [332, 36, 354, 34], [333, 6, 356, 4], [333, 12, 356, 10, "left"], [333, 16, 356, 14], [333, 19, 356, 17, "Math"], [333, 23, 356, 21], [333, 24, 356, 22, "min"], [333, 27, 356, 25], [333, 28, 356, 26, "box1"], [333, 32, 356, 30], [333, 33, 356, 31, "xCenter"], [333, 40, 356, 38], [333, 43, 356, 41, "box1"], [333, 47, 356, 45], [333, 48, 356, 46, "width"], [333, 53, 356, 51], [333, 56, 356, 52], [333, 57, 356, 53], [333, 59, 356, 55, "box2"], [333, 63, 356, 59], [333, 64, 356, 60, "xCenter"], [333, 71, 356, 67], [333, 74, 356, 70, "box2"], [333, 78, 356, 74], [333, 79, 356, 75, "width"], [333, 84, 356, 80], [333, 87, 356, 81], [333, 88, 356, 82], [333, 89, 356, 83], [334, 6, 357, 4], [334, 12, 357, 10, "right"], [334, 17, 357, 15], [334, 20, 357, 18, "Math"], [334, 24, 357, 22], [334, 25, 357, 23, "max"], [334, 28, 357, 26], [334, 29, 357, 27, "box1"], [334, 33, 357, 31], [334, 34, 357, 32, "xCenter"], [334, 41, 357, 39], [334, 44, 357, 42, "box1"], [334, 48, 357, 46], [334, 49, 357, 47, "width"], [334, 54, 357, 52], [334, 57, 357, 53], [334, 58, 357, 54], [334, 60, 357, 56, "box2"], [334, 64, 357, 60], [334, 65, 357, 61, "xCenter"], [334, 72, 357, 68], [334, 75, 357, 71, "box2"], [334, 79, 357, 75], [334, 80, 357, 76, "width"], [334, 85, 357, 81], [334, 88, 357, 82], [334, 89, 357, 83], [334, 90, 357, 84], [335, 6, 358, 4], [335, 12, 358, 10, "top"], [335, 15, 358, 13], [335, 18, 358, 16, "Math"], [335, 22, 358, 20], [335, 23, 358, 21, "min"], [335, 26, 358, 24], [335, 27, 358, 25, "box1"], [335, 31, 358, 29], [335, 32, 358, 30, "yCenter"], [335, 39, 358, 37], [335, 42, 358, 40, "box1"], [335, 46, 358, 44], [335, 47, 358, 45, "height"], [335, 53, 358, 51], [335, 56, 358, 52], [335, 57, 358, 53], [335, 59, 358, 55, "box2"], [335, 63, 358, 59], [335, 64, 358, 60, "yCenter"], [335, 71, 358, 67], [335, 74, 358, 70, "box2"], [335, 78, 358, 74], [335, 79, 358, 75, "height"], [335, 85, 358, 81], [335, 88, 358, 82], [335, 89, 358, 83], [335, 90, 358, 84], [336, 6, 359, 4], [336, 12, 359, 10, "bottom"], [336, 18, 359, 16], [336, 21, 359, 19, "Math"], [336, 25, 359, 23], [336, 26, 359, 24, "max"], [336, 29, 359, 27], [336, 30, 359, 28, "box1"], [336, 34, 359, 32], [336, 35, 359, 33, "yCenter"], [336, 42, 359, 40], [336, 45, 359, 43, "box1"], [336, 49, 359, 47], [336, 50, 359, 48, "height"], [336, 56, 359, 54], [336, 59, 359, 55], [336, 60, 359, 56], [336, 62, 359, 58, "box2"], [336, 66, 359, 62], [336, 67, 359, 63, "yCenter"], [336, 74, 359, 70], [336, 77, 359, 73, "box2"], [336, 81, 359, 77], [336, 82, 359, 78, "height"], [336, 88, 359, 84], [336, 91, 359, 85], [336, 92, 359, 86], [336, 93, 359, 87], [337, 6, 361, 4], [337, 13, 361, 11], [338, 8, 362, 6, "boundingBox"], [338, 19, 362, 17], [338, 21, 362, 19], [339, 10, 363, 8, "xCenter"], [339, 17, 363, 15], [339, 19, 363, 17], [339, 20, 363, 18, "left"], [339, 24, 363, 22], [339, 27, 363, 25, "right"], [339, 32, 363, 30], [339, 36, 363, 34], [339, 37, 363, 35], [340, 10, 364, 8, "yCenter"], [340, 17, 364, 15], [340, 19, 364, 17], [340, 20, 364, 18, "top"], [340, 23, 364, 21], [340, 26, 364, 24, "bottom"], [340, 32, 364, 30], [340, 36, 364, 34], [340, 37, 364, 35], [341, 10, 365, 8, "width"], [341, 15, 365, 13], [341, 17, 365, 15, "right"], [341, 22, 365, 20], [341, 25, 365, 23, "left"], [341, 29, 365, 27], [342, 10, 366, 8, "height"], [342, 16, 366, 14], [342, 18, 366, 16, "bottom"], [342, 24, 366, 22], [342, 27, 366, 25, "top"], [343, 8, 367, 6], [344, 6, 368, 4], [344, 7, 368, 5], [345, 4, 369, 2], [345, 5, 369, 3], [347, 4, 371, 2], [348, 4, 372, 2], [348, 10, 372, 8, "applyStrongBlur"], [348, 25, 372, 23], [348, 28, 372, 26, "applyStrongBlur"], [348, 29, 372, 27, "ctx"], [348, 32, 372, 56], [348, 34, 372, 58, "x"], [348, 35, 372, 67], [348, 37, 372, 69, "y"], [348, 38, 372, 78], [348, 40, 372, 80, "width"], [348, 45, 372, 93], [348, 47, 372, 95, "height"], [348, 53, 372, 109], [348, 58, 372, 114], [349, 6, 373, 4], [350, 6, 374, 4], [350, 12, 374, 10, "canvasWidth"], [350, 23, 374, 21], [350, 26, 374, 24, "ctx"], [350, 29, 374, 27], [350, 30, 374, 28, "canvas"], [350, 36, 374, 34], [350, 37, 374, 35, "width"], [350, 42, 374, 40], [351, 6, 375, 4], [351, 12, 375, 10, "canvasHeight"], [351, 24, 375, 22], [351, 27, 375, 25, "ctx"], [351, 30, 375, 28], [351, 31, 375, 29, "canvas"], [351, 37, 375, 35], [351, 38, 375, 36, "height"], [351, 44, 375, 42], [352, 6, 377, 4], [352, 12, 377, 10, "clampedX"], [352, 20, 377, 18], [352, 23, 377, 21, "Math"], [352, 27, 377, 25], [352, 28, 377, 26, "max"], [352, 31, 377, 29], [352, 32, 377, 30], [352, 33, 377, 31], [352, 35, 377, 33, "Math"], [352, 39, 377, 37], [352, 40, 377, 38, "min"], [352, 43, 377, 41], [352, 44, 377, 42, "Math"], [352, 48, 377, 46], [352, 49, 377, 47, "floor"], [352, 54, 377, 52], [352, 55, 377, 53, "x"], [352, 56, 377, 54], [352, 57, 377, 55], [352, 59, 377, 57, "canvasWidth"], [352, 70, 377, 68], [352, 73, 377, 71], [352, 74, 377, 72], [352, 75, 377, 73], [352, 76, 377, 74], [353, 6, 378, 4], [353, 12, 378, 10, "clampedY"], [353, 20, 378, 18], [353, 23, 378, 21, "Math"], [353, 27, 378, 25], [353, 28, 378, 26, "max"], [353, 31, 378, 29], [353, 32, 378, 30], [353, 33, 378, 31], [353, 35, 378, 33, "Math"], [353, 39, 378, 37], [353, 40, 378, 38, "min"], [353, 43, 378, 41], [353, 44, 378, 42, "Math"], [353, 48, 378, 46], [353, 49, 378, 47, "floor"], [353, 54, 378, 52], [353, 55, 378, 53, "y"], [353, 56, 378, 54], [353, 57, 378, 55], [353, 59, 378, 57, "canvasHeight"], [353, 71, 378, 69], [353, 74, 378, 72], [353, 75, 378, 73], [353, 76, 378, 74], [353, 77, 378, 75], [354, 6, 379, 4], [354, 12, 379, 10, "<PERSON><PERSON><PERSON><PERSON>"], [354, 24, 379, 22], [354, 27, 379, 25, "Math"], [354, 31, 379, 29], [354, 32, 379, 30, "min"], [354, 35, 379, 33], [354, 36, 379, 34, "Math"], [354, 40, 379, 38], [354, 41, 379, 39, "floor"], [354, 46, 379, 44], [354, 47, 379, 45, "width"], [354, 52, 379, 50], [354, 53, 379, 51], [354, 55, 379, 53, "canvasWidth"], [354, 66, 379, 64], [354, 69, 379, 67, "clampedX"], [354, 77, 379, 75], [354, 78, 379, 76], [355, 6, 380, 4], [355, 12, 380, 10, "clampedHeight"], [355, 25, 380, 23], [355, 28, 380, 26, "Math"], [355, 32, 380, 30], [355, 33, 380, 31, "min"], [355, 36, 380, 34], [355, 37, 380, 35, "Math"], [355, 41, 380, 39], [355, 42, 380, 40, "floor"], [355, 47, 380, 45], [355, 48, 380, 46, "height"], [355, 54, 380, 52], [355, 55, 380, 53], [355, 57, 380, 55, "canvasHeight"], [355, 69, 380, 67], [355, 72, 380, 70, "clampedY"], [355, 80, 380, 78], [355, 81, 380, 79], [356, 6, 382, 4], [356, 10, 382, 8, "<PERSON><PERSON><PERSON><PERSON>"], [356, 22, 382, 20], [356, 26, 382, 24], [356, 27, 382, 25], [356, 31, 382, 29, "clampedHeight"], [356, 44, 382, 42], [356, 48, 382, 46], [356, 49, 382, 47], [356, 51, 382, 49], [357, 8, 383, 6, "console"], [357, 15, 383, 13], [357, 16, 383, 14, "warn"], [357, 20, 383, 18], [357, 21, 383, 19], [357, 72, 383, 70], [357, 73, 383, 71], [358, 8, 384, 6], [359, 6, 385, 4], [361, 6, 387, 4], [362, 6, 388, 4], [362, 12, 388, 10, "imageData"], [362, 21, 388, 19], [362, 24, 388, 22, "ctx"], [362, 27, 388, 25], [362, 28, 388, 26, "getImageData"], [362, 40, 388, 38], [362, 41, 388, 39, "clampedX"], [362, 49, 388, 47], [362, 51, 388, 49, "clampedY"], [362, 59, 388, 57], [362, 61, 388, 59, "<PERSON><PERSON><PERSON><PERSON>"], [362, 73, 388, 71], [362, 75, 388, 73, "clampedHeight"], [362, 88, 388, 86], [362, 89, 388, 87], [363, 6, 389, 4], [363, 12, 389, 10, "data"], [363, 16, 389, 14], [363, 19, 389, 17, "imageData"], [363, 28, 389, 26], [363, 29, 389, 27, "data"], [363, 33, 389, 31], [365, 6, 391, 4], [366, 6, 392, 4], [366, 12, 392, 10, "pixelSize"], [366, 21, 392, 19], [366, 24, 392, 22, "Math"], [366, 28, 392, 26], [366, 29, 392, 27, "max"], [366, 32, 392, 30], [366, 33, 392, 31], [366, 35, 392, 33], [366, 37, 392, 35, "Math"], [366, 41, 392, 39], [366, 42, 392, 40, "min"], [366, 45, 392, 43], [366, 46, 392, 44, "<PERSON><PERSON><PERSON><PERSON>"], [366, 58, 392, 56], [366, 60, 392, 58, "clampedHeight"], [366, 73, 392, 71], [366, 74, 392, 72], [366, 77, 392, 75], [366, 78, 392, 76], [366, 79, 392, 77], [367, 6, 394, 4], [367, 11, 394, 9], [367, 15, 394, 13, "py"], [367, 17, 394, 15], [367, 20, 394, 18], [367, 21, 394, 19], [367, 23, 394, 21, "py"], [367, 25, 394, 23], [367, 28, 394, 26, "clampedHeight"], [367, 41, 394, 39], [367, 43, 394, 41, "py"], [367, 45, 394, 43], [367, 49, 394, 47, "pixelSize"], [367, 58, 394, 56], [367, 60, 394, 58], [368, 8, 395, 6], [368, 13, 395, 11], [368, 17, 395, 15, "px"], [368, 19, 395, 17], [368, 22, 395, 20], [368, 23, 395, 21], [368, 25, 395, 23, "px"], [368, 27, 395, 25], [368, 30, 395, 28, "<PERSON><PERSON><PERSON><PERSON>"], [368, 42, 395, 40], [368, 44, 395, 42, "px"], [368, 46, 395, 44], [368, 50, 395, 48, "pixelSize"], [368, 59, 395, 57], [368, 61, 395, 59], [369, 10, 396, 8], [370, 10, 397, 8], [370, 14, 397, 12, "r"], [370, 15, 397, 13], [370, 18, 397, 16], [370, 19, 397, 17], [371, 12, 397, 19, "g"], [371, 13, 397, 20], [371, 16, 397, 23], [371, 17, 397, 24], [372, 12, 397, 26, "b"], [372, 13, 397, 27], [372, 16, 397, 30], [372, 17, 397, 31], [373, 12, 397, 33, "count"], [373, 17, 397, 38], [373, 20, 397, 41], [373, 21, 397, 42], [374, 10, 399, 8], [374, 15, 399, 13], [374, 19, 399, 17, "dy"], [374, 21, 399, 19], [374, 24, 399, 22], [374, 25, 399, 23], [374, 27, 399, 25, "dy"], [374, 29, 399, 27], [374, 32, 399, 30, "pixelSize"], [374, 41, 399, 39], [374, 45, 399, 43, "py"], [374, 47, 399, 45], [374, 50, 399, 48, "dy"], [374, 52, 399, 50], [374, 55, 399, 53, "clampedHeight"], [374, 68, 399, 66], [374, 70, 399, 68, "dy"], [374, 72, 399, 70], [374, 74, 399, 72], [374, 76, 399, 74], [375, 12, 400, 10], [375, 17, 400, 15], [375, 21, 400, 19, "dx"], [375, 23, 400, 21], [375, 26, 400, 24], [375, 27, 400, 25], [375, 29, 400, 27, "dx"], [375, 31, 400, 29], [375, 34, 400, 32, "pixelSize"], [375, 43, 400, 41], [375, 47, 400, 45, "px"], [375, 49, 400, 47], [375, 52, 400, 50, "dx"], [375, 54, 400, 52], [375, 57, 400, 55, "<PERSON><PERSON><PERSON><PERSON>"], [375, 69, 400, 67], [375, 71, 400, 69, "dx"], [375, 73, 400, 71], [375, 75, 400, 73], [375, 77, 400, 75], [376, 14, 401, 12], [376, 20, 401, 18, "index"], [376, 25, 401, 23], [376, 28, 401, 26], [376, 29, 401, 27], [376, 30, 401, 28, "py"], [376, 32, 401, 30], [376, 35, 401, 33, "dy"], [376, 37, 401, 35], [376, 41, 401, 39, "<PERSON><PERSON><PERSON><PERSON>"], [376, 53, 401, 51], [376, 57, 401, 55, "px"], [376, 59, 401, 57], [376, 62, 401, 60, "dx"], [376, 64, 401, 62], [376, 65, 401, 63], [376, 69, 401, 67], [376, 70, 401, 68], [377, 14, 402, 12, "r"], [377, 15, 402, 13], [377, 19, 402, 17, "data"], [377, 23, 402, 21], [377, 24, 402, 22, "index"], [377, 29, 402, 27], [377, 30, 402, 28], [378, 14, 403, 12, "g"], [378, 15, 403, 13], [378, 19, 403, 17, "data"], [378, 23, 403, 21], [378, 24, 403, 22, "index"], [378, 29, 403, 27], [378, 32, 403, 30], [378, 33, 403, 31], [378, 34, 403, 32], [379, 14, 404, 12, "b"], [379, 15, 404, 13], [379, 19, 404, 17, "data"], [379, 23, 404, 21], [379, 24, 404, 22, "index"], [379, 29, 404, 27], [379, 32, 404, 30], [379, 33, 404, 31], [379, 34, 404, 32], [380, 14, 405, 12, "count"], [380, 19, 405, 17], [380, 21, 405, 19], [381, 12, 406, 10], [382, 10, 407, 8], [383, 10, 409, 8], [383, 14, 409, 12, "count"], [383, 19, 409, 17], [383, 22, 409, 20], [383, 23, 409, 21], [383, 25, 409, 23], [384, 12, 410, 10, "r"], [384, 13, 410, 11], [384, 16, 410, 14, "Math"], [384, 20, 410, 18], [384, 21, 410, 19, "floor"], [384, 26, 410, 24], [384, 27, 410, 25, "r"], [384, 28, 410, 26], [384, 31, 410, 29, "count"], [384, 36, 410, 34], [384, 37, 410, 35], [385, 12, 411, 10, "g"], [385, 13, 411, 11], [385, 16, 411, 14, "Math"], [385, 20, 411, 18], [385, 21, 411, 19, "floor"], [385, 26, 411, 24], [385, 27, 411, 25, "g"], [385, 28, 411, 26], [385, 31, 411, 29, "count"], [385, 36, 411, 34], [385, 37, 411, 35], [386, 12, 412, 10, "b"], [386, 13, 412, 11], [386, 16, 412, 14, "Math"], [386, 20, 412, 18], [386, 21, 412, 19, "floor"], [386, 26, 412, 24], [386, 27, 412, 25, "b"], [386, 28, 412, 26], [386, 31, 412, 29, "count"], [386, 36, 412, 34], [386, 37, 412, 35], [388, 12, 414, 10], [389, 12, 415, 10], [389, 17, 415, 15], [389, 21, 415, 19, "dy"], [389, 23, 415, 21], [389, 26, 415, 24], [389, 27, 415, 25], [389, 29, 415, 27, "dy"], [389, 31, 415, 29], [389, 34, 415, 32, "pixelSize"], [389, 43, 415, 41], [389, 47, 415, 45, "py"], [389, 49, 415, 47], [389, 52, 415, 50, "dy"], [389, 54, 415, 52], [389, 57, 415, 55, "clampedHeight"], [389, 70, 415, 68], [389, 72, 415, 70, "dy"], [389, 74, 415, 72], [389, 76, 415, 74], [389, 78, 415, 76], [390, 14, 416, 12], [390, 19, 416, 17], [390, 23, 416, 21, "dx"], [390, 25, 416, 23], [390, 28, 416, 26], [390, 29, 416, 27], [390, 31, 416, 29, "dx"], [390, 33, 416, 31], [390, 36, 416, 34, "pixelSize"], [390, 45, 416, 43], [390, 49, 416, 47, "px"], [390, 51, 416, 49], [390, 54, 416, 52, "dx"], [390, 56, 416, 54], [390, 59, 416, 57, "<PERSON><PERSON><PERSON><PERSON>"], [390, 71, 416, 69], [390, 73, 416, 71, "dx"], [390, 75, 416, 73], [390, 77, 416, 75], [390, 79, 416, 77], [391, 16, 417, 14], [391, 22, 417, 20, "index"], [391, 27, 417, 25], [391, 30, 417, 28], [391, 31, 417, 29], [391, 32, 417, 30, "py"], [391, 34, 417, 32], [391, 37, 417, 35, "dy"], [391, 39, 417, 37], [391, 43, 417, 41, "<PERSON><PERSON><PERSON><PERSON>"], [391, 55, 417, 53], [391, 59, 417, 57, "px"], [391, 61, 417, 59], [391, 64, 417, 62, "dx"], [391, 66, 417, 64], [391, 67, 417, 65], [391, 71, 417, 69], [391, 72, 417, 70], [392, 16, 418, 14, "data"], [392, 20, 418, 18], [392, 21, 418, 19, "index"], [392, 26, 418, 24], [392, 27, 418, 25], [392, 30, 418, 28, "r"], [392, 31, 418, 29], [393, 16, 419, 14, "data"], [393, 20, 419, 18], [393, 21, 419, 19, "index"], [393, 26, 419, 24], [393, 29, 419, 27], [393, 30, 419, 28], [393, 31, 419, 29], [393, 34, 419, 32, "g"], [393, 35, 419, 33], [394, 16, 420, 14, "data"], [394, 20, 420, 18], [394, 21, 420, 19, "index"], [394, 26, 420, 24], [394, 29, 420, 27], [394, 30, 420, 28], [394, 31, 420, 29], [394, 34, 420, 32, "b"], [394, 35, 420, 33], [395, 16, 421, 14], [396, 14, 422, 12], [397, 12, 423, 10], [398, 10, 424, 8], [399, 8, 425, 6], [400, 6, 426, 4], [402, 6, 428, 4], [403, 6, 429, 4], [403, 11, 429, 9], [403, 15, 429, 13, "i"], [403, 16, 429, 14], [403, 19, 429, 17], [403, 20, 429, 18], [403, 22, 429, 20, "i"], [403, 23, 429, 21], [403, 26, 429, 24], [403, 27, 429, 25], [403, 29, 429, 27, "i"], [403, 30, 429, 28], [403, 32, 429, 30], [403, 34, 429, 32], [404, 8, 430, 6, "applySimpleBlur"], [404, 23, 430, 21], [404, 24, 430, 22, "data"], [404, 28, 430, 26], [404, 30, 430, 28, "<PERSON><PERSON><PERSON><PERSON>"], [404, 42, 430, 40], [404, 44, 430, 42, "clampedHeight"], [404, 57, 430, 55], [404, 58, 430, 56], [405, 6, 431, 4], [407, 6, 433, 4], [408, 6, 434, 4, "ctx"], [408, 9, 434, 7], [408, 10, 434, 8, "putImageData"], [408, 22, 434, 20], [408, 23, 434, 21, "imageData"], [408, 32, 434, 30], [408, 34, 434, 32, "clampedX"], [408, 42, 434, 40], [408, 44, 434, 42, "clampedY"], [408, 52, 434, 50], [408, 53, 434, 51], [410, 6, 436, 4], [411, 6, 437, 4, "ctx"], [411, 9, 437, 7], [411, 10, 437, 8, "fillStyle"], [411, 19, 437, 17], [411, 22, 437, 20], [411, 48, 437, 46], [412, 6, 438, 4, "ctx"], [412, 9, 438, 7], [412, 10, 438, 8, "fillRect"], [412, 18, 438, 16], [412, 19, 438, 17, "clampedX"], [412, 27, 438, 25], [412, 29, 438, 27, "clampedY"], [412, 37, 438, 35], [412, 39, 438, 37, "<PERSON><PERSON><PERSON><PERSON>"], [412, 51, 438, 49], [412, 53, 438, 51, "clampedHeight"], [412, 66, 438, 64], [412, 67, 438, 65], [413, 4, 439, 2], [413, 5, 439, 3], [414, 4, 441, 2], [414, 10, 441, 8, "applySimpleBlur"], [414, 25, 441, 23], [414, 28, 441, 26, "applySimpleBlur"], [414, 29, 441, 27, "data"], [414, 33, 441, 50], [414, 35, 441, 52, "width"], [414, 40, 441, 65], [414, 42, 441, 67, "height"], [414, 48, 441, 81], [414, 53, 441, 86], [415, 6, 442, 4], [415, 12, 442, 10, "original"], [415, 20, 442, 18], [415, 23, 442, 21], [415, 27, 442, 25, "Uint8ClampedArray"], [415, 44, 442, 42], [415, 45, 442, 43, "data"], [415, 49, 442, 47], [415, 50, 442, 48], [416, 6, 444, 4], [416, 11, 444, 9], [416, 15, 444, 13, "y"], [416, 16, 444, 14], [416, 19, 444, 17], [416, 20, 444, 18], [416, 22, 444, 20, "y"], [416, 23, 444, 21], [416, 26, 444, 24, "height"], [416, 32, 444, 30], [416, 35, 444, 33], [416, 36, 444, 34], [416, 38, 444, 36, "y"], [416, 39, 444, 37], [416, 41, 444, 39], [416, 43, 444, 41], [417, 8, 445, 6], [417, 13, 445, 11], [417, 17, 445, 15, "x"], [417, 18, 445, 16], [417, 21, 445, 19], [417, 22, 445, 20], [417, 24, 445, 22, "x"], [417, 25, 445, 23], [417, 28, 445, 26, "width"], [417, 33, 445, 31], [417, 36, 445, 34], [417, 37, 445, 35], [417, 39, 445, 37, "x"], [417, 40, 445, 38], [417, 42, 445, 40], [417, 44, 445, 42], [418, 10, 446, 8], [418, 16, 446, 14, "index"], [418, 21, 446, 19], [418, 24, 446, 22], [418, 25, 446, 23, "y"], [418, 26, 446, 24], [418, 29, 446, 27, "width"], [418, 34, 446, 32], [418, 37, 446, 35, "x"], [418, 38, 446, 36], [418, 42, 446, 40], [418, 43, 446, 41], [420, 10, 448, 8], [421, 10, 449, 8], [421, 14, 449, 12, "r"], [421, 15, 449, 13], [421, 18, 449, 16], [421, 19, 449, 17], [422, 12, 449, 19, "g"], [422, 13, 449, 20], [422, 16, 449, 23], [422, 17, 449, 24], [423, 12, 449, 26, "b"], [423, 13, 449, 27], [423, 16, 449, 30], [423, 17, 449, 31], [424, 10, 450, 8], [424, 15, 450, 13], [424, 19, 450, 17, "dy"], [424, 21, 450, 19], [424, 24, 450, 22], [424, 25, 450, 23], [424, 26, 450, 24], [424, 28, 450, 26, "dy"], [424, 30, 450, 28], [424, 34, 450, 32], [424, 35, 450, 33], [424, 37, 450, 35, "dy"], [424, 39, 450, 37], [424, 41, 450, 39], [424, 43, 450, 41], [425, 12, 451, 10], [425, 17, 451, 15], [425, 21, 451, 19, "dx"], [425, 23, 451, 21], [425, 26, 451, 24], [425, 27, 451, 25], [425, 28, 451, 26], [425, 30, 451, 28, "dx"], [425, 32, 451, 30], [425, 36, 451, 34], [425, 37, 451, 35], [425, 39, 451, 37, "dx"], [425, 41, 451, 39], [425, 43, 451, 41], [425, 45, 451, 43], [426, 14, 452, 12], [426, 20, 452, 18, "neighborIndex"], [426, 33, 452, 31], [426, 36, 452, 34], [426, 37, 452, 35], [426, 38, 452, 36, "y"], [426, 39, 452, 37], [426, 42, 452, 40, "dy"], [426, 44, 452, 42], [426, 48, 452, 46, "width"], [426, 53, 452, 51], [426, 57, 452, 55, "x"], [426, 58, 452, 56], [426, 61, 452, 59, "dx"], [426, 63, 452, 61], [426, 64, 452, 62], [426, 68, 452, 66], [426, 69, 452, 67], [427, 14, 453, 12, "r"], [427, 15, 453, 13], [427, 19, 453, 17, "original"], [427, 27, 453, 25], [427, 28, 453, 26, "neighborIndex"], [427, 41, 453, 39], [427, 42, 453, 40], [428, 14, 454, 12, "g"], [428, 15, 454, 13], [428, 19, 454, 17, "original"], [428, 27, 454, 25], [428, 28, 454, 26, "neighborIndex"], [428, 41, 454, 39], [428, 44, 454, 42], [428, 45, 454, 43], [428, 46, 454, 44], [429, 14, 455, 12, "b"], [429, 15, 455, 13], [429, 19, 455, 17, "original"], [429, 27, 455, 25], [429, 28, 455, 26, "neighborIndex"], [429, 41, 455, 39], [429, 44, 455, 42], [429, 45, 455, 43], [429, 46, 455, 44], [430, 12, 456, 10], [431, 10, 457, 8], [432, 10, 459, 8, "data"], [432, 14, 459, 12], [432, 15, 459, 13, "index"], [432, 20, 459, 18], [432, 21, 459, 19], [432, 24, 459, 22, "r"], [432, 25, 459, 23], [432, 28, 459, 26], [432, 29, 459, 27], [433, 10, 460, 8, "data"], [433, 14, 460, 12], [433, 15, 460, 13, "index"], [433, 20, 460, 18], [433, 23, 460, 21], [433, 24, 460, 22], [433, 25, 460, 23], [433, 28, 460, 26, "g"], [433, 29, 460, 27], [433, 32, 460, 30], [433, 33, 460, 31], [434, 10, 461, 8, "data"], [434, 14, 461, 12], [434, 15, 461, 13, "index"], [434, 20, 461, 18], [434, 23, 461, 21], [434, 24, 461, 22], [434, 25, 461, 23], [434, 28, 461, 26, "b"], [434, 29, 461, 27], [434, 32, 461, 30], [434, 33, 461, 31], [435, 8, 462, 6], [436, 6, 463, 4], [437, 4, 464, 2], [437, 5, 464, 3], [438, 4, 466, 2], [438, 10, 466, 8, "applyFallbackFaceBlur"], [438, 31, 466, 29], [438, 34, 466, 32, "applyFallbackFaceBlur"], [438, 35, 466, 33, "ctx"], [438, 38, 466, 62], [438, 40, 466, 64, "imgWidth"], [438, 48, 466, 80], [438, 50, 466, 82, "imgHeight"], [438, 59, 466, 99], [438, 64, 466, 104], [439, 6, 467, 4, "console"], [439, 13, 467, 11], [439, 14, 467, 12, "log"], [439, 17, 467, 15], [439, 18, 467, 16], [439, 90, 467, 88], [439, 91, 467, 89], [441, 6, 469, 4], [442, 6, 470, 4], [442, 12, 470, 10, "areas"], [442, 17, 470, 15], [442, 20, 470, 18], [443, 6, 471, 6], [444, 6, 472, 6], [445, 8, 472, 8, "x"], [445, 9, 472, 9], [445, 11, 472, 11, "imgWidth"], [445, 19, 472, 19], [445, 22, 472, 22], [445, 26, 472, 26], [446, 8, 472, 28, "y"], [446, 9, 472, 29], [446, 11, 472, 31, "imgHeight"], [446, 20, 472, 40], [446, 23, 472, 43], [446, 27, 472, 47], [447, 8, 472, 49, "w"], [447, 9, 472, 50], [447, 11, 472, 52, "imgWidth"], [447, 19, 472, 60], [447, 22, 472, 63], [447, 25, 472, 66], [448, 8, 472, 68, "h"], [448, 9, 472, 69], [448, 11, 472, 71, "imgHeight"], [448, 20, 472, 80], [448, 23, 472, 83], [449, 6, 472, 87], [449, 7, 472, 88], [450, 6, 473, 6], [451, 6, 474, 6], [452, 8, 474, 8, "x"], [452, 9, 474, 9], [452, 11, 474, 11, "imgWidth"], [452, 19, 474, 19], [452, 22, 474, 22], [452, 25, 474, 25], [453, 8, 474, 27, "y"], [453, 9, 474, 28], [453, 11, 474, 30, "imgHeight"], [453, 20, 474, 39], [453, 23, 474, 42], [453, 26, 474, 45], [454, 8, 474, 47, "w"], [454, 9, 474, 48], [454, 11, 474, 50, "imgWidth"], [454, 19, 474, 58], [454, 22, 474, 61], [454, 26, 474, 65], [455, 8, 474, 67, "h"], [455, 9, 474, 68], [455, 11, 474, 70, "imgHeight"], [455, 20, 474, 79], [455, 23, 474, 82], [456, 6, 474, 86], [456, 7, 474, 87], [457, 6, 475, 6], [458, 6, 476, 6], [459, 8, 476, 8, "x"], [459, 9, 476, 9], [459, 11, 476, 11, "imgWidth"], [459, 19, 476, 19], [459, 22, 476, 22], [459, 26, 476, 26], [460, 8, 476, 28, "y"], [460, 9, 476, 29], [460, 11, 476, 31, "imgHeight"], [460, 20, 476, 40], [460, 23, 476, 43], [460, 26, 476, 46], [461, 8, 476, 48, "w"], [461, 9, 476, 49], [461, 11, 476, 51, "imgWidth"], [461, 19, 476, 59], [461, 22, 476, 62], [461, 26, 476, 66], [462, 8, 476, 68, "h"], [462, 9, 476, 69], [462, 11, 476, 71, "imgHeight"], [462, 20, 476, 80], [462, 23, 476, 83], [463, 6, 476, 87], [463, 7, 476, 88], [463, 8, 477, 5], [464, 6, 479, 4, "areas"], [464, 11, 479, 9], [464, 12, 479, 10, "for<PERSON>ach"], [464, 19, 479, 17], [464, 20, 479, 18], [464, 21, 479, 19, "area"], [464, 25, 479, 23], [464, 27, 479, 25, "index"], [464, 32, 479, 30], [464, 37, 479, 35], [465, 8, 480, 6, "console"], [465, 15, 480, 13], [465, 16, 480, 14, "log"], [465, 19, 480, 17], [465, 20, 480, 18], [465, 65, 480, 63, "index"], [465, 70, 480, 68], [465, 73, 480, 71], [465, 74, 480, 72], [465, 77, 480, 75], [465, 79, 480, 77, "area"], [465, 83, 480, 81], [465, 84, 480, 82], [466, 8, 481, 6, "applyStrongBlur"], [466, 23, 481, 21], [466, 24, 481, 22, "ctx"], [466, 27, 481, 25], [466, 29, 481, 27, "area"], [466, 33, 481, 31], [466, 34, 481, 32, "x"], [466, 35, 481, 33], [466, 37, 481, 35, "area"], [466, 41, 481, 39], [466, 42, 481, 40, "y"], [466, 43, 481, 41], [466, 45, 481, 43, "area"], [466, 49, 481, 47], [466, 50, 481, 48, "w"], [466, 51, 481, 49], [466, 53, 481, 51, "area"], [466, 57, 481, 55], [466, 58, 481, 56, "h"], [466, 59, 481, 57], [466, 60, 481, 58], [467, 6, 482, 4], [467, 7, 482, 5], [467, 8, 482, 6], [468, 4, 483, 2], [468, 5, 483, 3], [470, 4, 485, 2], [471, 4, 486, 2], [471, 10, 486, 8, "capturePhoto"], [471, 22, 486, 20], [471, 25, 486, 23], [471, 29, 486, 23, "useCallback"], [471, 47, 486, 34], [471, 49, 486, 35], [471, 61, 486, 47], [472, 6, 487, 4], [473, 6, 488, 4], [473, 12, 488, 10, "isDev"], [473, 17, 488, 15], [473, 20, 488, 18, "process"], [473, 27, 488, 25], [473, 28, 488, 26, "env"], [473, 31, 488, 29], [473, 32, 488, 30, "NODE_ENV"], [473, 40, 488, 38], [473, 45, 488, 43], [473, 58, 488, 56], [473, 62, 488, 60, "__DEV__"], [473, 69, 488, 67], [474, 6, 490, 4], [474, 10, 490, 8], [474, 11, 490, 9, "cameraRef"], [474, 20, 490, 18], [474, 21, 490, 19, "current"], [474, 28, 490, 26], [474, 32, 490, 30], [474, 33, 490, 31, "isDev"], [474, 38, 490, 36], [474, 40, 490, 38], [475, 8, 491, 6, "<PERSON><PERSON>"], [475, 22, 491, 11], [475, 23, 491, 12, "alert"], [475, 28, 491, 17], [475, 29, 491, 18], [475, 36, 491, 25], [475, 38, 491, 27], [475, 56, 491, 45], [475, 57, 491, 46], [476, 8, 492, 6], [477, 6, 493, 4], [478, 6, 494, 4], [478, 10, 494, 8], [479, 8, 495, 6, "setProcessingState"], [479, 26, 495, 24], [479, 27, 495, 25], [479, 38, 495, 36], [479, 39, 495, 37], [480, 8, 496, 6, "setProcessingProgress"], [480, 29, 496, 27], [480, 30, 496, 28], [480, 32, 496, 30], [480, 33, 496, 31], [481, 8, 497, 6], [482, 8, 498, 6], [483, 8, 499, 6], [484, 8, 500, 6], [484, 14, 500, 12], [484, 18, 500, 16, "Promise"], [484, 25, 500, 23], [484, 26, 500, 24, "resolve"], [484, 33, 500, 31], [484, 37, 500, 35, "setTimeout"], [484, 47, 500, 45], [484, 48, 500, 46, "resolve"], [484, 55, 500, 53], [484, 57, 500, 55], [484, 59, 500, 57], [484, 60, 500, 58], [484, 61, 500, 59], [485, 8, 501, 6], [486, 8, 502, 6], [486, 12, 502, 10, "photo"], [486, 17, 502, 15], [487, 8, 504, 6], [487, 12, 504, 10], [488, 10, 505, 8, "photo"], [488, 15, 505, 13], [488, 18, 505, 16], [488, 24, 505, 22, "cameraRef"], [488, 33, 505, 31], [488, 34, 505, 32, "current"], [488, 41, 505, 39], [488, 42, 505, 40, "takePictureAsync"], [488, 58, 505, 56], [488, 59, 505, 57], [489, 12, 506, 10, "quality"], [489, 19, 506, 17], [489, 21, 506, 19], [489, 24, 506, 22], [490, 12, 507, 10, "base64"], [490, 18, 507, 16], [490, 20, 507, 18], [490, 25, 507, 23], [491, 12, 508, 10, "skipProcessing"], [491, 26, 508, 24], [491, 28, 508, 26], [491, 32, 508, 30], [491, 33, 508, 32], [492, 10, 509, 8], [492, 11, 509, 9], [492, 12, 509, 10], [493, 8, 510, 6], [493, 9, 510, 7], [493, 10, 510, 8], [493, 17, 510, 15, "cameraError"], [493, 28, 510, 26], [493, 30, 510, 28], [494, 10, 511, 8, "console"], [494, 17, 511, 15], [494, 18, 511, 16, "log"], [494, 21, 511, 19], [494, 22, 511, 20], [494, 82, 511, 80], [494, 84, 511, 82, "cameraError"], [494, 95, 511, 93], [494, 96, 511, 94], [495, 10, 512, 8], [496, 10, 513, 8], [496, 14, 513, 12, "isDev"], [496, 19, 513, 17], [496, 21, 513, 19], [497, 12, 514, 10, "photo"], [497, 17, 514, 15], [497, 20, 514, 18], [498, 14, 515, 12, "uri"], [498, 17, 515, 15], [498, 19, 515, 17], [499, 12, 516, 10], [499, 13, 516, 11], [500, 10, 517, 8], [500, 11, 517, 9], [500, 17, 517, 15], [501, 12, 518, 10], [501, 18, 518, 16, "cameraError"], [501, 29, 518, 27], [502, 10, 519, 8], [503, 8, 520, 6], [504, 8, 521, 6], [504, 12, 521, 10], [504, 13, 521, 11, "photo"], [504, 18, 521, 16], [504, 20, 521, 18], [505, 10, 522, 8], [505, 16, 522, 14], [505, 20, 522, 18, "Error"], [505, 25, 522, 23], [505, 26, 522, 24], [505, 51, 522, 49], [505, 52, 522, 50], [506, 8, 523, 6], [507, 8, 524, 6, "console"], [507, 15, 524, 13], [507, 16, 524, 14, "log"], [507, 19, 524, 17], [507, 20, 524, 18], [507, 56, 524, 54], [507, 58, 524, 56, "photo"], [507, 63, 524, 61], [507, 64, 524, 62, "uri"], [507, 67, 524, 65], [507, 68, 524, 66], [508, 8, 525, 6, "setCapturedPhoto"], [508, 24, 525, 22], [508, 25, 525, 23, "photo"], [508, 30, 525, 28], [508, 31, 525, 29, "uri"], [508, 34, 525, 32], [508, 35, 525, 33], [509, 8, 526, 6, "setProcessingProgress"], [509, 29, 526, 27], [509, 30, 526, 28], [509, 32, 526, 30], [509, 33, 526, 31], [510, 8, 527, 6], [511, 8, 528, 6, "console"], [511, 15, 528, 13], [511, 16, 528, 14, "log"], [511, 19, 528, 17], [511, 20, 528, 18], [511, 73, 528, 71], [511, 74, 528, 72], [512, 8, 529, 6], [512, 14, 529, 12, "processImageWithFaceBlur"], [512, 38, 529, 36], [512, 39, 529, 37, "photo"], [512, 44, 529, 42], [512, 45, 529, 43, "uri"], [512, 48, 529, 46], [512, 49, 529, 47], [513, 8, 530, 6, "console"], [513, 15, 530, 13], [513, 16, 530, 14, "log"], [513, 19, 530, 17], [513, 20, 530, 18], [513, 71, 530, 69], [513, 72, 530, 70], [514, 6, 531, 4], [514, 7, 531, 5], [514, 8, 531, 6], [514, 15, 531, 13, "error"], [514, 20, 531, 18], [514, 22, 531, 20], [515, 8, 532, 6, "console"], [515, 15, 532, 13], [515, 16, 532, 14, "error"], [515, 21, 532, 19], [515, 22, 532, 20], [515, 54, 532, 52], [515, 56, 532, 54, "error"], [515, 61, 532, 59], [515, 62, 532, 60], [516, 8, 533, 6, "setErrorMessage"], [516, 23, 533, 21], [516, 24, 533, 22], [516, 68, 533, 66], [516, 69, 533, 67], [517, 8, 534, 6, "setProcessingState"], [517, 26, 534, 24], [517, 27, 534, 25], [517, 34, 534, 32], [517, 35, 534, 33], [518, 6, 535, 4], [519, 4, 536, 2], [519, 5, 536, 3], [519, 7, 536, 5], [519, 9, 536, 7], [519, 10, 536, 8], [520, 4, 537, 2], [521, 4, 538, 2], [521, 10, 538, 8, "processImageWithFaceBlur"], [521, 34, 538, 32], [521, 37, 538, 35], [521, 43, 538, 42, "photoUri"], [521, 51, 538, 58], [521, 55, 538, 63], [522, 6, 539, 4], [522, 10, 539, 8], [523, 8, 540, 6, "console"], [523, 15, 540, 13], [523, 16, 540, 14, "log"], [523, 19, 540, 17], [523, 20, 540, 18], [523, 84, 540, 82], [523, 85, 540, 83], [524, 8, 541, 6, "setProcessingState"], [524, 26, 541, 24], [524, 27, 541, 25], [524, 39, 541, 37], [524, 40, 541, 38], [525, 8, 542, 6, "setProcessingProgress"], [525, 29, 542, 27], [525, 30, 542, 28], [525, 32, 542, 30], [525, 33, 542, 31], [527, 8, 544, 6], [528, 8, 545, 6], [528, 14, 545, 12, "canvas"], [528, 20, 545, 18], [528, 23, 545, 21, "document"], [528, 31, 545, 29], [528, 32, 545, 30, "createElement"], [528, 45, 545, 43], [528, 46, 545, 44], [528, 54, 545, 52], [528, 55, 545, 53], [529, 8, 546, 6], [529, 14, 546, 12, "ctx"], [529, 17, 546, 15], [529, 20, 546, 18, "canvas"], [529, 26, 546, 24], [529, 27, 546, 25, "getContext"], [529, 37, 546, 35], [529, 38, 546, 36], [529, 42, 546, 40], [529, 43, 546, 41], [530, 8, 547, 6], [530, 12, 547, 10], [530, 13, 547, 11, "ctx"], [530, 16, 547, 14], [530, 18, 547, 16], [530, 24, 547, 22], [530, 28, 547, 26, "Error"], [530, 33, 547, 31], [530, 34, 547, 32], [530, 64, 547, 62], [530, 65, 547, 63], [532, 8, 549, 6], [533, 8, 550, 6], [533, 14, 550, 12, "img"], [533, 17, 550, 15], [533, 20, 550, 18], [533, 24, 550, 22, "Image"], [533, 29, 550, 27], [533, 30, 550, 28], [533, 31, 550, 29], [534, 8, 551, 6], [534, 14, 551, 12], [534, 18, 551, 16, "Promise"], [534, 25, 551, 23], [534, 26, 551, 24], [534, 27, 551, 25, "resolve"], [534, 34, 551, 32], [534, 36, 551, 34, "reject"], [534, 42, 551, 40], [534, 47, 551, 45], [535, 10, 552, 8, "img"], [535, 13, 552, 11], [535, 14, 552, 12, "onload"], [535, 20, 552, 18], [535, 23, 552, 21, "resolve"], [535, 30, 552, 28], [536, 10, 553, 8, "img"], [536, 13, 553, 11], [536, 14, 553, 12, "onerror"], [536, 21, 553, 19], [536, 24, 553, 22, "reject"], [536, 30, 553, 28], [537, 10, 554, 8, "img"], [537, 13, 554, 11], [537, 14, 554, 12, "src"], [537, 17, 554, 15], [537, 20, 554, 18, "photoUri"], [537, 28, 554, 26], [538, 8, 555, 6], [538, 9, 555, 7], [538, 10, 555, 8], [540, 8, 557, 6], [541, 8, 558, 6, "canvas"], [541, 14, 558, 12], [541, 15, 558, 13, "width"], [541, 20, 558, 18], [541, 23, 558, 21, "img"], [541, 26, 558, 24], [541, 27, 558, 25, "width"], [541, 32, 558, 30], [542, 8, 559, 6, "canvas"], [542, 14, 559, 12], [542, 15, 559, 13, "height"], [542, 21, 559, 19], [542, 24, 559, 22, "img"], [542, 27, 559, 25], [542, 28, 559, 26, "height"], [542, 34, 559, 32], [543, 8, 560, 6, "console"], [543, 15, 560, 13], [543, 16, 560, 14, "log"], [543, 19, 560, 17], [543, 20, 560, 18], [543, 54, 560, 52], [543, 56, 560, 54], [544, 10, 560, 56, "width"], [544, 15, 560, 61], [544, 17, 560, 63, "img"], [544, 20, 560, 66], [544, 21, 560, 67, "width"], [544, 26, 560, 72], [545, 10, 560, 74, "height"], [545, 16, 560, 80], [545, 18, 560, 82, "img"], [545, 21, 560, 85], [545, 22, 560, 86, "height"], [546, 8, 560, 93], [546, 9, 560, 94], [546, 10, 560, 95], [548, 8, 562, 6], [549, 8, 563, 6, "ctx"], [549, 11, 563, 9], [549, 12, 563, 10, "drawImage"], [549, 21, 563, 19], [549, 22, 563, 20, "img"], [549, 25, 563, 23], [549, 27, 563, 25], [549, 28, 563, 26], [549, 30, 563, 28], [549, 31, 563, 29], [549, 32, 563, 30], [550, 8, 564, 6, "console"], [550, 15, 564, 13], [550, 16, 564, 14, "log"], [550, 19, 564, 17], [550, 20, 564, 18], [550, 72, 564, 70], [550, 73, 564, 71], [551, 8, 566, 6, "setProcessingProgress"], [551, 29, 566, 27], [551, 30, 566, 28], [551, 32, 566, 30], [551, 33, 566, 31], [553, 8, 568, 6], [554, 8, 569, 6], [554, 12, 569, 10, "detectedFaces"], [554, 25, 569, 23], [554, 28, 569, 26], [554, 30, 569, 28], [555, 8, 571, 6, "console"], [555, 15, 571, 13], [555, 16, 571, 14, "log"], [555, 19, 571, 17], [555, 20, 571, 18], [555, 81, 571, 79], [555, 82, 571, 80], [557, 8, 573, 6], [558, 8, 574, 6], [558, 12, 574, 10], [559, 10, 575, 8, "console"], [559, 17, 575, 15], [559, 18, 575, 16, "log"], [559, 21, 575, 19], [559, 22, 575, 20], [559, 81, 575, 79], [559, 82, 575, 80], [560, 10, 576, 8], [560, 16, 576, 14, "loadTensorFlowFaceDetection"], [560, 43, 576, 41], [560, 44, 576, 42], [560, 45, 576, 43], [561, 10, 577, 8, "console"], [561, 17, 577, 15], [561, 18, 577, 16, "log"], [561, 21, 577, 19], [561, 22, 577, 20], [561, 90, 577, 88], [561, 91, 577, 89], [562, 10, 578, 8, "detectedFaces"], [562, 23, 578, 21], [562, 26, 578, 24], [562, 32, 578, 30, "detectFacesWithTensorFlow"], [562, 57, 578, 55], [562, 58, 578, 56, "img"], [562, 61, 578, 59], [562, 62, 578, 60], [563, 10, 579, 8, "console"], [563, 17, 579, 15], [563, 18, 579, 16, "log"], [563, 21, 579, 19], [563, 22, 579, 20], [563, 70, 579, 68, "detectedFaces"], [563, 83, 579, 81], [563, 84, 579, 82, "length"], [563, 90, 579, 88], [563, 98, 579, 96], [563, 99, 579, 97], [564, 8, 580, 6], [564, 9, 580, 7], [564, 10, 580, 8], [564, 17, 580, 15, "tensorFlowError"], [564, 32, 580, 30], [564, 34, 580, 32], [565, 10, 581, 8, "console"], [565, 17, 581, 15], [565, 18, 581, 16, "warn"], [565, 22, 581, 20], [565, 23, 581, 21], [565, 61, 581, 59], [565, 63, 581, 61, "tensorFlowError"], [565, 78, 581, 76], [565, 79, 581, 77], [566, 10, 582, 8, "console"], [566, 17, 582, 15], [566, 18, 582, 16, "warn"], [566, 22, 582, 20], [566, 23, 582, 21], [566, 68, 582, 66], [566, 70, 582, 68], [567, 12, 583, 10, "message"], [567, 19, 583, 17], [567, 21, 583, 19, "tensorFlowError"], [567, 36, 583, 34], [567, 37, 583, 35, "message"], [567, 44, 583, 42], [568, 12, 584, 10, "stack"], [568, 17, 584, 15], [568, 19, 584, 17, "tensorFlowError"], [568, 34, 584, 32], [568, 35, 584, 33, "stack"], [569, 10, 585, 8], [569, 11, 585, 9], [569, 12, 585, 10], [571, 10, 587, 8], [572, 10, 588, 8, "console"], [572, 17, 588, 15], [572, 18, 588, 16, "log"], [572, 21, 588, 19], [572, 22, 588, 20], [572, 86, 588, 84], [572, 87, 588, 85], [573, 10, 589, 8, "detectedFaces"], [573, 23, 589, 21], [573, 26, 589, 24, "detectFacesHeuristic"], [573, 46, 589, 44], [573, 47, 589, 45, "img"], [573, 50, 589, 48], [573, 52, 589, 50, "ctx"], [573, 55, 589, 53], [573, 56, 589, 54], [574, 10, 590, 8, "console"], [574, 17, 590, 15], [574, 18, 590, 16, "log"], [574, 21, 590, 19], [574, 22, 590, 20], [574, 70, 590, 68, "detectedFaces"], [574, 83, 590, 81], [574, 84, 590, 82, "length"], [574, 90, 590, 88], [574, 98, 590, 96], [574, 99, 590, 97], [575, 8, 591, 6], [577, 8, 593, 6], [578, 8, 594, 6], [578, 12, 594, 10, "detectedFaces"], [578, 25, 594, 23], [578, 26, 594, 24, "length"], [578, 32, 594, 30], [578, 37, 594, 35], [578, 38, 594, 36], [578, 40, 594, 38], [579, 10, 595, 8, "console"], [579, 17, 595, 15], [579, 18, 595, 16, "log"], [579, 21, 595, 19], [579, 22, 595, 20], [579, 89, 595, 87], [579, 90, 595, 88], [580, 10, 596, 8, "detectedFaces"], [580, 23, 596, 21], [580, 26, 596, 24, "detectFacesAggressive"], [580, 47, 596, 45], [580, 48, 596, 46, "img"], [580, 51, 596, 49], [580, 53, 596, 51, "ctx"], [580, 56, 596, 54], [580, 57, 596, 55], [581, 10, 597, 8, "console"], [581, 17, 597, 15], [581, 18, 597, 16, "log"], [581, 21, 597, 19], [581, 22, 597, 20], [581, 71, 597, 69, "detectedFaces"], [581, 84, 597, 82], [581, 85, 597, 83, "length"], [581, 91, 597, 89], [581, 99, 597, 97], [581, 100, 597, 98], [582, 8, 598, 6], [583, 8, 600, 6, "console"], [583, 15, 600, 13], [583, 16, 600, 14, "log"], [583, 19, 600, 17], [583, 20, 600, 18], [583, 72, 600, 70, "detectedFaces"], [583, 85, 600, 83], [583, 86, 600, 84, "length"], [583, 92, 600, 90], [583, 100, 600, 98], [583, 101, 600, 99], [584, 8, 601, 6], [584, 12, 601, 10, "detectedFaces"], [584, 25, 601, 23], [584, 26, 601, 24, "length"], [584, 32, 601, 30], [584, 35, 601, 33], [584, 36, 601, 34], [584, 38, 601, 36], [585, 10, 602, 8, "console"], [585, 17, 602, 15], [585, 18, 602, 16, "log"], [585, 21, 602, 19], [585, 22, 602, 20], [585, 66, 602, 64], [585, 68, 602, 66, "detectedFaces"], [585, 81, 602, 79], [585, 82, 602, 80, "map"], [585, 85, 602, 83], [585, 86, 602, 84], [585, 87, 602, 85, "face"], [585, 91, 602, 89], [585, 93, 602, 91, "i"], [585, 94, 602, 92], [585, 100, 602, 98], [586, 12, 603, 10, "faceNumber"], [586, 22, 603, 20], [586, 24, 603, 22, "i"], [586, 25, 603, 23], [586, 28, 603, 26], [586, 29, 603, 27], [587, 12, 604, 10, "centerX"], [587, 19, 604, 17], [587, 21, 604, 19, "face"], [587, 25, 604, 23], [587, 26, 604, 24, "boundingBox"], [587, 37, 604, 35], [587, 38, 604, 36, "xCenter"], [587, 45, 604, 43], [588, 12, 605, 10, "centerY"], [588, 19, 605, 17], [588, 21, 605, 19, "face"], [588, 25, 605, 23], [588, 26, 605, 24, "boundingBox"], [588, 37, 605, 35], [588, 38, 605, 36, "yCenter"], [588, 45, 605, 43], [589, 12, 606, 10, "width"], [589, 17, 606, 15], [589, 19, 606, 17, "face"], [589, 23, 606, 21], [589, 24, 606, 22, "boundingBox"], [589, 35, 606, 33], [589, 36, 606, 34, "width"], [589, 41, 606, 39], [590, 12, 607, 10, "height"], [590, 18, 607, 16], [590, 20, 607, 18, "face"], [590, 24, 607, 22], [590, 25, 607, 23, "boundingBox"], [590, 36, 607, 34], [590, 37, 607, 35, "height"], [591, 10, 608, 8], [591, 11, 608, 9], [591, 12, 608, 10], [591, 13, 608, 11], [591, 14, 608, 12], [592, 8, 609, 6], [592, 9, 609, 7], [592, 15, 609, 13], [593, 10, 610, 8, "console"], [593, 17, 610, 15], [593, 18, 610, 16, "log"], [593, 21, 610, 19], [593, 22, 610, 20], [593, 91, 610, 89], [593, 92, 610, 90], [594, 8, 611, 6], [595, 8, 613, 6, "setProcessingProgress"], [595, 29, 613, 27], [595, 30, 613, 28], [595, 32, 613, 30], [595, 33, 613, 31], [597, 8, 615, 6], [598, 8, 616, 6], [598, 12, 616, 10, "detectedFaces"], [598, 25, 616, 23], [598, 26, 616, 24, "length"], [598, 32, 616, 30], [598, 35, 616, 33], [598, 36, 616, 34], [598, 38, 616, 36], [599, 10, 617, 8, "console"], [599, 17, 617, 15], [599, 18, 617, 16, "log"], [599, 21, 617, 19], [599, 22, 617, 20], [599, 61, 617, 59, "detectedFaces"], [599, 74, 617, 72], [599, 75, 617, 73, "length"], [599, 81, 617, 79], [599, 101, 617, 99], [599, 102, 617, 100], [600, 10, 619, 8, "detectedFaces"], [600, 23, 619, 21], [600, 24, 619, 22, "for<PERSON>ach"], [600, 31, 619, 29], [600, 32, 619, 30], [600, 33, 619, 31, "detection"], [600, 42, 619, 40], [600, 44, 619, 42, "index"], [600, 49, 619, 47], [600, 54, 619, 52], [601, 12, 620, 10], [601, 18, 620, 16, "bbox"], [601, 22, 620, 20], [601, 25, 620, 23, "detection"], [601, 34, 620, 32], [601, 35, 620, 33, "boundingBox"], [601, 46, 620, 44], [603, 12, 622, 10], [604, 12, 623, 10], [604, 18, 623, 16, "faceX"], [604, 23, 623, 21], [604, 26, 623, 24, "bbox"], [604, 30, 623, 28], [604, 31, 623, 29, "xCenter"], [604, 38, 623, 36], [604, 41, 623, 39, "img"], [604, 44, 623, 42], [604, 45, 623, 43, "width"], [604, 50, 623, 48], [604, 53, 623, 52, "bbox"], [604, 57, 623, 56], [604, 58, 623, 57, "width"], [604, 63, 623, 62], [604, 66, 623, 65, "img"], [604, 69, 623, 68], [604, 70, 623, 69, "width"], [604, 75, 623, 74], [604, 78, 623, 78], [604, 79, 623, 79], [605, 12, 624, 10], [605, 18, 624, 16, "faceY"], [605, 23, 624, 21], [605, 26, 624, 24, "bbox"], [605, 30, 624, 28], [605, 31, 624, 29, "yCenter"], [605, 38, 624, 36], [605, 41, 624, 39, "img"], [605, 44, 624, 42], [605, 45, 624, 43, "height"], [605, 51, 624, 49], [605, 54, 624, 53, "bbox"], [605, 58, 624, 57], [605, 59, 624, 58, "height"], [605, 65, 624, 64], [605, 68, 624, 67, "img"], [605, 71, 624, 70], [605, 72, 624, 71, "height"], [605, 78, 624, 77], [605, 81, 624, 81], [605, 82, 624, 82], [606, 12, 625, 10], [606, 18, 625, 16, "faceWidth"], [606, 27, 625, 25], [606, 30, 625, 28, "bbox"], [606, 34, 625, 32], [606, 35, 625, 33, "width"], [606, 40, 625, 38], [606, 43, 625, 41, "img"], [606, 46, 625, 44], [606, 47, 625, 45, "width"], [606, 52, 625, 50], [607, 12, 626, 10], [607, 18, 626, 16, "faceHeight"], [607, 28, 626, 26], [607, 31, 626, 29, "bbox"], [607, 35, 626, 33], [607, 36, 626, 34, "height"], [607, 42, 626, 40], [607, 45, 626, 43, "img"], [607, 48, 626, 46], [607, 49, 626, 47, "height"], [607, 55, 626, 53], [609, 12, 628, 10], [610, 12, 629, 10], [610, 18, 629, 16, "padding"], [610, 25, 629, 23], [610, 28, 629, 26], [610, 31, 629, 29], [611, 12, 630, 10], [611, 18, 630, 16, "paddedX"], [611, 25, 630, 23], [611, 28, 630, 26, "Math"], [611, 32, 630, 30], [611, 33, 630, 31, "max"], [611, 36, 630, 34], [611, 37, 630, 35], [611, 38, 630, 36], [611, 40, 630, 38, "faceX"], [611, 45, 630, 43], [611, 48, 630, 46, "faceWidth"], [611, 57, 630, 55], [611, 60, 630, 58, "padding"], [611, 67, 630, 65], [611, 68, 630, 66], [612, 12, 631, 10], [612, 18, 631, 16, "paddedY"], [612, 25, 631, 23], [612, 28, 631, 26, "Math"], [612, 32, 631, 30], [612, 33, 631, 31, "max"], [612, 36, 631, 34], [612, 37, 631, 35], [612, 38, 631, 36], [612, 40, 631, 38, "faceY"], [612, 45, 631, 43], [612, 48, 631, 46, "faceHeight"], [612, 58, 631, 56], [612, 61, 631, 59, "padding"], [612, 68, 631, 66], [612, 69, 631, 67], [613, 12, 632, 10], [613, 18, 632, 16, "<PERSON><PERSON><PERSON><PERSON>"], [613, 29, 632, 27], [613, 32, 632, 30, "Math"], [613, 36, 632, 34], [613, 37, 632, 35, "min"], [613, 40, 632, 38], [613, 41, 632, 39, "img"], [613, 44, 632, 42], [613, 45, 632, 43, "width"], [613, 50, 632, 48], [613, 53, 632, 51, "paddedX"], [613, 60, 632, 58], [613, 62, 632, 60, "faceWidth"], [613, 71, 632, 69], [613, 75, 632, 73], [613, 76, 632, 74], [613, 79, 632, 77], [613, 80, 632, 78], [613, 83, 632, 81, "padding"], [613, 90, 632, 88], [613, 91, 632, 89], [613, 92, 632, 90], [614, 12, 633, 10], [614, 18, 633, 16, "paddedHeight"], [614, 30, 633, 28], [614, 33, 633, 31, "Math"], [614, 37, 633, 35], [614, 38, 633, 36, "min"], [614, 41, 633, 39], [614, 42, 633, 40, "img"], [614, 45, 633, 43], [614, 46, 633, 44, "height"], [614, 52, 633, 50], [614, 55, 633, 53, "paddedY"], [614, 62, 633, 60], [614, 64, 633, 62, "faceHeight"], [614, 74, 633, 72], [614, 78, 633, 76], [614, 79, 633, 77], [614, 82, 633, 80], [614, 83, 633, 81], [614, 86, 633, 84, "padding"], [614, 93, 633, 91], [614, 94, 633, 92], [614, 95, 633, 93], [615, 12, 635, 10, "console"], [615, 19, 635, 17], [615, 20, 635, 18, "log"], [615, 23, 635, 21], [615, 24, 635, 22], [615, 60, 635, 58, "index"], [615, 65, 635, 63], [615, 68, 635, 66], [615, 69, 635, 67], [615, 72, 635, 70], [615, 74, 635, 72], [616, 14, 636, 12, "original"], [616, 22, 636, 20], [616, 24, 636, 22], [617, 16, 636, 24, "x"], [617, 17, 636, 25], [617, 19, 636, 27, "Math"], [617, 23, 636, 31], [617, 24, 636, 32, "round"], [617, 29, 636, 37], [617, 30, 636, 38, "faceX"], [617, 35, 636, 43], [617, 36, 636, 44], [618, 16, 636, 46, "y"], [618, 17, 636, 47], [618, 19, 636, 49, "Math"], [618, 23, 636, 53], [618, 24, 636, 54, "round"], [618, 29, 636, 59], [618, 30, 636, 60, "faceY"], [618, 35, 636, 65], [618, 36, 636, 66], [619, 16, 636, 68, "w"], [619, 17, 636, 69], [619, 19, 636, 71, "Math"], [619, 23, 636, 75], [619, 24, 636, 76, "round"], [619, 29, 636, 81], [619, 30, 636, 82, "faceWidth"], [619, 39, 636, 91], [619, 40, 636, 92], [620, 16, 636, 94, "h"], [620, 17, 636, 95], [620, 19, 636, 97, "Math"], [620, 23, 636, 101], [620, 24, 636, 102, "round"], [620, 29, 636, 107], [620, 30, 636, 108, "faceHeight"], [620, 40, 636, 118], [621, 14, 636, 120], [621, 15, 636, 121], [622, 14, 637, 12, "padded"], [622, 20, 637, 18], [622, 22, 637, 20], [623, 16, 637, 22, "x"], [623, 17, 637, 23], [623, 19, 637, 25, "Math"], [623, 23, 637, 29], [623, 24, 637, 30, "round"], [623, 29, 637, 35], [623, 30, 637, 36, "paddedX"], [623, 37, 637, 43], [623, 38, 637, 44], [624, 16, 637, 46, "y"], [624, 17, 637, 47], [624, 19, 637, 49, "Math"], [624, 23, 637, 53], [624, 24, 637, 54, "round"], [624, 29, 637, 59], [624, 30, 637, 60, "paddedY"], [624, 37, 637, 67], [624, 38, 637, 68], [625, 16, 637, 70, "w"], [625, 17, 637, 71], [625, 19, 637, 73, "Math"], [625, 23, 637, 77], [625, 24, 637, 78, "round"], [625, 29, 637, 83], [625, 30, 637, 84, "<PERSON><PERSON><PERSON><PERSON>"], [625, 41, 637, 95], [625, 42, 637, 96], [626, 16, 637, 98, "h"], [626, 17, 637, 99], [626, 19, 637, 101, "Math"], [626, 23, 637, 105], [626, 24, 637, 106, "round"], [626, 29, 637, 111], [626, 30, 637, 112, "paddedHeight"], [626, 42, 637, 124], [627, 14, 637, 126], [628, 12, 638, 10], [628, 13, 638, 11], [628, 14, 638, 12], [630, 12, 640, 10], [631, 12, 641, 10, "console"], [631, 19, 641, 17], [631, 20, 641, 18, "log"], [631, 23, 641, 21], [631, 24, 641, 22], [631, 70, 641, 68], [631, 72, 641, 70], [632, 14, 642, 12, "width"], [632, 19, 642, 17], [632, 21, 642, 19, "canvas"], [632, 27, 642, 25], [632, 28, 642, 26, "width"], [632, 33, 642, 31], [633, 14, 643, 12, "height"], [633, 20, 643, 18], [633, 22, 643, 20, "canvas"], [633, 28, 643, 26], [633, 29, 643, 27, "height"], [633, 35, 643, 33], [634, 14, 644, 12, "contextValid"], [634, 26, 644, 24], [634, 28, 644, 26], [634, 29, 644, 27], [634, 30, 644, 28, "ctx"], [635, 12, 645, 10], [635, 13, 645, 11], [635, 14, 645, 12], [637, 12, 647, 10], [638, 12, 648, 10, "applyStrongBlur"], [638, 27, 648, 25], [638, 28, 648, 26, "ctx"], [638, 31, 648, 29], [638, 33, 648, 31, "paddedX"], [638, 40, 648, 38], [638, 42, 648, 40, "paddedY"], [638, 49, 648, 47], [638, 51, 648, 49, "<PERSON><PERSON><PERSON><PERSON>"], [638, 62, 648, 60], [638, 64, 648, 62, "paddedHeight"], [638, 76, 648, 74], [638, 77, 648, 75], [640, 12, 650, 10], [641, 12, 651, 10, "console"], [641, 19, 651, 17], [641, 20, 651, 18, "log"], [641, 23, 651, 21], [641, 24, 651, 22], [641, 102, 651, 100], [641, 103, 651, 101], [643, 12, 653, 10], [644, 12, 654, 10], [644, 18, 654, 16, "testImageData"], [644, 31, 654, 29], [644, 34, 654, 32, "ctx"], [644, 37, 654, 35], [644, 38, 654, 36, "getImageData"], [644, 50, 654, 48], [644, 51, 654, 49, "paddedX"], [644, 58, 654, 56], [644, 61, 654, 59], [644, 63, 654, 61], [644, 65, 654, 63, "paddedY"], [644, 72, 654, 70], [644, 75, 654, 73], [644, 77, 654, 75], [644, 79, 654, 77], [644, 81, 654, 79], [644, 83, 654, 81], [644, 85, 654, 83], [644, 86, 654, 84], [645, 12, 655, 10, "console"], [645, 19, 655, 17], [645, 20, 655, 18, "log"], [645, 23, 655, 21], [645, 24, 655, 22], [645, 70, 655, 68], [645, 72, 655, 70], [646, 14, 656, 12, "firstPixel"], [646, 24, 656, 22], [646, 26, 656, 24], [646, 27, 656, 25, "testImageData"], [646, 40, 656, 38], [646, 41, 656, 39, "data"], [646, 45, 656, 43], [646, 46, 656, 44], [646, 47, 656, 45], [646, 48, 656, 46], [646, 50, 656, 48, "testImageData"], [646, 63, 656, 61], [646, 64, 656, 62, "data"], [646, 68, 656, 66], [646, 69, 656, 67], [646, 70, 656, 68], [646, 71, 656, 69], [646, 73, 656, 71, "testImageData"], [646, 86, 656, 84], [646, 87, 656, 85, "data"], [646, 91, 656, 89], [646, 92, 656, 90], [646, 93, 656, 91], [646, 94, 656, 92], [646, 95, 656, 93], [647, 14, 657, 12, "secondPixel"], [647, 25, 657, 23], [647, 27, 657, 25], [647, 28, 657, 26, "testImageData"], [647, 41, 657, 39], [647, 42, 657, 40, "data"], [647, 46, 657, 44], [647, 47, 657, 45], [647, 48, 657, 46], [647, 49, 657, 47], [647, 51, 657, 49, "testImageData"], [647, 64, 657, 62], [647, 65, 657, 63, "data"], [647, 69, 657, 67], [647, 70, 657, 68], [647, 71, 657, 69], [647, 72, 657, 70], [647, 74, 657, 72, "testImageData"], [647, 87, 657, 85], [647, 88, 657, 86, "data"], [647, 92, 657, 90], [647, 93, 657, 91], [647, 94, 657, 92], [647, 95, 657, 93], [648, 12, 658, 10], [648, 13, 658, 11], [648, 14, 658, 12], [649, 12, 660, 10, "console"], [649, 19, 660, 17], [649, 20, 660, 18, "log"], [649, 23, 660, 21], [649, 24, 660, 22], [649, 50, 660, 48, "index"], [649, 55, 660, 53], [649, 58, 660, 56], [649, 59, 660, 57], [649, 79, 660, 77], [649, 80, 660, 78], [650, 10, 661, 8], [650, 11, 661, 9], [650, 12, 661, 10], [651, 10, 663, 8, "console"], [651, 17, 663, 15], [651, 18, 663, 16, "log"], [651, 21, 663, 19], [651, 22, 663, 20], [651, 48, 663, 46, "detectedFaces"], [651, 61, 663, 59], [651, 62, 663, 60, "length"], [651, 68, 663, 66], [651, 104, 663, 102], [651, 105, 663, 103], [652, 8, 664, 6], [652, 9, 664, 7], [652, 15, 664, 13], [653, 10, 665, 8, "console"], [653, 17, 665, 15], [653, 18, 665, 16, "log"], [653, 21, 665, 19], [653, 22, 665, 20], [653, 109, 665, 107], [653, 110, 665, 108], [654, 10, 666, 8], [655, 10, 667, 8, "applyFallbackFaceBlur"], [655, 31, 667, 29], [655, 32, 667, 30, "ctx"], [655, 35, 667, 33], [655, 37, 667, 35, "img"], [655, 40, 667, 38], [655, 41, 667, 39, "width"], [655, 46, 667, 44], [655, 48, 667, 46, "img"], [655, 51, 667, 49], [655, 52, 667, 50, "height"], [655, 58, 667, 56], [655, 59, 667, 57], [656, 8, 668, 6], [657, 8, 670, 6, "setProcessingProgress"], [657, 29, 670, 27], [657, 30, 670, 28], [657, 32, 670, 30], [657, 33, 670, 31], [659, 8, 672, 6], [660, 8, 673, 6, "console"], [660, 15, 673, 13], [660, 16, 673, 14, "log"], [660, 19, 673, 17], [660, 20, 673, 18], [660, 85, 673, 83], [660, 86, 673, 84], [661, 8, 674, 6], [661, 14, 674, 12, "blurredImageBlob"], [661, 30, 674, 28], [661, 33, 674, 31], [661, 39, 674, 37], [661, 43, 674, 41, "Promise"], [661, 50, 674, 48], [661, 51, 674, 56, "resolve"], [661, 58, 674, 63], [661, 62, 674, 68], [662, 10, 675, 8, "canvas"], [662, 16, 675, 14], [662, 17, 675, 15, "toBlob"], [662, 23, 675, 21], [662, 24, 675, 23, "blob"], [662, 28, 675, 27], [662, 32, 675, 32, "resolve"], [662, 39, 675, 39], [662, 40, 675, 40, "blob"], [662, 44, 675, 45], [662, 45, 675, 46], [662, 47, 675, 48], [662, 59, 675, 60], [662, 61, 675, 62], [662, 64, 675, 65], [662, 65, 675, 66], [663, 8, 676, 6], [663, 9, 676, 7], [663, 10, 676, 8], [664, 8, 678, 6], [664, 14, 678, 12, "blurredImageUrl"], [664, 29, 678, 27], [664, 32, 678, 30, "URL"], [664, 35, 678, 33], [664, 36, 678, 34, "createObjectURL"], [664, 51, 678, 49], [664, 52, 678, 50, "blurredImageBlob"], [664, 68, 678, 66], [664, 69, 678, 67], [665, 8, 679, 6, "console"], [665, 15, 679, 13], [665, 16, 679, 14, "log"], [665, 19, 679, 17], [665, 20, 679, 18], [665, 66, 679, 64], [665, 68, 679, 66, "blurredImageUrl"], [665, 83, 679, 81], [665, 84, 679, 82, "substring"], [665, 93, 679, 91], [665, 94, 679, 92], [665, 95, 679, 93], [665, 97, 679, 95], [665, 99, 679, 97], [665, 100, 679, 98], [665, 103, 679, 101], [665, 108, 679, 106], [665, 109, 679, 107], [667, 8, 681, 6], [668, 8, 682, 6, "setCapturedPhoto"], [668, 24, 682, 22], [668, 25, 682, 23, "blurredImageUrl"], [668, 40, 682, 38], [668, 41, 682, 39], [669, 8, 683, 6, "console"], [669, 15, 683, 13], [669, 16, 683, 14, "log"], [669, 19, 683, 17], [669, 20, 683, 18], [669, 87, 683, 85], [669, 88, 683, 86], [670, 8, 685, 6, "setProcessingProgress"], [670, 29, 685, 27], [670, 30, 685, 28], [670, 33, 685, 31], [670, 34, 685, 32], [672, 8, 687, 6], [673, 8, 688, 6], [673, 14, 688, 12, "completeProcessing"], [673, 32, 688, 30], [673, 33, 688, 31, "blurredImageUrl"], [673, 48, 688, 46], [673, 49, 688, 47], [674, 6, 690, 4], [674, 7, 690, 5], [674, 8, 690, 6], [674, 15, 690, 13, "error"], [674, 20, 690, 18], [674, 22, 690, 20], [675, 8, 691, 6, "console"], [675, 15, 691, 13], [675, 16, 691, 14, "error"], [675, 21, 691, 19], [675, 22, 691, 20], [675, 57, 691, 55], [675, 59, 691, 57, "error"], [675, 64, 691, 62], [675, 65, 691, 63], [676, 8, 692, 6, "setErrorMessage"], [676, 23, 692, 21], [676, 24, 692, 22], [676, 50, 692, 48], [676, 51, 692, 49], [677, 8, 693, 6, "setProcessingState"], [677, 26, 693, 24], [677, 27, 693, 25], [677, 34, 693, 32], [677, 35, 693, 33], [678, 6, 694, 4], [679, 4, 695, 2], [679, 5, 695, 3], [681, 4, 697, 2], [682, 4, 698, 2], [682, 10, 698, 8, "completeProcessing"], [682, 28, 698, 26], [682, 31, 698, 29], [682, 37, 698, 36, "blurredImageUrl"], [682, 52, 698, 59], [682, 56, 698, 64], [683, 6, 699, 4], [683, 10, 699, 8], [684, 8, 700, 6, "setProcessingState"], [684, 26, 700, 24], [684, 27, 700, 25], [684, 37, 700, 35], [684, 38, 700, 36], [686, 8, 702, 6], [687, 8, 703, 6], [687, 14, 703, 12, "timestamp"], [687, 23, 703, 21], [687, 26, 703, 24, "Date"], [687, 30, 703, 28], [687, 31, 703, 29, "now"], [687, 34, 703, 32], [687, 35, 703, 33], [687, 36, 703, 34], [688, 8, 704, 6], [688, 14, 704, 12, "result"], [688, 20, 704, 18], [688, 23, 704, 21], [689, 10, 705, 8, "imageUrl"], [689, 18, 705, 16], [689, 20, 705, 18, "blurredImageUrl"], [689, 35, 705, 33], [690, 10, 706, 8, "localUri"], [690, 18, 706, 16], [690, 20, 706, 18, "blurredImageUrl"], [690, 35, 706, 33], [691, 10, 707, 8, "challengeCode"], [691, 23, 707, 21], [691, 25, 707, 23, "challengeCode"], [691, 38, 707, 36], [691, 42, 707, 40], [691, 44, 707, 42], [692, 10, 708, 8, "timestamp"], [692, 19, 708, 17], [693, 10, 709, 8, "jobId"], [693, 15, 709, 13], [693, 17, 709, 15], [693, 27, 709, 25, "timestamp"], [693, 36, 709, 34], [693, 38, 709, 36], [694, 10, 710, 8, "status"], [694, 16, 710, 14], [694, 18, 710, 16], [695, 8, 711, 6], [695, 9, 711, 7], [696, 8, 713, 6, "console"], [696, 15, 713, 13], [696, 16, 713, 14, "log"], [696, 19, 713, 17], [696, 20, 713, 18], [696, 100, 713, 98], [696, 102, 713, 100], [697, 10, 714, 8, "imageUrl"], [697, 18, 714, 16], [697, 20, 714, 18, "blurredImageUrl"], [697, 35, 714, 33], [697, 36, 714, 34, "substring"], [697, 45, 714, 43], [697, 46, 714, 44], [697, 47, 714, 45], [697, 49, 714, 47], [697, 51, 714, 49], [697, 52, 714, 50], [697, 55, 714, 53], [697, 60, 714, 58], [698, 10, 715, 8, "timestamp"], [698, 19, 715, 17], [699, 10, 716, 8, "jobId"], [699, 15, 716, 13], [699, 17, 716, 15, "result"], [699, 23, 716, 21], [699, 24, 716, 22, "jobId"], [700, 8, 717, 6], [700, 9, 717, 7], [700, 10, 717, 8], [702, 8, 719, 6], [703, 8, 720, 6, "onComplete"], [703, 18, 720, 16], [703, 19, 720, 17, "result"], [703, 25, 720, 23], [703, 26, 720, 24], [704, 6, 722, 4], [704, 7, 722, 5], [704, 8, 722, 6], [704, 15, 722, 13, "error"], [704, 20, 722, 18], [704, 22, 722, 20], [705, 8, 723, 6, "console"], [705, 15, 723, 13], [705, 16, 723, 14, "error"], [705, 21, 723, 19], [705, 22, 723, 20], [705, 57, 723, 55], [705, 59, 723, 57, "error"], [705, 64, 723, 62], [705, 65, 723, 63], [706, 8, 724, 6, "setErrorMessage"], [706, 23, 724, 21], [706, 24, 724, 22], [706, 56, 724, 54], [706, 57, 724, 55], [707, 8, 725, 6, "setProcessingState"], [707, 26, 725, 24], [707, 27, 725, 25], [707, 34, 725, 32], [707, 35, 725, 33], [708, 6, 726, 4], [709, 4, 727, 2], [709, 5, 727, 3], [711, 4, 729, 2], [712, 4, 730, 2], [712, 10, 730, 8, "triggerServerProcessing"], [712, 33, 730, 31], [712, 36, 730, 34], [712, 42, 730, 34, "triggerServerProcessing"], [712, 43, 730, 41, "privateImageUrl"], [712, 58, 730, 64], [712, 60, 730, 66, "timestamp"], [712, 69, 730, 83], [712, 74, 730, 88], [713, 6, 731, 4], [713, 10, 731, 8], [714, 8, 732, 6, "console"], [714, 15, 732, 13], [714, 16, 732, 14, "log"], [714, 19, 732, 17], [714, 20, 732, 18], [714, 74, 732, 72], [714, 76, 732, 74, "privateImageUrl"], [714, 91, 732, 89], [714, 92, 732, 90], [715, 8, 733, 6, "setProcessingState"], [715, 26, 733, 24], [715, 27, 733, 25], [715, 39, 733, 37], [715, 40, 733, 38], [716, 8, 734, 6, "setProcessingProgress"], [716, 29, 734, 27], [716, 30, 734, 28], [716, 32, 734, 30], [716, 33, 734, 31], [717, 8, 736, 6], [717, 14, 736, 12, "requestBody"], [717, 25, 736, 23], [717, 28, 736, 26], [718, 10, 737, 8, "imageUrl"], [718, 18, 737, 16], [718, 20, 737, 18, "privateImageUrl"], [718, 35, 737, 33], [719, 10, 738, 8, "userId"], [719, 16, 738, 14], [720, 10, 739, 8, "requestId"], [720, 19, 739, 17], [721, 10, 740, 8, "timestamp"], [721, 19, 740, 17], [722, 10, 741, 8, "platform"], [722, 18, 741, 16], [722, 20, 741, 18], [723, 8, 742, 6], [723, 9, 742, 7], [724, 8, 744, 6, "console"], [724, 15, 744, 13], [724, 16, 744, 14, "log"], [724, 19, 744, 17], [724, 20, 744, 18], [724, 65, 744, 63], [724, 67, 744, 65, "requestBody"], [724, 78, 744, 76], [724, 79, 744, 77], [726, 8, 746, 6], [727, 8, 747, 6], [727, 14, 747, 12, "response"], [727, 22, 747, 20], [727, 25, 747, 23], [727, 31, 747, 29, "fetch"], [727, 36, 747, 34], [727, 37, 747, 35], [727, 40, 747, 38, "API_BASE_URL"], [727, 52, 747, 50], [727, 72, 747, 70], [727, 74, 747, 72], [728, 10, 748, 8, "method"], [728, 16, 748, 14], [728, 18, 748, 16], [728, 24, 748, 22], [729, 10, 749, 8, "headers"], [729, 17, 749, 15], [729, 19, 749, 17], [730, 12, 750, 10], [730, 26, 750, 24], [730, 28, 750, 26], [730, 46, 750, 44], [731, 12, 751, 10], [731, 27, 751, 25], [731, 29, 751, 27], [731, 39, 751, 37], [731, 45, 751, 43, "getAuthToken"], [731, 57, 751, 55], [731, 58, 751, 56], [731, 59, 751, 57], [732, 10, 752, 8], [732, 11, 752, 9], [733, 10, 753, 8, "body"], [733, 14, 753, 12], [733, 16, 753, 14, "JSON"], [733, 20, 753, 18], [733, 21, 753, 19, "stringify"], [733, 30, 753, 28], [733, 31, 753, 29, "requestBody"], [733, 42, 753, 40], [734, 8, 754, 6], [734, 9, 754, 7], [734, 10, 754, 8], [735, 8, 756, 6], [735, 12, 756, 10], [735, 13, 756, 11, "response"], [735, 21, 756, 19], [735, 22, 756, 20, "ok"], [735, 24, 756, 22], [735, 26, 756, 24], [736, 10, 757, 8], [736, 16, 757, 14, "errorText"], [736, 25, 757, 23], [736, 28, 757, 26], [736, 34, 757, 32, "response"], [736, 42, 757, 40], [736, 43, 757, 41, "text"], [736, 47, 757, 45], [736, 48, 757, 46], [736, 49, 757, 47], [737, 10, 758, 8, "console"], [737, 17, 758, 15], [737, 18, 758, 16, "error"], [737, 23, 758, 21], [737, 24, 758, 22], [737, 68, 758, 66], [737, 70, 758, 68, "response"], [737, 78, 758, 76], [737, 79, 758, 77, "status"], [737, 85, 758, 83], [737, 87, 758, 85, "errorText"], [737, 96, 758, 94], [737, 97, 758, 95], [738, 10, 759, 8], [738, 16, 759, 14], [738, 20, 759, 18, "Error"], [738, 25, 759, 23], [738, 26, 759, 24], [738, 48, 759, 46, "response"], [738, 56, 759, 54], [738, 57, 759, 55, "status"], [738, 63, 759, 61], [738, 67, 759, 65, "response"], [738, 75, 759, 73], [738, 76, 759, 74, "statusText"], [738, 86, 759, 84], [738, 88, 759, 86], [738, 89, 759, 87], [739, 8, 760, 6], [740, 8, 762, 6], [740, 14, 762, 12, "result"], [740, 20, 762, 18], [740, 23, 762, 21], [740, 29, 762, 27, "response"], [740, 37, 762, 35], [740, 38, 762, 36, "json"], [740, 42, 762, 40], [740, 43, 762, 41], [740, 44, 762, 42], [741, 8, 763, 6, "console"], [741, 15, 763, 13], [741, 16, 763, 14, "log"], [741, 19, 763, 17], [741, 20, 763, 18], [741, 68, 763, 66], [741, 70, 763, 68, "result"], [741, 76, 763, 74], [741, 77, 763, 75], [742, 8, 765, 6], [742, 12, 765, 10], [742, 13, 765, 11, "result"], [742, 19, 765, 17], [742, 20, 765, 18, "jobId"], [742, 25, 765, 23], [742, 27, 765, 25], [743, 10, 766, 8], [743, 16, 766, 14], [743, 20, 766, 18, "Error"], [743, 25, 766, 23], [743, 26, 766, 24], [743, 70, 766, 68], [743, 71, 766, 69], [744, 8, 767, 6], [746, 8, 769, 6], [747, 8, 770, 6], [747, 14, 770, 12, "pollForCompletion"], [747, 31, 770, 29], [747, 32, 770, 30, "result"], [747, 38, 770, 36], [747, 39, 770, 37, "jobId"], [747, 44, 770, 42], [747, 46, 770, 44, "timestamp"], [747, 55, 770, 53], [747, 56, 770, 54], [748, 6, 771, 4], [748, 7, 771, 5], [748, 8, 771, 6], [748, 15, 771, 13, "error"], [748, 20, 771, 18], [748, 22, 771, 20], [749, 8, 772, 6, "console"], [749, 15, 772, 13], [749, 16, 772, 14, "error"], [749, 21, 772, 19], [749, 22, 772, 20], [749, 57, 772, 55], [749, 59, 772, 57, "error"], [749, 64, 772, 62], [749, 65, 772, 63], [750, 8, 773, 6, "setErrorMessage"], [750, 23, 773, 21], [750, 24, 773, 22], [750, 52, 773, 50, "error"], [750, 57, 773, 55], [750, 58, 773, 56, "message"], [750, 65, 773, 63], [750, 67, 773, 65], [750, 68, 773, 66], [751, 8, 774, 6, "setProcessingState"], [751, 26, 774, 24], [751, 27, 774, 25], [751, 34, 774, 32], [751, 35, 774, 33], [752, 6, 775, 4], [753, 4, 776, 2], [753, 5, 776, 3], [754, 4, 777, 2], [755, 4, 778, 2], [755, 10, 778, 8, "pollForCompletion"], [755, 27, 778, 25], [755, 30, 778, 28], [755, 36, 778, 28, "pollForCompletion"], [755, 37, 778, 35, "jobId"], [755, 42, 778, 48], [755, 44, 778, 50, "timestamp"], [755, 53, 778, 67], [755, 55, 778, 69, "attempts"], [755, 63, 778, 77], [755, 66, 778, 80], [755, 67, 778, 81], [755, 72, 778, 86], [756, 6, 779, 4], [756, 12, 779, 10, "MAX_ATTEMPTS"], [756, 24, 779, 22], [756, 27, 779, 25], [756, 29, 779, 27], [756, 30, 779, 28], [756, 31, 779, 29], [757, 6, 780, 4], [757, 12, 780, 10, "POLL_INTERVAL"], [757, 25, 780, 23], [757, 28, 780, 26], [757, 32, 780, 30], [757, 33, 780, 31], [757, 34, 780, 32], [759, 6, 782, 4, "console"], [759, 13, 782, 11], [759, 14, 782, 12, "log"], [759, 17, 782, 15], [759, 18, 782, 16], [759, 53, 782, 51, "attempts"], [759, 61, 782, 59], [759, 64, 782, 62], [759, 65, 782, 63], [759, 69, 782, 67, "MAX_ATTEMPTS"], [759, 81, 782, 79], [759, 93, 782, 91, "jobId"], [759, 98, 782, 96], [759, 100, 782, 98], [759, 101, 782, 99], [760, 6, 784, 4], [760, 10, 784, 8, "attempts"], [760, 18, 784, 16], [760, 22, 784, 20, "MAX_ATTEMPTS"], [760, 34, 784, 32], [760, 36, 784, 34], [761, 8, 785, 6, "console"], [761, 15, 785, 13], [761, 16, 785, 14, "error"], [761, 21, 785, 19], [761, 22, 785, 20], [761, 75, 785, 73], [761, 76, 785, 74], [762, 8, 786, 6, "setErrorMessage"], [762, 23, 786, 21], [762, 24, 786, 22], [762, 63, 786, 61], [762, 64, 786, 62], [763, 8, 787, 6, "setProcessingState"], [763, 26, 787, 24], [763, 27, 787, 25], [763, 34, 787, 32], [763, 35, 787, 33], [764, 8, 788, 6], [765, 6, 789, 4], [766, 6, 791, 4], [766, 10, 791, 8], [767, 8, 792, 6], [767, 14, 792, 12, "response"], [767, 22, 792, 20], [767, 25, 792, 23], [767, 31, 792, 29, "fetch"], [767, 36, 792, 34], [767, 37, 792, 35], [767, 40, 792, 38, "API_BASE_URL"], [767, 52, 792, 50], [767, 75, 792, 73, "jobId"], [767, 80, 792, 78], [767, 82, 792, 80], [767, 84, 792, 82], [768, 10, 793, 8, "headers"], [768, 17, 793, 15], [768, 19, 793, 17], [769, 12, 794, 10], [769, 27, 794, 25], [769, 29, 794, 27], [769, 39, 794, 37], [769, 45, 794, 43, "getAuthToken"], [769, 57, 794, 55], [769, 58, 794, 56], [769, 59, 794, 57], [770, 10, 795, 8], [771, 8, 796, 6], [771, 9, 796, 7], [771, 10, 796, 8], [772, 8, 798, 6], [772, 12, 798, 10], [772, 13, 798, 11, "response"], [772, 21, 798, 19], [772, 22, 798, 20, "ok"], [772, 24, 798, 22], [772, 26, 798, 24], [773, 10, 799, 8], [773, 16, 799, 14], [773, 20, 799, 18, "Error"], [773, 25, 799, 23], [773, 26, 799, 24], [773, 34, 799, 32, "response"], [773, 42, 799, 40], [773, 43, 799, 41, "status"], [773, 49, 799, 47], [773, 54, 799, 52, "response"], [773, 62, 799, 60], [773, 63, 799, 61, "statusText"], [773, 73, 799, 71], [773, 75, 799, 73], [773, 76, 799, 74], [774, 8, 800, 6], [775, 8, 802, 6], [775, 14, 802, 12, "status"], [775, 20, 802, 18], [775, 23, 802, 21], [775, 29, 802, 27, "response"], [775, 37, 802, 35], [775, 38, 802, 36, "json"], [775, 42, 802, 40], [775, 43, 802, 41], [775, 44, 802, 42], [776, 8, 803, 6, "console"], [776, 15, 803, 13], [776, 16, 803, 14, "log"], [776, 19, 803, 17], [776, 20, 803, 18], [776, 54, 803, 52], [776, 56, 803, 54, "status"], [776, 62, 803, 60], [776, 63, 803, 61], [777, 8, 805, 6], [777, 12, 805, 10, "status"], [777, 18, 805, 16], [777, 19, 805, 17, "status"], [777, 25, 805, 23], [777, 30, 805, 28], [777, 41, 805, 39], [777, 43, 805, 41], [778, 10, 806, 8, "console"], [778, 17, 806, 15], [778, 18, 806, 16, "log"], [778, 21, 806, 19], [778, 22, 806, 20], [778, 73, 806, 71], [778, 74, 806, 72], [779, 10, 807, 8, "setProcessingProgress"], [779, 31, 807, 29], [779, 32, 807, 30], [779, 35, 807, 33], [779, 36, 807, 34], [780, 10, 808, 8, "setProcessingState"], [780, 28, 808, 26], [780, 29, 808, 27], [780, 40, 808, 38], [780, 41, 808, 39], [781, 10, 809, 8], [782, 10, 810, 8], [782, 16, 810, 14, "result"], [782, 22, 810, 20], [782, 25, 810, 23], [783, 12, 811, 10, "imageUrl"], [783, 20, 811, 18], [783, 22, 811, 20, "status"], [783, 28, 811, 26], [783, 29, 811, 27, "publicUrl"], [783, 38, 811, 36], [784, 12, 811, 38], [785, 12, 812, 10, "localUri"], [785, 20, 812, 18], [785, 22, 812, 20, "capturedPhoto"], [785, 35, 812, 33], [785, 39, 812, 37, "status"], [785, 45, 812, 43], [785, 46, 812, 44, "publicUrl"], [785, 55, 812, 53], [786, 12, 812, 55], [787, 12, 813, 10, "challengeCode"], [787, 25, 813, 23], [787, 27, 813, 25, "challengeCode"], [787, 40, 813, 38], [787, 44, 813, 42], [787, 46, 813, 44], [788, 12, 814, 10, "timestamp"], [788, 21, 814, 19], [789, 12, 815, 10, "processingStatus"], [789, 28, 815, 26], [789, 30, 815, 28], [790, 10, 816, 8], [790, 11, 816, 9], [791, 10, 817, 8, "console"], [791, 17, 817, 15], [791, 18, 817, 16, "log"], [791, 21, 817, 19], [791, 22, 817, 20], [791, 57, 817, 55], [791, 59, 817, 57, "result"], [791, 65, 817, 63], [791, 66, 817, 64], [792, 10, 818, 8, "onComplete"], [792, 20, 818, 18], [792, 21, 818, 19, "result"], [792, 27, 818, 25], [792, 28, 818, 26], [793, 10, 819, 8], [794, 8, 820, 6], [794, 9, 820, 7], [794, 15, 820, 13], [794, 19, 820, 17, "status"], [794, 25, 820, 23], [794, 26, 820, 24, "status"], [794, 32, 820, 30], [794, 37, 820, 35], [794, 45, 820, 43], [794, 47, 820, 45], [795, 10, 821, 8, "console"], [795, 17, 821, 15], [795, 18, 821, 16, "error"], [795, 23, 821, 21], [795, 24, 821, 22], [795, 60, 821, 58], [795, 62, 821, 60, "status"], [795, 68, 821, 66], [795, 69, 821, 67, "error"], [795, 74, 821, 72], [795, 75, 821, 73], [796, 10, 822, 8], [796, 16, 822, 14], [796, 20, 822, 18, "Error"], [796, 25, 822, 23], [796, 26, 822, 24, "status"], [796, 32, 822, 30], [796, 33, 822, 31, "error"], [796, 38, 822, 36], [796, 42, 822, 40], [796, 61, 822, 59], [796, 62, 822, 60], [797, 8, 823, 6], [797, 9, 823, 7], [797, 15, 823, 13], [798, 10, 824, 8], [799, 10, 825, 8], [799, 16, 825, 14, "progressValue"], [799, 29, 825, 27], [799, 32, 825, 30], [799, 34, 825, 32], [799, 37, 825, 36, "attempts"], [799, 45, 825, 44], [799, 48, 825, 47, "MAX_ATTEMPTS"], [799, 60, 825, 59], [799, 63, 825, 63], [799, 65, 825, 65], [800, 10, 826, 8, "console"], [800, 17, 826, 15], [800, 18, 826, 16, "log"], [800, 21, 826, 19], [800, 22, 826, 20], [800, 71, 826, 69, "progressValue"], [800, 84, 826, 82], [800, 87, 826, 85], [800, 88, 826, 86], [801, 10, 827, 8, "setProcessingProgress"], [801, 31, 827, 29], [801, 32, 827, 30, "progressValue"], [801, 45, 827, 43], [801, 46, 827, 44], [802, 10, 829, 8, "setTimeout"], [802, 20, 829, 18], [802, 21, 829, 19], [802, 27, 829, 25], [803, 12, 830, 10, "pollForCompletion"], [803, 29, 830, 27], [803, 30, 830, 28, "jobId"], [803, 35, 830, 33], [803, 37, 830, 35, "timestamp"], [803, 46, 830, 44], [803, 48, 830, 46, "attempts"], [803, 56, 830, 54], [803, 59, 830, 57], [803, 60, 830, 58], [803, 61, 830, 59], [804, 10, 831, 8], [804, 11, 831, 9], [804, 13, 831, 11, "POLL_INTERVAL"], [804, 26, 831, 24], [804, 27, 831, 25], [805, 8, 832, 6], [806, 6, 833, 4], [806, 7, 833, 5], [806, 8, 833, 6], [806, 15, 833, 13, "error"], [806, 20, 833, 18], [806, 22, 833, 20], [807, 8, 834, 6, "console"], [807, 15, 834, 13], [807, 16, 834, 14, "error"], [807, 21, 834, 19], [807, 22, 834, 20], [807, 54, 834, 52], [807, 56, 834, 54, "error"], [807, 61, 834, 59], [807, 62, 834, 60], [808, 8, 835, 6, "setErrorMessage"], [808, 23, 835, 21], [808, 24, 835, 22], [808, 62, 835, 60, "error"], [808, 67, 835, 65], [808, 68, 835, 66, "message"], [808, 75, 835, 73], [808, 77, 835, 75], [808, 78, 835, 76], [809, 8, 836, 6, "setProcessingState"], [809, 26, 836, 24], [809, 27, 836, 25], [809, 34, 836, 32], [809, 35, 836, 33], [810, 6, 837, 4], [811, 4, 838, 2], [811, 5, 838, 3], [812, 4, 839, 2], [813, 4, 840, 2], [813, 10, 840, 8, "getAuthToken"], [813, 22, 840, 20], [813, 25, 840, 23], [813, 31, 840, 23, "getAuthToken"], [813, 32, 840, 23], [813, 37, 840, 52], [814, 6, 841, 4], [815, 6, 842, 4], [816, 6, 843, 4], [816, 13, 843, 11], [816, 30, 843, 28], [817, 4, 844, 2], [817, 5, 844, 3], [819, 4, 846, 2], [820, 4, 847, 2], [820, 10, 847, 8, "retryCapture"], [820, 22, 847, 20], [820, 25, 847, 23], [820, 29, 847, 23, "useCallback"], [820, 47, 847, 34], [820, 49, 847, 35], [820, 55, 847, 41], [821, 6, 848, 4, "console"], [821, 13, 848, 11], [821, 14, 848, 12, "log"], [821, 17, 848, 15], [821, 18, 848, 16], [821, 55, 848, 53], [821, 56, 848, 54], [822, 6, 849, 4, "setProcessingState"], [822, 24, 849, 22], [822, 25, 849, 23], [822, 31, 849, 29], [822, 32, 849, 30], [823, 6, 850, 4, "setErrorMessage"], [823, 21, 850, 19], [823, 22, 850, 20], [823, 24, 850, 22], [823, 25, 850, 23], [824, 6, 851, 4, "setCapturedPhoto"], [824, 22, 851, 20], [824, 23, 851, 21], [824, 25, 851, 23], [824, 26, 851, 24], [825, 6, 852, 4, "setProcessingProgress"], [825, 27, 852, 25], [825, 28, 852, 26], [825, 29, 852, 27], [825, 30, 852, 28], [826, 4, 853, 2], [826, 5, 853, 3], [826, 7, 853, 5], [826, 9, 853, 7], [826, 10, 853, 8], [827, 4, 854, 2], [828, 4, 855, 2], [828, 8, 855, 2, "useEffect"], [828, 24, 855, 11], [828, 26, 855, 12], [828, 32, 855, 18], [829, 6, 856, 4, "console"], [829, 13, 856, 11], [829, 14, 856, 12, "log"], [829, 17, 856, 15], [829, 18, 856, 16], [829, 53, 856, 51], [829, 55, 856, 53, "permission"], [829, 65, 856, 63], [829, 66, 856, 64], [830, 6, 857, 4], [830, 10, 857, 8, "permission"], [830, 20, 857, 18], [830, 22, 857, 20], [831, 8, 858, 6, "console"], [831, 15, 858, 13], [831, 16, 858, 14, "log"], [831, 19, 858, 17], [831, 20, 858, 18], [831, 57, 858, 55], [831, 59, 858, 57, "permission"], [831, 69, 858, 67], [831, 70, 858, 68, "granted"], [831, 77, 858, 75], [831, 78, 858, 76], [832, 6, 859, 4], [833, 4, 860, 2], [833, 5, 860, 3], [833, 7, 860, 5], [833, 8, 860, 6, "permission"], [833, 18, 860, 16], [833, 19, 860, 17], [833, 20, 860, 18], [834, 4, 861, 2], [835, 4, 862, 2], [835, 8, 862, 6], [835, 9, 862, 7, "permission"], [835, 19, 862, 17], [835, 21, 862, 19], [836, 6, 863, 4, "console"], [836, 13, 863, 11], [836, 14, 863, 12, "log"], [836, 17, 863, 15], [836, 18, 863, 16], [836, 67, 863, 65], [836, 68, 863, 66], [837, 6, 864, 4], [837, 26, 865, 6], [837, 30, 865, 6, "_jsxDevRuntime"], [837, 44, 865, 6], [837, 45, 865, 6, "jsxDEV"], [837, 51, 865, 6], [837, 53, 865, 7, "_View"], [837, 58, 865, 7], [837, 59, 865, 7, "default"], [837, 66, 865, 11], [838, 8, 865, 12, "style"], [838, 13, 865, 17], [838, 15, 865, 19, "styles"], [838, 21, 865, 25], [838, 22, 865, 26, "container"], [838, 31, 865, 36], [839, 8, 865, 36, "children"], [839, 16, 865, 36], [839, 32, 866, 8], [839, 36, 866, 8, "_jsxDevRuntime"], [839, 50, 866, 8], [839, 51, 866, 8, "jsxDEV"], [839, 57, 866, 8], [839, 59, 866, 9, "_ActivityIndicator"], [839, 77, 866, 9], [839, 78, 866, 9, "default"], [839, 85, 866, 26], [840, 10, 866, 27, "size"], [840, 14, 866, 31], [840, 16, 866, 32], [840, 23, 866, 39], [841, 10, 866, 40, "color"], [841, 15, 866, 45], [841, 17, 866, 46], [842, 8, 866, 55], [843, 10, 866, 55, "fileName"], [843, 18, 866, 55], [843, 20, 866, 55, "_jsxFileName"], [843, 32, 866, 55], [844, 10, 866, 55, "lineNumber"], [844, 20, 866, 55], [845, 10, 866, 55, "columnNumber"], [845, 22, 866, 55], [846, 8, 866, 55], [846, 15, 866, 57], [846, 16, 866, 58], [846, 31, 867, 8], [846, 35, 867, 8, "_jsxDevRuntime"], [846, 49, 867, 8], [846, 50, 867, 8, "jsxDEV"], [846, 56, 867, 8], [846, 58, 867, 9, "_Text"], [846, 63, 867, 9], [846, 64, 867, 9, "default"], [846, 71, 867, 13], [847, 10, 867, 14, "style"], [847, 15, 867, 19], [847, 17, 867, 21, "styles"], [847, 23, 867, 27], [847, 24, 867, 28, "loadingText"], [847, 35, 867, 40], [848, 10, 867, 40, "children"], [848, 18, 867, 40], [848, 20, 867, 41], [849, 8, 867, 58], [850, 10, 867, 58, "fileName"], [850, 18, 867, 58], [850, 20, 867, 58, "_jsxFileName"], [850, 32, 867, 58], [851, 10, 867, 58, "lineNumber"], [851, 20, 867, 58], [852, 10, 867, 58, "columnNumber"], [852, 22, 867, 58], [853, 8, 867, 58], [853, 15, 867, 64], [853, 16, 867, 65], [854, 6, 867, 65], [855, 8, 867, 65, "fileName"], [855, 16, 867, 65], [855, 18, 867, 65, "_jsxFileName"], [855, 30, 867, 65], [856, 8, 867, 65, "lineNumber"], [856, 18, 867, 65], [857, 8, 867, 65, "columnNumber"], [857, 20, 867, 65], [858, 6, 867, 65], [858, 13, 868, 12], [858, 14, 868, 13], [859, 4, 870, 2], [860, 4, 871, 2], [860, 8, 871, 6], [860, 9, 871, 7, "permission"], [860, 19, 871, 17], [860, 20, 871, 18, "granted"], [860, 27, 871, 25], [860, 29, 871, 27], [861, 6, 872, 4, "console"], [861, 13, 872, 11], [861, 14, 872, 12, "log"], [861, 17, 872, 15], [861, 18, 872, 16], [861, 93, 872, 91], [861, 94, 872, 92], [862, 6, 873, 4], [862, 26, 874, 6], [862, 30, 874, 6, "_jsxDevRuntime"], [862, 44, 874, 6], [862, 45, 874, 6, "jsxDEV"], [862, 51, 874, 6], [862, 53, 874, 7, "_View"], [862, 58, 874, 7], [862, 59, 874, 7, "default"], [862, 66, 874, 11], [863, 8, 874, 12, "style"], [863, 13, 874, 17], [863, 15, 874, 19, "styles"], [863, 21, 874, 25], [863, 22, 874, 26, "container"], [863, 31, 874, 36], [864, 8, 874, 36, "children"], [864, 16, 874, 36], [864, 31, 875, 8], [864, 35, 875, 8, "_jsxDevRuntime"], [864, 49, 875, 8], [864, 50, 875, 8, "jsxDEV"], [864, 56, 875, 8], [864, 58, 875, 9, "_View"], [864, 63, 875, 9], [864, 64, 875, 9, "default"], [864, 71, 875, 13], [865, 10, 875, 14, "style"], [865, 15, 875, 19], [865, 17, 875, 21, "styles"], [865, 23, 875, 27], [865, 24, 875, 28, "permissionContent"], [865, 41, 875, 46], [866, 10, 875, 46, "children"], [866, 18, 875, 46], [866, 34, 876, 10], [866, 38, 876, 10, "_jsxDevRuntime"], [866, 52, 876, 10], [866, 53, 876, 10, "jsxDEV"], [866, 59, 876, 10], [866, 61, 876, 11, "_lucideReactNative"], [866, 79, 876, 11], [866, 80, 876, 11, "Camera"], [866, 86, 876, 21], [867, 12, 876, 22, "size"], [867, 16, 876, 26], [867, 18, 876, 28], [867, 20, 876, 31], [868, 12, 876, 32, "color"], [868, 17, 876, 37], [868, 19, 876, 38], [869, 10, 876, 47], [870, 12, 876, 47, "fileName"], [870, 20, 876, 47], [870, 22, 876, 47, "_jsxFileName"], [870, 34, 876, 47], [871, 12, 876, 47, "lineNumber"], [871, 22, 876, 47], [872, 12, 876, 47, "columnNumber"], [872, 24, 876, 47], [873, 10, 876, 47], [873, 17, 876, 49], [873, 18, 876, 50], [873, 33, 877, 10], [873, 37, 877, 10, "_jsxDevRuntime"], [873, 51, 877, 10], [873, 52, 877, 10, "jsxDEV"], [873, 58, 877, 10], [873, 60, 877, 11, "_Text"], [873, 65, 877, 11], [873, 66, 877, 11, "default"], [873, 73, 877, 15], [874, 12, 877, 16, "style"], [874, 17, 877, 21], [874, 19, 877, 23, "styles"], [874, 25, 877, 29], [874, 26, 877, 30, "permissionTitle"], [874, 41, 877, 46], [875, 12, 877, 46, "children"], [875, 20, 877, 46], [875, 22, 877, 47], [876, 10, 877, 73], [877, 12, 877, 73, "fileName"], [877, 20, 877, 73], [877, 22, 877, 73, "_jsxFileName"], [877, 34, 877, 73], [878, 12, 877, 73, "lineNumber"], [878, 22, 877, 73], [879, 12, 877, 73, "columnNumber"], [879, 24, 877, 73], [880, 10, 877, 73], [880, 17, 877, 79], [880, 18, 877, 80], [880, 33, 878, 10], [880, 37, 878, 10, "_jsxDevRuntime"], [880, 51, 878, 10], [880, 52, 878, 10, "jsxDEV"], [880, 58, 878, 10], [880, 60, 878, 11, "_Text"], [880, 65, 878, 11], [880, 66, 878, 11, "default"], [880, 73, 878, 15], [881, 12, 878, 16, "style"], [881, 17, 878, 21], [881, 19, 878, 23, "styles"], [881, 25, 878, 29], [881, 26, 878, 30, "permissionDescription"], [881, 47, 878, 52], [882, 12, 878, 52, "children"], [882, 20, 878, 52], [882, 22, 878, 53], [883, 10, 881, 10], [884, 12, 881, 10, "fileName"], [884, 20, 881, 10], [884, 22, 881, 10, "_jsxFileName"], [884, 34, 881, 10], [885, 12, 881, 10, "lineNumber"], [885, 22, 881, 10], [886, 12, 881, 10, "columnNumber"], [886, 24, 881, 10], [887, 10, 881, 10], [887, 17, 881, 16], [887, 18, 881, 17], [887, 33, 882, 10], [887, 37, 882, 10, "_jsxDevRuntime"], [887, 51, 882, 10], [887, 52, 882, 10, "jsxDEV"], [887, 58, 882, 10], [887, 60, 882, 11, "_TouchableOpacity"], [887, 77, 882, 11], [887, 78, 882, 11, "default"], [887, 85, 882, 27], [888, 12, 882, 28, "onPress"], [888, 19, 882, 35], [888, 21, 882, 37, "requestPermission"], [888, 38, 882, 55], [889, 12, 882, 56, "style"], [889, 17, 882, 61], [889, 19, 882, 63, "styles"], [889, 25, 882, 69], [889, 26, 882, 70, "primaryButton"], [889, 39, 882, 84], [890, 12, 882, 84, "children"], [890, 20, 882, 84], [890, 35, 883, 12], [890, 39, 883, 12, "_jsxDevRuntime"], [890, 53, 883, 12], [890, 54, 883, 12, "jsxDEV"], [890, 60, 883, 12], [890, 62, 883, 13, "_Text"], [890, 67, 883, 13], [890, 68, 883, 13, "default"], [890, 75, 883, 17], [891, 14, 883, 18, "style"], [891, 19, 883, 23], [891, 21, 883, 25, "styles"], [891, 27, 883, 31], [891, 28, 883, 32, "primaryButtonText"], [891, 45, 883, 50], [892, 14, 883, 50, "children"], [892, 22, 883, 50], [892, 24, 883, 51], [893, 12, 883, 67], [894, 14, 883, 67, "fileName"], [894, 22, 883, 67], [894, 24, 883, 67, "_jsxFileName"], [894, 36, 883, 67], [895, 14, 883, 67, "lineNumber"], [895, 24, 883, 67], [896, 14, 883, 67, "columnNumber"], [896, 26, 883, 67], [897, 12, 883, 67], [897, 19, 883, 73], [898, 10, 883, 74], [899, 12, 883, 74, "fileName"], [899, 20, 883, 74], [899, 22, 883, 74, "_jsxFileName"], [899, 34, 883, 74], [900, 12, 883, 74, "lineNumber"], [900, 22, 883, 74], [901, 12, 883, 74, "columnNumber"], [901, 24, 883, 74], [902, 10, 883, 74], [902, 17, 884, 28], [902, 18, 884, 29], [902, 33, 885, 10], [902, 37, 885, 10, "_jsxDevRuntime"], [902, 51, 885, 10], [902, 52, 885, 10, "jsxDEV"], [902, 58, 885, 10], [902, 60, 885, 11, "_TouchableOpacity"], [902, 77, 885, 11], [902, 78, 885, 11, "default"], [902, 85, 885, 27], [903, 12, 885, 28, "onPress"], [903, 19, 885, 35], [903, 21, 885, 37, "onCancel"], [903, 29, 885, 46], [904, 12, 885, 47, "style"], [904, 17, 885, 52], [904, 19, 885, 54, "styles"], [904, 25, 885, 60], [904, 26, 885, 61, "secondaryButton"], [904, 41, 885, 77], [905, 12, 885, 77, "children"], [905, 20, 885, 77], [905, 35, 886, 12], [905, 39, 886, 12, "_jsxDevRuntime"], [905, 53, 886, 12], [905, 54, 886, 12, "jsxDEV"], [905, 60, 886, 12], [905, 62, 886, 13, "_Text"], [905, 67, 886, 13], [905, 68, 886, 13, "default"], [905, 75, 886, 17], [906, 14, 886, 18, "style"], [906, 19, 886, 23], [906, 21, 886, 25, "styles"], [906, 27, 886, 31], [906, 28, 886, 32, "secondaryButtonText"], [906, 47, 886, 52], [907, 14, 886, 52, "children"], [907, 22, 886, 52], [907, 24, 886, 53], [908, 12, 886, 59], [909, 14, 886, 59, "fileName"], [909, 22, 886, 59], [909, 24, 886, 59, "_jsxFileName"], [909, 36, 886, 59], [910, 14, 886, 59, "lineNumber"], [910, 24, 886, 59], [911, 14, 886, 59, "columnNumber"], [911, 26, 886, 59], [912, 12, 886, 59], [912, 19, 886, 65], [913, 10, 886, 66], [914, 12, 886, 66, "fileName"], [914, 20, 886, 66], [914, 22, 886, 66, "_jsxFileName"], [914, 34, 886, 66], [915, 12, 886, 66, "lineNumber"], [915, 22, 886, 66], [916, 12, 886, 66, "columnNumber"], [916, 24, 886, 66], [917, 10, 886, 66], [917, 17, 887, 28], [917, 18, 887, 29], [918, 8, 887, 29], [919, 10, 887, 29, "fileName"], [919, 18, 887, 29], [919, 20, 887, 29, "_jsxFileName"], [919, 32, 887, 29], [920, 10, 887, 29, "lineNumber"], [920, 20, 887, 29], [921, 10, 887, 29, "columnNumber"], [921, 22, 887, 29], [922, 8, 887, 29], [922, 15, 888, 14], [923, 6, 888, 15], [924, 8, 888, 15, "fileName"], [924, 16, 888, 15], [924, 18, 888, 15, "_jsxFileName"], [924, 30, 888, 15], [925, 8, 888, 15, "lineNumber"], [925, 18, 888, 15], [926, 8, 888, 15, "columnNumber"], [926, 20, 888, 15], [927, 6, 888, 15], [927, 13, 889, 12], [927, 14, 889, 13], [928, 4, 891, 2], [929, 4, 892, 2], [930, 4, 893, 2, "console"], [930, 11, 893, 9], [930, 12, 893, 10, "log"], [930, 15, 893, 13], [930, 16, 893, 14], [930, 55, 893, 53], [930, 56, 893, 54], [931, 4, 895, 2], [931, 24, 896, 4], [931, 28, 896, 4, "_jsxDevRuntime"], [931, 42, 896, 4], [931, 43, 896, 4, "jsxDEV"], [931, 49, 896, 4], [931, 51, 896, 5, "_View"], [931, 56, 896, 5], [931, 57, 896, 5, "default"], [931, 64, 896, 9], [932, 6, 896, 10, "style"], [932, 11, 896, 15], [932, 13, 896, 17, "styles"], [932, 19, 896, 23], [932, 20, 896, 24, "container"], [932, 29, 896, 34], [933, 6, 896, 34, "children"], [933, 14, 896, 34], [933, 30, 898, 6], [933, 34, 898, 6, "_jsxDevRuntime"], [933, 48, 898, 6], [933, 49, 898, 6, "jsxDEV"], [933, 55, 898, 6], [933, 57, 898, 7, "_View"], [933, 62, 898, 7], [933, 63, 898, 7, "default"], [933, 70, 898, 11], [934, 8, 898, 12, "style"], [934, 13, 898, 17], [934, 15, 898, 19, "styles"], [934, 21, 898, 25], [934, 22, 898, 26, "cameraContainer"], [934, 37, 898, 42], [935, 8, 898, 43, "id"], [935, 10, 898, 45], [935, 12, 898, 46], [935, 29, 898, 63], [936, 8, 898, 63, "children"], [936, 16, 898, 63], [936, 32, 899, 8], [936, 36, 899, 8, "_jsxDevRuntime"], [936, 50, 899, 8], [936, 51, 899, 8, "jsxDEV"], [936, 57, 899, 8], [936, 59, 899, 9, "_expoCamera"], [936, 70, 899, 9], [936, 71, 899, 9, "CameraView"], [936, 81, 899, 19], [937, 10, 900, 10, "ref"], [937, 13, 900, 13], [937, 15, 900, 15, "cameraRef"], [937, 24, 900, 25], [938, 10, 901, 10, "style"], [938, 15, 901, 15], [938, 17, 901, 17], [938, 18, 901, 18, "styles"], [938, 24, 901, 24], [938, 25, 901, 25, "camera"], [938, 31, 901, 31], [938, 33, 901, 33], [939, 12, 901, 35, "backgroundColor"], [939, 27, 901, 50], [939, 29, 901, 52], [940, 10, 901, 62], [940, 11, 901, 63], [940, 12, 901, 65], [941, 10, 902, 10, "facing"], [941, 16, 902, 16], [941, 18, 902, 17], [941, 24, 902, 23], [942, 10, 903, 10, "onLayout"], [942, 18, 903, 18], [942, 20, 903, 21, "e"], [942, 21, 903, 22], [942, 25, 903, 27], [943, 12, 904, 12, "console"], [943, 19, 904, 19], [943, 20, 904, 20, "log"], [943, 23, 904, 23], [943, 24, 904, 24], [943, 56, 904, 56], [943, 58, 904, 58, "e"], [943, 59, 904, 59], [943, 60, 904, 60, "nativeEvent"], [943, 71, 904, 71], [943, 72, 904, 72, "layout"], [943, 78, 904, 78], [943, 79, 904, 79], [944, 12, 905, 12, "setViewSize"], [944, 23, 905, 23], [944, 24, 905, 24], [945, 14, 905, 26, "width"], [945, 19, 905, 31], [945, 21, 905, 33, "e"], [945, 22, 905, 34], [945, 23, 905, 35, "nativeEvent"], [945, 34, 905, 46], [945, 35, 905, 47, "layout"], [945, 41, 905, 53], [945, 42, 905, 54, "width"], [945, 47, 905, 59], [946, 14, 905, 61, "height"], [946, 20, 905, 67], [946, 22, 905, 69, "e"], [946, 23, 905, 70], [946, 24, 905, 71, "nativeEvent"], [946, 35, 905, 82], [946, 36, 905, 83, "layout"], [946, 42, 905, 89], [946, 43, 905, 90, "height"], [947, 12, 905, 97], [947, 13, 905, 98], [947, 14, 905, 99], [948, 10, 906, 10], [948, 11, 906, 12], [949, 10, 907, 10, "onCameraReady"], [949, 23, 907, 23], [949, 25, 907, 25, "onCameraReady"], [949, 26, 907, 25], [949, 31, 907, 31], [950, 12, 908, 12, "console"], [950, 19, 908, 19], [950, 20, 908, 20, "log"], [950, 23, 908, 23], [950, 24, 908, 24], [950, 55, 908, 55], [950, 56, 908, 56], [951, 12, 909, 12, "setIsCameraReady"], [951, 28, 909, 28], [951, 29, 909, 29], [951, 33, 909, 33], [951, 34, 909, 34], [951, 35, 909, 35], [951, 36, 909, 36], [952, 10, 910, 10], [952, 11, 910, 12], [953, 10, 911, 10, "onMountError"], [953, 22, 911, 22], [953, 24, 911, 25, "error"], [953, 29, 911, 30], [953, 33, 911, 35], [954, 12, 912, 12, "console"], [954, 19, 912, 19], [954, 20, 912, 20, "error"], [954, 25, 912, 25], [954, 26, 912, 26], [954, 63, 912, 63], [954, 65, 912, 65, "error"], [954, 70, 912, 70], [954, 71, 912, 71], [955, 12, 913, 12, "setErrorMessage"], [955, 27, 913, 27], [955, 28, 913, 28], [955, 57, 913, 57], [955, 58, 913, 58], [956, 12, 914, 12, "setProcessingState"], [956, 30, 914, 30], [956, 31, 914, 31], [956, 38, 914, 38], [956, 39, 914, 39], [957, 10, 915, 10], [958, 8, 915, 12], [959, 10, 915, 12, "fileName"], [959, 18, 915, 12], [959, 20, 915, 12, "_jsxFileName"], [959, 32, 915, 12], [960, 10, 915, 12, "lineNumber"], [960, 20, 915, 12], [961, 10, 915, 12, "columnNumber"], [961, 22, 915, 12], [962, 8, 915, 12], [962, 15, 916, 9], [962, 16, 916, 10], [962, 18, 918, 9], [962, 19, 918, 10, "isCameraReady"], [962, 32, 918, 23], [962, 49, 919, 10], [962, 53, 919, 10, "_jsxDevRuntime"], [962, 67, 919, 10], [962, 68, 919, 10, "jsxDEV"], [962, 74, 919, 10], [962, 76, 919, 11, "_View"], [962, 81, 919, 11], [962, 82, 919, 11, "default"], [962, 89, 919, 15], [963, 10, 919, 16, "style"], [963, 15, 919, 21], [963, 17, 919, 23], [963, 18, 919, 24, "StyleSheet"], [963, 37, 919, 34], [963, 38, 919, 35, "absoluteFill"], [963, 50, 919, 47], [963, 52, 919, 49], [964, 12, 919, 51, "backgroundColor"], [964, 27, 919, 66], [964, 29, 919, 68], [964, 49, 919, 88], [965, 12, 919, 90, "justifyContent"], [965, 26, 919, 104], [965, 28, 919, 106], [965, 36, 919, 114], [966, 12, 919, 116, "alignItems"], [966, 22, 919, 126], [966, 24, 919, 128], [966, 32, 919, 136], [967, 12, 919, 138, "zIndex"], [967, 18, 919, 144], [967, 20, 919, 146], [968, 10, 919, 151], [968, 11, 919, 152], [968, 12, 919, 154], [969, 10, 919, 154, "children"], [969, 18, 919, 154], [969, 33, 920, 12], [969, 37, 920, 12, "_jsxDevRuntime"], [969, 51, 920, 12], [969, 52, 920, 12, "jsxDEV"], [969, 58, 920, 12], [969, 60, 920, 13, "_View"], [969, 65, 920, 13], [969, 66, 920, 13, "default"], [969, 73, 920, 17], [970, 12, 920, 18, "style"], [970, 17, 920, 23], [970, 19, 920, 25], [971, 14, 920, 27, "backgroundColor"], [971, 29, 920, 42], [971, 31, 920, 44], [971, 51, 920, 64], [972, 14, 920, 66, "padding"], [972, 21, 920, 73], [972, 23, 920, 75], [972, 25, 920, 77], [973, 14, 920, 79, "borderRadius"], [973, 26, 920, 91], [973, 28, 920, 93], [973, 30, 920, 95], [974, 14, 920, 97, "alignItems"], [974, 24, 920, 107], [974, 26, 920, 109], [975, 12, 920, 118], [975, 13, 920, 120], [976, 12, 920, 120, "children"], [976, 20, 920, 120], [976, 36, 921, 14], [976, 40, 921, 14, "_jsxDevRuntime"], [976, 54, 921, 14], [976, 55, 921, 14, "jsxDEV"], [976, 61, 921, 14], [976, 63, 921, 15, "_ActivityIndicator"], [976, 81, 921, 15], [976, 82, 921, 15, "default"], [976, 89, 921, 32], [977, 14, 921, 33, "size"], [977, 18, 921, 37], [977, 20, 921, 38], [977, 27, 921, 45], [978, 14, 921, 46, "color"], [978, 19, 921, 51], [978, 21, 921, 52], [978, 30, 921, 61], [979, 14, 921, 62, "style"], [979, 19, 921, 67], [979, 21, 921, 69], [980, 16, 921, 71, "marginBottom"], [980, 28, 921, 83], [980, 30, 921, 85], [981, 14, 921, 88], [982, 12, 921, 90], [983, 14, 921, 90, "fileName"], [983, 22, 921, 90], [983, 24, 921, 90, "_jsxFileName"], [983, 36, 921, 90], [984, 14, 921, 90, "lineNumber"], [984, 24, 921, 90], [985, 14, 921, 90, "columnNumber"], [985, 26, 921, 90], [986, 12, 921, 90], [986, 19, 921, 92], [986, 20, 921, 93], [986, 35, 922, 14], [986, 39, 922, 14, "_jsxDevRuntime"], [986, 53, 922, 14], [986, 54, 922, 14, "jsxDEV"], [986, 60, 922, 14], [986, 62, 922, 15, "_Text"], [986, 67, 922, 15], [986, 68, 922, 15, "default"], [986, 75, 922, 19], [987, 14, 922, 20, "style"], [987, 19, 922, 25], [987, 21, 922, 27], [988, 16, 922, 29, "color"], [988, 21, 922, 34], [988, 23, 922, 36], [988, 29, 922, 42], [989, 16, 922, 44, "fontSize"], [989, 24, 922, 52], [989, 26, 922, 54], [989, 28, 922, 56], [990, 16, 922, 58, "fontWeight"], [990, 26, 922, 68], [990, 28, 922, 70], [991, 14, 922, 76], [991, 15, 922, 78], [992, 14, 922, 78, "children"], [992, 22, 922, 78], [992, 24, 922, 79], [993, 12, 922, 101], [994, 14, 922, 101, "fileName"], [994, 22, 922, 101], [994, 24, 922, 101, "_jsxFileName"], [994, 36, 922, 101], [995, 14, 922, 101, "lineNumber"], [995, 24, 922, 101], [996, 14, 922, 101, "columnNumber"], [996, 26, 922, 101], [997, 12, 922, 101], [997, 19, 922, 107], [997, 20, 922, 108], [997, 35, 923, 14], [997, 39, 923, 14, "_jsxDevRuntime"], [997, 53, 923, 14], [997, 54, 923, 14, "jsxDEV"], [997, 60, 923, 14], [997, 62, 923, 15, "_Text"], [997, 67, 923, 15], [997, 68, 923, 15, "default"], [997, 75, 923, 19], [998, 14, 923, 20, "style"], [998, 19, 923, 25], [998, 21, 923, 27], [999, 16, 923, 29, "color"], [999, 21, 923, 34], [999, 23, 923, 36], [999, 32, 923, 45], [1000, 16, 923, 47, "fontSize"], [1000, 24, 923, 55], [1000, 26, 923, 57], [1000, 28, 923, 59], [1001, 16, 923, 61, "marginTop"], [1001, 25, 923, 70], [1001, 27, 923, 72], [1002, 14, 923, 74], [1002, 15, 923, 76], [1003, 14, 923, 76, "children"], [1003, 22, 923, 76], [1003, 24, 923, 77], [1004, 12, 923, 88], [1005, 14, 923, 88, "fileName"], [1005, 22, 923, 88], [1005, 24, 923, 88, "_jsxFileName"], [1005, 36, 923, 88], [1006, 14, 923, 88, "lineNumber"], [1006, 24, 923, 88], [1007, 14, 923, 88, "columnNumber"], [1007, 26, 923, 88], [1008, 12, 923, 88], [1008, 19, 923, 94], [1008, 20, 923, 95], [1009, 10, 923, 95], [1010, 12, 923, 95, "fileName"], [1010, 20, 923, 95], [1010, 22, 923, 95, "_jsxFileName"], [1010, 34, 923, 95], [1011, 12, 923, 95, "lineNumber"], [1011, 22, 923, 95], [1012, 12, 923, 95, "columnNumber"], [1012, 24, 923, 95], [1013, 10, 923, 95], [1013, 17, 924, 18], [1014, 8, 924, 19], [1015, 10, 924, 19, "fileName"], [1015, 18, 924, 19], [1015, 20, 924, 19, "_jsxFileName"], [1015, 32, 924, 19], [1016, 10, 924, 19, "lineNumber"], [1016, 20, 924, 19], [1017, 10, 924, 19, "columnNumber"], [1017, 22, 924, 19], [1018, 8, 924, 19], [1018, 15, 925, 16], [1018, 16, 926, 9], [1018, 18, 929, 9, "isCameraReady"], [1018, 31, 929, 22], [1018, 35, 929, 26, "previewBlurEnabled"], [1018, 53, 929, 44], [1018, 57, 929, 48, "viewSize"], [1018, 65, 929, 56], [1018, 66, 929, 57, "width"], [1018, 71, 929, 62], [1018, 74, 929, 65], [1018, 75, 929, 66], [1018, 92, 930, 10], [1018, 96, 930, 10, "_jsxDevRuntime"], [1018, 110, 930, 10], [1018, 111, 930, 10, "jsxDEV"], [1018, 117, 930, 10], [1018, 119, 930, 10, "_jsxDevRuntime"], [1018, 133, 930, 10], [1018, 134, 930, 10, "Fragment"], [1018, 142, 930, 10], [1019, 10, 930, 10, "children"], [1019, 18, 930, 10], [1019, 34, 932, 12], [1019, 38, 932, 12, "_jsxDevRuntime"], [1019, 52, 932, 12], [1019, 53, 932, 12, "jsxDEV"], [1019, 59, 932, 12], [1019, 61, 932, 13, "_LiveFaceCanvas"], [1019, 76, 932, 13], [1019, 77, 932, 13, "default"], [1019, 84, 932, 27], [1020, 12, 932, 28, "containerId"], [1020, 23, 932, 39], [1020, 25, 932, 40], [1020, 42, 932, 57], [1021, 12, 932, 58, "width"], [1021, 17, 932, 63], [1021, 19, 932, 65, "viewSize"], [1021, 27, 932, 73], [1021, 28, 932, 74, "width"], [1021, 33, 932, 80], [1022, 12, 932, 81, "height"], [1022, 18, 932, 87], [1022, 20, 932, 89, "viewSize"], [1022, 28, 932, 97], [1022, 29, 932, 98, "height"], [1023, 10, 932, 105], [1024, 12, 932, 105, "fileName"], [1024, 20, 932, 105], [1024, 22, 932, 105, "_jsxFileName"], [1024, 34, 932, 105], [1025, 12, 932, 105, "lineNumber"], [1025, 22, 932, 105], [1026, 12, 932, 105, "columnNumber"], [1026, 24, 932, 105], [1027, 10, 932, 105], [1027, 17, 932, 107], [1027, 18, 932, 108], [1027, 33, 933, 12], [1027, 37, 933, 12, "_jsxDevRuntime"], [1027, 51, 933, 12], [1027, 52, 933, 12, "jsxDEV"], [1027, 58, 933, 12], [1027, 60, 933, 13, "_View"], [1027, 65, 933, 13], [1027, 66, 933, 13, "default"], [1027, 73, 933, 17], [1028, 12, 933, 18, "style"], [1028, 17, 933, 23], [1028, 19, 933, 25], [1028, 20, 933, 26, "StyleSheet"], [1028, 39, 933, 36], [1028, 40, 933, 37, "absoluteFill"], [1028, 52, 933, 49], [1028, 54, 933, 51], [1029, 14, 933, 53, "pointerEvents"], [1029, 27, 933, 66], [1029, 29, 933, 68], [1030, 12, 933, 75], [1030, 13, 933, 76], [1030, 14, 933, 78], [1031, 12, 933, 78, "children"], [1031, 20, 933, 78], [1031, 36, 935, 12], [1031, 40, 935, 12, "_jsxDevRuntime"], [1031, 54, 935, 12], [1031, 55, 935, 12, "jsxDEV"], [1031, 61, 935, 12], [1031, 63, 935, 13, "_expoBlur"], [1031, 72, 935, 13], [1031, 73, 935, 13, "BlurView"], [1031, 81, 935, 21], [1032, 14, 935, 22, "intensity"], [1032, 23, 935, 31], [1032, 25, 935, 33], [1032, 27, 935, 36], [1033, 14, 935, 37, "tint"], [1033, 18, 935, 41], [1033, 20, 935, 42], [1033, 26, 935, 48], [1034, 14, 935, 49, "style"], [1034, 19, 935, 54], [1034, 21, 935, 56], [1034, 22, 935, 57, "styles"], [1034, 28, 935, 63], [1034, 29, 935, 64, "blurZone"], [1034, 37, 935, 72], [1034, 39, 935, 74], [1035, 16, 936, 14, "left"], [1035, 20, 936, 18], [1035, 22, 936, 20], [1035, 23, 936, 21], [1036, 16, 937, 14, "top"], [1036, 19, 937, 17], [1036, 21, 937, 19, "viewSize"], [1036, 29, 937, 27], [1036, 30, 937, 28, "height"], [1036, 36, 937, 34], [1036, 39, 937, 37], [1036, 42, 937, 40], [1037, 16, 938, 14, "width"], [1037, 21, 938, 19], [1037, 23, 938, 21, "viewSize"], [1037, 31, 938, 29], [1037, 32, 938, 30, "width"], [1037, 37, 938, 35], [1038, 16, 939, 14, "height"], [1038, 22, 939, 20], [1038, 24, 939, 22, "viewSize"], [1038, 32, 939, 30], [1038, 33, 939, 31, "height"], [1038, 39, 939, 37], [1038, 42, 939, 40], [1038, 46, 939, 44], [1039, 16, 940, 14, "borderRadius"], [1039, 28, 940, 26], [1039, 30, 940, 28], [1040, 14, 941, 12], [1040, 15, 941, 13], [1041, 12, 941, 15], [1042, 14, 941, 15, "fileName"], [1042, 22, 941, 15], [1042, 24, 941, 15, "_jsxFileName"], [1042, 36, 941, 15], [1043, 14, 941, 15, "lineNumber"], [1043, 24, 941, 15], [1044, 14, 941, 15, "columnNumber"], [1044, 26, 941, 15], [1045, 12, 941, 15], [1045, 19, 941, 17], [1045, 20, 941, 18], [1045, 35, 943, 12], [1045, 39, 943, 12, "_jsxDevRuntime"], [1045, 53, 943, 12], [1045, 54, 943, 12, "jsxDEV"], [1045, 60, 943, 12], [1045, 62, 943, 13, "_expoBlur"], [1045, 71, 943, 13], [1045, 72, 943, 13, "BlurView"], [1045, 80, 943, 21], [1046, 14, 943, 22, "intensity"], [1046, 23, 943, 31], [1046, 25, 943, 33], [1046, 27, 943, 36], [1047, 14, 943, 37, "tint"], [1047, 18, 943, 41], [1047, 20, 943, 42], [1047, 26, 943, 48], [1048, 14, 943, 49, "style"], [1048, 19, 943, 54], [1048, 21, 943, 56], [1048, 22, 943, 57, "styles"], [1048, 28, 943, 63], [1048, 29, 943, 64, "blurZone"], [1048, 37, 943, 72], [1048, 39, 943, 74], [1049, 16, 944, 14, "left"], [1049, 20, 944, 18], [1049, 22, 944, 20], [1049, 23, 944, 21], [1050, 16, 945, 14, "top"], [1050, 19, 945, 17], [1050, 21, 945, 19], [1050, 22, 945, 20], [1051, 16, 946, 14, "width"], [1051, 21, 946, 19], [1051, 23, 946, 21, "viewSize"], [1051, 31, 946, 29], [1051, 32, 946, 30, "width"], [1051, 37, 946, 35], [1052, 16, 947, 14, "height"], [1052, 22, 947, 20], [1052, 24, 947, 22, "viewSize"], [1052, 32, 947, 30], [1052, 33, 947, 31, "height"], [1052, 39, 947, 37], [1052, 42, 947, 40], [1052, 45, 947, 43], [1053, 16, 948, 14, "borderRadius"], [1053, 28, 948, 26], [1053, 30, 948, 28], [1054, 14, 949, 12], [1054, 15, 949, 13], [1055, 12, 949, 15], [1056, 14, 949, 15, "fileName"], [1056, 22, 949, 15], [1056, 24, 949, 15, "_jsxFileName"], [1056, 36, 949, 15], [1057, 14, 949, 15, "lineNumber"], [1057, 24, 949, 15], [1058, 14, 949, 15, "columnNumber"], [1058, 26, 949, 15], [1059, 12, 949, 15], [1059, 19, 949, 17], [1059, 20, 949, 18], [1059, 35, 951, 12], [1059, 39, 951, 12, "_jsxDevRuntime"], [1059, 53, 951, 12], [1059, 54, 951, 12, "jsxDEV"], [1059, 60, 951, 12], [1059, 62, 951, 13, "_expoBlur"], [1059, 71, 951, 13], [1059, 72, 951, 13, "BlurView"], [1059, 80, 951, 21], [1060, 14, 951, 22, "intensity"], [1060, 23, 951, 31], [1060, 25, 951, 33], [1060, 27, 951, 36], [1061, 14, 951, 37, "tint"], [1061, 18, 951, 41], [1061, 20, 951, 42], [1061, 26, 951, 48], [1062, 14, 951, 49, "style"], [1062, 19, 951, 54], [1062, 21, 951, 56], [1062, 22, 951, 57, "styles"], [1062, 28, 951, 63], [1062, 29, 951, 64, "blurZone"], [1062, 37, 951, 72], [1062, 39, 951, 74], [1063, 16, 952, 14, "left"], [1063, 20, 952, 18], [1063, 22, 952, 20, "viewSize"], [1063, 30, 952, 28], [1063, 31, 952, 29, "width"], [1063, 36, 952, 34], [1063, 39, 952, 37], [1063, 42, 952, 40], [1063, 45, 952, 44, "viewSize"], [1063, 53, 952, 52], [1063, 54, 952, 53, "width"], [1063, 59, 952, 58], [1063, 62, 952, 61], [1063, 66, 952, 66], [1064, 16, 953, 14, "top"], [1064, 19, 953, 17], [1064, 21, 953, 19, "viewSize"], [1064, 29, 953, 27], [1064, 30, 953, 28, "height"], [1064, 36, 953, 34], [1064, 39, 953, 37], [1064, 43, 953, 41], [1064, 46, 953, 45, "viewSize"], [1064, 54, 953, 53], [1064, 55, 953, 54, "width"], [1064, 60, 953, 59], [1064, 63, 953, 62], [1064, 67, 953, 67], [1065, 16, 954, 14, "width"], [1065, 21, 954, 19], [1065, 23, 954, 21, "viewSize"], [1065, 31, 954, 29], [1065, 32, 954, 30, "width"], [1065, 37, 954, 35], [1065, 40, 954, 38], [1065, 43, 954, 41], [1066, 16, 955, 14, "height"], [1066, 22, 955, 20], [1066, 24, 955, 22, "viewSize"], [1066, 32, 955, 30], [1066, 33, 955, 31, "width"], [1066, 38, 955, 36], [1066, 41, 955, 39], [1066, 44, 955, 42], [1067, 16, 956, 14, "borderRadius"], [1067, 28, 956, 26], [1067, 30, 956, 29, "viewSize"], [1067, 38, 956, 37], [1067, 39, 956, 38, "width"], [1067, 44, 956, 43], [1067, 47, 956, 46], [1067, 50, 956, 49], [1067, 53, 956, 53], [1068, 14, 957, 12], [1068, 15, 957, 13], [1069, 12, 957, 15], [1070, 14, 957, 15, "fileName"], [1070, 22, 957, 15], [1070, 24, 957, 15, "_jsxFileName"], [1070, 36, 957, 15], [1071, 14, 957, 15, "lineNumber"], [1071, 24, 957, 15], [1072, 14, 957, 15, "columnNumber"], [1072, 26, 957, 15], [1073, 12, 957, 15], [1073, 19, 957, 17], [1073, 20, 957, 18], [1073, 35, 958, 12], [1073, 39, 958, 12, "_jsxDevRuntime"], [1073, 53, 958, 12], [1073, 54, 958, 12, "jsxDEV"], [1073, 60, 958, 12], [1073, 62, 958, 13, "_expoBlur"], [1073, 71, 958, 13], [1073, 72, 958, 13, "BlurView"], [1073, 80, 958, 21], [1074, 14, 958, 22, "intensity"], [1074, 23, 958, 31], [1074, 25, 958, 33], [1074, 27, 958, 36], [1075, 14, 958, 37, "tint"], [1075, 18, 958, 41], [1075, 20, 958, 42], [1075, 26, 958, 48], [1076, 14, 958, 49, "style"], [1076, 19, 958, 54], [1076, 21, 958, 56], [1076, 22, 958, 57, "styles"], [1076, 28, 958, 63], [1076, 29, 958, 64, "blurZone"], [1076, 37, 958, 72], [1076, 39, 958, 74], [1077, 16, 959, 14, "left"], [1077, 20, 959, 18], [1077, 22, 959, 20, "viewSize"], [1077, 30, 959, 28], [1077, 31, 959, 29, "width"], [1077, 36, 959, 34], [1077, 39, 959, 37], [1077, 42, 959, 40], [1077, 45, 959, 44, "viewSize"], [1077, 53, 959, 52], [1077, 54, 959, 53, "width"], [1077, 59, 959, 58], [1077, 62, 959, 61], [1077, 66, 959, 66], [1078, 16, 960, 14, "top"], [1078, 19, 960, 17], [1078, 21, 960, 19, "viewSize"], [1078, 29, 960, 27], [1078, 30, 960, 28, "height"], [1078, 36, 960, 34], [1078, 39, 960, 37], [1078, 42, 960, 40], [1078, 45, 960, 44, "viewSize"], [1078, 53, 960, 52], [1078, 54, 960, 53, "width"], [1078, 59, 960, 58], [1078, 62, 960, 61], [1078, 66, 960, 66], [1079, 16, 961, 14, "width"], [1079, 21, 961, 19], [1079, 23, 961, 21, "viewSize"], [1079, 31, 961, 29], [1079, 32, 961, 30, "width"], [1079, 37, 961, 35], [1079, 40, 961, 38], [1079, 43, 961, 41], [1080, 16, 962, 14, "height"], [1080, 22, 962, 20], [1080, 24, 962, 22, "viewSize"], [1080, 32, 962, 30], [1080, 33, 962, 31, "width"], [1080, 38, 962, 36], [1080, 41, 962, 39], [1080, 44, 962, 42], [1081, 16, 963, 14, "borderRadius"], [1081, 28, 963, 26], [1081, 30, 963, 29, "viewSize"], [1081, 38, 963, 37], [1081, 39, 963, 38, "width"], [1081, 44, 963, 43], [1081, 47, 963, 46], [1081, 50, 963, 49], [1081, 53, 963, 53], [1082, 14, 964, 12], [1082, 15, 964, 13], [1083, 12, 964, 15], [1084, 14, 964, 15, "fileName"], [1084, 22, 964, 15], [1084, 24, 964, 15, "_jsxFileName"], [1084, 36, 964, 15], [1085, 14, 964, 15, "lineNumber"], [1085, 24, 964, 15], [1086, 14, 964, 15, "columnNumber"], [1086, 26, 964, 15], [1087, 12, 964, 15], [1087, 19, 964, 17], [1087, 20, 964, 18], [1087, 35, 965, 12], [1087, 39, 965, 12, "_jsxDevRuntime"], [1087, 53, 965, 12], [1087, 54, 965, 12, "jsxDEV"], [1087, 60, 965, 12], [1087, 62, 965, 13, "_expoBlur"], [1087, 71, 965, 13], [1087, 72, 965, 13, "BlurView"], [1087, 80, 965, 21], [1088, 14, 965, 22, "intensity"], [1088, 23, 965, 31], [1088, 25, 965, 33], [1088, 27, 965, 36], [1089, 14, 965, 37, "tint"], [1089, 18, 965, 41], [1089, 20, 965, 42], [1089, 26, 965, 48], [1090, 14, 965, 49, "style"], [1090, 19, 965, 54], [1090, 21, 965, 56], [1090, 22, 965, 57, "styles"], [1090, 28, 965, 63], [1090, 29, 965, 64, "blurZone"], [1090, 37, 965, 72], [1090, 39, 965, 74], [1091, 16, 966, 14, "left"], [1091, 20, 966, 18], [1091, 22, 966, 20, "viewSize"], [1091, 30, 966, 28], [1091, 31, 966, 29, "width"], [1091, 36, 966, 34], [1091, 39, 966, 37], [1091, 42, 966, 40], [1091, 45, 966, 44, "viewSize"], [1091, 53, 966, 52], [1091, 54, 966, 53, "width"], [1091, 59, 966, 58], [1091, 62, 966, 61], [1091, 66, 966, 66], [1092, 16, 967, 14, "top"], [1092, 19, 967, 17], [1092, 21, 967, 19, "viewSize"], [1092, 29, 967, 27], [1092, 30, 967, 28, "height"], [1092, 36, 967, 34], [1092, 39, 967, 37], [1092, 42, 967, 40], [1092, 45, 967, 44, "viewSize"], [1092, 53, 967, 52], [1092, 54, 967, 53, "width"], [1092, 59, 967, 58], [1092, 62, 967, 61], [1092, 66, 967, 66], [1093, 16, 968, 14, "width"], [1093, 21, 968, 19], [1093, 23, 968, 21, "viewSize"], [1093, 31, 968, 29], [1093, 32, 968, 30, "width"], [1093, 37, 968, 35], [1093, 40, 968, 38], [1093, 43, 968, 41], [1094, 16, 969, 14, "height"], [1094, 22, 969, 20], [1094, 24, 969, 22, "viewSize"], [1094, 32, 969, 30], [1094, 33, 969, 31, "width"], [1094, 38, 969, 36], [1094, 41, 969, 39], [1094, 44, 969, 42], [1095, 16, 970, 14, "borderRadius"], [1095, 28, 970, 26], [1095, 30, 970, 29, "viewSize"], [1095, 38, 970, 37], [1095, 39, 970, 38, "width"], [1095, 44, 970, 43], [1095, 47, 970, 46], [1095, 50, 970, 49], [1095, 53, 970, 53], [1096, 14, 971, 12], [1096, 15, 971, 13], [1097, 12, 971, 15], [1098, 14, 971, 15, "fileName"], [1098, 22, 971, 15], [1098, 24, 971, 15, "_jsxFileName"], [1098, 36, 971, 15], [1099, 14, 971, 15, "lineNumber"], [1099, 24, 971, 15], [1100, 14, 971, 15, "columnNumber"], [1100, 26, 971, 15], [1101, 12, 971, 15], [1101, 19, 971, 17], [1101, 20, 971, 18], [1101, 22, 973, 13, "__DEV__"], [1101, 29, 973, 20], [1101, 46, 974, 14], [1101, 50, 974, 14, "_jsxDevRuntime"], [1101, 64, 974, 14], [1101, 65, 974, 14, "jsxDEV"], [1101, 71, 974, 14], [1101, 73, 974, 15, "_View"], [1101, 78, 974, 15], [1101, 79, 974, 15, "default"], [1101, 86, 974, 19], [1102, 14, 974, 20, "style"], [1102, 19, 974, 25], [1102, 21, 974, 27, "styles"], [1102, 27, 974, 33], [1102, 28, 974, 34, "previewChip"], [1102, 39, 974, 46], [1103, 14, 974, 46, "children"], [1103, 22, 974, 46], [1103, 37, 975, 16], [1103, 41, 975, 16, "_jsxDevRuntime"], [1103, 55, 975, 16], [1103, 56, 975, 16, "jsxDEV"], [1103, 62, 975, 16], [1103, 64, 975, 17, "_Text"], [1103, 69, 975, 17], [1103, 70, 975, 17, "default"], [1103, 77, 975, 21], [1104, 16, 975, 22, "style"], [1104, 21, 975, 27], [1104, 23, 975, 29, "styles"], [1104, 29, 975, 35], [1104, 30, 975, 36, "previewChipText"], [1104, 45, 975, 52], [1105, 16, 975, 52, "children"], [1105, 24, 975, 52], [1105, 26, 975, 53], [1106, 14, 975, 73], [1107, 16, 975, 73, "fileName"], [1107, 24, 975, 73], [1107, 26, 975, 73, "_jsxFileName"], [1107, 38, 975, 73], [1108, 16, 975, 73, "lineNumber"], [1108, 26, 975, 73], [1109, 16, 975, 73, "columnNumber"], [1109, 28, 975, 73], [1110, 14, 975, 73], [1110, 21, 975, 79], [1111, 12, 975, 80], [1112, 14, 975, 80, "fileName"], [1112, 22, 975, 80], [1112, 24, 975, 80, "_jsxFileName"], [1112, 36, 975, 80], [1113, 14, 975, 80, "lineNumber"], [1113, 24, 975, 80], [1114, 14, 975, 80, "columnNumber"], [1114, 26, 975, 80], [1115, 12, 975, 80], [1115, 19, 976, 20], [1115, 20, 977, 13], [1116, 10, 977, 13], [1117, 12, 977, 13, "fileName"], [1117, 20, 977, 13], [1117, 22, 977, 13, "_jsxFileName"], [1117, 34, 977, 13], [1118, 12, 977, 13, "lineNumber"], [1118, 22, 977, 13], [1119, 12, 977, 13, "columnNumber"], [1119, 24, 977, 13], [1120, 10, 977, 13], [1120, 17, 978, 18], [1120, 18, 978, 19], [1121, 8, 978, 19], [1121, 23, 979, 12], [1121, 24, 980, 9], [1121, 26, 982, 9, "isCameraReady"], [1121, 39, 982, 22], [1121, 56, 983, 10], [1121, 60, 983, 10, "_jsxDevRuntime"], [1121, 74, 983, 10], [1121, 75, 983, 10, "jsxDEV"], [1121, 81, 983, 10], [1121, 83, 983, 10, "_jsxDevRuntime"], [1121, 97, 983, 10], [1121, 98, 983, 10, "Fragment"], [1121, 106, 983, 10], [1122, 10, 983, 10, "children"], [1122, 18, 983, 10], [1122, 34, 985, 12], [1122, 38, 985, 12, "_jsxDevRuntime"], [1122, 52, 985, 12], [1122, 53, 985, 12, "jsxDEV"], [1122, 59, 985, 12], [1122, 61, 985, 13, "_View"], [1122, 66, 985, 13], [1122, 67, 985, 13, "default"], [1122, 74, 985, 17], [1123, 12, 985, 18, "style"], [1123, 17, 985, 23], [1123, 19, 985, 25, "styles"], [1123, 25, 985, 31], [1123, 26, 985, 32, "headerOverlay"], [1123, 39, 985, 46], [1124, 12, 985, 46, "children"], [1124, 20, 985, 46], [1124, 35, 986, 14], [1124, 39, 986, 14, "_jsxDevRuntime"], [1124, 53, 986, 14], [1124, 54, 986, 14, "jsxDEV"], [1124, 60, 986, 14], [1124, 62, 986, 15, "_View"], [1124, 67, 986, 15], [1124, 68, 986, 15, "default"], [1124, 75, 986, 19], [1125, 14, 986, 20, "style"], [1125, 19, 986, 25], [1125, 21, 986, 27, "styles"], [1125, 27, 986, 33], [1125, 28, 986, 34, "headerContent"], [1125, 41, 986, 48], [1126, 14, 986, 48, "children"], [1126, 22, 986, 48], [1126, 38, 987, 16], [1126, 42, 987, 16, "_jsxDevRuntime"], [1126, 56, 987, 16], [1126, 57, 987, 16, "jsxDEV"], [1126, 63, 987, 16], [1126, 65, 987, 17, "_View"], [1126, 70, 987, 17], [1126, 71, 987, 17, "default"], [1126, 78, 987, 21], [1127, 16, 987, 22, "style"], [1127, 21, 987, 27], [1127, 23, 987, 29, "styles"], [1127, 29, 987, 35], [1127, 30, 987, 36, "headerLeft"], [1127, 40, 987, 47], [1128, 16, 987, 47, "children"], [1128, 24, 987, 47], [1128, 40, 988, 18], [1128, 44, 988, 18, "_jsxDevRuntime"], [1128, 58, 988, 18], [1128, 59, 988, 18, "jsxDEV"], [1128, 65, 988, 18], [1128, 67, 988, 19, "_Text"], [1128, 72, 988, 19], [1128, 73, 988, 19, "default"], [1128, 80, 988, 23], [1129, 18, 988, 24, "style"], [1129, 23, 988, 29], [1129, 25, 988, 31, "styles"], [1129, 31, 988, 37], [1129, 32, 988, 38, "headerTitle"], [1129, 43, 988, 50], [1130, 18, 988, 50, "children"], [1130, 26, 988, 50], [1130, 28, 988, 51], [1131, 16, 988, 62], [1132, 18, 988, 62, "fileName"], [1132, 26, 988, 62], [1132, 28, 988, 62, "_jsxFileName"], [1132, 40, 988, 62], [1133, 18, 988, 62, "lineNumber"], [1133, 28, 988, 62], [1134, 18, 988, 62, "columnNumber"], [1134, 30, 988, 62], [1135, 16, 988, 62], [1135, 23, 988, 68], [1135, 24, 988, 69], [1135, 39, 989, 18], [1135, 43, 989, 18, "_jsxDevRuntime"], [1135, 57, 989, 18], [1135, 58, 989, 18, "jsxDEV"], [1135, 64, 989, 18], [1135, 66, 989, 19, "_View"], [1135, 71, 989, 19], [1135, 72, 989, 19, "default"], [1135, 79, 989, 23], [1136, 18, 989, 24, "style"], [1136, 23, 989, 29], [1136, 25, 989, 31, "styles"], [1136, 31, 989, 37], [1136, 32, 989, 38, "subtitleRow"], [1136, 43, 989, 50], [1137, 18, 989, 50, "children"], [1137, 26, 989, 50], [1137, 42, 990, 20], [1137, 46, 990, 20, "_jsxDevRuntime"], [1137, 60, 990, 20], [1137, 61, 990, 20, "jsxDEV"], [1137, 67, 990, 20], [1137, 69, 990, 21, "_Text"], [1137, 74, 990, 21], [1137, 75, 990, 21, "default"], [1137, 82, 990, 25], [1138, 20, 990, 26, "style"], [1138, 25, 990, 31], [1138, 27, 990, 33, "styles"], [1138, 33, 990, 39], [1138, 34, 990, 40, "webIcon"], [1138, 41, 990, 48], [1139, 20, 990, 48, "children"], [1139, 28, 990, 48], [1139, 30, 990, 49], [1140, 18, 990, 51], [1141, 20, 990, 51, "fileName"], [1141, 28, 990, 51], [1141, 30, 990, 51, "_jsxFileName"], [1141, 42, 990, 51], [1142, 20, 990, 51, "lineNumber"], [1142, 30, 990, 51], [1143, 20, 990, 51, "columnNumber"], [1143, 32, 990, 51], [1144, 18, 990, 51], [1144, 25, 990, 57], [1144, 26, 990, 58], [1144, 41, 991, 20], [1144, 45, 991, 20, "_jsxDevRuntime"], [1144, 59, 991, 20], [1144, 60, 991, 20, "jsxDEV"], [1144, 66, 991, 20], [1144, 68, 991, 21, "_Text"], [1144, 73, 991, 21], [1144, 74, 991, 21, "default"], [1144, 81, 991, 25], [1145, 20, 991, 26, "style"], [1145, 25, 991, 31], [1145, 27, 991, 33, "styles"], [1145, 33, 991, 39], [1145, 34, 991, 40, "headerSubtitle"], [1145, 48, 991, 55], [1146, 20, 991, 55, "children"], [1146, 28, 991, 55], [1146, 30, 991, 56], [1147, 18, 991, 71], [1148, 20, 991, 71, "fileName"], [1148, 28, 991, 71], [1148, 30, 991, 71, "_jsxFileName"], [1148, 42, 991, 71], [1149, 20, 991, 71, "lineNumber"], [1149, 30, 991, 71], [1150, 20, 991, 71, "columnNumber"], [1150, 32, 991, 71], [1151, 18, 991, 71], [1151, 25, 991, 77], [1151, 26, 991, 78], [1152, 16, 991, 78], [1153, 18, 991, 78, "fileName"], [1153, 26, 991, 78], [1153, 28, 991, 78, "_jsxFileName"], [1153, 40, 991, 78], [1154, 18, 991, 78, "lineNumber"], [1154, 28, 991, 78], [1155, 18, 991, 78, "columnNumber"], [1155, 30, 991, 78], [1156, 16, 991, 78], [1156, 23, 992, 24], [1156, 24, 992, 25], [1156, 26, 993, 19, "challengeCode"], [1156, 39, 993, 32], [1156, 56, 994, 20], [1156, 60, 994, 20, "_jsxDevRuntime"], [1156, 74, 994, 20], [1156, 75, 994, 20, "jsxDEV"], [1156, 81, 994, 20], [1156, 83, 994, 21, "_View"], [1156, 88, 994, 21], [1156, 89, 994, 21, "default"], [1156, 96, 994, 25], [1157, 18, 994, 26, "style"], [1157, 23, 994, 31], [1157, 25, 994, 33, "styles"], [1157, 31, 994, 39], [1157, 32, 994, 40, "challengeRow"], [1157, 44, 994, 53], [1158, 18, 994, 53, "children"], [1158, 26, 994, 53], [1158, 42, 995, 22], [1158, 46, 995, 22, "_jsxDevRuntime"], [1158, 60, 995, 22], [1158, 61, 995, 22, "jsxDEV"], [1158, 67, 995, 22], [1158, 69, 995, 23, "_lucideReactNative"], [1158, 87, 995, 23], [1158, 88, 995, 23, "Shield"], [1158, 94, 995, 29], [1159, 20, 995, 30, "size"], [1159, 24, 995, 34], [1159, 26, 995, 36], [1159, 28, 995, 39], [1160, 20, 995, 40, "color"], [1160, 25, 995, 45], [1160, 27, 995, 46], [1161, 18, 995, 52], [1162, 20, 995, 52, "fileName"], [1162, 28, 995, 52], [1162, 30, 995, 52, "_jsxFileName"], [1162, 42, 995, 52], [1163, 20, 995, 52, "lineNumber"], [1163, 30, 995, 52], [1164, 20, 995, 52, "columnNumber"], [1164, 32, 995, 52], [1165, 18, 995, 52], [1165, 25, 995, 54], [1165, 26, 995, 55], [1165, 41, 996, 22], [1165, 45, 996, 22, "_jsxDevRuntime"], [1165, 59, 996, 22], [1165, 60, 996, 22, "jsxDEV"], [1165, 66, 996, 22], [1165, 68, 996, 23, "_Text"], [1165, 73, 996, 23], [1165, 74, 996, 23, "default"], [1165, 81, 996, 27], [1166, 20, 996, 28, "style"], [1166, 25, 996, 33], [1166, 27, 996, 35, "styles"], [1166, 33, 996, 41], [1166, 34, 996, 42, "challengeCode"], [1166, 47, 996, 56], [1167, 20, 996, 56, "children"], [1167, 28, 996, 56], [1167, 30, 996, 58, "challengeCode"], [1168, 18, 996, 71], [1169, 20, 996, 71, "fileName"], [1169, 28, 996, 71], [1169, 30, 996, 71, "_jsxFileName"], [1169, 42, 996, 71], [1170, 20, 996, 71, "lineNumber"], [1170, 30, 996, 71], [1171, 20, 996, 71, "columnNumber"], [1171, 32, 996, 71], [1172, 18, 996, 71], [1172, 25, 996, 78], [1172, 26, 996, 79], [1173, 16, 996, 79], [1174, 18, 996, 79, "fileName"], [1174, 26, 996, 79], [1174, 28, 996, 79, "_jsxFileName"], [1174, 40, 996, 79], [1175, 18, 996, 79, "lineNumber"], [1175, 28, 996, 79], [1176, 18, 996, 79, "columnNumber"], [1176, 30, 996, 79], [1177, 16, 996, 79], [1177, 23, 997, 26], [1177, 24, 998, 19], [1178, 14, 998, 19], [1179, 16, 998, 19, "fileName"], [1179, 24, 998, 19], [1179, 26, 998, 19, "_jsxFileName"], [1179, 38, 998, 19], [1180, 16, 998, 19, "lineNumber"], [1180, 26, 998, 19], [1181, 16, 998, 19, "columnNumber"], [1181, 28, 998, 19], [1182, 14, 998, 19], [1182, 21, 999, 22], [1182, 22, 999, 23], [1182, 37, 1000, 16], [1182, 41, 1000, 16, "_jsxDevRuntime"], [1182, 55, 1000, 16], [1182, 56, 1000, 16, "jsxDEV"], [1182, 62, 1000, 16], [1182, 64, 1000, 17, "_TouchableOpacity"], [1182, 81, 1000, 17], [1182, 82, 1000, 17, "default"], [1182, 89, 1000, 33], [1183, 16, 1000, 34, "onPress"], [1183, 23, 1000, 41], [1183, 25, 1000, 43, "onCancel"], [1183, 33, 1000, 52], [1184, 16, 1000, 53, "style"], [1184, 21, 1000, 58], [1184, 23, 1000, 60, "styles"], [1184, 29, 1000, 66], [1184, 30, 1000, 67, "closeButton"], [1184, 41, 1000, 79], [1185, 16, 1000, 79, "children"], [1185, 24, 1000, 79], [1185, 39, 1001, 18], [1185, 43, 1001, 18, "_jsxDevRuntime"], [1185, 57, 1001, 18], [1185, 58, 1001, 18, "jsxDEV"], [1185, 64, 1001, 18], [1185, 66, 1001, 19, "_lucideReactNative"], [1185, 84, 1001, 19], [1185, 85, 1001, 19, "X"], [1185, 86, 1001, 20], [1186, 18, 1001, 21, "size"], [1186, 22, 1001, 25], [1186, 24, 1001, 27], [1186, 26, 1001, 30], [1187, 18, 1001, 31, "color"], [1187, 23, 1001, 36], [1187, 25, 1001, 37], [1188, 16, 1001, 43], [1189, 18, 1001, 43, "fileName"], [1189, 26, 1001, 43], [1189, 28, 1001, 43, "_jsxFileName"], [1189, 40, 1001, 43], [1190, 18, 1001, 43, "lineNumber"], [1190, 28, 1001, 43], [1191, 18, 1001, 43, "columnNumber"], [1191, 30, 1001, 43], [1192, 16, 1001, 43], [1192, 23, 1001, 45], [1193, 14, 1001, 46], [1194, 16, 1001, 46, "fileName"], [1194, 24, 1001, 46], [1194, 26, 1001, 46, "_jsxFileName"], [1194, 38, 1001, 46], [1195, 16, 1001, 46, "lineNumber"], [1195, 26, 1001, 46], [1196, 16, 1001, 46, "columnNumber"], [1196, 28, 1001, 46], [1197, 14, 1001, 46], [1197, 21, 1002, 34], [1197, 22, 1002, 35], [1198, 12, 1002, 35], [1199, 14, 1002, 35, "fileName"], [1199, 22, 1002, 35], [1199, 24, 1002, 35, "_jsxFileName"], [1199, 36, 1002, 35], [1200, 14, 1002, 35, "lineNumber"], [1200, 24, 1002, 35], [1201, 14, 1002, 35, "columnNumber"], [1201, 26, 1002, 35], [1202, 12, 1002, 35], [1202, 19, 1003, 20], [1203, 10, 1003, 21], [1204, 12, 1003, 21, "fileName"], [1204, 20, 1003, 21], [1204, 22, 1003, 21, "_jsxFileName"], [1204, 34, 1003, 21], [1205, 12, 1003, 21, "lineNumber"], [1205, 22, 1003, 21], [1206, 12, 1003, 21, "columnNumber"], [1206, 24, 1003, 21], [1207, 10, 1003, 21], [1207, 17, 1004, 18], [1207, 18, 1004, 19], [1207, 33, 1006, 12], [1207, 37, 1006, 12, "_jsxDevRuntime"], [1207, 51, 1006, 12], [1207, 52, 1006, 12, "jsxDEV"], [1207, 58, 1006, 12], [1207, 60, 1006, 13, "_View"], [1207, 65, 1006, 13], [1207, 66, 1006, 13, "default"], [1207, 73, 1006, 17], [1208, 12, 1006, 18, "style"], [1208, 17, 1006, 23], [1208, 19, 1006, 25, "styles"], [1208, 25, 1006, 31], [1208, 26, 1006, 32, "privacyNotice"], [1208, 39, 1006, 46], [1209, 12, 1006, 46, "children"], [1209, 20, 1006, 46], [1209, 36, 1007, 14], [1209, 40, 1007, 14, "_jsxDevRuntime"], [1209, 54, 1007, 14], [1209, 55, 1007, 14, "jsxDEV"], [1209, 61, 1007, 14], [1209, 63, 1007, 15, "_lucideReactNative"], [1209, 81, 1007, 15], [1209, 82, 1007, 15, "Shield"], [1209, 88, 1007, 21], [1210, 14, 1007, 22, "size"], [1210, 18, 1007, 26], [1210, 20, 1007, 28], [1210, 22, 1007, 31], [1211, 14, 1007, 32, "color"], [1211, 19, 1007, 37], [1211, 21, 1007, 38], [1212, 12, 1007, 47], [1213, 14, 1007, 47, "fileName"], [1213, 22, 1007, 47], [1213, 24, 1007, 47, "_jsxFileName"], [1213, 36, 1007, 47], [1214, 14, 1007, 47, "lineNumber"], [1214, 24, 1007, 47], [1215, 14, 1007, 47, "columnNumber"], [1215, 26, 1007, 47], [1216, 12, 1007, 47], [1216, 19, 1007, 49], [1216, 20, 1007, 50], [1216, 35, 1008, 14], [1216, 39, 1008, 14, "_jsxDevRuntime"], [1216, 53, 1008, 14], [1216, 54, 1008, 14, "jsxDEV"], [1216, 60, 1008, 14], [1216, 62, 1008, 15, "_Text"], [1216, 67, 1008, 15], [1216, 68, 1008, 15, "default"], [1216, 75, 1008, 19], [1217, 14, 1008, 20, "style"], [1217, 19, 1008, 25], [1217, 21, 1008, 27, "styles"], [1217, 27, 1008, 33], [1217, 28, 1008, 34, "privacyText"], [1217, 39, 1008, 46], [1218, 14, 1008, 46, "children"], [1218, 22, 1008, 46], [1218, 24, 1008, 47], [1219, 12, 1010, 14], [1220, 14, 1010, 14, "fileName"], [1220, 22, 1010, 14], [1220, 24, 1010, 14, "_jsxFileName"], [1220, 36, 1010, 14], [1221, 14, 1010, 14, "lineNumber"], [1221, 24, 1010, 14], [1222, 14, 1010, 14, "columnNumber"], [1222, 26, 1010, 14], [1223, 12, 1010, 14], [1223, 19, 1010, 20], [1223, 20, 1010, 21], [1224, 10, 1010, 21], [1225, 12, 1010, 21, "fileName"], [1225, 20, 1010, 21], [1225, 22, 1010, 21, "_jsxFileName"], [1225, 34, 1010, 21], [1226, 12, 1010, 21, "lineNumber"], [1226, 22, 1010, 21], [1227, 12, 1010, 21, "columnNumber"], [1227, 24, 1010, 21], [1228, 10, 1010, 21], [1228, 17, 1011, 18], [1228, 18, 1011, 19], [1228, 33, 1013, 12], [1228, 37, 1013, 12, "_jsxDevRuntime"], [1228, 51, 1013, 12], [1228, 52, 1013, 12, "jsxDEV"], [1228, 58, 1013, 12], [1228, 60, 1013, 13, "_View"], [1228, 65, 1013, 13], [1228, 66, 1013, 13, "default"], [1228, 73, 1013, 17], [1229, 12, 1013, 18, "style"], [1229, 17, 1013, 23], [1229, 19, 1013, 25, "styles"], [1229, 25, 1013, 31], [1229, 26, 1013, 32, "footer<PERSON><PERSON><PERSON>"], [1229, 39, 1013, 46], [1230, 12, 1013, 46, "children"], [1230, 20, 1013, 46], [1230, 36, 1014, 14], [1230, 40, 1014, 14, "_jsxDevRuntime"], [1230, 54, 1014, 14], [1230, 55, 1014, 14, "jsxDEV"], [1230, 61, 1014, 14], [1230, 63, 1014, 15, "_Text"], [1230, 68, 1014, 15], [1230, 69, 1014, 15, "default"], [1230, 76, 1014, 19], [1231, 14, 1014, 20, "style"], [1231, 19, 1014, 25], [1231, 21, 1014, 27, "styles"], [1231, 27, 1014, 33], [1231, 28, 1014, 34, "instruction"], [1231, 39, 1014, 46], [1232, 14, 1014, 46, "children"], [1232, 22, 1014, 46], [1232, 24, 1014, 47], [1233, 12, 1016, 14], [1234, 14, 1016, 14, "fileName"], [1234, 22, 1016, 14], [1234, 24, 1016, 14, "_jsxFileName"], [1234, 36, 1016, 14], [1235, 14, 1016, 14, "lineNumber"], [1235, 24, 1016, 14], [1236, 14, 1016, 14, "columnNumber"], [1236, 26, 1016, 14], [1237, 12, 1016, 14], [1237, 19, 1016, 20], [1237, 20, 1016, 21], [1237, 35, 1018, 14], [1237, 39, 1018, 14, "_jsxDevRuntime"], [1237, 53, 1018, 14], [1237, 54, 1018, 14, "jsxDEV"], [1237, 60, 1018, 14], [1237, 62, 1018, 15, "_TouchableOpacity"], [1237, 79, 1018, 15], [1237, 80, 1018, 15, "default"], [1237, 87, 1018, 31], [1238, 14, 1019, 16, "onPress"], [1238, 21, 1019, 23], [1238, 23, 1019, 25, "capturePhoto"], [1238, 35, 1019, 38], [1239, 14, 1020, 16, "disabled"], [1239, 22, 1020, 24], [1239, 24, 1020, 26, "processingState"], [1239, 39, 1020, 41], [1239, 44, 1020, 46], [1239, 50, 1020, 52], [1239, 54, 1020, 56], [1239, 55, 1020, 57, "isCameraReady"], [1239, 68, 1020, 71], [1240, 14, 1021, 16, "style"], [1240, 19, 1021, 21], [1240, 21, 1021, 23], [1240, 22, 1022, 18, "styles"], [1240, 28, 1022, 24], [1240, 29, 1022, 25, "shutterButton"], [1240, 42, 1022, 38], [1240, 44, 1023, 18, "processingState"], [1240, 59, 1023, 33], [1240, 64, 1023, 38], [1240, 70, 1023, 44], [1240, 74, 1023, 48, "styles"], [1240, 80, 1023, 54], [1240, 81, 1023, 55, "shutterButtonDisabled"], [1240, 102, 1023, 76], [1240, 103, 1024, 18], [1241, 14, 1024, 18, "children"], [1241, 22, 1024, 18], [1241, 24, 1026, 17, "processingState"], [1241, 39, 1026, 32], [1241, 44, 1026, 37], [1241, 50, 1026, 43], [1241, 66, 1027, 18], [1241, 70, 1027, 18, "_jsxDevRuntime"], [1241, 84, 1027, 18], [1241, 85, 1027, 18, "jsxDEV"], [1241, 91, 1027, 18], [1241, 93, 1027, 19, "_View"], [1241, 98, 1027, 19], [1241, 99, 1027, 19, "default"], [1241, 106, 1027, 23], [1242, 16, 1027, 24, "style"], [1242, 21, 1027, 29], [1242, 23, 1027, 31, "styles"], [1242, 29, 1027, 37], [1242, 30, 1027, 38, "shutterInner"], [1243, 14, 1027, 51], [1244, 16, 1027, 51, "fileName"], [1244, 24, 1027, 51], [1244, 26, 1027, 51, "_jsxFileName"], [1244, 38, 1027, 51], [1245, 16, 1027, 51, "lineNumber"], [1245, 26, 1027, 51], [1246, 16, 1027, 51, "columnNumber"], [1246, 28, 1027, 51], [1247, 14, 1027, 51], [1247, 21, 1027, 53], [1247, 22, 1027, 54], [1247, 38, 1029, 18], [1247, 42, 1029, 18, "_jsxDevRuntime"], [1247, 56, 1029, 18], [1247, 57, 1029, 18, "jsxDEV"], [1247, 63, 1029, 18], [1247, 65, 1029, 19, "_ActivityIndicator"], [1247, 83, 1029, 19], [1247, 84, 1029, 19, "default"], [1247, 91, 1029, 36], [1248, 16, 1029, 37, "size"], [1248, 20, 1029, 41], [1248, 22, 1029, 42], [1248, 29, 1029, 49], [1249, 16, 1029, 50, "color"], [1249, 21, 1029, 55], [1249, 23, 1029, 56], [1250, 14, 1029, 65], [1251, 16, 1029, 65, "fileName"], [1251, 24, 1029, 65], [1251, 26, 1029, 65, "_jsxFileName"], [1251, 38, 1029, 65], [1252, 16, 1029, 65, "lineNumber"], [1252, 26, 1029, 65], [1253, 16, 1029, 65, "columnNumber"], [1253, 28, 1029, 65], [1254, 14, 1029, 65], [1254, 21, 1029, 67], [1255, 12, 1030, 17], [1256, 14, 1030, 17, "fileName"], [1256, 22, 1030, 17], [1256, 24, 1030, 17, "_jsxFileName"], [1256, 36, 1030, 17], [1257, 14, 1030, 17, "lineNumber"], [1257, 24, 1030, 17], [1258, 14, 1030, 17, "columnNumber"], [1258, 26, 1030, 17], [1259, 12, 1030, 17], [1259, 19, 1031, 32], [1259, 20, 1031, 33], [1259, 35, 1032, 14], [1259, 39, 1032, 14, "_jsxDevRuntime"], [1259, 53, 1032, 14], [1259, 54, 1032, 14, "jsxDEV"], [1259, 60, 1032, 14], [1259, 62, 1032, 15, "_Text"], [1259, 67, 1032, 15], [1259, 68, 1032, 15, "default"], [1259, 75, 1032, 19], [1260, 14, 1032, 20, "style"], [1260, 19, 1032, 25], [1260, 21, 1032, 27, "styles"], [1260, 27, 1032, 33], [1260, 28, 1032, 34, "privacyNote"], [1260, 39, 1032, 46], [1261, 14, 1032, 46, "children"], [1261, 22, 1032, 46], [1261, 24, 1032, 47], [1262, 12, 1034, 14], [1263, 14, 1034, 14, "fileName"], [1263, 22, 1034, 14], [1263, 24, 1034, 14, "_jsxFileName"], [1263, 36, 1034, 14], [1264, 14, 1034, 14, "lineNumber"], [1264, 24, 1034, 14], [1265, 14, 1034, 14, "columnNumber"], [1265, 26, 1034, 14], [1266, 12, 1034, 14], [1266, 19, 1034, 20], [1266, 20, 1034, 21], [1267, 10, 1034, 21], [1268, 12, 1034, 21, "fileName"], [1268, 20, 1034, 21], [1268, 22, 1034, 21, "_jsxFileName"], [1268, 34, 1034, 21], [1269, 12, 1034, 21, "lineNumber"], [1269, 22, 1034, 21], [1270, 12, 1034, 21, "columnNumber"], [1270, 24, 1034, 21], [1271, 10, 1034, 21], [1271, 17, 1035, 18], [1271, 18, 1035, 19], [1272, 8, 1035, 19], [1272, 23, 1036, 12], [1272, 24, 1037, 9], [1273, 6, 1037, 9], [1274, 8, 1037, 9, "fileName"], [1274, 16, 1037, 9], [1274, 18, 1037, 9, "_jsxFileName"], [1274, 30, 1037, 9], [1275, 8, 1037, 9, "lineNumber"], [1275, 18, 1037, 9], [1276, 8, 1037, 9, "columnNumber"], [1276, 20, 1037, 9], [1277, 6, 1037, 9], [1277, 13, 1038, 12], [1277, 14, 1038, 13], [1277, 29, 1040, 6], [1277, 33, 1040, 6, "_jsxDevRuntime"], [1277, 47, 1040, 6], [1277, 48, 1040, 6, "jsxDEV"], [1277, 54, 1040, 6], [1277, 56, 1040, 7, "_Modal"], [1277, 62, 1040, 7], [1277, 63, 1040, 7, "default"], [1277, 70, 1040, 12], [1278, 8, 1041, 8, "visible"], [1278, 15, 1041, 15], [1278, 17, 1041, 17, "processingState"], [1278, 32, 1041, 32], [1278, 37, 1041, 37], [1278, 43, 1041, 43], [1278, 47, 1041, 47, "processingState"], [1278, 62, 1041, 62], [1278, 67, 1041, 67], [1278, 74, 1041, 75], [1279, 8, 1042, 8, "transparent"], [1279, 19, 1042, 19], [1280, 8, 1043, 8, "animationType"], [1280, 21, 1043, 21], [1280, 23, 1043, 22], [1280, 29, 1043, 28], [1281, 8, 1043, 28, "children"], [1281, 16, 1043, 28], [1281, 31, 1045, 8], [1281, 35, 1045, 8, "_jsxDevRuntime"], [1281, 49, 1045, 8], [1281, 50, 1045, 8, "jsxDEV"], [1281, 56, 1045, 8], [1281, 58, 1045, 9, "_View"], [1281, 63, 1045, 9], [1281, 64, 1045, 9, "default"], [1281, 71, 1045, 13], [1282, 10, 1045, 14, "style"], [1282, 15, 1045, 19], [1282, 17, 1045, 21, "styles"], [1282, 23, 1045, 27], [1282, 24, 1045, 28, "processingModal"], [1282, 39, 1045, 44], [1283, 10, 1045, 44, "children"], [1283, 18, 1045, 44], [1283, 33, 1046, 10], [1283, 37, 1046, 10, "_jsxDevRuntime"], [1283, 51, 1046, 10], [1283, 52, 1046, 10, "jsxDEV"], [1283, 58, 1046, 10], [1283, 60, 1046, 11, "_View"], [1283, 65, 1046, 11], [1283, 66, 1046, 11, "default"], [1283, 73, 1046, 15], [1284, 12, 1046, 16, "style"], [1284, 17, 1046, 21], [1284, 19, 1046, 23, "styles"], [1284, 25, 1046, 29], [1284, 26, 1046, 30, "processingContent"], [1284, 43, 1046, 48], [1285, 12, 1046, 48, "children"], [1285, 20, 1046, 48], [1285, 36, 1047, 12], [1285, 40, 1047, 12, "_jsxDevRuntime"], [1285, 54, 1047, 12], [1285, 55, 1047, 12, "jsxDEV"], [1285, 61, 1047, 12], [1285, 63, 1047, 13, "_ActivityIndicator"], [1285, 81, 1047, 13], [1285, 82, 1047, 13, "default"], [1285, 89, 1047, 30], [1286, 14, 1047, 31, "size"], [1286, 18, 1047, 35], [1286, 20, 1047, 36], [1286, 27, 1047, 43], [1287, 14, 1047, 44, "color"], [1287, 19, 1047, 49], [1287, 21, 1047, 50], [1288, 12, 1047, 59], [1289, 14, 1047, 59, "fileName"], [1289, 22, 1047, 59], [1289, 24, 1047, 59, "_jsxFileName"], [1289, 36, 1047, 59], [1290, 14, 1047, 59, "lineNumber"], [1290, 24, 1047, 59], [1291, 14, 1047, 59, "columnNumber"], [1291, 26, 1047, 59], [1292, 12, 1047, 59], [1292, 19, 1047, 61], [1292, 20, 1047, 62], [1292, 35, 1049, 12], [1292, 39, 1049, 12, "_jsxDevRuntime"], [1292, 53, 1049, 12], [1292, 54, 1049, 12, "jsxDEV"], [1292, 60, 1049, 12], [1292, 62, 1049, 13, "_Text"], [1292, 67, 1049, 13], [1292, 68, 1049, 13, "default"], [1292, 75, 1049, 17], [1293, 14, 1049, 18, "style"], [1293, 19, 1049, 23], [1293, 21, 1049, 25, "styles"], [1293, 27, 1049, 31], [1293, 28, 1049, 32, "processingTitle"], [1293, 43, 1049, 48], [1294, 14, 1049, 48, "children"], [1294, 22, 1049, 48], [1294, 25, 1050, 15, "processingState"], [1294, 40, 1050, 30], [1294, 45, 1050, 35], [1294, 56, 1050, 46], [1294, 60, 1050, 50], [1294, 80, 1050, 70], [1294, 82, 1051, 15, "processingState"], [1294, 97, 1051, 30], [1294, 102, 1051, 35], [1294, 113, 1051, 46], [1294, 117, 1051, 50], [1294, 146, 1051, 79], [1294, 148, 1052, 15, "processingState"], [1294, 163, 1052, 30], [1294, 168, 1052, 35], [1294, 180, 1052, 47], [1294, 184, 1052, 51], [1294, 216, 1052, 83], [1294, 218, 1053, 15, "processingState"], [1294, 233, 1053, 30], [1294, 238, 1053, 35], [1294, 249, 1053, 46], [1294, 253, 1053, 50], [1294, 275, 1053, 72], [1295, 12, 1053, 72], [1296, 14, 1053, 72, "fileName"], [1296, 22, 1053, 72], [1296, 24, 1053, 72, "_jsxFileName"], [1296, 36, 1053, 72], [1297, 14, 1053, 72, "lineNumber"], [1297, 24, 1053, 72], [1298, 14, 1053, 72, "columnNumber"], [1298, 26, 1053, 72], [1299, 12, 1053, 72], [1299, 19, 1054, 18], [1299, 20, 1054, 19], [1299, 35, 1055, 12], [1299, 39, 1055, 12, "_jsxDevRuntime"], [1299, 53, 1055, 12], [1299, 54, 1055, 12, "jsxDEV"], [1299, 60, 1055, 12], [1299, 62, 1055, 13, "_View"], [1299, 67, 1055, 13], [1299, 68, 1055, 13, "default"], [1299, 75, 1055, 17], [1300, 14, 1055, 18, "style"], [1300, 19, 1055, 23], [1300, 21, 1055, 25, "styles"], [1300, 27, 1055, 31], [1300, 28, 1055, 32, "progressBar"], [1300, 39, 1055, 44], [1301, 14, 1055, 44, "children"], [1301, 22, 1055, 44], [1301, 37, 1056, 14], [1301, 41, 1056, 14, "_jsxDevRuntime"], [1301, 55, 1056, 14], [1301, 56, 1056, 14, "jsxDEV"], [1301, 62, 1056, 14], [1301, 64, 1056, 15, "_View"], [1301, 69, 1056, 15], [1301, 70, 1056, 15, "default"], [1301, 77, 1056, 19], [1302, 16, 1057, 16, "style"], [1302, 21, 1057, 21], [1302, 23, 1057, 23], [1302, 24, 1058, 18, "styles"], [1302, 30, 1058, 24], [1302, 31, 1058, 25, "progressFill"], [1302, 43, 1058, 37], [1302, 45, 1059, 18], [1303, 18, 1059, 20, "width"], [1303, 23, 1059, 25], [1303, 25, 1059, 27], [1303, 28, 1059, 30, "processingProgress"], [1303, 46, 1059, 48], [1304, 16, 1059, 52], [1304, 17, 1059, 53], [1305, 14, 1060, 18], [1306, 16, 1060, 18, "fileName"], [1306, 24, 1060, 18], [1306, 26, 1060, 18, "_jsxFileName"], [1306, 38, 1060, 18], [1307, 16, 1060, 18, "lineNumber"], [1307, 26, 1060, 18], [1308, 16, 1060, 18, "columnNumber"], [1308, 28, 1060, 18], [1309, 14, 1060, 18], [1309, 21, 1061, 15], [1310, 12, 1061, 16], [1311, 14, 1061, 16, "fileName"], [1311, 22, 1061, 16], [1311, 24, 1061, 16, "_jsxFileName"], [1311, 36, 1061, 16], [1312, 14, 1061, 16, "lineNumber"], [1312, 24, 1061, 16], [1313, 14, 1061, 16, "columnNumber"], [1313, 26, 1061, 16], [1314, 12, 1061, 16], [1314, 19, 1062, 18], [1314, 20, 1062, 19], [1314, 35, 1063, 12], [1314, 39, 1063, 12, "_jsxDevRuntime"], [1314, 53, 1063, 12], [1314, 54, 1063, 12, "jsxDEV"], [1314, 60, 1063, 12], [1314, 62, 1063, 13, "_Text"], [1314, 67, 1063, 13], [1314, 68, 1063, 13, "default"], [1314, 75, 1063, 17], [1315, 14, 1063, 18, "style"], [1315, 19, 1063, 23], [1315, 21, 1063, 25, "styles"], [1315, 27, 1063, 31], [1315, 28, 1063, 32, "processingDescription"], [1315, 49, 1063, 54], [1316, 14, 1063, 54, "children"], [1316, 22, 1063, 54], [1316, 25, 1064, 15, "processingState"], [1316, 40, 1064, 30], [1316, 45, 1064, 35], [1316, 56, 1064, 46], [1316, 60, 1064, 50], [1316, 89, 1064, 79], [1316, 91, 1065, 15, "processingState"], [1316, 106, 1065, 30], [1316, 111, 1065, 35], [1316, 122, 1065, 46], [1316, 126, 1065, 50], [1316, 164, 1065, 88], [1316, 166, 1066, 15, "processingState"], [1316, 181, 1066, 30], [1316, 186, 1066, 35], [1316, 198, 1066, 47], [1316, 202, 1066, 51], [1316, 247, 1066, 96], [1316, 249, 1067, 15, "processingState"], [1316, 264, 1067, 30], [1316, 269, 1067, 35], [1316, 280, 1067, 46], [1316, 284, 1067, 50], [1316, 325, 1067, 91], [1317, 12, 1067, 91], [1318, 14, 1067, 91, "fileName"], [1318, 22, 1067, 91], [1318, 24, 1067, 91, "_jsxFileName"], [1318, 36, 1067, 91], [1319, 14, 1067, 91, "lineNumber"], [1319, 24, 1067, 91], [1320, 14, 1067, 91, "columnNumber"], [1320, 26, 1067, 91], [1321, 12, 1067, 91], [1321, 19, 1068, 18], [1321, 20, 1068, 19], [1321, 22, 1069, 13, "processingState"], [1321, 37, 1069, 28], [1321, 42, 1069, 33], [1321, 53, 1069, 44], [1321, 70, 1070, 14], [1321, 74, 1070, 14, "_jsxDevRuntime"], [1321, 88, 1070, 14], [1321, 89, 1070, 14, "jsxDEV"], [1321, 95, 1070, 14], [1321, 97, 1070, 15, "_lucideReactNative"], [1321, 115, 1070, 15], [1321, 116, 1070, 15, "CheckCircle"], [1321, 127, 1070, 26], [1322, 14, 1070, 27, "size"], [1322, 18, 1070, 31], [1322, 20, 1070, 33], [1322, 22, 1070, 36], [1323, 14, 1070, 37, "color"], [1323, 19, 1070, 42], [1323, 21, 1070, 43], [1323, 30, 1070, 52], [1324, 14, 1070, 53, "style"], [1324, 19, 1070, 58], [1324, 21, 1070, 60, "styles"], [1324, 27, 1070, 66], [1324, 28, 1070, 67, "successIcon"], [1325, 12, 1070, 79], [1326, 14, 1070, 79, "fileName"], [1326, 22, 1070, 79], [1326, 24, 1070, 79, "_jsxFileName"], [1326, 36, 1070, 79], [1327, 14, 1070, 79, "lineNumber"], [1327, 24, 1070, 79], [1328, 14, 1070, 79, "columnNumber"], [1328, 26, 1070, 79], [1329, 12, 1070, 79], [1329, 19, 1070, 81], [1329, 20, 1071, 13], [1330, 10, 1071, 13], [1331, 12, 1071, 13, "fileName"], [1331, 20, 1071, 13], [1331, 22, 1071, 13, "_jsxFileName"], [1331, 34, 1071, 13], [1332, 12, 1071, 13, "lineNumber"], [1332, 22, 1071, 13], [1333, 12, 1071, 13, "columnNumber"], [1333, 24, 1071, 13], [1334, 10, 1071, 13], [1334, 17, 1072, 16], [1335, 8, 1072, 17], [1336, 10, 1072, 17, "fileName"], [1336, 18, 1072, 17], [1336, 20, 1072, 17, "_jsxFileName"], [1336, 32, 1072, 17], [1337, 10, 1072, 17, "lineNumber"], [1337, 20, 1072, 17], [1338, 10, 1072, 17, "columnNumber"], [1338, 22, 1072, 17], [1339, 8, 1072, 17], [1339, 15, 1073, 14], [1340, 6, 1073, 15], [1341, 8, 1073, 15, "fileName"], [1341, 16, 1073, 15], [1341, 18, 1073, 15, "_jsxFileName"], [1341, 30, 1073, 15], [1342, 8, 1073, 15, "lineNumber"], [1342, 18, 1073, 15], [1343, 8, 1073, 15, "columnNumber"], [1343, 20, 1073, 15], [1344, 6, 1073, 15], [1344, 13, 1074, 13], [1344, 14, 1074, 14], [1344, 29, 1076, 6], [1344, 33, 1076, 6, "_jsxDevRuntime"], [1344, 47, 1076, 6], [1344, 48, 1076, 6, "jsxDEV"], [1344, 54, 1076, 6], [1344, 56, 1076, 7, "_Modal"], [1344, 62, 1076, 7], [1344, 63, 1076, 7, "default"], [1344, 70, 1076, 12], [1345, 8, 1077, 8, "visible"], [1345, 15, 1077, 15], [1345, 17, 1077, 17, "processingState"], [1345, 32, 1077, 32], [1345, 37, 1077, 37], [1345, 44, 1077, 45], [1346, 8, 1078, 8, "transparent"], [1346, 19, 1078, 19], [1347, 8, 1079, 8, "animationType"], [1347, 21, 1079, 21], [1347, 23, 1079, 22], [1347, 29, 1079, 28], [1348, 8, 1079, 28, "children"], [1348, 16, 1079, 28], [1348, 31, 1081, 8], [1348, 35, 1081, 8, "_jsxDevRuntime"], [1348, 49, 1081, 8], [1348, 50, 1081, 8, "jsxDEV"], [1348, 56, 1081, 8], [1348, 58, 1081, 9, "_View"], [1348, 63, 1081, 9], [1348, 64, 1081, 9, "default"], [1348, 71, 1081, 13], [1349, 10, 1081, 14, "style"], [1349, 15, 1081, 19], [1349, 17, 1081, 21, "styles"], [1349, 23, 1081, 27], [1349, 24, 1081, 28, "processingModal"], [1349, 39, 1081, 44], [1350, 10, 1081, 44, "children"], [1350, 18, 1081, 44], [1350, 33, 1082, 10], [1350, 37, 1082, 10, "_jsxDevRuntime"], [1350, 51, 1082, 10], [1350, 52, 1082, 10, "jsxDEV"], [1350, 58, 1082, 10], [1350, 60, 1082, 11, "_View"], [1350, 65, 1082, 11], [1350, 66, 1082, 11, "default"], [1350, 73, 1082, 15], [1351, 12, 1082, 16, "style"], [1351, 17, 1082, 21], [1351, 19, 1082, 23, "styles"], [1351, 25, 1082, 29], [1351, 26, 1082, 30, "errorContent"], [1351, 38, 1082, 43], [1352, 12, 1082, 43, "children"], [1352, 20, 1082, 43], [1352, 36, 1083, 12], [1352, 40, 1083, 12, "_jsxDevRuntime"], [1352, 54, 1083, 12], [1352, 55, 1083, 12, "jsxDEV"], [1352, 61, 1083, 12], [1352, 63, 1083, 13, "_lucideReactNative"], [1352, 81, 1083, 13], [1352, 82, 1083, 13, "X"], [1352, 83, 1083, 14], [1353, 14, 1083, 15, "size"], [1353, 18, 1083, 19], [1353, 20, 1083, 21], [1353, 22, 1083, 24], [1354, 14, 1083, 25, "color"], [1354, 19, 1083, 30], [1354, 21, 1083, 31], [1355, 12, 1083, 40], [1356, 14, 1083, 40, "fileName"], [1356, 22, 1083, 40], [1356, 24, 1083, 40, "_jsxFileName"], [1356, 36, 1083, 40], [1357, 14, 1083, 40, "lineNumber"], [1357, 24, 1083, 40], [1358, 14, 1083, 40, "columnNumber"], [1358, 26, 1083, 40], [1359, 12, 1083, 40], [1359, 19, 1083, 42], [1359, 20, 1083, 43], [1359, 35, 1084, 12], [1359, 39, 1084, 12, "_jsxDevRuntime"], [1359, 53, 1084, 12], [1359, 54, 1084, 12, "jsxDEV"], [1359, 60, 1084, 12], [1359, 62, 1084, 13, "_Text"], [1359, 67, 1084, 13], [1359, 68, 1084, 13, "default"], [1359, 75, 1084, 17], [1360, 14, 1084, 18, "style"], [1360, 19, 1084, 23], [1360, 21, 1084, 25, "styles"], [1360, 27, 1084, 31], [1360, 28, 1084, 32, "errorTitle"], [1360, 38, 1084, 43], [1361, 14, 1084, 43, "children"], [1361, 22, 1084, 43], [1361, 24, 1084, 44], [1362, 12, 1084, 61], [1363, 14, 1084, 61, "fileName"], [1363, 22, 1084, 61], [1363, 24, 1084, 61, "_jsxFileName"], [1363, 36, 1084, 61], [1364, 14, 1084, 61, "lineNumber"], [1364, 24, 1084, 61], [1365, 14, 1084, 61, "columnNumber"], [1365, 26, 1084, 61], [1366, 12, 1084, 61], [1366, 19, 1084, 67], [1366, 20, 1084, 68], [1366, 35, 1085, 12], [1366, 39, 1085, 12, "_jsxDevRuntime"], [1366, 53, 1085, 12], [1366, 54, 1085, 12, "jsxDEV"], [1366, 60, 1085, 12], [1366, 62, 1085, 13, "_Text"], [1366, 67, 1085, 13], [1366, 68, 1085, 13, "default"], [1366, 75, 1085, 17], [1367, 14, 1085, 18, "style"], [1367, 19, 1085, 23], [1367, 21, 1085, 25, "styles"], [1367, 27, 1085, 31], [1367, 28, 1085, 32, "errorMessage"], [1367, 40, 1085, 45], [1368, 14, 1085, 45, "children"], [1368, 22, 1085, 45], [1368, 24, 1085, 47, "errorMessage"], [1369, 12, 1085, 59], [1370, 14, 1085, 59, "fileName"], [1370, 22, 1085, 59], [1370, 24, 1085, 59, "_jsxFileName"], [1370, 36, 1085, 59], [1371, 14, 1085, 59, "lineNumber"], [1371, 24, 1085, 59], [1372, 14, 1085, 59, "columnNumber"], [1372, 26, 1085, 59], [1373, 12, 1085, 59], [1373, 19, 1085, 66], [1373, 20, 1085, 67], [1373, 35, 1086, 12], [1373, 39, 1086, 12, "_jsxDevRuntime"], [1373, 53, 1086, 12], [1373, 54, 1086, 12, "jsxDEV"], [1373, 60, 1086, 12], [1373, 62, 1086, 13, "_TouchableOpacity"], [1373, 79, 1086, 13], [1373, 80, 1086, 13, "default"], [1373, 87, 1086, 29], [1374, 14, 1087, 14, "onPress"], [1374, 21, 1087, 21], [1374, 23, 1087, 23, "retryCapture"], [1374, 35, 1087, 36], [1375, 14, 1088, 14, "style"], [1375, 19, 1088, 19], [1375, 21, 1088, 21, "styles"], [1375, 27, 1088, 27], [1375, 28, 1088, 28, "primaryButton"], [1375, 41, 1088, 42], [1376, 14, 1088, 42, "children"], [1376, 22, 1088, 42], [1376, 37, 1090, 14], [1376, 41, 1090, 14, "_jsxDevRuntime"], [1376, 55, 1090, 14], [1376, 56, 1090, 14, "jsxDEV"], [1376, 62, 1090, 14], [1376, 64, 1090, 15, "_Text"], [1376, 69, 1090, 15], [1376, 70, 1090, 15, "default"], [1376, 77, 1090, 19], [1377, 16, 1090, 20, "style"], [1377, 21, 1090, 25], [1377, 23, 1090, 27, "styles"], [1377, 29, 1090, 33], [1377, 30, 1090, 34, "primaryButtonText"], [1377, 47, 1090, 52], [1378, 16, 1090, 52, "children"], [1378, 24, 1090, 52], [1378, 26, 1090, 53], [1379, 14, 1090, 62], [1380, 16, 1090, 62, "fileName"], [1380, 24, 1090, 62], [1380, 26, 1090, 62, "_jsxFileName"], [1380, 38, 1090, 62], [1381, 16, 1090, 62, "lineNumber"], [1381, 26, 1090, 62], [1382, 16, 1090, 62, "columnNumber"], [1382, 28, 1090, 62], [1383, 14, 1090, 62], [1383, 21, 1090, 68], [1384, 12, 1090, 69], [1385, 14, 1090, 69, "fileName"], [1385, 22, 1090, 69], [1385, 24, 1090, 69, "_jsxFileName"], [1385, 36, 1090, 69], [1386, 14, 1090, 69, "lineNumber"], [1386, 24, 1090, 69], [1387, 14, 1090, 69, "columnNumber"], [1387, 26, 1090, 69], [1388, 12, 1090, 69], [1388, 19, 1091, 30], [1388, 20, 1091, 31], [1388, 35, 1092, 12], [1388, 39, 1092, 12, "_jsxDevRuntime"], [1388, 53, 1092, 12], [1388, 54, 1092, 12, "jsxDEV"], [1388, 60, 1092, 12], [1388, 62, 1092, 13, "_TouchableOpacity"], [1388, 79, 1092, 13], [1388, 80, 1092, 13, "default"], [1388, 87, 1092, 29], [1389, 14, 1093, 14, "onPress"], [1389, 21, 1093, 21], [1389, 23, 1093, 23, "onCancel"], [1389, 31, 1093, 32], [1390, 14, 1094, 14, "style"], [1390, 19, 1094, 19], [1390, 21, 1094, 21, "styles"], [1390, 27, 1094, 27], [1390, 28, 1094, 28, "secondaryButton"], [1390, 43, 1094, 44], [1391, 14, 1094, 44, "children"], [1391, 22, 1094, 44], [1391, 37, 1096, 14], [1391, 41, 1096, 14, "_jsxDevRuntime"], [1391, 55, 1096, 14], [1391, 56, 1096, 14, "jsxDEV"], [1391, 62, 1096, 14], [1391, 64, 1096, 15, "_Text"], [1391, 69, 1096, 15], [1391, 70, 1096, 15, "default"], [1391, 77, 1096, 19], [1392, 16, 1096, 20, "style"], [1392, 21, 1096, 25], [1392, 23, 1096, 27, "styles"], [1392, 29, 1096, 33], [1392, 30, 1096, 34, "secondaryButtonText"], [1392, 49, 1096, 54], [1393, 16, 1096, 54, "children"], [1393, 24, 1096, 54], [1393, 26, 1096, 55], [1394, 14, 1096, 61], [1395, 16, 1096, 61, "fileName"], [1395, 24, 1096, 61], [1395, 26, 1096, 61, "_jsxFileName"], [1395, 38, 1096, 61], [1396, 16, 1096, 61, "lineNumber"], [1396, 26, 1096, 61], [1397, 16, 1096, 61, "columnNumber"], [1397, 28, 1096, 61], [1398, 14, 1096, 61], [1398, 21, 1096, 67], [1399, 12, 1096, 68], [1400, 14, 1096, 68, "fileName"], [1400, 22, 1096, 68], [1400, 24, 1096, 68, "_jsxFileName"], [1400, 36, 1096, 68], [1401, 14, 1096, 68, "lineNumber"], [1401, 24, 1096, 68], [1402, 14, 1096, 68, "columnNumber"], [1402, 26, 1096, 68], [1403, 12, 1096, 68], [1403, 19, 1097, 30], [1403, 20, 1097, 31], [1404, 10, 1097, 31], [1405, 12, 1097, 31, "fileName"], [1405, 20, 1097, 31], [1405, 22, 1097, 31, "_jsxFileName"], [1405, 34, 1097, 31], [1406, 12, 1097, 31, "lineNumber"], [1406, 22, 1097, 31], [1407, 12, 1097, 31, "columnNumber"], [1407, 24, 1097, 31], [1408, 10, 1097, 31], [1408, 17, 1098, 16], [1409, 8, 1098, 17], [1410, 10, 1098, 17, "fileName"], [1410, 18, 1098, 17], [1410, 20, 1098, 17, "_jsxFileName"], [1410, 32, 1098, 17], [1411, 10, 1098, 17, "lineNumber"], [1411, 20, 1098, 17], [1412, 10, 1098, 17, "columnNumber"], [1412, 22, 1098, 17], [1413, 8, 1098, 17], [1413, 15, 1099, 14], [1414, 6, 1099, 15], [1415, 8, 1099, 15, "fileName"], [1415, 16, 1099, 15], [1415, 18, 1099, 15, "_jsxFileName"], [1415, 30, 1099, 15], [1416, 8, 1099, 15, "lineNumber"], [1416, 18, 1099, 15], [1417, 8, 1099, 15, "columnNumber"], [1417, 20, 1099, 15], [1418, 6, 1099, 15], [1418, 13, 1100, 13], [1418, 14, 1100, 14], [1419, 4, 1100, 14], [1420, 6, 1100, 14, "fileName"], [1420, 14, 1100, 14], [1420, 16, 1100, 14, "_jsxFileName"], [1420, 28, 1100, 14], [1421, 6, 1100, 14, "lineNumber"], [1421, 16, 1100, 14], [1422, 6, 1100, 14, "columnNumber"], [1422, 18, 1100, 14], [1423, 4, 1100, 14], [1423, 11, 1101, 10], [1423, 12, 1101, 11], [1424, 2, 1103, 0], [1425, 2, 1103, 1, "_s"], [1425, 4, 1103, 1], [1425, 5, 51, 24, "EchoCameraWeb"], [1425, 18, 51, 37], [1426, 4, 51, 37], [1426, 12, 58, 42, "useCameraPermissions"], [1426, 44, 58, 62], [1426, 46, 72, 19, "useUpload"], [1426, 64, 72, 28], [1427, 2, 72, 28], [1428, 2, 72, 28, "_c"], [1428, 4, 72, 28], [1428, 7, 51, 24, "EchoCameraWeb"], [1428, 20, 51, 37], [1429, 2, 1104, 0], [1429, 8, 1104, 6, "styles"], [1429, 14, 1104, 12], [1429, 17, 1104, 15, "StyleSheet"], [1429, 36, 1104, 25], [1429, 37, 1104, 26, "create"], [1429, 43, 1104, 32], [1429, 44, 1104, 33], [1430, 4, 1105, 2, "container"], [1430, 13, 1105, 11], [1430, 15, 1105, 13], [1431, 6, 1106, 4, "flex"], [1431, 10, 1106, 8], [1431, 12, 1106, 10], [1431, 13, 1106, 11], [1432, 6, 1107, 4, "backgroundColor"], [1432, 21, 1107, 19], [1432, 23, 1107, 21], [1433, 4, 1108, 2], [1433, 5, 1108, 3], [1434, 4, 1109, 2, "cameraContainer"], [1434, 19, 1109, 17], [1434, 21, 1109, 19], [1435, 6, 1110, 4, "flex"], [1435, 10, 1110, 8], [1435, 12, 1110, 10], [1435, 13, 1110, 11], [1436, 6, 1111, 4, "max<PERSON><PERSON><PERSON>"], [1436, 14, 1111, 12], [1436, 16, 1111, 14], [1436, 19, 1111, 17], [1437, 6, 1112, 4, "alignSelf"], [1437, 15, 1112, 13], [1437, 17, 1112, 15], [1437, 25, 1112, 23], [1438, 6, 1113, 4, "width"], [1438, 11, 1113, 9], [1438, 13, 1113, 11], [1439, 4, 1114, 2], [1439, 5, 1114, 3], [1440, 4, 1115, 2, "camera"], [1440, 10, 1115, 8], [1440, 12, 1115, 10], [1441, 6, 1116, 4, "flex"], [1441, 10, 1116, 8], [1441, 12, 1116, 10], [1442, 4, 1117, 2], [1442, 5, 1117, 3], [1443, 4, 1118, 2, "headerOverlay"], [1443, 17, 1118, 15], [1443, 19, 1118, 17], [1444, 6, 1119, 4, "position"], [1444, 14, 1119, 12], [1444, 16, 1119, 14], [1444, 26, 1119, 24], [1445, 6, 1120, 4, "top"], [1445, 9, 1120, 7], [1445, 11, 1120, 9], [1445, 12, 1120, 10], [1446, 6, 1121, 4, "left"], [1446, 10, 1121, 8], [1446, 12, 1121, 10], [1446, 13, 1121, 11], [1447, 6, 1122, 4, "right"], [1447, 11, 1122, 9], [1447, 13, 1122, 11], [1447, 14, 1122, 12], [1448, 6, 1123, 4, "backgroundColor"], [1448, 21, 1123, 19], [1448, 23, 1123, 21], [1448, 36, 1123, 34], [1449, 6, 1124, 4, "paddingTop"], [1449, 16, 1124, 14], [1449, 18, 1124, 16], [1449, 20, 1124, 18], [1450, 6, 1125, 4, "paddingHorizontal"], [1450, 23, 1125, 21], [1450, 25, 1125, 23], [1450, 27, 1125, 25], [1451, 6, 1126, 4, "paddingBottom"], [1451, 19, 1126, 17], [1451, 21, 1126, 19], [1452, 4, 1127, 2], [1452, 5, 1127, 3], [1453, 4, 1128, 2, "headerContent"], [1453, 17, 1128, 15], [1453, 19, 1128, 17], [1454, 6, 1129, 4, "flexDirection"], [1454, 19, 1129, 17], [1454, 21, 1129, 19], [1454, 26, 1129, 24], [1455, 6, 1130, 4, "justifyContent"], [1455, 20, 1130, 18], [1455, 22, 1130, 20], [1455, 37, 1130, 35], [1456, 6, 1131, 4, "alignItems"], [1456, 16, 1131, 14], [1456, 18, 1131, 16], [1457, 4, 1132, 2], [1457, 5, 1132, 3], [1458, 4, 1133, 2, "headerLeft"], [1458, 14, 1133, 12], [1458, 16, 1133, 14], [1459, 6, 1134, 4, "flex"], [1459, 10, 1134, 8], [1459, 12, 1134, 10], [1460, 4, 1135, 2], [1460, 5, 1135, 3], [1461, 4, 1136, 2, "headerTitle"], [1461, 15, 1136, 13], [1461, 17, 1136, 15], [1462, 6, 1137, 4, "fontSize"], [1462, 14, 1137, 12], [1462, 16, 1137, 14], [1462, 18, 1137, 16], [1463, 6, 1138, 4, "fontWeight"], [1463, 16, 1138, 14], [1463, 18, 1138, 16], [1463, 23, 1138, 21], [1464, 6, 1139, 4, "color"], [1464, 11, 1139, 9], [1464, 13, 1139, 11], [1464, 19, 1139, 17], [1465, 6, 1140, 4, "marginBottom"], [1465, 18, 1140, 16], [1465, 20, 1140, 18], [1466, 4, 1141, 2], [1466, 5, 1141, 3], [1467, 4, 1142, 2, "subtitleRow"], [1467, 15, 1142, 13], [1467, 17, 1142, 15], [1468, 6, 1143, 4, "flexDirection"], [1468, 19, 1143, 17], [1468, 21, 1143, 19], [1468, 26, 1143, 24], [1469, 6, 1144, 4, "alignItems"], [1469, 16, 1144, 14], [1469, 18, 1144, 16], [1469, 26, 1144, 24], [1470, 6, 1145, 4, "marginBottom"], [1470, 18, 1145, 16], [1470, 20, 1145, 18], [1471, 4, 1146, 2], [1471, 5, 1146, 3], [1472, 4, 1147, 2, "webIcon"], [1472, 11, 1147, 9], [1472, 13, 1147, 11], [1473, 6, 1148, 4, "fontSize"], [1473, 14, 1148, 12], [1473, 16, 1148, 14], [1473, 18, 1148, 16], [1474, 6, 1149, 4, "marginRight"], [1474, 17, 1149, 15], [1474, 19, 1149, 17], [1475, 4, 1150, 2], [1475, 5, 1150, 3], [1476, 4, 1151, 2, "headerSubtitle"], [1476, 18, 1151, 16], [1476, 20, 1151, 18], [1477, 6, 1152, 4, "fontSize"], [1477, 14, 1152, 12], [1477, 16, 1152, 14], [1477, 18, 1152, 16], [1478, 6, 1153, 4, "color"], [1478, 11, 1153, 9], [1478, 13, 1153, 11], [1478, 19, 1153, 17], [1479, 6, 1154, 4, "opacity"], [1479, 13, 1154, 11], [1479, 15, 1154, 13], [1480, 4, 1155, 2], [1480, 5, 1155, 3], [1481, 4, 1156, 2, "challengeRow"], [1481, 16, 1156, 14], [1481, 18, 1156, 16], [1482, 6, 1157, 4, "flexDirection"], [1482, 19, 1157, 17], [1482, 21, 1157, 19], [1482, 26, 1157, 24], [1483, 6, 1158, 4, "alignItems"], [1483, 16, 1158, 14], [1483, 18, 1158, 16], [1484, 4, 1159, 2], [1484, 5, 1159, 3], [1485, 4, 1160, 2, "challengeCode"], [1485, 17, 1160, 15], [1485, 19, 1160, 17], [1486, 6, 1161, 4, "fontSize"], [1486, 14, 1161, 12], [1486, 16, 1161, 14], [1486, 18, 1161, 16], [1487, 6, 1162, 4, "color"], [1487, 11, 1162, 9], [1487, 13, 1162, 11], [1487, 19, 1162, 17], [1488, 6, 1163, 4, "marginLeft"], [1488, 16, 1163, 14], [1488, 18, 1163, 16], [1488, 19, 1163, 17], [1489, 6, 1164, 4, "fontFamily"], [1489, 16, 1164, 14], [1489, 18, 1164, 16], [1490, 4, 1165, 2], [1490, 5, 1165, 3], [1491, 4, 1166, 2, "closeButton"], [1491, 15, 1166, 13], [1491, 17, 1166, 15], [1492, 6, 1167, 4, "padding"], [1492, 13, 1167, 11], [1492, 15, 1167, 13], [1493, 4, 1168, 2], [1493, 5, 1168, 3], [1494, 4, 1169, 2, "privacyNotice"], [1494, 17, 1169, 15], [1494, 19, 1169, 17], [1495, 6, 1170, 4, "position"], [1495, 14, 1170, 12], [1495, 16, 1170, 14], [1495, 26, 1170, 24], [1496, 6, 1171, 4, "top"], [1496, 9, 1171, 7], [1496, 11, 1171, 9], [1496, 14, 1171, 12], [1497, 6, 1172, 4, "left"], [1497, 10, 1172, 8], [1497, 12, 1172, 10], [1497, 14, 1172, 12], [1498, 6, 1173, 4, "right"], [1498, 11, 1173, 9], [1498, 13, 1173, 11], [1498, 15, 1173, 13], [1499, 6, 1174, 4, "backgroundColor"], [1499, 21, 1174, 19], [1499, 23, 1174, 21], [1499, 48, 1174, 46], [1500, 6, 1175, 4, "borderRadius"], [1500, 18, 1175, 16], [1500, 20, 1175, 18], [1500, 21, 1175, 19], [1501, 6, 1176, 4, "padding"], [1501, 13, 1176, 11], [1501, 15, 1176, 13], [1501, 17, 1176, 15], [1502, 6, 1177, 4, "flexDirection"], [1502, 19, 1177, 17], [1502, 21, 1177, 19], [1502, 26, 1177, 24], [1503, 6, 1178, 4, "alignItems"], [1503, 16, 1178, 14], [1503, 18, 1178, 16], [1504, 4, 1179, 2], [1504, 5, 1179, 3], [1505, 4, 1180, 2, "privacyText"], [1505, 15, 1180, 13], [1505, 17, 1180, 15], [1506, 6, 1181, 4, "color"], [1506, 11, 1181, 9], [1506, 13, 1181, 11], [1506, 19, 1181, 17], [1507, 6, 1182, 4, "fontSize"], [1507, 14, 1182, 12], [1507, 16, 1182, 14], [1507, 18, 1182, 16], [1508, 6, 1183, 4, "marginLeft"], [1508, 16, 1183, 14], [1508, 18, 1183, 16], [1508, 19, 1183, 17], [1509, 6, 1184, 4, "flex"], [1509, 10, 1184, 8], [1509, 12, 1184, 10], [1510, 4, 1185, 2], [1510, 5, 1185, 3], [1511, 4, 1186, 2, "footer<PERSON><PERSON><PERSON>"], [1511, 17, 1186, 15], [1511, 19, 1186, 17], [1512, 6, 1187, 4, "position"], [1512, 14, 1187, 12], [1512, 16, 1187, 14], [1512, 26, 1187, 24], [1513, 6, 1188, 4, "bottom"], [1513, 12, 1188, 10], [1513, 14, 1188, 12], [1513, 15, 1188, 13], [1514, 6, 1189, 4, "left"], [1514, 10, 1189, 8], [1514, 12, 1189, 10], [1514, 13, 1189, 11], [1515, 6, 1190, 4, "right"], [1515, 11, 1190, 9], [1515, 13, 1190, 11], [1515, 14, 1190, 12], [1516, 6, 1191, 4, "backgroundColor"], [1516, 21, 1191, 19], [1516, 23, 1191, 21], [1516, 36, 1191, 34], [1517, 6, 1192, 4, "paddingBottom"], [1517, 19, 1192, 17], [1517, 21, 1192, 19], [1517, 23, 1192, 21], [1518, 6, 1193, 4, "paddingTop"], [1518, 16, 1193, 14], [1518, 18, 1193, 16], [1518, 20, 1193, 18], [1519, 6, 1194, 4, "alignItems"], [1519, 16, 1194, 14], [1519, 18, 1194, 16], [1520, 4, 1195, 2], [1520, 5, 1195, 3], [1521, 4, 1196, 2, "instruction"], [1521, 15, 1196, 13], [1521, 17, 1196, 15], [1522, 6, 1197, 4, "fontSize"], [1522, 14, 1197, 12], [1522, 16, 1197, 14], [1522, 18, 1197, 16], [1523, 6, 1198, 4, "color"], [1523, 11, 1198, 9], [1523, 13, 1198, 11], [1523, 19, 1198, 17], [1524, 6, 1199, 4, "marginBottom"], [1524, 18, 1199, 16], [1524, 20, 1199, 18], [1525, 4, 1200, 2], [1525, 5, 1200, 3], [1526, 4, 1201, 2, "shutterButton"], [1526, 17, 1201, 15], [1526, 19, 1201, 17], [1527, 6, 1202, 4, "width"], [1527, 11, 1202, 9], [1527, 13, 1202, 11], [1527, 15, 1202, 13], [1528, 6, 1203, 4, "height"], [1528, 12, 1203, 10], [1528, 14, 1203, 12], [1528, 16, 1203, 14], [1529, 6, 1204, 4, "borderRadius"], [1529, 18, 1204, 16], [1529, 20, 1204, 18], [1529, 22, 1204, 20], [1530, 6, 1205, 4, "backgroundColor"], [1530, 21, 1205, 19], [1530, 23, 1205, 21], [1530, 29, 1205, 27], [1531, 6, 1206, 4, "justifyContent"], [1531, 20, 1206, 18], [1531, 22, 1206, 20], [1531, 30, 1206, 28], [1532, 6, 1207, 4, "alignItems"], [1532, 16, 1207, 14], [1532, 18, 1207, 16], [1532, 26, 1207, 24], [1533, 6, 1208, 4, "marginBottom"], [1533, 18, 1208, 16], [1533, 20, 1208, 18], [1533, 22, 1208, 20], [1534, 6, 1209, 4], [1534, 9, 1209, 7, "Platform"], [1534, 26, 1209, 15], [1534, 27, 1209, 16, "select"], [1534, 33, 1209, 22], [1534, 34, 1209, 23], [1535, 8, 1210, 6, "ios"], [1535, 11, 1210, 9], [1535, 13, 1210, 11], [1536, 10, 1211, 8, "shadowColor"], [1536, 21, 1211, 19], [1536, 23, 1211, 21], [1536, 32, 1211, 30], [1537, 10, 1212, 8, "shadowOffset"], [1537, 22, 1212, 20], [1537, 24, 1212, 22], [1538, 12, 1212, 24, "width"], [1538, 17, 1212, 29], [1538, 19, 1212, 31], [1538, 20, 1212, 32], [1539, 12, 1212, 34, "height"], [1539, 18, 1212, 40], [1539, 20, 1212, 42], [1540, 10, 1212, 44], [1540, 11, 1212, 45], [1541, 10, 1213, 8, "shadowOpacity"], [1541, 23, 1213, 21], [1541, 25, 1213, 23], [1541, 28, 1213, 26], [1542, 10, 1214, 8, "shadowRadius"], [1542, 22, 1214, 20], [1542, 24, 1214, 22], [1543, 8, 1215, 6], [1543, 9, 1215, 7], [1544, 8, 1216, 6, "android"], [1544, 15, 1216, 13], [1544, 17, 1216, 15], [1545, 10, 1217, 8, "elevation"], [1545, 19, 1217, 17], [1545, 21, 1217, 19], [1546, 8, 1218, 6], [1546, 9, 1218, 7], [1547, 8, 1219, 6, "web"], [1547, 11, 1219, 9], [1547, 13, 1219, 11], [1548, 10, 1220, 8, "boxShadow"], [1548, 19, 1220, 17], [1548, 21, 1220, 19], [1549, 8, 1221, 6], [1550, 6, 1222, 4], [1550, 7, 1222, 5], [1551, 4, 1223, 2], [1551, 5, 1223, 3], [1552, 4, 1224, 2, "shutterButtonDisabled"], [1552, 25, 1224, 23], [1552, 27, 1224, 25], [1553, 6, 1225, 4, "opacity"], [1553, 13, 1225, 11], [1553, 15, 1225, 13], [1554, 4, 1226, 2], [1554, 5, 1226, 3], [1555, 4, 1227, 2, "shutterInner"], [1555, 16, 1227, 14], [1555, 18, 1227, 16], [1556, 6, 1228, 4, "width"], [1556, 11, 1228, 9], [1556, 13, 1228, 11], [1556, 15, 1228, 13], [1557, 6, 1229, 4, "height"], [1557, 12, 1229, 10], [1557, 14, 1229, 12], [1557, 16, 1229, 14], [1558, 6, 1230, 4, "borderRadius"], [1558, 18, 1230, 16], [1558, 20, 1230, 18], [1558, 22, 1230, 20], [1559, 6, 1231, 4, "backgroundColor"], [1559, 21, 1231, 19], [1559, 23, 1231, 21], [1559, 29, 1231, 27], [1560, 6, 1232, 4, "borderWidth"], [1560, 17, 1232, 15], [1560, 19, 1232, 17], [1560, 20, 1232, 18], [1561, 6, 1233, 4, "borderColor"], [1561, 17, 1233, 15], [1561, 19, 1233, 17], [1562, 4, 1234, 2], [1562, 5, 1234, 3], [1563, 4, 1235, 2, "privacyNote"], [1563, 15, 1235, 13], [1563, 17, 1235, 15], [1564, 6, 1236, 4, "fontSize"], [1564, 14, 1236, 12], [1564, 16, 1236, 14], [1564, 18, 1236, 16], [1565, 6, 1237, 4, "color"], [1565, 11, 1237, 9], [1565, 13, 1237, 11], [1566, 4, 1238, 2], [1566, 5, 1238, 3], [1567, 4, 1239, 2, "processingModal"], [1567, 19, 1239, 17], [1567, 21, 1239, 19], [1568, 6, 1240, 4, "flex"], [1568, 10, 1240, 8], [1568, 12, 1240, 10], [1568, 13, 1240, 11], [1569, 6, 1241, 4, "backgroundColor"], [1569, 21, 1241, 19], [1569, 23, 1241, 21], [1569, 43, 1241, 41], [1570, 6, 1242, 4, "justifyContent"], [1570, 20, 1242, 18], [1570, 22, 1242, 20], [1570, 30, 1242, 28], [1571, 6, 1243, 4, "alignItems"], [1571, 16, 1243, 14], [1571, 18, 1243, 16], [1572, 4, 1244, 2], [1572, 5, 1244, 3], [1573, 4, 1245, 2, "processingContent"], [1573, 21, 1245, 19], [1573, 23, 1245, 21], [1574, 6, 1246, 4, "backgroundColor"], [1574, 21, 1246, 19], [1574, 23, 1246, 21], [1574, 29, 1246, 27], [1575, 6, 1247, 4, "borderRadius"], [1575, 18, 1247, 16], [1575, 20, 1247, 18], [1575, 22, 1247, 20], [1576, 6, 1248, 4, "padding"], [1576, 13, 1248, 11], [1576, 15, 1248, 13], [1576, 17, 1248, 15], [1577, 6, 1249, 4, "width"], [1577, 11, 1249, 9], [1577, 13, 1249, 11], [1577, 18, 1249, 16], [1578, 6, 1250, 4, "max<PERSON><PERSON><PERSON>"], [1578, 14, 1250, 12], [1578, 16, 1250, 14], [1578, 19, 1250, 17], [1579, 6, 1251, 4, "alignItems"], [1579, 16, 1251, 14], [1579, 18, 1251, 16], [1580, 4, 1252, 2], [1580, 5, 1252, 3], [1581, 4, 1253, 2, "processingTitle"], [1581, 19, 1253, 17], [1581, 21, 1253, 19], [1582, 6, 1254, 4, "fontSize"], [1582, 14, 1254, 12], [1582, 16, 1254, 14], [1582, 18, 1254, 16], [1583, 6, 1255, 4, "fontWeight"], [1583, 16, 1255, 14], [1583, 18, 1255, 16], [1583, 23, 1255, 21], [1584, 6, 1256, 4, "color"], [1584, 11, 1256, 9], [1584, 13, 1256, 11], [1584, 22, 1256, 20], [1585, 6, 1257, 4, "marginTop"], [1585, 15, 1257, 13], [1585, 17, 1257, 15], [1585, 19, 1257, 17], [1586, 6, 1258, 4, "marginBottom"], [1586, 18, 1258, 16], [1586, 20, 1258, 18], [1587, 4, 1259, 2], [1587, 5, 1259, 3], [1588, 4, 1260, 2, "progressBar"], [1588, 15, 1260, 13], [1588, 17, 1260, 15], [1589, 6, 1261, 4, "width"], [1589, 11, 1261, 9], [1589, 13, 1261, 11], [1589, 19, 1261, 17], [1590, 6, 1262, 4, "height"], [1590, 12, 1262, 10], [1590, 14, 1262, 12], [1590, 15, 1262, 13], [1591, 6, 1263, 4, "backgroundColor"], [1591, 21, 1263, 19], [1591, 23, 1263, 21], [1591, 32, 1263, 30], [1592, 6, 1264, 4, "borderRadius"], [1592, 18, 1264, 16], [1592, 20, 1264, 18], [1592, 21, 1264, 19], [1593, 6, 1265, 4, "overflow"], [1593, 14, 1265, 12], [1593, 16, 1265, 14], [1593, 24, 1265, 22], [1594, 6, 1266, 4, "marginBottom"], [1594, 18, 1266, 16], [1594, 20, 1266, 18], [1595, 4, 1267, 2], [1595, 5, 1267, 3], [1596, 4, 1268, 2, "progressFill"], [1596, 16, 1268, 14], [1596, 18, 1268, 16], [1597, 6, 1269, 4, "height"], [1597, 12, 1269, 10], [1597, 14, 1269, 12], [1597, 20, 1269, 18], [1598, 6, 1270, 4, "backgroundColor"], [1598, 21, 1270, 19], [1598, 23, 1270, 21], [1598, 32, 1270, 30], [1599, 6, 1271, 4, "borderRadius"], [1599, 18, 1271, 16], [1599, 20, 1271, 18], [1600, 4, 1272, 2], [1600, 5, 1272, 3], [1601, 4, 1273, 2, "processingDescription"], [1601, 25, 1273, 23], [1601, 27, 1273, 25], [1602, 6, 1274, 4, "fontSize"], [1602, 14, 1274, 12], [1602, 16, 1274, 14], [1602, 18, 1274, 16], [1603, 6, 1275, 4, "color"], [1603, 11, 1275, 9], [1603, 13, 1275, 11], [1603, 22, 1275, 20], [1604, 6, 1276, 4, "textAlign"], [1604, 15, 1276, 13], [1604, 17, 1276, 15], [1605, 4, 1277, 2], [1605, 5, 1277, 3], [1606, 4, 1278, 2, "successIcon"], [1606, 15, 1278, 13], [1606, 17, 1278, 15], [1607, 6, 1279, 4, "marginTop"], [1607, 15, 1279, 13], [1607, 17, 1279, 15], [1608, 4, 1280, 2], [1608, 5, 1280, 3], [1609, 4, 1281, 2, "errorContent"], [1609, 16, 1281, 14], [1609, 18, 1281, 16], [1610, 6, 1282, 4, "backgroundColor"], [1610, 21, 1282, 19], [1610, 23, 1282, 21], [1610, 29, 1282, 27], [1611, 6, 1283, 4, "borderRadius"], [1611, 18, 1283, 16], [1611, 20, 1283, 18], [1611, 22, 1283, 20], [1612, 6, 1284, 4, "padding"], [1612, 13, 1284, 11], [1612, 15, 1284, 13], [1612, 17, 1284, 15], [1613, 6, 1285, 4, "width"], [1613, 11, 1285, 9], [1613, 13, 1285, 11], [1613, 18, 1285, 16], [1614, 6, 1286, 4, "max<PERSON><PERSON><PERSON>"], [1614, 14, 1286, 12], [1614, 16, 1286, 14], [1614, 19, 1286, 17], [1615, 6, 1287, 4, "alignItems"], [1615, 16, 1287, 14], [1615, 18, 1287, 16], [1616, 4, 1288, 2], [1616, 5, 1288, 3], [1617, 4, 1289, 2, "errorTitle"], [1617, 14, 1289, 12], [1617, 16, 1289, 14], [1618, 6, 1290, 4, "fontSize"], [1618, 14, 1290, 12], [1618, 16, 1290, 14], [1618, 18, 1290, 16], [1619, 6, 1291, 4, "fontWeight"], [1619, 16, 1291, 14], [1619, 18, 1291, 16], [1619, 23, 1291, 21], [1620, 6, 1292, 4, "color"], [1620, 11, 1292, 9], [1620, 13, 1292, 11], [1620, 22, 1292, 20], [1621, 6, 1293, 4, "marginTop"], [1621, 15, 1293, 13], [1621, 17, 1293, 15], [1621, 19, 1293, 17], [1622, 6, 1294, 4, "marginBottom"], [1622, 18, 1294, 16], [1622, 20, 1294, 18], [1623, 4, 1295, 2], [1623, 5, 1295, 3], [1624, 4, 1296, 2, "errorMessage"], [1624, 16, 1296, 14], [1624, 18, 1296, 16], [1625, 6, 1297, 4, "fontSize"], [1625, 14, 1297, 12], [1625, 16, 1297, 14], [1625, 18, 1297, 16], [1626, 6, 1298, 4, "color"], [1626, 11, 1298, 9], [1626, 13, 1298, 11], [1626, 22, 1298, 20], [1627, 6, 1299, 4, "textAlign"], [1627, 15, 1299, 13], [1627, 17, 1299, 15], [1627, 25, 1299, 23], [1628, 6, 1300, 4, "marginBottom"], [1628, 18, 1300, 16], [1628, 20, 1300, 18], [1629, 4, 1301, 2], [1629, 5, 1301, 3], [1630, 4, 1302, 2, "primaryButton"], [1630, 17, 1302, 15], [1630, 19, 1302, 17], [1631, 6, 1303, 4, "backgroundColor"], [1631, 21, 1303, 19], [1631, 23, 1303, 21], [1631, 32, 1303, 30], [1632, 6, 1304, 4, "paddingHorizontal"], [1632, 23, 1304, 21], [1632, 25, 1304, 23], [1632, 27, 1304, 25], [1633, 6, 1305, 4, "paddingVertical"], [1633, 21, 1305, 19], [1633, 23, 1305, 21], [1633, 25, 1305, 23], [1634, 6, 1306, 4, "borderRadius"], [1634, 18, 1306, 16], [1634, 20, 1306, 18], [1634, 21, 1306, 19], [1635, 6, 1307, 4, "marginTop"], [1635, 15, 1307, 13], [1635, 17, 1307, 15], [1636, 4, 1308, 2], [1636, 5, 1308, 3], [1637, 4, 1309, 2, "primaryButtonText"], [1637, 21, 1309, 19], [1637, 23, 1309, 21], [1638, 6, 1310, 4, "color"], [1638, 11, 1310, 9], [1638, 13, 1310, 11], [1638, 19, 1310, 17], [1639, 6, 1311, 4, "fontSize"], [1639, 14, 1311, 12], [1639, 16, 1311, 14], [1639, 18, 1311, 16], [1640, 6, 1312, 4, "fontWeight"], [1640, 16, 1312, 14], [1640, 18, 1312, 16], [1641, 4, 1313, 2], [1641, 5, 1313, 3], [1642, 4, 1314, 2, "secondaryButton"], [1642, 19, 1314, 17], [1642, 21, 1314, 19], [1643, 6, 1315, 4, "paddingHorizontal"], [1643, 23, 1315, 21], [1643, 25, 1315, 23], [1643, 27, 1315, 25], [1644, 6, 1316, 4, "paddingVertical"], [1644, 21, 1316, 19], [1644, 23, 1316, 21], [1644, 25, 1316, 23], [1645, 6, 1317, 4, "marginTop"], [1645, 15, 1317, 13], [1645, 17, 1317, 15], [1646, 4, 1318, 2], [1646, 5, 1318, 3], [1647, 4, 1319, 2, "secondaryButtonText"], [1647, 23, 1319, 21], [1647, 25, 1319, 23], [1648, 6, 1320, 4, "color"], [1648, 11, 1320, 9], [1648, 13, 1320, 11], [1648, 22, 1320, 20], [1649, 6, 1321, 4, "fontSize"], [1649, 14, 1321, 12], [1649, 16, 1321, 14], [1650, 4, 1322, 2], [1650, 5, 1322, 3], [1651, 4, 1323, 2, "permissionContent"], [1651, 21, 1323, 19], [1651, 23, 1323, 21], [1652, 6, 1324, 4, "flex"], [1652, 10, 1324, 8], [1652, 12, 1324, 10], [1652, 13, 1324, 11], [1653, 6, 1325, 4, "justifyContent"], [1653, 20, 1325, 18], [1653, 22, 1325, 20], [1653, 30, 1325, 28], [1654, 6, 1326, 4, "alignItems"], [1654, 16, 1326, 14], [1654, 18, 1326, 16], [1654, 26, 1326, 24], [1655, 6, 1327, 4, "padding"], [1655, 13, 1327, 11], [1655, 15, 1327, 13], [1656, 4, 1328, 2], [1656, 5, 1328, 3], [1657, 4, 1329, 2, "permissionTitle"], [1657, 19, 1329, 17], [1657, 21, 1329, 19], [1658, 6, 1330, 4, "fontSize"], [1658, 14, 1330, 12], [1658, 16, 1330, 14], [1658, 18, 1330, 16], [1659, 6, 1331, 4, "fontWeight"], [1659, 16, 1331, 14], [1659, 18, 1331, 16], [1659, 23, 1331, 21], [1660, 6, 1332, 4, "color"], [1660, 11, 1332, 9], [1660, 13, 1332, 11], [1660, 22, 1332, 20], [1661, 6, 1333, 4, "marginTop"], [1661, 15, 1333, 13], [1661, 17, 1333, 15], [1661, 19, 1333, 17], [1662, 6, 1334, 4, "marginBottom"], [1662, 18, 1334, 16], [1662, 20, 1334, 18], [1663, 4, 1335, 2], [1663, 5, 1335, 3], [1664, 4, 1336, 2, "permissionDescription"], [1664, 25, 1336, 23], [1664, 27, 1336, 25], [1665, 6, 1337, 4, "fontSize"], [1665, 14, 1337, 12], [1665, 16, 1337, 14], [1665, 18, 1337, 16], [1666, 6, 1338, 4, "color"], [1666, 11, 1338, 9], [1666, 13, 1338, 11], [1666, 22, 1338, 20], [1667, 6, 1339, 4, "textAlign"], [1667, 15, 1339, 13], [1667, 17, 1339, 15], [1667, 25, 1339, 23], [1668, 6, 1340, 4, "marginBottom"], [1668, 18, 1340, 16], [1668, 20, 1340, 18], [1669, 4, 1341, 2], [1669, 5, 1341, 3], [1670, 4, 1342, 2, "loadingText"], [1670, 15, 1342, 13], [1670, 17, 1342, 15], [1671, 6, 1343, 4, "color"], [1671, 11, 1343, 9], [1671, 13, 1343, 11], [1671, 22, 1343, 20], [1672, 6, 1344, 4, "marginTop"], [1672, 15, 1344, 13], [1672, 17, 1344, 15], [1673, 4, 1345, 2], [1673, 5, 1345, 3], [1674, 4, 1346, 2], [1675, 4, 1347, 2, "blurZone"], [1675, 12, 1347, 10], [1675, 14, 1347, 12], [1676, 6, 1348, 4, "position"], [1676, 14, 1348, 12], [1676, 16, 1348, 14], [1676, 26, 1348, 24], [1677, 6, 1349, 4, "overflow"], [1677, 14, 1349, 12], [1677, 16, 1349, 14], [1678, 4, 1350, 2], [1678, 5, 1350, 3], [1679, 4, 1351, 2, "previewChip"], [1679, 15, 1351, 13], [1679, 17, 1351, 15], [1680, 6, 1352, 4, "position"], [1680, 14, 1352, 12], [1680, 16, 1352, 14], [1680, 26, 1352, 24], [1681, 6, 1353, 4, "top"], [1681, 9, 1353, 7], [1681, 11, 1353, 9], [1681, 12, 1353, 10], [1682, 6, 1354, 4, "right"], [1682, 11, 1354, 9], [1682, 13, 1354, 11], [1682, 14, 1354, 12], [1683, 6, 1355, 4, "backgroundColor"], [1683, 21, 1355, 19], [1683, 23, 1355, 21], [1683, 40, 1355, 38], [1684, 6, 1356, 4, "paddingHorizontal"], [1684, 23, 1356, 21], [1684, 25, 1356, 23], [1684, 27, 1356, 25], [1685, 6, 1357, 4, "paddingVertical"], [1685, 21, 1357, 19], [1685, 23, 1357, 21], [1685, 24, 1357, 22], [1686, 6, 1358, 4, "borderRadius"], [1686, 18, 1358, 16], [1686, 20, 1358, 18], [1687, 4, 1359, 2], [1687, 5, 1359, 3], [1688, 4, 1360, 2, "previewChipText"], [1688, 19, 1360, 17], [1688, 21, 1360, 19], [1689, 6, 1361, 4, "color"], [1689, 11, 1361, 9], [1689, 13, 1361, 11], [1689, 19, 1361, 17], [1690, 6, 1362, 4, "fontSize"], [1690, 14, 1362, 12], [1690, 16, 1362, 14], [1690, 18, 1362, 16], [1691, 6, 1363, 4, "fontWeight"], [1691, 16, 1363, 14], [1691, 18, 1363, 16], [1692, 4, 1364, 2], [1693, 2, 1365, 0], [1693, 3, 1365, 1], [1693, 4, 1365, 2], [1694, 2, 1365, 3], [1694, 6, 1365, 3, "_c"], [1694, 8, 1365, 3], [1695, 2, 1365, 3, "$RefreshReg$"], [1695, 14, 1365, 3], [1695, 15, 1365, 3, "_c"], [1695, 17, 1365, 3], [1696, 0, 1365, 3], [1696, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "detectFacesAggressive", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;gCSE;GToC;+BUE;GV0C;qBWE;GXQ;8BYE;GZ4B;2BaE;Gba;wBcE;GdiB;0BeG;GfmE;0BgBE;GhBuB;gCiBE;kBCa;KDG;GjBC;mCmBG;wBfc,kCe;GnBoC;mCoBE;wBhBa;OgBI;oFC+C;UDM;8BEW;SF0C;uDhBa;sBmBC,wBnB;OgBC;GpBmB;6BwBG;GxB6B;kCyBG;GzB8C;4B0BE;mBCmD;SDE;G1BO;uB4BE;G5BI;mC6BG;G7BM;YCE;GDK;oB8B2C;W9BG;yB+BC;W/BG;wBgCC;WhCI;CD4L"}}, "type": "js/module"}]}