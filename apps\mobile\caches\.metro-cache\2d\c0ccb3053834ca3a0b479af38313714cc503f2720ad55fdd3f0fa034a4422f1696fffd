{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 36, "index": 638}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkRect", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 639}, "end": {"line": 5, "column": 40, "index": 679}}], "key": "VBkFjQz9GOtB0AbNPoXYbn3D5z0=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkVertices = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkRect = require(_dependencyMap[1], \"./JsiSkRect\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class JsiSkVertices extends _Host.HostObject {\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"Vertices\");\n      _defineProperty(this, \"dispose\", () => {\n        this.ref.delete();\n      });\n    }\n    bounds() {\n      return new _JsiSkRect.JsiSkRect(this.CanvasKit, this.ref.bounds());\n    }\n    uniqueID() {\n      return this.ref.uniqueID();\n    }\n  }\n  exports.JsiSkVertices = JsiSkVertices;\n});", "lineCount": 45, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_Host"], [6, 11, 4, 0], [6, 14, 4, 0, "require"], [6, 21, 4, 0], [6, 22, 4, 0, "_dependencyMap"], [6, 36, 4, 0], [7, 2, 5, 0], [7, 6, 5, 0, "_JsiSkRect"], [7, 16, 5, 0], [7, 19, 5, 0, "require"], [7, 26, 5, 0], [7, 27, 5, 0, "_dependencyMap"], [7, 41, 5, 0], [8, 2, 1, 0], [8, 11, 1, 9, "_defineProperty"], [8, 26, 1, 24, "_defineProperty"], [8, 27, 1, 25, "e"], [8, 28, 1, 26], [8, 30, 1, 28, "r"], [8, 31, 1, 29], [8, 33, 1, 31, "t"], [8, 34, 1, 32], [8, 36, 1, 34], [9, 4, 1, 36], [9, 11, 1, 43], [9, 12, 1, 44, "r"], [9, 13, 1, 45], [9, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [9, 30, 1, 62], [9, 31, 1, 63, "r"], [9, 32, 1, 64], [9, 33, 1, 65], [9, 38, 1, 70, "e"], [9, 39, 1, 71], [9, 42, 1, 74, "Object"], [9, 48, 1, 80], [9, 49, 1, 81, "defineProperty"], [9, 63, 1, 95], [9, 64, 1, 96, "e"], [9, 65, 1, 97], [9, 67, 1, 99, "r"], [9, 68, 1, 100], [9, 70, 1, 102], [10, 6, 1, 104, "value"], [10, 11, 1, 109], [10, 13, 1, 111, "t"], [10, 14, 1, 112], [11, 6, 1, 114, "enumerable"], [11, 16, 1, 124], [11, 18, 1, 126], [11, 19, 1, 127], [11, 20, 1, 128], [12, 6, 1, 130, "configurable"], [12, 18, 1, 142], [12, 20, 1, 144], [12, 21, 1, 145], [12, 22, 1, 146], [13, 6, 1, 148, "writable"], [13, 14, 1, 156], [13, 16, 1, 158], [13, 17, 1, 159], [14, 4, 1, 161], [14, 5, 1, 162], [14, 6, 1, 163], [14, 9, 1, 166, "e"], [14, 10, 1, 167], [14, 11, 1, 168, "r"], [14, 12, 1, 169], [14, 13, 1, 170], [14, 16, 1, 173, "t"], [14, 17, 1, 174], [14, 19, 1, 176, "e"], [14, 20, 1, 177], [15, 2, 1, 179], [16, 2, 2, 0], [16, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [16, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [16, 26, 2, 24, "t"], [16, 27, 2, 25], [16, 29, 2, 27], [17, 4, 2, 29], [17, 8, 2, 33, "i"], [17, 9, 2, 34], [17, 12, 2, 37, "_toPrimitive"], [17, 24, 2, 49], [17, 25, 2, 50, "t"], [17, 26, 2, 51], [17, 28, 2, 53], [17, 36, 2, 61], [17, 37, 2, 62], [18, 4, 2, 64], [18, 11, 2, 71], [18, 19, 2, 79], [18, 23, 2, 83], [18, 30, 2, 90, "i"], [18, 31, 2, 91], [18, 34, 2, 94, "i"], [18, 35, 2, 95], [18, 38, 2, 98, "i"], [18, 39, 2, 99], [18, 42, 2, 102], [18, 44, 2, 104], [19, 2, 2, 106], [20, 2, 3, 0], [20, 11, 3, 9, "_toPrimitive"], [20, 23, 3, 21, "_toPrimitive"], [20, 24, 3, 22, "t"], [20, 25, 3, 23], [20, 27, 3, 25, "r"], [20, 28, 3, 26], [20, 30, 3, 28], [21, 4, 3, 30], [21, 8, 3, 34], [21, 16, 3, 42], [21, 20, 3, 46], [21, 27, 3, 53, "t"], [21, 28, 3, 54], [21, 32, 3, 58], [21, 33, 3, 59, "t"], [21, 34, 3, 60], [21, 36, 3, 62], [21, 43, 3, 69, "t"], [21, 44, 3, 70], [22, 4, 3, 72], [22, 8, 3, 76, "e"], [22, 9, 3, 77], [22, 12, 3, 80, "t"], [22, 13, 3, 81], [22, 14, 3, 82, "Symbol"], [22, 20, 3, 88], [22, 21, 3, 89, "toPrimitive"], [22, 32, 3, 100], [22, 33, 3, 101], [23, 4, 3, 103], [23, 8, 3, 107], [23, 13, 3, 112], [23, 14, 3, 113], [23, 19, 3, 118, "e"], [23, 20, 3, 119], [23, 22, 3, 121], [24, 6, 3, 123], [24, 10, 3, 127, "i"], [24, 11, 3, 128], [24, 14, 3, 131, "e"], [24, 15, 3, 132], [24, 16, 3, 133, "call"], [24, 20, 3, 137], [24, 21, 3, 138, "t"], [24, 22, 3, 139], [24, 24, 3, 141, "r"], [24, 25, 3, 142], [24, 29, 3, 146], [24, 38, 3, 155], [24, 39, 3, 156], [25, 6, 3, 158], [25, 10, 3, 162], [25, 18, 3, 170], [25, 22, 3, 174], [25, 29, 3, 181, "i"], [25, 30, 3, 182], [25, 32, 3, 184], [25, 39, 3, 191, "i"], [25, 40, 3, 192], [26, 6, 3, 194], [26, 12, 3, 200], [26, 16, 3, 204, "TypeError"], [26, 25, 3, 213], [26, 26, 3, 214], [26, 72, 3, 260], [26, 73, 3, 261], [27, 4, 3, 263], [28, 4, 3, 265], [28, 11, 3, 272], [28, 12, 3, 273], [28, 20, 3, 281], [28, 25, 3, 286, "r"], [28, 26, 3, 287], [28, 29, 3, 290, "String"], [28, 35, 3, 296], [28, 38, 3, 299, "Number"], [28, 44, 3, 305], [28, 46, 3, 307, "t"], [28, 47, 3, 308], [28, 48, 3, 309], [29, 2, 3, 311], [30, 2, 6, 7], [30, 8, 6, 13, "JsiSkVertices"], [30, 21, 6, 26], [30, 30, 6, 35, "HostObject"], [30, 46, 6, 45], [30, 47, 6, 46], [31, 4, 7, 2, "constructor"], [31, 15, 7, 13, "constructor"], [31, 16, 7, 14, "CanvasKit"], [31, 25, 7, 23], [31, 27, 7, 25, "ref"], [31, 30, 7, 28], [31, 32, 7, 30], [32, 6, 8, 4], [32, 11, 8, 9], [32, 12, 8, 10, "CanvasKit"], [32, 21, 8, 19], [32, 23, 8, 21, "ref"], [32, 26, 8, 24], [32, 28, 8, 26], [32, 38, 8, 36], [32, 39, 8, 37], [33, 6, 9, 4, "_defineProperty"], [33, 21, 9, 19], [33, 22, 9, 20], [33, 26, 9, 24], [33, 28, 9, 26], [33, 37, 9, 35], [33, 39, 9, 37], [33, 45, 9, 43], [34, 8, 10, 6], [34, 12, 10, 10], [34, 13, 10, 11, "ref"], [34, 16, 10, 14], [34, 17, 10, 15, "delete"], [34, 23, 10, 21], [34, 24, 10, 22], [34, 25, 10, 23], [35, 6, 11, 4], [35, 7, 11, 5], [35, 8, 11, 6], [36, 4, 12, 2], [37, 4, 13, 2, "bounds"], [37, 10, 13, 8, "bounds"], [37, 11, 13, 8], [37, 13, 13, 11], [38, 6, 14, 4], [38, 13, 14, 11], [38, 17, 14, 15, "JsiSkRect"], [38, 37, 14, 24], [38, 38, 14, 25], [38, 42, 14, 29], [38, 43, 14, 30, "CanvasKit"], [38, 52, 14, 39], [38, 54, 14, 41], [38, 58, 14, 45], [38, 59, 14, 46, "ref"], [38, 62, 14, 49], [38, 63, 14, 50, "bounds"], [38, 69, 14, 56], [38, 70, 14, 57], [38, 71, 14, 58], [38, 72, 14, 59], [39, 4, 15, 2], [40, 4, 16, 2, "uniqueID"], [40, 12, 16, 10, "uniqueID"], [40, 13, 16, 10], [40, 15, 16, 13], [41, 6, 17, 4], [41, 13, 17, 11], [41, 17, 17, 15], [41, 18, 17, 16, "ref"], [41, 21, 17, 19], [41, 22, 17, 20, "uniqueID"], [41, 30, 17, 28], [41, 31, 17, 29], [41, 32, 17, 30], [42, 4, 18, 2], [43, 2, 19, 0], [44, 2, 19, 1, "exports"], [44, 9, 19, 1], [44, 10, 19, 1, "JsiSkVertices"], [44, 23, 19, 1], [44, 26, 19, 1, "JsiSkVertices"], [44, 39, 19, 1], [45, 0, 19, 1], [45, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "JsiSkVertices", "constructor", "_defineProperty$argument_2", "bounds", "uniqueID"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGG;ECC;qCCE;KDE;GDC;EGC;GHE;EIC;GJE;CHC"}}, "type": "js/module"}]}