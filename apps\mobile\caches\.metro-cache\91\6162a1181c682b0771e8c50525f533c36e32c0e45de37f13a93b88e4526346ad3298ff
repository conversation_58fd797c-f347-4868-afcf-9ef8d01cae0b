{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 26, "index": 26}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "../Group", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 27}, "end": {"line": 2, "column": 33, "index": 60}}], "key": "nkmtmvfA8QTKCcfZTirG7rPnLHc=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.BackdropFilter = void 0;\n  var _react = _interopRequireDefault(require(_dependencyMap[1], \"react\"));\n  var _Group = require(_dependencyMap[2], \"../Group\");\n  const BackdropFilter = ({\n    filter,\n    children: groupChildren,\n    ...props\n  }) => {\n    return /*#__PURE__*/_react.default.createElement(_Group.Group, props, /*#__PURE__*/_react.default.createElement(\"skBackdropFilter\", null, filter), groupChildren);\n  };\n  exports.BackdropFilter = BackdropFilter;\n});", "lineCount": 17, "map": [[7, 2, 1, 0], [7, 6, 1, 0, "_react"], [7, 12, 1, 0], [7, 15, 1, 0, "_interopRequireDefault"], [7, 37, 1, 0], [7, 38, 1, 0, "require"], [7, 45, 1, 0], [7, 46, 1, 0, "_dependencyMap"], [7, 60, 1, 0], [8, 2, 2, 0], [8, 6, 2, 0, "_Group"], [8, 12, 2, 0], [8, 15, 2, 0, "require"], [8, 22, 2, 0], [8, 23, 2, 0, "_dependencyMap"], [8, 37, 2, 0], [9, 2, 3, 7], [9, 8, 3, 13, "<PERSON>drop<PERSON><PERSON><PERSON>"], [9, 22, 3, 27], [9, 25, 3, 30, "<PERSON>drop<PERSON><PERSON><PERSON>"], [9, 26, 3, 31], [10, 4, 4, 2, "filter"], [10, 10, 4, 8], [11, 4, 5, 2, "children"], [11, 12, 5, 10], [11, 14, 5, 12, "groupChildren"], [11, 27, 5, 25], [12, 4, 6, 2], [12, 7, 6, 5, "props"], [13, 2, 7, 0], [13, 3, 7, 1], [13, 8, 7, 6], [14, 4, 8, 2], [14, 11, 8, 9], [14, 24, 8, 22, "React"], [14, 38, 8, 27], [14, 39, 8, 28, "createElement"], [14, 52, 8, 41], [14, 53, 8, 42, "Group"], [14, 65, 8, 47], [14, 67, 8, 49, "props"], [14, 72, 8, 54], [14, 74, 8, 56], [14, 87, 8, 69, "React"], [14, 101, 8, 74], [14, 102, 8, 75, "createElement"], [14, 115, 8, 88], [14, 116, 8, 89], [14, 134, 8, 107], [14, 136, 8, 109], [14, 140, 8, 113], [14, 142, 8, 115, "filter"], [14, 148, 8, 121], [14, 149, 8, 122], [14, 151, 8, 124, "groupChildren"], [14, 164, 8, 137], [14, 165, 8, 138], [15, 2, 9, 0], [15, 3, 9, 1], [16, 2, 9, 2, "exports"], [16, 9, 9, 2], [16, 10, 9, 2, "<PERSON>drop<PERSON><PERSON><PERSON>"], [16, 24, 9, 2], [16, 27, 9, 2, "<PERSON>drop<PERSON><PERSON><PERSON>"], [16, 41, 9, 2], [17, 0, 9, 2], [17, 3]], "functionMap": {"names": ["<global>", "<PERSON>drop<PERSON><PERSON><PERSON>"], "mappings": "AAA;8BCE;CDM"}}, "type": "js/module"}]}