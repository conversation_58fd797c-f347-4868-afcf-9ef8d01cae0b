{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // Real face detection and blurring using browser APIs and CDN libraries\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        setProcessingProgress(60);\n\n        // Try multiple face detection approaches\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting face detection on image:', {\n          width: img.width,\n          height: img.height,\n          src: photoUri.substring(0, 50) + '...'\n        });\n\n        // Method 1: Try browser's native Face Detection API\n        try {\n          if ('FaceDetector' in window) {\n            console.log('[EchoCameraWeb] ✅ Browser Face Detection API available, attempting detection...');\n            const faceDetector = new window.FaceDetector({\n              maxDetectedFaces: 10,\n              fastMode: false\n            });\n            const browserDetections = await faceDetector.detect(img);\n            detectedFaces = browserDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.boundingBox.x + detection.boundingBox.width / 2) / img.width,\n                yCenter: (detection.boundingBox.y + detection.boundingBox.height / 2) / img.height,\n                width: detection.boundingBox.width / img.width,\n                height: detection.boundingBox.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] ✅ Browser Face Detection API found ${detectedFaces.length} faces`);\n          } else {\n            console.log('[EchoCameraWeb] ❌ Browser Face Detection API not available in this browser');\n            throw new Error('Browser Face Detection API not available');\n          }\n        } catch (browserError) {\n          console.warn('[EchoCameraWeb] ❌ Browser face detection failed, trying face-api.js from CDN:', browserError);\n\n          // Method 2: Try loading face-api.js from CDN\n          try {\n            // Load face-api.js from CDN if not already loaded\n            if (!window.faceapi) {\n              await new Promise((resolve, reject) => {\n                const script = document.createElement('script');\n                script.src = 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js';\n                script.onload = resolve;\n                script.onerror = reject;\n                document.head.appendChild(script);\n              });\n            }\n            const faceapi = window.faceapi;\n\n            // Load models from CDN\n            await Promise.all([faceapi.nets.tinyFaceDetector.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'), faceapi.nets.faceLandmark68Net.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights')]);\n\n            // Detect faces\n            const faceDetections = await faceapi.detectAllFaces(img, new faceapi.TinyFaceDetectorOptions());\n            detectedFaces = faceDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.box.x + detection.box.width / 2) / img.width,\n                yCenter: (detection.box.y + detection.box.height / 2) / img.height,\n                width: detection.box.width / img.width,\n                height: detection.box.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] ✅ face-api.js found ${detectedFaces.length} faces`);\n          } catch (faceApiError) {\n            console.warn('[EchoCameraWeb] ❌ face-api.js also failed:', faceApiError);\n\n            // Method 3: Fallback - Mock face detection for testing (assumes center face)\n            console.log('[EchoCameraWeb] 🧪 Using fallback mock face detection for testing...');\n            detectedFaces = [{\n              boundingBox: {\n                xCenter: 0.5,\n                // Center of image\n                yCenter: 0.4,\n                // Slightly above center (typical face position)\n                width: 0.3,\n                // 30% of image width\n                height: 0.4 // 40% of image height\n              }\n            }];\n            console.log(`[EchoCameraWeb] 🧪 Mock detection created 1 face at center of image`);\n          }\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // Apply blurring to each detected face\n        if (detectedFaces.length > 0) {\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add some padding around the face\n            const padding = 0.2; // 20% padding\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎨 Blurring face ${index + 1} at (${Math.round(paddedX)}, ${Math.round(paddedY)}) size ${Math.round(paddedWidth)}x${Math.round(paddedHeight)}`);\n\n            // Get the face region image data\n            const faceImageData = ctx.getImageData(paddedX, paddedY, paddedWidth, paddedHeight);\n            const data = faceImageData.data;\n            console.log(`[EchoCameraWeb] 📊 Face region data: ${data.length} bytes, ${paddedWidth}x${paddedHeight} pixels`);\n\n            // Apply pixelation blur effect\n            const pixelSize = Math.max(12, Math.min(paddedWidth, paddedHeight) / 15); // Increased pixel size for more visible effect\n            console.log(`[EchoCameraWeb] 🔲 Using pixel size: ${pixelSize}px for blurring`);\n            for (let y = 0; y < paddedHeight; y += pixelSize) {\n              for (let x = 0; x < paddedWidth; x += pixelSize) {\n                // Get the color of the top-left pixel in this block\n                const pixelIndex = (y * paddedWidth + x) * 4;\n                const r = data[pixelIndex];\n                const g = data[pixelIndex + 1];\n                const b = data[pixelIndex + 2];\n                const a = data[pixelIndex + 3];\n\n                // Apply this color to the entire block\n                for (let dy = 0; dy < pixelSize && y + dy < paddedHeight; dy++) {\n                  for (let dx = 0; dx < pixelSize && x + dx < paddedWidth; dx++) {\n                    const blockPixelIndex = ((y + dy) * paddedWidth + (x + dx)) * 4;\n                    data[blockPixelIndex] = r;\n                    data[blockPixelIndex + 1] = g;\n                    data[blockPixelIndex + 2] = b;\n                    data[blockPixelIndex + 3] = a;\n                  }\n                }\n              }\n            }\n\n            // Put the blurred face region back on the canvas\n            ctx.putImageData(faceImageData, paddedX, paddedY);\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} blurring applied successfully`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] Processing complete:', result);\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 519,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 575,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 642,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 638,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 679,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 708,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 698,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 697,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 692,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 737,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 748,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 744,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 734,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 733,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 728,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 548,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1348, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [89, 4, 89, 2], [90, 4, 90, 2], [90, 10, 90, 8, "capturePhoto"], [90, 22, 90, 20], [90, 25, 90, 23], [90, 29, 90, 23, "useCallback"], [90, 47, 90, 34], [90, 49, 90, 35], [90, 61, 90, 47], [91, 6, 91, 4], [92, 6, 92, 4], [92, 12, 92, 10, "isDev"], [92, 17, 92, 15], [92, 20, 92, 18, "process"], [92, 27, 92, 25], [92, 28, 92, 26, "env"], [92, 31, 92, 29], [92, 32, 92, 30, "NODE_ENV"], [92, 40, 92, 38], [92, 45, 92, 43], [92, 58, 92, 56], [92, 62, 92, 60, "__DEV__"], [92, 69, 92, 67], [93, 6, 94, 4], [93, 10, 94, 8], [93, 11, 94, 9, "cameraRef"], [93, 20, 94, 18], [93, 21, 94, 19, "current"], [93, 28, 94, 26], [93, 32, 94, 30], [93, 33, 94, 31, "isDev"], [93, 38, 94, 36], [93, 40, 94, 38], [94, 8, 95, 6, "<PERSON><PERSON>"], [94, 22, 95, 11], [94, 23, 95, 12, "alert"], [94, 28, 95, 17], [94, 29, 95, 18], [94, 36, 95, 25], [94, 38, 95, 27], [94, 56, 95, 45], [94, 57, 95, 46], [95, 8, 96, 6], [96, 6, 97, 4], [97, 6, 98, 4], [97, 10, 98, 8], [98, 8, 99, 6, "setProcessingState"], [98, 26, 99, 24], [98, 27, 99, 25], [98, 38, 99, 36], [98, 39, 99, 37], [99, 8, 100, 6, "setProcessingProgress"], [99, 29, 100, 27], [99, 30, 100, 28], [99, 32, 100, 30], [99, 33, 100, 31], [100, 8, 101, 6], [101, 8, 102, 6], [102, 8, 103, 6], [103, 8, 104, 6], [103, 14, 104, 12], [103, 18, 104, 16, "Promise"], [103, 25, 104, 23], [103, 26, 104, 24, "resolve"], [103, 33, 104, 31], [103, 37, 104, 35, "setTimeout"], [103, 47, 104, 45], [103, 48, 104, 46, "resolve"], [103, 55, 104, 53], [103, 57, 104, 55], [103, 59, 104, 57], [103, 60, 104, 58], [103, 61, 104, 59], [104, 8, 105, 6], [105, 8, 106, 6], [105, 12, 106, 10, "photo"], [105, 17, 106, 15], [106, 8, 108, 6], [106, 12, 108, 10], [107, 10, 109, 8, "photo"], [107, 15, 109, 13], [107, 18, 109, 16], [107, 24, 109, 22, "cameraRef"], [107, 33, 109, 31], [107, 34, 109, 32, "current"], [107, 41, 109, 39], [107, 42, 109, 40, "takePictureAsync"], [107, 58, 109, 56], [107, 59, 109, 57], [108, 12, 110, 10, "quality"], [108, 19, 110, 17], [108, 21, 110, 19], [108, 24, 110, 22], [109, 12, 111, 10, "base64"], [109, 18, 111, 16], [109, 20, 111, 18], [109, 25, 111, 23], [110, 12, 112, 10, "skipProcessing"], [110, 26, 112, 24], [110, 28, 112, 26], [110, 32, 112, 30], [110, 33, 112, 32], [111, 10, 113, 8], [111, 11, 113, 9], [111, 12, 113, 10], [112, 8, 114, 6], [112, 9, 114, 7], [112, 10, 114, 8], [112, 17, 114, 15, "cameraError"], [112, 28, 114, 26], [112, 30, 114, 28], [113, 10, 115, 8, "console"], [113, 17, 115, 15], [113, 18, 115, 16, "log"], [113, 21, 115, 19], [113, 22, 115, 20], [113, 82, 115, 80], [113, 84, 115, 82, "cameraError"], [113, 95, 115, 93], [113, 96, 115, 94], [114, 10, 116, 8], [115, 10, 117, 8], [115, 14, 117, 12, "isDev"], [115, 19, 117, 17], [115, 21, 117, 19], [116, 12, 118, 10, "photo"], [116, 17, 118, 15], [116, 20, 118, 18], [117, 14, 119, 12, "uri"], [117, 17, 119, 15], [117, 19, 119, 17], [118, 12, 120, 10], [118, 13, 120, 11], [119, 10, 121, 8], [119, 11, 121, 9], [119, 17, 121, 15], [120, 12, 122, 10], [120, 18, 122, 16, "cameraError"], [120, 29, 122, 27], [121, 10, 123, 8], [122, 8, 124, 6], [123, 8, 125, 6], [123, 12, 125, 10], [123, 13, 125, 11, "photo"], [123, 18, 125, 16], [123, 20, 125, 18], [124, 10, 126, 8], [124, 16, 126, 14], [124, 20, 126, 18, "Error"], [124, 25, 126, 23], [124, 26, 126, 24], [124, 51, 126, 49], [124, 52, 126, 50], [125, 8, 127, 6], [126, 8, 128, 6, "console"], [126, 15, 128, 13], [126, 16, 128, 14, "log"], [126, 19, 128, 17], [126, 20, 128, 18], [126, 56, 128, 54], [126, 58, 128, 56, "photo"], [126, 63, 128, 61], [126, 64, 128, 62, "uri"], [126, 67, 128, 65], [126, 68, 128, 66], [127, 8, 129, 6, "setCapturedPhoto"], [127, 24, 129, 22], [127, 25, 129, 23, "photo"], [127, 30, 129, 28], [127, 31, 129, 29, "uri"], [127, 34, 129, 32], [127, 35, 129, 33], [128, 8, 130, 6, "setProcessingProgress"], [128, 29, 130, 27], [128, 30, 130, 28], [128, 32, 130, 30], [128, 33, 130, 31], [129, 8, 131, 6], [130, 8, 132, 6, "console"], [130, 15, 132, 13], [130, 16, 132, 14, "log"], [130, 19, 132, 17], [130, 20, 132, 18], [130, 73, 132, 71], [130, 74, 132, 72], [131, 8, 133, 6], [131, 14, 133, 12, "processImageWithFaceBlur"], [131, 38, 133, 36], [131, 39, 133, 37, "photo"], [131, 44, 133, 42], [131, 45, 133, 43, "uri"], [131, 48, 133, 46], [131, 49, 133, 47], [132, 8, 134, 6, "console"], [132, 15, 134, 13], [132, 16, 134, 14, "log"], [132, 19, 134, 17], [132, 20, 134, 18], [132, 71, 134, 69], [132, 72, 134, 70], [133, 6, 135, 4], [133, 7, 135, 5], [133, 8, 135, 6], [133, 15, 135, 13, "error"], [133, 20, 135, 18], [133, 22, 135, 20], [134, 8, 136, 6, "console"], [134, 15, 136, 13], [134, 16, 136, 14, "error"], [134, 21, 136, 19], [134, 22, 136, 20], [134, 54, 136, 52], [134, 56, 136, 54, "error"], [134, 61, 136, 59], [134, 62, 136, 60], [135, 8, 137, 6, "setErrorMessage"], [135, 23, 137, 21], [135, 24, 137, 22], [135, 68, 137, 66], [135, 69, 137, 67], [136, 8, 138, 6, "setProcessingState"], [136, 26, 138, 24], [136, 27, 138, 25], [136, 34, 138, 32], [136, 35, 138, 33], [137, 6, 139, 4], [138, 4, 140, 2], [138, 5, 140, 3], [138, 7, 140, 5], [138, 9, 140, 7], [138, 10, 140, 8], [139, 4, 141, 2], [140, 4, 142, 2], [140, 10, 142, 8, "processImageWithFaceBlur"], [140, 34, 142, 32], [140, 37, 142, 35], [140, 43, 142, 42, "photoUri"], [140, 51, 142, 58], [140, 55, 142, 63], [141, 6, 143, 4], [141, 10, 143, 8], [142, 8, 144, 6, "setProcessingState"], [142, 26, 144, 24], [142, 27, 144, 25], [142, 39, 144, 37], [142, 40, 144, 38], [143, 8, 145, 6, "setProcessingProgress"], [143, 29, 145, 27], [143, 30, 145, 28], [143, 32, 145, 30], [143, 33, 145, 31], [145, 8, 147, 6], [146, 8, 148, 6], [146, 14, 148, 12, "canvas"], [146, 20, 148, 18], [146, 23, 148, 21, "document"], [146, 31, 148, 29], [146, 32, 148, 30, "createElement"], [146, 45, 148, 43], [146, 46, 148, 44], [146, 54, 148, 52], [146, 55, 148, 53], [147, 8, 149, 6], [147, 14, 149, 12, "ctx"], [147, 17, 149, 15], [147, 20, 149, 18, "canvas"], [147, 26, 149, 24], [147, 27, 149, 25, "getContext"], [147, 37, 149, 35], [147, 38, 149, 36], [147, 42, 149, 40], [147, 43, 149, 41], [148, 8, 150, 6], [148, 12, 150, 10], [148, 13, 150, 11, "ctx"], [148, 16, 150, 14], [148, 18, 150, 16], [148, 24, 150, 22], [148, 28, 150, 26, "Error"], [148, 33, 150, 31], [148, 34, 150, 32], [148, 64, 150, 62], [148, 65, 150, 63], [150, 8, 152, 6], [151, 8, 153, 6], [151, 14, 153, 12, "img"], [151, 17, 153, 15], [151, 20, 153, 18], [151, 24, 153, 22, "Image"], [151, 29, 153, 27], [151, 30, 153, 28], [151, 31, 153, 29], [152, 8, 154, 6], [152, 14, 154, 12], [152, 18, 154, 16, "Promise"], [152, 25, 154, 23], [152, 26, 154, 24], [152, 27, 154, 25, "resolve"], [152, 34, 154, 32], [152, 36, 154, 34, "reject"], [152, 42, 154, 40], [152, 47, 154, 45], [153, 10, 155, 8, "img"], [153, 13, 155, 11], [153, 14, 155, 12, "onload"], [153, 20, 155, 18], [153, 23, 155, 21, "resolve"], [153, 30, 155, 28], [154, 10, 156, 8, "img"], [154, 13, 156, 11], [154, 14, 156, 12, "onerror"], [154, 21, 156, 19], [154, 24, 156, 22, "reject"], [154, 30, 156, 28], [155, 10, 157, 8, "img"], [155, 13, 157, 11], [155, 14, 157, 12, "src"], [155, 17, 157, 15], [155, 20, 157, 18, "photoUri"], [155, 28, 157, 26], [156, 8, 158, 6], [156, 9, 158, 7], [156, 10, 158, 8], [158, 8, 160, 6], [159, 8, 161, 6, "canvas"], [159, 14, 161, 12], [159, 15, 161, 13, "width"], [159, 20, 161, 18], [159, 23, 161, 21, "img"], [159, 26, 161, 24], [159, 27, 161, 25, "width"], [159, 32, 161, 30], [160, 8, 162, 6, "canvas"], [160, 14, 162, 12], [160, 15, 162, 13, "height"], [160, 21, 162, 19], [160, 24, 162, 22, "img"], [160, 27, 162, 25], [160, 28, 162, 26, "height"], [160, 34, 162, 32], [162, 8, 164, 6], [163, 8, 165, 6, "ctx"], [163, 11, 165, 9], [163, 12, 165, 10, "drawImage"], [163, 21, 165, 19], [163, 22, 165, 20, "img"], [163, 25, 165, 23], [163, 27, 165, 25], [163, 28, 165, 26], [163, 30, 165, 28], [163, 31, 165, 29], [163, 32, 165, 30], [164, 8, 167, 6, "setProcessingProgress"], [164, 29, 167, 27], [164, 30, 167, 28], [164, 32, 167, 30], [164, 33, 167, 31], [166, 8, 169, 6], [167, 8, 170, 6], [167, 12, 170, 10, "detectedFaces"], [167, 25, 170, 23], [167, 28, 170, 26], [167, 30, 170, 28], [168, 8, 172, 6, "console"], [168, 15, 172, 13], [168, 16, 172, 14, "log"], [168, 19, 172, 17], [168, 20, 172, 18], [168, 74, 172, 72], [168, 76, 172, 74], [169, 10, 173, 8, "width"], [169, 15, 173, 13], [169, 17, 173, 15, "img"], [169, 20, 173, 18], [169, 21, 173, 19, "width"], [169, 26, 173, 24], [170, 10, 174, 8, "height"], [170, 16, 174, 14], [170, 18, 174, 16, "img"], [170, 21, 174, 19], [170, 22, 174, 20, "height"], [170, 28, 174, 26], [171, 10, 175, 8, "src"], [171, 13, 175, 11], [171, 15, 175, 13, "photoUri"], [171, 23, 175, 21], [171, 24, 175, 22, "substring"], [171, 33, 175, 31], [171, 34, 175, 32], [171, 35, 175, 33], [171, 37, 175, 35], [171, 39, 175, 37], [171, 40, 175, 38], [171, 43, 175, 41], [172, 8, 176, 6], [172, 9, 176, 7], [172, 10, 176, 8], [174, 8, 178, 6], [175, 8, 179, 6], [175, 12, 179, 10], [176, 10, 180, 8], [176, 14, 180, 12], [176, 28, 180, 26], [176, 32, 180, 30, "window"], [176, 38, 180, 36], [176, 40, 180, 38], [177, 12, 181, 10, "console"], [177, 19, 181, 17], [177, 20, 181, 18, "log"], [177, 23, 181, 21], [177, 24, 181, 22], [177, 105, 181, 103], [177, 106, 181, 104], [178, 12, 182, 10], [178, 18, 182, 16, "faceDetector"], [178, 30, 182, 28], [178, 33, 182, 31], [178, 37, 182, 36, "window"], [178, 43, 182, 42], [178, 44, 182, 51, "FaceDetector"], [178, 56, 182, 63], [178, 57, 182, 64], [179, 14, 183, 12, "maxDetectedFaces"], [179, 30, 183, 28], [179, 32, 183, 30], [179, 34, 183, 32], [180, 14, 184, 12, "fastMode"], [180, 22, 184, 20], [180, 24, 184, 22], [181, 12, 185, 10], [181, 13, 185, 11], [181, 14, 185, 12], [182, 12, 187, 10], [182, 18, 187, 16, "browserDetections"], [182, 35, 187, 33], [182, 38, 187, 36], [182, 44, 187, 42, "faceDetector"], [182, 56, 187, 54], [182, 57, 187, 55, "detect"], [182, 63, 187, 61], [182, 64, 187, 62, "img"], [182, 67, 187, 65], [182, 68, 187, 66], [183, 12, 188, 10, "detectedFaces"], [183, 25, 188, 23], [183, 28, 188, 26, "browserDetections"], [183, 45, 188, 43], [183, 46, 188, 44, "map"], [183, 49, 188, 47], [183, 50, 188, 49, "detection"], [183, 59, 188, 63], [183, 64, 188, 69], [184, 14, 189, 12, "boundingBox"], [184, 25, 189, 23], [184, 27, 189, 25], [185, 16, 190, 14, "xCenter"], [185, 23, 190, 21], [185, 25, 190, 23], [185, 26, 190, 24, "detection"], [185, 35, 190, 33], [185, 36, 190, 34, "boundingBox"], [185, 47, 190, 45], [185, 48, 190, 46, "x"], [185, 49, 190, 47], [185, 52, 190, 50, "detection"], [185, 61, 190, 59], [185, 62, 190, 60, "boundingBox"], [185, 73, 190, 71], [185, 74, 190, 72, "width"], [185, 79, 190, 77], [185, 82, 190, 80], [185, 83, 190, 81], [185, 87, 190, 85, "img"], [185, 90, 190, 88], [185, 91, 190, 89, "width"], [185, 96, 190, 94], [186, 16, 191, 14, "yCenter"], [186, 23, 191, 21], [186, 25, 191, 23], [186, 26, 191, 24, "detection"], [186, 35, 191, 33], [186, 36, 191, 34, "boundingBox"], [186, 47, 191, 45], [186, 48, 191, 46, "y"], [186, 49, 191, 47], [186, 52, 191, 50, "detection"], [186, 61, 191, 59], [186, 62, 191, 60, "boundingBox"], [186, 73, 191, 71], [186, 74, 191, 72, "height"], [186, 80, 191, 78], [186, 83, 191, 81], [186, 84, 191, 82], [186, 88, 191, 86, "img"], [186, 91, 191, 89], [186, 92, 191, 90, "height"], [186, 98, 191, 96], [187, 16, 192, 14, "width"], [187, 21, 192, 19], [187, 23, 192, 21, "detection"], [187, 32, 192, 30], [187, 33, 192, 31, "boundingBox"], [187, 44, 192, 42], [187, 45, 192, 43, "width"], [187, 50, 192, 48], [187, 53, 192, 51, "img"], [187, 56, 192, 54], [187, 57, 192, 55, "width"], [187, 62, 192, 60], [188, 16, 193, 14, "height"], [188, 22, 193, 20], [188, 24, 193, 22, "detection"], [188, 33, 193, 31], [188, 34, 193, 32, "boundingBox"], [188, 45, 193, 43], [188, 46, 193, 44, "height"], [188, 52, 193, 50], [188, 55, 193, 53, "img"], [188, 58, 193, 56], [188, 59, 193, 57, "height"], [189, 14, 194, 12], [190, 12, 195, 10], [190, 13, 195, 11], [190, 14, 195, 12], [190, 15, 195, 13], [191, 12, 196, 10, "console"], [191, 19, 196, 17], [191, 20, 196, 18, "log"], [191, 23, 196, 21], [191, 24, 196, 22], [191, 78, 196, 76, "detectedFaces"], [191, 91, 196, 89], [191, 92, 196, 90, "length"], [191, 98, 196, 96], [191, 106, 196, 104], [191, 107, 196, 105], [192, 10, 197, 8], [192, 11, 197, 9], [192, 17, 197, 15], [193, 12, 198, 10, "console"], [193, 19, 198, 17], [193, 20, 198, 18, "log"], [193, 23, 198, 21], [193, 24, 198, 22], [193, 100, 198, 98], [193, 101, 198, 99], [194, 12, 199, 10], [194, 18, 199, 16], [194, 22, 199, 20, "Error"], [194, 27, 199, 25], [194, 28, 199, 26], [194, 70, 199, 68], [194, 71, 199, 69], [195, 10, 200, 8], [196, 8, 201, 6], [196, 9, 201, 7], [196, 10, 201, 8], [196, 17, 201, 15, "browserError"], [196, 29, 201, 27], [196, 31, 201, 29], [197, 10, 202, 8, "console"], [197, 17, 202, 15], [197, 18, 202, 16, "warn"], [197, 22, 202, 20], [197, 23, 202, 21], [197, 102, 202, 100], [197, 104, 202, 102, "browserError"], [197, 116, 202, 114], [197, 117, 202, 115], [199, 10, 204, 8], [200, 10, 205, 8], [200, 14, 205, 12], [201, 12, 206, 10], [202, 12, 207, 10], [202, 16, 207, 14], [202, 17, 207, 16, "window"], [202, 23, 207, 22], [202, 24, 207, 31, "<PERSON>ap<PERSON>"], [202, 31, 207, 38], [202, 33, 207, 40], [203, 14, 208, 12], [203, 20, 208, 18], [203, 24, 208, 22, "Promise"], [203, 31, 208, 29], [203, 32, 208, 30], [203, 33, 208, 31, "resolve"], [203, 40, 208, 38], [203, 42, 208, 40, "reject"], [203, 48, 208, 46], [203, 53, 208, 51], [204, 16, 209, 14], [204, 22, 209, 20, "script"], [204, 28, 209, 26], [204, 31, 209, 29, "document"], [204, 39, 209, 37], [204, 40, 209, 38, "createElement"], [204, 53, 209, 51], [204, 54, 209, 52], [204, 62, 209, 60], [204, 63, 209, 61], [205, 16, 210, 14, "script"], [205, 22, 210, 20], [205, 23, 210, 21, "src"], [205, 26, 210, 24], [205, 29, 210, 27], [205, 99, 210, 97], [206, 16, 211, 14, "script"], [206, 22, 211, 20], [206, 23, 211, 21, "onload"], [206, 29, 211, 27], [206, 32, 211, 30, "resolve"], [206, 39, 211, 37], [207, 16, 212, 14, "script"], [207, 22, 212, 20], [207, 23, 212, 21, "onerror"], [207, 30, 212, 28], [207, 33, 212, 31, "reject"], [207, 39, 212, 37], [208, 16, 213, 14, "document"], [208, 24, 213, 22], [208, 25, 213, 23, "head"], [208, 29, 213, 27], [208, 30, 213, 28, "append<PERSON><PERSON><PERSON>"], [208, 41, 213, 39], [208, 42, 213, 40, "script"], [208, 48, 213, 46], [208, 49, 213, 47], [209, 14, 214, 12], [209, 15, 214, 13], [209, 16, 214, 14], [210, 12, 215, 10], [211, 12, 217, 10], [211, 18, 217, 16, "<PERSON>ap<PERSON>"], [211, 25, 217, 23], [211, 28, 217, 27, "window"], [211, 34, 217, 33], [211, 35, 217, 42, "<PERSON>ap<PERSON>"], [211, 42, 217, 49], [213, 12, 219, 10], [214, 12, 220, 10], [214, 18, 220, 16, "Promise"], [214, 25, 220, 23], [214, 26, 220, 24, "all"], [214, 29, 220, 27], [214, 30, 220, 28], [214, 31, 221, 12, "<PERSON>ap<PERSON>"], [214, 38, 221, 19], [214, 39, 221, 20, "nets"], [214, 43, 221, 24], [214, 44, 221, 25, "tinyFaceDetector"], [214, 60, 221, 41], [214, 61, 221, 42, "loadFromUri"], [214, 72, 221, 53], [214, 73, 221, 54], [214, 130, 221, 111], [214, 131, 221, 112], [214, 133, 222, 12, "<PERSON>ap<PERSON>"], [214, 140, 222, 19], [214, 141, 222, 20, "nets"], [214, 145, 222, 24], [214, 146, 222, 25, "faceLandmark68Net"], [214, 163, 222, 42], [214, 164, 222, 43, "loadFromUri"], [214, 175, 222, 54], [214, 176, 222, 55], [214, 233, 222, 112], [214, 234, 222, 113], [214, 235, 223, 11], [214, 236, 223, 12], [216, 12, 225, 10], [217, 12, 226, 10], [217, 18, 226, 16, "faceDetections"], [217, 32, 226, 30], [217, 35, 226, 33], [217, 41, 226, 39, "<PERSON>ap<PERSON>"], [217, 48, 226, 46], [217, 49, 226, 47, "detectAllFaces"], [217, 63, 226, 61], [217, 64, 226, 62, "img"], [217, 67, 226, 65], [217, 69, 226, 67], [217, 73, 226, 71, "<PERSON>ap<PERSON>"], [217, 80, 226, 78], [217, 81, 226, 79, "TinyFaceDetectorOptions"], [217, 104, 226, 102], [217, 105, 226, 103], [217, 106, 226, 104], [217, 107, 226, 105], [218, 12, 228, 10, "detectedFaces"], [218, 25, 228, 23], [218, 28, 228, 26, "faceDetections"], [218, 42, 228, 40], [218, 43, 228, 41, "map"], [218, 46, 228, 44], [218, 47, 228, 46, "detection"], [218, 56, 228, 60], [218, 61, 228, 66], [219, 14, 229, 12, "boundingBox"], [219, 25, 229, 23], [219, 27, 229, 25], [220, 16, 230, 14, "xCenter"], [220, 23, 230, 21], [220, 25, 230, 23], [220, 26, 230, 24, "detection"], [220, 35, 230, 33], [220, 36, 230, 34, "box"], [220, 39, 230, 37], [220, 40, 230, 38, "x"], [220, 41, 230, 39], [220, 44, 230, 42, "detection"], [220, 53, 230, 51], [220, 54, 230, 52, "box"], [220, 57, 230, 55], [220, 58, 230, 56, "width"], [220, 63, 230, 61], [220, 66, 230, 64], [220, 67, 230, 65], [220, 71, 230, 69, "img"], [220, 74, 230, 72], [220, 75, 230, 73, "width"], [220, 80, 230, 78], [221, 16, 231, 14, "yCenter"], [221, 23, 231, 21], [221, 25, 231, 23], [221, 26, 231, 24, "detection"], [221, 35, 231, 33], [221, 36, 231, 34, "box"], [221, 39, 231, 37], [221, 40, 231, 38, "y"], [221, 41, 231, 39], [221, 44, 231, 42, "detection"], [221, 53, 231, 51], [221, 54, 231, 52, "box"], [221, 57, 231, 55], [221, 58, 231, 56, "height"], [221, 64, 231, 62], [221, 67, 231, 65], [221, 68, 231, 66], [221, 72, 231, 70, "img"], [221, 75, 231, 73], [221, 76, 231, 74, "height"], [221, 82, 231, 80], [222, 16, 232, 14, "width"], [222, 21, 232, 19], [222, 23, 232, 21, "detection"], [222, 32, 232, 30], [222, 33, 232, 31, "box"], [222, 36, 232, 34], [222, 37, 232, 35, "width"], [222, 42, 232, 40], [222, 45, 232, 43, "img"], [222, 48, 232, 46], [222, 49, 232, 47, "width"], [222, 54, 232, 52], [223, 16, 233, 14, "height"], [223, 22, 233, 20], [223, 24, 233, 22, "detection"], [223, 33, 233, 31], [223, 34, 233, 32, "box"], [223, 37, 233, 35], [223, 38, 233, 36, "height"], [223, 44, 233, 42], [223, 47, 233, 45, "img"], [223, 50, 233, 48], [223, 51, 233, 49, "height"], [224, 14, 234, 12], [225, 12, 235, 10], [225, 13, 235, 11], [225, 14, 235, 12], [225, 15, 235, 13], [226, 12, 237, 10, "console"], [226, 19, 237, 17], [226, 20, 237, 18, "log"], [226, 23, 237, 21], [226, 24, 237, 22], [226, 63, 237, 61, "detectedFaces"], [226, 76, 237, 74], [226, 77, 237, 75, "length"], [226, 83, 237, 81], [226, 91, 237, 89], [226, 92, 237, 90], [227, 10, 238, 8], [227, 11, 238, 9], [227, 12, 238, 10], [227, 19, 238, 17, "faceApiError"], [227, 31, 238, 29], [227, 33, 238, 31], [228, 12, 239, 10, "console"], [228, 19, 239, 17], [228, 20, 239, 18, "warn"], [228, 24, 239, 22], [228, 25, 239, 23], [228, 69, 239, 67], [228, 71, 239, 69, "faceApiError"], [228, 83, 239, 81], [228, 84, 239, 82], [230, 12, 241, 10], [231, 12, 242, 10, "console"], [231, 19, 242, 17], [231, 20, 242, 18, "log"], [231, 23, 242, 21], [231, 24, 242, 22], [231, 94, 242, 92], [231, 95, 242, 93], [232, 12, 243, 10, "detectedFaces"], [232, 25, 243, 23], [232, 28, 243, 26], [232, 29, 243, 27], [233, 14, 244, 12, "boundingBox"], [233, 25, 244, 23], [233, 27, 244, 25], [234, 16, 245, 14, "xCenter"], [234, 23, 245, 21], [234, 25, 245, 23], [234, 28, 245, 26], [235, 16, 245, 29], [236, 16, 246, 14, "yCenter"], [236, 23, 246, 21], [236, 25, 246, 23], [236, 28, 246, 26], [237, 16, 246, 29], [238, 16, 247, 14, "width"], [238, 21, 247, 19], [238, 23, 247, 21], [238, 26, 247, 24], [239, 16, 247, 29], [240, 16, 248, 14, "height"], [240, 22, 248, 20], [240, 24, 248, 22], [240, 27, 248, 25], [240, 28, 248, 29], [241, 14, 249, 12], [242, 12, 250, 10], [242, 13, 250, 11], [242, 14, 250, 12], [243, 12, 251, 10, "console"], [243, 19, 251, 17], [243, 20, 251, 18, "log"], [243, 23, 251, 21], [243, 24, 251, 22], [243, 93, 251, 91], [243, 94, 251, 92], [244, 10, 252, 8], [245, 8, 253, 6], [246, 8, 255, 6, "console"], [246, 15, 255, 13], [246, 16, 255, 14, "log"], [246, 19, 255, 17], [246, 20, 255, 18], [246, 72, 255, 70, "detectedFaces"], [246, 85, 255, 83], [246, 86, 255, 84, "length"], [246, 92, 255, 90], [246, 100, 255, 98], [246, 101, 255, 99], [247, 8, 256, 6], [247, 12, 256, 10, "detectedFaces"], [247, 25, 256, 23], [247, 26, 256, 24, "length"], [247, 32, 256, 30], [247, 35, 256, 33], [247, 36, 256, 34], [247, 38, 256, 36], [248, 10, 257, 8, "console"], [248, 17, 257, 15], [248, 18, 257, 16, "log"], [248, 21, 257, 19], [248, 22, 257, 20], [248, 66, 257, 64], [248, 68, 257, 66, "detectedFaces"], [248, 81, 257, 79], [248, 82, 257, 80, "map"], [248, 85, 257, 83], [248, 86, 257, 84], [248, 87, 257, 85, "face"], [248, 91, 257, 89], [248, 93, 257, 91, "i"], [248, 94, 257, 92], [248, 100, 257, 98], [249, 12, 258, 10, "faceNumber"], [249, 22, 258, 20], [249, 24, 258, 22, "i"], [249, 25, 258, 23], [249, 28, 258, 26], [249, 29, 258, 27], [250, 12, 259, 10, "centerX"], [250, 19, 259, 17], [250, 21, 259, 19, "face"], [250, 25, 259, 23], [250, 26, 259, 24, "boundingBox"], [250, 37, 259, 35], [250, 38, 259, 36, "xCenter"], [250, 45, 259, 43], [251, 12, 260, 10, "centerY"], [251, 19, 260, 17], [251, 21, 260, 19, "face"], [251, 25, 260, 23], [251, 26, 260, 24, "boundingBox"], [251, 37, 260, 35], [251, 38, 260, 36, "yCenter"], [251, 45, 260, 43], [252, 12, 261, 10, "width"], [252, 17, 261, 15], [252, 19, 261, 17, "face"], [252, 23, 261, 21], [252, 24, 261, 22, "boundingBox"], [252, 35, 261, 33], [252, 36, 261, 34, "width"], [252, 41, 261, 39], [253, 12, 262, 10, "height"], [253, 18, 262, 16], [253, 20, 262, 18, "face"], [253, 24, 262, 22], [253, 25, 262, 23, "boundingBox"], [253, 36, 262, 34], [253, 37, 262, 35, "height"], [254, 10, 263, 8], [254, 11, 263, 9], [254, 12, 263, 10], [254, 13, 263, 11], [254, 14, 263, 12], [255, 8, 264, 6], [255, 9, 264, 7], [255, 15, 264, 13], [256, 10, 265, 8, "console"], [256, 17, 265, 15], [256, 18, 265, 16, "log"], [256, 21, 265, 19], [256, 22, 265, 20], [256, 91, 265, 89], [256, 92, 265, 90], [257, 8, 266, 6], [258, 8, 268, 6, "setProcessingProgress"], [258, 29, 268, 27], [258, 30, 268, 28], [258, 32, 268, 30], [258, 33, 268, 31], [260, 8, 270, 6], [261, 8, 271, 6], [261, 12, 271, 10, "detectedFaces"], [261, 25, 271, 23], [261, 26, 271, 24, "length"], [261, 32, 271, 30], [261, 35, 271, 33], [261, 36, 271, 34], [261, 38, 271, 36], [262, 10, 272, 8, "detectedFaces"], [262, 23, 272, 21], [262, 24, 272, 22, "for<PERSON>ach"], [262, 31, 272, 29], [262, 32, 272, 30], [262, 33, 272, 31, "detection"], [262, 42, 272, 40], [262, 44, 272, 42, "index"], [262, 49, 272, 47], [262, 54, 272, 52], [263, 12, 273, 10], [263, 18, 273, 16, "bbox"], [263, 22, 273, 20], [263, 25, 273, 23, "detection"], [263, 34, 273, 32], [263, 35, 273, 33, "boundingBox"], [263, 46, 273, 44], [265, 12, 275, 10], [266, 12, 276, 10], [266, 18, 276, 16, "faceX"], [266, 23, 276, 21], [266, 26, 276, 24, "bbox"], [266, 30, 276, 28], [266, 31, 276, 29, "xCenter"], [266, 38, 276, 36], [266, 41, 276, 39, "img"], [266, 44, 276, 42], [266, 45, 276, 43, "width"], [266, 50, 276, 48], [266, 53, 276, 52, "bbox"], [266, 57, 276, 56], [266, 58, 276, 57, "width"], [266, 63, 276, 62], [266, 66, 276, 65, "img"], [266, 69, 276, 68], [266, 70, 276, 69, "width"], [266, 75, 276, 74], [266, 78, 276, 78], [266, 79, 276, 79], [267, 12, 277, 10], [267, 18, 277, 16, "faceY"], [267, 23, 277, 21], [267, 26, 277, 24, "bbox"], [267, 30, 277, 28], [267, 31, 277, 29, "yCenter"], [267, 38, 277, 36], [267, 41, 277, 39, "img"], [267, 44, 277, 42], [267, 45, 277, 43, "height"], [267, 51, 277, 49], [267, 54, 277, 53, "bbox"], [267, 58, 277, 57], [267, 59, 277, 58, "height"], [267, 65, 277, 64], [267, 68, 277, 67, "img"], [267, 71, 277, 70], [267, 72, 277, 71, "height"], [267, 78, 277, 77], [267, 81, 277, 81], [267, 82, 277, 82], [268, 12, 278, 10], [268, 18, 278, 16, "faceWidth"], [268, 27, 278, 25], [268, 30, 278, 28, "bbox"], [268, 34, 278, 32], [268, 35, 278, 33, "width"], [268, 40, 278, 38], [268, 43, 278, 41, "img"], [268, 46, 278, 44], [268, 47, 278, 45, "width"], [268, 52, 278, 50], [269, 12, 279, 10], [269, 18, 279, 16, "faceHeight"], [269, 28, 279, 26], [269, 31, 279, 29, "bbox"], [269, 35, 279, 33], [269, 36, 279, 34, "height"], [269, 42, 279, 40], [269, 45, 279, 43, "img"], [269, 48, 279, 46], [269, 49, 279, 47, "height"], [269, 55, 279, 53], [271, 12, 281, 10], [272, 12, 282, 10], [272, 18, 282, 16, "padding"], [272, 25, 282, 23], [272, 28, 282, 26], [272, 31, 282, 29], [272, 32, 282, 30], [272, 33, 282, 31], [273, 12, 283, 10], [273, 18, 283, 16, "paddedX"], [273, 25, 283, 23], [273, 28, 283, 26, "Math"], [273, 32, 283, 30], [273, 33, 283, 31, "max"], [273, 36, 283, 34], [273, 37, 283, 35], [273, 38, 283, 36], [273, 40, 283, 38, "faceX"], [273, 45, 283, 43], [273, 48, 283, 46, "faceWidth"], [273, 57, 283, 55], [273, 60, 283, 58, "padding"], [273, 67, 283, 65], [273, 68, 283, 66], [274, 12, 284, 10], [274, 18, 284, 16, "paddedY"], [274, 25, 284, 23], [274, 28, 284, 26, "Math"], [274, 32, 284, 30], [274, 33, 284, 31, "max"], [274, 36, 284, 34], [274, 37, 284, 35], [274, 38, 284, 36], [274, 40, 284, 38, "faceY"], [274, 45, 284, 43], [274, 48, 284, 46, "faceHeight"], [274, 58, 284, 56], [274, 61, 284, 59, "padding"], [274, 68, 284, 66], [274, 69, 284, 67], [275, 12, 285, 10], [275, 18, 285, 16, "<PERSON><PERSON><PERSON><PERSON>"], [275, 29, 285, 27], [275, 32, 285, 30, "Math"], [275, 36, 285, 34], [275, 37, 285, 35, "min"], [275, 40, 285, 38], [275, 41, 285, 39, "img"], [275, 44, 285, 42], [275, 45, 285, 43, "width"], [275, 50, 285, 48], [275, 53, 285, 51, "paddedX"], [275, 60, 285, 58], [275, 62, 285, 60, "faceWidth"], [275, 71, 285, 69], [275, 75, 285, 73], [275, 76, 285, 74], [275, 79, 285, 77], [275, 80, 285, 78], [275, 83, 285, 81, "padding"], [275, 90, 285, 88], [275, 91, 285, 89], [275, 92, 285, 90], [276, 12, 286, 10], [276, 18, 286, 16, "paddedHeight"], [276, 30, 286, 28], [276, 33, 286, 31, "Math"], [276, 37, 286, 35], [276, 38, 286, 36, "min"], [276, 41, 286, 39], [276, 42, 286, 40, "img"], [276, 45, 286, 43], [276, 46, 286, 44, "height"], [276, 52, 286, 50], [276, 55, 286, 53, "paddedY"], [276, 62, 286, 60], [276, 64, 286, 62, "faceHeight"], [276, 74, 286, 72], [276, 78, 286, 76], [276, 79, 286, 77], [276, 82, 286, 80], [276, 83, 286, 81], [276, 86, 286, 84, "padding"], [276, 93, 286, 91], [276, 94, 286, 92], [276, 95, 286, 93], [277, 12, 288, 10, "console"], [277, 19, 288, 17], [277, 20, 288, 18, "log"], [277, 23, 288, 21], [277, 24, 288, 22], [277, 60, 288, 58, "index"], [277, 65, 288, 63], [277, 68, 288, 66], [277, 69, 288, 67], [277, 77, 288, 75, "Math"], [277, 81, 288, 79], [277, 82, 288, 80, "round"], [277, 87, 288, 85], [277, 88, 288, 86, "paddedX"], [277, 95, 288, 93], [277, 96, 288, 94], [277, 101, 288, 99, "Math"], [277, 105, 288, 103], [277, 106, 288, 104, "round"], [277, 111, 288, 109], [277, 112, 288, 110, "paddedY"], [277, 119, 288, 117], [277, 120, 288, 118], [277, 130, 288, 128, "Math"], [277, 134, 288, 132], [277, 135, 288, 133, "round"], [277, 140, 288, 138], [277, 141, 288, 139, "<PERSON><PERSON><PERSON><PERSON>"], [277, 152, 288, 150], [277, 153, 288, 151], [277, 157, 288, 155, "Math"], [277, 161, 288, 159], [277, 162, 288, 160, "round"], [277, 167, 288, 165], [277, 168, 288, 166, "paddedHeight"], [277, 180, 288, 178], [277, 181, 288, 179], [277, 183, 288, 181], [277, 184, 288, 182], [279, 12, 290, 10], [280, 12, 291, 10], [280, 18, 291, 16, "faceImageData"], [280, 31, 291, 29], [280, 34, 291, 32, "ctx"], [280, 37, 291, 35], [280, 38, 291, 36, "getImageData"], [280, 50, 291, 48], [280, 51, 291, 49, "paddedX"], [280, 58, 291, 56], [280, 60, 291, 58, "paddedY"], [280, 67, 291, 65], [280, 69, 291, 67, "<PERSON><PERSON><PERSON><PERSON>"], [280, 80, 291, 78], [280, 82, 291, 80, "paddedHeight"], [280, 94, 291, 92], [280, 95, 291, 93], [281, 12, 292, 10], [281, 18, 292, 16, "data"], [281, 22, 292, 20], [281, 25, 292, 23, "faceImageData"], [281, 38, 292, 36], [281, 39, 292, 37, "data"], [281, 43, 292, 41], [282, 12, 294, 10, "console"], [282, 19, 294, 17], [282, 20, 294, 18, "log"], [282, 23, 294, 21], [282, 24, 294, 22], [282, 64, 294, 62, "data"], [282, 68, 294, 66], [282, 69, 294, 67, "length"], [282, 75, 294, 73], [282, 86, 294, 84, "<PERSON><PERSON><PERSON><PERSON>"], [282, 97, 294, 95], [282, 101, 294, 99, "paddedHeight"], [282, 113, 294, 111], [282, 122, 294, 120], [282, 123, 294, 121], [284, 12, 296, 10], [285, 12, 297, 10], [285, 18, 297, 16, "pixelSize"], [285, 27, 297, 25], [285, 30, 297, 28, "Math"], [285, 34, 297, 32], [285, 35, 297, 33, "max"], [285, 38, 297, 36], [285, 39, 297, 37], [285, 41, 297, 39], [285, 43, 297, 41, "Math"], [285, 47, 297, 45], [285, 48, 297, 46, "min"], [285, 51, 297, 49], [285, 52, 297, 50, "<PERSON><PERSON><PERSON><PERSON>"], [285, 63, 297, 61], [285, 65, 297, 63, "paddedHeight"], [285, 77, 297, 75], [285, 78, 297, 76], [285, 81, 297, 79], [285, 83, 297, 81], [285, 84, 297, 82], [285, 85, 297, 83], [285, 86, 297, 84], [286, 12, 298, 10, "console"], [286, 19, 298, 17], [286, 20, 298, 18, "log"], [286, 23, 298, 21], [286, 24, 298, 22], [286, 64, 298, 62, "pixelSize"], [286, 73, 298, 71], [286, 90, 298, 88], [286, 91, 298, 89], [287, 12, 299, 10], [287, 17, 299, 15], [287, 21, 299, 19, "y"], [287, 22, 299, 20], [287, 25, 299, 23], [287, 26, 299, 24], [287, 28, 299, 26, "y"], [287, 29, 299, 27], [287, 32, 299, 30, "paddedHeight"], [287, 44, 299, 42], [287, 46, 299, 44, "y"], [287, 47, 299, 45], [287, 51, 299, 49, "pixelSize"], [287, 60, 299, 58], [287, 62, 299, 60], [288, 14, 300, 12], [288, 19, 300, 17], [288, 23, 300, 21, "x"], [288, 24, 300, 22], [288, 27, 300, 25], [288, 28, 300, 26], [288, 30, 300, 28, "x"], [288, 31, 300, 29], [288, 34, 300, 32, "<PERSON><PERSON><PERSON><PERSON>"], [288, 45, 300, 43], [288, 47, 300, 45, "x"], [288, 48, 300, 46], [288, 52, 300, 50, "pixelSize"], [288, 61, 300, 59], [288, 63, 300, 61], [289, 16, 301, 14], [290, 16, 302, 14], [290, 22, 302, 20, "pixelIndex"], [290, 32, 302, 30], [290, 35, 302, 33], [290, 36, 302, 34, "y"], [290, 37, 302, 35], [290, 40, 302, 38, "<PERSON><PERSON><PERSON><PERSON>"], [290, 51, 302, 49], [290, 54, 302, 52, "x"], [290, 55, 302, 53], [290, 59, 302, 57], [290, 60, 302, 58], [291, 16, 303, 14], [291, 22, 303, 20, "r"], [291, 23, 303, 21], [291, 26, 303, 24, "data"], [291, 30, 303, 28], [291, 31, 303, 29, "pixelIndex"], [291, 41, 303, 39], [291, 42, 303, 40], [292, 16, 304, 14], [292, 22, 304, 20, "g"], [292, 23, 304, 21], [292, 26, 304, 24, "data"], [292, 30, 304, 28], [292, 31, 304, 29, "pixelIndex"], [292, 41, 304, 39], [292, 44, 304, 42], [292, 45, 304, 43], [292, 46, 304, 44], [293, 16, 305, 14], [293, 22, 305, 20, "b"], [293, 23, 305, 21], [293, 26, 305, 24, "data"], [293, 30, 305, 28], [293, 31, 305, 29, "pixelIndex"], [293, 41, 305, 39], [293, 44, 305, 42], [293, 45, 305, 43], [293, 46, 305, 44], [294, 16, 306, 14], [294, 22, 306, 20, "a"], [294, 23, 306, 21], [294, 26, 306, 24, "data"], [294, 30, 306, 28], [294, 31, 306, 29, "pixelIndex"], [294, 41, 306, 39], [294, 44, 306, 42], [294, 45, 306, 43], [294, 46, 306, 44], [296, 16, 308, 14], [297, 16, 309, 14], [297, 21, 309, 19], [297, 25, 309, 23, "dy"], [297, 27, 309, 25], [297, 30, 309, 28], [297, 31, 309, 29], [297, 33, 309, 31, "dy"], [297, 35, 309, 33], [297, 38, 309, 36, "pixelSize"], [297, 47, 309, 45], [297, 51, 309, 49, "y"], [297, 52, 309, 50], [297, 55, 309, 53, "dy"], [297, 57, 309, 55], [297, 60, 309, 58, "paddedHeight"], [297, 72, 309, 70], [297, 74, 309, 72, "dy"], [297, 76, 309, 74], [297, 78, 309, 76], [297, 80, 309, 78], [298, 18, 310, 16], [298, 23, 310, 21], [298, 27, 310, 25, "dx"], [298, 29, 310, 27], [298, 32, 310, 30], [298, 33, 310, 31], [298, 35, 310, 33, "dx"], [298, 37, 310, 35], [298, 40, 310, 38, "pixelSize"], [298, 49, 310, 47], [298, 53, 310, 51, "x"], [298, 54, 310, 52], [298, 57, 310, 55, "dx"], [298, 59, 310, 57], [298, 62, 310, 60, "<PERSON><PERSON><PERSON><PERSON>"], [298, 73, 310, 71], [298, 75, 310, 73, "dx"], [298, 77, 310, 75], [298, 79, 310, 77], [298, 81, 310, 79], [299, 20, 311, 18], [299, 26, 311, 24, "blockPixelIndex"], [299, 41, 311, 39], [299, 44, 311, 42], [299, 45, 311, 43], [299, 46, 311, 44, "y"], [299, 47, 311, 45], [299, 50, 311, 48, "dy"], [299, 52, 311, 50], [299, 56, 311, 54, "<PERSON><PERSON><PERSON><PERSON>"], [299, 67, 311, 65], [299, 71, 311, 69, "x"], [299, 72, 311, 70], [299, 75, 311, 73, "dx"], [299, 77, 311, 75], [299, 78, 311, 76], [299, 82, 311, 80], [299, 83, 311, 81], [300, 20, 312, 18, "data"], [300, 24, 312, 22], [300, 25, 312, 23, "blockPixelIndex"], [300, 40, 312, 38], [300, 41, 312, 39], [300, 44, 312, 42, "r"], [300, 45, 312, 43], [301, 20, 313, 18, "data"], [301, 24, 313, 22], [301, 25, 313, 23, "blockPixelIndex"], [301, 40, 313, 38], [301, 43, 313, 41], [301, 44, 313, 42], [301, 45, 313, 43], [301, 48, 313, 46, "g"], [301, 49, 313, 47], [302, 20, 314, 18, "data"], [302, 24, 314, 22], [302, 25, 314, 23, "blockPixelIndex"], [302, 40, 314, 38], [302, 43, 314, 41], [302, 44, 314, 42], [302, 45, 314, 43], [302, 48, 314, 46, "b"], [302, 49, 314, 47], [303, 20, 315, 18, "data"], [303, 24, 315, 22], [303, 25, 315, 23, "blockPixelIndex"], [303, 40, 315, 38], [303, 43, 315, 41], [303, 44, 315, 42], [303, 45, 315, 43], [303, 48, 315, 46, "a"], [303, 49, 315, 47], [304, 18, 316, 16], [305, 16, 317, 14], [306, 14, 318, 12], [307, 12, 319, 10], [309, 12, 321, 10], [310, 12, 322, 10, "ctx"], [310, 15, 322, 13], [310, 16, 322, 14, "putImageData"], [310, 28, 322, 26], [310, 29, 322, 27, "faceImageData"], [310, 42, 322, 40], [310, 44, 322, 42, "paddedX"], [310, 51, 322, 49], [310, 53, 322, 51, "paddedY"], [310, 60, 322, 58], [310, 61, 322, 59], [311, 12, 323, 10, "console"], [311, 19, 323, 17], [311, 20, 323, 18, "log"], [311, 23, 323, 21], [311, 24, 323, 22], [311, 50, 323, 48, "index"], [311, 55, 323, 53], [311, 58, 323, 56], [311, 59, 323, 57], [311, 91, 323, 89], [311, 92, 323, 90], [312, 10, 324, 8], [312, 11, 324, 9], [312, 12, 324, 10], [313, 10, 325, 8, "console"], [313, 17, 325, 15], [313, 18, 325, 16, "log"], [313, 21, 325, 19], [313, 22, 325, 20], [313, 48, 325, 46, "detectedFaces"], [313, 61, 325, 59], [313, 62, 325, 60, "length"], [313, 68, 325, 66], [313, 95, 325, 93], [313, 96, 325, 94], [314, 8, 326, 6], [314, 9, 326, 7], [314, 15, 326, 13], [315, 10, 327, 8, "console"], [315, 17, 327, 15], [315, 18, 327, 16, "log"], [315, 21, 327, 19], [315, 22, 327, 20], [315, 91, 327, 89], [315, 92, 327, 90], [316, 8, 328, 6], [317, 8, 330, 6, "setProcessingProgress"], [317, 29, 330, 27], [317, 30, 330, 28], [317, 32, 330, 30], [317, 33, 330, 31], [319, 8, 332, 6], [320, 8, 333, 6, "console"], [320, 15, 333, 13], [320, 16, 333, 14, "log"], [320, 19, 333, 17], [320, 20, 333, 18], [320, 85, 333, 83], [320, 86, 333, 84], [321, 8, 334, 6], [321, 14, 334, 12, "blurredImageBlob"], [321, 30, 334, 28], [321, 33, 334, 31], [321, 39, 334, 37], [321, 43, 334, 41, "Promise"], [321, 50, 334, 48], [321, 51, 334, 56, "resolve"], [321, 58, 334, 63], [321, 62, 334, 68], [322, 10, 335, 8, "canvas"], [322, 16, 335, 14], [322, 17, 335, 15, "toBlob"], [322, 23, 335, 21], [322, 24, 335, 23, "blob"], [322, 28, 335, 27], [322, 32, 335, 32, "resolve"], [322, 39, 335, 39], [322, 40, 335, 40, "blob"], [322, 44, 335, 45], [322, 45, 335, 46], [322, 47, 335, 48], [322, 59, 335, 60], [322, 61, 335, 62], [322, 64, 335, 65], [322, 65, 335, 66], [323, 8, 336, 6], [323, 9, 336, 7], [323, 10, 336, 8], [324, 8, 338, 6], [324, 14, 338, 12, "blurredImageUrl"], [324, 29, 338, 27], [324, 32, 338, 30, "URL"], [324, 35, 338, 33], [324, 36, 338, 34, "createObjectURL"], [324, 51, 338, 49], [324, 52, 338, 50, "blurredImageBlob"], [324, 68, 338, 66], [324, 69, 338, 67], [325, 8, 339, 6, "console"], [325, 15, 339, 13], [325, 16, 339, 14, "log"], [325, 19, 339, 17], [325, 20, 339, 18], [325, 66, 339, 64], [325, 68, 339, 66, "blurredImageUrl"], [325, 83, 339, 81], [325, 84, 339, 82, "substring"], [325, 93, 339, 91], [325, 94, 339, 92], [325, 95, 339, 93], [325, 97, 339, 95], [325, 99, 339, 97], [325, 100, 339, 98], [325, 103, 339, 101], [325, 108, 339, 106], [325, 109, 339, 107], [326, 8, 341, 6, "setProcessingProgress"], [326, 29, 341, 27], [326, 30, 341, 28], [326, 33, 341, 31], [326, 34, 341, 32], [328, 8, 343, 6], [329, 8, 344, 6], [329, 14, 344, 12, "completeProcessing"], [329, 32, 344, 30], [329, 33, 344, 31, "blurredImageUrl"], [329, 48, 344, 46], [329, 49, 344, 47], [330, 6, 346, 4], [330, 7, 346, 5], [330, 8, 346, 6], [330, 15, 346, 13, "error"], [330, 20, 346, 18], [330, 22, 346, 20], [331, 8, 347, 6, "console"], [331, 15, 347, 13], [331, 16, 347, 14, "error"], [331, 21, 347, 19], [331, 22, 347, 20], [331, 57, 347, 55], [331, 59, 347, 57, "error"], [331, 64, 347, 62], [331, 65, 347, 63], [332, 8, 348, 6, "setErrorMessage"], [332, 23, 348, 21], [332, 24, 348, 22], [332, 50, 348, 48], [332, 51, 348, 49], [333, 8, 349, 6, "setProcessingState"], [333, 26, 349, 24], [333, 27, 349, 25], [333, 34, 349, 32], [333, 35, 349, 33], [334, 6, 350, 4], [335, 4, 351, 2], [335, 5, 351, 3], [337, 4, 353, 2], [338, 4, 354, 2], [338, 10, 354, 8, "completeProcessing"], [338, 28, 354, 26], [338, 31, 354, 29], [338, 37, 354, 36, "blurredImageUrl"], [338, 52, 354, 59], [338, 56, 354, 64], [339, 6, 355, 4], [339, 10, 355, 8], [340, 8, 356, 6, "setProcessingState"], [340, 26, 356, 24], [340, 27, 356, 25], [340, 37, 356, 35], [340, 38, 356, 36], [342, 8, 358, 6], [343, 8, 359, 6], [343, 14, 359, 12, "timestamp"], [343, 23, 359, 21], [343, 26, 359, 24, "Date"], [343, 30, 359, 28], [343, 31, 359, 29, "now"], [343, 34, 359, 32], [343, 35, 359, 33], [343, 36, 359, 34], [344, 8, 360, 6], [344, 14, 360, 12, "result"], [344, 20, 360, 18], [344, 23, 360, 21], [345, 10, 361, 8, "imageUrl"], [345, 18, 361, 16], [345, 20, 361, 18, "blurredImageUrl"], [345, 35, 361, 33], [346, 10, 362, 8, "localUri"], [346, 18, 362, 16], [346, 20, 362, 18, "blurredImageUrl"], [346, 35, 362, 33], [347, 10, 363, 8, "challengeCode"], [347, 23, 363, 21], [347, 25, 363, 23, "challengeCode"], [347, 38, 363, 36], [347, 42, 363, 40], [347, 44, 363, 42], [348, 10, 364, 8, "timestamp"], [348, 19, 364, 17], [349, 10, 365, 8, "jobId"], [349, 15, 365, 13], [349, 17, 365, 15], [349, 27, 365, 25, "timestamp"], [349, 36, 365, 34], [349, 38, 365, 36], [350, 10, 366, 8, "status"], [350, 16, 366, 14], [350, 18, 366, 16], [351, 8, 367, 6], [351, 9, 367, 7], [352, 8, 369, 6, "console"], [352, 15, 369, 13], [352, 16, 369, 14, "log"], [352, 19, 369, 17], [352, 20, 369, 18], [352, 58, 369, 56], [352, 60, 369, 58, "result"], [352, 66, 369, 64], [352, 67, 369, 65], [354, 8, 371, 6], [355, 8, 372, 6, "onComplete"], [355, 18, 372, 16], [355, 19, 372, 17, "result"], [355, 25, 372, 23], [355, 26, 372, 24], [356, 6, 374, 4], [356, 7, 374, 5], [356, 8, 374, 6], [356, 15, 374, 13, "error"], [356, 20, 374, 18], [356, 22, 374, 20], [357, 8, 375, 6, "console"], [357, 15, 375, 13], [357, 16, 375, 14, "error"], [357, 21, 375, 19], [357, 22, 375, 20], [357, 57, 375, 55], [357, 59, 375, 57, "error"], [357, 64, 375, 62], [357, 65, 375, 63], [358, 8, 376, 6, "setErrorMessage"], [358, 23, 376, 21], [358, 24, 376, 22], [358, 56, 376, 54], [358, 57, 376, 55], [359, 8, 377, 6, "setProcessingState"], [359, 26, 377, 24], [359, 27, 377, 25], [359, 34, 377, 32], [359, 35, 377, 33], [360, 6, 378, 4], [361, 4, 379, 2], [361, 5, 379, 3], [363, 4, 381, 2], [364, 4, 382, 2], [364, 10, 382, 8, "triggerServerProcessing"], [364, 33, 382, 31], [364, 36, 382, 34], [364, 42, 382, 34, "triggerServerProcessing"], [364, 43, 382, 41, "privateImageUrl"], [364, 58, 382, 64], [364, 60, 382, 66, "timestamp"], [364, 69, 382, 83], [364, 74, 382, 88], [365, 6, 383, 4], [365, 10, 383, 8], [366, 8, 384, 6, "console"], [366, 15, 384, 13], [366, 16, 384, 14, "log"], [366, 19, 384, 17], [366, 20, 384, 18], [366, 74, 384, 72], [366, 76, 384, 74, "privateImageUrl"], [366, 91, 384, 89], [366, 92, 384, 90], [367, 8, 385, 6, "setProcessingState"], [367, 26, 385, 24], [367, 27, 385, 25], [367, 39, 385, 37], [367, 40, 385, 38], [368, 8, 386, 6, "setProcessingProgress"], [368, 29, 386, 27], [368, 30, 386, 28], [368, 32, 386, 30], [368, 33, 386, 31], [369, 8, 388, 6], [369, 14, 388, 12, "requestBody"], [369, 25, 388, 23], [369, 28, 388, 26], [370, 10, 389, 8, "imageUrl"], [370, 18, 389, 16], [370, 20, 389, 18, "privateImageUrl"], [370, 35, 389, 33], [371, 10, 390, 8, "userId"], [371, 16, 390, 14], [372, 10, 391, 8, "requestId"], [372, 19, 391, 17], [373, 10, 392, 8, "timestamp"], [373, 19, 392, 17], [374, 10, 393, 8, "platform"], [374, 18, 393, 16], [374, 20, 393, 18], [375, 8, 394, 6], [375, 9, 394, 7], [376, 8, 396, 6, "console"], [376, 15, 396, 13], [376, 16, 396, 14, "log"], [376, 19, 396, 17], [376, 20, 396, 18], [376, 65, 396, 63], [376, 67, 396, 65, "requestBody"], [376, 78, 396, 76], [376, 79, 396, 77], [378, 8, 398, 6], [379, 8, 399, 6], [379, 14, 399, 12, "response"], [379, 22, 399, 20], [379, 25, 399, 23], [379, 31, 399, 29, "fetch"], [379, 36, 399, 34], [379, 37, 399, 35], [379, 40, 399, 38, "API_BASE_URL"], [379, 52, 399, 50], [379, 72, 399, 70], [379, 74, 399, 72], [380, 10, 400, 8, "method"], [380, 16, 400, 14], [380, 18, 400, 16], [380, 24, 400, 22], [381, 10, 401, 8, "headers"], [381, 17, 401, 15], [381, 19, 401, 17], [382, 12, 402, 10], [382, 26, 402, 24], [382, 28, 402, 26], [382, 46, 402, 44], [383, 12, 403, 10], [383, 27, 403, 25], [383, 29, 403, 27], [383, 39, 403, 37], [383, 45, 403, 43, "getAuthToken"], [383, 57, 403, 55], [383, 58, 403, 56], [383, 59, 403, 57], [384, 10, 404, 8], [384, 11, 404, 9], [385, 10, 405, 8, "body"], [385, 14, 405, 12], [385, 16, 405, 14, "JSON"], [385, 20, 405, 18], [385, 21, 405, 19, "stringify"], [385, 30, 405, 28], [385, 31, 405, 29, "requestBody"], [385, 42, 405, 40], [386, 8, 406, 6], [386, 9, 406, 7], [386, 10, 406, 8], [387, 8, 408, 6], [387, 12, 408, 10], [387, 13, 408, 11, "response"], [387, 21, 408, 19], [387, 22, 408, 20, "ok"], [387, 24, 408, 22], [387, 26, 408, 24], [388, 10, 409, 8], [388, 16, 409, 14, "errorText"], [388, 25, 409, 23], [388, 28, 409, 26], [388, 34, 409, 32, "response"], [388, 42, 409, 40], [388, 43, 409, 41, "text"], [388, 47, 409, 45], [388, 48, 409, 46], [388, 49, 409, 47], [389, 10, 410, 8, "console"], [389, 17, 410, 15], [389, 18, 410, 16, "error"], [389, 23, 410, 21], [389, 24, 410, 22], [389, 68, 410, 66], [389, 70, 410, 68, "response"], [389, 78, 410, 76], [389, 79, 410, 77, "status"], [389, 85, 410, 83], [389, 87, 410, 85, "errorText"], [389, 96, 410, 94], [389, 97, 410, 95], [390, 10, 411, 8], [390, 16, 411, 14], [390, 20, 411, 18, "Error"], [390, 25, 411, 23], [390, 26, 411, 24], [390, 48, 411, 46, "response"], [390, 56, 411, 54], [390, 57, 411, 55, "status"], [390, 63, 411, 61], [390, 67, 411, 65, "response"], [390, 75, 411, 73], [390, 76, 411, 74, "statusText"], [390, 86, 411, 84], [390, 88, 411, 86], [390, 89, 411, 87], [391, 8, 412, 6], [392, 8, 414, 6], [392, 14, 414, 12, "result"], [392, 20, 414, 18], [392, 23, 414, 21], [392, 29, 414, 27, "response"], [392, 37, 414, 35], [392, 38, 414, 36, "json"], [392, 42, 414, 40], [392, 43, 414, 41], [392, 44, 414, 42], [393, 8, 415, 6, "console"], [393, 15, 415, 13], [393, 16, 415, 14, "log"], [393, 19, 415, 17], [393, 20, 415, 18], [393, 68, 415, 66], [393, 70, 415, 68, "result"], [393, 76, 415, 74], [393, 77, 415, 75], [394, 8, 417, 6], [394, 12, 417, 10], [394, 13, 417, 11, "result"], [394, 19, 417, 17], [394, 20, 417, 18, "jobId"], [394, 25, 417, 23], [394, 27, 417, 25], [395, 10, 418, 8], [395, 16, 418, 14], [395, 20, 418, 18, "Error"], [395, 25, 418, 23], [395, 26, 418, 24], [395, 70, 418, 68], [395, 71, 418, 69], [396, 8, 419, 6], [398, 8, 421, 6], [399, 8, 422, 6], [399, 14, 422, 12, "pollForCompletion"], [399, 31, 422, 29], [399, 32, 422, 30, "result"], [399, 38, 422, 36], [399, 39, 422, 37, "jobId"], [399, 44, 422, 42], [399, 46, 422, 44, "timestamp"], [399, 55, 422, 53], [399, 56, 422, 54], [400, 6, 423, 4], [400, 7, 423, 5], [400, 8, 423, 6], [400, 15, 423, 13, "error"], [400, 20, 423, 18], [400, 22, 423, 20], [401, 8, 424, 6, "console"], [401, 15, 424, 13], [401, 16, 424, 14, "error"], [401, 21, 424, 19], [401, 22, 424, 20], [401, 57, 424, 55], [401, 59, 424, 57, "error"], [401, 64, 424, 62], [401, 65, 424, 63], [402, 8, 425, 6, "setErrorMessage"], [402, 23, 425, 21], [402, 24, 425, 22], [402, 52, 425, 50, "error"], [402, 57, 425, 55], [402, 58, 425, 56, "message"], [402, 65, 425, 63], [402, 67, 425, 65], [402, 68, 425, 66], [403, 8, 426, 6, "setProcessingState"], [403, 26, 426, 24], [403, 27, 426, 25], [403, 34, 426, 32], [403, 35, 426, 33], [404, 6, 427, 4], [405, 4, 428, 2], [405, 5, 428, 3], [406, 4, 429, 2], [407, 4, 430, 2], [407, 10, 430, 8, "pollForCompletion"], [407, 27, 430, 25], [407, 30, 430, 28], [407, 36, 430, 28, "pollForCompletion"], [407, 37, 430, 35, "jobId"], [407, 42, 430, 48], [407, 44, 430, 50, "timestamp"], [407, 53, 430, 67], [407, 55, 430, 69, "attempts"], [407, 63, 430, 77], [407, 66, 430, 80], [407, 67, 430, 81], [407, 72, 430, 86], [408, 6, 431, 4], [408, 12, 431, 10, "MAX_ATTEMPTS"], [408, 24, 431, 22], [408, 27, 431, 25], [408, 29, 431, 27], [408, 30, 431, 28], [408, 31, 431, 29], [409, 6, 432, 4], [409, 12, 432, 10, "POLL_INTERVAL"], [409, 25, 432, 23], [409, 28, 432, 26], [409, 32, 432, 30], [409, 33, 432, 31], [409, 34, 432, 32], [411, 6, 434, 4, "console"], [411, 13, 434, 11], [411, 14, 434, 12, "log"], [411, 17, 434, 15], [411, 18, 434, 16], [411, 53, 434, 51, "attempts"], [411, 61, 434, 59], [411, 64, 434, 62], [411, 65, 434, 63], [411, 69, 434, 67, "MAX_ATTEMPTS"], [411, 81, 434, 79], [411, 93, 434, 91, "jobId"], [411, 98, 434, 96], [411, 100, 434, 98], [411, 101, 434, 99], [412, 6, 436, 4], [412, 10, 436, 8, "attempts"], [412, 18, 436, 16], [412, 22, 436, 20, "MAX_ATTEMPTS"], [412, 34, 436, 32], [412, 36, 436, 34], [413, 8, 437, 6, "console"], [413, 15, 437, 13], [413, 16, 437, 14, "error"], [413, 21, 437, 19], [413, 22, 437, 20], [413, 75, 437, 73], [413, 76, 437, 74], [414, 8, 438, 6, "setErrorMessage"], [414, 23, 438, 21], [414, 24, 438, 22], [414, 63, 438, 61], [414, 64, 438, 62], [415, 8, 439, 6, "setProcessingState"], [415, 26, 439, 24], [415, 27, 439, 25], [415, 34, 439, 32], [415, 35, 439, 33], [416, 8, 440, 6], [417, 6, 441, 4], [418, 6, 443, 4], [418, 10, 443, 8], [419, 8, 444, 6], [419, 14, 444, 12, "response"], [419, 22, 444, 20], [419, 25, 444, 23], [419, 31, 444, 29, "fetch"], [419, 36, 444, 34], [419, 37, 444, 35], [419, 40, 444, 38, "API_BASE_URL"], [419, 52, 444, 50], [419, 75, 444, 73, "jobId"], [419, 80, 444, 78], [419, 82, 444, 80], [419, 84, 444, 82], [420, 10, 445, 8, "headers"], [420, 17, 445, 15], [420, 19, 445, 17], [421, 12, 446, 10], [421, 27, 446, 25], [421, 29, 446, 27], [421, 39, 446, 37], [421, 45, 446, 43, "getAuthToken"], [421, 57, 446, 55], [421, 58, 446, 56], [421, 59, 446, 57], [422, 10, 447, 8], [423, 8, 448, 6], [423, 9, 448, 7], [423, 10, 448, 8], [424, 8, 450, 6], [424, 12, 450, 10], [424, 13, 450, 11, "response"], [424, 21, 450, 19], [424, 22, 450, 20, "ok"], [424, 24, 450, 22], [424, 26, 450, 24], [425, 10, 451, 8], [425, 16, 451, 14], [425, 20, 451, 18, "Error"], [425, 25, 451, 23], [425, 26, 451, 24], [425, 34, 451, 32, "response"], [425, 42, 451, 40], [425, 43, 451, 41, "status"], [425, 49, 451, 47], [425, 54, 451, 52, "response"], [425, 62, 451, 60], [425, 63, 451, 61, "statusText"], [425, 73, 451, 71], [425, 75, 451, 73], [425, 76, 451, 74], [426, 8, 452, 6], [427, 8, 454, 6], [427, 14, 454, 12, "status"], [427, 20, 454, 18], [427, 23, 454, 21], [427, 29, 454, 27, "response"], [427, 37, 454, 35], [427, 38, 454, 36, "json"], [427, 42, 454, 40], [427, 43, 454, 41], [427, 44, 454, 42], [428, 8, 455, 6, "console"], [428, 15, 455, 13], [428, 16, 455, 14, "log"], [428, 19, 455, 17], [428, 20, 455, 18], [428, 54, 455, 52], [428, 56, 455, 54, "status"], [428, 62, 455, 60], [428, 63, 455, 61], [429, 8, 457, 6], [429, 12, 457, 10, "status"], [429, 18, 457, 16], [429, 19, 457, 17, "status"], [429, 25, 457, 23], [429, 30, 457, 28], [429, 41, 457, 39], [429, 43, 457, 41], [430, 10, 458, 8, "console"], [430, 17, 458, 15], [430, 18, 458, 16, "log"], [430, 21, 458, 19], [430, 22, 458, 20], [430, 73, 458, 71], [430, 74, 458, 72], [431, 10, 459, 8, "setProcessingProgress"], [431, 31, 459, 29], [431, 32, 459, 30], [431, 35, 459, 33], [431, 36, 459, 34], [432, 10, 460, 8, "setProcessingState"], [432, 28, 460, 26], [432, 29, 460, 27], [432, 40, 460, 38], [432, 41, 460, 39], [433, 10, 461, 8], [434, 10, 462, 8], [434, 16, 462, 14, "result"], [434, 22, 462, 20], [434, 25, 462, 23], [435, 12, 463, 10, "imageUrl"], [435, 20, 463, 18], [435, 22, 463, 20, "status"], [435, 28, 463, 26], [435, 29, 463, 27, "publicUrl"], [435, 38, 463, 36], [436, 12, 463, 38], [437, 12, 464, 10, "localUri"], [437, 20, 464, 18], [437, 22, 464, 20, "capturedPhoto"], [437, 35, 464, 33], [437, 39, 464, 37, "status"], [437, 45, 464, 43], [437, 46, 464, 44, "publicUrl"], [437, 55, 464, 53], [438, 12, 464, 55], [439, 12, 465, 10, "challengeCode"], [439, 25, 465, 23], [439, 27, 465, 25, "challengeCode"], [439, 40, 465, 38], [439, 44, 465, 42], [439, 46, 465, 44], [440, 12, 466, 10, "timestamp"], [440, 21, 466, 19], [441, 12, 467, 10, "processingStatus"], [441, 28, 467, 26], [441, 30, 467, 28], [442, 10, 468, 8], [442, 11, 468, 9], [443, 10, 469, 8, "console"], [443, 17, 469, 15], [443, 18, 469, 16, "log"], [443, 21, 469, 19], [443, 22, 469, 20], [443, 57, 469, 55], [443, 59, 469, 57, "result"], [443, 65, 469, 63], [443, 66, 469, 64], [444, 10, 470, 8, "onComplete"], [444, 20, 470, 18], [444, 21, 470, 19, "result"], [444, 27, 470, 25], [444, 28, 470, 26], [445, 10, 471, 8], [446, 8, 472, 6], [446, 9, 472, 7], [446, 15, 472, 13], [446, 19, 472, 17, "status"], [446, 25, 472, 23], [446, 26, 472, 24, "status"], [446, 32, 472, 30], [446, 37, 472, 35], [446, 45, 472, 43], [446, 47, 472, 45], [447, 10, 473, 8, "console"], [447, 17, 473, 15], [447, 18, 473, 16, "error"], [447, 23, 473, 21], [447, 24, 473, 22], [447, 60, 473, 58], [447, 62, 473, 60, "status"], [447, 68, 473, 66], [447, 69, 473, 67, "error"], [447, 74, 473, 72], [447, 75, 473, 73], [448, 10, 474, 8], [448, 16, 474, 14], [448, 20, 474, 18, "Error"], [448, 25, 474, 23], [448, 26, 474, 24, "status"], [448, 32, 474, 30], [448, 33, 474, 31, "error"], [448, 38, 474, 36], [448, 42, 474, 40], [448, 61, 474, 59], [448, 62, 474, 60], [449, 8, 475, 6], [449, 9, 475, 7], [449, 15, 475, 13], [450, 10, 476, 8], [451, 10, 477, 8], [451, 16, 477, 14, "progressValue"], [451, 29, 477, 27], [451, 32, 477, 30], [451, 34, 477, 32], [451, 37, 477, 36, "attempts"], [451, 45, 477, 44], [451, 48, 477, 47, "MAX_ATTEMPTS"], [451, 60, 477, 59], [451, 63, 477, 63], [451, 65, 477, 65], [452, 10, 478, 8, "console"], [452, 17, 478, 15], [452, 18, 478, 16, "log"], [452, 21, 478, 19], [452, 22, 478, 20], [452, 71, 478, 69, "progressValue"], [452, 84, 478, 82], [452, 87, 478, 85], [452, 88, 478, 86], [453, 10, 479, 8, "setProcessingProgress"], [453, 31, 479, 29], [453, 32, 479, 30, "progressValue"], [453, 45, 479, 43], [453, 46, 479, 44], [454, 10, 481, 8, "setTimeout"], [454, 20, 481, 18], [454, 21, 481, 19], [454, 27, 481, 25], [455, 12, 482, 10, "pollForCompletion"], [455, 29, 482, 27], [455, 30, 482, 28, "jobId"], [455, 35, 482, 33], [455, 37, 482, 35, "timestamp"], [455, 46, 482, 44], [455, 48, 482, 46, "attempts"], [455, 56, 482, 54], [455, 59, 482, 57], [455, 60, 482, 58], [455, 61, 482, 59], [456, 10, 483, 8], [456, 11, 483, 9], [456, 13, 483, 11, "POLL_INTERVAL"], [456, 26, 483, 24], [456, 27, 483, 25], [457, 8, 484, 6], [458, 6, 485, 4], [458, 7, 485, 5], [458, 8, 485, 6], [458, 15, 485, 13, "error"], [458, 20, 485, 18], [458, 22, 485, 20], [459, 8, 486, 6, "console"], [459, 15, 486, 13], [459, 16, 486, 14, "error"], [459, 21, 486, 19], [459, 22, 486, 20], [459, 54, 486, 52], [459, 56, 486, 54, "error"], [459, 61, 486, 59], [459, 62, 486, 60], [460, 8, 487, 6, "setErrorMessage"], [460, 23, 487, 21], [460, 24, 487, 22], [460, 62, 487, 60, "error"], [460, 67, 487, 65], [460, 68, 487, 66, "message"], [460, 75, 487, 73], [460, 77, 487, 75], [460, 78, 487, 76], [461, 8, 488, 6, "setProcessingState"], [461, 26, 488, 24], [461, 27, 488, 25], [461, 34, 488, 32], [461, 35, 488, 33], [462, 6, 489, 4], [463, 4, 490, 2], [463, 5, 490, 3], [464, 4, 491, 2], [465, 4, 492, 2], [465, 10, 492, 8, "getAuthToken"], [465, 22, 492, 20], [465, 25, 492, 23], [465, 31, 492, 23, "getAuthToken"], [465, 32, 492, 23], [465, 37, 492, 52], [466, 6, 493, 4], [467, 6, 494, 4], [468, 6, 495, 4], [468, 13, 495, 11], [468, 30, 495, 28], [469, 4, 496, 2], [469, 5, 496, 3], [471, 4, 498, 2], [472, 4, 499, 2], [472, 10, 499, 8, "retryCapture"], [472, 22, 499, 20], [472, 25, 499, 23], [472, 29, 499, 23, "useCallback"], [472, 47, 499, 34], [472, 49, 499, 35], [472, 55, 499, 41], [473, 6, 500, 4, "console"], [473, 13, 500, 11], [473, 14, 500, 12, "log"], [473, 17, 500, 15], [473, 18, 500, 16], [473, 55, 500, 53], [473, 56, 500, 54], [474, 6, 501, 4, "setProcessingState"], [474, 24, 501, 22], [474, 25, 501, 23], [474, 31, 501, 29], [474, 32, 501, 30], [475, 6, 502, 4, "setErrorMessage"], [475, 21, 502, 19], [475, 22, 502, 20], [475, 24, 502, 22], [475, 25, 502, 23], [476, 6, 503, 4, "setCapturedPhoto"], [476, 22, 503, 20], [476, 23, 503, 21], [476, 25, 503, 23], [476, 26, 503, 24], [477, 6, 504, 4, "setProcessingProgress"], [477, 27, 504, 25], [477, 28, 504, 26], [477, 29, 504, 27], [477, 30, 504, 28], [478, 4, 505, 2], [478, 5, 505, 3], [478, 7, 505, 5], [478, 9, 505, 7], [478, 10, 505, 8], [479, 4, 506, 2], [480, 4, 507, 2], [480, 8, 507, 2, "useEffect"], [480, 24, 507, 11], [480, 26, 507, 12], [480, 32, 507, 18], [481, 6, 508, 4, "console"], [481, 13, 508, 11], [481, 14, 508, 12, "log"], [481, 17, 508, 15], [481, 18, 508, 16], [481, 53, 508, 51], [481, 55, 508, 53, "permission"], [481, 65, 508, 63], [481, 66, 508, 64], [482, 6, 509, 4], [482, 10, 509, 8, "permission"], [482, 20, 509, 18], [482, 22, 509, 20], [483, 8, 510, 6, "console"], [483, 15, 510, 13], [483, 16, 510, 14, "log"], [483, 19, 510, 17], [483, 20, 510, 18], [483, 57, 510, 55], [483, 59, 510, 57, "permission"], [483, 69, 510, 67], [483, 70, 510, 68, "granted"], [483, 77, 510, 75], [483, 78, 510, 76], [484, 6, 511, 4], [485, 4, 512, 2], [485, 5, 512, 3], [485, 7, 512, 5], [485, 8, 512, 6, "permission"], [485, 18, 512, 16], [485, 19, 512, 17], [485, 20, 512, 18], [486, 4, 513, 2], [487, 4, 514, 2], [487, 8, 514, 6], [487, 9, 514, 7, "permission"], [487, 19, 514, 17], [487, 21, 514, 19], [488, 6, 515, 4, "console"], [488, 13, 515, 11], [488, 14, 515, 12, "log"], [488, 17, 515, 15], [488, 18, 515, 16], [488, 67, 515, 65], [488, 68, 515, 66], [489, 6, 516, 4], [489, 26, 517, 6], [489, 30, 517, 6, "_jsxDevRuntime"], [489, 44, 517, 6], [489, 45, 517, 6, "jsxDEV"], [489, 51, 517, 6], [489, 53, 517, 7, "_View"], [489, 58, 517, 7], [489, 59, 517, 7, "default"], [489, 66, 517, 11], [490, 8, 517, 12, "style"], [490, 13, 517, 17], [490, 15, 517, 19, "styles"], [490, 21, 517, 25], [490, 22, 517, 26, "container"], [490, 31, 517, 36], [491, 8, 517, 36, "children"], [491, 16, 517, 36], [491, 32, 518, 8], [491, 36, 518, 8, "_jsxDevRuntime"], [491, 50, 518, 8], [491, 51, 518, 8, "jsxDEV"], [491, 57, 518, 8], [491, 59, 518, 9, "_ActivityIndicator"], [491, 77, 518, 9], [491, 78, 518, 9, "default"], [491, 85, 518, 26], [492, 10, 518, 27, "size"], [492, 14, 518, 31], [492, 16, 518, 32], [492, 23, 518, 39], [493, 10, 518, 40, "color"], [493, 15, 518, 45], [493, 17, 518, 46], [494, 8, 518, 55], [495, 10, 518, 55, "fileName"], [495, 18, 518, 55], [495, 20, 518, 55, "_jsxFileName"], [495, 32, 518, 55], [496, 10, 518, 55, "lineNumber"], [496, 20, 518, 55], [497, 10, 518, 55, "columnNumber"], [497, 22, 518, 55], [498, 8, 518, 55], [498, 15, 518, 57], [498, 16, 518, 58], [498, 31, 519, 8], [498, 35, 519, 8, "_jsxDevRuntime"], [498, 49, 519, 8], [498, 50, 519, 8, "jsxDEV"], [498, 56, 519, 8], [498, 58, 519, 9, "_Text"], [498, 63, 519, 9], [498, 64, 519, 9, "default"], [498, 71, 519, 13], [499, 10, 519, 14, "style"], [499, 15, 519, 19], [499, 17, 519, 21, "styles"], [499, 23, 519, 27], [499, 24, 519, 28, "loadingText"], [499, 35, 519, 40], [500, 10, 519, 40, "children"], [500, 18, 519, 40], [500, 20, 519, 41], [501, 8, 519, 58], [502, 10, 519, 58, "fileName"], [502, 18, 519, 58], [502, 20, 519, 58, "_jsxFileName"], [502, 32, 519, 58], [503, 10, 519, 58, "lineNumber"], [503, 20, 519, 58], [504, 10, 519, 58, "columnNumber"], [504, 22, 519, 58], [505, 8, 519, 58], [505, 15, 519, 64], [505, 16, 519, 65], [506, 6, 519, 65], [507, 8, 519, 65, "fileName"], [507, 16, 519, 65], [507, 18, 519, 65, "_jsxFileName"], [507, 30, 519, 65], [508, 8, 519, 65, "lineNumber"], [508, 18, 519, 65], [509, 8, 519, 65, "columnNumber"], [509, 20, 519, 65], [510, 6, 519, 65], [510, 13, 520, 12], [510, 14, 520, 13], [511, 4, 522, 2], [512, 4, 523, 2], [512, 8, 523, 6], [512, 9, 523, 7, "permission"], [512, 19, 523, 17], [512, 20, 523, 18, "granted"], [512, 27, 523, 25], [512, 29, 523, 27], [513, 6, 524, 4, "console"], [513, 13, 524, 11], [513, 14, 524, 12, "log"], [513, 17, 524, 15], [513, 18, 524, 16], [513, 93, 524, 91], [513, 94, 524, 92], [514, 6, 525, 4], [514, 26, 526, 6], [514, 30, 526, 6, "_jsxDevRuntime"], [514, 44, 526, 6], [514, 45, 526, 6, "jsxDEV"], [514, 51, 526, 6], [514, 53, 526, 7, "_View"], [514, 58, 526, 7], [514, 59, 526, 7, "default"], [514, 66, 526, 11], [515, 8, 526, 12, "style"], [515, 13, 526, 17], [515, 15, 526, 19, "styles"], [515, 21, 526, 25], [515, 22, 526, 26, "container"], [515, 31, 526, 36], [516, 8, 526, 36, "children"], [516, 16, 526, 36], [516, 31, 527, 8], [516, 35, 527, 8, "_jsxDevRuntime"], [516, 49, 527, 8], [516, 50, 527, 8, "jsxDEV"], [516, 56, 527, 8], [516, 58, 527, 9, "_View"], [516, 63, 527, 9], [516, 64, 527, 9, "default"], [516, 71, 527, 13], [517, 10, 527, 14, "style"], [517, 15, 527, 19], [517, 17, 527, 21, "styles"], [517, 23, 527, 27], [517, 24, 527, 28, "permissionContent"], [517, 41, 527, 46], [518, 10, 527, 46, "children"], [518, 18, 527, 46], [518, 34, 528, 10], [518, 38, 528, 10, "_jsxDevRuntime"], [518, 52, 528, 10], [518, 53, 528, 10, "jsxDEV"], [518, 59, 528, 10], [518, 61, 528, 11, "_lucideReactNative"], [518, 79, 528, 11], [518, 80, 528, 11, "Camera"], [518, 86, 528, 21], [519, 12, 528, 22, "size"], [519, 16, 528, 26], [519, 18, 528, 28], [519, 20, 528, 31], [520, 12, 528, 32, "color"], [520, 17, 528, 37], [520, 19, 528, 38], [521, 10, 528, 47], [522, 12, 528, 47, "fileName"], [522, 20, 528, 47], [522, 22, 528, 47, "_jsxFileName"], [522, 34, 528, 47], [523, 12, 528, 47, "lineNumber"], [523, 22, 528, 47], [524, 12, 528, 47, "columnNumber"], [524, 24, 528, 47], [525, 10, 528, 47], [525, 17, 528, 49], [525, 18, 528, 50], [525, 33, 529, 10], [525, 37, 529, 10, "_jsxDevRuntime"], [525, 51, 529, 10], [525, 52, 529, 10, "jsxDEV"], [525, 58, 529, 10], [525, 60, 529, 11, "_Text"], [525, 65, 529, 11], [525, 66, 529, 11, "default"], [525, 73, 529, 15], [526, 12, 529, 16, "style"], [526, 17, 529, 21], [526, 19, 529, 23, "styles"], [526, 25, 529, 29], [526, 26, 529, 30, "permissionTitle"], [526, 41, 529, 46], [527, 12, 529, 46, "children"], [527, 20, 529, 46], [527, 22, 529, 47], [528, 10, 529, 73], [529, 12, 529, 73, "fileName"], [529, 20, 529, 73], [529, 22, 529, 73, "_jsxFileName"], [529, 34, 529, 73], [530, 12, 529, 73, "lineNumber"], [530, 22, 529, 73], [531, 12, 529, 73, "columnNumber"], [531, 24, 529, 73], [532, 10, 529, 73], [532, 17, 529, 79], [532, 18, 529, 80], [532, 33, 530, 10], [532, 37, 530, 10, "_jsxDevRuntime"], [532, 51, 530, 10], [532, 52, 530, 10, "jsxDEV"], [532, 58, 530, 10], [532, 60, 530, 11, "_Text"], [532, 65, 530, 11], [532, 66, 530, 11, "default"], [532, 73, 530, 15], [533, 12, 530, 16, "style"], [533, 17, 530, 21], [533, 19, 530, 23, "styles"], [533, 25, 530, 29], [533, 26, 530, 30, "permissionDescription"], [533, 47, 530, 52], [534, 12, 530, 52, "children"], [534, 20, 530, 52], [534, 22, 530, 53], [535, 10, 533, 10], [536, 12, 533, 10, "fileName"], [536, 20, 533, 10], [536, 22, 533, 10, "_jsxFileName"], [536, 34, 533, 10], [537, 12, 533, 10, "lineNumber"], [537, 22, 533, 10], [538, 12, 533, 10, "columnNumber"], [538, 24, 533, 10], [539, 10, 533, 10], [539, 17, 533, 16], [539, 18, 533, 17], [539, 33, 534, 10], [539, 37, 534, 10, "_jsxDevRuntime"], [539, 51, 534, 10], [539, 52, 534, 10, "jsxDEV"], [539, 58, 534, 10], [539, 60, 534, 11, "_TouchableOpacity"], [539, 77, 534, 11], [539, 78, 534, 11, "default"], [539, 85, 534, 27], [540, 12, 534, 28, "onPress"], [540, 19, 534, 35], [540, 21, 534, 37, "requestPermission"], [540, 38, 534, 55], [541, 12, 534, 56, "style"], [541, 17, 534, 61], [541, 19, 534, 63, "styles"], [541, 25, 534, 69], [541, 26, 534, 70, "primaryButton"], [541, 39, 534, 84], [542, 12, 534, 84, "children"], [542, 20, 534, 84], [542, 35, 535, 12], [542, 39, 535, 12, "_jsxDevRuntime"], [542, 53, 535, 12], [542, 54, 535, 12, "jsxDEV"], [542, 60, 535, 12], [542, 62, 535, 13, "_Text"], [542, 67, 535, 13], [542, 68, 535, 13, "default"], [542, 75, 535, 17], [543, 14, 535, 18, "style"], [543, 19, 535, 23], [543, 21, 535, 25, "styles"], [543, 27, 535, 31], [543, 28, 535, 32, "primaryButtonText"], [543, 45, 535, 50], [544, 14, 535, 50, "children"], [544, 22, 535, 50], [544, 24, 535, 51], [545, 12, 535, 67], [546, 14, 535, 67, "fileName"], [546, 22, 535, 67], [546, 24, 535, 67, "_jsxFileName"], [546, 36, 535, 67], [547, 14, 535, 67, "lineNumber"], [547, 24, 535, 67], [548, 14, 535, 67, "columnNumber"], [548, 26, 535, 67], [549, 12, 535, 67], [549, 19, 535, 73], [550, 10, 535, 74], [551, 12, 535, 74, "fileName"], [551, 20, 535, 74], [551, 22, 535, 74, "_jsxFileName"], [551, 34, 535, 74], [552, 12, 535, 74, "lineNumber"], [552, 22, 535, 74], [553, 12, 535, 74, "columnNumber"], [553, 24, 535, 74], [554, 10, 535, 74], [554, 17, 536, 28], [554, 18, 536, 29], [554, 33, 537, 10], [554, 37, 537, 10, "_jsxDevRuntime"], [554, 51, 537, 10], [554, 52, 537, 10, "jsxDEV"], [554, 58, 537, 10], [554, 60, 537, 11, "_TouchableOpacity"], [554, 77, 537, 11], [554, 78, 537, 11, "default"], [554, 85, 537, 27], [555, 12, 537, 28, "onPress"], [555, 19, 537, 35], [555, 21, 537, 37, "onCancel"], [555, 29, 537, 46], [556, 12, 537, 47, "style"], [556, 17, 537, 52], [556, 19, 537, 54, "styles"], [556, 25, 537, 60], [556, 26, 537, 61, "secondaryButton"], [556, 41, 537, 77], [557, 12, 537, 77, "children"], [557, 20, 537, 77], [557, 35, 538, 12], [557, 39, 538, 12, "_jsxDevRuntime"], [557, 53, 538, 12], [557, 54, 538, 12, "jsxDEV"], [557, 60, 538, 12], [557, 62, 538, 13, "_Text"], [557, 67, 538, 13], [557, 68, 538, 13, "default"], [557, 75, 538, 17], [558, 14, 538, 18, "style"], [558, 19, 538, 23], [558, 21, 538, 25, "styles"], [558, 27, 538, 31], [558, 28, 538, 32, "secondaryButtonText"], [558, 47, 538, 52], [559, 14, 538, 52, "children"], [559, 22, 538, 52], [559, 24, 538, 53], [560, 12, 538, 59], [561, 14, 538, 59, "fileName"], [561, 22, 538, 59], [561, 24, 538, 59, "_jsxFileName"], [561, 36, 538, 59], [562, 14, 538, 59, "lineNumber"], [562, 24, 538, 59], [563, 14, 538, 59, "columnNumber"], [563, 26, 538, 59], [564, 12, 538, 59], [564, 19, 538, 65], [565, 10, 538, 66], [566, 12, 538, 66, "fileName"], [566, 20, 538, 66], [566, 22, 538, 66, "_jsxFileName"], [566, 34, 538, 66], [567, 12, 538, 66, "lineNumber"], [567, 22, 538, 66], [568, 12, 538, 66, "columnNumber"], [568, 24, 538, 66], [569, 10, 538, 66], [569, 17, 539, 28], [569, 18, 539, 29], [570, 8, 539, 29], [571, 10, 539, 29, "fileName"], [571, 18, 539, 29], [571, 20, 539, 29, "_jsxFileName"], [571, 32, 539, 29], [572, 10, 539, 29, "lineNumber"], [572, 20, 539, 29], [573, 10, 539, 29, "columnNumber"], [573, 22, 539, 29], [574, 8, 539, 29], [574, 15, 540, 14], [575, 6, 540, 15], [576, 8, 540, 15, "fileName"], [576, 16, 540, 15], [576, 18, 540, 15, "_jsxFileName"], [576, 30, 540, 15], [577, 8, 540, 15, "lineNumber"], [577, 18, 540, 15], [578, 8, 540, 15, "columnNumber"], [578, 20, 540, 15], [579, 6, 540, 15], [579, 13, 541, 12], [579, 14, 541, 13], [580, 4, 543, 2], [581, 4, 544, 2], [582, 4, 545, 2, "console"], [582, 11, 545, 9], [582, 12, 545, 10, "log"], [582, 15, 545, 13], [582, 16, 545, 14], [582, 55, 545, 53], [582, 56, 545, 54], [583, 4, 547, 2], [583, 24, 548, 4], [583, 28, 548, 4, "_jsxDevRuntime"], [583, 42, 548, 4], [583, 43, 548, 4, "jsxDEV"], [583, 49, 548, 4], [583, 51, 548, 5, "_View"], [583, 56, 548, 5], [583, 57, 548, 5, "default"], [583, 64, 548, 9], [584, 6, 548, 10, "style"], [584, 11, 548, 15], [584, 13, 548, 17, "styles"], [584, 19, 548, 23], [584, 20, 548, 24, "container"], [584, 29, 548, 34], [585, 6, 548, 34, "children"], [585, 14, 548, 34], [585, 30, 550, 6], [585, 34, 550, 6, "_jsxDevRuntime"], [585, 48, 550, 6], [585, 49, 550, 6, "jsxDEV"], [585, 55, 550, 6], [585, 57, 550, 7, "_View"], [585, 62, 550, 7], [585, 63, 550, 7, "default"], [585, 70, 550, 11], [586, 8, 550, 12, "style"], [586, 13, 550, 17], [586, 15, 550, 19, "styles"], [586, 21, 550, 25], [586, 22, 550, 26, "cameraContainer"], [586, 37, 550, 42], [587, 8, 550, 43, "id"], [587, 10, 550, 45], [587, 12, 550, 46], [587, 29, 550, 63], [588, 8, 550, 63, "children"], [588, 16, 550, 63], [588, 32, 551, 8], [588, 36, 551, 8, "_jsxDevRuntime"], [588, 50, 551, 8], [588, 51, 551, 8, "jsxDEV"], [588, 57, 551, 8], [588, 59, 551, 9, "_expoCamera"], [588, 70, 551, 9], [588, 71, 551, 9, "CameraView"], [588, 81, 551, 19], [589, 10, 552, 10, "ref"], [589, 13, 552, 13], [589, 15, 552, 15, "cameraRef"], [589, 24, 552, 25], [590, 10, 553, 10, "style"], [590, 15, 553, 15], [590, 17, 553, 17], [590, 18, 553, 18, "styles"], [590, 24, 553, 24], [590, 25, 553, 25, "camera"], [590, 31, 553, 31], [590, 33, 553, 33], [591, 12, 553, 35, "backgroundColor"], [591, 27, 553, 50], [591, 29, 553, 52], [592, 10, 553, 62], [592, 11, 553, 63], [592, 12, 553, 65], [593, 10, 554, 10, "facing"], [593, 16, 554, 16], [593, 18, 554, 17], [593, 24, 554, 23], [594, 10, 555, 10, "onLayout"], [594, 18, 555, 18], [594, 20, 555, 21, "e"], [594, 21, 555, 22], [594, 25, 555, 27], [595, 12, 556, 12, "console"], [595, 19, 556, 19], [595, 20, 556, 20, "log"], [595, 23, 556, 23], [595, 24, 556, 24], [595, 56, 556, 56], [595, 58, 556, 58, "e"], [595, 59, 556, 59], [595, 60, 556, 60, "nativeEvent"], [595, 71, 556, 71], [595, 72, 556, 72, "layout"], [595, 78, 556, 78], [595, 79, 556, 79], [596, 12, 557, 12, "setViewSize"], [596, 23, 557, 23], [596, 24, 557, 24], [597, 14, 557, 26, "width"], [597, 19, 557, 31], [597, 21, 557, 33, "e"], [597, 22, 557, 34], [597, 23, 557, 35, "nativeEvent"], [597, 34, 557, 46], [597, 35, 557, 47, "layout"], [597, 41, 557, 53], [597, 42, 557, 54, "width"], [597, 47, 557, 59], [598, 14, 557, 61, "height"], [598, 20, 557, 67], [598, 22, 557, 69, "e"], [598, 23, 557, 70], [598, 24, 557, 71, "nativeEvent"], [598, 35, 557, 82], [598, 36, 557, 83, "layout"], [598, 42, 557, 89], [598, 43, 557, 90, "height"], [599, 12, 557, 97], [599, 13, 557, 98], [599, 14, 557, 99], [600, 10, 558, 10], [600, 11, 558, 12], [601, 10, 559, 10, "onCameraReady"], [601, 23, 559, 23], [601, 25, 559, 25, "onCameraReady"], [601, 26, 559, 25], [601, 31, 559, 31], [602, 12, 560, 12, "console"], [602, 19, 560, 19], [602, 20, 560, 20, "log"], [602, 23, 560, 23], [602, 24, 560, 24], [602, 55, 560, 55], [602, 56, 560, 56], [603, 12, 561, 12, "setIsCameraReady"], [603, 28, 561, 28], [603, 29, 561, 29], [603, 33, 561, 33], [603, 34, 561, 34], [603, 35, 561, 35], [603, 36, 561, 36], [604, 10, 562, 10], [604, 11, 562, 12], [605, 10, 563, 10, "onMountError"], [605, 22, 563, 22], [605, 24, 563, 25, "error"], [605, 29, 563, 30], [605, 33, 563, 35], [606, 12, 564, 12, "console"], [606, 19, 564, 19], [606, 20, 564, 20, "error"], [606, 25, 564, 25], [606, 26, 564, 26], [606, 63, 564, 63], [606, 65, 564, 65, "error"], [606, 70, 564, 70], [606, 71, 564, 71], [607, 12, 565, 12, "setErrorMessage"], [607, 27, 565, 27], [607, 28, 565, 28], [607, 57, 565, 57], [607, 58, 565, 58], [608, 12, 566, 12, "setProcessingState"], [608, 30, 566, 30], [608, 31, 566, 31], [608, 38, 566, 38], [608, 39, 566, 39], [609, 10, 567, 10], [610, 8, 567, 12], [611, 10, 567, 12, "fileName"], [611, 18, 567, 12], [611, 20, 567, 12, "_jsxFileName"], [611, 32, 567, 12], [612, 10, 567, 12, "lineNumber"], [612, 20, 567, 12], [613, 10, 567, 12, "columnNumber"], [613, 22, 567, 12], [614, 8, 567, 12], [614, 15, 568, 9], [614, 16, 568, 10], [614, 18, 570, 9], [614, 19, 570, 10, "isCameraReady"], [614, 32, 570, 23], [614, 49, 571, 10], [614, 53, 571, 10, "_jsxDevRuntime"], [614, 67, 571, 10], [614, 68, 571, 10, "jsxDEV"], [614, 74, 571, 10], [614, 76, 571, 11, "_View"], [614, 81, 571, 11], [614, 82, 571, 11, "default"], [614, 89, 571, 15], [615, 10, 571, 16, "style"], [615, 15, 571, 21], [615, 17, 571, 23], [615, 18, 571, 24, "StyleSheet"], [615, 37, 571, 34], [615, 38, 571, 35, "absoluteFill"], [615, 50, 571, 47], [615, 52, 571, 49], [616, 12, 571, 51, "backgroundColor"], [616, 27, 571, 66], [616, 29, 571, 68], [616, 49, 571, 88], [617, 12, 571, 90, "justifyContent"], [617, 26, 571, 104], [617, 28, 571, 106], [617, 36, 571, 114], [618, 12, 571, 116, "alignItems"], [618, 22, 571, 126], [618, 24, 571, 128], [618, 32, 571, 136], [619, 12, 571, 138, "zIndex"], [619, 18, 571, 144], [619, 20, 571, 146], [620, 10, 571, 151], [620, 11, 571, 152], [620, 12, 571, 154], [621, 10, 571, 154, "children"], [621, 18, 571, 154], [621, 33, 572, 12], [621, 37, 572, 12, "_jsxDevRuntime"], [621, 51, 572, 12], [621, 52, 572, 12, "jsxDEV"], [621, 58, 572, 12], [621, 60, 572, 13, "_View"], [621, 65, 572, 13], [621, 66, 572, 13, "default"], [621, 73, 572, 17], [622, 12, 572, 18, "style"], [622, 17, 572, 23], [622, 19, 572, 25], [623, 14, 572, 27, "backgroundColor"], [623, 29, 572, 42], [623, 31, 572, 44], [623, 51, 572, 64], [624, 14, 572, 66, "padding"], [624, 21, 572, 73], [624, 23, 572, 75], [624, 25, 572, 77], [625, 14, 572, 79, "borderRadius"], [625, 26, 572, 91], [625, 28, 572, 93], [625, 30, 572, 95], [626, 14, 572, 97, "alignItems"], [626, 24, 572, 107], [626, 26, 572, 109], [627, 12, 572, 118], [627, 13, 572, 120], [628, 12, 572, 120, "children"], [628, 20, 572, 120], [628, 36, 573, 14], [628, 40, 573, 14, "_jsxDevRuntime"], [628, 54, 573, 14], [628, 55, 573, 14, "jsxDEV"], [628, 61, 573, 14], [628, 63, 573, 15, "_ActivityIndicator"], [628, 81, 573, 15], [628, 82, 573, 15, "default"], [628, 89, 573, 32], [629, 14, 573, 33, "size"], [629, 18, 573, 37], [629, 20, 573, 38], [629, 27, 573, 45], [630, 14, 573, 46, "color"], [630, 19, 573, 51], [630, 21, 573, 52], [630, 30, 573, 61], [631, 14, 573, 62, "style"], [631, 19, 573, 67], [631, 21, 573, 69], [632, 16, 573, 71, "marginBottom"], [632, 28, 573, 83], [632, 30, 573, 85], [633, 14, 573, 88], [634, 12, 573, 90], [635, 14, 573, 90, "fileName"], [635, 22, 573, 90], [635, 24, 573, 90, "_jsxFileName"], [635, 36, 573, 90], [636, 14, 573, 90, "lineNumber"], [636, 24, 573, 90], [637, 14, 573, 90, "columnNumber"], [637, 26, 573, 90], [638, 12, 573, 90], [638, 19, 573, 92], [638, 20, 573, 93], [638, 35, 574, 14], [638, 39, 574, 14, "_jsxDevRuntime"], [638, 53, 574, 14], [638, 54, 574, 14, "jsxDEV"], [638, 60, 574, 14], [638, 62, 574, 15, "_Text"], [638, 67, 574, 15], [638, 68, 574, 15, "default"], [638, 75, 574, 19], [639, 14, 574, 20, "style"], [639, 19, 574, 25], [639, 21, 574, 27], [640, 16, 574, 29, "color"], [640, 21, 574, 34], [640, 23, 574, 36], [640, 29, 574, 42], [641, 16, 574, 44, "fontSize"], [641, 24, 574, 52], [641, 26, 574, 54], [641, 28, 574, 56], [642, 16, 574, 58, "fontWeight"], [642, 26, 574, 68], [642, 28, 574, 70], [643, 14, 574, 76], [643, 15, 574, 78], [644, 14, 574, 78, "children"], [644, 22, 574, 78], [644, 24, 574, 79], [645, 12, 574, 101], [646, 14, 574, 101, "fileName"], [646, 22, 574, 101], [646, 24, 574, 101, "_jsxFileName"], [646, 36, 574, 101], [647, 14, 574, 101, "lineNumber"], [647, 24, 574, 101], [648, 14, 574, 101, "columnNumber"], [648, 26, 574, 101], [649, 12, 574, 101], [649, 19, 574, 107], [649, 20, 574, 108], [649, 35, 575, 14], [649, 39, 575, 14, "_jsxDevRuntime"], [649, 53, 575, 14], [649, 54, 575, 14, "jsxDEV"], [649, 60, 575, 14], [649, 62, 575, 15, "_Text"], [649, 67, 575, 15], [649, 68, 575, 15, "default"], [649, 75, 575, 19], [650, 14, 575, 20, "style"], [650, 19, 575, 25], [650, 21, 575, 27], [651, 16, 575, 29, "color"], [651, 21, 575, 34], [651, 23, 575, 36], [651, 32, 575, 45], [652, 16, 575, 47, "fontSize"], [652, 24, 575, 55], [652, 26, 575, 57], [652, 28, 575, 59], [653, 16, 575, 61, "marginTop"], [653, 25, 575, 70], [653, 27, 575, 72], [654, 14, 575, 74], [654, 15, 575, 76], [655, 14, 575, 76, "children"], [655, 22, 575, 76], [655, 24, 575, 77], [656, 12, 575, 88], [657, 14, 575, 88, "fileName"], [657, 22, 575, 88], [657, 24, 575, 88, "_jsxFileName"], [657, 36, 575, 88], [658, 14, 575, 88, "lineNumber"], [658, 24, 575, 88], [659, 14, 575, 88, "columnNumber"], [659, 26, 575, 88], [660, 12, 575, 88], [660, 19, 575, 94], [660, 20, 575, 95], [661, 10, 575, 95], [662, 12, 575, 95, "fileName"], [662, 20, 575, 95], [662, 22, 575, 95, "_jsxFileName"], [662, 34, 575, 95], [663, 12, 575, 95, "lineNumber"], [663, 22, 575, 95], [664, 12, 575, 95, "columnNumber"], [664, 24, 575, 95], [665, 10, 575, 95], [665, 17, 576, 18], [666, 8, 576, 19], [667, 10, 576, 19, "fileName"], [667, 18, 576, 19], [667, 20, 576, 19, "_jsxFileName"], [667, 32, 576, 19], [668, 10, 576, 19, "lineNumber"], [668, 20, 576, 19], [669, 10, 576, 19, "columnNumber"], [669, 22, 576, 19], [670, 8, 576, 19], [670, 15, 577, 16], [670, 16, 578, 9], [670, 18, 581, 9, "isCameraReady"], [670, 31, 581, 22], [670, 35, 581, 26, "previewBlurEnabled"], [670, 53, 581, 44], [670, 57, 581, 48, "viewSize"], [670, 65, 581, 56], [670, 66, 581, 57, "width"], [670, 71, 581, 62], [670, 74, 581, 65], [670, 75, 581, 66], [670, 92, 582, 10], [670, 96, 582, 10, "_jsxDevRuntime"], [670, 110, 582, 10], [670, 111, 582, 10, "jsxDEV"], [670, 117, 582, 10], [670, 119, 582, 10, "_jsxDevRuntime"], [670, 133, 582, 10], [670, 134, 582, 10, "Fragment"], [670, 142, 582, 10], [671, 10, 582, 10, "children"], [671, 18, 582, 10], [671, 34, 584, 12], [671, 38, 584, 12, "_jsxDevRuntime"], [671, 52, 584, 12], [671, 53, 584, 12, "jsxDEV"], [671, 59, 584, 12], [671, 61, 584, 13, "_LiveFaceCanvas"], [671, 76, 584, 13], [671, 77, 584, 13, "default"], [671, 84, 584, 27], [672, 12, 584, 28, "containerId"], [672, 23, 584, 39], [672, 25, 584, 40], [672, 42, 584, 57], [673, 12, 584, 58, "width"], [673, 17, 584, 63], [673, 19, 584, 65, "viewSize"], [673, 27, 584, 73], [673, 28, 584, 74, "width"], [673, 33, 584, 80], [674, 12, 584, 81, "height"], [674, 18, 584, 87], [674, 20, 584, 89, "viewSize"], [674, 28, 584, 97], [674, 29, 584, 98, "height"], [675, 10, 584, 105], [676, 12, 584, 105, "fileName"], [676, 20, 584, 105], [676, 22, 584, 105, "_jsxFileName"], [676, 34, 584, 105], [677, 12, 584, 105, "lineNumber"], [677, 22, 584, 105], [678, 12, 584, 105, "columnNumber"], [678, 24, 584, 105], [679, 10, 584, 105], [679, 17, 584, 107], [679, 18, 584, 108], [679, 33, 585, 12], [679, 37, 585, 12, "_jsxDevRuntime"], [679, 51, 585, 12], [679, 52, 585, 12, "jsxDEV"], [679, 58, 585, 12], [679, 60, 585, 13, "_View"], [679, 65, 585, 13], [679, 66, 585, 13, "default"], [679, 73, 585, 17], [680, 12, 585, 18, "style"], [680, 17, 585, 23], [680, 19, 585, 25], [680, 20, 585, 26, "StyleSheet"], [680, 39, 585, 36], [680, 40, 585, 37, "absoluteFill"], [680, 52, 585, 49], [680, 54, 585, 51], [681, 14, 585, 53, "pointerEvents"], [681, 27, 585, 66], [681, 29, 585, 68], [682, 12, 585, 75], [682, 13, 585, 76], [682, 14, 585, 78], [683, 12, 585, 78, "children"], [683, 20, 585, 78], [683, 36, 587, 12], [683, 40, 587, 12, "_jsxDevRuntime"], [683, 54, 587, 12], [683, 55, 587, 12, "jsxDEV"], [683, 61, 587, 12], [683, 63, 587, 13, "_expoBlur"], [683, 72, 587, 13], [683, 73, 587, 13, "BlurView"], [683, 81, 587, 21], [684, 14, 587, 22, "intensity"], [684, 23, 587, 31], [684, 25, 587, 33], [684, 27, 587, 36], [685, 14, 587, 37, "tint"], [685, 18, 587, 41], [685, 20, 587, 42], [685, 26, 587, 48], [686, 14, 587, 49, "style"], [686, 19, 587, 54], [686, 21, 587, 56], [686, 22, 587, 57, "styles"], [686, 28, 587, 63], [686, 29, 587, 64, "blurZone"], [686, 37, 587, 72], [686, 39, 587, 74], [687, 16, 588, 14, "left"], [687, 20, 588, 18], [687, 22, 588, 20], [687, 23, 588, 21], [688, 16, 589, 14, "top"], [688, 19, 589, 17], [688, 21, 589, 19, "viewSize"], [688, 29, 589, 27], [688, 30, 589, 28, "height"], [688, 36, 589, 34], [688, 39, 589, 37], [688, 42, 589, 40], [689, 16, 590, 14, "width"], [689, 21, 590, 19], [689, 23, 590, 21, "viewSize"], [689, 31, 590, 29], [689, 32, 590, 30, "width"], [689, 37, 590, 35], [690, 16, 591, 14, "height"], [690, 22, 591, 20], [690, 24, 591, 22, "viewSize"], [690, 32, 591, 30], [690, 33, 591, 31, "height"], [690, 39, 591, 37], [690, 42, 591, 40], [690, 46, 591, 44], [691, 16, 592, 14, "borderRadius"], [691, 28, 592, 26], [691, 30, 592, 28], [692, 14, 593, 12], [692, 15, 593, 13], [693, 12, 593, 15], [694, 14, 593, 15, "fileName"], [694, 22, 593, 15], [694, 24, 593, 15, "_jsxFileName"], [694, 36, 593, 15], [695, 14, 593, 15, "lineNumber"], [695, 24, 593, 15], [696, 14, 593, 15, "columnNumber"], [696, 26, 593, 15], [697, 12, 593, 15], [697, 19, 593, 17], [697, 20, 593, 18], [697, 35, 595, 12], [697, 39, 595, 12, "_jsxDevRuntime"], [697, 53, 595, 12], [697, 54, 595, 12, "jsxDEV"], [697, 60, 595, 12], [697, 62, 595, 13, "_expoBlur"], [697, 71, 595, 13], [697, 72, 595, 13, "BlurView"], [697, 80, 595, 21], [698, 14, 595, 22, "intensity"], [698, 23, 595, 31], [698, 25, 595, 33], [698, 27, 595, 36], [699, 14, 595, 37, "tint"], [699, 18, 595, 41], [699, 20, 595, 42], [699, 26, 595, 48], [700, 14, 595, 49, "style"], [700, 19, 595, 54], [700, 21, 595, 56], [700, 22, 595, 57, "styles"], [700, 28, 595, 63], [700, 29, 595, 64, "blurZone"], [700, 37, 595, 72], [700, 39, 595, 74], [701, 16, 596, 14, "left"], [701, 20, 596, 18], [701, 22, 596, 20], [701, 23, 596, 21], [702, 16, 597, 14, "top"], [702, 19, 597, 17], [702, 21, 597, 19], [702, 22, 597, 20], [703, 16, 598, 14, "width"], [703, 21, 598, 19], [703, 23, 598, 21, "viewSize"], [703, 31, 598, 29], [703, 32, 598, 30, "width"], [703, 37, 598, 35], [704, 16, 599, 14, "height"], [704, 22, 599, 20], [704, 24, 599, 22, "viewSize"], [704, 32, 599, 30], [704, 33, 599, 31, "height"], [704, 39, 599, 37], [704, 42, 599, 40], [704, 45, 599, 43], [705, 16, 600, 14, "borderRadius"], [705, 28, 600, 26], [705, 30, 600, 28], [706, 14, 601, 12], [706, 15, 601, 13], [707, 12, 601, 15], [708, 14, 601, 15, "fileName"], [708, 22, 601, 15], [708, 24, 601, 15, "_jsxFileName"], [708, 36, 601, 15], [709, 14, 601, 15, "lineNumber"], [709, 24, 601, 15], [710, 14, 601, 15, "columnNumber"], [710, 26, 601, 15], [711, 12, 601, 15], [711, 19, 601, 17], [711, 20, 601, 18], [711, 35, 603, 12], [711, 39, 603, 12, "_jsxDevRuntime"], [711, 53, 603, 12], [711, 54, 603, 12, "jsxDEV"], [711, 60, 603, 12], [711, 62, 603, 13, "_expoBlur"], [711, 71, 603, 13], [711, 72, 603, 13, "BlurView"], [711, 80, 603, 21], [712, 14, 603, 22, "intensity"], [712, 23, 603, 31], [712, 25, 603, 33], [712, 27, 603, 36], [713, 14, 603, 37, "tint"], [713, 18, 603, 41], [713, 20, 603, 42], [713, 26, 603, 48], [714, 14, 603, 49, "style"], [714, 19, 603, 54], [714, 21, 603, 56], [714, 22, 603, 57, "styles"], [714, 28, 603, 63], [714, 29, 603, 64, "blurZone"], [714, 37, 603, 72], [714, 39, 603, 74], [715, 16, 604, 14, "left"], [715, 20, 604, 18], [715, 22, 604, 20, "viewSize"], [715, 30, 604, 28], [715, 31, 604, 29, "width"], [715, 36, 604, 34], [715, 39, 604, 37], [715, 42, 604, 40], [715, 45, 604, 44, "viewSize"], [715, 53, 604, 52], [715, 54, 604, 53, "width"], [715, 59, 604, 58], [715, 62, 604, 61], [715, 66, 604, 66], [716, 16, 605, 14, "top"], [716, 19, 605, 17], [716, 21, 605, 19, "viewSize"], [716, 29, 605, 27], [716, 30, 605, 28, "height"], [716, 36, 605, 34], [716, 39, 605, 37], [716, 43, 605, 41], [716, 46, 605, 45, "viewSize"], [716, 54, 605, 53], [716, 55, 605, 54, "width"], [716, 60, 605, 59], [716, 63, 605, 62], [716, 67, 605, 67], [717, 16, 606, 14, "width"], [717, 21, 606, 19], [717, 23, 606, 21, "viewSize"], [717, 31, 606, 29], [717, 32, 606, 30, "width"], [717, 37, 606, 35], [717, 40, 606, 38], [717, 43, 606, 41], [718, 16, 607, 14, "height"], [718, 22, 607, 20], [718, 24, 607, 22, "viewSize"], [718, 32, 607, 30], [718, 33, 607, 31, "width"], [718, 38, 607, 36], [718, 41, 607, 39], [718, 44, 607, 42], [719, 16, 608, 14, "borderRadius"], [719, 28, 608, 26], [719, 30, 608, 29, "viewSize"], [719, 38, 608, 37], [719, 39, 608, 38, "width"], [719, 44, 608, 43], [719, 47, 608, 46], [719, 50, 608, 49], [719, 53, 608, 53], [720, 14, 609, 12], [720, 15, 609, 13], [721, 12, 609, 15], [722, 14, 609, 15, "fileName"], [722, 22, 609, 15], [722, 24, 609, 15, "_jsxFileName"], [722, 36, 609, 15], [723, 14, 609, 15, "lineNumber"], [723, 24, 609, 15], [724, 14, 609, 15, "columnNumber"], [724, 26, 609, 15], [725, 12, 609, 15], [725, 19, 609, 17], [725, 20, 609, 18], [725, 35, 610, 12], [725, 39, 610, 12, "_jsxDevRuntime"], [725, 53, 610, 12], [725, 54, 610, 12, "jsxDEV"], [725, 60, 610, 12], [725, 62, 610, 13, "_expoBlur"], [725, 71, 610, 13], [725, 72, 610, 13, "BlurView"], [725, 80, 610, 21], [726, 14, 610, 22, "intensity"], [726, 23, 610, 31], [726, 25, 610, 33], [726, 27, 610, 36], [727, 14, 610, 37, "tint"], [727, 18, 610, 41], [727, 20, 610, 42], [727, 26, 610, 48], [728, 14, 610, 49, "style"], [728, 19, 610, 54], [728, 21, 610, 56], [728, 22, 610, 57, "styles"], [728, 28, 610, 63], [728, 29, 610, 64, "blurZone"], [728, 37, 610, 72], [728, 39, 610, 74], [729, 16, 611, 14, "left"], [729, 20, 611, 18], [729, 22, 611, 20, "viewSize"], [729, 30, 611, 28], [729, 31, 611, 29, "width"], [729, 36, 611, 34], [729, 39, 611, 37], [729, 42, 611, 40], [729, 45, 611, 44, "viewSize"], [729, 53, 611, 52], [729, 54, 611, 53, "width"], [729, 59, 611, 58], [729, 62, 611, 61], [729, 66, 611, 66], [730, 16, 612, 14, "top"], [730, 19, 612, 17], [730, 21, 612, 19, "viewSize"], [730, 29, 612, 27], [730, 30, 612, 28, "height"], [730, 36, 612, 34], [730, 39, 612, 37], [730, 42, 612, 40], [730, 45, 612, 44, "viewSize"], [730, 53, 612, 52], [730, 54, 612, 53, "width"], [730, 59, 612, 58], [730, 62, 612, 61], [730, 66, 612, 66], [731, 16, 613, 14, "width"], [731, 21, 613, 19], [731, 23, 613, 21, "viewSize"], [731, 31, 613, 29], [731, 32, 613, 30, "width"], [731, 37, 613, 35], [731, 40, 613, 38], [731, 43, 613, 41], [732, 16, 614, 14, "height"], [732, 22, 614, 20], [732, 24, 614, 22, "viewSize"], [732, 32, 614, 30], [732, 33, 614, 31, "width"], [732, 38, 614, 36], [732, 41, 614, 39], [732, 44, 614, 42], [733, 16, 615, 14, "borderRadius"], [733, 28, 615, 26], [733, 30, 615, 29, "viewSize"], [733, 38, 615, 37], [733, 39, 615, 38, "width"], [733, 44, 615, 43], [733, 47, 615, 46], [733, 50, 615, 49], [733, 53, 615, 53], [734, 14, 616, 12], [734, 15, 616, 13], [735, 12, 616, 15], [736, 14, 616, 15, "fileName"], [736, 22, 616, 15], [736, 24, 616, 15, "_jsxFileName"], [736, 36, 616, 15], [737, 14, 616, 15, "lineNumber"], [737, 24, 616, 15], [738, 14, 616, 15, "columnNumber"], [738, 26, 616, 15], [739, 12, 616, 15], [739, 19, 616, 17], [739, 20, 616, 18], [739, 35, 617, 12], [739, 39, 617, 12, "_jsxDevRuntime"], [739, 53, 617, 12], [739, 54, 617, 12, "jsxDEV"], [739, 60, 617, 12], [739, 62, 617, 13, "_expoBlur"], [739, 71, 617, 13], [739, 72, 617, 13, "BlurView"], [739, 80, 617, 21], [740, 14, 617, 22, "intensity"], [740, 23, 617, 31], [740, 25, 617, 33], [740, 27, 617, 36], [741, 14, 617, 37, "tint"], [741, 18, 617, 41], [741, 20, 617, 42], [741, 26, 617, 48], [742, 14, 617, 49, "style"], [742, 19, 617, 54], [742, 21, 617, 56], [742, 22, 617, 57, "styles"], [742, 28, 617, 63], [742, 29, 617, 64, "blurZone"], [742, 37, 617, 72], [742, 39, 617, 74], [743, 16, 618, 14, "left"], [743, 20, 618, 18], [743, 22, 618, 20, "viewSize"], [743, 30, 618, 28], [743, 31, 618, 29, "width"], [743, 36, 618, 34], [743, 39, 618, 37], [743, 42, 618, 40], [743, 45, 618, 44, "viewSize"], [743, 53, 618, 52], [743, 54, 618, 53, "width"], [743, 59, 618, 58], [743, 62, 618, 61], [743, 66, 618, 66], [744, 16, 619, 14, "top"], [744, 19, 619, 17], [744, 21, 619, 19, "viewSize"], [744, 29, 619, 27], [744, 30, 619, 28, "height"], [744, 36, 619, 34], [744, 39, 619, 37], [744, 42, 619, 40], [744, 45, 619, 44, "viewSize"], [744, 53, 619, 52], [744, 54, 619, 53, "width"], [744, 59, 619, 58], [744, 62, 619, 61], [744, 66, 619, 66], [745, 16, 620, 14, "width"], [745, 21, 620, 19], [745, 23, 620, 21, "viewSize"], [745, 31, 620, 29], [745, 32, 620, 30, "width"], [745, 37, 620, 35], [745, 40, 620, 38], [745, 43, 620, 41], [746, 16, 621, 14, "height"], [746, 22, 621, 20], [746, 24, 621, 22, "viewSize"], [746, 32, 621, 30], [746, 33, 621, 31, "width"], [746, 38, 621, 36], [746, 41, 621, 39], [746, 44, 621, 42], [747, 16, 622, 14, "borderRadius"], [747, 28, 622, 26], [747, 30, 622, 29, "viewSize"], [747, 38, 622, 37], [747, 39, 622, 38, "width"], [747, 44, 622, 43], [747, 47, 622, 46], [747, 50, 622, 49], [747, 53, 622, 53], [748, 14, 623, 12], [748, 15, 623, 13], [749, 12, 623, 15], [750, 14, 623, 15, "fileName"], [750, 22, 623, 15], [750, 24, 623, 15, "_jsxFileName"], [750, 36, 623, 15], [751, 14, 623, 15, "lineNumber"], [751, 24, 623, 15], [752, 14, 623, 15, "columnNumber"], [752, 26, 623, 15], [753, 12, 623, 15], [753, 19, 623, 17], [753, 20, 623, 18], [753, 22, 625, 13, "__DEV__"], [753, 29, 625, 20], [753, 46, 626, 14], [753, 50, 626, 14, "_jsxDevRuntime"], [753, 64, 626, 14], [753, 65, 626, 14, "jsxDEV"], [753, 71, 626, 14], [753, 73, 626, 15, "_View"], [753, 78, 626, 15], [753, 79, 626, 15, "default"], [753, 86, 626, 19], [754, 14, 626, 20, "style"], [754, 19, 626, 25], [754, 21, 626, 27, "styles"], [754, 27, 626, 33], [754, 28, 626, 34, "previewChip"], [754, 39, 626, 46], [755, 14, 626, 46, "children"], [755, 22, 626, 46], [755, 37, 627, 16], [755, 41, 627, 16, "_jsxDevRuntime"], [755, 55, 627, 16], [755, 56, 627, 16, "jsxDEV"], [755, 62, 627, 16], [755, 64, 627, 17, "_Text"], [755, 69, 627, 17], [755, 70, 627, 17, "default"], [755, 77, 627, 21], [756, 16, 627, 22, "style"], [756, 21, 627, 27], [756, 23, 627, 29, "styles"], [756, 29, 627, 35], [756, 30, 627, 36, "previewChipText"], [756, 45, 627, 52], [757, 16, 627, 52, "children"], [757, 24, 627, 52], [757, 26, 627, 53], [758, 14, 627, 73], [759, 16, 627, 73, "fileName"], [759, 24, 627, 73], [759, 26, 627, 73, "_jsxFileName"], [759, 38, 627, 73], [760, 16, 627, 73, "lineNumber"], [760, 26, 627, 73], [761, 16, 627, 73, "columnNumber"], [761, 28, 627, 73], [762, 14, 627, 73], [762, 21, 627, 79], [763, 12, 627, 80], [764, 14, 627, 80, "fileName"], [764, 22, 627, 80], [764, 24, 627, 80, "_jsxFileName"], [764, 36, 627, 80], [765, 14, 627, 80, "lineNumber"], [765, 24, 627, 80], [766, 14, 627, 80, "columnNumber"], [766, 26, 627, 80], [767, 12, 627, 80], [767, 19, 628, 20], [767, 20, 629, 13], [768, 10, 629, 13], [769, 12, 629, 13, "fileName"], [769, 20, 629, 13], [769, 22, 629, 13, "_jsxFileName"], [769, 34, 629, 13], [770, 12, 629, 13, "lineNumber"], [770, 22, 629, 13], [771, 12, 629, 13, "columnNumber"], [771, 24, 629, 13], [772, 10, 629, 13], [772, 17, 630, 18], [772, 18, 630, 19], [773, 8, 630, 19], [773, 23, 631, 12], [773, 24, 632, 9], [773, 26, 634, 9, "isCameraReady"], [773, 39, 634, 22], [773, 56, 635, 10], [773, 60, 635, 10, "_jsxDevRuntime"], [773, 74, 635, 10], [773, 75, 635, 10, "jsxDEV"], [773, 81, 635, 10], [773, 83, 635, 10, "_jsxDevRuntime"], [773, 97, 635, 10], [773, 98, 635, 10, "Fragment"], [773, 106, 635, 10], [774, 10, 635, 10, "children"], [774, 18, 635, 10], [774, 34, 637, 12], [774, 38, 637, 12, "_jsxDevRuntime"], [774, 52, 637, 12], [774, 53, 637, 12, "jsxDEV"], [774, 59, 637, 12], [774, 61, 637, 13, "_View"], [774, 66, 637, 13], [774, 67, 637, 13, "default"], [774, 74, 637, 17], [775, 12, 637, 18, "style"], [775, 17, 637, 23], [775, 19, 637, 25, "styles"], [775, 25, 637, 31], [775, 26, 637, 32, "headerOverlay"], [775, 39, 637, 46], [776, 12, 637, 46, "children"], [776, 20, 637, 46], [776, 35, 638, 14], [776, 39, 638, 14, "_jsxDevRuntime"], [776, 53, 638, 14], [776, 54, 638, 14, "jsxDEV"], [776, 60, 638, 14], [776, 62, 638, 15, "_View"], [776, 67, 638, 15], [776, 68, 638, 15, "default"], [776, 75, 638, 19], [777, 14, 638, 20, "style"], [777, 19, 638, 25], [777, 21, 638, 27, "styles"], [777, 27, 638, 33], [777, 28, 638, 34, "headerContent"], [777, 41, 638, 48], [778, 14, 638, 48, "children"], [778, 22, 638, 48], [778, 38, 639, 16], [778, 42, 639, 16, "_jsxDevRuntime"], [778, 56, 639, 16], [778, 57, 639, 16, "jsxDEV"], [778, 63, 639, 16], [778, 65, 639, 17, "_View"], [778, 70, 639, 17], [778, 71, 639, 17, "default"], [778, 78, 639, 21], [779, 16, 639, 22, "style"], [779, 21, 639, 27], [779, 23, 639, 29, "styles"], [779, 29, 639, 35], [779, 30, 639, 36, "headerLeft"], [779, 40, 639, 47], [780, 16, 639, 47, "children"], [780, 24, 639, 47], [780, 40, 640, 18], [780, 44, 640, 18, "_jsxDevRuntime"], [780, 58, 640, 18], [780, 59, 640, 18, "jsxDEV"], [780, 65, 640, 18], [780, 67, 640, 19, "_Text"], [780, 72, 640, 19], [780, 73, 640, 19, "default"], [780, 80, 640, 23], [781, 18, 640, 24, "style"], [781, 23, 640, 29], [781, 25, 640, 31, "styles"], [781, 31, 640, 37], [781, 32, 640, 38, "headerTitle"], [781, 43, 640, 50], [782, 18, 640, 50, "children"], [782, 26, 640, 50], [782, 28, 640, 51], [783, 16, 640, 62], [784, 18, 640, 62, "fileName"], [784, 26, 640, 62], [784, 28, 640, 62, "_jsxFileName"], [784, 40, 640, 62], [785, 18, 640, 62, "lineNumber"], [785, 28, 640, 62], [786, 18, 640, 62, "columnNumber"], [786, 30, 640, 62], [787, 16, 640, 62], [787, 23, 640, 68], [787, 24, 640, 69], [787, 39, 641, 18], [787, 43, 641, 18, "_jsxDevRuntime"], [787, 57, 641, 18], [787, 58, 641, 18, "jsxDEV"], [787, 64, 641, 18], [787, 66, 641, 19, "_View"], [787, 71, 641, 19], [787, 72, 641, 19, "default"], [787, 79, 641, 23], [788, 18, 641, 24, "style"], [788, 23, 641, 29], [788, 25, 641, 31, "styles"], [788, 31, 641, 37], [788, 32, 641, 38, "subtitleRow"], [788, 43, 641, 50], [789, 18, 641, 50, "children"], [789, 26, 641, 50], [789, 42, 642, 20], [789, 46, 642, 20, "_jsxDevRuntime"], [789, 60, 642, 20], [789, 61, 642, 20, "jsxDEV"], [789, 67, 642, 20], [789, 69, 642, 21, "_Text"], [789, 74, 642, 21], [789, 75, 642, 21, "default"], [789, 82, 642, 25], [790, 20, 642, 26, "style"], [790, 25, 642, 31], [790, 27, 642, 33, "styles"], [790, 33, 642, 39], [790, 34, 642, 40, "webIcon"], [790, 41, 642, 48], [791, 20, 642, 48, "children"], [791, 28, 642, 48], [791, 30, 642, 49], [792, 18, 642, 51], [793, 20, 642, 51, "fileName"], [793, 28, 642, 51], [793, 30, 642, 51, "_jsxFileName"], [793, 42, 642, 51], [794, 20, 642, 51, "lineNumber"], [794, 30, 642, 51], [795, 20, 642, 51, "columnNumber"], [795, 32, 642, 51], [796, 18, 642, 51], [796, 25, 642, 57], [796, 26, 642, 58], [796, 41, 643, 20], [796, 45, 643, 20, "_jsxDevRuntime"], [796, 59, 643, 20], [796, 60, 643, 20, "jsxDEV"], [796, 66, 643, 20], [796, 68, 643, 21, "_Text"], [796, 73, 643, 21], [796, 74, 643, 21, "default"], [796, 81, 643, 25], [797, 20, 643, 26, "style"], [797, 25, 643, 31], [797, 27, 643, 33, "styles"], [797, 33, 643, 39], [797, 34, 643, 40, "headerSubtitle"], [797, 48, 643, 55], [798, 20, 643, 55, "children"], [798, 28, 643, 55], [798, 30, 643, 56], [799, 18, 643, 71], [800, 20, 643, 71, "fileName"], [800, 28, 643, 71], [800, 30, 643, 71, "_jsxFileName"], [800, 42, 643, 71], [801, 20, 643, 71, "lineNumber"], [801, 30, 643, 71], [802, 20, 643, 71, "columnNumber"], [802, 32, 643, 71], [803, 18, 643, 71], [803, 25, 643, 77], [803, 26, 643, 78], [804, 16, 643, 78], [805, 18, 643, 78, "fileName"], [805, 26, 643, 78], [805, 28, 643, 78, "_jsxFileName"], [805, 40, 643, 78], [806, 18, 643, 78, "lineNumber"], [806, 28, 643, 78], [807, 18, 643, 78, "columnNumber"], [807, 30, 643, 78], [808, 16, 643, 78], [808, 23, 644, 24], [808, 24, 644, 25], [808, 26, 645, 19, "challengeCode"], [808, 39, 645, 32], [808, 56, 646, 20], [808, 60, 646, 20, "_jsxDevRuntime"], [808, 74, 646, 20], [808, 75, 646, 20, "jsxDEV"], [808, 81, 646, 20], [808, 83, 646, 21, "_View"], [808, 88, 646, 21], [808, 89, 646, 21, "default"], [808, 96, 646, 25], [809, 18, 646, 26, "style"], [809, 23, 646, 31], [809, 25, 646, 33, "styles"], [809, 31, 646, 39], [809, 32, 646, 40, "challengeRow"], [809, 44, 646, 53], [810, 18, 646, 53, "children"], [810, 26, 646, 53], [810, 42, 647, 22], [810, 46, 647, 22, "_jsxDevRuntime"], [810, 60, 647, 22], [810, 61, 647, 22, "jsxDEV"], [810, 67, 647, 22], [810, 69, 647, 23, "_lucideReactNative"], [810, 87, 647, 23], [810, 88, 647, 23, "Shield"], [810, 94, 647, 29], [811, 20, 647, 30, "size"], [811, 24, 647, 34], [811, 26, 647, 36], [811, 28, 647, 39], [812, 20, 647, 40, "color"], [812, 25, 647, 45], [812, 27, 647, 46], [813, 18, 647, 52], [814, 20, 647, 52, "fileName"], [814, 28, 647, 52], [814, 30, 647, 52, "_jsxFileName"], [814, 42, 647, 52], [815, 20, 647, 52, "lineNumber"], [815, 30, 647, 52], [816, 20, 647, 52, "columnNumber"], [816, 32, 647, 52], [817, 18, 647, 52], [817, 25, 647, 54], [817, 26, 647, 55], [817, 41, 648, 22], [817, 45, 648, 22, "_jsxDevRuntime"], [817, 59, 648, 22], [817, 60, 648, 22, "jsxDEV"], [817, 66, 648, 22], [817, 68, 648, 23, "_Text"], [817, 73, 648, 23], [817, 74, 648, 23, "default"], [817, 81, 648, 27], [818, 20, 648, 28, "style"], [818, 25, 648, 33], [818, 27, 648, 35, "styles"], [818, 33, 648, 41], [818, 34, 648, 42, "challengeCode"], [818, 47, 648, 56], [819, 20, 648, 56, "children"], [819, 28, 648, 56], [819, 30, 648, 58, "challengeCode"], [820, 18, 648, 71], [821, 20, 648, 71, "fileName"], [821, 28, 648, 71], [821, 30, 648, 71, "_jsxFileName"], [821, 42, 648, 71], [822, 20, 648, 71, "lineNumber"], [822, 30, 648, 71], [823, 20, 648, 71, "columnNumber"], [823, 32, 648, 71], [824, 18, 648, 71], [824, 25, 648, 78], [824, 26, 648, 79], [825, 16, 648, 79], [826, 18, 648, 79, "fileName"], [826, 26, 648, 79], [826, 28, 648, 79, "_jsxFileName"], [826, 40, 648, 79], [827, 18, 648, 79, "lineNumber"], [827, 28, 648, 79], [828, 18, 648, 79, "columnNumber"], [828, 30, 648, 79], [829, 16, 648, 79], [829, 23, 649, 26], [829, 24, 650, 19], [830, 14, 650, 19], [831, 16, 650, 19, "fileName"], [831, 24, 650, 19], [831, 26, 650, 19, "_jsxFileName"], [831, 38, 650, 19], [832, 16, 650, 19, "lineNumber"], [832, 26, 650, 19], [833, 16, 650, 19, "columnNumber"], [833, 28, 650, 19], [834, 14, 650, 19], [834, 21, 651, 22], [834, 22, 651, 23], [834, 37, 652, 16], [834, 41, 652, 16, "_jsxDevRuntime"], [834, 55, 652, 16], [834, 56, 652, 16, "jsxDEV"], [834, 62, 652, 16], [834, 64, 652, 17, "_TouchableOpacity"], [834, 81, 652, 17], [834, 82, 652, 17, "default"], [834, 89, 652, 33], [835, 16, 652, 34, "onPress"], [835, 23, 652, 41], [835, 25, 652, 43, "onCancel"], [835, 33, 652, 52], [836, 16, 652, 53, "style"], [836, 21, 652, 58], [836, 23, 652, 60, "styles"], [836, 29, 652, 66], [836, 30, 652, 67, "closeButton"], [836, 41, 652, 79], [837, 16, 652, 79, "children"], [837, 24, 652, 79], [837, 39, 653, 18], [837, 43, 653, 18, "_jsxDevRuntime"], [837, 57, 653, 18], [837, 58, 653, 18, "jsxDEV"], [837, 64, 653, 18], [837, 66, 653, 19, "_lucideReactNative"], [837, 84, 653, 19], [837, 85, 653, 19, "X"], [837, 86, 653, 20], [838, 18, 653, 21, "size"], [838, 22, 653, 25], [838, 24, 653, 27], [838, 26, 653, 30], [839, 18, 653, 31, "color"], [839, 23, 653, 36], [839, 25, 653, 37], [840, 16, 653, 43], [841, 18, 653, 43, "fileName"], [841, 26, 653, 43], [841, 28, 653, 43, "_jsxFileName"], [841, 40, 653, 43], [842, 18, 653, 43, "lineNumber"], [842, 28, 653, 43], [843, 18, 653, 43, "columnNumber"], [843, 30, 653, 43], [844, 16, 653, 43], [844, 23, 653, 45], [845, 14, 653, 46], [846, 16, 653, 46, "fileName"], [846, 24, 653, 46], [846, 26, 653, 46, "_jsxFileName"], [846, 38, 653, 46], [847, 16, 653, 46, "lineNumber"], [847, 26, 653, 46], [848, 16, 653, 46, "columnNumber"], [848, 28, 653, 46], [849, 14, 653, 46], [849, 21, 654, 34], [849, 22, 654, 35], [850, 12, 654, 35], [851, 14, 654, 35, "fileName"], [851, 22, 654, 35], [851, 24, 654, 35, "_jsxFileName"], [851, 36, 654, 35], [852, 14, 654, 35, "lineNumber"], [852, 24, 654, 35], [853, 14, 654, 35, "columnNumber"], [853, 26, 654, 35], [854, 12, 654, 35], [854, 19, 655, 20], [855, 10, 655, 21], [856, 12, 655, 21, "fileName"], [856, 20, 655, 21], [856, 22, 655, 21, "_jsxFileName"], [856, 34, 655, 21], [857, 12, 655, 21, "lineNumber"], [857, 22, 655, 21], [858, 12, 655, 21, "columnNumber"], [858, 24, 655, 21], [859, 10, 655, 21], [859, 17, 656, 18], [859, 18, 656, 19], [859, 33, 658, 12], [859, 37, 658, 12, "_jsxDevRuntime"], [859, 51, 658, 12], [859, 52, 658, 12, "jsxDEV"], [859, 58, 658, 12], [859, 60, 658, 13, "_View"], [859, 65, 658, 13], [859, 66, 658, 13, "default"], [859, 73, 658, 17], [860, 12, 658, 18, "style"], [860, 17, 658, 23], [860, 19, 658, 25, "styles"], [860, 25, 658, 31], [860, 26, 658, 32, "privacyNotice"], [860, 39, 658, 46], [861, 12, 658, 46, "children"], [861, 20, 658, 46], [861, 36, 659, 14], [861, 40, 659, 14, "_jsxDevRuntime"], [861, 54, 659, 14], [861, 55, 659, 14, "jsxDEV"], [861, 61, 659, 14], [861, 63, 659, 15, "_lucideReactNative"], [861, 81, 659, 15], [861, 82, 659, 15, "Shield"], [861, 88, 659, 21], [862, 14, 659, 22, "size"], [862, 18, 659, 26], [862, 20, 659, 28], [862, 22, 659, 31], [863, 14, 659, 32, "color"], [863, 19, 659, 37], [863, 21, 659, 38], [864, 12, 659, 47], [865, 14, 659, 47, "fileName"], [865, 22, 659, 47], [865, 24, 659, 47, "_jsxFileName"], [865, 36, 659, 47], [866, 14, 659, 47, "lineNumber"], [866, 24, 659, 47], [867, 14, 659, 47, "columnNumber"], [867, 26, 659, 47], [868, 12, 659, 47], [868, 19, 659, 49], [868, 20, 659, 50], [868, 35, 660, 14], [868, 39, 660, 14, "_jsxDevRuntime"], [868, 53, 660, 14], [868, 54, 660, 14, "jsxDEV"], [868, 60, 660, 14], [868, 62, 660, 15, "_Text"], [868, 67, 660, 15], [868, 68, 660, 15, "default"], [868, 75, 660, 19], [869, 14, 660, 20, "style"], [869, 19, 660, 25], [869, 21, 660, 27, "styles"], [869, 27, 660, 33], [869, 28, 660, 34, "privacyText"], [869, 39, 660, 46], [870, 14, 660, 46, "children"], [870, 22, 660, 46], [870, 24, 660, 47], [871, 12, 662, 14], [872, 14, 662, 14, "fileName"], [872, 22, 662, 14], [872, 24, 662, 14, "_jsxFileName"], [872, 36, 662, 14], [873, 14, 662, 14, "lineNumber"], [873, 24, 662, 14], [874, 14, 662, 14, "columnNumber"], [874, 26, 662, 14], [875, 12, 662, 14], [875, 19, 662, 20], [875, 20, 662, 21], [876, 10, 662, 21], [877, 12, 662, 21, "fileName"], [877, 20, 662, 21], [877, 22, 662, 21, "_jsxFileName"], [877, 34, 662, 21], [878, 12, 662, 21, "lineNumber"], [878, 22, 662, 21], [879, 12, 662, 21, "columnNumber"], [879, 24, 662, 21], [880, 10, 662, 21], [880, 17, 663, 18], [880, 18, 663, 19], [880, 33, 665, 12], [880, 37, 665, 12, "_jsxDevRuntime"], [880, 51, 665, 12], [880, 52, 665, 12, "jsxDEV"], [880, 58, 665, 12], [880, 60, 665, 13, "_View"], [880, 65, 665, 13], [880, 66, 665, 13, "default"], [880, 73, 665, 17], [881, 12, 665, 18, "style"], [881, 17, 665, 23], [881, 19, 665, 25, "styles"], [881, 25, 665, 31], [881, 26, 665, 32, "footer<PERSON><PERSON><PERSON>"], [881, 39, 665, 46], [882, 12, 665, 46, "children"], [882, 20, 665, 46], [882, 36, 666, 14], [882, 40, 666, 14, "_jsxDevRuntime"], [882, 54, 666, 14], [882, 55, 666, 14, "jsxDEV"], [882, 61, 666, 14], [882, 63, 666, 15, "_Text"], [882, 68, 666, 15], [882, 69, 666, 15, "default"], [882, 76, 666, 19], [883, 14, 666, 20, "style"], [883, 19, 666, 25], [883, 21, 666, 27, "styles"], [883, 27, 666, 33], [883, 28, 666, 34, "instruction"], [883, 39, 666, 46], [884, 14, 666, 46, "children"], [884, 22, 666, 46], [884, 24, 666, 47], [885, 12, 668, 14], [886, 14, 668, 14, "fileName"], [886, 22, 668, 14], [886, 24, 668, 14, "_jsxFileName"], [886, 36, 668, 14], [887, 14, 668, 14, "lineNumber"], [887, 24, 668, 14], [888, 14, 668, 14, "columnNumber"], [888, 26, 668, 14], [889, 12, 668, 14], [889, 19, 668, 20], [889, 20, 668, 21], [889, 35, 670, 14], [889, 39, 670, 14, "_jsxDevRuntime"], [889, 53, 670, 14], [889, 54, 670, 14, "jsxDEV"], [889, 60, 670, 14], [889, 62, 670, 15, "_TouchableOpacity"], [889, 79, 670, 15], [889, 80, 670, 15, "default"], [889, 87, 670, 31], [890, 14, 671, 16, "onPress"], [890, 21, 671, 23], [890, 23, 671, 25, "capturePhoto"], [890, 35, 671, 38], [891, 14, 672, 16, "disabled"], [891, 22, 672, 24], [891, 24, 672, 26, "processingState"], [891, 39, 672, 41], [891, 44, 672, 46], [891, 50, 672, 52], [891, 54, 672, 56], [891, 55, 672, 57, "isCameraReady"], [891, 68, 672, 71], [892, 14, 673, 16, "style"], [892, 19, 673, 21], [892, 21, 673, 23], [892, 22, 674, 18, "styles"], [892, 28, 674, 24], [892, 29, 674, 25, "shutterButton"], [892, 42, 674, 38], [892, 44, 675, 18, "processingState"], [892, 59, 675, 33], [892, 64, 675, 38], [892, 70, 675, 44], [892, 74, 675, 48, "styles"], [892, 80, 675, 54], [892, 81, 675, 55, "shutterButtonDisabled"], [892, 102, 675, 76], [892, 103, 676, 18], [893, 14, 676, 18, "children"], [893, 22, 676, 18], [893, 24, 678, 17, "processingState"], [893, 39, 678, 32], [893, 44, 678, 37], [893, 50, 678, 43], [893, 66, 679, 18], [893, 70, 679, 18, "_jsxDevRuntime"], [893, 84, 679, 18], [893, 85, 679, 18, "jsxDEV"], [893, 91, 679, 18], [893, 93, 679, 19, "_View"], [893, 98, 679, 19], [893, 99, 679, 19, "default"], [893, 106, 679, 23], [894, 16, 679, 24, "style"], [894, 21, 679, 29], [894, 23, 679, 31, "styles"], [894, 29, 679, 37], [894, 30, 679, 38, "shutterInner"], [895, 14, 679, 51], [896, 16, 679, 51, "fileName"], [896, 24, 679, 51], [896, 26, 679, 51, "_jsxFileName"], [896, 38, 679, 51], [897, 16, 679, 51, "lineNumber"], [897, 26, 679, 51], [898, 16, 679, 51, "columnNumber"], [898, 28, 679, 51], [899, 14, 679, 51], [899, 21, 679, 53], [899, 22, 679, 54], [899, 38, 681, 18], [899, 42, 681, 18, "_jsxDevRuntime"], [899, 56, 681, 18], [899, 57, 681, 18, "jsxDEV"], [899, 63, 681, 18], [899, 65, 681, 19, "_ActivityIndicator"], [899, 83, 681, 19], [899, 84, 681, 19, "default"], [899, 91, 681, 36], [900, 16, 681, 37, "size"], [900, 20, 681, 41], [900, 22, 681, 42], [900, 29, 681, 49], [901, 16, 681, 50, "color"], [901, 21, 681, 55], [901, 23, 681, 56], [902, 14, 681, 65], [903, 16, 681, 65, "fileName"], [903, 24, 681, 65], [903, 26, 681, 65, "_jsxFileName"], [903, 38, 681, 65], [904, 16, 681, 65, "lineNumber"], [904, 26, 681, 65], [905, 16, 681, 65, "columnNumber"], [905, 28, 681, 65], [906, 14, 681, 65], [906, 21, 681, 67], [907, 12, 682, 17], [908, 14, 682, 17, "fileName"], [908, 22, 682, 17], [908, 24, 682, 17, "_jsxFileName"], [908, 36, 682, 17], [909, 14, 682, 17, "lineNumber"], [909, 24, 682, 17], [910, 14, 682, 17, "columnNumber"], [910, 26, 682, 17], [911, 12, 682, 17], [911, 19, 683, 32], [911, 20, 683, 33], [911, 35, 684, 14], [911, 39, 684, 14, "_jsxDevRuntime"], [911, 53, 684, 14], [911, 54, 684, 14, "jsxDEV"], [911, 60, 684, 14], [911, 62, 684, 15, "_Text"], [911, 67, 684, 15], [911, 68, 684, 15, "default"], [911, 75, 684, 19], [912, 14, 684, 20, "style"], [912, 19, 684, 25], [912, 21, 684, 27, "styles"], [912, 27, 684, 33], [912, 28, 684, 34, "privacyNote"], [912, 39, 684, 46], [913, 14, 684, 46, "children"], [913, 22, 684, 46], [913, 24, 684, 47], [914, 12, 686, 14], [915, 14, 686, 14, "fileName"], [915, 22, 686, 14], [915, 24, 686, 14, "_jsxFileName"], [915, 36, 686, 14], [916, 14, 686, 14, "lineNumber"], [916, 24, 686, 14], [917, 14, 686, 14, "columnNumber"], [917, 26, 686, 14], [918, 12, 686, 14], [918, 19, 686, 20], [918, 20, 686, 21], [919, 10, 686, 21], [920, 12, 686, 21, "fileName"], [920, 20, 686, 21], [920, 22, 686, 21, "_jsxFileName"], [920, 34, 686, 21], [921, 12, 686, 21, "lineNumber"], [921, 22, 686, 21], [922, 12, 686, 21, "columnNumber"], [922, 24, 686, 21], [923, 10, 686, 21], [923, 17, 687, 18], [923, 18, 687, 19], [924, 8, 687, 19], [924, 23, 688, 12], [924, 24, 689, 9], [925, 6, 689, 9], [926, 8, 689, 9, "fileName"], [926, 16, 689, 9], [926, 18, 689, 9, "_jsxFileName"], [926, 30, 689, 9], [927, 8, 689, 9, "lineNumber"], [927, 18, 689, 9], [928, 8, 689, 9, "columnNumber"], [928, 20, 689, 9], [929, 6, 689, 9], [929, 13, 690, 12], [929, 14, 690, 13], [929, 29, 692, 6], [929, 33, 692, 6, "_jsxDevRuntime"], [929, 47, 692, 6], [929, 48, 692, 6, "jsxDEV"], [929, 54, 692, 6], [929, 56, 692, 7, "_Modal"], [929, 62, 692, 7], [929, 63, 692, 7, "default"], [929, 70, 692, 12], [930, 8, 693, 8, "visible"], [930, 15, 693, 15], [930, 17, 693, 17, "processingState"], [930, 32, 693, 32], [930, 37, 693, 37], [930, 43, 693, 43], [930, 47, 693, 47, "processingState"], [930, 62, 693, 62], [930, 67, 693, 67], [930, 74, 693, 75], [931, 8, 694, 8, "transparent"], [931, 19, 694, 19], [932, 8, 695, 8, "animationType"], [932, 21, 695, 21], [932, 23, 695, 22], [932, 29, 695, 28], [933, 8, 695, 28, "children"], [933, 16, 695, 28], [933, 31, 697, 8], [933, 35, 697, 8, "_jsxDevRuntime"], [933, 49, 697, 8], [933, 50, 697, 8, "jsxDEV"], [933, 56, 697, 8], [933, 58, 697, 9, "_View"], [933, 63, 697, 9], [933, 64, 697, 9, "default"], [933, 71, 697, 13], [934, 10, 697, 14, "style"], [934, 15, 697, 19], [934, 17, 697, 21, "styles"], [934, 23, 697, 27], [934, 24, 697, 28, "processingModal"], [934, 39, 697, 44], [935, 10, 697, 44, "children"], [935, 18, 697, 44], [935, 33, 698, 10], [935, 37, 698, 10, "_jsxDevRuntime"], [935, 51, 698, 10], [935, 52, 698, 10, "jsxDEV"], [935, 58, 698, 10], [935, 60, 698, 11, "_View"], [935, 65, 698, 11], [935, 66, 698, 11, "default"], [935, 73, 698, 15], [936, 12, 698, 16, "style"], [936, 17, 698, 21], [936, 19, 698, 23, "styles"], [936, 25, 698, 29], [936, 26, 698, 30, "processingContent"], [936, 43, 698, 48], [937, 12, 698, 48, "children"], [937, 20, 698, 48], [937, 36, 699, 12], [937, 40, 699, 12, "_jsxDevRuntime"], [937, 54, 699, 12], [937, 55, 699, 12, "jsxDEV"], [937, 61, 699, 12], [937, 63, 699, 13, "_ActivityIndicator"], [937, 81, 699, 13], [937, 82, 699, 13, "default"], [937, 89, 699, 30], [938, 14, 699, 31, "size"], [938, 18, 699, 35], [938, 20, 699, 36], [938, 27, 699, 43], [939, 14, 699, 44, "color"], [939, 19, 699, 49], [939, 21, 699, 50], [940, 12, 699, 59], [941, 14, 699, 59, "fileName"], [941, 22, 699, 59], [941, 24, 699, 59, "_jsxFileName"], [941, 36, 699, 59], [942, 14, 699, 59, "lineNumber"], [942, 24, 699, 59], [943, 14, 699, 59, "columnNumber"], [943, 26, 699, 59], [944, 12, 699, 59], [944, 19, 699, 61], [944, 20, 699, 62], [944, 35, 701, 12], [944, 39, 701, 12, "_jsxDevRuntime"], [944, 53, 701, 12], [944, 54, 701, 12, "jsxDEV"], [944, 60, 701, 12], [944, 62, 701, 13, "_Text"], [944, 67, 701, 13], [944, 68, 701, 13, "default"], [944, 75, 701, 17], [945, 14, 701, 18, "style"], [945, 19, 701, 23], [945, 21, 701, 25, "styles"], [945, 27, 701, 31], [945, 28, 701, 32, "processingTitle"], [945, 43, 701, 48], [946, 14, 701, 48, "children"], [946, 22, 701, 48], [946, 25, 702, 15, "processingState"], [946, 40, 702, 30], [946, 45, 702, 35], [946, 56, 702, 46], [946, 60, 702, 50], [946, 80, 702, 70], [946, 82, 703, 15, "processingState"], [946, 97, 703, 30], [946, 102, 703, 35], [946, 113, 703, 46], [946, 117, 703, 50], [946, 146, 703, 79], [946, 148, 704, 15, "processingState"], [946, 163, 704, 30], [946, 168, 704, 35], [946, 180, 704, 47], [946, 184, 704, 51], [946, 216, 704, 83], [946, 218, 705, 15, "processingState"], [946, 233, 705, 30], [946, 238, 705, 35], [946, 249, 705, 46], [946, 253, 705, 50], [946, 275, 705, 72], [947, 12, 705, 72], [948, 14, 705, 72, "fileName"], [948, 22, 705, 72], [948, 24, 705, 72, "_jsxFileName"], [948, 36, 705, 72], [949, 14, 705, 72, "lineNumber"], [949, 24, 705, 72], [950, 14, 705, 72, "columnNumber"], [950, 26, 705, 72], [951, 12, 705, 72], [951, 19, 706, 18], [951, 20, 706, 19], [951, 35, 707, 12], [951, 39, 707, 12, "_jsxDevRuntime"], [951, 53, 707, 12], [951, 54, 707, 12, "jsxDEV"], [951, 60, 707, 12], [951, 62, 707, 13, "_View"], [951, 67, 707, 13], [951, 68, 707, 13, "default"], [951, 75, 707, 17], [952, 14, 707, 18, "style"], [952, 19, 707, 23], [952, 21, 707, 25, "styles"], [952, 27, 707, 31], [952, 28, 707, 32, "progressBar"], [952, 39, 707, 44], [953, 14, 707, 44, "children"], [953, 22, 707, 44], [953, 37, 708, 14], [953, 41, 708, 14, "_jsxDevRuntime"], [953, 55, 708, 14], [953, 56, 708, 14, "jsxDEV"], [953, 62, 708, 14], [953, 64, 708, 15, "_View"], [953, 69, 708, 15], [953, 70, 708, 15, "default"], [953, 77, 708, 19], [954, 16, 709, 16, "style"], [954, 21, 709, 21], [954, 23, 709, 23], [954, 24, 710, 18, "styles"], [954, 30, 710, 24], [954, 31, 710, 25, "progressFill"], [954, 43, 710, 37], [954, 45, 711, 18], [955, 18, 711, 20, "width"], [955, 23, 711, 25], [955, 25, 711, 27], [955, 28, 711, 30, "processingProgress"], [955, 46, 711, 48], [956, 16, 711, 52], [956, 17, 711, 53], [957, 14, 712, 18], [958, 16, 712, 18, "fileName"], [958, 24, 712, 18], [958, 26, 712, 18, "_jsxFileName"], [958, 38, 712, 18], [959, 16, 712, 18, "lineNumber"], [959, 26, 712, 18], [960, 16, 712, 18, "columnNumber"], [960, 28, 712, 18], [961, 14, 712, 18], [961, 21, 713, 15], [962, 12, 713, 16], [963, 14, 713, 16, "fileName"], [963, 22, 713, 16], [963, 24, 713, 16, "_jsxFileName"], [963, 36, 713, 16], [964, 14, 713, 16, "lineNumber"], [964, 24, 713, 16], [965, 14, 713, 16, "columnNumber"], [965, 26, 713, 16], [966, 12, 713, 16], [966, 19, 714, 18], [966, 20, 714, 19], [966, 35, 715, 12], [966, 39, 715, 12, "_jsxDevRuntime"], [966, 53, 715, 12], [966, 54, 715, 12, "jsxDEV"], [966, 60, 715, 12], [966, 62, 715, 13, "_Text"], [966, 67, 715, 13], [966, 68, 715, 13, "default"], [966, 75, 715, 17], [967, 14, 715, 18, "style"], [967, 19, 715, 23], [967, 21, 715, 25, "styles"], [967, 27, 715, 31], [967, 28, 715, 32, "processingDescription"], [967, 49, 715, 54], [968, 14, 715, 54, "children"], [968, 22, 715, 54], [968, 25, 716, 15, "processingState"], [968, 40, 716, 30], [968, 45, 716, 35], [968, 56, 716, 46], [968, 60, 716, 50], [968, 89, 716, 79], [968, 91, 717, 15, "processingState"], [968, 106, 717, 30], [968, 111, 717, 35], [968, 122, 717, 46], [968, 126, 717, 50], [968, 164, 717, 88], [968, 166, 718, 15, "processingState"], [968, 181, 718, 30], [968, 186, 718, 35], [968, 198, 718, 47], [968, 202, 718, 51], [968, 247, 718, 96], [968, 249, 719, 15, "processingState"], [968, 264, 719, 30], [968, 269, 719, 35], [968, 280, 719, 46], [968, 284, 719, 50], [968, 325, 719, 91], [969, 12, 719, 91], [970, 14, 719, 91, "fileName"], [970, 22, 719, 91], [970, 24, 719, 91, "_jsxFileName"], [970, 36, 719, 91], [971, 14, 719, 91, "lineNumber"], [971, 24, 719, 91], [972, 14, 719, 91, "columnNumber"], [972, 26, 719, 91], [973, 12, 719, 91], [973, 19, 720, 18], [973, 20, 720, 19], [973, 22, 721, 13, "processingState"], [973, 37, 721, 28], [973, 42, 721, 33], [973, 53, 721, 44], [973, 70, 722, 14], [973, 74, 722, 14, "_jsxDevRuntime"], [973, 88, 722, 14], [973, 89, 722, 14, "jsxDEV"], [973, 95, 722, 14], [973, 97, 722, 15, "_lucideReactNative"], [973, 115, 722, 15], [973, 116, 722, 15, "CheckCircle"], [973, 127, 722, 26], [974, 14, 722, 27, "size"], [974, 18, 722, 31], [974, 20, 722, 33], [974, 22, 722, 36], [975, 14, 722, 37, "color"], [975, 19, 722, 42], [975, 21, 722, 43], [975, 30, 722, 52], [976, 14, 722, 53, "style"], [976, 19, 722, 58], [976, 21, 722, 60, "styles"], [976, 27, 722, 66], [976, 28, 722, 67, "successIcon"], [977, 12, 722, 79], [978, 14, 722, 79, "fileName"], [978, 22, 722, 79], [978, 24, 722, 79, "_jsxFileName"], [978, 36, 722, 79], [979, 14, 722, 79, "lineNumber"], [979, 24, 722, 79], [980, 14, 722, 79, "columnNumber"], [980, 26, 722, 79], [981, 12, 722, 79], [981, 19, 722, 81], [981, 20, 723, 13], [982, 10, 723, 13], [983, 12, 723, 13, "fileName"], [983, 20, 723, 13], [983, 22, 723, 13, "_jsxFileName"], [983, 34, 723, 13], [984, 12, 723, 13, "lineNumber"], [984, 22, 723, 13], [985, 12, 723, 13, "columnNumber"], [985, 24, 723, 13], [986, 10, 723, 13], [986, 17, 724, 16], [987, 8, 724, 17], [988, 10, 724, 17, "fileName"], [988, 18, 724, 17], [988, 20, 724, 17, "_jsxFileName"], [988, 32, 724, 17], [989, 10, 724, 17, "lineNumber"], [989, 20, 724, 17], [990, 10, 724, 17, "columnNumber"], [990, 22, 724, 17], [991, 8, 724, 17], [991, 15, 725, 14], [992, 6, 725, 15], [993, 8, 725, 15, "fileName"], [993, 16, 725, 15], [993, 18, 725, 15, "_jsxFileName"], [993, 30, 725, 15], [994, 8, 725, 15, "lineNumber"], [994, 18, 725, 15], [995, 8, 725, 15, "columnNumber"], [995, 20, 725, 15], [996, 6, 725, 15], [996, 13, 726, 13], [996, 14, 726, 14], [996, 29, 728, 6], [996, 33, 728, 6, "_jsxDevRuntime"], [996, 47, 728, 6], [996, 48, 728, 6, "jsxDEV"], [996, 54, 728, 6], [996, 56, 728, 7, "_Modal"], [996, 62, 728, 7], [996, 63, 728, 7, "default"], [996, 70, 728, 12], [997, 8, 729, 8, "visible"], [997, 15, 729, 15], [997, 17, 729, 17, "processingState"], [997, 32, 729, 32], [997, 37, 729, 37], [997, 44, 729, 45], [998, 8, 730, 8, "transparent"], [998, 19, 730, 19], [999, 8, 731, 8, "animationType"], [999, 21, 731, 21], [999, 23, 731, 22], [999, 29, 731, 28], [1000, 8, 731, 28, "children"], [1000, 16, 731, 28], [1000, 31, 733, 8], [1000, 35, 733, 8, "_jsxDevRuntime"], [1000, 49, 733, 8], [1000, 50, 733, 8, "jsxDEV"], [1000, 56, 733, 8], [1000, 58, 733, 9, "_View"], [1000, 63, 733, 9], [1000, 64, 733, 9, "default"], [1000, 71, 733, 13], [1001, 10, 733, 14, "style"], [1001, 15, 733, 19], [1001, 17, 733, 21, "styles"], [1001, 23, 733, 27], [1001, 24, 733, 28, "processingModal"], [1001, 39, 733, 44], [1002, 10, 733, 44, "children"], [1002, 18, 733, 44], [1002, 33, 734, 10], [1002, 37, 734, 10, "_jsxDevRuntime"], [1002, 51, 734, 10], [1002, 52, 734, 10, "jsxDEV"], [1002, 58, 734, 10], [1002, 60, 734, 11, "_View"], [1002, 65, 734, 11], [1002, 66, 734, 11, "default"], [1002, 73, 734, 15], [1003, 12, 734, 16, "style"], [1003, 17, 734, 21], [1003, 19, 734, 23, "styles"], [1003, 25, 734, 29], [1003, 26, 734, 30, "errorContent"], [1003, 38, 734, 43], [1004, 12, 734, 43, "children"], [1004, 20, 734, 43], [1004, 36, 735, 12], [1004, 40, 735, 12, "_jsxDevRuntime"], [1004, 54, 735, 12], [1004, 55, 735, 12, "jsxDEV"], [1004, 61, 735, 12], [1004, 63, 735, 13, "_lucideReactNative"], [1004, 81, 735, 13], [1004, 82, 735, 13, "X"], [1004, 83, 735, 14], [1005, 14, 735, 15, "size"], [1005, 18, 735, 19], [1005, 20, 735, 21], [1005, 22, 735, 24], [1006, 14, 735, 25, "color"], [1006, 19, 735, 30], [1006, 21, 735, 31], [1007, 12, 735, 40], [1008, 14, 735, 40, "fileName"], [1008, 22, 735, 40], [1008, 24, 735, 40, "_jsxFileName"], [1008, 36, 735, 40], [1009, 14, 735, 40, "lineNumber"], [1009, 24, 735, 40], [1010, 14, 735, 40, "columnNumber"], [1010, 26, 735, 40], [1011, 12, 735, 40], [1011, 19, 735, 42], [1011, 20, 735, 43], [1011, 35, 736, 12], [1011, 39, 736, 12, "_jsxDevRuntime"], [1011, 53, 736, 12], [1011, 54, 736, 12, "jsxDEV"], [1011, 60, 736, 12], [1011, 62, 736, 13, "_Text"], [1011, 67, 736, 13], [1011, 68, 736, 13, "default"], [1011, 75, 736, 17], [1012, 14, 736, 18, "style"], [1012, 19, 736, 23], [1012, 21, 736, 25, "styles"], [1012, 27, 736, 31], [1012, 28, 736, 32, "errorTitle"], [1012, 38, 736, 43], [1013, 14, 736, 43, "children"], [1013, 22, 736, 43], [1013, 24, 736, 44], [1014, 12, 736, 61], [1015, 14, 736, 61, "fileName"], [1015, 22, 736, 61], [1015, 24, 736, 61, "_jsxFileName"], [1015, 36, 736, 61], [1016, 14, 736, 61, "lineNumber"], [1016, 24, 736, 61], [1017, 14, 736, 61, "columnNumber"], [1017, 26, 736, 61], [1018, 12, 736, 61], [1018, 19, 736, 67], [1018, 20, 736, 68], [1018, 35, 737, 12], [1018, 39, 737, 12, "_jsxDevRuntime"], [1018, 53, 737, 12], [1018, 54, 737, 12, "jsxDEV"], [1018, 60, 737, 12], [1018, 62, 737, 13, "_Text"], [1018, 67, 737, 13], [1018, 68, 737, 13, "default"], [1018, 75, 737, 17], [1019, 14, 737, 18, "style"], [1019, 19, 737, 23], [1019, 21, 737, 25, "styles"], [1019, 27, 737, 31], [1019, 28, 737, 32, "errorMessage"], [1019, 40, 737, 45], [1020, 14, 737, 45, "children"], [1020, 22, 737, 45], [1020, 24, 737, 47, "errorMessage"], [1021, 12, 737, 59], [1022, 14, 737, 59, "fileName"], [1022, 22, 737, 59], [1022, 24, 737, 59, "_jsxFileName"], [1022, 36, 737, 59], [1023, 14, 737, 59, "lineNumber"], [1023, 24, 737, 59], [1024, 14, 737, 59, "columnNumber"], [1024, 26, 737, 59], [1025, 12, 737, 59], [1025, 19, 737, 66], [1025, 20, 737, 67], [1025, 35, 738, 12], [1025, 39, 738, 12, "_jsxDevRuntime"], [1025, 53, 738, 12], [1025, 54, 738, 12, "jsxDEV"], [1025, 60, 738, 12], [1025, 62, 738, 13, "_TouchableOpacity"], [1025, 79, 738, 13], [1025, 80, 738, 13, "default"], [1025, 87, 738, 29], [1026, 14, 739, 14, "onPress"], [1026, 21, 739, 21], [1026, 23, 739, 23, "retryCapture"], [1026, 35, 739, 36], [1027, 14, 740, 14, "style"], [1027, 19, 740, 19], [1027, 21, 740, 21, "styles"], [1027, 27, 740, 27], [1027, 28, 740, 28, "primaryButton"], [1027, 41, 740, 42], [1028, 14, 740, 42, "children"], [1028, 22, 740, 42], [1028, 37, 742, 14], [1028, 41, 742, 14, "_jsxDevRuntime"], [1028, 55, 742, 14], [1028, 56, 742, 14, "jsxDEV"], [1028, 62, 742, 14], [1028, 64, 742, 15, "_Text"], [1028, 69, 742, 15], [1028, 70, 742, 15, "default"], [1028, 77, 742, 19], [1029, 16, 742, 20, "style"], [1029, 21, 742, 25], [1029, 23, 742, 27, "styles"], [1029, 29, 742, 33], [1029, 30, 742, 34, "primaryButtonText"], [1029, 47, 742, 52], [1030, 16, 742, 52, "children"], [1030, 24, 742, 52], [1030, 26, 742, 53], [1031, 14, 742, 62], [1032, 16, 742, 62, "fileName"], [1032, 24, 742, 62], [1032, 26, 742, 62, "_jsxFileName"], [1032, 38, 742, 62], [1033, 16, 742, 62, "lineNumber"], [1033, 26, 742, 62], [1034, 16, 742, 62, "columnNumber"], [1034, 28, 742, 62], [1035, 14, 742, 62], [1035, 21, 742, 68], [1036, 12, 742, 69], [1037, 14, 742, 69, "fileName"], [1037, 22, 742, 69], [1037, 24, 742, 69, "_jsxFileName"], [1037, 36, 742, 69], [1038, 14, 742, 69, "lineNumber"], [1038, 24, 742, 69], [1039, 14, 742, 69, "columnNumber"], [1039, 26, 742, 69], [1040, 12, 742, 69], [1040, 19, 743, 30], [1040, 20, 743, 31], [1040, 35, 744, 12], [1040, 39, 744, 12, "_jsxDevRuntime"], [1040, 53, 744, 12], [1040, 54, 744, 12, "jsxDEV"], [1040, 60, 744, 12], [1040, 62, 744, 13, "_TouchableOpacity"], [1040, 79, 744, 13], [1040, 80, 744, 13, "default"], [1040, 87, 744, 29], [1041, 14, 745, 14, "onPress"], [1041, 21, 745, 21], [1041, 23, 745, 23, "onCancel"], [1041, 31, 745, 32], [1042, 14, 746, 14, "style"], [1042, 19, 746, 19], [1042, 21, 746, 21, "styles"], [1042, 27, 746, 27], [1042, 28, 746, 28, "secondaryButton"], [1042, 43, 746, 44], [1043, 14, 746, 44, "children"], [1043, 22, 746, 44], [1043, 37, 748, 14], [1043, 41, 748, 14, "_jsxDevRuntime"], [1043, 55, 748, 14], [1043, 56, 748, 14, "jsxDEV"], [1043, 62, 748, 14], [1043, 64, 748, 15, "_Text"], [1043, 69, 748, 15], [1043, 70, 748, 15, "default"], [1043, 77, 748, 19], [1044, 16, 748, 20, "style"], [1044, 21, 748, 25], [1044, 23, 748, 27, "styles"], [1044, 29, 748, 33], [1044, 30, 748, 34, "secondaryButtonText"], [1044, 49, 748, 54], [1045, 16, 748, 54, "children"], [1045, 24, 748, 54], [1045, 26, 748, 55], [1046, 14, 748, 61], [1047, 16, 748, 61, "fileName"], [1047, 24, 748, 61], [1047, 26, 748, 61, "_jsxFileName"], [1047, 38, 748, 61], [1048, 16, 748, 61, "lineNumber"], [1048, 26, 748, 61], [1049, 16, 748, 61, "columnNumber"], [1049, 28, 748, 61], [1050, 14, 748, 61], [1050, 21, 748, 67], [1051, 12, 748, 68], [1052, 14, 748, 68, "fileName"], [1052, 22, 748, 68], [1052, 24, 748, 68, "_jsxFileName"], [1052, 36, 748, 68], [1053, 14, 748, 68, "lineNumber"], [1053, 24, 748, 68], [1054, 14, 748, 68, "columnNumber"], [1054, 26, 748, 68], [1055, 12, 748, 68], [1055, 19, 749, 30], [1055, 20, 749, 31], [1056, 10, 749, 31], [1057, 12, 749, 31, "fileName"], [1057, 20, 749, 31], [1057, 22, 749, 31, "_jsxFileName"], [1057, 34, 749, 31], [1058, 12, 749, 31, "lineNumber"], [1058, 22, 749, 31], [1059, 12, 749, 31, "columnNumber"], [1059, 24, 749, 31], [1060, 10, 749, 31], [1060, 17, 750, 16], [1061, 8, 750, 17], [1062, 10, 750, 17, "fileName"], [1062, 18, 750, 17], [1062, 20, 750, 17, "_jsxFileName"], [1062, 32, 750, 17], [1063, 10, 750, 17, "lineNumber"], [1063, 20, 750, 17], [1064, 10, 750, 17, "columnNumber"], [1064, 22, 750, 17], [1065, 8, 750, 17], [1065, 15, 751, 14], [1066, 6, 751, 15], [1067, 8, 751, 15, "fileName"], [1067, 16, 751, 15], [1067, 18, 751, 15, "_jsxFileName"], [1067, 30, 751, 15], [1068, 8, 751, 15, "lineNumber"], [1068, 18, 751, 15], [1069, 8, 751, 15, "columnNumber"], [1069, 20, 751, 15], [1070, 6, 751, 15], [1070, 13, 752, 13], [1070, 14, 752, 14], [1071, 4, 752, 14], [1072, 6, 752, 14, "fileName"], [1072, 14, 752, 14], [1072, 16, 752, 14, "_jsxFileName"], [1072, 28, 752, 14], [1073, 6, 752, 14, "lineNumber"], [1073, 16, 752, 14], [1074, 6, 752, 14, "columnNumber"], [1074, 18, 752, 14], [1075, 4, 752, 14], [1075, 11, 753, 10], [1075, 12, 753, 11], [1076, 2, 755, 0], [1077, 2, 755, 1, "_s"], [1077, 4, 755, 1], [1077, 5, 51, 24, "EchoCameraWeb"], [1077, 18, 51, 37], [1078, 4, 51, 37], [1078, 12, 58, 42, "useCameraPermissions"], [1078, 44, 58, 62], [1078, 46, 72, 19, "useUpload"], [1078, 64, 72, 28], [1079, 2, 72, 28], [1080, 2, 72, 28, "_c"], [1080, 4, 72, 28], [1080, 7, 51, 24, "EchoCameraWeb"], [1080, 20, 51, 37], [1081, 2, 756, 0], [1081, 8, 756, 6, "styles"], [1081, 14, 756, 12], [1081, 17, 756, 15, "StyleSheet"], [1081, 36, 756, 25], [1081, 37, 756, 26, "create"], [1081, 43, 756, 32], [1081, 44, 756, 33], [1082, 4, 757, 2, "container"], [1082, 13, 757, 11], [1082, 15, 757, 13], [1083, 6, 758, 4, "flex"], [1083, 10, 758, 8], [1083, 12, 758, 10], [1083, 13, 758, 11], [1084, 6, 759, 4, "backgroundColor"], [1084, 21, 759, 19], [1084, 23, 759, 21], [1085, 4, 760, 2], [1085, 5, 760, 3], [1086, 4, 761, 2, "cameraContainer"], [1086, 19, 761, 17], [1086, 21, 761, 19], [1087, 6, 762, 4, "flex"], [1087, 10, 762, 8], [1087, 12, 762, 10], [1087, 13, 762, 11], [1088, 6, 763, 4, "max<PERSON><PERSON><PERSON>"], [1088, 14, 763, 12], [1088, 16, 763, 14], [1088, 19, 763, 17], [1089, 6, 764, 4, "alignSelf"], [1089, 15, 764, 13], [1089, 17, 764, 15], [1089, 25, 764, 23], [1090, 6, 765, 4, "width"], [1090, 11, 765, 9], [1090, 13, 765, 11], [1091, 4, 766, 2], [1091, 5, 766, 3], [1092, 4, 767, 2, "camera"], [1092, 10, 767, 8], [1092, 12, 767, 10], [1093, 6, 768, 4, "flex"], [1093, 10, 768, 8], [1093, 12, 768, 10], [1094, 4, 769, 2], [1094, 5, 769, 3], [1095, 4, 770, 2, "headerOverlay"], [1095, 17, 770, 15], [1095, 19, 770, 17], [1096, 6, 771, 4, "position"], [1096, 14, 771, 12], [1096, 16, 771, 14], [1096, 26, 771, 24], [1097, 6, 772, 4, "top"], [1097, 9, 772, 7], [1097, 11, 772, 9], [1097, 12, 772, 10], [1098, 6, 773, 4, "left"], [1098, 10, 773, 8], [1098, 12, 773, 10], [1098, 13, 773, 11], [1099, 6, 774, 4, "right"], [1099, 11, 774, 9], [1099, 13, 774, 11], [1099, 14, 774, 12], [1100, 6, 775, 4, "backgroundColor"], [1100, 21, 775, 19], [1100, 23, 775, 21], [1100, 36, 775, 34], [1101, 6, 776, 4, "paddingTop"], [1101, 16, 776, 14], [1101, 18, 776, 16], [1101, 20, 776, 18], [1102, 6, 777, 4, "paddingHorizontal"], [1102, 23, 777, 21], [1102, 25, 777, 23], [1102, 27, 777, 25], [1103, 6, 778, 4, "paddingBottom"], [1103, 19, 778, 17], [1103, 21, 778, 19], [1104, 4, 779, 2], [1104, 5, 779, 3], [1105, 4, 780, 2, "headerContent"], [1105, 17, 780, 15], [1105, 19, 780, 17], [1106, 6, 781, 4, "flexDirection"], [1106, 19, 781, 17], [1106, 21, 781, 19], [1106, 26, 781, 24], [1107, 6, 782, 4, "justifyContent"], [1107, 20, 782, 18], [1107, 22, 782, 20], [1107, 37, 782, 35], [1108, 6, 783, 4, "alignItems"], [1108, 16, 783, 14], [1108, 18, 783, 16], [1109, 4, 784, 2], [1109, 5, 784, 3], [1110, 4, 785, 2, "headerLeft"], [1110, 14, 785, 12], [1110, 16, 785, 14], [1111, 6, 786, 4, "flex"], [1111, 10, 786, 8], [1111, 12, 786, 10], [1112, 4, 787, 2], [1112, 5, 787, 3], [1113, 4, 788, 2, "headerTitle"], [1113, 15, 788, 13], [1113, 17, 788, 15], [1114, 6, 789, 4, "fontSize"], [1114, 14, 789, 12], [1114, 16, 789, 14], [1114, 18, 789, 16], [1115, 6, 790, 4, "fontWeight"], [1115, 16, 790, 14], [1115, 18, 790, 16], [1115, 23, 790, 21], [1116, 6, 791, 4, "color"], [1116, 11, 791, 9], [1116, 13, 791, 11], [1116, 19, 791, 17], [1117, 6, 792, 4, "marginBottom"], [1117, 18, 792, 16], [1117, 20, 792, 18], [1118, 4, 793, 2], [1118, 5, 793, 3], [1119, 4, 794, 2, "subtitleRow"], [1119, 15, 794, 13], [1119, 17, 794, 15], [1120, 6, 795, 4, "flexDirection"], [1120, 19, 795, 17], [1120, 21, 795, 19], [1120, 26, 795, 24], [1121, 6, 796, 4, "alignItems"], [1121, 16, 796, 14], [1121, 18, 796, 16], [1121, 26, 796, 24], [1122, 6, 797, 4, "marginBottom"], [1122, 18, 797, 16], [1122, 20, 797, 18], [1123, 4, 798, 2], [1123, 5, 798, 3], [1124, 4, 799, 2, "webIcon"], [1124, 11, 799, 9], [1124, 13, 799, 11], [1125, 6, 800, 4, "fontSize"], [1125, 14, 800, 12], [1125, 16, 800, 14], [1125, 18, 800, 16], [1126, 6, 801, 4, "marginRight"], [1126, 17, 801, 15], [1126, 19, 801, 17], [1127, 4, 802, 2], [1127, 5, 802, 3], [1128, 4, 803, 2, "headerSubtitle"], [1128, 18, 803, 16], [1128, 20, 803, 18], [1129, 6, 804, 4, "fontSize"], [1129, 14, 804, 12], [1129, 16, 804, 14], [1129, 18, 804, 16], [1130, 6, 805, 4, "color"], [1130, 11, 805, 9], [1130, 13, 805, 11], [1130, 19, 805, 17], [1131, 6, 806, 4, "opacity"], [1131, 13, 806, 11], [1131, 15, 806, 13], [1132, 4, 807, 2], [1132, 5, 807, 3], [1133, 4, 808, 2, "challengeRow"], [1133, 16, 808, 14], [1133, 18, 808, 16], [1134, 6, 809, 4, "flexDirection"], [1134, 19, 809, 17], [1134, 21, 809, 19], [1134, 26, 809, 24], [1135, 6, 810, 4, "alignItems"], [1135, 16, 810, 14], [1135, 18, 810, 16], [1136, 4, 811, 2], [1136, 5, 811, 3], [1137, 4, 812, 2, "challengeCode"], [1137, 17, 812, 15], [1137, 19, 812, 17], [1138, 6, 813, 4, "fontSize"], [1138, 14, 813, 12], [1138, 16, 813, 14], [1138, 18, 813, 16], [1139, 6, 814, 4, "color"], [1139, 11, 814, 9], [1139, 13, 814, 11], [1139, 19, 814, 17], [1140, 6, 815, 4, "marginLeft"], [1140, 16, 815, 14], [1140, 18, 815, 16], [1140, 19, 815, 17], [1141, 6, 816, 4, "fontFamily"], [1141, 16, 816, 14], [1141, 18, 816, 16], [1142, 4, 817, 2], [1142, 5, 817, 3], [1143, 4, 818, 2, "closeButton"], [1143, 15, 818, 13], [1143, 17, 818, 15], [1144, 6, 819, 4, "padding"], [1144, 13, 819, 11], [1144, 15, 819, 13], [1145, 4, 820, 2], [1145, 5, 820, 3], [1146, 4, 821, 2, "privacyNotice"], [1146, 17, 821, 15], [1146, 19, 821, 17], [1147, 6, 822, 4, "position"], [1147, 14, 822, 12], [1147, 16, 822, 14], [1147, 26, 822, 24], [1148, 6, 823, 4, "top"], [1148, 9, 823, 7], [1148, 11, 823, 9], [1148, 14, 823, 12], [1149, 6, 824, 4, "left"], [1149, 10, 824, 8], [1149, 12, 824, 10], [1149, 14, 824, 12], [1150, 6, 825, 4, "right"], [1150, 11, 825, 9], [1150, 13, 825, 11], [1150, 15, 825, 13], [1151, 6, 826, 4, "backgroundColor"], [1151, 21, 826, 19], [1151, 23, 826, 21], [1151, 48, 826, 46], [1152, 6, 827, 4, "borderRadius"], [1152, 18, 827, 16], [1152, 20, 827, 18], [1152, 21, 827, 19], [1153, 6, 828, 4, "padding"], [1153, 13, 828, 11], [1153, 15, 828, 13], [1153, 17, 828, 15], [1154, 6, 829, 4, "flexDirection"], [1154, 19, 829, 17], [1154, 21, 829, 19], [1154, 26, 829, 24], [1155, 6, 830, 4, "alignItems"], [1155, 16, 830, 14], [1155, 18, 830, 16], [1156, 4, 831, 2], [1156, 5, 831, 3], [1157, 4, 832, 2, "privacyText"], [1157, 15, 832, 13], [1157, 17, 832, 15], [1158, 6, 833, 4, "color"], [1158, 11, 833, 9], [1158, 13, 833, 11], [1158, 19, 833, 17], [1159, 6, 834, 4, "fontSize"], [1159, 14, 834, 12], [1159, 16, 834, 14], [1159, 18, 834, 16], [1160, 6, 835, 4, "marginLeft"], [1160, 16, 835, 14], [1160, 18, 835, 16], [1160, 19, 835, 17], [1161, 6, 836, 4, "flex"], [1161, 10, 836, 8], [1161, 12, 836, 10], [1162, 4, 837, 2], [1162, 5, 837, 3], [1163, 4, 838, 2, "footer<PERSON><PERSON><PERSON>"], [1163, 17, 838, 15], [1163, 19, 838, 17], [1164, 6, 839, 4, "position"], [1164, 14, 839, 12], [1164, 16, 839, 14], [1164, 26, 839, 24], [1165, 6, 840, 4, "bottom"], [1165, 12, 840, 10], [1165, 14, 840, 12], [1165, 15, 840, 13], [1166, 6, 841, 4, "left"], [1166, 10, 841, 8], [1166, 12, 841, 10], [1166, 13, 841, 11], [1167, 6, 842, 4, "right"], [1167, 11, 842, 9], [1167, 13, 842, 11], [1167, 14, 842, 12], [1168, 6, 843, 4, "backgroundColor"], [1168, 21, 843, 19], [1168, 23, 843, 21], [1168, 36, 843, 34], [1169, 6, 844, 4, "paddingBottom"], [1169, 19, 844, 17], [1169, 21, 844, 19], [1169, 23, 844, 21], [1170, 6, 845, 4, "paddingTop"], [1170, 16, 845, 14], [1170, 18, 845, 16], [1170, 20, 845, 18], [1171, 6, 846, 4, "alignItems"], [1171, 16, 846, 14], [1171, 18, 846, 16], [1172, 4, 847, 2], [1172, 5, 847, 3], [1173, 4, 848, 2, "instruction"], [1173, 15, 848, 13], [1173, 17, 848, 15], [1174, 6, 849, 4, "fontSize"], [1174, 14, 849, 12], [1174, 16, 849, 14], [1174, 18, 849, 16], [1175, 6, 850, 4, "color"], [1175, 11, 850, 9], [1175, 13, 850, 11], [1175, 19, 850, 17], [1176, 6, 851, 4, "marginBottom"], [1176, 18, 851, 16], [1176, 20, 851, 18], [1177, 4, 852, 2], [1177, 5, 852, 3], [1178, 4, 853, 2, "shutterButton"], [1178, 17, 853, 15], [1178, 19, 853, 17], [1179, 6, 854, 4, "width"], [1179, 11, 854, 9], [1179, 13, 854, 11], [1179, 15, 854, 13], [1180, 6, 855, 4, "height"], [1180, 12, 855, 10], [1180, 14, 855, 12], [1180, 16, 855, 14], [1181, 6, 856, 4, "borderRadius"], [1181, 18, 856, 16], [1181, 20, 856, 18], [1181, 22, 856, 20], [1182, 6, 857, 4, "backgroundColor"], [1182, 21, 857, 19], [1182, 23, 857, 21], [1182, 29, 857, 27], [1183, 6, 858, 4, "justifyContent"], [1183, 20, 858, 18], [1183, 22, 858, 20], [1183, 30, 858, 28], [1184, 6, 859, 4, "alignItems"], [1184, 16, 859, 14], [1184, 18, 859, 16], [1184, 26, 859, 24], [1185, 6, 860, 4, "marginBottom"], [1185, 18, 860, 16], [1185, 20, 860, 18], [1185, 22, 860, 20], [1186, 6, 861, 4], [1186, 9, 861, 7, "Platform"], [1186, 26, 861, 15], [1186, 27, 861, 16, "select"], [1186, 33, 861, 22], [1186, 34, 861, 23], [1187, 8, 862, 6, "ios"], [1187, 11, 862, 9], [1187, 13, 862, 11], [1188, 10, 863, 8, "shadowColor"], [1188, 21, 863, 19], [1188, 23, 863, 21], [1188, 32, 863, 30], [1189, 10, 864, 8, "shadowOffset"], [1189, 22, 864, 20], [1189, 24, 864, 22], [1190, 12, 864, 24, "width"], [1190, 17, 864, 29], [1190, 19, 864, 31], [1190, 20, 864, 32], [1191, 12, 864, 34, "height"], [1191, 18, 864, 40], [1191, 20, 864, 42], [1192, 10, 864, 44], [1192, 11, 864, 45], [1193, 10, 865, 8, "shadowOpacity"], [1193, 23, 865, 21], [1193, 25, 865, 23], [1193, 28, 865, 26], [1194, 10, 866, 8, "shadowRadius"], [1194, 22, 866, 20], [1194, 24, 866, 22], [1195, 8, 867, 6], [1195, 9, 867, 7], [1196, 8, 868, 6, "android"], [1196, 15, 868, 13], [1196, 17, 868, 15], [1197, 10, 869, 8, "elevation"], [1197, 19, 869, 17], [1197, 21, 869, 19], [1198, 8, 870, 6], [1198, 9, 870, 7], [1199, 8, 871, 6, "web"], [1199, 11, 871, 9], [1199, 13, 871, 11], [1200, 10, 872, 8, "boxShadow"], [1200, 19, 872, 17], [1200, 21, 872, 19], [1201, 8, 873, 6], [1202, 6, 874, 4], [1202, 7, 874, 5], [1203, 4, 875, 2], [1203, 5, 875, 3], [1204, 4, 876, 2, "shutterButtonDisabled"], [1204, 25, 876, 23], [1204, 27, 876, 25], [1205, 6, 877, 4, "opacity"], [1205, 13, 877, 11], [1205, 15, 877, 13], [1206, 4, 878, 2], [1206, 5, 878, 3], [1207, 4, 879, 2, "shutterInner"], [1207, 16, 879, 14], [1207, 18, 879, 16], [1208, 6, 880, 4, "width"], [1208, 11, 880, 9], [1208, 13, 880, 11], [1208, 15, 880, 13], [1209, 6, 881, 4, "height"], [1209, 12, 881, 10], [1209, 14, 881, 12], [1209, 16, 881, 14], [1210, 6, 882, 4, "borderRadius"], [1210, 18, 882, 16], [1210, 20, 882, 18], [1210, 22, 882, 20], [1211, 6, 883, 4, "backgroundColor"], [1211, 21, 883, 19], [1211, 23, 883, 21], [1211, 29, 883, 27], [1212, 6, 884, 4, "borderWidth"], [1212, 17, 884, 15], [1212, 19, 884, 17], [1212, 20, 884, 18], [1213, 6, 885, 4, "borderColor"], [1213, 17, 885, 15], [1213, 19, 885, 17], [1214, 4, 886, 2], [1214, 5, 886, 3], [1215, 4, 887, 2, "privacyNote"], [1215, 15, 887, 13], [1215, 17, 887, 15], [1216, 6, 888, 4, "fontSize"], [1216, 14, 888, 12], [1216, 16, 888, 14], [1216, 18, 888, 16], [1217, 6, 889, 4, "color"], [1217, 11, 889, 9], [1217, 13, 889, 11], [1218, 4, 890, 2], [1218, 5, 890, 3], [1219, 4, 891, 2, "processingModal"], [1219, 19, 891, 17], [1219, 21, 891, 19], [1220, 6, 892, 4, "flex"], [1220, 10, 892, 8], [1220, 12, 892, 10], [1220, 13, 892, 11], [1221, 6, 893, 4, "backgroundColor"], [1221, 21, 893, 19], [1221, 23, 893, 21], [1221, 43, 893, 41], [1222, 6, 894, 4, "justifyContent"], [1222, 20, 894, 18], [1222, 22, 894, 20], [1222, 30, 894, 28], [1223, 6, 895, 4, "alignItems"], [1223, 16, 895, 14], [1223, 18, 895, 16], [1224, 4, 896, 2], [1224, 5, 896, 3], [1225, 4, 897, 2, "processingContent"], [1225, 21, 897, 19], [1225, 23, 897, 21], [1226, 6, 898, 4, "backgroundColor"], [1226, 21, 898, 19], [1226, 23, 898, 21], [1226, 29, 898, 27], [1227, 6, 899, 4, "borderRadius"], [1227, 18, 899, 16], [1227, 20, 899, 18], [1227, 22, 899, 20], [1228, 6, 900, 4, "padding"], [1228, 13, 900, 11], [1228, 15, 900, 13], [1228, 17, 900, 15], [1229, 6, 901, 4, "width"], [1229, 11, 901, 9], [1229, 13, 901, 11], [1229, 18, 901, 16], [1230, 6, 902, 4, "max<PERSON><PERSON><PERSON>"], [1230, 14, 902, 12], [1230, 16, 902, 14], [1230, 19, 902, 17], [1231, 6, 903, 4, "alignItems"], [1231, 16, 903, 14], [1231, 18, 903, 16], [1232, 4, 904, 2], [1232, 5, 904, 3], [1233, 4, 905, 2, "processingTitle"], [1233, 19, 905, 17], [1233, 21, 905, 19], [1234, 6, 906, 4, "fontSize"], [1234, 14, 906, 12], [1234, 16, 906, 14], [1234, 18, 906, 16], [1235, 6, 907, 4, "fontWeight"], [1235, 16, 907, 14], [1235, 18, 907, 16], [1235, 23, 907, 21], [1236, 6, 908, 4, "color"], [1236, 11, 908, 9], [1236, 13, 908, 11], [1236, 22, 908, 20], [1237, 6, 909, 4, "marginTop"], [1237, 15, 909, 13], [1237, 17, 909, 15], [1237, 19, 909, 17], [1238, 6, 910, 4, "marginBottom"], [1238, 18, 910, 16], [1238, 20, 910, 18], [1239, 4, 911, 2], [1239, 5, 911, 3], [1240, 4, 912, 2, "progressBar"], [1240, 15, 912, 13], [1240, 17, 912, 15], [1241, 6, 913, 4, "width"], [1241, 11, 913, 9], [1241, 13, 913, 11], [1241, 19, 913, 17], [1242, 6, 914, 4, "height"], [1242, 12, 914, 10], [1242, 14, 914, 12], [1242, 15, 914, 13], [1243, 6, 915, 4, "backgroundColor"], [1243, 21, 915, 19], [1243, 23, 915, 21], [1243, 32, 915, 30], [1244, 6, 916, 4, "borderRadius"], [1244, 18, 916, 16], [1244, 20, 916, 18], [1244, 21, 916, 19], [1245, 6, 917, 4, "overflow"], [1245, 14, 917, 12], [1245, 16, 917, 14], [1245, 24, 917, 22], [1246, 6, 918, 4, "marginBottom"], [1246, 18, 918, 16], [1246, 20, 918, 18], [1247, 4, 919, 2], [1247, 5, 919, 3], [1248, 4, 920, 2, "progressFill"], [1248, 16, 920, 14], [1248, 18, 920, 16], [1249, 6, 921, 4, "height"], [1249, 12, 921, 10], [1249, 14, 921, 12], [1249, 20, 921, 18], [1250, 6, 922, 4, "backgroundColor"], [1250, 21, 922, 19], [1250, 23, 922, 21], [1250, 32, 922, 30], [1251, 6, 923, 4, "borderRadius"], [1251, 18, 923, 16], [1251, 20, 923, 18], [1252, 4, 924, 2], [1252, 5, 924, 3], [1253, 4, 925, 2, "processingDescription"], [1253, 25, 925, 23], [1253, 27, 925, 25], [1254, 6, 926, 4, "fontSize"], [1254, 14, 926, 12], [1254, 16, 926, 14], [1254, 18, 926, 16], [1255, 6, 927, 4, "color"], [1255, 11, 927, 9], [1255, 13, 927, 11], [1255, 22, 927, 20], [1256, 6, 928, 4, "textAlign"], [1256, 15, 928, 13], [1256, 17, 928, 15], [1257, 4, 929, 2], [1257, 5, 929, 3], [1258, 4, 930, 2, "successIcon"], [1258, 15, 930, 13], [1258, 17, 930, 15], [1259, 6, 931, 4, "marginTop"], [1259, 15, 931, 13], [1259, 17, 931, 15], [1260, 4, 932, 2], [1260, 5, 932, 3], [1261, 4, 933, 2, "errorContent"], [1261, 16, 933, 14], [1261, 18, 933, 16], [1262, 6, 934, 4, "backgroundColor"], [1262, 21, 934, 19], [1262, 23, 934, 21], [1262, 29, 934, 27], [1263, 6, 935, 4, "borderRadius"], [1263, 18, 935, 16], [1263, 20, 935, 18], [1263, 22, 935, 20], [1264, 6, 936, 4, "padding"], [1264, 13, 936, 11], [1264, 15, 936, 13], [1264, 17, 936, 15], [1265, 6, 937, 4, "width"], [1265, 11, 937, 9], [1265, 13, 937, 11], [1265, 18, 937, 16], [1266, 6, 938, 4, "max<PERSON><PERSON><PERSON>"], [1266, 14, 938, 12], [1266, 16, 938, 14], [1266, 19, 938, 17], [1267, 6, 939, 4, "alignItems"], [1267, 16, 939, 14], [1267, 18, 939, 16], [1268, 4, 940, 2], [1268, 5, 940, 3], [1269, 4, 941, 2, "errorTitle"], [1269, 14, 941, 12], [1269, 16, 941, 14], [1270, 6, 942, 4, "fontSize"], [1270, 14, 942, 12], [1270, 16, 942, 14], [1270, 18, 942, 16], [1271, 6, 943, 4, "fontWeight"], [1271, 16, 943, 14], [1271, 18, 943, 16], [1271, 23, 943, 21], [1272, 6, 944, 4, "color"], [1272, 11, 944, 9], [1272, 13, 944, 11], [1272, 22, 944, 20], [1273, 6, 945, 4, "marginTop"], [1273, 15, 945, 13], [1273, 17, 945, 15], [1273, 19, 945, 17], [1274, 6, 946, 4, "marginBottom"], [1274, 18, 946, 16], [1274, 20, 946, 18], [1275, 4, 947, 2], [1275, 5, 947, 3], [1276, 4, 948, 2, "errorMessage"], [1276, 16, 948, 14], [1276, 18, 948, 16], [1277, 6, 949, 4, "fontSize"], [1277, 14, 949, 12], [1277, 16, 949, 14], [1277, 18, 949, 16], [1278, 6, 950, 4, "color"], [1278, 11, 950, 9], [1278, 13, 950, 11], [1278, 22, 950, 20], [1279, 6, 951, 4, "textAlign"], [1279, 15, 951, 13], [1279, 17, 951, 15], [1279, 25, 951, 23], [1280, 6, 952, 4, "marginBottom"], [1280, 18, 952, 16], [1280, 20, 952, 18], [1281, 4, 953, 2], [1281, 5, 953, 3], [1282, 4, 954, 2, "primaryButton"], [1282, 17, 954, 15], [1282, 19, 954, 17], [1283, 6, 955, 4, "backgroundColor"], [1283, 21, 955, 19], [1283, 23, 955, 21], [1283, 32, 955, 30], [1284, 6, 956, 4, "paddingHorizontal"], [1284, 23, 956, 21], [1284, 25, 956, 23], [1284, 27, 956, 25], [1285, 6, 957, 4, "paddingVertical"], [1285, 21, 957, 19], [1285, 23, 957, 21], [1285, 25, 957, 23], [1286, 6, 958, 4, "borderRadius"], [1286, 18, 958, 16], [1286, 20, 958, 18], [1286, 21, 958, 19], [1287, 6, 959, 4, "marginTop"], [1287, 15, 959, 13], [1287, 17, 959, 15], [1288, 4, 960, 2], [1288, 5, 960, 3], [1289, 4, 961, 2, "primaryButtonText"], [1289, 21, 961, 19], [1289, 23, 961, 21], [1290, 6, 962, 4, "color"], [1290, 11, 962, 9], [1290, 13, 962, 11], [1290, 19, 962, 17], [1291, 6, 963, 4, "fontSize"], [1291, 14, 963, 12], [1291, 16, 963, 14], [1291, 18, 963, 16], [1292, 6, 964, 4, "fontWeight"], [1292, 16, 964, 14], [1292, 18, 964, 16], [1293, 4, 965, 2], [1293, 5, 965, 3], [1294, 4, 966, 2, "secondaryButton"], [1294, 19, 966, 17], [1294, 21, 966, 19], [1295, 6, 967, 4, "paddingHorizontal"], [1295, 23, 967, 21], [1295, 25, 967, 23], [1295, 27, 967, 25], [1296, 6, 968, 4, "paddingVertical"], [1296, 21, 968, 19], [1296, 23, 968, 21], [1296, 25, 968, 23], [1297, 6, 969, 4, "marginTop"], [1297, 15, 969, 13], [1297, 17, 969, 15], [1298, 4, 970, 2], [1298, 5, 970, 3], [1299, 4, 971, 2, "secondaryButtonText"], [1299, 23, 971, 21], [1299, 25, 971, 23], [1300, 6, 972, 4, "color"], [1300, 11, 972, 9], [1300, 13, 972, 11], [1300, 22, 972, 20], [1301, 6, 973, 4, "fontSize"], [1301, 14, 973, 12], [1301, 16, 973, 14], [1302, 4, 974, 2], [1302, 5, 974, 3], [1303, 4, 975, 2, "permissionContent"], [1303, 21, 975, 19], [1303, 23, 975, 21], [1304, 6, 976, 4, "flex"], [1304, 10, 976, 8], [1304, 12, 976, 10], [1304, 13, 976, 11], [1305, 6, 977, 4, "justifyContent"], [1305, 20, 977, 18], [1305, 22, 977, 20], [1305, 30, 977, 28], [1306, 6, 978, 4, "alignItems"], [1306, 16, 978, 14], [1306, 18, 978, 16], [1306, 26, 978, 24], [1307, 6, 979, 4, "padding"], [1307, 13, 979, 11], [1307, 15, 979, 13], [1308, 4, 980, 2], [1308, 5, 980, 3], [1309, 4, 981, 2, "permissionTitle"], [1309, 19, 981, 17], [1309, 21, 981, 19], [1310, 6, 982, 4, "fontSize"], [1310, 14, 982, 12], [1310, 16, 982, 14], [1310, 18, 982, 16], [1311, 6, 983, 4, "fontWeight"], [1311, 16, 983, 14], [1311, 18, 983, 16], [1311, 23, 983, 21], [1312, 6, 984, 4, "color"], [1312, 11, 984, 9], [1312, 13, 984, 11], [1312, 22, 984, 20], [1313, 6, 985, 4, "marginTop"], [1313, 15, 985, 13], [1313, 17, 985, 15], [1313, 19, 985, 17], [1314, 6, 986, 4, "marginBottom"], [1314, 18, 986, 16], [1314, 20, 986, 18], [1315, 4, 987, 2], [1315, 5, 987, 3], [1316, 4, 988, 2, "permissionDescription"], [1316, 25, 988, 23], [1316, 27, 988, 25], [1317, 6, 989, 4, "fontSize"], [1317, 14, 989, 12], [1317, 16, 989, 14], [1317, 18, 989, 16], [1318, 6, 990, 4, "color"], [1318, 11, 990, 9], [1318, 13, 990, 11], [1318, 22, 990, 20], [1319, 6, 991, 4, "textAlign"], [1319, 15, 991, 13], [1319, 17, 991, 15], [1319, 25, 991, 23], [1320, 6, 992, 4, "marginBottom"], [1320, 18, 992, 16], [1320, 20, 992, 18], [1321, 4, 993, 2], [1321, 5, 993, 3], [1322, 4, 994, 2, "loadingText"], [1322, 15, 994, 13], [1322, 17, 994, 15], [1323, 6, 995, 4, "color"], [1323, 11, 995, 9], [1323, 13, 995, 11], [1323, 22, 995, 20], [1324, 6, 996, 4, "marginTop"], [1324, 15, 996, 13], [1324, 17, 996, 15], [1325, 4, 997, 2], [1325, 5, 997, 3], [1326, 4, 998, 2], [1327, 4, 999, 2, "blurZone"], [1327, 12, 999, 10], [1327, 14, 999, 12], [1328, 6, 1000, 4, "position"], [1328, 14, 1000, 12], [1328, 16, 1000, 14], [1328, 26, 1000, 24], [1329, 6, 1001, 4, "overflow"], [1329, 14, 1001, 12], [1329, 16, 1001, 14], [1330, 4, 1002, 2], [1330, 5, 1002, 3], [1331, 4, 1003, 2, "previewChip"], [1331, 15, 1003, 13], [1331, 17, 1003, 15], [1332, 6, 1004, 4, "position"], [1332, 14, 1004, 12], [1332, 16, 1004, 14], [1332, 26, 1004, 24], [1333, 6, 1005, 4, "top"], [1333, 9, 1005, 7], [1333, 11, 1005, 9], [1333, 12, 1005, 10], [1334, 6, 1006, 4, "right"], [1334, 11, 1006, 9], [1334, 13, 1006, 11], [1334, 14, 1006, 12], [1335, 6, 1007, 4, "backgroundColor"], [1335, 21, 1007, 19], [1335, 23, 1007, 21], [1335, 40, 1007, 38], [1336, 6, 1008, 4, "paddingHorizontal"], [1336, 23, 1008, 21], [1336, 25, 1008, 23], [1336, 27, 1008, 25], [1337, 6, 1009, 4, "paddingVertical"], [1337, 21, 1009, 19], [1337, 23, 1009, 21], [1337, 24, 1009, 22], [1338, 6, 1010, 4, "borderRadius"], [1338, 18, 1010, 16], [1338, 20, 1010, 18], [1339, 4, 1011, 2], [1339, 5, 1011, 3], [1340, 4, 1012, 2, "previewChipText"], [1340, 19, 1012, 17], [1340, 21, 1012, 19], [1341, 6, 1013, 4, "color"], [1341, 11, 1013, 9], [1341, 13, 1013, 11], [1341, 19, 1013, 17], [1342, 6, 1014, 4, "fontSize"], [1342, 14, 1014, 12], [1342, 16, 1014, 14], [1342, 18, 1014, 16], [1343, 6, 1015, 4, "fontWeight"], [1343, 16, 1015, 14], [1343, 18, 1015, 16], [1344, 4, 1016, 2], [1345, 2, 1017, 0], [1345, 3, 1017, 1], [1345, 4, 1017, 2], [1346, 2, 1017, 3], [1346, 6, 1017, 3, "_c"], [1346, 8, 1017, 3], [1347, 2, 1017, 3, "$RefreshReg$"], [1347, 14, 1017, 3], [1347, 15, 1017, 3, "_c"], [1347, 17, 1017, 3], [1348, 0, 1017, 3], [1348, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "capturePhoto", "Promise$argument_0", "processImageWithFaceBlur", "browserDetections.map$argument_0", "faceDetections.map$argument_0", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;mCGE;wBCc,kCD;GHoC;mCKE;wBDY;OCI;gDC8B;YDO;8BDa;aCM;6CEc;YFO;oFGsB;UHM;8BIS;SJoD;uDDU;sBMC,wBN;OCC;GLe;6BWG;GXyB;kCYG;GZ8C;4BaE;mBCmD;SDE;GbO;uBeE;GfI;mCgBG;GhBM;YCE;GDK;oBiB2C;WjBG;yBkBC;WlBG;wBmBC;WnBI;CD4L"}}, "type": "js/module"}]}