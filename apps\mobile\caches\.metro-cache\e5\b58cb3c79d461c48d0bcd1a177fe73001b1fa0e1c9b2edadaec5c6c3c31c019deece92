{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 602}, "end": {"line": 4, "column": 36, "index": 638}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkPathEffect = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  function _defineProperty(e, r, t) {\n    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n      value: t,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }) : e[r] = t, e;\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : i + \"\";\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  class JsiSkPathEffect extends _Host.HostObject {\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"PathEffect\");\n      _defineProperty(this, \"dispose\", () => {\n        this.ref.delete();\n      });\n    }\n  }\n  exports.JsiSkPathEffect = JsiSkPathEffect;\n});", "lineCount": 38, "map": [[6, 2, 4, 0], [6, 6, 4, 0, "_Host"], [6, 11, 4, 0], [6, 14, 4, 0, "require"], [6, 21, 4, 0], [6, 22, 4, 0, "_dependencyMap"], [6, 36, 4, 0], [7, 2, 1, 0], [7, 11, 1, 9, "_defineProperty"], [7, 26, 1, 24, "_defineProperty"], [7, 27, 1, 25, "e"], [7, 28, 1, 26], [7, 30, 1, 28, "r"], [7, 31, 1, 29], [7, 33, 1, 31, "t"], [7, 34, 1, 32], [7, 36, 1, 34], [8, 4, 1, 36], [8, 11, 1, 43], [8, 12, 1, 44, "r"], [8, 13, 1, 45], [8, 16, 1, 48, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [8, 30, 1, 62], [8, 31, 1, 63, "r"], [8, 32, 1, 64], [8, 33, 1, 65], [8, 38, 1, 70, "e"], [8, 39, 1, 71], [8, 42, 1, 74, "Object"], [8, 48, 1, 80], [8, 49, 1, 81, "defineProperty"], [8, 63, 1, 95], [8, 64, 1, 96, "e"], [8, 65, 1, 97], [8, 67, 1, 99, "r"], [8, 68, 1, 100], [8, 70, 1, 102], [9, 6, 1, 104, "value"], [9, 11, 1, 109], [9, 13, 1, 111, "t"], [9, 14, 1, 112], [10, 6, 1, 114, "enumerable"], [10, 16, 1, 124], [10, 18, 1, 126], [10, 19, 1, 127], [10, 20, 1, 128], [11, 6, 1, 130, "configurable"], [11, 18, 1, 142], [11, 20, 1, 144], [11, 21, 1, 145], [11, 22, 1, 146], [12, 6, 1, 148, "writable"], [12, 14, 1, 156], [12, 16, 1, 158], [12, 17, 1, 159], [13, 4, 1, 161], [13, 5, 1, 162], [13, 6, 1, 163], [13, 9, 1, 166, "e"], [13, 10, 1, 167], [13, 11, 1, 168, "r"], [13, 12, 1, 169], [13, 13, 1, 170], [13, 16, 1, 173, "t"], [13, 17, 1, 174], [13, 19, 1, 176, "e"], [13, 20, 1, 177], [14, 2, 1, 179], [15, 2, 2, 0], [15, 11, 2, 9, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [15, 25, 2, 23, "_to<PERSON><PERSON><PERSON><PERSON><PERSON>"], [15, 26, 2, 24, "t"], [15, 27, 2, 25], [15, 29, 2, 27], [16, 4, 2, 29], [16, 8, 2, 33, "i"], [16, 9, 2, 34], [16, 12, 2, 37, "_toPrimitive"], [16, 24, 2, 49], [16, 25, 2, 50, "t"], [16, 26, 2, 51], [16, 28, 2, 53], [16, 36, 2, 61], [16, 37, 2, 62], [17, 4, 2, 64], [17, 11, 2, 71], [17, 19, 2, 79], [17, 23, 2, 83], [17, 30, 2, 90, "i"], [17, 31, 2, 91], [17, 34, 2, 94, "i"], [17, 35, 2, 95], [17, 38, 2, 98, "i"], [17, 39, 2, 99], [17, 42, 2, 102], [17, 44, 2, 104], [18, 2, 2, 106], [19, 2, 3, 0], [19, 11, 3, 9, "_toPrimitive"], [19, 23, 3, 21, "_toPrimitive"], [19, 24, 3, 22, "t"], [19, 25, 3, 23], [19, 27, 3, 25, "r"], [19, 28, 3, 26], [19, 30, 3, 28], [20, 4, 3, 30], [20, 8, 3, 34], [20, 16, 3, 42], [20, 20, 3, 46], [20, 27, 3, 53, "t"], [20, 28, 3, 54], [20, 32, 3, 58], [20, 33, 3, 59, "t"], [20, 34, 3, 60], [20, 36, 3, 62], [20, 43, 3, 69, "t"], [20, 44, 3, 70], [21, 4, 3, 72], [21, 8, 3, 76, "e"], [21, 9, 3, 77], [21, 12, 3, 80, "t"], [21, 13, 3, 81], [21, 14, 3, 82, "Symbol"], [21, 20, 3, 88], [21, 21, 3, 89, "toPrimitive"], [21, 32, 3, 100], [21, 33, 3, 101], [22, 4, 3, 103], [22, 8, 3, 107], [22, 13, 3, 112], [22, 14, 3, 113], [22, 19, 3, 118, "e"], [22, 20, 3, 119], [22, 22, 3, 121], [23, 6, 3, 123], [23, 10, 3, 127, "i"], [23, 11, 3, 128], [23, 14, 3, 131, "e"], [23, 15, 3, 132], [23, 16, 3, 133, "call"], [23, 20, 3, 137], [23, 21, 3, 138, "t"], [23, 22, 3, 139], [23, 24, 3, 141, "r"], [23, 25, 3, 142], [23, 29, 3, 146], [23, 38, 3, 155], [23, 39, 3, 156], [24, 6, 3, 158], [24, 10, 3, 162], [24, 18, 3, 170], [24, 22, 3, 174], [24, 29, 3, 181, "i"], [24, 30, 3, 182], [24, 32, 3, 184], [24, 39, 3, 191, "i"], [24, 40, 3, 192], [25, 6, 3, 194], [25, 12, 3, 200], [25, 16, 3, 204, "TypeError"], [25, 25, 3, 213], [25, 26, 3, 214], [25, 72, 3, 260], [25, 73, 3, 261], [26, 4, 3, 263], [27, 4, 3, 265], [27, 11, 3, 272], [27, 12, 3, 273], [27, 20, 3, 281], [27, 25, 3, 286, "r"], [27, 26, 3, 287], [27, 29, 3, 290, "String"], [27, 35, 3, 296], [27, 38, 3, 299, "Number"], [27, 44, 3, 305], [27, 46, 3, 307, "t"], [27, 47, 3, 308], [27, 48, 3, 309], [28, 2, 3, 311], [29, 2, 5, 7], [29, 8, 5, 13, "JsiSkPathEffect"], [29, 23, 5, 28], [29, 32, 5, 37, "HostObject"], [29, 48, 5, 47], [29, 49, 5, 48], [30, 4, 6, 2, "constructor"], [30, 15, 6, 13, "constructor"], [30, 16, 6, 14, "CanvasKit"], [30, 25, 6, 23], [30, 27, 6, 25, "ref"], [30, 30, 6, 28], [30, 32, 6, 30], [31, 6, 7, 4], [31, 11, 7, 9], [31, 12, 7, 10, "CanvasKit"], [31, 21, 7, 19], [31, 23, 7, 21, "ref"], [31, 26, 7, 24], [31, 28, 7, 26], [31, 40, 7, 38], [31, 41, 7, 39], [32, 6, 8, 4, "_defineProperty"], [32, 21, 8, 19], [32, 22, 8, 20], [32, 26, 8, 24], [32, 28, 8, 26], [32, 37, 8, 35], [32, 39, 8, 37], [32, 45, 8, 43], [33, 8, 9, 6], [33, 12, 9, 10], [33, 13, 9, 11, "ref"], [33, 16, 9, 14], [33, 17, 9, 15, "delete"], [33, 23, 9, 21], [33, 24, 9, 22], [33, 25, 9, 23], [34, 6, 10, 4], [34, 7, 10, 5], [34, 8, 10, 6], [35, 4, 11, 2], [36, 2, 12, 0], [37, 2, 12, 1, "exports"], [37, 9, 12, 1], [37, 10, 12, 1, "JsiSkPathEffect"], [37, 25, 12, 1], [37, 28, 12, 1, "JsiSkPathEffect"], [37, 43, 12, 1], [38, 0, 12, 1], [38, 3]], "functionMap": {"names": ["_defineProperty", "<global>", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "JsiSkPathEffect", "constructor", "_defineProperty$argument_2"], "mappings": "AAA,oLC;ACC,2GD;AEC,wTF;OGE;ECC;qCCE;KDE;GDC;CHC"}}, "type": "js/module"}]}