{"dependencies": [{"name": "react-native-reanimated/package.json", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 7, "column": 28, "index": 366}, "end": {"line": 7, "column": 75, "index": 413}}], "key": "L1muT8DbRgNELk1rNId64LBjVL4=", "exportNames": ["*"], "isOptional": true}}, {"name": "react-native-reanimated", "data": {"asyncType": null, "isESMImport": false, "locs": [{"start": {"line": 8, "column": 2, "index": 425}, "end": {"line": 8, "column": 36, "index": 459}}], "key": "+aUP6OdvebG47hiJSteO076+5ZE=", "exportNames": ["*"], "isOptional": true}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.HAS_REANIMATED_3 = void 0;\n  let HAS_REANIMATED_3 = exports.HAS_REANIMATED_3 = false;\n  try {\n    // This logic is convoluted but necessary\n    // In most systems, `require(\"react-native-reanimated\")` throws an error, all is well.\n    // In webpack, in some configuration it will return an empty object.\n    // So it will not throw an error and we need to check the version to know if it's there.\n    const reanimatedVersion = require(_dependencyMap[0], \"react-native-reanimated/package.json\").version;\n    require(_dependencyMap[1], \"react-native-reanimated\");\n    if (reanimatedVersion && (reanimatedVersion >= \"3.0.0\" || reanimatedVersion.includes(\"3.0.0-\"))) {\n      exports.HAS_REANIMATED_3 = HAS_REANIMATED_3 = true;\n    }\n  } catch (e) {\n    // do nothing\n  }\n});", "lineCount": 20, "map": [[6, 2, 1, 7], [6, 6, 1, 11, "HAS_REANIMATED_3"], [6, 22, 1, 27], [6, 25, 1, 27, "exports"], [6, 32, 1, 27], [6, 33, 1, 27, "HAS_REANIMATED_3"], [6, 49, 1, 27], [6, 52, 1, 30], [6, 57, 1, 35], [7, 2, 2, 0], [7, 6, 2, 4], [8, 4, 3, 2], [9, 4, 4, 2], [10, 4, 5, 2], [11, 4, 6, 2], [12, 4, 7, 2], [12, 10, 7, 8, "reanimatedVersion"], [12, 27, 7, 25], [12, 30, 7, 28, "require"], [12, 37, 7, 35], [12, 38, 7, 35, "_dependencyMap"], [12, 52, 7, 35], [12, 95, 7, 74], [12, 96, 7, 75], [12, 97, 7, 76, "version"], [12, 104, 7, 83], [13, 4, 8, 2, "require"], [13, 11, 8, 9], [13, 12, 8, 9, "_dependencyMap"], [13, 26, 8, 9], [13, 56, 8, 35], [13, 57, 8, 36], [14, 4, 9, 2], [14, 8, 9, 6, "reanimatedVersion"], [14, 25, 9, 23], [14, 30, 9, 28, "reanimatedVersion"], [14, 47, 9, 45], [14, 51, 9, 49], [14, 58, 9, 56], [14, 62, 9, 60, "reanimatedVersion"], [14, 79, 9, 77], [14, 80, 9, 78, "includes"], [14, 88, 9, 86], [14, 89, 9, 87], [14, 97, 9, 95], [14, 98, 9, 96], [14, 99, 9, 97], [14, 101, 9, 99], [15, 6, 10, 4, "exports"], [15, 13, 10, 4], [15, 14, 10, 4, "HAS_REANIMATED_3"], [15, 30, 10, 4], [15, 33, 10, 4, "HAS_REANIMATED_3"], [15, 49, 10, 20], [15, 52, 10, 23], [15, 56, 10, 27], [16, 4, 11, 2], [17, 2, 12, 0], [17, 3, 12, 1], [17, 4, 12, 2], [17, 11, 12, 9, "e"], [17, 12, 12, 10], [17, 14, 12, 12], [18, 4, 13, 2], [19, 2, 13, 2], [20, 0, 14, 1], [20, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}