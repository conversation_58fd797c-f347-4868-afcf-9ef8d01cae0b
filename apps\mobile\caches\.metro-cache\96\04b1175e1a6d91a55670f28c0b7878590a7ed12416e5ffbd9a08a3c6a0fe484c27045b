{"dependencies": [{"name": "./Coordinates", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 30, "index": 30}}], "key": "sIVxZvNHFmYZfO7p7LGJvl+4EqA=", "exportNames": ["*"]}}, {"name": "./Math", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 31}, "end": {"line": 2, "column": 23, "index": 54}}], "key": "CcwTHUFay8qAaPRKnEe5eebXhrI=", "exportNames": ["*"]}}, {"name": "./Transforms", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 55}, "end": {"line": 3, "column": 29, "index": 84}}], "key": "VHi2j8YENu0sB8LgeMQQRpKBkRo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  var _Coordinates = require(_dependencyMap[0], \"./Coordinates\");\n  Object.keys(_Coordinates).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _Coordinates[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _Coordinates[key];\n      }\n    });\n  });\n  var _Math = require(_dependencyMap[1], \"./Math\");\n  Object.keys(_Math).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _Math[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _Math[key];\n      }\n    });\n  });\n  var _Transforms = require(_dependencyMap[2], \"./Transforms\");\n  Object.keys(_Transforms).forEach(function (key) {\n    if (key === \"default\" || key === \"__esModule\") return;\n    if (key in exports && exports[key] === _Transforms[key]) return;\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return _Transforms[key];\n      }\n    });\n  });\n});", "lineCount": 38, "map": [[5, 2, 1, 0], [5, 6, 1, 0, "_Coordinates"], [5, 18, 1, 0], [5, 21, 1, 0, "require"], [5, 28, 1, 0], [5, 29, 1, 0, "_dependencyMap"], [5, 43, 1, 0], [6, 2, 1, 0, "Object"], [6, 8, 1, 0], [6, 9, 1, 0, "keys"], [6, 13, 1, 0], [6, 14, 1, 0, "_Coordinates"], [6, 26, 1, 0], [6, 28, 1, 0, "for<PERSON>ach"], [6, 35, 1, 0], [6, 46, 1, 0, "key"], [6, 49, 1, 0], [7, 4, 1, 0], [7, 8, 1, 0, "key"], [7, 11, 1, 0], [7, 29, 1, 0, "key"], [7, 32, 1, 0], [8, 4, 1, 0], [8, 8, 1, 0, "key"], [8, 11, 1, 0], [8, 15, 1, 0, "exports"], [8, 22, 1, 0], [8, 26, 1, 0, "exports"], [8, 33, 1, 0], [8, 34, 1, 0, "key"], [8, 37, 1, 0], [8, 43, 1, 0, "_Coordinates"], [8, 55, 1, 0], [8, 56, 1, 0, "key"], [8, 59, 1, 0], [9, 4, 1, 0, "Object"], [9, 10, 1, 0], [9, 11, 1, 0, "defineProperty"], [9, 25, 1, 0], [9, 26, 1, 0, "exports"], [9, 33, 1, 0], [9, 35, 1, 0, "key"], [9, 38, 1, 0], [10, 6, 1, 0, "enumerable"], [10, 16, 1, 0], [11, 6, 1, 0, "get"], [11, 9, 1, 0], [11, 20, 1, 0, "get"], [11, 21, 1, 0], [12, 8, 1, 0], [12, 15, 1, 0, "_Coordinates"], [12, 27, 1, 0], [12, 28, 1, 0, "key"], [12, 31, 1, 0], [13, 6, 1, 0], [14, 4, 1, 0], [15, 2, 1, 0], [16, 2, 2, 0], [16, 6, 2, 0, "_Math"], [16, 11, 2, 0], [16, 14, 2, 0, "require"], [16, 21, 2, 0], [16, 22, 2, 0, "_dependencyMap"], [16, 36, 2, 0], [17, 2, 2, 0, "Object"], [17, 8, 2, 0], [17, 9, 2, 0, "keys"], [17, 13, 2, 0], [17, 14, 2, 0, "_Math"], [17, 19, 2, 0], [17, 21, 2, 0, "for<PERSON>ach"], [17, 28, 2, 0], [17, 39, 2, 0, "key"], [17, 42, 2, 0], [18, 4, 2, 0], [18, 8, 2, 0, "key"], [18, 11, 2, 0], [18, 29, 2, 0, "key"], [18, 32, 2, 0], [19, 4, 2, 0], [19, 8, 2, 0, "key"], [19, 11, 2, 0], [19, 15, 2, 0, "exports"], [19, 22, 2, 0], [19, 26, 2, 0, "exports"], [19, 33, 2, 0], [19, 34, 2, 0, "key"], [19, 37, 2, 0], [19, 43, 2, 0, "_Math"], [19, 48, 2, 0], [19, 49, 2, 0, "key"], [19, 52, 2, 0], [20, 4, 2, 0, "Object"], [20, 10, 2, 0], [20, 11, 2, 0, "defineProperty"], [20, 25, 2, 0], [20, 26, 2, 0, "exports"], [20, 33, 2, 0], [20, 35, 2, 0, "key"], [20, 38, 2, 0], [21, 6, 2, 0, "enumerable"], [21, 16, 2, 0], [22, 6, 2, 0, "get"], [22, 9, 2, 0], [22, 20, 2, 0, "get"], [22, 21, 2, 0], [23, 8, 2, 0], [23, 15, 2, 0, "_Math"], [23, 20, 2, 0], [23, 21, 2, 0, "key"], [23, 24, 2, 0], [24, 6, 2, 0], [25, 4, 2, 0], [26, 2, 2, 0], [27, 2, 3, 0], [27, 6, 3, 0, "_Transforms"], [27, 17, 3, 0], [27, 20, 3, 0, "require"], [27, 27, 3, 0], [27, 28, 3, 0, "_dependencyMap"], [27, 42, 3, 0], [28, 2, 3, 0, "Object"], [28, 8, 3, 0], [28, 9, 3, 0, "keys"], [28, 13, 3, 0], [28, 14, 3, 0, "_Transforms"], [28, 25, 3, 0], [28, 27, 3, 0, "for<PERSON>ach"], [28, 34, 3, 0], [28, 45, 3, 0, "key"], [28, 48, 3, 0], [29, 4, 3, 0], [29, 8, 3, 0, "key"], [29, 11, 3, 0], [29, 29, 3, 0, "key"], [29, 32, 3, 0], [30, 4, 3, 0], [30, 8, 3, 0, "key"], [30, 11, 3, 0], [30, 15, 3, 0, "exports"], [30, 22, 3, 0], [30, 26, 3, 0, "exports"], [30, 33, 3, 0], [30, 34, 3, 0, "key"], [30, 37, 3, 0], [30, 43, 3, 0, "_Transforms"], [30, 54, 3, 0], [30, 55, 3, 0, "key"], [30, 58, 3, 0], [31, 4, 3, 0, "Object"], [31, 10, 3, 0], [31, 11, 3, 0, "defineProperty"], [31, 25, 3, 0], [31, 26, 3, 0, "exports"], [31, 33, 3, 0], [31, 35, 3, 0, "key"], [31, 38, 3, 0], [32, 6, 3, 0, "enumerable"], [32, 16, 3, 0], [33, 6, 3, 0, "get"], [33, 9, 3, 0], [33, 20, 3, 0, "get"], [33, 21, 3, 0], [34, 8, 3, 0], [34, 15, 3, 0, "_Transforms"], [34, 26, 3, 0], [34, 27, 3, 0, "key"], [34, 30, 3, 0], [35, 6, 3, 0], [36, 4, 3, 0], [37, 2, 3, 0], [38, 0, 3, 29], [38, 3]], "functionMap": {"names": ["<global>"], "mappings": "AAA"}}, "type": "js/module"}]}