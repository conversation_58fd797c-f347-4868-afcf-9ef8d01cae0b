"use strict";
/**
 * @license
 * Copyright 2018 Google Inc. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
Object.defineProperty(exports, "__esModule", { value: true });
var glsl_version_1 = require("./glsl_version");
var shader_util = require("./shader_compiler_util");
var EncodeMatrixProgram = /** @class */ (function () {
    function EncodeMatrixProgram(outputShape, texShape, inputIsUnsignedByte) {
        if (inputIsUnsignedByte === void 0) { inputIsUnsignedByte = false; }
        this.variableNames = ['A'];
        var glsl = glsl_version_1.getGlslDifferences();
        var height = texShape[0], width = texShape[1];
        this.outputShape = outputShape;
        var output = "result";
        if (inputIsUnsignedByte) {
            output = "floor(result * 255. + 0.5)";
        }
        this.userCode = "\n      " + shader_util.getFlatIndexFrom3D(outputShape) + "\n\n      void main() {\n        ivec3 coords = getOutputCoords();\n\n        int flatIndex = getFlatIndex(coords);\n        int offset = imod(flatIndex, 4);\n\n        flatIndex = idiv(flatIndex, 4, 1.);\n        \n        int r = flatIndex / " + width + ";\n        int c = imod(flatIndex, " + width + ");\n        vec2 uv = (vec2(c, r) + halfCR) / vec2(" + width + ".0, " + height + ".0);\n        vec4 values = " + glsl.texture2D + "(A, uv);\n\n        float result;\n\n        if(offset == 0) {\n          result = values[0];\n        } else if(offset == 1) {\n          result = values[1];\n        } else if(offset == 2) {\n          result = values[2];\n        } else {\n          result = values[3];\n        }\n\n        " + glsl.output + " = vec4(" + output + ", 0., 0., 0.);\n      }\n    ";
    }
    return EncodeMatrixProgram;
}());
exports.EncodeMatrixProgram = EncodeMatrixProgram;
//# sourceMappingURL=encode_matrix_gpu.js.map