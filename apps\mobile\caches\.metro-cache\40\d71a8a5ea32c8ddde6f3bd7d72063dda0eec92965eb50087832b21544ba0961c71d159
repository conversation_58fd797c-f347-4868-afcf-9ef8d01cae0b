{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 30, "index": 30}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 31}, "end": {"line": 2, "column": 40, "index": 71}}], "key": "c+biP0KCYKcLc6xq5NFUJB5wGKM=", "exportNames": ["*"]}}, {"name": "./JsiSkAnimatedImage", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 72}, "end": {"line": 3, "column": 58, "index": 130}}], "key": "tEnKtrX065N5iNpJA0IDVy/2YEk=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkAnimatedImageFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkData = require(_dependencyMap[1], \"./JsiSkData\");\n  var _JsiSkAnimatedImage = require(_dependencyMap[2], \"./JsiSkAnimatedImage\");\n  class JsiSkAnimatedImageFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    MakeAnimatedImageFromEncoded(encoded) {\n      const image = this.CanvasKit.MakeAnimatedImageFromEncoded(_JsiSkData.JsiSkData.fromValue(encoded));\n      if (image === null) {\n        return null;\n      }\n      return new _JsiSkAnimatedImage.JsiSkAnimatedImage(this.CanvasKit, image);\n    }\n  }\n  exports.JsiSkAnimatedImageFactory = JsiSkAnimatedImageFactory;\n});", "lineCount": 22, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkData"], [7, 16, 2, 0], [7, 19, 2, 0, "require"], [7, 26, 2, 0], [7, 27, 2, 0, "_dependencyMap"], [7, 41, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_JsiSkAnimatedImage"], [8, 25, 3, 0], [8, 28, 3, 0, "require"], [8, 35, 3, 0], [8, 36, 3, 0, "_dependencyMap"], [8, 50, 3, 0], [9, 2, 4, 7], [9, 8, 4, 13, "JsiSkAnimatedImageFactory"], [9, 33, 4, 38], [9, 42, 4, 47, "Host"], [9, 52, 4, 51], [9, 53, 4, 52], [10, 4, 5, 2, "constructor"], [10, 15, 5, 13, "constructor"], [10, 16, 5, 14, "CanvasKit"], [10, 25, 5, 23], [10, 27, 5, 25], [11, 6, 6, 4], [11, 11, 6, 9], [11, 12, 6, 10, "CanvasKit"], [11, 21, 6, 19], [11, 22, 6, 20], [12, 4, 7, 2], [13, 4, 8, 2, "MakeAnimatedImageFromEncoded"], [13, 32, 8, 30, "MakeAnimatedImageFromEncoded"], [13, 33, 8, 31, "encoded"], [13, 40, 8, 38], [13, 42, 8, 40], [14, 6, 9, 4], [14, 12, 9, 10, "image"], [14, 17, 9, 15], [14, 20, 9, 18], [14, 24, 9, 22], [14, 25, 9, 23, "CanvasKit"], [14, 34, 9, 32], [14, 35, 9, 33, "MakeAnimatedImageFromEncoded"], [14, 63, 9, 61], [14, 64, 9, 62, "JsiSkData"], [14, 84, 9, 71], [14, 85, 9, 72, "fromValue"], [14, 94, 9, 81], [14, 95, 9, 82, "encoded"], [14, 102, 9, 89], [14, 103, 9, 90], [14, 104, 9, 91], [15, 6, 10, 4], [15, 10, 10, 8, "image"], [15, 15, 10, 13], [15, 20, 10, 18], [15, 24, 10, 22], [15, 26, 10, 24], [16, 8, 11, 6], [16, 15, 11, 13], [16, 19, 11, 17], [17, 6, 12, 4], [18, 6, 13, 4], [18, 13, 13, 11], [18, 17, 13, 15, "JsiSkAnimatedImage"], [18, 55, 13, 33], [18, 56, 13, 34], [18, 60, 13, 38], [18, 61, 13, 39, "CanvasKit"], [18, 70, 13, 48], [18, 72, 13, 50, "image"], [18, 77, 13, 55], [18, 78, 13, 56], [19, 4, 14, 2], [20, 2, 15, 0], [21, 2, 15, 1, "exports"], [21, 9, 15, 1], [21, 10, 15, 1, "JsiSkAnimatedImageFactory"], [21, 35, 15, 1], [21, 38, 15, 1, "JsiSkAnimatedImageFactory"], [21, 63, 15, 1], [22, 0, 15, 1], [22, 3]], "functionMap": {"names": ["<global>", "JsiSkAnimatedImageFactory", "constructor", "MakeAnimatedImageFromEncoded"], "mappings": "AAA;OCG;ECC;GDE;EEC;GFM;CDC"}}, "type": "js/module"}]}