{"dependencies": [{"name": "../utils.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 61}, "end": {"line": 2, "column": 42, "index": 103}}], "key": "V6Cd8v/K/LtdxP2wf1WD5HCNhqM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _utils = require(_dependencyMap[0], \"../utils.web\");\n  /**\n   * Hermite resize - fast image resize/resample using Hermite filter. 1 cpu version!\n   * https://stackoverflow.com/a/18320662/4047926\n   *\n   * @param {HTMLCanvasElement} canvas\n   * @param {int} width\n   * @param {int} height\n   * @param {boolean} resizeCanvas if true, canvas will be resized. Optional.\n   */\n  function resampleSingle(canvas, width, height, resizeCanvas = false) {\n    const result = document.createElement('canvas');\n    result.width = canvas.width;\n    result.height = canvas.height;\n    const widthSource = canvas.width;\n    const heightSource = canvas.height;\n    width = Math.round(width);\n    height = Math.round(height);\n    const wRatio = widthSource / width;\n    const hRatio = heightSource / height;\n    const wRatioHalf = Math.ceil(wRatio / 2);\n    const hRatioHalf = Math.ceil(hRatio / 2);\n    const ctx = (0, _utils.getContext)(canvas);\n    const img = ctx.getImageData(0, 0, widthSource, heightSource);\n    const img2 = ctx.createImageData(width, height);\n    const data = img.data;\n    const data2 = img2.data;\n    for (let j = 0; j < height; j++) {\n      for (let i = 0; i < width; i++) {\n        const x2 = (i + j * width) * 4;\n        let weight = 0;\n        let weights = 0;\n        let weightsAlpha = 0;\n        let gx_r = 0;\n        let gx_g = 0;\n        let gx_b = 0;\n        let gx_a = 0;\n        const yCenter = (j + 0.5) * hRatio;\n        const yy_start = Math.floor(j * hRatio);\n        const yy_stop = Math.ceil((j + 1) * hRatio);\n        for (let yy = yy_start; yy < yy_stop; yy++) {\n          const dy = Math.abs(yCenter - (yy + 0.5)) / hRatioHalf;\n          const center_x = (i + 0.5) * wRatio;\n          const w0 = dy * dy; //pre-calc part of w\n          const xx_start = Math.floor(i * wRatio);\n          const xx_stop = Math.ceil((i + 1) * wRatio);\n          for (let xx = xx_start; xx < xx_stop; xx++) {\n            const dx = Math.abs(center_x - (xx + 0.5)) / wRatioHalf;\n            const w = Math.sqrt(w0 + dx * dx);\n            if (w >= 1) {\n              //pixel too far\n              continue;\n            }\n            //hermite filter\n            weight = 2 * w * w * w - 3 * w * w + 1;\n            const xPosition = 4 * (xx + yy * widthSource);\n            //alpha\n            gx_a += weight * data[xPosition + 3];\n            weightsAlpha += weight;\n            //colors\n            if (data[xPosition + 3] < 255) {\n              weight = weight * data[xPosition + 3] / 250;\n            }\n            gx_r += weight * data[xPosition];\n            gx_g += weight * data[xPosition + 1];\n            gx_b += weight * data[xPosition + 2];\n            weights += weight;\n          }\n        }\n        data2[x2] = gx_r / weights;\n        data2[x2 + 1] = gx_g / weights;\n        data2[x2 + 2] = gx_b / weights;\n        data2[x2 + 3] = gx_a / weightsAlpha;\n      }\n    }\n\n    //resize canvas\n    if (resizeCanvas) {\n      result.width = width;\n      result.height = height;\n    }\n\n    //draw\n    const context = (0, _utils.getContext)(result);\n    context.putImageData(img2, 0, 0);\n    return result;\n  }\n  var _default = (canvas, {\n    width,\n    height\n  }) => {\n    const imageRatio = canvas.width / canvas.height;\n    let requestedWidth = 0;\n    let requestedHeight = 0;\n    if (width !== undefined) {\n      requestedWidth = width;\n      requestedHeight = requestedWidth / imageRatio;\n    }\n    if (height !== undefined) {\n      requestedHeight = height;\n      if (requestedWidth === 0) {\n        requestedWidth = requestedHeight * imageRatio;\n      }\n    }\n    return resampleSingle(canvas, requestedWidth, requestedHeight, true);\n  };\n  exports.default = _default;\n});", "lineCount": 113, "map": [[6, 2, 2, 0], [6, 6, 2, 0, "_utils"], [6, 12, 2, 0], [6, 15, 2, 0, "require"], [6, 22, 2, 0], [6, 23, 2, 0, "_dependencyMap"], [6, 37, 2, 0], [7, 2, 4, 0], [8, 0, 5, 0], [9, 0, 6, 0], [10, 0, 7, 0], [11, 0, 8, 0], [12, 0, 9, 0], [13, 0, 10, 0], [14, 0, 11, 0], [15, 0, 12, 0], [16, 2, 13, 0], [16, 11, 13, 9, "resampleSingle"], [16, 25, 13, 23, "resampleSingle"], [16, 26, 14, 2, "canvas"], [16, 32, 14, 27], [16, 34, 15, 2, "width"], [16, 39, 15, 15], [16, 41, 16, 2, "height"], [16, 47, 16, 16], [16, 49, 17, 2, "resizeCanvas"], [16, 61, 17, 23], [16, 64, 17, 26], [16, 69, 17, 31], [16, 71, 18, 21], [17, 4, 19, 2], [17, 10, 19, 8, "result"], [17, 16, 19, 14], [17, 19, 19, 17, "document"], [17, 27, 19, 25], [17, 28, 19, 26, "createElement"], [17, 41, 19, 39], [17, 42, 19, 40], [17, 50, 19, 48], [17, 51, 19, 49], [18, 4, 20, 2, "result"], [18, 10, 20, 8], [18, 11, 20, 9, "width"], [18, 16, 20, 14], [18, 19, 20, 17, "canvas"], [18, 25, 20, 23], [18, 26, 20, 24, "width"], [18, 31, 20, 29], [19, 4, 21, 2, "result"], [19, 10, 21, 8], [19, 11, 21, 9, "height"], [19, 17, 21, 15], [19, 20, 21, 18, "canvas"], [19, 26, 21, 24], [19, 27, 21, 25, "height"], [19, 33, 21, 31], [20, 4, 23, 2], [20, 10, 23, 8, "widthSource"], [20, 21, 23, 19], [20, 24, 23, 22, "canvas"], [20, 30, 23, 28], [20, 31, 23, 29, "width"], [20, 36, 23, 34], [21, 4, 24, 2], [21, 10, 24, 8, "heightSource"], [21, 22, 24, 20], [21, 25, 24, 23, "canvas"], [21, 31, 24, 29], [21, 32, 24, 30, "height"], [21, 38, 24, 36], [22, 4, 25, 2, "width"], [22, 9, 25, 7], [22, 12, 25, 10, "Math"], [22, 16, 25, 14], [22, 17, 25, 15, "round"], [22, 22, 25, 20], [22, 23, 25, 21, "width"], [22, 28, 25, 26], [22, 29, 25, 27], [23, 4, 26, 2, "height"], [23, 10, 26, 8], [23, 13, 26, 11, "Math"], [23, 17, 26, 15], [23, 18, 26, 16, "round"], [23, 23, 26, 21], [23, 24, 26, 22, "height"], [23, 30, 26, 28], [23, 31, 26, 29], [24, 4, 28, 2], [24, 10, 28, 8, "wRatio"], [24, 16, 28, 14], [24, 19, 28, 17, "widthSource"], [24, 30, 28, 28], [24, 33, 28, 31, "width"], [24, 38, 28, 36], [25, 4, 29, 2], [25, 10, 29, 8, "hRatio"], [25, 16, 29, 14], [25, 19, 29, 17, "heightSource"], [25, 31, 29, 29], [25, 34, 29, 32, "height"], [25, 40, 29, 38], [26, 4, 30, 2], [26, 10, 30, 8, "wRatioHalf"], [26, 20, 30, 18], [26, 23, 30, 21, "Math"], [26, 27, 30, 25], [26, 28, 30, 26, "ceil"], [26, 32, 30, 30], [26, 33, 30, 31, "wRatio"], [26, 39, 30, 37], [26, 42, 30, 40], [26, 43, 30, 41], [26, 44, 30, 42], [27, 4, 31, 2], [27, 10, 31, 8, "hRatioHalf"], [27, 20, 31, 18], [27, 23, 31, 21, "Math"], [27, 27, 31, 25], [27, 28, 31, 26, "ceil"], [27, 32, 31, 30], [27, 33, 31, 31, "hRatio"], [27, 39, 31, 37], [27, 42, 31, 40], [27, 43, 31, 41], [27, 44, 31, 42], [28, 4, 33, 2], [28, 10, 33, 8, "ctx"], [28, 13, 33, 11], [28, 16, 33, 14], [28, 20, 33, 14, "getContext"], [28, 37, 33, 24], [28, 39, 33, 25, "canvas"], [28, 45, 33, 31], [28, 46, 33, 32], [29, 4, 35, 2], [29, 10, 35, 8, "img"], [29, 13, 35, 11], [29, 16, 35, 14, "ctx"], [29, 19, 35, 17], [29, 20, 35, 18, "getImageData"], [29, 32, 35, 30], [29, 33, 35, 31], [29, 34, 35, 32], [29, 36, 35, 34], [29, 37, 35, 35], [29, 39, 35, 37, "widthSource"], [29, 50, 35, 48], [29, 52, 35, 50, "heightSource"], [29, 64, 35, 62], [29, 65, 35, 63], [30, 4, 36, 2], [30, 10, 36, 8, "img2"], [30, 14, 36, 12], [30, 17, 36, 15, "ctx"], [30, 20, 36, 18], [30, 21, 36, 19, "createImageData"], [30, 36, 36, 34], [30, 37, 36, 35, "width"], [30, 42, 36, 40], [30, 44, 36, 42, "height"], [30, 50, 36, 48], [30, 51, 36, 49], [31, 4, 37, 2], [31, 10, 37, 8, "data"], [31, 14, 37, 12], [31, 17, 37, 15, "img"], [31, 20, 37, 18], [31, 21, 37, 19, "data"], [31, 25, 37, 23], [32, 4, 38, 2], [32, 10, 38, 8, "data2"], [32, 15, 38, 13], [32, 18, 38, 16, "img2"], [32, 22, 38, 20], [32, 23, 38, 21, "data"], [32, 27, 38, 25], [33, 4, 40, 2], [33, 9, 40, 7], [33, 13, 40, 11, "j"], [33, 14, 40, 12], [33, 17, 40, 15], [33, 18, 40, 16], [33, 20, 40, 18, "j"], [33, 21, 40, 19], [33, 24, 40, 22, "height"], [33, 30, 40, 28], [33, 32, 40, 30, "j"], [33, 33, 40, 31], [33, 35, 40, 33], [33, 37, 40, 35], [34, 6, 41, 4], [34, 11, 41, 9], [34, 15, 41, 13, "i"], [34, 16, 41, 14], [34, 19, 41, 17], [34, 20, 41, 18], [34, 22, 41, 20, "i"], [34, 23, 41, 21], [34, 26, 41, 24, "width"], [34, 31, 41, 29], [34, 33, 41, 31, "i"], [34, 34, 41, 32], [34, 36, 41, 34], [34, 38, 41, 36], [35, 8, 42, 6], [35, 14, 42, 12, "x2"], [35, 16, 42, 14], [35, 19, 42, 17], [35, 20, 42, 18, "i"], [35, 21, 42, 19], [35, 24, 42, 22, "j"], [35, 25, 42, 23], [35, 28, 42, 26, "width"], [35, 33, 42, 31], [35, 37, 42, 35], [35, 38, 42, 36], [36, 8, 43, 6], [36, 12, 43, 10, "weight"], [36, 18, 43, 16], [36, 21, 43, 19], [36, 22, 43, 20], [37, 8, 44, 6], [37, 12, 44, 10, "weights"], [37, 19, 44, 17], [37, 22, 44, 20], [37, 23, 44, 21], [38, 8, 45, 6], [38, 12, 45, 10, "weightsAlpha"], [38, 24, 45, 22], [38, 27, 45, 25], [38, 28, 45, 26], [39, 8, 46, 6], [39, 12, 46, 10, "gx_r"], [39, 16, 46, 14], [39, 19, 46, 17], [39, 20, 46, 18], [40, 8, 47, 6], [40, 12, 47, 10, "gx_g"], [40, 16, 47, 14], [40, 19, 47, 17], [40, 20, 47, 18], [41, 8, 48, 6], [41, 12, 48, 10, "gx_b"], [41, 16, 48, 14], [41, 19, 48, 17], [41, 20, 48, 18], [42, 8, 49, 6], [42, 12, 49, 10, "gx_a"], [42, 16, 49, 14], [42, 19, 49, 17], [42, 20, 49, 18], [43, 8, 50, 6], [43, 14, 50, 12, "yCenter"], [43, 21, 50, 19], [43, 24, 50, 22], [43, 25, 50, 23, "j"], [43, 26, 50, 24], [43, 29, 50, 27], [43, 32, 50, 30], [43, 36, 50, 34, "hRatio"], [43, 42, 50, 40], [44, 8, 51, 6], [44, 14, 51, 12, "yy_start"], [44, 22, 51, 20], [44, 25, 51, 23, "Math"], [44, 29, 51, 27], [44, 30, 51, 28, "floor"], [44, 35, 51, 33], [44, 36, 51, 34, "j"], [44, 37, 51, 35], [44, 40, 51, 38, "hRatio"], [44, 46, 51, 44], [44, 47, 51, 45], [45, 8, 52, 6], [45, 14, 52, 12, "yy_stop"], [45, 21, 52, 19], [45, 24, 52, 22, "Math"], [45, 28, 52, 26], [45, 29, 52, 27, "ceil"], [45, 33, 52, 31], [45, 34, 52, 32], [45, 35, 52, 33, "j"], [45, 36, 52, 34], [45, 39, 52, 37], [45, 40, 52, 38], [45, 44, 52, 42, "hRatio"], [45, 50, 52, 48], [45, 51, 52, 49], [46, 8, 53, 6], [46, 13, 53, 11], [46, 17, 53, 15, "yy"], [46, 19, 53, 17], [46, 22, 53, 20, "yy_start"], [46, 30, 53, 28], [46, 32, 53, 30, "yy"], [46, 34, 53, 32], [46, 37, 53, 35, "yy_stop"], [46, 44, 53, 42], [46, 46, 53, 44, "yy"], [46, 48, 53, 46], [46, 50, 53, 48], [46, 52, 53, 50], [47, 10, 54, 8], [47, 16, 54, 14, "dy"], [47, 18, 54, 16], [47, 21, 54, 19, "Math"], [47, 25, 54, 23], [47, 26, 54, 24, "abs"], [47, 29, 54, 27], [47, 30, 54, 28, "yCenter"], [47, 37, 54, 35], [47, 41, 54, 39, "yy"], [47, 43, 54, 41], [47, 46, 54, 44], [47, 49, 54, 47], [47, 50, 54, 48], [47, 51, 54, 49], [47, 54, 54, 52, "hRatioHalf"], [47, 64, 54, 62], [48, 10, 55, 8], [48, 16, 55, 14, "center_x"], [48, 24, 55, 22], [48, 27, 55, 25], [48, 28, 55, 26, "i"], [48, 29, 55, 27], [48, 32, 55, 30], [48, 35, 55, 33], [48, 39, 55, 37, "wRatio"], [48, 45, 55, 43], [49, 10, 56, 8], [49, 16, 56, 14, "w0"], [49, 18, 56, 16], [49, 21, 56, 19, "dy"], [49, 23, 56, 21], [49, 26, 56, 24, "dy"], [49, 28, 56, 26], [49, 29, 56, 27], [49, 30, 56, 28], [50, 10, 57, 8], [50, 16, 57, 14, "xx_start"], [50, 24, 57, 22], [50, 27, 57, 25, "Math"], [50, 31, 57, 29], [50, 32, 57, 30, "floor"], [50, 37, 57, 35], [50, 38, 57, 36, "i"], [50, 39, 57, 37], [50, 42, 57, 40, "wRatio"], [50, 48, 57, 46], [50, 49, 57, 47], [51, 10, 58, 8], [51, 16, 58, 14, "xx_stop"], [51, 23, 58, 21], [51, 26, 58, 24, "Math"], [51, 30, 58, 28], [51, 31, 58, 29, "ceil"], [51, 35, 58, 33], [51, 36, 58, 34], [51, 37, 58, 35, "i"], [51, 38, 58, 36], [51, 41, 58, 39], [51, 42, 58, 40], [51, 46, 58, 44, "wRatio"], [51, 52, 58, 50], [51, 53, 58, 51], [52, 10, 59, 8], [52, 15, 59, 13], [52, 19, 59, 17, "xx"], [52, 21, 59, 19], [52, 24, 59, 22, "xx_start"], [52, 32, 59, 30], [52, 34, 59, 32, "xx"], [52, 36, 59, 34], [52, 39, 59, 37, "xx_stop"], [52, 46, 59, 44], [52, 48, 59, 46, "xx"], [52, 50, 59, 48], [52, 52, 59, 50], [52, 54, 59, 52], [53, 12, 60, 10], [53, 18, 60, 16, "dx"], [53, 20, 60, 18], [53, 23, 60, 21, "Math"], [53, 27, 60, 25], [53, 28, 60, 26, "abs"], [53, 31, 60, 29], [53, 32, 60, 30, "center_x"], [53, 40, 60, 38], [53, 44, 60, 42, "xx"], [53, 46, 60, 44], [53, 49, 60, 47], [53, 52, 60, 50], [53, 53, 60, 51], [53, 54, 60, 52], [53, 57, 60, 55, "wRatioHalf"], [53, 67, 60, 65], [54, 12, 61, 10], [54, 18, 61, 16, "w"], [54, 19, 61, 17], [54, 22, 61, 20, "Math"], [54, 26, 61, 24], [54, 27, 61, 25, "sqrt"], [54, 31, 61, 29], [54, 32, 61, 30, "w0"], [54, 34, 61, 32], [54, 37, 61, 35, "dx"], [54, 39, 61, 37], [54, 42, 61, 40, "dx"], [54, 44, 61, 42], [54, 45, 61, 43], [55, 12, 62, 10], [55, 16, 62, 14, "w"], [55, 17, 62, 15], [55, 21, 62, 19], [55, 22, 62, 20], [55, 24, 62, 22], [56, 14, 63, 12], [57, 14, 64, 12], [58, 12, 65, 10], [59, 12, 66, 10], [60, 12, 67, 10, "weight"], [60, 18, 67, 16], [60, 21, 67, 19], [60, 22, 67, 20], [60, 25, 67, 23, "w"], [60, 26, 67, 24], [60, 29, 67, 27, "w"], [60, 30, 67, 28], [60, 33, 67, 31, "w"], [60, 34, 67, 32], [60, 37, 67, 35], [60, 38, 67, 36], [60, 41, 67, 39, "w"], [60, 42, 67, 40], [60, 45, 67, 43, "w"], [60, 46, 67, 44], [60, 49, 67, 47], [60, 50, 67, 48], [61, 12, 68, 10], [61, 18, 68, 16, "xPosition"], [61, 27, 68, 25], [61, 30, 68, 28], [61, 31, 68, 29], [61, 35, 68, 33, "xx"], [61, 37, 68, 35], [61, 40, 68, 38, "yy"], [61, 42, 68, 40], [61, 45, 68, 43, "widthSource"], [61, 56, 68, 54], [61, 57, 68, 55], [62, 12, 69, 10], [63, 12, 70, 10, "gx_a"], [63, 16, 70, 14], [63, 20, 70, 18, "weight"], [63, 26, 70, 24], [63, 29, 70, 27, "data"], [63, 33, 70, 31], [63, 34, 70, 32, "xPosition"], [63, 43, 70, 41], [63, 46, 70, 44], [63, 47, 70, 45], [63, 48, 70, 46], [64, 12, 71, 10, "weightsAlpha"], [64, 24, 71, 22], [64, 28, 71, 26, "weight"], [64, 34, 71, 32], [65, 12, 72, 10], [66, 12, 73, 10], [66, 16, 73, 14, "data"], [66, 20, 73, 18], [66, 21, 73, 19, "xPosition"], [66, 30, 73, 28], [66, 33, 73, 31], [66, 34, 73, 32], [66, 35, 73, 33], [66, 38, 73, 36], [66, 41, 73, 39], [66, 43, 73, 41], [67, 14, 74, 12, "weight"], [67, 20, 74, 18], [67, 23, 74, 22, "weight"], [67, 29, 74, 28], [67, 32, 74, 31, "data"], [67, 36, 74, 35], [67, 37, 74, 36, "xPosition"], [67, 46, 74, 45], [67, 49, 74, 48], [67, 50, 74, 49], [67, 51, 74, 50], [67, 54, 74, 54], [67, 57, 74, 57], [68, 12, 75, 10], [69, 12, 76, 10, "gx_r"], [69, 16, 76, 14], [69, 20, 76, 18, "weight"], [69, 26, 76, 24], [69, 29, 76, 27, "data"], [69, 33, 76, 31], [69, 34, 76, 32, "xPosition"], [69, 43, 76, 41], [69, 44, 76, 42], [70, 12, 77, 10, "gx_g"], [70, 16, 77, 14], [70, 20, 77, 18, "weight"], [70, 26, 77, 24], [70, 29, 77, 27, "data"], [70, 33, 77, 31], [70, 34, 77, 32, "xPosition"], [70, 43, 77, 41], [70, 46, 77, 44], [70, 47, 77, 45], [70, 48, 77, 46], [71, 12, 78, 10, "gx_b"], [71, 16, 78, 14], [71, 20, 78, 18, "weight"], [71, 26, 78, 24], [71, 29, 78, 27, "data"], [71, 33, 78, 31], [71, 34, 78, 32, "xPosition"], [71, 43, 78, 41], [71, 46, 78, 44], [71, 47, 78, 45], [71, 48, 78, 46], [72, 12, 79, 10, "weights"], [72, 19, 79, 17], [72, 23, 79, 21, "weight"], [72, 29, 79, 27], [73, 10, 80, 8], [74, 8, 81, 6], [75, 8, 82, 6, "data2"], [75, 13, 82, 11], [75, 14, 82, 12, "x2"], [75, 16, 82, 14], [75, 17, 82, 15], [75, 20, 82, 18, "gx_r"], [75, 24, 82, 22], [75, 27, 82, 25, "weights"], [75, 34, 82, 32], [76, 8, 83, 6, "data2"], [76, 13, 83, 11], [76, 14, 83, 12, "x2"], [76, 16, 83, 14], [76, 19, 83, 17], [76, 20, 83, 18], [76, 21, 83, 19], [76, 24, 83, 22, "gx_g"], [76, 28, 83, 26], [76, 31, 83, 29, "weights"], [76, 38, 83, 36], [77, 8, 84, 6, "data2"], [77, 13, 84, 11], [77, 14, 84, 12, "x2"], [77, 16, 84, 14], [77, 19, 84, 17], [77, 20, 84, 18], [77, 21, 84, 19], [77, 24, 84, 22, "gx_b"], [77, 28, 84, 26], [77, 31, 84, 29, "weights"], [77, 38, 84, 36], [78, 8, 85, 6, "data2"], [78, 13, 85, 11], [78, 14, 85, 12, "x2"], [78, 16, 85, 14], [78, 19, 85, 17], [78, 20, 85, 18], [78, 21, 85, 19], [78, 24, 85, 22, "gx_a"], [78, 28, 85, 26], [78, 31, 85, 29, "weightsAlpha"], [78, 43, 85, 41], [79, 6, 86, 4], [80, 4, 87, 2], [82, 4, 89, 2], [83, 4, 90, 2], [83, 8, 90, 6, "resizeCanvas"], [83, 20, 90, 18], [83, 22, 90, 20], [84, 6, 91, 4, "result"], [84, 12, 91, 10], [84, 13, 91, 11, "width"], [84, 18, 91, 16], [84, 21, 91, 19, "width"], [84, 26, 91, 24], [85, 6, 92, 4, "result"], [85, 12, 92, 10], [85, 13, 92, 11, "height"], [85, 19, 92, 17], [85, 22, 92, 20, "height"], [85, 28, 92, 26], [86, 4, 93, 2], [88, 4, 95, 2], [89, 4, 96, 2], [89, 10, 96, 8, "context"], [89, 17, 96, 15], [89, 20, 96, 18], [89, 24, 96, 18, "getContext"], [89, 41, 96, 28], [89, 43, 96, 29, "result"], [89, 49, 96, 35], [89, 50, 96, 36], [90, 4, 97, 2, "context"], [90, 11, 97, 9], [90, 12, 97, 10, "putImageData"], [90, 24, 97, 22], [90, 25, 97, 23, "img2"], [90, 29, 97, 27], [90, 31, 97, 29], [90, 32, 97, 30], [90, 34, 97, 32], [90, 35, 97, 33], [90, 36, 97, 34], [91, 4, 99, 2], [91, 11, 99, 9, "result"], [91, 17, 99, 15], [92, 2, 100, 0], [93, 2, 100, 1], [93, 6, 100, 1, "_default"], [93, 14, 100, 1], [93, 17, 102, 15, "_default"], [93, 18, 102, 16, "canvas"], [93, 24, 102, 41], [93, 26, 102, 43], [94, 4, 102, 45, "width"], [94, 9, 102, 50], [95, 4, 102, 52, "height"], [96, 2, 102, 83], [96, 3, 102, 84], [96, 8, 102, 89], [97, 4, 103, 2], [97, 10, 103, 8, "imageRatio"], [97, 20, 103, 18], [97, 23, 103, 21, "canvas"], [97, 29, 103, 27], [97, 30, 103, 28, "width"], [97, 35, 103, 33], [97, 38, 103, 36, "canvas"], [97, 44, 103, 42], [97, 45, 103, 43, "height"], [97, 51, 103, 49], [98, 4, 105, 2], [98, 8, 105, 6, "<PERSON><PERSON><PERSON><PERSON>"], [98, 22, 105, 28], [98, 25, 105, 31], [98, 26, 105, 32], [99, 4, 106, 2], [99, 8, 106, 6, "requestedHeight"], [99, 23, 106, 29], [99, 26, 106, 32], [99, 27, 106, 33], [100, 4, 107, 2], [100, 8, 107, 6, "width"], [100, 13, 107, 11], [100, 18, 107, 16, "undefined"], [100, 27, 107, 25], [100, 29, 107, 27], [101, 6, 108, 4, "<PERSON><PERSON><PERSON><PERSON>"], [101, 20, 108, 18], [101, 23, 108, 21, "width"], [101, 28, 108, 26], [102, 6, 109, 4, "requestedHeight"], [102, 21, 109, 19], [102, 24, 109, 22, "<PERSON><PERSON><PERSON><PERSON>"], [102, 38, 109, 36], [102, 41, 109, 39, "imageRatio"], [102, 51, 109, 49], [103, 4, 110, 2], [104, 4, 111, 2], [104, 8, 111, 6, "height"], [104, 14, 111, 12], [104, 19, 111, 17, "undefined"], [104, 28, 111, 26], [104, 30, 111, 28], [105, 6, 112, 4, "requestedHeight"], [105, 21, 112, 19], [105, 24, 112, 22, "height"], [105, 30, 112, 28], [106, 6, 113, 4], [106, 10, 113, 8, "<PERSON><PERSON><PERSON><PERSON>"], [106, 24, 113, 22], [106, 29, 113, 27], [106, 30, 113, 28], [106, 32, 113, 30], [107, 8, 114, 6, "<PERSON><PERSON><PERSON><PERSON>"], [107, 22, 114, 20], [107, 25, 114, 23, "requestedHeight"], [107, 40, 114, 38], [107, 43, 114, 41, "imageRatio"], [107, 53, 114, 51], [108, 6, 115, 4], [109, 4, 116, 2], [110, 4, 118, 2], [110, 11, 118, 9, "resampleSingle"], [110, 25, 118, 23], [110, 26, 118, 24, "canvas"], [110, 32, 118, 30], [110, 34, 118, 32, "<PERSON><PERSON><PERSON><PERSON>"], [110, 48, 118, 46], [110, 50, 118, 48, "requestedHeight"], [110, 65, 118, 63], [110, 67, 118, 65], [110, 71, 118, 69], [110, 72, 118, 70], [111, 2, 119, 0], [111, 3, 119, 1], [112, 2, 119, 1, "exports"], [112, 9, 119, 1], [112, 10, 119, 1, "default"], [112, 17, 119, 1], [112, 20, 119, 1, "_default"], [112, 28, 119, 1], [113, 0, 119, 1], [113, 3]], "functionMap": {"names": ["<global>", "resampleSingle", "default"], "mappings": "AAA;ACY;CDuF;eEE;CFiB"}}, "type": "js/module"}]}