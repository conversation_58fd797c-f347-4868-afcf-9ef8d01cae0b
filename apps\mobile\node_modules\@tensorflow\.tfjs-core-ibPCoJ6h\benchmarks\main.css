/**
* @license
* Copyright 2019 Google LLC. All Rights Reserved.
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
* http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
* =============================================================================
*/

html,
body {
  font-family: 'Roboto', sans-serif;
  font-size: 13px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
}

body {
  margin: 20px 100px;
}

h2 {
  margin-bottom: 30px;
}

#kernels {
  max-width: 750px;
}

#container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.box {
  margin-right: 30px;
  margin-bottom: 30px;
}

.box pre {
  margin: 0;
  border: 1px solid #ccc;
  padding: 8px;
  font-size: 10px;
}

div[id*='trendline-container'] svg {
  overflow: visible;
  border-bottom: 1px solid #ccc;
  border-left: 1px solid #ccc;
}

div[id*='trendline-container'] .label {
  font-size: 14px;
  font-weight: bold;
}

div[id*='trendline-container'] path {
  fill: none;
  stroke: #222;
}

.trendline {
  position: relative;
  margin-top: 20px;
}

.trendline .yMax,
.trendline .yMin {
  position: absolute;
  right: calc(100% + 6px);
  font-size: 11px;
  white-space: nowrap;
}

.trendline .yMin {
  bottom: 0;
}

.trendline .yMax {
  top: 0;
}

#modal-msg {
  border-radius: 5px;
  background-color: black;
  color: white;
  padding: 7px;
  top: 15px;
  left: 45%;
  display: none;
  position: absolute;
}

.table {
  margin-right: 30px;
  margin-bottom: 30px;
  border: 1px solid #ccc;
  border-collapse: collapse;
  border-spacing: 0;
}

.table tr {
  border-bottom: 1px solid #ddd;
}

.table tr:nth-child(even) {
  background-color: #f1f1f1;
}

.table th {
  font-weight: bold;
}

.table td,
th {
  padding: 8px 8px;
  font-size: 13px;
  text-align: left;
  vertical-align: top;
}

.table td:first-child,
th:first-child {
  padding-left: 16px;
}
