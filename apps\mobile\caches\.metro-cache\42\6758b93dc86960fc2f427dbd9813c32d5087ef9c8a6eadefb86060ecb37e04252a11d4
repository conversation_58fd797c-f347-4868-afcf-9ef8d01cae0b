{"dependencies": [{"name": "../types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 62, "index": 62}}], "key": "SiqkZ9nARqNkdXfcIWbBgsKp5Yo=", "exportNames": ["*"]}}, {"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 63}, "end": {"line": 2, "column": 36, "index": 99}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkParagraph", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 100}, "end": {"line": 3, "column": 50, "index": 150}}], "key": "by1H/SO60Wqkg2Xt0Ee+Y04qqoA=", "exportNames": ["*"]}}, {"name": "./JsiSkTextStyle", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 151}, "end": {"line": 4, "column": 50, "index": 201}}], "key": "Zh5MfxVi9jtqzugox7ZZiGyOVV8=", "exportNames": ["*"]}}, {"name": "./JsiSkPaint", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 5, "column": 0, "index": 202}, "end": {"line": 5, "column": 42, "index": 244}}], "key": "tZL5XO67L1lBKN/ngQFuxeWOGxA=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkParagraphBuilder = void 0;\n  var _types = require(_dependencyMap[0], \"../types\");\n  var _Host = require(_dependencyMap[1], \"./Host\");\n  var _JsiSkParagraph = require(_dependencyMap[2], \"./JsiSkParagraph\");\n  var _JsiSkTextStyle = require(_dependencyMap[3], \"./JsiSkTextStyle\");\n  var _JsiSkPaint = require(_dependencyMap[4], \"./JsiSkPaint\");\n  class JsiSkParagraphBuilder extends _Host.HostObject {\n    constructor(CanvasKit, ref) {\n      super(CanvasKit, ref, \"ParagraphBuilder\");\n    }\n    addPlaceholder(width = 0, height = 0, alignment = _types.PlaceholderAlignment.Baseline, baseline = _types.TextBaseline.Alphabetic, offset = 0) {\n      this.ref.addPlaceholder(width, height, {\n        value: alignment\n      }, {\n        value: baseline\n      }, offset);\n      return this;\n    }\n    addText(text) {\n      this.ref.addText(text);\n      return this;\n    }\n    build() {\n      return new _JsiSkParagraph.JsiSkParagraph(this.CanvasKit, this.ref.build());\n    }\n    reset() {\n      this.ref.reset();\n    }\n    pushStyle(style, foregroundPaint, backgroundPaint) {\n      const textStyle = _JsiSkTextStyle.JsiSkTextStyle.toTextStyle(style);\n      if (foregroundPaint || backgroundPaint) {\n        var _textStyle$color, _textStyle$background;\n        // Due the canvaskit API not exposing textStyle methods,\n        // we set the default paint color to black for the foreground\n        // and transparent for the background\n        const fg = foregroundPaint ? _JsiSkPaint.JsiSkPaint.fromValue(foregroundPaint) : this.makePaint((_textStyle$color = textStyle.color) !== null && _textStyle$color !== void 0 ? _textStyle$color : Float32Array.of(0, 0, 0, 1));\n        const bg = backgroundPaint ? _JsiSkPaint.JsiSkPaint.fromValue(backgroundPaint) : this.makePaint((_textStyle$background = textStyle.backgroundColor) !== null && _textStyle$background !== void 0 ? _textStyle$background : Float32Array.of(0, 0, 0, 0));\n        this.ref.pushPaintStyle(new this.CanvasKit.TextStyle(textStyle), fg, bg);\n      } else {\n        this.ref.pushStyle(new this.CanvasKit.TextStyle(textStyle));\n      }\n      return this;\n    }\n    pop() {\n      this.ref.pop();\n      return this;\n    }\n    dispose() {\n      this.ref.delete();\n    }\n    makePaint(color) {\n      const paint = new this.CanvasKit.Paint();\n      paint.setColor(color);\n      return paint;\n    }\n  }\n  exports.JsiSkParagraphBuilder = JsiSkParagraphBuilder;\n});", "lineCount": 62, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_types"], [6, 12, 1, 0], [6, 15, 1, 0, "require"], [6, 22, 1, 0], [6, 23, 1, 0, "_dependencyMap"], [6, 37, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_Host"], [7, 11, 2, 0], [7, 14, 2, 0, "require"], [7, 21, 2, 0], [7, 22, 2, 0, "_dependencyMap"], [7, 36, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_JsiSkParagraph"], [8, 21, 3, 0], [8, 24, 3, 0, "require"], [8, 31, 3, 0], [8, 32, 3, 0, "_dependencyMap"], [8, 46, 3, 0], [9, 2, 4, 0], [9, 6, 4, 0, "_JsiSkTextStyle"], [9, 21, 4, 0], [9, 24, 4, 0, "require"], [9, 31, 4, 0], [9, 32, 4, 0, "_dependencyMap"], [9, 46, 4, 0], [10, 2, 5, 0], [10, 6, 5, 0, "_JsiSkPaint"], [10, 17, 5, 0], [10, 20, 5, 0, "require"], [10, 27, 5, 0], [10, 28, 5, 0, "_dependencyMap"], [10, 42, 5, 0], [11, 2, 6, 7], [11, 8, 6, 13, "JsiSkParagraphBuilder"], [11, 29, 6, 34], [11, 38, 6, 43, "HostObject"], [11, 54, 6, 53], [11, 55, 6, 54], [12, 4, 7, 2, "constructor"], [12, 15, 7, 13, "constructor"], [12, 16, 7, 14, "CanvasKit"], [12, 25, 7, 23], [12, 27, 7, 25, "ref"], [12, 30, 7, 28], [12, 32, 7, 30], [13, 6, 8, 4], [13, 11, 8, 9], [13, 12, 8, 10, "CanvasKit"], [13, 21, 8, 19], [13, 23, 8, 21, "ref"], [13, 26, 8, 24], [13, 28, 8, 26], [13, 46, 8, 44], [13, 47, 8, 45], [14, 4, 9, 2], [15, 4, 10, 2, "addPlaceholder"], [15, 18, 10, 16, "addPlaceholder"], [15, 19, 10, 17, "width"], [15, 24, 10, 22], [15, 27, 10, 25], [15, 28, 10, 26], [15, 30, 10, 28, "height"], [15, 36, 10, 34], [15, 39, 10, 37], [15, 40, 10, 38], [15, 42, 10, 40, "alignment"], [15, 51, 10, 49], [15, 54, 10, 52, "PlaceholderAlignment"], [15, 81, 10, 72], [15, 82, 10, 73, "Baseline"], [15, 90, 10, 81], [15, 92, 10, 83, "baseline"], [15, 100, 10, 91], [15, 103, 10, 94, "TextBaseline"], [15, 122, 10, 106], [15, 123, 10, 107, "Alphabetic"], [15, 133, 10, 117], [15, 135, 10, 119, "offset"], [15, 141, 10, 125], [15, 144, 10, 128], [15, 145, 10, 129], [15, 147, 10, 131], [16, 6, 11, 4], [16, 10, 11, 8], [16, 11, 11, 9, "ref"], [16, 14, 11, 12], [16, 15, 11, 13, "addPlaceholder"], [16, 29, 11, 27], [16, 30, 11, 28, "width"], [16, 35, 11, 33], [16, 37, 11, 35, "height"], [16, 43, 11, 41], [16, 45, 11, 43], [17, 8, 12, 6, "value"], [17, 13, 12, 11], [17, 15, 12, 13, "alignment"], [18, 6, 13, 4], [18, 7, 13, 5], [18, 9, 13, 7], [19, 8, 14, 6, "value"], [19, 13, 14, 11], [19, 15, 14, 13, "baseline"], [20, 6, 15, 4], [20, 7, 15, 5], [20, 9, 15, 7, "offset"], [20, 15, 15, 13], [20, 16, 15, 14], [21, 6, 16, 4], [21, 13, 16, 11], [21, 17, 16, 15], [22, 4, 17, 2], [23, 4, 18, 2, "addText"], [23, 11, 18, 9, "addText"], [23, 12, 18, 10, "text"], [23, 16, 18, 14], [23, 18, 18, 16], [24, 6, 19, 4], [24, 10, 19, 8], [24, 11, 19, 9, "ref"], [24, 14, 19, 12], [24, 15, 19, 13, "addText"], [24, 22, 19, 20], [24, 23, 19, 21, "text"], [24, 27, 19, 25], [24, 28, 19, 26], [25, 6, 20, 4], [25, 13, 20, 11], [25, 17, 20, 15], [26, 4, 21, 2], [27, 4, 22, 2, "build"], [27, 9, 22, 7, "build"], [27, 10, 22, 7], [27, 12, 22, 10], [28, 6, 23, 4], [28, 13, 23, 11], [28, 17, 23, 15, "JsiSkParagraph"], [28, 47, 23, 29], [28, 48, 23, 30], [28, 52, 23, 34], [28, 53, 23, 35, "CanvasKit"], [28, 62, 23, 44], [28, 64, 23, 46], [28, 68, 23, 50], [28, 69, 23, 51, "ref"], [28, 72, 23, 54], [28, 73, 23, 55, "build"], [28, 78, 23, 60], [28, 79, 23, 61], [28, 80, 23, 62], [28, 81, 23, 63], [29, 4, 24, 2], [30, 4, 25, 2, "reset"], [30, 9, 25, 7, "reset"], [30, 10, 25, 7], [30, 12, 25, 10], [31, 6, 26, 4], [31, 10, 26, 8], [31, 11, 26, 9, "ref"], [31, 14, 26, 12], [31, 15, 26, 13, "reset"], [31, 20, 26, 18], [31, 21, 26, 19], [31, 22, 26, 20], [32, 4, 27, 2], [33, 4, 28, 2, "pushStyle"], [33, 13, 28, 11, "pushStyle"], [33, 14, 28, 12, "style"], [33, 19, 28, 17], [33, 21, 28, 19, "foreground<PERSON><PERSON><PERSON>"], [33, 36, 28, 34], [33, 38, 28, 36, "<PERSON><PERSON><PERSON><PERSON>"], [33, 53, 28, 51], [33, 55, 28, 53], [34, 6, 29, 4], [34, 12, 29, 10, "textStyle"], [34, 21, 29, 19], [34, 24, 29, 22, "JsiSkTextStyle"], [34, 54, 29, 36], [34, 55, 29, 37, "toTextStyle"], [34, 66, 29, 48], [34, 67, 29, 49, "style"], [34, 72, 29, 54], [34, 73, 29, 55], [35, 6, 30, 4], [35, 10, 30, 8, "foreground<PERSON><PERSON><PERSON>"], [35, 25, 30, 23], [35, 29, 30, 27, "<PERSON><PERSON><PERSON><PERSON>"], [35, 44, 30, 42], [35, 46, 30, 44], [36, 8, 31, 6], [36, 12, 31, 10, "_textStyle$color"], [36, 28, 31, 26], [36, 30, 31, 28, "_textStyle$background"], [36, 51, 31, 49], [37, 8, 32, 6], [38, 8, 33, 6], [39, 8, 34, 6], [40, 8, 35, 6], [40, 14, 35, 12, "fg"], [40, 16, 35, 14], [40, 19, 35, 17, "foreground<PERSON><PERSON><PERSON>"], [40, 34, 35, 32], [40, 37, 35, 35, "JsiSkPaint"], [40, 59, 35, 45], [40, 60, 35, 46, "fromValue"], [40, 69, 35, 55], [40, 70, 35, 56, "foreground<PERSON><PERSON><PERSON>"], [40, 85, 35, 71], [40, 86, 35, 72], [40, 89, 35, 75], [40, 93, 35, 79], [40, 94, 35, 80, "<PERSON><PERSON><PERSON><PERSON>"], [40, 103, 35, 89], [40, 104, 35, 90], [40, 105, 35, 91, "_textStyle$color"], [40, 121, 35, 107], [40, 124, 35, 110, "textStyle"], [40, 133, 35, 119], [40, 134, 35, 120, "color"], [40, 139, 35, 125], [40, 145, 35, 131], [40, 149, 35, 135], [40, 153, 35, 139, "_textStyle$color"], [40, 169, 35, 155], [40, 174, 35, 160], [40, 179, 35, 165], [40, 180, 35, 166], [40, 183, 35, 169, "_textStyle$color"], [40, 199, 35, 185], [40, 202, 35, 188, "Float32Array"], [40, 214, 35, 200], [40, 215, 35, 201, "of"], [40, 217, 35, 203], [40, 218, 35, 204], [40, 219, 35, 205], [40, 221, 35, 207], [40, 222, 35, 208], [40, 224, 35, 210], [40, 225, 35, 211], [40, 227, 35, 213], [40, 228, 35, 214], [40, 229, 35, 215], [40, 230, 35, 216], [41, 8, 36, 6], [41, 14, 36, 12, "bg"], [41, 16, 36, 14], [41, 19, 36, 17, "<PERSON><PERSON><PERSON><PERSON>"], [41, 34, 36, 32], [41, 37, 36, 35, "JsiSkPaint"], [41, 59, 36, 45], [41, 60, 36, 46, "fromValue"], [41, 69, 36, 55], [41, 70, 36, 56, "<PERSON><PERSON><PERSON><PERSON>"], [41, 85, 36, 71], [41, 86, 36, 72], [41, 89, 36, 75], [41, 93, 36, 79], [41, 94, 36, 80, "<PERSON><PERSON><PERSON><PERSON>"], [41, 103, 36, 89], [41, 104, 36, 90], [41, 105, 36, 91, "_textStyle$background"], [41, 126, 36, 112], [41, 129, 36, 115, "textStyle"], [41, 138, 36, 124], [41, 139, 36, 125, "backgroundColor"], [41, 154, 36, 140], [41, 160, 36, 146], [41, 164, 36, 150], [41, 168, 36, 154, "_textStyle$background"], [41, 189, 36, 175], [41, 194, 36, 180], [41, 199, 36, 185], [41, 200, 36, 186], [41, 203, 36, 189, "_textStyle$background"], [41, 224, 36, 210], [41, 227, 36, 213, "Float32Array"], [41, 239, 36, 225], [41, 240, 36, 226, "of"], [41, 242, 36, 228], [41, 243, 36, 229], [41, 244, 36, 230], [41, 246, 36, 232], [41, 247, 36, 233], [41, 249, 36, 235], [41, 250, 36, 236], [41, 252, 36, 238], [41, 253, 36, 239], [41, 254, 36, 240], [41, 255, 36, 241], [42, 8, 37, 6], [42, 12, 37, 10], [42, 13, 37, 11, "ref"], [42, 16, 37, 14], [42, 17, 37, 15, "pushPaintStyle"], [42, 31, 37, 29], [42, 32, 37, 30], [42, 36, 37, 34], [42, 40, 37, 38], [42, 41, 37, 39, "CanvasKit"], [42, 50, 37, 48], [42, 51, 37, 49, "TextStyle"], [42, 60, 37, 58], [42, 61, 37, 59, "textStyle"], [42, 70, 37, 68], [42, 71, 37, 69], [42, 73, 37, 71, "fg"], [42, 75, 37, 73], [42, 77, 37, 75, "bg"], [42, 79, 37, 77], [42, 80, 37, 78], [43, 6, 38, 4], [43, 7, 38, 5], [43, 13, 38, 11], [44, 8, 39, 6], [44, 12, 39, 10], [44, 13, 39, 11, "ref"], [44, 16, 39, 14], [44, 17, 39, 15, "pushStyle"], [44, 26, 39, 24], [44, 27, 39, 25], [44, 31, 39, 29], [44, 35, 39, 33], [44, 36, 39, 34, "CanvasKit"], [44, 45, 39, 43], [44, 46, 39, 44, "TextStyle"], [44, 55, 39, 53], [44, 56, 39, 54, "textStyle"], [44, 65, 39, 63], [44, 66, 39, 64], [44, 67, 39, 65], [45, 6, 40, 4], [46, 6, 41, 4], [46, 13, 41, 11], [46, 17, 41, 15], [47, 4, 42, 2], [48, 4, 43, 2, "pop"], [48, 7, 43, 5, "pop"], [48, 8, 43, 5], [48, 10, 43, 8], [49, 6, 44, 4], [49, 10, 44, 8], [49, 11, 44, 9, "ref"], [49, 14, 44, 12], [49, 15, 44, 13, "pop"], [49, 18, 44, 16], [49, 19, 44, 17], [49, 20, 44, 18], [50, 6, 45, 4], [50, 13, 45, 11], [50, 17, 45, 15], [51, 4, 46, 2], [52, 4, 47, 2, "dispose"], [52, 11, 47, 9, "dispose"], [52, 12, 47, 9], [52, 14, 47, 12], [53, 6, 48, 4], [53, 10, 48, 8], [53, 11, 48, 9, "ref"], [53, 14, 48, 12], [53, 15, 48, 13, "delete"], [53, 21, 48, 19], [53, 22, 48, 20], [53, 23, 48, 21], [54, 4, 49, 2], [55, 4, 50, 2, "<PERSON><PERSON><PERSON><PERSON>"], [55, 13, 50, 11, "<PERSON><PERSON><PERSON><PERSON>"], [55, 14, 50, 12, "color"], [55, 19, 50, 17], [55, 21, 50, 19], [56, 6, 51, 4], [56, 12, 51, 10, "paint"], [56, 17, 51, 15], [56, 20, 51, 18], [56, 24, 51, 22], [56, 28, 51, 26], [56, 29, 51, 27, "CanvasKit"], [56, 38, 51, 36], [56, 39, 51, 37, "Paint"], [56, 44, 51, 42], [56, 45, 51, 43], [56, 46, 51, 44], [57, 6, 52, 4, "paint"], [57, 11, 52, 9], [57, 12, 52, 10, "setColor"], [57, 20, 52, 18], [57, 21, 52, 19, "color"], [57, 26, 52, 24], [57, 27, 52, 25], [58, 6, 53, 4], [58, 13, 53, 11, "paint"], [58, 18, 53, 16], [59, 4, 54, 2], [60, 2, 55, 0], [61, 2, 55, 1, "exports"], [61, 9, 55, 1], [61, 10, 55, 1, "JsiSkParagraphBuilder"], [61, 31, 55, 1], [61, 34, 55, 1, "JsiSkParagraphBuilder"], [61, 55, 55, 1], [62, 0, 55, 1], [62, 3]], "functionMap": {"names": ["<global>", "JsiSkParagraphBuilder", "constructor", "addPlaceholder", "addText", "build", "reset", "pushStyle", "pop", "dispose", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAA;OCK;ECC;GDE;EEC;GFO;EGC;GHG;EIC;GJE;EKC;GLE;EMC;GNc;EOC;GPG;EQC;GRE;ESC;GTI;CDC"}}, "type": "js/module"}]}