<html>
<head>
<link rel="stylesheet" href="lib/qunit.css">
</head>
<body>
<div id="qunit"></div>
<div id="qunit-fixture"></div>
<script src="../seedrandom.min.js"></script>
<script src="lib/qunit.js"></script>
<script>
QUnit.module("Options API Test");

QUnit.test("Verify that state method is not normally present",
function(assert) {

var seedrandom = Math.seedrandom;
var dummy = seedrandom('hello');
var unexpected = -1;
var expected = -1;
try {
  unexpected = dummy.state();
} catch(e) {
  expected = 1;
}
assert.equal(unexpected, -1);
assert.equal(expected, 1);
var count = 0;
for (var x in dummy) {
  if (x == 'state') count += 1;
}
assert.equal(count, 0);

});

QUnit.test("Verify that state option works as advertised", function(assert) {

var seedrandom = Math.seedrandom;
var saveable = seedrandom("secret-seed", {state: true});
var ordinary = seedrandom("secret-seed");
for (var j = 0; j < 1e3; ++j) {
  assert.equal(ordinary(), saveable());
}
var virgin = seedrandom("secret-seed");
var saved = saveable.state();
var replica = seedrandom("", {state: saved});
for (var j = 0; j < 1e2; ++j) {
  var r = replica();
  assert.equal(r, saveable());
  assert.equal(r, ordinary());
  assert.ok(r != virgin());
}

});
</script>
</body>
</html>
