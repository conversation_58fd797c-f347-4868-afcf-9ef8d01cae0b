{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        await processImageWithFaceBlur(photo.uri);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // Real face detection and blurring using browser APIs and CDN libraries\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        setProcessingProgress(60);\n\n        // Try multiple face detection approaches\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting face detection on image:', {\n          width: img.width,\n          height: img.height,\n          src: photoUri.substring(0, 50) + '...'\n        });\n\n        // Method 1: Try browser's native Face Detection API\n        try {\n          if ('FaceDetector' in window) {\n            console.log('[EchoCameraWeb] ✅ Browser Face Detection API available, attempting detection...');\n            const faceDetector = new window.FaceDetector({\n              maxDetectedFaces: 10,\n              fastMode: false\n            });\n            const browserDetections = await faceDetector.detect(img);\n            detectedFaces = browserDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.boundingBox.x + detection.boundingBox.width / 2) / img.width,\n                yCenter: (detection.boundingBox.y + detection.boundingBox.height / 2) / img.height,\n                width: detection.boundingBox.width / img.width,\n                height: detection.boundingBox.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] ✅ Browser Face Detection API found ${detectedFaces.length} faces`);\n          } else {\n            console.log('[EchoCameraWeb] ❌ Browser Face Detection API not available in this browser');\n            throw new Error('Browser Face Detection API not available');\n          }\n        } catch (browserError) {\n          console.warn('[EchoCameraWeb] ❌ Browser face detection failed, trying face-api.js from CDN:', browserError);\n\n          // Method 2: Try loading face-api.js from CDN\n          try {\n            // Load face-api.js from CDN if not already loaded\n            if (!window.faceapi) {\n              await new Promise((resolve, reject) => {\n                const script = document.createElement('script');\n                script.src = 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js';\n                script.onload = resolve;\n                script.onerror = reject;\n                document.head.appendChild(script);\n              });\n            }\n            const faceapi = window.faceapi;\n\n            // Load models from CDN\n            await Promise.all([faceapi.nets.tinyFaceDetector.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'), faceapi.nets.faceLandmark68Net.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights')]);\n\n            // Detect faces\n            const faceDetections = await faceapi.detectAllFaces(img, new faceapi.TinyFaceDetectorOptions());\n            detectedFaces = faceDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.box.x + detection.box.width / 2) / img.width,\n                yCenter: (detection.box.y + detection.box.height / 2) / img.height,\n                width: detection.box.width / img.width,\n                height: detection.box.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] ✅ face-api.js found ${detectedFaces.length} faces`);\n          } catch (faceApiError) {\n            console.warn('[EchoCameraWeb] ❌ face-api.js also failed:', faceApiError);\n\n            // Method 3: Fallback - Mock face detection for testing (assumes center face)\n            console.log('[EchoCameraWeb] 🧪 Using fallback mock face detection for testing...');\n            detectedFaces = [{\n              boundingBox: {\n                xCenter: 0.5,\n                // Center of image\n                yCenter: 0.4,\n                // Slightly above center (typical face position)\n                width: 0.3,\n                // 30% of image width\n                height: 0.4 // 40% of image height\n              }\n            }];\n            console.log(`[EchoCameraWeb] 🧪 Mock detection created 1 face at center of image`);\n          }\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // Apply blurring to each detected face\n        if (detectedFaces.length > 0) {\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add some padding around the face\n            const padding = 0.2; // 20% padding\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎨 Blurring face ${index + 1} at (${Math.round(paddedX)}, ${Math.round(paddedY)}) size ${Math.round(paddedWidth)}x${Math.round(paddedHeight)}`);\n\n            // Get the face region image data\n            const faceImageData = ctx.getImageData(paddedX, paddedY, paddedWidth, paddedHeight);\n            const data = faceImageData.data;\n            console.log(`[EchoCameraWeb] 📊 Face region data: ${data.length} bytes, ${paddedWidth}x${paddedHeight} pixels`);\n\n            // Apply pixelation blur effect\n            const pixelSize = Math.max(12, Math.min(paddedWidth, paddedHeight) / 15); // Increased pixel size for more visible effect\n            console.log(`[EchoCameraWeb] 🔲 Using pixel size: ${pixelSize}px for blurring`);\n            for (let y = 0; y < paddedHeight; y += pixelSize) {\n              for (let x = 0; x < paddedWidth; x += pixelSize) {\n                // Get the color of the top-left pixel in this block\n                const pixelIndex = (y * paddedWidth + x) * 4;\n                const r = data[pixelIndex];\n                const g = data[pixelIndex + 1];\n                const b = data[pixelIndex + 2];\n                const a = data[pixelIndex + 3];\n\n                // Apply this color to the entire block\n                for (let dy = 0; dy < pixelSize && y + dy < paddedHeight; dy++) {\n                  for (let dx = 0; dx < pixelSize && x + dx < paddedWidth; dx++) {\n                    const blockPixelIndex = ((y + dy) * paddedWidth + (x + dx)) * 4;\n                    data[blockPixelIndex] = r;\n                    data[blockPixelIndex + 1] = g;\n                    data[blockPixelIndex + 2] = b;\n                    data[blockPixelIndex + 3] = a;\n                  }\n                }\n              }\n            }\n\n            // Put the blurred face region back on the canvas\n            ctx.putImageData(faceImageData, paddedX, paddedY);\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} blurring applied successfully`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] Processing complete:', result);\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 582,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 625,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 624,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 641,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 646,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 650,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 657,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 658,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 679,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 548,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 699,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 705,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 720,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 696,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 695,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 690,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 733,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 740,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 732,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 731,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 726,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1346, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [89, 4, 89, 2], [90, 4, 90, 2], [90, 10, 90, 8, "capturePhoto"], [90, 22, 90, 20], [90, 25, 90, 23], [90, 29, 90, 23, "useCallback"], [90, 47, 90, 34], [90, 49, 90, 35], [90, 61, 90, 47], [91, 6, 91, 4], [92, 6, 92, 4], [92, 12, 92, 10, "isDev"], [92, 17, 92, 15], [92, 20, 92, 18, "process"], [92, 27, 92, 25], [92, 28, 92, 26, "env"], [92, 31, 92, 29], [92, 32, 92, 30, "NODE_ENV"], [92, 40, 92, 38], [92, 45, 92, 43], [92, 58, 92, 56], [92, 62, 92, 60, "__DEV__"], [92, 69, 92, 67], [93, 6, 94, 4], [93, 10, 94, 8], [93, 11, 94, 9, "cameraRef"], [93, 20, 94, 18], [93, 21, 94, 19, "current"], [93, 28, 94, 26], [93, 32, 94, 30], [93, 33, 94, 31, "isDev"], [93, 38, 94, 36], [93, 40, 94, 38], [94, 8, 95, 6, "<PERSON><PERSON>"], [94, 22, 95, 11], [94, 23, 95, 12, "alert"], [94, 28, 95, 17], [94, 29, 95, 18], [94, 36, 95, 25], [94, 38, 95, 27], [94, 56, 95, 45], [94, 57, 95, 46], [95, 8, 96, 6], [96, 6, 97, 4], [97, 6, 98, 4], [97, 10, 98, 8], [98, 8, 99, 6, "setProcessingState"], [98, 26, 99, 24], [98, 27, 99, 25], [98, 38, 99, 36], [98, 39, 99, 37], [99, 8, 100, 6, "setProcessingProgress"], [99, 29, 100, 27], [99, 30, 100, 28], [99, 32, 100, 30], [99, 33, 100, 31], [100, 8, 101, 6], [101, 8, 102, 6], [102, 8, 103, 6], [103, 8, 104, 6], [103, 14, 104, 12], [103, 18, 104, 16, "Promise"], [103, 25, 104, 23], [103, 26, 104, 24, "resolve"], [103, 33, 104, 31], [103, 37, 104, 35, "setTimeout"], [103, 47, 104, 45], [103, 48, 104, 46, "resolve"], [103, 55, 104, 53], [103, 57, 104, 55], [103, 59, 104, 57], [103, 60, 104, 58], [103, 61, 104, 59], [104, 8, 105, 6], [105, 8, 106, 6], [105, 12, 106, 10, "photo"], [105, 17, 106, 15], [106, 8, 108, 6], [106, 12, 108, 10], [107, 10, 109, 8, "photo"], [107, 15, 109, 13], [107, 18, 109, 16], [107, 24, 109, 22, "cameraRef"], [107, 33, 109, 31], [107, 34, 109, 32, "current"], [107, 41, 109, 39], [107, 42, 109, 40, "takePictureAsync"], [107, 58, 109, 56], [107, 59, 109, 57], [108, 12, 110, 10, "quality"], [108, 19, 110, 17], [108, 21, 110, 19], [108, 24, 110, 22], [109, 12, 111, 10, "base64"], [109, 18, 111, 16], [109, 20, 111, 18], [109, 25, 111, 23], [110, 12, 112, 10, "skipProcessing"], [110, 26, 112, 24], [110, 28, 112, 26], [110, 32, 112, 30], [110, 33, 112, 32], [111, 10, 113, 8], [111, 11, 113, 9], [111, 12, 113, 10], [112, 8, 114, 6], [112, 9, 114, 7], [112, 10, 114, 8], [112, 17, 114, 15, "cameraError"], [112, 28, 114, 26], [112, 30, 114, 28], [113, 10, 115, 8, "console"], [113, 17, 115, 15], [113, 18, 115, 16, "log"], [113, 21, 115, 19], [113, 22, 115, 20], [113, 82, 115, 80], [113, 84, 115, 82, "cameraError"], [113, 95, 115, 93], [113, 96, 115, 94], [114, 10, 116, 8], [115, 10, 117, 8], [115, 14, 117, 12, "isDev"], [115, 19, 117, 17], [115, 21, 117, 19], [116, 12, 118, 10, "photo"], [116, 17, 118, 15], [116, 20, 118, 18], [117, 14, 119, 12, "uri"], [117, 17, 119, 15], [117, 19, 119, 17], [118, 12, 120, 10], [118, 13, 120, 11], [119, 10, 121, 8], [119, 11, 121, 9], [119, 17, 121, 15], [120, 12, 122, 10], [120, 18, 122, 16, "cameraError"], [120, 29, 122, 27], [121, 10, 123, 8], [122, 8, 124, 6], [123, 8, 125, 6], [123, 12, 125, 10], [123, 13, 125, 11, "photo"], [123, 18, 125, 16], [123, 20, 125, 18], [124, 10, 126, 8], [124, 16, 126, 14], [124, 20, 126, 18, "Error"], [124, 25, 126, 23], [124, 26, 126, 24], [124, 51, 126, 49], [124, 52, 126, 50], [125, 8, 127, 6], [126, 8, 128, 6, "console"], [126, 15, 128, 13], [126, 16, 128, 14, "log"], [126, 19, 128, 17], [126, 20, 128, 18], [126, 53, 128, 51], [126, 55, 128, 53, "photo"], [126, 60, 128, 58], [126, 61, 128, 59, "uri"], [126, 64, 128, 62], [126, 65, 128, 63], [127, 8, 129, 6, "setCapturedPhoto"], [127, 24, 129, 22], [127, 25, 129, 23, "photo"], [127, 30, 129, 28], [127, 31, 129, 29, "uri"], [127, 34, 129, 32], [127, 35, 129, 33], [128, 8, 130, 6, "setProcessingProgress"], [128, 29, 130, 27], [128, 30, 130, 28], [128, 32, 130, 30], [128, 33, 130, 31], [129, 8, 131, 6], [130, 8, 132, 6], [130, 14, 132, 12, "processImageWithFaceBlur"], [130, 38, 132, 36], [130, 39, 132, 37, "photo"], [130, 44, 132, 42], [130, 45, 132, 43, "uri"], [130, 48, 132, 46], [130, 49, 132, 47], [131, 6, 133, 4], [131, 7, 133, 5], [131, 8, 133, 6], [131, 15, 133, 13, "error"], [131, 20, 133, 18], [131, 22, 133, 20], [132, 8, 134, 6, "console"], [132, 15, 134, 13], [132, 16, 134, 14, "error"], [132, 21, 134, 19], [132, 22, 134, 20], [132, 54, 134, 52], [132, 56, 134, 54, "error"], [132, 61, 134, 59], [132, 62, 134, 60], [133, 8, 135, 6, "setErrorMessage"], [133, 23, 135, 21], [133, 24, 135, 22], [133, 68, 135, 66], [133, 69, 135, 67], [134, 8, 136, 6, "setProcessingState"], [134, 26, 136, 24], [134, 27, 136, 25], [134, 34, 136, 32], [134, 35, 136, 33], [135, 6, 137, 4], [136, 4, 138, 2], [136, 5, 138, 3], [136, 7, 138, 5], [136, 9, 138, 7], [136, 10, 138, 8], [137, 4, 139, 2], [138, 4, 140, 2], [138, 10, 140, 8, "processImageWithFaceBlur"], [138, 34, 140, 32], [138, 37, 140, 35], [138, 43, 140, 42, "photoUri"], [138, 51, 140, 58], [138, 55, 140, 63], [139, 6, 141, 4], [139, 10, 141, 8], [140, 8, 142, 6, "setProcessingState"], [140, 26, 142, 24], [140, 27, 142, 25], [140, 39, 142, 37], [140, 40, 142, 38], [141, 8, 143, 6, "setProcessingProgress"], [141, 29, 143, 27], [141, 30, 143, 28], [141, 32, 143, 30], [141, 33, 143, 31], [143, 8, 145, 6], [144, 8, 146, 6], [144, 14, 146, 12, "canvas"], [144, 20, 146, 18], [144, 23, 146, 21, "document"], [144, 31, 146, 29], [144, 32, 146, 30, "createElement"], [144, 45, 146, 43], [144, 46, 146, 44], [144, 54, 146, 52], [144, 55, 146, 53], [145, 8, 147, 6], [145, 14, 147, 12, "ctx"], [145, 17, 147, 15], [145, 20, 147, 18, "canvas"], [145, 26, 147, 24], [145, 27, 147, 25, "getContext"], [145, 37, 147, 35], [145, 38, 147, 36], [145, 42, 147, 40], [145, 43, 147, 41], [146, 8, 148, 6], [146, 12, 148, 10], [146, 13, 148, 11, "ctx"], [146, 16, 148, 14], [146, 18, 148, 16], [146, 24, 148, 22], [146, 28, 148, 26, "Error"], [146, 33, 148, 31], [146, 34, 148, 32], [146, 64, 148, 62], [146, 65, 148, 63], [148, 8, 150, 6], [149, 8, 151, 6], [149, 14, 151, 12, "img"], [149, 17, 151, 15], [149, 20, 151, 18], [149, 24, 151, 22, "Image"], [149, 29, 151, 27], [149, 30, 151, 28], [149, 31, 151, 29], [150, 8, 152, 6], [150, 14, 152, 12], [150, 18, 152, 16, "Promise"], [150, 25, 152, 23], [150, 26, 152, 24], [150, 27, 152, 25, "resolve"], [150, 34, 152, 32], [150, 36, 152, 34, "reject"], [150, 42, 152, 40], [150, 47, 152, 45], [151, 10, 153, 8, "img"], [151, 13, 153, 11], [151, 14, 153, 12, "onload"], [151, 20, 153, 18], [151, 23, 153, 21, "resolve"], [151, 30, 153, 28], [152, 10, 154, 8, "img"], [152, 13, 154, 11], [152, 14, 154, 12, "onerror"], [152, 21, 154, 19], [152, 24, 154, 22, "reject"], [152, 30, 154, 28], [153, 10, 155, 8, "img"], [153, 13, 155, 11], [153, 14, 155, 12, "src"], [153, 17, 155, 15], [153, 20, 155, 18, "photoUri"], [153, 28, 155, 26], [154, 8, 156, 6], [154, 9, 156, 7], [154, 10, 156, 8], [156, 8, 158, 6], [157, 8, 159, 6, "canvas"], [157, 14, 159, 12], [157, 15, 159, 13, "width"], [157, 20, 159, 18], [157, 23, 159, 21, "img"], [157, 26, 159, 24], [157, 27, 159, 25, "width"], [157, 32, 159, 30], [158, 8, 160, 6, "canvas"], [158, 14, 160, 12], [158, 15, 160, 13, "height"], [158, 21, 160, 19], [158, 24, 160, 22, "img"], [158, 27, 160, 25], [158, 28, 160, 26, "height"], [158, 34, 160, 32], [160, 8, 162, 6], [161, 8, 163, 6, "ctx"], [161, 11, 163, 9], [161, 12, 163, 10, "drawImage"], [161, 21, 163, 19], [161, 22, 163, 20, "img"], [161, 25, 163, 23], [161, 27, 163, 25], [161, 28, 163, 26], [161, 30, 163, 28], [161, 31, 163, 29], [161, 32, 163, 30], [162, 8, 165, 6, "setProcessingProgress"], [162, 29, 165, 27], [162, 30, 165, 28], [162, 32, 165, 30], [162, 33, 165, 31], [164, 8, 167, 6], [165, 8, 168, 6], [165, 12, 168, 10, "detectedFaces"], [165, 25, 168, 23], [165, 28, 168, 26], [165, 30, 168, 28], [166, 8, 170, 6, "console"], [166, 15, 170, 13], [166, 16, 170, 14, "log"], [166, 19, 170, 17], [166, 20, 170, 18], [166, 74, 170, 72], [166, 76, 170, 74], [167, 10, 171, 8, "width"], [167, 15, 171, 13], [167, 17, 171, 15, "img"], [167, 20, 171, 18], [167, 21, 171, 19, "width"], [167, 26, 171, 24], [168, 10, 172, 8, "height"], [168, 16, 172, 14], [168, 18, 172, 16, "img"], [168, 21, 172, 19], [168, 22, 172, 20, "height"], [168, 28, 172, 26], [169, 10, 173, 8, "src"], [169, 13, 173, 11], [169, 15, 173, 13, "photoUri"], [169, 23, 173, 21], [169, 24, 173, 22, "substring"], [169, 33, 173, 31], [169, 34, 173, 32], [169, 35, 173, 33], [169, 37, 173, 35], [169, 39, 173, 37], [169, 40, 173, 38], [169, 43, 173, 41], [170, 8, 174, 6], [170, 9, 174, 7], [170, 10, 174, 8], [172, 8, 176, 6], [173, 8, 177, 6], [173, 12, 177, 10], [174, 10, 178, 8], [174, 14, 178, 12], [174, 28, 178, 26], [174, 32, 178, 30, "window"], [174, 38, 178, 36], [174, 40, 178, 38], [175, 12, 179, 10, "console"], [175, 19, 179, 17], [175, 20, 179, 18, "log"], [175, 23, 179, 21], [175, 24, 179, 22], [175, 105, 179, 103], [175, 106, 179, 104], [176, 12, 180, 10], [176, 18, 180, 16, "faceDetector"], [176, 30, 180, 28], [176, 33, 180, 31], [176, 37, 180, 36, "window"], [176, 43, 180, 42], [176, 44, 180, 51, "FaceDetector"], [176, 56, 180, 63], [176, 57, 180, 64], [177, 14, 181, 12, "maxDetectedFaces"], [177, 30, 181, 28], [177, 32, 181, 30], [177, 34, 181, 32], [178, 14, 182, 12, "fastMode"], [178, 22, 182, 20], [178, 24, 182, 22], [179, 12, 183, 10], [179, 13, 183, 11], [179, 14, 183, 12], [180, 12, 185, 10], [180, 18, 185, 16, "browserDetections"], [180, 35, 185, 33], [180, 38, 185, 36], [180, 44, 185, 42, "faceDetector"], [180, 56, 185, 54], [180, 57, 185, 55, "detect"], [180, 63, 185, 61], [180, 64, 185, 62, "img"], [180, 67, 185, 65], [180, 68, 185, 66], [181, 12, 186, 10, "detectedFaces"], [181, 25, 186, 23], [181, 28, 186, 26, "browserDetections"], [181, 45, 186, 43], [181, 46, 186, 44, "map"], [181, 49, 186, 47], [181, 50, 186, 49, "detection"], [181, 59, 186, 63], [181, 64, 186, 69], [182, 14, 187, 12, "boundingBox"], [182, 25, 187, 23], [182, 27, 187, 25], [183, 16, 188, 14, "xCenter"], [183, 23, 188, 21], [183, 25, 188, 23], [183, 26, 188, 24, "detection"], [183, 35, 188, 33], [183, 36, 188, 34, "boundingBox"], [183, 47, 188, 45], [183, 48, 188, 46, "x"], [183, 49, 188, 47], [183, 52, 188, 50, "detection"], [183, 61, 188, 59], [183, 62, 188, 60, "boundingBox"], [183, 73, 188, 71], [183, 74, 188, 72, "width"], [183, 79, 188, 77], [183, 82, 188, 80], [183, 83, 188, 81], [183, 87, 188, 85, "img"], [183, 90, 188, 88], [183, 91, 188, 89, "width"], [183, 96, 188, 94], [184, 16, 189, 14, "yCenter"], [184, 23, 189, 21], [184, 25, 189, 23], [184, 26, 189, 24, "detection"], [184, 35, 189, 33], [184, 36, 189, 34, "boundingBox"], [184, 47, 189, 45], [184, 48, 189, 46, "y"], [184, 49, 189, 47], [184, 52, 189, 50, "detection"], [184, 61, 189, 59], [184, 62, 189, 60, "boundingBox"], [184, 73, 189, 71], [184, 74, 189, 72, "height"], [184, 80, 189, 78], [184, 83, 189, 81], [184, 84, 189, 82], [184, 88, 189, 86, "img"], [184, 91, 189, 89], [184, 92, 189, 90, "height"], [184, 98, 189, 96], [185, 16, 190, 14, "width"], [185, 21, 190, 19], [185, 23, 190, 21, "detection"], [185, 32, 190, 30], [185, 33, 190, 31, "boundingBox"], [185, 44, 190, 42], [185, 45, 190, 43, "width"], [185, 50, 190, 48], [185, 53, 190, 51, "img"], [185, 56, 190, 54], [185, 57, 190, 55, "width"], [185, 62, 190, 60], [186, 16, 191, 14, "height"], [186, 22, 191, 20], [186, 24, 191, 22, "detection"], [186, 33, 191, 31], [186, 34, 191, 32, "boundingBox"], [186, 45, 191, 43], [186, 46, 191, 44, "height"], [186, 52, 191, 50], [186, 55, 191, 53, "img"], [186, 58, 191, 56], [186, 59, 191, 57, "height"], [187, 14, 192, 12], [188, 12, 193, 10], [188, 13, 193, 11], [188, 14, 193, 12], [188, 15, 193, 13], [189, 12, 194, 10, "console"], [189, 19, 194, 17], [189, 20, 194, 18, "log"], [189, 23, 194, 21], [189, 24, 194, 22], [189, 78, 194, 76, "detectedFaces"], [189, 91, 194, 89], [189, 92, 194, 90, "length"], [189, 98, 194, 96], [189, 106, 194, 104], [189, 107, 194, 105], [190, 10, 195, 8], [190, 11, 195, 9], [190, 17, 195, 15], [191, 12, 196, 10, "console"], [191, 19, 196, 17], [191, 20, 196, 18, "log"], [191, 23, 196, 21], [191, 24, 196, 22], [191, 100, 196, 98], [191, 101, 196, 99], [192, 12, 197, 10], [192, 18, 197, 16], [192, 22, 197, 20, "Error"], [192, 27, 197, 25], [192, 28, 197, 26], [192, 70, 197, 68], [192, 71, 197, 69], [193, 10, 198, 8], [194, 8, 199, 6], [194, 9, 199, 7], [194, 10, 199, 8], [194, 17, 199, 15, "browserError"], [194, 29, 199, 27], [194, 31, 199, 29], [195, 10, 200, 8, "console"], [195, 17, 200, 15], [195, 18, 200, 16, "warn"], [195, 22, 200, 20], [195, 23, 200, 21], [195, 102, 200, 100], [195, 104, 200, 102, "browserError"], [195, 116, 200, 114], [195, 117, 200, 115], [197, 10, 202, 8], [198, 10, 203, 8], [198, 14, 203, 12], [199, 12, 204, 10], [200, 12, 205, 10], [200, 16, 205, 14], [200, 17, 205, 16, "window"], [200, 23, 205, 22], [200, 24, 205, 31, "<PERSON>ap<PERSON>"], [200, 31, 205, 38], [200, 33, 205, 40], [201, 14, 206, 12], [201, 20, 206, 18], [201, 24, 206, 22, "Promise"], [201, 31, 206, 29], [201, 32, 206, 30], [201, 33, 206, 31, "resolve"], [201, 40, 206, 38], [201, 42, 206, 40, "reject"], [201, 48, 206, 46], [201, 53, 206, 51], [202, 16, 207, 14], [202, 22, 207, 20, "script"], [202, 28, 207, 26], [202, 31, 207, 29, "document"], [202, 39, 207, 37], [202, 40, 207, 38, "createElement"], [202, 53, 207, 51], [202, 54, 207, 52], [202, 62, 207, 60], [202, 63, 207, 61], [203, 16, 208, 14, "script"], [203, 22, 208, 20], [203, 23, 208, 21, "src"], [203, 26, 208, 24], [203, 29, 208, 27], [203, 99, 208, 97], [204, 16, 209, 14, "script"], [204, 22, 209, 20], [204, 23, 209, 21, "onload"], [204, 29, 209, 27], [204, 32, 209, 30, "resolve"], [204, 39, 209, 37], [205, 16, 210, 14, "script"], [205, 22, 210, 20], [205, 23, 210, 21, "onerror"], [205, 30, 210, 28], [205, 33, 210, 31, "reject"], [205, 39, 210, 37], [206, 16, 211, 14, "document"], [206, 24, 211, 22], [206, 25, 211, 23, "head"], [206, 29, 211, 27], [206, 30, 211, 28, "append<PERSON><PERSON><PERSON>"], [206, 41, 211, 39], [206, 42, 211, 40, "script"], [206, 48, 211, 46], [206, 49, 211, 47], [207, 14, 212, 12], [207, 15, 212, 13], [207, 16, 212, 14], [208, 12, 213, 10], [209, 12, 215, 10], [209, 18, 215, 16, "<PERSON>ap<PERSON>"], [209, 25, 215, 23], [209, 28, 215, 27, "window"], [209, 34, 215, 33], [209, 35, 215, 42, "<PERSON>ap<PERSON>"], [209, 42, 215, 49], [211, 12, 217, 10], [212, 12, 218, 10], [212, 18, 218, 16, "Promise"], [212, 25, 218, 23], [212, 26, 218, 24, "all"], [212, 29, 218, 27], [212, 30, 218, 28], [212, 31, 219, 12, "<PERSON>ap<PERSON>"], [212, 38, 219, 19], [212, 39, 219, 20, "nets"], [212, 43, 219, 24], [212, 44, 219, 25, "tinyFaceDetector"], [212, 60, 219, 41], [212, 61, 219, 42, "loadFromUri"], [212, 72, 219, 53], [212, 73, 219, 54], [212, 130, 219, 111], [212, 131, 219, 112], [212, 133, 220, 12, "<PERSON>ap<PERSON>"], [212, 140, 220, 19], [212, 141, 220, 20, "nets"], [212, 145, 220, 24], [212, 146, 220, 25, "faceLandmark68Net"], [212, 163, 220, 42], [212, 164, 220, 43, "loadFromUri"], [212, 175, 220, 54], [212, 176, 220, 55], [212, 233, 220, 112], [212, 234, 220, 113], [212, 235, 221, 11], [212, 236, 221, 12], [214, 12, 223, 10], [215, 12, 224, 10], [215, 18, 224, 16, "faceDetections"], [215, 32, 224, 30], [215, 35, 224, 33], [215, 41, 224, 39, "<PERSON>ap<PERSON>"], [215, 48, 224, 46], [215, 49, 224, 47, "detectAllFaces"], [215, 63, 224, 61], [215, 64, 224, 62, "img"], [215, 67, 224, 65], [215, 69, 224, 67], [215, 73, 224, 71, "<PERSON>ap<PERSON>"], [215, 80, 224, 78], [215, 81, 224, 79, "TinyFaceDetectorOptions"], [215, 104, 224, 102], [215, 105, 224, 103], [215, 106, 224, 104], [215, 107, 224, 105], [216, 12, 226, 10, "detectedFaces"], [216, 25, 226, 23], [216, 28, 226, 26, "faceDetections"], [216, 42, 226, 40], [216, 43, 226, 41, "map"], [216, 46, 226, 44], [216, 47, 226, 46, "detection"], [216, 56, 226, 60], [216, 61, 226, 66], [217, 14, 227, 12, "boundingBox"], [217, 25, 227, 23], [217, 27, 227, 25], [218, 16, 228, 14, "xCenter"], [218, 23, 228, 21], [218, 25, 228, 23], [218, 26, 228, 24, "detection"], [218, 35, 228, 33], [218, 36, 228, 34, "box"], [218, 39, 228, 37], [218, 40, 228, 38, "x"], [218, 41, 228, 39], [218, 44, 228, 42, "detection"], [218, 53, 228, 51], [218, 54, 228, 52, "box"], [218, 57, 228, 55], [218, 58, 228, 56, "width"], [218, 63, 228, 61], [218, 66, 228, 64], [218, 67, 228, 65], [218, 71, 228, 69, "img"], [218, 74, 228, 72], [218, 75, 228, 73, "width"], [218, 80, 228, 78], [219, 16, 229, 14, "yCenter"], [219, 23, 229, 21], [219, 25, 229, 23], [219, 26, 229, 24, "detection"], [219, 35, 229, 33], [219, 36, 229, 34, "box"], [219, 39, 229, 37], [219, 40, 229, 38, "y"], [219, 41, 229, 39], [219, 44, 229, 42, "detection"], [219, 53, 229, 51], [219, 54, 229, 52, "box"], [219, 57, 229, 55], [219, 58, 229, 56, "height"], [219, 64, 229, 62], [219, 67, 229, 65], [219, 68, 229, 66], [219, 72, 229, 70, "img"], [219, 75, 229, 73], [219, 76, 229, 74, "height"], [219, 82, 229, 80], [220, 16, 230, 14, "width"], [220, 21, 230, 19], [220, 23, 230, 21, "detection"], [220, 32, 230, 30], [220, 33, 230, 31, "box"], [220, 36, 230, 34], [220, 37, 230, 35, "width"], [220, 42, 230, 40], [220, 45, 230, 43, "img"], [220, 48, 230, 46], [220, 49, 230, 47, "width"], [220, 54, 230, 52], [221, 16, 231, 14, "height"], [221, 22, 231, 20], [221, 24, 231, 22, "detection"], [221, 33, 231, 31], [221, 34, 231, 32, "box"], [221, 37, 231, 35], [221, 38, 231, 36, "height"], [221, 44, 231, 42], [221, 47, 231, 45, "img"], [221, 50, 231, 48], [221, 51, 231, 49, "height"], [222, 14, 232, 12], [223, 12, 233, 10], [223, 13, 233, 11], [223, 14, 233, 12], [223, 15, 233, 13], [224, 12, 235, 10, "console"], [224, 19, 235, 17], [224, 20, 235, 18, "log"], [224, 23, 235, 21], [224, 24, 235, 22], [224, 63, 235, 61, "detectedFaces"], [224, 76, 235, 74], [224, 77, 235, 75, "length"], [224, 83, 235, 81], [224, 91, 235, 89], [224, 92, 235, 90], [225, 10, 236, 8], [225, 11, 236, 9], [225, 12, 236, 10], [225, 19, 236, 17, "faceApiError"], [225, 31, 236, 29], [225, 33, 236, 31], [226, 12, 237, 10, "console"], [226, 19, 237, 17], [226, 20, 237, 18, "warn"], [226, 24, 237, 22], [226, 25, 237, 23], [226, 69, 237, 67], [226, 71, 237, 69, "faceApiError"], [226, 83, 237, 81], [226, 84, 237, 82], [228, 12, 239, 10], [229, 12, 240, 10, "console"], [229, 19, 240, 17], [229, 20, 240, 18, "log"], [229, 23, 240, 21], [229, 24, 240, 22], [229, 94, 240, 92], [229, 95, 240, 93], [230, 12, 241, 10, "detectedFaces"], [230, 25, 241, 23], [230, 28, 241, 26], [230, 29, 241, 27], [231, 14, 242, 12, "boundingBox"], [231, 25, 242, 23], [231, 27, 242, 25], [232, 16, 243, 14, "xCenter"], [232, 23, 243, 21], [232, 25, 243, 23], [232, 28, 243, 26], [233, 16, 243, 29], [234, 16, 244, 14, "yCenter"], [234, 23, 244, 21], [234, 25, 244, 23], [234, 28, 244, 26], [235, 16, 244, 29], [236, 16, 245, 14, "width"], [236, 21, 245, 19], [236, 23, 245, 21], [236, 26, 245, 24], [237, 16, 245, 29], [238, 16, 246, 14, "height"], [238, 22, 246, 20], [238, 24, 246, 22], [238, 27, 246, 25], [238, 28, 246, 29], [239, 14, 247, 12], [240, 12, 248, 10], [240, 13, 248, 11], [240, 14, 248, 12], [241, 12, 249, 10, "console"], [241, 19, 249, 17], [241, 20, 249, 18, "log"], [241, 23, 249, 21], [241, 24, 249, 22], [241, 93, 249, 91], [241, 94, 249, 92], [242, 10, 250, 8], [243, 8, 251, 6], [244, 8, 253, 6, "console"], [244, 15, 253, 13], [244, 16, 253, 14, "log"], [244, 19, 253, 17], [244, 20, 253, 18], [244, 72, 253, 70, "detectedFaces"], [244, 85, 253, 83], [244, 86, 253, 84, "length"], [244, 92, 253, 90], [244, 100, 253, 98], [244, 101, 253, 99], [245, 8, 254, 6], [245, 12, 254, 10, "detectedFaces"], [245, 25, 254, 23], [245, 26, 254, 24, "length"], [245, 32, 254, 30], [245, 35, 254, 33], [245, 36, 254, 34], [245, 38, 254, 36], [246, 10, 255, 8, "console"], [246, 17, 255, 15], [246, 18, 255, 16, "log"], [246, 21, 255, 19], [246, 22, 255, 20], [246, 66, 255, 64], [246, 68, 255, 66, "detectedFaces"], [246, 81, 255, 79], [246, 82, 255, 80, "map"], [246, 85, 255, 83], [246, 86, 255, 84], [246, 87, 255, 85, "face"], [246, 91, 255, 89], [246, 93, 255, 91, "i"], [246, 94, 255, 92], [246, 100, 255, 98], [247, 12, 256, 10, "faceNumber"], [247, 22, 256, 20], [247, 24, 256, 22, "i"], [247, 25, 256, 23], [247, 28, 256, 26], [247, 29, 256, 27], [248, 12, 257, 10, "centerX"], [248, 19, 257, 17], [248, 21, 257, 19, "face"], [248, 25, 257, 23], [248, 26, 257, 24, "boundingBox"], [248, 37, 257, 35], [248, 38, 257, 36, "xCenter"], [248, 45, 257, 43], [249, 12, 258, 10, "centerY"], [249, 19, 258, 17], [249, 21, 258, 19, "face"], [249, 25, 258, 23], [249, 26, 258, 24, "boundingBox"], [249, 37, 258, 35], [249, 38, 258, 36, "yCenter"], [249, 45, 258, 43], [250, 12, 259, 10, "width"], [250, 17, 259, 15], [250, 19, 259, 17, "face"], [250, 23, 259, 21], [250, 24, 259, 22, "boundingBox"], [250, 35, 259, 33], [250, 36, 259, 34, "width"], [250, 41, 259, 39], [251, 12, 260, 10, "height"], [251, 18, 260, 16], [251, 20, 260, 18, "face"], [251, 24, 260, 22], [251, 25, 260, 23, "boundingBox"], [251, 36, 260, 34], [251, 37, 260, 35, "height"], [252, 10, 261, 8], [252, 11, 261, 9], [252, 12, 261, 10], [252, 13, 261, 11], [252, 14, 261, 12], [253, 8, 262, 6], [253, 9, 262, 7], [253, 15, 262, 13], [254, 10, 263, 8, "console"], [254, 17, 263, 15], [254, 18, 263, 16, "log"], [254, 21, 263, 19], [254, 22, 263, 20], [254, 91, 263, 89], [254, 92, 263, 90], [255, 8, 264, 6], [256, 8, 266, 6, "setProcessingProgress"], [256, 29, 266, 27], [256, 30, 266, 28], [256, 32, 266, 30], [256, 33, 266, 31], [258, 8, 268, 6], [259, 8, 269, 6], [259, 12, 269, 10, "detectedFaces"], [259, 25, 269, 23], [259, 26, 269, 24, "length"], [259, 32, 269, 30], [259, 35, 269, 33], [259, 36, 269, 34], [259, 38, 269, 36], [260, 10, 270, 8, "detectedFaces"], [260, 23, 270, 21], [260, 24, 270, 22, "for<PERSON>ach"], [260, 31, 270, 29], [260, 32, 270, 30], [260, 33, 270, 31, "detection"], [260, 42, 270, 40], [260, 44, 270, 42, "index"], [260, 49, 270, 47], [260, 54, 270, 52], [261, 12, 271, 10], [261, 18, 271, 16, "bbox"], [261, 22, 271, 20], [261, 25, 271, 23, "detection"], [261, 34, 271, 32], [261, 35, 271, 33, "boundingBox"], [261, 46, 271, 44], [263, 12, 273, 10], [264, 12, 274, 10], [264, 18, 274, 16, "faceX"], [264, 23, 274, 21], [264, 26, 274, 24, "bbox"], [264, 30, 274, 28], [264, 31, 274, 29, "xCenter"], [264, 38, 274, 36], [264, 41, 274, 39, "img"], [264, 44, 274, 42], [264, 45, 274, 43, "width"], [264, 50, 274, 48], [264, 53, 274, 52, "bbox"], [264, 57, 274, 56], [264, 58, 274, 57, "width"], [264, 63, 274, 62], [264, 66, 274, 65, "img"], [264, 69, 274, 68], [264, 70, 274, 69, "width"], [264, 75, 274, 74], [264, 78, 274, 78], [264, 79, 274, 79], [265, 12, 275, 10], [265, 18, 275, 16, "faceY"], [265, 23, 275, 21], [265, 26, 275, 24, "bbox"], [265, 30, 275, 28], [265, 31, 275, 29, "yCenter"], [265, 38, 275, 36], [265, 41, 275, 39, "img"], [265, 44, 275, 42], [265, 45, 275, 43, "height"], [265, 51, 275, 49], [265, 54, 275, 53, "bbox"], [265, 58, 275, 57], [265, 59, 275, 58, "height"], [265, 65, 275, 64], [265, 68, 275, 67, "img"], [265, 71, 275, 70], [265, 72, 275, 71, "height"], [265, 78, 275, 77], [265, 81, 275, 81], [265, 82, 275, 82], [266, 12, 276, 10], [266, 18, 276, 16, "faceWidth"], [266, 27, 276, 25], [266, 30, 276, 28, "bbox"], [266, 34, 276, 32], [266, 35, 276, 33, "width"], [266, 40, 276, 38], [266, 43, 276, 41, "img"], [266, 46, 276, 44], [266, 47, 276, 45, "width"], [266, 52, 276, 50], [267, 12, 277, 10], [267, 18, 277, 16, "faceHeight"], [267, 28, 277, 26], [267, 31, 277, 29, "bbox"], [267, 35, 277, 33], [267, 36, 277, 34, "height"], [267, 42, 277, 40], [267, 45, 277, 43, "img"], [267, 48, 277, 46], [267, 49, 277, 47, "height"], [267, 55, 277, 53], [269, 12, 279, 10], [270, 12, 280, 10], [270, 18, 280, 16, "padding"], [270, 25, 280, 23], [270, 28, 280, 26], [270, 31, 280, 29], [270, 32, 280, 30], [270, 33, 280, 31], [271, 12, 281, 10], [271, 18, 281, 16, "paddedX"], [271, 25, 281, 23], [271, 28, 281, 26, "Math"], [271, 32, 281, 30], [271, 33, 281, 31, "max"], [271, 36, 281, 34], [271, 37, 281, 35], [271, 38, 281, 36], [271, 40, 281, 38, "faceX"], [271, 45, 281, 43], [271, 48, 281, 46, "faceWidth"], [271, 57, 281, 55], [271, 60, 281, 58, "padding"], [271, 67, 281, 65], [271, 68, 281, 66], [272, 12, 282, 10], [272, 18, 282, 16, "paddedY"], [272, 25, 282, 23], [272, 28, 282, 26, "Math"], [272, 32, 282, 30], [272, 33, 282, 31, "max"], [272, 36, 282, 34], [272, 37, 282, 35], [272, 38, 282, 36], [272, 40, 282, 38, "faceY"], [272, 45, 282, 43], [272, 48, 282, 46, "faceHeight"], [272, 58, 282, 56], [272, 61, 282, 59, "padding"], [272, 68, 282, 66], [272, 69, 282, 67], [273, 12, 283, 10], [273, 18, 283, 16, "<PERSON><PERSON><PERSON><PERSON>"], [273, 29, 283, 27], [273, 32, 283, 30, "Math"], [273, 36, 283, 34], [273, 37, 283, 35, "min"], [273, 40, 283, 38], [273, 41, 283, 39, "img"], [273, 44, 283, 42], [273, 45, 283, 43, "width"], [273, 50, 283, 48], [273, 53, 283, 51, "paddedX"], [273, 60, 283, 58], [273, 62, 283, 60, "faceWidth"], [273, 71, 283, 69], [273, 75, 283, 73], [273, 76, 283, 74], [273, 79, 283, 77], [273, 80, 283, 78], [273, 83, 283, 81, "padding"], [273, 90, 283, 88], [273, 91, 283, 89], [273, 92, 283, 90], [274, 12, 284, 10], [274, 18, 284, 16, "paddedHeight"], [274, 30, 284, 28], [274, 33, 284, 31, "Math"], [274, 37, 284, 35], [274, 38, 284, 36, "min"], [274, 41, 284, 39], [274, 42, 284, 40, "img"], [274, 45, 284, 43], [274, 46, 284, 44, "height"], [274, 52, 284, 50], [274, 55, 284, 53, "paddedY"], [274, 62, 284, 60], [274, 64, 284, 62, "faceHeight"], [274, 74, 284, 72], [274, 78, 284, 76], [274, 79, 284, 77], [274, 82, 284, 80], [274, 83, 284, 81], [274, 86, 284, 84, "padding"], [274, 93, 284, 91], [274, 94, 284, 92], [274, 95, 284, 93], [275, 12, 286, 10, "console"], [275, 19, 286, 17], [275, 20, 286, 18, "log"], [275, 23, 286, 21], [275, 24, 286, 22], [275, 60, 286, 58, "index"], [275, 65, 286, 63], [275, 68, 286, 66], [275, 69, 286, 67], [275, 77, 286, 75, "Math"], [275, 81, 286, 79], [275, 82, 286, 80, "round"], [275, 87, 286, 85], [275, 88, 286, 86, "paddedX"], [275, 95, 286, 93], [275, 96, 286, 94], [275, 101, 286, 99, "Math"], [275, 105, 286, 103], [275, 106, 286, 104, "round"], [275, 111, 286, 109], [275, 112, 286, 110, "paddedY"], [275, 119, 286, 117], [275, 120, 286, 118], [275, 130, 286, 128, "Math"], [275, 134, 286, 132], [275, 135, 286, 133, "round"], [275, 140, 286, 138], [275, 141, 286, 139, "<PERSON><PERSON><PERSON><PERSON>"], [275, 152, 286, 150], [275, 153, 286, 151], [275, 157, 286, 155, "Math"], [275, 161, 286, 159], [275, 162, 286, 160, "round"], [275, 167, 286, 165], [275, 168, 286, 166, "paddedHeight"], [275, 180, 286, 178], [275, 181, 286, 179], [275, 183, 286, 181], [275, 184, 286, 182], [277, 12, 288, 10], [278, 12, 289, 10], [278, 18, 289, 16, "faceImageData"], [278, 31, 289, 29], [278, 34, 289, 32, "ctx"], [278, 37, 289, 35], [278, 38, 289, 36, "getImageData"], [278, 50, 289, 48], [278, 51, 289, 49, "paddedX"], [278, 58, 289, 56], [278, 60, 289, 58, "paddedY"], [278, 67, 289, 65], [278, 69, 289, 67, "<PERSON><PERSON><PERSON><PERSON>"], [278, 80, 289, 78], [278, 82, 289, 80, "paddedHeight"], [278, 94, 289, 92], [278, 95, 289, 93], [279, 12, 290, 10], [279, 18, 290, 16, "data"], [279, 22, 290, 20], [279, 25, 290, 23, "faceImageData"], [279, 38, 290, 36], [279, 39, 290, 37, "data"], [279, 43, 290, 41], [280, 12, 292, 10, "console"], [280, 19, 292, 17], [280, 20, 292, 18, "log"], [280, 23, 292, 21], [280, 24, 292, 22], [280, 64, 292, 62, "data"], [280, 68, 292, 66], [280, 69, 292, 67, "length"], [280, 75, 292, 73], [280, 86, 292, 84, "<PERSON><PERSON><PERSON><PERSON>"], [280, 97, 292, 95], [280, 101, 292, 99, "paddedHeight"], [280, 113, 292, 111], [280, 122, 292, 120], [280, 123, 292, 121], [282, 12, 294, 10], [283, 12, 295, 10], [283, 18, 295, 16, "pixelSize"], [283, 27, 295, 25], [283, 30, 295, 28, "Math"], [283, 34, 295, 32], [283, 35, 295, 33, "max"], [283, 38, 295, 36], [283, 39, 295, 37], [283, 41, 295, 39], [283, 43, 295, 41, "Math"], [283, 47, 295, 45], [283, 48, 295, 46, "min"], [283, 51, 295, 49], [283, 52, 295, 50, "<PERSON><PERSON><PERSON><PERSON>"], [283, 63, 295, 61], [283, 65, 295, 63, "paddedHeight"], [283, 77, 295, 75], [283, 78, 295, 76], [283, 81, 295, 79], [283, 83, 295, 81], [283, 84, 295, 82], [283, 85, 295, 83], [283, 86, 295, 84], [284, 12, 296, 10, "console"], [284, 19, 296, 17], [284, 20, 296, 18, "log"], [284, 23, 296, 21], [284, 24, 296, 22], [284, 64, 296, 62, "pixelSize"], [284, 73, 296, 71], [284, 90, 296, 88], [284, 91, 296, 89], [285, 12, 297, 10], [285, 17, 297, 15], [285, 21, 297, 19, "y"], [285, 22, 297, 20], [285, 25, 297, 23], [285, 26, 297, 24], [285, 28, 297, 26, "y"], [285, 29, 297, 27], [285, 32, 297, 30, "paddedHeight"], [285, 44, 297, 42], [285, 46, 297, 44, "y"], [285, 47, 297, 45], [285, 51, 297, 49, "pixelSize"], [285, 60, 297, 58], [285, 62, 297, 60], [286, 14, 298, 12], [286, 19, 298, 17], [286, 23, 298, 21, "x"], [286, 24, 298, 22], [286, 27, 298, 25], [286, 28, 298, 26], [286, 30, 298, 28, "x"], [286, 31, 298, 29], [286, 34, 298, 32, "<PERSON><PERSON><PERSON><PERSON>"], [286, 45, 298, 43], [286, 47, 298, 45, "x"], [286, 48, 298, 46], [286, 52, 298, 50, "pixelSize"], [286, 61, 298, 59], [286, 63, 298, 61], [287, 16, 299, 14], [288, 16, 300, 14], [288, 22, 300, 20, "pixelIndex"], [288, 32, 300, 30], [288, 35, 300, 33], [288, 36, 300, 34, "y"], [288, 37, 300, 35], [288, 40, 300, 38, "<PERSON><PERSON><PERSON><PERSON>"], [288, 51, 300, 49], [288, 54, 300, 52, "x"], [288, 55, 300, 53], [288, 59, 300, 57], [288, 60, 300, 58], [289, 16, 301, 14], [289, 22, 301, 20, "r"], [289, 23, 301, 21], [289, 26, 301, 24, "data"], [289, 30, 301, 28], [289, 31, 301, 29, "pixelIndex"], [289, 41, 301, 39], [289, 42, 301, 40], [290, 16, 302, 14], [290, 22, 302, 20, "g"], [290, 23, 302, 21], [290, 26, 302, 24, "data"], [290, 30, 302, 28], [290, 31, 302, 29, "pixelIndex"], [290, 41, 302, 39], [290, 44, 302, 42], [290, 45, 302, 43], [290, 46, 302, 44], [291, 16, 303, 14], [291, 22, 303, 20, "b"], [291, 23, 303, 21], [291, 26, 303, 24, "data"], [291, 30, 303, 28], [291, 31, 303, 29, "pixelIndex"], [291, 41, 303, 39], [291, 44, 303, 42], [291, 45, 303, 43], [291, 46, 303, 44], [292, 16, 304, 14], [292, 22, 304, 20, "a"], [292, 23, 304, 21], [292, 26, 304, 24, "data"], [292, 30, 304, 28], [292, 31, 304, 29, "pixelIndex"], [292, 41, 304, 39], [292, 44, 304, 42], [292, 45, 304, 43], [292, 46, 304, 44], [294, 16, 306, 14], [295, 16, 307, 14], [295, 21, 307, 19], [295, 25, 307, 23, "dy"], [295, 27, 307, 25], [295, 30, 307, 28], [295, 31, 307, 29], [295, 33, 307, 31, "dy"], [295, 35, 307, 33], [295, 38, 307, 36, "pixelSize"], [295, 47, 307, 45], [295, 51, 307, 49, "y"], [295, 52, 307, 50], [295, 55, 307, 53, "dy"], [295, 57, 307, 55], [295, 60, 307, 58, "paddedHeight"], [295, 72, 307, 70], [295, 74, 307, 72, "dy"], [295, 76, 307, 74], [295, 78, 307, 76], [295, 80, 307, 78], [296, 18, 308, 16], [296, 23, 308, 21], [296, 27, 308, 25, "dx"], [296, 29, 308, 27], [296, 32, 308, 30], [296, 33, 308, 31], [296, 35, 308, 33, "dx"], [296, 37, 308, 35], [296, 40, 308, 38, "pixelSize"], [296, 49, 308, 47], [296, 53, 308, 51, "x"], [296, 54, 308, 52], [296, 57, 308, 55, "dx"], [296, 59, 308, 57], [296, 62, 308, 60, "<PERSON><PERSON><PERSON><PERSON>"], [296, 73, 308, 71], [296, 75, 308, 73, "dx"], [296, 77, 308, 75], [296, 79, 308, 77], [296, 81, 308, 79], [297, 20, 309, 18], [297, 26, 309, 24, "blockPixelIndex"], [297, 41, 309, 39], [297, 44, 309, 42], [297, 45, 309, 43], [297, 46, 309, 44, "y"], [297, 47, 309, 45], [297, 50, 309, 48, "dy"], [297, 52, 309, 50], [297, 56, 309, 54, "<PERSON><PERSON><PERSON><PERSON>"], [297, 67, 309, 65], [297, 71, 309, 69, "x"], [297, 72, 309, 70], [297, 75, 309, 73, "dx"], [297, 77, 309, 75], [297, 78, 309, 76], [297, 82, 309, 80], [297, 83, 309, 81], [298, 20, 310, 18, "data"], [298, 24, 310, 22], [298, 25, 310, 23, "blockPixelIndex"], [298, 40, 310, 38], [298, 41, 310, 39], [298, 44, 310, 42, "r"], [298, 45, 310, 43], [299, 20, 311, 18, "data"], [299, 24, 311, 22], [299, 25, 311, 23, "blockPixelIndex"], [299, 40, 311, 38], [299, 43, 311, 41], [299, 44, 311, 42], [299, 45, 311, 43], [299, 48, 311, 46, "g"], [299, 49, 311, 47], [300, 20, 312, 18, "data"], [300, 24, 312, 22], [300, 25, 312, 23, "blockPixelIndex"], [300, 40, 312, 38], [300, 43, 312, 41], [300, 44, 312, 42], [300, 45, 312, 43], [300, 48, 312, 46, "b"], [300, 49, 312, 47], [301, 20, 313, 18, "data"], [301, 24, 313, 22], [301, 25, 313, 23, "blockPixelIndex"], [301, 40, 313, 38], [301, 43, 313, 41], [301, 44, 313, 42], [301, 45, 313, 43], [301, 48, 313, 46, "a"], [301, 49, 313, 47], [302, 18, 314, 16], [303, 16, 315, 14], [304, 14, 316, 12], [305, 12, 317, 10], [307, 12, 319, 10], [308, 12, 320, 10, "ctx"], [308, 15, 320, 13], [308, 16, 320, 14, "putImageData"], [308, 28, 320, 26], [308, 29, 320, 27, "faceImageData"], [308, 42, 320, 40], [308, 44, 320, 42, "paddedX"], [308, 51, 320, 49], [308, 53, 320, 51, "paddedY"], [308, 60, 320, 58], [308, 61, 320, 59], [309, 12, 321, 10, "console"], [309, 19, 321, 17], [309, 20, 321, 18, "log"], [309, 23, 321, 21], [309, 24, 321, 22], [309, 50, 321, 48, "index"], [309, 55, 321, 53], [309, 58, 321, 56], [309, 59, 321, 57], [309, 91, 321, 89], [309, 92, 321, 90], [310, 10, 322, 8], [310, 11, 322, 9], [310, 12, 322, 10], [311, 10, 323, 8, "console"], [311, 17, 323, 15], [311, 18, 323, 16, "log"], [311, 21, 323, 19], [311, 22, 323, 20], [311, 48, 323, 46, "detectedFaces"], [311, 61, 323, 59], [311, 62, 323, 60, "length"], [311, 68, 323, 66], [311, 95, 323, 93], [311, 96, 323, 94], [312, 8, 324, 6], [312, 9, 324, 7], [312, 15, 324, 13], [313, 10, 325, 8, "console"], [313, 17, 325, 15], [313, 18, 325, 16, "log"], [313, 21, 325, 19], [313, 22, 325, 20], [313, 91, 325, 89], [313, 92, 325, 90], [314, 8, 326, 6], [315, 8, 328, 6, "setProcessingProgress"], [315, 29, 328, 27], [315, 30, 328, 28], [315, 32, 328, 30], [315, 33, 328, 31], [317, 8, 330, 6], [318, 8, 331, 6, "console"], [318, 15, 331, 13], [318, 16, 331, 14, "log"], [318, 19, 331, 17], [318, 20, 331, 18], [318, 85, 331, 83], [318, 86, 331, 84], [319, 8, 332, 6], [319, 14, 332, 12, "blurredImageBlob"], [319, 30, 332, 28], [319, 33, 332, 31], [319, 39, 332, 37], [319, 43, 332, 41, "Promise"], [319, 50, 332, 48], [319, 51, 332, 56, "resolve"], [319, 58, 332, 63], [319, 62, 332, 68], [320, 10, 333, 8, "canvas"], [320, 16, 333, 14], [320, 17, 333, 15, "toBlob"], [320, 23, 333, 21], [320, 24, 333, 23, "blob"], [320, 28, 333, 27], [320, 32, 333, 32, "resolve"], [320, 39, 333, 39], [320, 40, 333, 40, "blob"], [320, 44, 333, 45], [320, 45, 333, 46], [320, 47, 333, 48], [320, 59, 333, 60], [320, 61, 333, 62], [320, 64, 333, 65], [320, 65, 333, 66], [321, 8, 334, 6], [321, 9, 334, 7], [321, 10, 334, 8], [322, 8, 336, 6], [322, 14, 336, 12, "blurredImageUrl"], [322, 29, 336, 27], [322, 32, 336, 30, "URL"], [322, 35, 336, 33], [322, 36, 336, 34, "createObjectURL"], [322, 51, 336, 49], [322, 52, 336, 50, "blurredImageBlob"], [322, 68, 336, 66], [322, 69, 336, 67], [323, 8, 337, 6, "console"], [323, 15, 337, 13], [323, 16, 337, 14, "log"], [323, 19, 337, 17], [323, 20, 337, 18], [323, 66, 337, 64], [323, 68, 337, 66, "blurredImageUrl"], [323, 83, 337, 81], [323, 84, 337, 82, "substring"], [323, 93, 337, 91], [323, 94, 337, 92], [323, 95, 337, 93], [323, 97, 337, 95], [323, 99, 337, 97], [323, 100, 337, 98], [323, 103, 337, 101], [323, 108, 337, 106], [323, 109, 337, 107], [324, 8, 339, 6, "setProcessingProgress"], [324, 29, 339, 27], [324, 30, 339, 28], [324, 33, 339, 31], [324, 34, 339, 32], [326, 8, 341, 6], [327, 8, 342, 6], [327, 14, 342, 12, "completeProcessing"], [327, 32, 342, 30], [327, 33, 342, 31, "blurredImageUrl"], [327, 48, 342, 46], [327, 49, 342, 47], [328, 6, 344, 4], [328, 7, 344, 5], [328, 8, 344, 6], [328, 15, 344, 13, "error"], [328, 20, 344, 18], [328, 22, 344, 20], [329, 8, 345, 6, "console"], [329, 15, 345, 13], [329, 16, 345, 14, "error"], [329, 21, 345, 19], [329, 22, 345, 20], [329, 57, 345, 55], [329, 59, 345, 57, "error"], [329, 64, 345, 62], [329, 65, 345, 63], [330, 8, 346, 6, "setErrorMessage"], [330, 23, 346, 21], [330, 24, 346, 22], [330, 50, 346, 48], [330, 51, 346, 49], [331, 8, 347, 6, "setProcessingState"], [331, 26, 347, 24], [331, 27, 347, 25], [331, 34, 347, 32], [331, 35, 347, 33], [332, 6, 348, 4], [333, 4, 349, 2], [333, 5, 349, 3], [335, 4, 351, 2], [336, 4, 352, 2], [336, 10, 352, 8, "completeProcessing"], [336, 28, 352, 26], [336, 31, 352, 29], [336, 37, 352, 36, "blurredImageUrl"], [336, 52, 352, 59], [336, 56, 352, 64], [337, 6, 353, 4], [337, 10, 353, 8], [338, 8, 354, 6, "setProcessingState"], [338, 26, 354, 24], [338, 27, 354, 25], [338, 37, 354, 35], [338, 38, 354, 36], [340, 8, 356, 6], [341, 8, 357, 6], [341, 14, 357, 12, "timestamp"], [341, 23, 357, 21], [341, 26, 357, 24, "Date"], [341, 30, 357, 28], [341, 31, 357, 29, "now"], [341, 34, 357, 32], [341, 35, 357, 33], [341, 36, 357, 34], [342, 8, 358, 6], [342, 14, 358, 12, "result"], [342, 20, 358, 18], [342, 23, 358, 21], [343, 10, 359, 8, "imageUrl"], [343, 18, 359, 16], [343, 20, 359, 18, "blurredImageUrl"], [343, 35, 359, 33], [344, 10, 360, 8, "localUri"], [344, 18, 360, 16], [344, 20, 360, 18, "blurredImageUrl"], [344, 35, 360, 33], [345, 10, 361, 8, "challengeCode"], [345, 23, 361, 21], [345, 25, 361, 23, "challengeCode"], [345, 38, 361, 36], [345, 42, 361, 40], [345, 44, 361, 42], [346, 10, 362, 8, "timestamp"], [346, 19, 362, 17], [347, 10, 363, 8, "jobId"], [347, 15, 363, 13], [347, 17, 363, 15], [347, 27, 363, 25, "timestamp"], [347, 36, 363, 34], [347, 38, 363, 36], [348, 10, 364, 8, "status"], [348, 16, 364, 14], [348, 18, 364, 16], [349, 8, 365, 6], [349, 9, 365, 7], [350, 8, 367, 6, "console"], [350, 15, 367, 13], [350, 16, 367, 14, "log"], [350, 19, 367, 17], [350, 20, 367, 18], [350, 58, 367, 56], [350, 60, 367, 58, "result"], [350, 66, 367, 64], [350, 67, 367, 65], [352, 8, 369, 6], [353, 8, 370, 6, "onComplete"], [353, 18, 370, 16], [353, 19, 370, 17, "result"], [353, 25, 370, 23], [353, 26, 370, 24], [354, 6, 372, 4], [354, 7, 372, 5], [354, 8, 372, 6], [354, 15, 372, 13, "error"], [354, 20, 372, 18], [354, 22, 372, 20], [355, 8, 373, 6, "console"], [355, 15, 373, 13], [355, 16, 373, 14, "error"], [355, 21, 373, 19], [355, 22, 373, 20], [355, 57, 373, 55], [355, 59, 373, 57, "error"], [355, 64, 373, 62], [355, 65, 373, 63], [356, 8, 374, 6, "setErrorMessage"], [356, 23, 374, 21], [356, 24, 374, 22], [356, 56, 374, 54], [356, 57, 374, 55], [357, 8, 375, 6, "setProcessingState"], [357, 26, 375, 24], [357, 27, 375, 25], [357, 34, 375, 32], [357, 35, 375, 33], [358, 6, 376, 4], [359, 4, 377, 2], [359, 5, 377, 3], [361, 4, 379, 2], [362, 4, 380, 2], [362, 10, 380, 8, "triggerServerProcessing"], [362, 33, 380, 31], [362, 36, 380, 34], [362, 42, 380, 34, "triggerServerProcessing"], [362, 43, 380, 41, "privateImageUrl"], [362, 58, 380, 64], [362, 60, 380, 66, "timestamp"], [362, 69, 380, 83], [362, 74, 380, 88], [363, 6, 381, 4], [363, 10, 381, 8], [364, 8, 382, 6, "console"], [364, 15, 382, 13], [364, 16, 382, 14, "log"], [364, 19, 382, 17], [364, 20, 382, 18], [364, 74, 382, 72], [364, 76, 382, 74, "privateImageUrl"], [364, 91, 382, 89], [364, 92, 382, 90], [365, 8, 383, 6, "setProcessingState"], [365, 26, 383, 24], [365, 27, 383, 25], [365, 39, 383, 37], [365, 40, 383, 38], [366, 8, 384, 6, "setProcessingProgress"], [366, 29, 384, 27], [366, 30, 384, 28], [366, 32, 384, 30], [366, 33, 384, 31], [367, 8, 386, 6], [367, 14, 386, 12, "requestBody"], [367, 25, 386, 23], [367, 28, 386, 26], [368, 10, 387, 8, "imageUrl"], [368, 18, 387, 16], [368, 20, 387, 18, "privateImageUrl"], [368, 35, 387, 33], [369, 10, 388, 8, "userId"], [369, 16, 388, 14], [370, 10, 389, 8, "requestId"], [370, 19, 389, 17], [371, 10, 390, 8, "timestamp"], [371, 19, 390, 17], [372, 10, 391, 8, "platform"], [372, 18, 391, 16], [372, 20, 391, 18], [373, 8, 392, 6], [373, 9, 392, 7], [374, 8, 394, 6, "console"], [374, 15, 394, 13], [374, 16, 394, 14, "log"], [374, 19, 394, 17], [374, 20, 394, 18], [374, 65, 394, 63], [374, 67, 394, 65, "requestBody"], [374, 78, 394, 76], [374, 79, 394, 77], [376, 8, 396, 6], [377, 8, 397, 6], [377, 14, 397, 12, "response"], [377, 22, 397, 20], [377, 25, 397, 23], [377, 31, 397, 29, "fetch"], [377, 36, 397, 34], [377, 37, 397, 35], [377, 40, 397, 38, "API_BASE_URL"], [377, 52, 397, 50], [377, 72, 397, 70], [377, 74, 397, 72], [378, 10, 398, 8, "method"], [378, 16, 398, 14], [378, 18, 398, 16], [378, 24, 398, 22], [379, 10, 399, 8, "headers"], [379, 17, 399, 15], [379, 19, 399, 17], [380, 12, 400, 10], [380, 26, 400, 24], [380, 28, 400, 26], [380, 46, 400, 44], [381, 12, 401, 10], [381, 27, 401, 25], [381, 29, 401, 27], [381, 39, 401, 37], [381, 45, 401, 43, "getAuthToken"], [381, 57, 401, 55], [381, 58, 401, 56], [381, 59, 401, 57], [382, 10, 402, 8], [382, 11, 402, 9], [383, 10, 403, 8, "body"], [383, 14, 403, 12], [383, 16, 403, 14, "JSON"], [383, 20, 403, 18], [383, 21, 403, 19, "stringify"], [383, 30, 403, 28], [383, 31, 403, 29, "requestBody"], [383, 42, 403, 40], [384, 8, 404, 6], [384, 9, 404, 7], [384, 10, 404, 8], [385, 8, 406, 6], [385, 12, 406, 10], [385, 13, 406, 11, "response"], [385, 21, 406, 19], [385, 22, 406, 20, "ok"], [385, 24, 406, 22], [385, 26, 406, 24], [386, 10, 407, 8], [386, 16, 407, 14, "errorText"], [386, 25, 407, 23], [386, 28, 407, 26], [386, 34, 407, 32, "response"], [386, 42, 407, 40], [386, 43, 407, 41, "text"], [386, 47, 407, 45], [386, 48, 407, 46], [386, 49, 407, 47], [387, 10, 408, 8, "console"], [387, 17, 408, 15], [387, 18, 408, 16, "error"], [387, 23, 408, 21], [387, 24, 408, 22], [387, 68, 408, 66], [387, 70, 408, 68, "response"], [387, 78, 408, 76], [387, 79, 408, 77, "status"], [387, 85, 408, 83], [387, 87, 408, 85, "errorText"], [387, 96, 408, 94], [387, 97, 408, 95], [388, 10, 409, 8], [388, 16, 409, 14], [388, 20, 409, 18, "Error"], [388, 25, 409, 23], [388, 26, 409, 24], [388, 48, 409, 46, "response"], [388, 56, 409, 54], [388, 57, 409, 55, "status"], [388, 63, 409, 61], [388, 67, 409, 65, "response"], [388, 75, 409, 73], [388, 76, 409, 74, "statusText"], [388, 86, 409, 84], [388, 88, 409, 86], [388, 89, 409, 87], [389, 8, 410, 6], [390, 8, 412, 6], [390, 14, 412, 12, "result"], [390, 20, 412, 18], [390, 23, 412, 21], [390, 29, 412, 27, "response"], [390, 37, 412, 35], [390, 38, 412, 36, "json"], [390, 42, 412, 40], [390, 43, 412, 41], [390, 44, 412, 42], [391, 8, 413, 6, "console"], [391, 15, 413, 13], [391, 16, 413, 14, "log"], [391, 19, 413, 17], [391, 20, 413, 18], [391, 68, 413, 66], [391, 70, 413, 68, "result"], [391, 76, 413, 74], [391, 77, 413, 75], [392, 8, 415, 6], [392, 12, 415, 10], [392, 13, 415, 11, "result"], [392, 19, 415, 17], [392, 20, 415, 18, "jobId"], [392, 25, 415, 23], [392, 27, 415, 25], [393, 10, 416, 8], [393, 16, 416, 14], [393, 20, 416, 18, "Error"], [393, 25, 416, 23], [393, 26, 416, 24], [393, 70, 416, 68], [393, 71, 416, 69], [394, 8, 417, 6], [396, 8, 419, 6], [397, 8, 420, 6], [397, 14, 420, 12, "pollForCompletion"], [397, 31, 420, 29], [397, 32, 420, 30, "result"], [397, 38, 420, 36], [397, 39, 420, 37, "jobId"], [397, 44, 420, 42], [397, 46, 420, 44, "timestamp"], [397, 55, 420, 53], [397, 56, 420, 54], [398, 6, 421, 4], [398, 7, 421, 5], [398, 8, 421, 6], [398, 15, 421, 13, "error"], [398, 20, 421, 18], [398, 22, 421, 20], [399, 8, 422, 6, "console"], [399, 15, 422, 13], [399, 16, 422, 14, "error"], [399, 21, 422, 19], [399, 22, 422, 20], [399, 57, 422, 55], [399, 59, 422, 57, "error"], [399, 64, 422, 62], [399, 65, 422, 63], [400, 8, 423, 6, "setErrorMessage"], [400, 23, 423, 21], [400, 24, 423, 22], [400, 52, 423, 50, "error"], [400, 57, 423, 55], [400, 58, 423, 56, "message"], [400, 65, 423, 63], [400, 67, 423, 65], [400, 68, 423, 66], [401, 8, 424, 6, "setProcessingState"], [401, 26, 424, 24], [401, 27, 424, 25], [401, 34, 424, 32], [401, 35, 424, 33], [402, 6, 425, 4], [403, 4, 426, 2], [403, 5, 426, 3], [404, 4, 427, 2], [405, 4, 428, 2], [405, 10, 428, 8, "pollForCompletion"], [405, 27, 428, 25], [405, 30, 428, 28], [405, 36, 428, 28, "pollForCompletion"], [405, 37, 428, 35, "jobId"], [405, 42, 428, 48], [405, 44, 428, 50, "timestamp"], [405, 53, 428, 67], [405, 55, 428, 69, "attempts"], [405, 63, 428, 77], [405, 66, 428, 80], [405, 67, 428, 81], [405, 72, 428, 86], [406, 6, 429, 4], [406, 12, 429, 10, "MAX_ATTEMPTS"], [406, 24, 429, 22], [406, 27, 429, 25], [406, 29, 429, 27], [406, 30, 429, 28], [406, 31, 429, 29], [407, 6, 430, 4], [407, 12, 430, 10, "POLL_INTERVAL"], [407, 25, 430, 23], [407, 28, 430, 26], [407, 32, 430, 30], [407, 33, 430, 31], [407, 34, 430, 32], [409, 6, 432, 4, "console"], [409, 13, 432, 11], [409, 14, 432, 12, "log"], [409, 17, 432, 15], [409, 18, 432, 16], [409, 53, 432, 51, "attempts"], [409, 61, 432, 59], [409, 64, 432, 62], [409, 65, 432, 63], [409, 69, 432, 67, "MAX_ATTEMPTS"], [409, 81, 432, 79], [409, 93, 432, 91, "jobId"], [409, 98, 432, 96], [409, 100, 432, 98], [409, 101, 432, 99], [410, 6, 434, 4], [410, 10, 434, 8, "attempts"], [410, 18, 434, 16], [410, 22, 434, 20, "MAX_ATTEMPTS"], [410, 34, 434, 32], [410, 36, 434, 34], [411, 8, 435, 6, "console"], [411, 15, 435, 13], [411, 16, 435, 14, "error"], [411, 21, 435, 19], [411, 22, 435, 20], [411, 75, 435, 73], [411, 76, 435, 74], [412, 8, 436, 6, "setErrorMessage"], [412, 23, 436, 21], [412, 24, 436, 22], [412, 63, 436, 61], [412, 64, 436, 62], [413, 8, 437, 6, "setProcessingState"], [413, 26, 437, 24], [413, 27, 437, 25], [413, 34, 437, 32], [413, 35, 437, 33], [414, 8, 438, 6], [415, 6, 439, 4], [416, 6, 441, 4], [416, 10, 441, 8], [417, 8, 442, 6], [417, 14, 442, 12, "response"], [417, 22, 442, 20], [417, 25, 442, 23], [417, 31, 442, 29, "fetch"], [417, 36, 442, 34], [417, 37, 442, 35], [417, 40, 442, 38, "API_BASE_URL"], [417, 52, 442, 50], [417, 75, 442, 73, "jobId"], [417, 80, 442, 78], [417, 82, 442, 80], [417, 84, 442, 82], [418, 10, 443, 8, "headers"], [418, 17, 443, 15], [418, 19, 443, 17], [419, 12, 444, 10], [419, 27, 444, 25], [419, 29, 444, 27], [419, 39, 444, 37], [419, 45, 444, 43, "getAuthToken"], [419, 57, 444, 55], [419, 58, 444, 56], [419, 59, 444, 57], [420, 10, 445, 8], [421, 8, 446, 6], [421, 9, 446, 7], [421, 10, 446, 8], [422, 8, 448, 6], [422, 12, 448, 10], [422, 13, 448, 11, "response"], [422, 21, 448, 19], [422, 22, 448, 20, "ok"], [422, 24, 448, 22], [422, 26, 448, 24], [423, 10, 449, 8], [423, 16, 449, 14], [423, 20, 449, 18, "Error"], [423, 25, 449, 23], [423, 26, 449, 24], [423, 34, 449, 32, "response"], [423, 42, 449, 40], [423, 43, 449, 41, "status"], [423, 49, 449, 47], [423, 54, 449, 52, "response"], [423, 62, 449, 60], [423, 63, 449, 61, "statusText"], [423, 73, 449, 71], [423, 75, 449, 73], [423, 76, 449, 74], [424, 8, 450, 6], [425, 8, 452, 6], [425, 14, 452, 12, "status"], [425, 20, 452, 18], [425, 23, 452, 21], [425, 29, 452, 27, "response"], [425, 37, 452, 35], [425, 38, 452, 36, "json"], [425, 42, 452, 40], [425, 43, 452, 41], [425, 44, 452, 42], [426, 8, 453, 6, "console"], [426, 15, 453, 13], [426, 16, 453, 14, "log"], [426, 19, 453, 17], [426, 20, 453, 18], [426, 54, 453, 52], [426, 56, 453, 54, "status"], [426, 62, 453, 60], [426, 63, 453, 61], [427, 8, 455, 6], [427, 12, 455, 10, "status"], [427, 18, 455, 16], [427, 19, 455, 17, "status"], [427, 25, 455, 23], [427, 30, 455, 28], [427, 41, 455, 39], [427, 43, 455, 41], [428, 10, 456, 8, "console"], [428, 17, 456, 15], [428, 18, 456, 16, "log"], [428, 21, 456, 19], [428, 22, 456, 20], [428, 73, 456, 71], [428, 74, 456, 72], [429, 10, 457, 8, "setProcessingProgress"], [429, 31, 457, 29], [429, 32, 457, 30], [429, 35, 457, 33], [429, 36, 457, 34], [430, 10, 458, 8, "setProcessingState"], [430, 28, 458, 26], [430, 29, 458, 27], [430, 40, 458, 38], [430, 41, 458, 39], [431, 10, 459, 8], [432, 10, 460, 8], [432, 16, 460, 14, "result"], [432, 22, 460, 20], [432, 25, 460, 23], [433, 12, 461, 10, "imageUrl"], [433, 20, 461, 18], [433, 22, 461, 20, "status"], [433, 28, 461, 26], [433, 29, 461, 27, "publicUrl"], [433, 38, 461, 36], [434, 12, 461, 38], [435, 12, 462, 10, "localUri"], [435, 20, 462, 18], [435, 22, 462, 20, "capturedPhoto"], [435, 35, 462, 33], [435, 39, 462, 37, "status"], [435, 45, 462, 43], [435, 46, 462, 44, "publicUrl"], [435, 55, 462, 53], [436, 12, 462, 55], [437, 12, 463, 10, "challengeCode"], [437, 25, 463, 23], [437, 27, 463, 25, "challengeCode"], [437, 40, 463, 38], [437, 44, 463, 42], [437, 46, 463, 44], [438, 12, 464, 10, "timestamp"], [438, 21, 464, 19], [439, 12, 465, 10, "processingStatus"], [439, 28, 465, 26], [439, 30, 465, 28], [440, 10, 466, 8], [440, 11, 466, 9], [441, 10, 467, 8, "console"], [441, 17, 467, 15], [441, 18, 467, 16, "log"], [441, 21, 467, 19], [441, 22, 467, 20], [441, 57, 467, 55], [441, 59, 467, 57, "result"], [441, 65, 467, 63], [441, 66, 467, 64], [442, 10, 468, 8, "onComplete"], [442, 20, 468, 18], [442, 21, 468, 19, "result"], [442, 27, 468, 25], [442, 28, 468, 26], [443, 10, 469, 8], [444, 8, 470, 6], [444, 9, 470, 7], [444, 15, 470, 13], [444, 19, 470, 17, "status"], [444, 25, 470, 23], [444, 26, 470, 24, "status"], [444, 32, 470, 30], [444, 37, 470, 35], [444, 45, 470, 43], [444, 47, 470, 45], [445, 10, 471, 8, "console"], [445, 17, 471, 15], [445, 18, 471, 16, "error"], [445, 23, 471, 21], [445, 24, 471, 22], [445, 60, 471, 58], [445, 62, 471, 60, "status"], [445, 68, 471, 66], [445, 69, 471, 67, "error"], [445, 74, 471, 72], [445, 75, 471, 73], [446, 10, 472, 8], [446, 16, 472, 14], [446, 20, 472, 18, "Error"], [446, 25, 472, 23], [446, 26, 472, 24, "status"], [446, 32, 472, 30], [446, 33, 472, 31, "error"], [446, 38, 472, 36], [446, 42, 472, 40], [446, 61, 472, 59], [446, 62, 472, 60], [447, 8, 473, 6], [447, 9, 473, 7], [447, 15, 473, 13], [448, 10, 474, 8], [449, 10, 475, 8], [449, 16, 475, 14, "progressValue"], [449, 29, 475, 27], [449, 32, 475, 30], [449, 34, 475, 32], [449, 37, 475, 36, "attempts"], [449, 45, 475, 44], [449, 48, 475, 47, "MAX_ATTEMPTS"], [449, 60, 475, 59], [449, 63, 475, 63], [449, 65, 475, 65], [450, 10, 476, 8, "console"], [450, 17, 476, 15], [450, 18, 476, 16, "log"], [450, 21, 476, 19], [450, 22, 476, 20], [450, 71, 476, 69, "progressValue"], [450, 84, 476, 82], [450, 87, 476, 85], [450, 88, 476, 86], [451, 10, 477, 8, "setProcessingProgress"], [451, 31, 477, 29], [451, 32, 477, 30, "progressValue"], [451, 45, 477, 43], [451, 46, 477, 44], [452, 10, 479, 8, "setTimeout"], [452, 20, 479, 18], [452, 21, 479, 19], [452, 27, 479, 25], [453, 12, 480, 10, "pollForCompletion"], [453, 29, 480, 27], [453, 30, 480, 28, "jobId"], [453, 35, 480, 33], [453, 37, 480, 35, "timestamp"], [453, 46, 480, 44], [453, 48, 480, 46, "attempts"], [453, 56, 480, 54], [453, 59, 480, 57], [453, 60, 480, 58], [453, 61, 480, 59], [454, 10, 481, 8], [454, 11, 481, 9], [454, 13, 481, 11, "POLL_INTERVAL"], [454, 26, 481, 24], [454, 27, 481, 25], [455, 8, 482, 6], [456, 6, 483, 4], [456, 7, 483, 5], [456, 8, 483, 6], [456, 15, 483, 13, "error"], [456, 20, 483, 18], [456, 22, 483, 20], [457, 8, 484, 6, "console"], [457, 15, 484, 13], [457, 16, 484, 14, "error"], [457, 21, 484, 19], [457, 22, 484, 20], [457, 54, 484, 52], [457, 56, 484, 54, "error"], [457, 61, 484, 59], [457, 62, 484, 60], [458, 8, 485, 6, "setErrorMessage"], [458, 23, 485, 21], [458, 24, 485, 22], [458, 62, 485, 60, "error"], [458, 67, 485, 65], [458, 68, 485, 66, "message"], [458, 75, 485, 73], [458, 77, 485, 75], [458, 78, 485, 76], [459, 8, 486, 6, "setProcessingState"], [459, 26, 486, 24], [459, 27, 486, 25], [459, 34, 486, 32], [459, 35, 486, 33], [460, 6, 487, 4], [461, 4, 488, 2], [461, 5, 488, 3], [462, 4, 489, 2], [463, 4, 490, 2], [463, 10, 490, 8, "getAuthToken"], [463, 22, 490, 20], [463, 25, 490, 23], [463, 31, 490, 23, "getAuthToken"], [463, 32, 490, 23], [463, 37, 490, 52], [464, 6, 491, 4], [465, 6, 492, 4], [466, 6, 493, 4], [466, 13, 493, 11], [466, 30, 493, 28], [467, 4, 494, 2], [467, 5, 494, 3], [469, 4, 496, 2], [470, 4, 497, 2], [470, 10, 497, 8, "retryCapture"], [470, 22, 497, 20], [470, 25, 497, 23], [470, 29, 497, 23, "useCallback"], [470, 47, 497, 34], [470, 49, 497, 35], [470, 55, 497, 41], [471, 6, 498, 4, "console"], [471, 13, 498, 11], [471, 14, 498, 12, "log"], [471, 17, 498, 15], [471, 18, 498, 16], [471, 55, 498, 53], [471, 56, 498, 54], [472, 6, 499, 4, "setProcessingState"], [472, 24, 499, 22], [472, 25, 499, 23], [472, 31, 499, 29], [472, 32, 499, 30], [473, 6, 500, 4, "setErrorMessage"], [473, 21, 500, 19], [473, 22, 500, 20], [473, 24, 500, 22], [473, 25, 500, 23], [474, 6, 501, 4, "setCapturedPhoto"], [474, 22, 501, 20], [474, 23, 501, 21], [474, 25, 501, 23], [474, 26, 501, 24], [475, 6, 502, 4, "setProcessingProgress"], [475, 27, 502, 25], [475, 28, 502, 26], [475, 29, 502, 27], [475, 30, 502, 28], [476, 4, 503, 2], [476, 5, 503, 3], [476, 7, 503, 5], [476, 9, 503, 7], [476, 10, 503, 8], [477, 4, 504, 2], [478, 4, 505, 2], [478, 8, 505, 2, "useEffect"], [478, 24, 505, 11], [478, 26, 505, 12], [478, 32, 505, 18], [479, 6, 506, 4, "console"], [479, 13, 506, 11], [479, 14, 506, 12, "log"], [479, 17, 506, 15], [479, 18, 506, 16], [479, 53, 506, 51], [479, 55, 506, 53, "permission"], [479, 65, 506, 63], [479, 66, 506, 64], [480, 6, 507, 4], [480, 10, 507, 8, "permission"], [480, 20, 507, 18], [480, 22, 507, 20], [481, 8, 508, 6, "console"], [481, 15, 508, 13], [481, 16, 508, 14, "log"], [481, 19, 508, 17], [481, 20, 508, 18], [481, 57, 508, 55], [481, 59, 508, 57, "permission"], [481, 69, 508, 67], [481, 70, 508, 68, "granted"], [481, 77, 508, 75], [481, 78, 508, 76], [482, 6, 509, 4], [483, 4, 510, 2], [483, 5, 510, 3], [483, 7, 510, 5], [483, 8, 510, 6, "permission"], [483, 18, 510, 16], [483, 19, 510, 17], [483, 20, 510, 18], [484, 4, 511, 2], [485, 4, 512, 2], [485, 8, 512, 6], [485, 9, 512, 7, "permission"], [485, 19, 512, 17], [485, 21, 512, 19], [486, 6, 513, 4, "console"], [486, 13, 513, 11], [486, 14, 513, 12, "log"], [486, 17, 513, 15], [486, 18, 513, 16], [486, 67, 513, 65], [486, 68, 513, 66], [487, 6, 514, 4], [487, 26, 515, 6], [487, 30, 515, 6, "_jsxDevRuntime"], [487, 44, 515, 6], [487, 45, 515, 6, "jsxDEV"], [487, 51, 515, 6], [487, 53, 515, 7, "_View"], [487, 58, 515, 7], [487, 59, 515, 7, "default"], [487, 66, 515, 11], [488, 8, 515, 12, "style"], [488, 13, 515, 17], [488, 15, 515, 19, "styles"], [488, 21, 515, 25], [488, 22, 515, 26, "container"], [488, 31, 515, 36], [489, 8, 515, 36, "children"], [489, 16, 515, 36], [489, 32, 516, 8], [489, 36, 516, 8, "_jsxDevRuntime"], [489, 50, 516, 8], [489, 51, 516, 8, "jsxDEV"], [489, 57, 516, 8], [489, 59, 516, 9, "_ActivityIndicator"], [489, 77, 516, 9], [489, 78, 516, 9, "default"], [489, 85, 516, 26], [490, 10, 516, 27, "size"], [490, 14, 516, 31], [490, 16, 516, 32], [490, 23, 516, 39], [491, 10, 516, 40, "color"], [491, 15, 516, 45], [491, 17, 516, 46], [492, 8, 516, 55], [493, 10, 516, 55, "fileName"], [493, 18, 516, 55], [493, 20, 516, 55, "_jsxFileName"], [493, 32, 516, 55], [494, 10, 516, 55, "lineNumber"], [494, 20, 516, 55], [495, 10, 516, 55, "columnNumber"], [495, 22, 516, 55], [496, 8, 516, 55], [496, 15, 516, 57], [496, 16, 516, 58], [496, 31, 517, 8], [496, 35, 517, 8, "_jsxDevRuntime"], [496, 49, 517, 8], [496, 50, 517, 8, "jsxDEV"], [496, 56, 517, 8], [496, 58, 517, 9, "_Text"], [496, 63, 517, 9], [496, 64, 517, 9, "default"], [496, 71, 517, 13], [497, 10, 517, 14, "style"], [497, 15, 517, 19], [497, 17, 517, 21, "styles"], [497, 23, 517, 27], [497, 24, 517, 28, "loadingText"], [497, 35, 517, 40], [498, 10, 517, 40, "children"], [498, 18, 517, 40], [498, 20, 517, 41], [499, 8, 517, 58], [500, 10, 517, 58, "fileName"], [500, 18, 517, 58], [500, 20, 517, 58, "_jsxFileName"], [500, 32, 517, 58], [501, 10, 517, 58, "lineNumber"], [501, 20, 517, 58], [502, 10, 517, 58, "columnNumber"], [502, 22, 517, 58], [503, 8, 517, 58], [503, 15, 517, 64], [503, 16, 517, 65], [504, 6, 517, 65], [505, 8, 517, 65, "fileName"], [505, 16, 517, 65], [505, 18, 517, 65, "_jsxFileName"], [505, 30, 517, 65], [506, 8, 517, 65, "lineNumber"], [506, 18, 517, 65], [507, 8, 517, 65, "columnNumber"], [507, 20, 517, 65], [508, 6, 517, 65], [508, 13, 518, 12], [508, 14, 518, 13], [509, 4, 520, 2], [510, 4, 521, 2], [510, 8, 521, 6], [510, 9, 521, 7, "permission"], [510, 19, 521, 17], [510, 20, 521, 18, "granted"], [510, 27, 521, 25], [510, 29, 521, 27], [511, 6, 522, 4, "console"], [511, 13, 522, 11], [511, 14, 522, 12, "log"], [511, 17, 522, 15], [511, 18, 522, 16], [511, 93, 522, 91], [511, 94, 522, 92], [512, 6, 523, 4], [512, 26, 524, 6], [512, 30, 524, 6, "_jsxDevRuntime"], [512, 44, 524, 6], [512, 45, 524, 6, "jsxDEV"], [512, 51, 524, 6], [512, 53, 524, 7, "_View"], [512, 58, 524, 7], [512, 59, 524, 7, "default"], [512, 66, 524, 11], [513, 8, 524, 12, "style"], [513, 13, 524, 17], [513, 15, 524, 19, "styles"], [513, 21, 524, 25], [513, 22, 524, 26, "container"], [513, 31, 524, 36], [514, 8, 524, 36, "children"], [514, 16, 524, 36], [514, 31, 525, 8], [514, 35, 525, 8, "_jsxDevRuntime"], [514, 49, 525, 8], [514, 50, 525, 8, "jsxDEV"], [514, 56, 525, 8], [514, 58, 525, 9, "_View"], [514, 63, 525, 9], [514, 64, 525, 9, "default"], [514, 71, 525, 13], [515, 10, 525, 14, "style"], [515, 15, 525, 19], [515, 17, 525, 21, "styles"], [515, 23, 525, 27], [515, 24, 525, 28, "permissionContent"], [515, 41, 525, 46], [516, 10, 525, 46, "children"], [516, 18, 525, 46], [516, 34, 526, 10], [516, 38, 526, 10, "_jsxDevRuntime"], [516, 52, 526, 10], [516, 53, 526, 10, "jsxDEV"], [516, 59, 526, 10], [516, 61, 526, 11, "_lucideReactNative"], [516, 79, 526, 11], [516, 80, 526, 11, "Camera"], [516, 86, 526, 21], [517, 12, 526, 22, "size"], [517, 16, 526, 26], [517, 18, 526, 28], [517, 20, 526, 31], [518, 12, 526, 32, "color"], [518, 17, 526, 37], [518, 19, 526, 38], [519, 10, 526, 47], [520, 12, 526, 47, "fileName"], [520, 20, 526, 47], [520, 22, 526, 47, "_jsxFileName"], [520, 34, 526, 47], [521, 12, 526, 47, "lineNumber"], [521, 22, 526, 47], [522, 12, 526, 47, "columnNumber"], [522, 24, 526, 47], [523, 10, 526, 47], [523, 17, 526, 49], [523, 18, 526, 50], [523, 33, 527, 10], [523, 37, 527, 10, "_jsxDevRuntime"], [523, 51, 527, 10], [523, 52, 527, 10, "jsxDEV"], [523, 58, 527, 10], [523, 60, 527, 11, "_Text"], [523, 65, 527, 11], [523, 66, 527, 11, "default"], [523, 73, 527, 15], [524, 12, 527, 16, "style"], [524, 17, 527, 21], [524, 19, 527, 23, "styles"], [524, 25, 527, 29], [524, 26, 527, 30, "permissionTitle"], [524, 41, 527, 46], [525, 12, 527, 46, "children"], [525, 20, 527, 46], [525, 22, 527, 47], [526, 10, 527, 73], [527, 12, 527, 73, "fileName"], [527, 20, 527, 73], [527, 22, 527, 73, "_jsxFileName"], [527, 34, 527, 73], [528, 12, 527, 73, "lineNumber"], [528, 22, 527, 73], [529, 12, 527, 73, "columnNumber"], [529, 24, 527, 73], [530, 10, 527, 73], [530, 17, 527, 79], [530, 18, 527, 80], [530, 33, 528, 10], [530, 37, 528, 10, "_jsxDevRuntime"], [530, 51, 528, 10], [530, 52, 528, 10, "jsxDEV"], [530, 58, 528, 10], [530, 60, 528, 11, "_Text"], [530, 65, 528, 11], [530, 66, 528, 11, "default"], [530, 73, 528, 15], [531, 12, 528, 16, "style"], [531, 17, 528, 21], [531, 19, 528, 23, "styles"], [531, 25, 528, 29], [531, 26, 528, 30, "permissionDescription"], [531, 47, 528, 52], [532, 12, 528, 52, "children"], [532, 20, 528, 52], [532, 22, 528, 53], [533, 10, 531, 10], [534, 12, 531, 10, "fileName"], [534, 20, 531, 10], [534, 22, 531, 10, "_jsxFileName"], [534, 34, 531, 10], [535, 12, 531, 10, "lineNumber"], [535, 22, 531, 10], [536, 12, 531, 10, "columnNumber"], [536, 24, 531, 10], [537, 10, 531, 10], [537, 17, 531, 16], [537, 18, 531, 17], [537, 33, 532, 10], [537, 37, 532, 10, "_jsxDevRuntime"], [537, 51, 532, 10], [537, 52, 532, 10, "jsxDEV"], [537, 58, 532, 10], [537, 60, 532, 11, "_TouchableOpacity"], [537, 77, 532, 11], [537, 78, 532, 11, "default"], [537, 85, 532, 27], [538, 12, 532, 28, "onPress"], [538, 19, 532, 35], [538, 21, 532, 37, "requestPermission"], [538, 38, 532, 55], [539, 12, 532, 56, "style"], [539, 17, 532, 61], [539, 19, 532, 63, "styles"], [539, 25, 532, 69], [539, 26, 532, 70, "primaryButton"], [539, 39, 532, 84], [540, 12, 532, 84, "children"], [540, 20, 532, 84], [540, 35, 533, 12], [540, 39, 533, 12, "_jsxDevRuntime"], [540, 53, 533, 12], [540, 54, 533, 12, "jsxDEV"], [540, 60, 533, 12], [540, 62, 533, 13, "_Text"], [540, 67, 533, 13], [540, 68, 533, 13, "default"], [540, 75, 533, 17], [541, 14, 533, 18, "style"], [541, 19, 533, 23], [541, 21, 533, 25, "styles"], [541, 27, 533, 31], [541, 28, 533, 32, "primaryButtonText"], [541, 45, 533, 50], [542, 14, 533, 50, "children"], [542, 22, 533, 50], [542, 24, 533, 51], [543, 12, 533, 67], [544, 14, 533, 67, "fileName"], [544, 22, 533, 67], [544, 24, 533, 67, "_jsxFileName"], [544, 36, 533, 67], [545, 14, 533, 67, "lineNumber"], [545, 24, 533, 67], [546, 14, 533, 67, "columnNumber"], [546, 26, 533, 67], [547, 12, 533, 67], [547, 19, 533, 73], [548, 10, 533, 74], [549, 12, 533, 74, "fileName"], [549, 20, 533, 74], [549, 22, 533, 74, "_jsxFileName"], [549, 34, 533, 74], [550, 12, 533, 74, "lineNumber"], [550, 22, 533, 74], [551, 12, 533, 74, "columnNumber"], [551, 24, 533, 74], [552, 10, 533, 74], [552, 17, 534, 28], [552, 18, 534, 29], [552, 33, 535, 10], [552, 37, 535, 10, "_jsxDevRuntime"], [552, 51, 535, 10], [552, 52, 535, 10, "jsxDEV"], [552, 58, 535, 10], [552, 60, 535, 11, "_TouchableOpacity"], [552, 77, 535, 11], [552, 78, 535, 11, "default"], [552, 85, 535, 27], [553, 12, 535, 28, "onPress"], [553, 19, 535, 35], [553, 21, 535, 37, "onCancel"], [553, 29, 535, 46], [554, 12, 535, 47, "style"], [554, 17, 535, 52], [554, 19, 535, 54, "styles"], [554, 25, 535, 60], [554, 26, 535, 61, "secondaryButton"], [554, 41, 535, 77], [555, 12, 535, 77, "children"], [555, 20, 535, 77], [555, 35, 536, 12], [555, 39, 536, 12, "_jsxDevRuntime"], [555, 53, 536, 12], [555, 54, 536, 12, "jsxDEV"], [555, 60, 536, 12], [555, 62, 536, 13, "_Text"], [555, 67, 536, 13], [555, 68, 536, 13, "default"], [555, 75, 536, 17], [556, 14, 536, 18, "style"], [556, 19, 536, 23], [556, 21, 536, 25, "styles"], [556, 27, 536, 31], [556, 28, 536, 32, "secondaryButtonText"], [556, 47, 536, 52], [557, 14, 536, 52, "children"], [557, 22, 536, 52], [557, 24, 536, 53], [558, 12, 536, 59], [559, 14, 536, 59, "fileName"], [559, 22, 536, 59], [559, 24, 536, 59, "_jsxFileName"], [559, 36, 536, 59], [560, 14, 536, 59, "lineNumber"], [560, 24, 536, 59], [561, 14, 536, 59, "columnNumber"], [561, 26, 536, 59], [562, 12, 536, 59], [562, 19, 536, 65], [563, 10, 536, 66], [564, 12, 536, 66, "fileName"], [564, 20, 536, 66], [564, 22, 536, 66, "_jsxFileName"], [564, 34, 536, 66], [565, 12, 536, 66, "lineNumber"], [565, 22, 536, 66], [566, 12, 536, 66, "columnNumber"], [566, 24, 536, 66], [567, 10, 536, 66], [567, 17, 537, 28], [567, 18, 537, 29], [568, 8, 537, 29], [569, 10, 537, 29, "fileName"], [569, 18, 537, 29], [569, 20, 537, 29, "_jsxFileName"], [569, 32, 537, 29], [570, 10, 537, 29, "lineNumber"], [570, 20, 537, 29], [571, 10, 537, 29, "columnNumber"], [571, 22, 537, 29], [572, 8, 537, 29], [572, 15, 538, 14], [573, 6, 538, 15], [574, 8, 538, 15, "fileName"], [574, 16, 538, 15], [574, 18, 538, 15, "_jsxFileName"], [574, 30, 538, 15], [575, 8, 538, 15, "lineNumber"], [575, 18, 538, 15], [576, 8, 538, 15, "columnNumber"], [576, 20, 538, 15], [577, 6, 538, 15], [577, 13, 539, 12], [577, 14, 539, 13], [578, 4, 541, 2], [579, 4, 542, 2], [580, 4, 543, 2, "console"], [580, 11, 543, 9], [580, 12, 543, 10, "log"], [580, 15, 543, 13], [580, 16, 543, 14], [580, 55, 543, 53], [580, 56, 543, 54], [581, 4, 545, 2], [581, 24, 546, 4], [581, 28, 546, 4, "_jsxDevRuntime"], [581, 42, 546, 4], [581, 43, 546, 4, "jsxDEV"], [581, 49, 546, 4], [581, 51, 546, 5, "_View"], [581, 56, 546, 5], [581, 57, 546, 5, "default"], [581, 64, 546, 9], [582, 6, 546, 10, "style"], [582, 11, 546, 15], [582, 13, 546, 17, "styles"], [582, 19, 546, 23], [582, 20, 546, 24, "container"], [582, 29, 546, 34], [583, 6, 546, 34, "children"], [583, 14, 546, 34], [583, 30, 548, 6], [583, 34, 548, 6, "_jsxDevRuntime"], [583, 48, 548, 6], [583, 49, 548, 6, "jsxDEV"], [583, 55, 548, 6], [583, 57, 548, 7, "_View"], [583, 62, 548, 7], [583, 63, 548, 7, "default"], [583, 70, 548, 11], [584, 8, 548, 12, "style"], [584, 13, 548, 17], [584, 15, 548, 19, "styles"], [584, 21, 548, 25], [584, 22, 548, 26, "cameraContainer"], [584, 37, 548, 42], [585, 8, 548, 43, "id"], [585, 10, 548, 45], [585, 12, 548, 46], [585, 29, 548, 63], [586, 8, 548, 63, "children"], [586, 16, 548, 63], [586, 32, 549, 8], [586, 36, 549, 8, "_jsxDevRuntime"], [586, 50, 549, 8], [586, 51, 549, 8, "jsxDEV"], [586, 57, 549, 8], [586, 59, 549, 9, "_expoCamera"], [586, 70, 549, 9], [586, 71, 549, 9, "CameraView"], [586, 81, 549, 19], [587, 10, 550, 10, "ref"], [587, 13, 550, 13], [587, 15, 550, 15, "cameraRef"], [587, 24, 550, 25], [588, 10, 551, 10, "style"], [588, 15, 551, 15], [588, 17, 551, 17], [588, 18, 551, 18, "styles"], [588, 24, 551, 24], [588, 25, 551, 25, "camera"], [588, 31, 551, 31], [588, 33, 551, 33], [589, 12, 551, 35, "backgroundColor"], [589, 27, 551, 50], [589, 29, 551, 52], [590, 10, 551, 62], [590, 11, 551, 63], [590, 12, 551, 65], [591, 10, 552, 10, "facing"], [591, 16, 552, 16], [591, 18, 552, 17], [591, 24, 552, 23], [592, 10, 553, 10, "onLayout"], [592, 18, 553, 18], [592, 20, 553, 21, "e"], [592, 21, 553, 22], [592, 25, 553, 27], [593, 12, 554, 12, "console"], [593, 19, 554, 19], [593, 20, 554, 20, "log"], [593, 23, 554, 23], [593, 24, 554, 24], [593, 56, 554, 56], [593, 58, 554, 58, "e"], [593, 59, 554, 59], [593, 60, 554, 60, "nativeEvent"], [593, 71, 554, 71], [593, 72, 554, 72, "layout"], [593, 78, 554, 78], [593, 79, 554, 79], [594, 12, 555, 12, "setViewSize"], [594, 23, 555, 23], [594, 24, 555, 24], [595, 14, 555, 26, "width"], [595, 19, 555, 31], [595, 21, 555, 33, "e"], [595, 22, 555, 34], [595, 23, 555, 35, "nativeEvent"], [595, 34, 555, 46], [595, 35, 555, 47, "layout"], [595, 41, 555, 53], [595, 42, 555, 54, "width"], [595, 47, 555, 59], [596, 14, 555, 61, "height"], [596, 20, 555, 67], [596, 22, 555, 69, "e"], [596, 23, 555, 70], [596, 24, 555, 71, "nativeEvent"], [596, 35, 555, 82], [596, 36, 555, 83, "layout"], [596, 42, 555, 89], [596, 43, 555, 90, "height"], [597, 12, 555, 97], [597, 13, 555, 98], [597, 14, 555, 99], [598, 10, 556, 10], [598, 11, 556, 12], [599, 10, 557, 10, "onCameraReady"], [599, 23, 557, 23], [599, 25, 557, 25, "onCameraReady"], [599, 26, 557, 25], [599, 31, 557, 31], [600, 12, 558, 12, "console"], [600, 19, 558, 19], [600, 20, 558, 20, "log"], [600, 23, 558, 23], [600, 24, 558, 24], [600, 55, 558, 55], [600, 56, 558, 56], [601, 12, 559, 12, "setIsCameraReady"], [601, 28, 559, 28], [601, 29, 559, 29], [601, 33, 559, 33], [601, 34, 559, 34], [601, 35, 559, 35], [601, 36, 559, 36], [602, 10, 560, 10], [602, 11, 560, 12], [603, 10, 561, 10, "onMountError"], [603, 22, 561, 22], [603, 24, 561, 25, "error"], [603, 29, 561, 30], [603, 33, 561, 35], [604, 12, 562, 12, "console"], [604, 19, 562, 19], [604, 20, 562, 20, "error"], [604, 25, 562, 25], [604, 26, 562, 26], [604, 63, 562, 63], [604, 65, 562, 65, "error"], [604, 70, 562, 70], [604, 71, 562, 71], [605, 12, 563, 12, "setErrorMessage"], [605, 27, 563, 27], [605, 28, 563, 28], [605, 57, 563, 57], [605, 58, 563, 58], [606, 12, 564, 12, "setProcessingState"], [606, 30, 564, 30], [606, 31, 564, 31], [606, 38, 564, 38], [606, 39, 564, 39], [607, 10, 565, 10], [608, 8, 565, 12], [609, 10, 565, 12, "fileName"], [609, 18, 565, 12], [609, 20, 565, 12, "_jsxFileName"], [609, 32, 565, 12], [610, 10, 565, 12, "lineNumber"], [610, 20, 565, 12], [611, 10, 565, 12, "columnNumber"], [611, 22, 565, 12], [612, 8, 565, 12], [612, 15, 566, 9], [612, 16, 566, 10], [612, 18, 568, 9], [612, 19, 568, 10, "isCameraReady"], [612, 32, 568, 23], [612, 49, 569, 10], [612, 53, 569, 10, "_jsxDevRuntime"], [612, 67, 569, 10], [612, 68, 569, 10, "jsxDEV"], [612, 74, 569, 10], [612, 76, 569, 11, "_View"], [612, 81, 569, 11], [612, 82, 569, 11, "default"], [612, 89, 569, 15], [613, 10, 569, 16, "style"], [613, 15, 569, 21], [613, 17, 569, 23], [613, 18, 569, 24, "StyleSheet"], [613, 37, 569, 34], [613, 38, 569, 35, "absoluteFill"], [613, 50, 569, 47], [613, 52, 569, 49], [614, 12, 569, 51, "backgroundColor"], [614, 27, 569, 66], [614, 29, 569, 68], [614, 49, 569, 88], [615, 12, 569, 90, "justifyContent"], [615, 26, 569, 104], [615, 28, 569, 106], [615, 36, 569, 114], [616, 12, 569, 116, "alignItems"], [616, 22, 569, 126], [616, 24, 569, 128], [616, 32, 569, 136], [617, 12, 569, 138, "zIndex"], [617, 18, 569, 144], [617, 20, 569, 146], [618, 10, 569, 151], [618, 11, 569, 152], [618, 12, 569, 154], [619, 10, 569, 154, "children"], [619, 18, 569, 154], [619, 33, 570, 12], [619, 37, 570, 12, "_jsxDevRuntime"], [619, 51, 570, 12], [619, 52, 570, 12, "jsxDEV"], [619, 58, 570, 12], [619, 60, 570, 13, "_View"], [619, 65, 570, 13], [619, 66, 570, 13, "default"], [619, 73, 570, 17], [620, 12, 570, 18, "style"], [620, 17, 570, 23], [620, 19, 570, 25], [621, 14, 570, 27, "backgroundColor"], [621, 29, 570, 42], [621, 31, 570, 44], [621, 51, 570, 64], [622, 14, 570, 66, "padding"], [622, 21, 570, 73], [622, 23, 570, 75], [622, 25, 570, 77], [623, 14, 570, 79, "borderRadius"], [623, 26, 570, 91], [623, 28, 570, 93], [623, 30, 570, 95], [624, 14, 570, 97, "alignItems"], [624, 24, 570, 107], [624, 26, 570, 109], [625, 12, 570, 118], [625, 13, 570, 120], [626, 12, 570, 120, "children"], [626, 20, 570, 120], [626, 36, 571, 14], [626, 40, 571, 14, "_jsxDevRuntime"], [626, 54, 571, 14], [626, 55, 571, 14, "jsxDEV"], [626, 61, 571, 14], [626, 63, 571, 15, "_ActivityIndicator"], [626, 81, 571, 15], [626, 82, 571, 15, "default"], [626, 89, 571, 32], [627, 14, 571, 33, "size"], [627, 18, 571, 37], [627, 20, 571, 38], [627, 27, 571, 45], [628, 14, 571, 46, "color"], [628, 19, 571, 51], [628, 21, 571, 52], [628, 30, 571, 61], [629, 14, 571, 62, "style"], [629, 19, 571, 67], [629, 21, 571, 69], [630, 16, 571, 71, "marginBottom"], [630, 28, 571, 83], [630, 30, 571, 85], [631, 14, 571, 88], [632, 12, 571, 90], [633, 14, 571, 90, "fileName"], [633, 22, 571, 90], [633, 24, 571, 90, "_jsxFileName"], [633, 36, 571, 90], [634, 14, 571, 90, "lineNumber"], [634, 24, 571, 90], [635, 14, 571, 90, "columnNumber"], [635, 26, 571, 90], [636, 12, 571, 90], [636, 19, 571, 92], [636, 20, 571, 93], [636, 35, 572, 14], [636, 39, 572, 14, "_jsxDevRuntime"], [636, 53, 572, 14], [636, 54, 572, 14, "jsxDEV"], [636, 60, 572, 14], [636, 62, 572, 15, "_Text"], [636, 67, 572, 15], [636, 68, 572, 15, "default"], [636, 75, 572, 19], [637, 14, 572, 20, "style"], [637, 19, 572, 25], [637, 21, 572, 27], [638, 16, 572, 29, "color"], [638, 21, 572, 34], [638, 23, 572, 36], [638, 29, 572, 42], [639, 16, 572, 44, "fontSize"], [639, 24, 572, 52], [639, 26, 572, 54], [639, 28, 572, 56], [640, 16, 572, 58, "fontWeight"], [640, 26, 572, 68], [640, 28, 572, 70], [641, 14, 572, 76], [641, 15, 572, 78], [642, 14, 572, 78, "children"], [642, 22, 572, 78], [642, 24, 572, 79], [643, 12, 572, 101], [644, 14, 572, 101, "fileName"], [644, 22, 572, 101], [644, 24, 572, 101, "_jsxFileName"], [644, 36, 572, 101], [645, 14, 572, 101, "lineNumber"], [645, 24, 572, 101], [646, 14, 572, 101, "columnNumber"], [646, 26, 572, 101], [647, 12, 572, 101], [647, 19, 572, 107], [647, 20, 572, 108], [647, 35, 573, 14], [647, 39, 573, 14, "_jsxDevRuntime"], [647, 53, 573, 14], [647, 54, 573, 14, "jsxDEV"], [647, 60, 573, 14], [647, 62, 573, 15, "_Text"], [647, 67, 573, 15], [647, 68, 573, 15, "default"], [647, 75, 573, 19], [648, 14, 573, 20, "style"], [648, 19, 573, 25], [648, 21, 573, 27], [649, 16, 573, 29, "color"], [649, 21, 573, 34], [649, 23, 573, 36], [649, 32, 573, 45], [650, 16, 573, 47, "fontSize"], [650, 24, 573, 55], [650, 26, 573, 57], [650, 28, 573, 59], [651, 16, 573, 61, "marginTop"], [651, 25, 573, 70], [651, 27, 573, 72], [652, 14, 573, 74], [652, 15, 573, 76], [653, 14, 573, 76, "children"], [653, 22, 573, 76], [653, 24, 573, 77], [654, 12, 573, 88], [655, 14, 573, 88, "fileName"], [655, 22, 573, 88], [655, 24, 573, 88, "_jsxFileName"], [655, 36, 573, 88], [656, 14, 573, 88, "lineNumber"], [656, 24, 573, 88], [657, 14, 573, 88, "columnNumber"], [657, 26, 573, 88], [658, 12, 573, 88], [658, 19, 573, 94], [658, 20, 573, 95], [659, 10, 573, 95], [660, 12, 573, 95, "fileName"], [660, 20, 573, 95], [660, 22, 573, 95, "_jsxFileName"], [660, 34, 573, 95], [661, 12, 573, 95, "lineNumber"], [661, 22, 573, 95], [662, 12, 573, 95, "columnNumber"], [662, 24, 573, 95], [663, 10, 573, 95], [663, 17, 574, 18], [664, 8, 574, 19], [665, 10, 574, 19, "fileName"], [665, 18, 574, 19], [665, 20, 574, 19, "_jsxFileName"], [665, 32, 574, 19], [666, 10, 574, 19, "lineNumber"], [666, 20, 574, 19], [667, 10, 574, 19, "columnNumber"], [667, 22, 574, 19], [668, 8, 574, 19], [668, 15, 575, 16], [668, 16, 576, 9], [668, 18, 579, 9, "isCameraReady"], [668, 31, 579, 22], [668, 35, 579, 26, "previewBlurEnabled"], [668, 53, 579, 44], [668, 57, 579, 48, "viewSize"], [668, 65, 579, 56], [668, 66, 579, 57, "width"], [668, 71, 579, 62], [668, 74, 579, 65], [668, 75, 579, 66], [668, 92, 580, 10], [668, 96, 580, 10, "_jsxDevRuntime"], [668, 110, 580, 10], [668, 111, 580, 10, "jsxDEV"], [668, 117, 580, 10], [668, 119, 580, 10, "_jsxDevRuntime"], [668, 133, 580, 10], [668, 134, 580, 10, "Fragment"], [668, 142, 580, 10], [669, 10, 580, 10, "children"], [669, 18, 580, 10], [669, 34, 582, 12], [669, 38, 582, 12, "_jsxDevRuntime"], [669, 52, 582, 12], [669, 53, 582, 12, "jsxDEV"], [669, 59, 582, 12], [669, 61, 582, 13, "_LiveFaceCanvas"], [669, 76, 582, 13], [669, 77, 582, 13, "default"], [669, 84, 582, 27], [670, 12, 582, 28, "containerId"], [670, 23, 582, 39], [670, 25, 582, 40], [670, 42, 582, 57], [671, 12, 582, 58, "width"], [671, 17, 582, 63], [671, 19, 582, 65, "viewSize"], [671, 27, 582, 73], [671, 28, 582, 74, "width"], [671, 33, 582, 80], [672, 12, 582, 81, "height"], [672, 18, 582, 87], [672, 20, 582, 89, "viewSize"], [672, 28, 582, 97], [672, 29, 582, 98, "height"], [673, 10, 582, 105], [674, 12, 582, 105, "fileName"], [674, 20, 582, 105], [674, 22, 582, 105, "_jsxFileName"], [674, 34, 582, 105], [675, 12, 582, 105, "lineNumber"], [675, 22, 582, 105], [676, 12, 582, 105, "columnNumber"], [676, 24, 582, 105], [677, 10, 582, 105], [677, 17, 582, 107], [677, 18, 582, 108], [677, 33, 583, 12], [677, 37, 583, 12, "_jsxDevRuntime"], [677, 51, 583, 12], [677, 52, 583, 12, "jsxDEV"], [677, 58, 583, 12], [677, 60, 583, 13, "_View"], [677, 65, 583, 13], [677, 66, 583, 13, "default"], [677, 73, 583, 17], [678, 12, 583, 18, "style"], [678, 17, 583, 23], [678, 19, 583, 25], [678, 20, 583, 26, "StyleSheet"], [678, 39, 583, 36], [678, 40, 583, 37, "absoluteFill"], [678, 52, 583, 49], [678, 54, 583, 51], [679, 14, 583, 53, "pointerEvents"], [679, 27, 583, 66], [679, 29, 583, 68], [680, 12, 583, 75], [680, 13, 583, 76], [680, 14, 583, 78], [681, 12, 583, 78, "children"], [681, 20, 583, 78], [681, 36, 585, 12], [681, 40, 585, 12, "_jsxDevRuntime"], [681, 54, 585, 12], [681, 55, 585, 12, "jsxDEV"], [681, 61, 585, 12], [681, 63, 585, 13, "_expoBlur"], [681, 72, 585, 13], [681, 73, 585, 13, "BlurView"], [681, 81, 585, 21], [682, 14, 585, 22, "intensity"], [682, 23, 585, 31], [682, 25, 585, 33], [682, 27, 585, 36], [683, 14, 585, 37, "tint"], [683, 18, 585, 41], [683, 20, 585, 42], [683, 26, 585, 48], [684, 14, 585, 49, "style"], [684, 19, 585, 54], [684, 21, 585, 56], [684, 22, 585, 57, "styles"], [684, 28, 585, 63], [684, 29, 585, 64, "blurZone"], [684, 37, 585, 72], [684, 39, 585, 74], [685, 16, 586, 14, "left"], [685, 20, 586, 18], [685, 22, 586, 20], [685, 23, 586, 21], [686, 16, 587, 14, "top"], [686, 19, 587, 17], [686, 21, 587, 19, "viewSize"], [686, 29, 587, 27], [686, 30, 587, 28, "height"], [686, 36, 587, 34], [686, 39, 587, 37], [686, 42, 587, 40], [687, 16, 588, 14, "width"], [687, 21, 588, 19], [687, 23, 588, 21, "viewSize"], [687, 31, 588, 29], [687, 32, 588, 30, "width"], [687, 37, 588, 35], [688, 16, 589, 14, "height"], [688, 22, 589, 20], [688, 24, 589, 22, "viewSize"], [688, 32, 589, 30], [688, 33, 589, 31, "height"], [688, 39, 589, 37], [688, 42, 589, 40], [688, 46, 589, 44], [689, 16, 590, 14, "borderRadius"], [689, 28, 590, 26], [689, 30, 590, 28], [690, 14, 591, 12], [690, 15, 591, 13], [691, 12, 591, 15], [692, 14, 591, 15, "fileName"], [692, 22, 591, 15], [692, 24, 591, 15, "_jsxFileName"], [692, 36, 591, 15], [693, 14, 591, 15, "lineNumber"], [693, 24, 591, 15], [694, 14, 591, 15, "columnNumber"], [694, 26, 591, 15], [695, 12, 591, 15], [695, 19, 591, 17], [695, 20, 591, 18], [695, 35, 593, 12], [695, 39, 593, 12, "_jsxDevRuntime"], [695, 53, 593, 12], [695, 54, 593, 12, "jsxDEV"], [695, 60, 593, 12], [695, 62, 593, 13, "_expoBlur"], [695, 71, 593, 13], [695, 72, 593, 13, "BlurView"], [695, 80, 593, 21], [696, 14, 593, 22, "intensity"], [696, 23, 593, 31], [696, 25, 593, 33], [696, 27, 593, 36], [697, 14, 593, 37, "tint"], [697, 18, 593, 41], [697, 20, 593, 42], [697, 26, 593, 48], [698, 14, 593, 49, "style"], [698, 19, 593, 54], [698, 21, 593, 56], [698, 22, 593, 57, "styles"], [698, 28, 593, 63], [698, 29, 593, 64, "blurZone"], [698, 37, 593, 72], [698, 39, 593, 74], [699, 16, 594, 14, "left"], [699, 20, 594, 18], [699, 22, 594, 20], [699, 23, 594, 21], [700, 16, 595, 14, "top"], [700, 19, 595, 17], [700, 21, 595, 19], [700, 22, 595, 20], [701, 16, 596, 14, "width"], [701, 21, 596, 19], [701, 23, 596, 21, "viewSize"], [701, 31, 596, 29], [701, 32, 596, 30, "width"], [701, 37, 596, 35], [702, 16, 597, 14, "height"], [702, 22, 597, 20], [702, 24, 597, 22, "viewSize"], [702, 32, 597, 30], [702, 33, 597, 31, "height"], [702, 39, 597, 37], [702, 42, 597, 40], [702, 45, 597, 43], [703, 16, 598, 14, "borderRadius"], [703, 28, 598, 26], [703, 30, 598, 28], [704, 14, 599, 12], [704, 15, 599, 13], [705, 12, 599, 15], [706, 14, 599, 15, "fileName"], [706, 22, 599, 15], [706, 24, 599, 15, "_jsxFileName"], [706, 36, 599, 15], [707, 14, 599, 15, "lineNumber"], [707, 24, 599, 15], [708, 14, 599, 15, "columnNumber"], [708, 26, 599, 15], [709, 12, 599, 15], [709, 19, 599, 17], [709, 20, 599, 18], [709, 35, 601, 12], [709, 39, 601, 12, "_jsxDevRuntime"], [709, 53, 601, 12], [709, 54, 601, 12, "jsxDEV"], [709, 60, 601, 12], [709, 62, 601, 13, "_expoBlur"], [709, 71, 601, 13], [709, 72, 601, 13, "BlurView"], [709, 80, 601, 21], [710, 14, 601, 22, "intensity"], [710, 23, 601, 31], [710, 25, 601, 33], [710, 27, 601, 36], [711, 14, 601, 37, "tint"], [711, 18, 601, 41], [711, 20, 601, 42], [711, 26, 601, 48], [712, 14, 601, 49, "style"], [712, 19, 601, 54], [712, 21, 601, 56], [712, 22, 601, 57, "styles"], [712, 28, 601, 63], [712, 29, 601, 64, "blurZone"], [712, 37, 601, 72], [712, 39, 601, 74], [713, 16, 602, 14, "left"], [713, 20, 602, 18], [713, 22, 602, 20, "viewSize"], [713, 30, 602, 28], [713, 31, 602, 29, "width"], [713, 36, 602, 34], [713, 39, 602, 37], [713, 42, 602, 40], [713, 45, 602, 44, "viewSize"], [713, 53, 602, 52], [713, 54, 602, 53, "width"], [713, 59, 602, 58], [713, 62, 602, 61], [713, 66, 602, 66], [714, 16, 603, 14, "top"], [714, 19, 603, 17], [714, 21, 603, 19, "viewSize"], [714, 29, 603, 27], [714, 30, 603, 28, "height"], [714, 36, 603, 34], [714, 39, 603, 37], [714, 43, 603, 41], [714, 46, 603, 45, "viewSize"], [714, 54, 603, 53], [714, 55, 603, 54, "width"], [714, 60, 603, 59], [714, 63, 603, 62], [714, 67, 603, 67], [715, 16, 604, 14, "width"], [715, 21, 604, 19], [715, 23, 604, 21, "viewSize"], [715, 31, 604, 29], [715, 32, 604, 30, "width"], [715, 37, 604, 35], [715, 40, 604, 38], [715, 43, 604, 41], [716, 16, 605, 14, "height"], [716, 22, 605, 20], [716, 24, 605, 22, "viewSize"], [716, 32, 605, 30], [716, 33, 605, 31, "width"], [716, 38, 605, 36], [716, 41, 605, 39], [716, 44, 605, 42], [717, 16, 606, 14, "borderRadius"], [717, 28, 606, 26], [717, 30, 606, 29, "viewSize"], [717, 38, 606, 37], [717, 39, 606, 38, "width"], [717, 44, 606, 43], [717, 47, 606, 46], [717, 50, 606, 49], [717, 53, 606, 53], [718, 14, 607, 12], [718, 15, 607, 13], [719, 12, 607, 15], [720, 14, 607, 15, "fileName"], [720, 22, 607, 15], [720, 24, 607, 15, "_jsxFileName"], [720, 36, 607, 15], [721, 14, 607, 15, "lineNumber"], [721, 24, 607, 15], [722, 14, 607, 15, "columnNumber"], [722, 26, 607, 15], [723, 12, 607, 15], [723, 19, 607, 17], [723, 20, 607, 18], [723, 35, 608, 12], [723, 39, 608, 12, "_jsxDevRuntime"], [723, 53, 608, 12], [723, 54, 608, 12, "jsxDEV"], [723, 60, 608, 12], [723, 62, 608, 13, "_expoBlur"], [723, 71, 608, 13], [723, 72, 608, 13, "BlurView"], [723, 80, 608, 21], [724, 14, 608, 22, "intensity"], [724, 23, 608, 31], [724, 25, 608, 33], [724, 27, 608, 36], [725, 14, 608, 37, "tint"], [725, 18, 608, 41], [725, 20, 608, 42], [725, 26, 608, 48], [726, 14, 608, 49, "style"], [726, 19, 608, 54], [726, 21, 608, 56], [726, 22, 608, 57, "styles"], [726, 28, 608, 63], [726, 29, 608, 64, "blurZone"], [726, 37, 608, 72], [726, 39, 608, 74], [727, 16, 609, 14, "left"], [727, 20, 609, 18], [727, 22, 609, 20, "viewSize"], [727, 30, 609, 28], [727, 31, 609, 29, "width"], [727, 36, 609, 34], [727, 39, 609, 37], [727, 42, 609, 40], [727, 45, 609, 44, "viewSize"], [727, 53, 609, 52], [727, 54, 609, 53, "width"], [727, 59, 609, 58], [727, 62, 609, 61], [727, 66, 609, 66], [728, 16, 610, 14, "top"], [728, 19, 610, 17], [728, 21, 610, 19, "viewSize"], [728, 29, 610, 27], [728, 30, 610, 28, "height"], [728, 36, 610, 34], [728, 39, 610, 37], [728, 42, 610, 40], [728, 45, 610, 44, "viewSize"], [728, 53, 610, 52], [728, 54, 610, 53, "width"], [728, 59, 610, 58], [728, 62, 610, 61], [728, 66, 610, 66], [729, 16, 611, 14, "width"], [729, 21, 611, 19], [729, 23, 611, 21, "viewSize"], [729, 31, 611, 29], [729, 32, 611, 30, "width"], [729, 37, 611, 35], [729, 40, 611, 38], [729, 43, 611, 41], [730, 16, 612, 14, "height"], [730, 22, 612, 20], [730, 24, 612, 22, "viewSize"], [730, 32, 612, 30], [730, 33, 612, 31, "width"], [730, 38, 612, 36], [730, 41, 612, 39], [730, 44, 612, 42], [731, 16, 613, 14, "borderRadius"], [731, 28, 613, 26], [731, 30, 613, 29, "viewSize"], [731, 38, 613, 37], [731, 39, 613, 38, "width"], [731, 44, 613, 43], [731, 47, 613, 46], [731, 50, 613, 49], [731, 53, 613, 53], [732, 14, 614, 12], [732, 15, 614, 13], [733, 12, 614, 15], [734, 14, 614, 15, "fileName"], [734, 22, 614, 15], [734, 24, 614, 15, "_jsxFileName"], [734, 36, 614, 15], [735, 14, 614, 15, "lineNumber"], [735, 24, 614, 15], [736, 14, 614, 15, "columnNumber"], [736, 26, 614, 15], [737, 12, 614, 15], [737, 19, 614, 17], [737, 20, 614, 18], [737, 35, 615, 12], [737, 39, 615, 12, "_jsxDevRuntime"], [737, 53, 615, 12], [737, 54, 615, 12, "jsxDEV"], [737, 60, 615, 12], [737, 62, 615, 13, "_expoBlur"], [737, 71, 615, 13], [737, 72, 615, 13, "BlurView"], [737, 80, 615, 21], [738, 14, 615, 22, "intensity"], [738, 23, 615, 31], [738, 25, 615, 33], [738, 27, 615, 36], [739, 14, 615, 37, "tint"], [739, 18, 615, 41], [739, 20, 615, 42], [739, 26, 615, 48], [740, 14, 615, 49, "style"], [740, 19, 615, 54], [740, 21, 615, 56], [740, 22, 615, 57, "styles"], [740, 28, 615, 63], [740, 29, 615, 64, "blurZone"], [740, 37, 615, 72], [740, 39, 615, 74], [741, 16, 616, 14, "left"], [741, 20, 616, 18], [741, 22, 616, 20, "viewSize"], [741, 30, 616, 28], [741, 31, 616, 29, "width"], [741, 36, 616, 34], [741, 39, 616, 37], [741, 42, 616, 40], [741, 45, 616, 44, "viewSize"], [741, 53, 616, 52], [741, 54, 616, 53, "width"], [741, 59, 616, 58], [741, 62, 616, 61], [741, 66, 616, 66], [742, 16, 617, 14, "top"], [742, 19, 617, 17], [742, 21, 617, 19, "viewSize"], [742, 29, 617, 27], [742, 30, 617, 28, "height"], [742, 36, 617, 34], [742, 39, 617, 37], [742, 42, 617, 40], [742, 45, 617, 44, "viewSize"], [742, 53, 617, 52], [742, 54, 617, 53, "width"], [742, 59, 617, 58], [742, 62, 617, 61], [742, 66, 617, 66], [743, 16, 618, 14, "width"], [743, 21, 618, 19], [743, 23, 618, 21, "viewSize"], [743, 31, 618, 29], [743, 32, 618, 30, "width"], [743, 37, 618, 35], [743, 40, 618, 38], [743, 43, 618, 41], [744, 16, 619, 14, "height"], [744, 22, 619, 20], [744, 24, 619, 22, "viewSize"], [744, 32, 619, 30], [744, 33, 619, 31, "width"], [744, 38, 619, 36], [744, 41, 619, 39], [744, 44, 619, 42], [745, 16, 620, 14, "borderRadius"], [745, 28, 620, 26], [745, 30, 620, 29, "viewSize"], [745, 38, 620, 37], [745, 39, 620, 38, "width"], [745, 44, 620, 43], [745, 47, 620, 46], [745, 50, 620, 49], [745, 53, 620, 53], [746, 14, 621, 12], [746, 15, 621, 13], [747, 12, 621, 15], [748, 14, 621, 15, "fileName"], [748, 22, 621, 15], [748, 24, 621, 15, "_jsxFileName"], [748, 36, 621, 15], [749, 14, 621, 15, "lineNumber"], [749, 24, 621, 15], [750, 14, 621, 15, "columnNumber"], [750, 26, 621, 15], [751, 12, 621, 15], [751, 19, 621, 17], [751, 20, 621, 18], [751, 22, 623, 13, "__DEV__"], [751, 29, 623, 20], [751, 46, 624, 14], [751, 50, 624, 14, "_jsxDevRuntime"], [751, 64, 624, 14], [751, 65, 624, 14, "jsxDEV"], [751, 71, 624, 14], [751, 73, 624, 15, "_View"], [751, 78, 624, 15], [751, 79, 624, 15, "default"], [751, 86, 624, 19], [752, 14, 624, 20, "style"], [752, 19, 624, 25], [752, 21, 624, 27, "styles"], [752, 27, 624, 33], [752, 28, 624, 34, "previewChip"], [752, 39, 624, 46], [753, 14, 624, 46, "children"], [753, 22, 624, 46], [753, 37, 625, 16], [753, 41, 625, 16, "_jsxDevRuntime"], [753, 55, 625, 16], [753, 56, 625, 16, "jsxDEV"], [753, 62, 625, 16], [753, 64, 625, 17, "_Text"], [753, 69, 625, 17], [753, 70, 625, 17, "default"], [753, 77, 625, 21], [754, 16, 625, 22, "style"], [754, 21, 625, 27], [754, 23, 625, 29, "styles"], [754, 29, 625, 35], [754, 30, 625, 36, "previewChipText"], [754, 45, 625, 52], [755, 16, 625, 52, "children"], [755, 24, 625, 52], [755, 26, 625, 53], [756, 14, 625, 73], [757, 16, 625, 73, "fileName"], [757, 24, 625, 73], [757, 26, 625, 73, "_jsxFileName"], [757, 38, 625, 73], [758, 16, 625, 73, "lineNumber"], [758, 26, 625, 73], [759, 16, 625, 73, "columnNumber"], [759, 28, 625, 73], [760, 14, 625, 73], [760, 21, 625, 79], [761, 12, 625, 80], [762, 14, 625, 80, "fileName"], [762, 22, 625, 80], [762, 24, 625, 80, "_jsxFileName"], [762, 36, 625, 80], [763, 14, 625, 80, "lineNumber"], [763, 24, 625, 80], [764, 14, 625, 80, "columnNumber"], [764, 26, 625, 80], [765, 12, 625, 80], [765, 19, 626, 20], [765, 20, 627, 13], [766, 10, 627, 13], [767, 12, 627, 13, "fileName"], [767, 20, 627, 13], [767, 22, 627, 13, "_jsxFileName"], [767, 34, 627, 13], [768, 12, 627, 13, "lineNumber"], [768, 22, 627, 13], [769, 12, 627, 13, "columnNumber"], [769, 24, 627, 13], [770, 10, 627, 13], [770, 17, 628, 18], [770, 18, 628, 19], [771, 8, 628, 19], [771, 23, 629, 12], [771, 24, 630, 9], [771, 26, 632, 9, "isCameraReady"], [771, 39, 632, 22], [771, 56, 633, 10], [771, 60, 633, 10, "_jsxDevRuntime"], [771, 74, 633, 10], [771, 75, 633, 10, "jsxDEV"], [771, 81, 633, 10], [771, 83, 633, 10, "_jsxDevRuntime"], [771, 97, 633, 10], [771, 98, 633, 10, "Fragment"], [771, 106, 633, 10], [772, 10, 633, 10, "children"], [772, 18, 633, 10], [772, 34, 635, 12], [772, 38, 635, 12, "_jsxDevRuntime"], [772, 52, 635, 12], [772, 53, 635, 12, "jsxDEV"], [772, 59, 635, 12], [772, 61, 635, 13, "_View"], [772, 66, 635, 13], [772, 67, 635, 13, "default"], [772, 74, 635, 17], [773, 12, 635, 18, "style"], [773, 17, 635, 23], [773, 19, 635, 25, "styles"], [773, 25, 635, 31], [773, 26, 635, 32, "headerOverlay"], [773, 39, 635, 46], [774, 12, 635, 46, "children"], [774, 20, 635, 46], [774, 35, 636, 14], [774, 39, 636, 14, "_jsxDevRuntime"], [774, 53, 636, 14], [774, 54, 636, 14, "jsxDEV"], [774, 60, 636, 14], [774, 62, 636, 15, "_View"], [774, 67, 636, 15], [774, 68, 636, 15, "default"], [774, 75, 636, 19], [775, 14, 636, 20, "style"], [775, 19, 636, 25], [775, 21, 636, 27, "styles"], [775, 27, 636, 33], [775, 28, 636, 34, "headerContent"], [775, 41, 636, 48], [776, 14, 636, 48, "children"], [776, 22, 636, 48], [776, 38, 637, 16], [776, 42, 637, 16, "_jsxDevRuntime"], [776, 56, 637, 16], [776, 57, 637, 16, "jsxDEV"], [776, 63, 637, 16], [776, 65, 637, 17, "_View"], [776, 70, 637, 17], [776, 71, 637, 17, "default"], [776, 78, 637, 21], [777, 16, 637, 22, "style"], [777, 21, 637, 27], [777, 23, 637, 29, "styles"], [777, 29, 637, 35], [777, 30, 637, 36, "headerLeft"], [777, 40, 637, 47], [778, 16, 637, 47, "children"], [778, 24, 637, 47], [778, 40, 638, 18], [778, 44, 638, 18, "_jsxDevRuntime"], [778, 58, 638, 18], [778, 59, 638, 18, "jsxDEV"], [778, 65, 638, 18], [778, 67, 638, 19, "_Text"], [778, 72, 638, 19], [778, 73, 638, 19, "default"], [778, 80, 638, 23], [779, 18, 638, 24, "style"], [779, 23, 638, 29], [779, 25, 638, 31, "styles"], [779, 31, 638, 37], [779, 32, 638, 38, "headerTitle"], [779, 43, 638, 50], [780, 18, 638, 50, "children"], [780, 26, 638, 50], [780, 28, 638, 51], [781, 16, 638, 62], [782, 18, 638, 62, "fileName"], [782, 26, 638, 62], [782, 28, 638, 62, "_jsxFileName"], [782, 40, 638, 62], [783, 18, 638, 62, "lineNumber"], [783, 28, 638, 62], [784, 18, 638, 62, "columnNumber"], [784, 30, 638, 62], [785, 16, 638, 62], [785, 23, 638, 68], [785, 24, 638, 69], [785, 39, 639, 18], [785, 43, 639, 18, "_jsxDevRuntime"], [785, 57, 639, 18], [785, 58, 639, 18, "jsxDEV"], [785, 64, 639, 18], [785, 66, 639, 19, "_View"], [785, 71, 639, 19], [785, 72, 639, 19, "default"], [785, 79, 639, 23], [786, 18, 639, 24, "style"], [786, 23, 639, 29], [786, 25, 639, 31, "styles"], [786, 31, 639, 37], [786, 32, 639, 38, "subtitleRow"], [786, 43, 639, 50], [787, 18, 639, 50, "children"], [787, 26, 639, 50], [787, 42, 640, 20], [787, 46, 640, 20, "_jsxDevRuntime"], [787, 60, 640, 20], [787, 61, 640, 20, "jsxDEV"], [787, 67, 640, 20], [787, 69, 640, 21, "_Text"], [787, 74, 640, 21], [787, 75, 640, 21, "default"], [787, 82, 640, 25], [788, 20, 640, 26, "style"], [788, 25, 640, 31], [788, 27, 640, 33, "styles"], [788, 33, 640, 39], [788, 34, 640, 40, "webIcon"], [788, 41, 640, 48], [789, 20, 640, 48, "children"], [789, 28, 640, 48], [789, 30, 640, 49], [790, 18, 640, 51], [791, 20, 640, 51, "fileName"], [791, 28, 640, 51], [791, 30, 640, 51, "_jsxFileName"], [791, 42, 640, 51], [792, 20, 640, 51, "lineNumber"], [792, 30, 640, 51], [793, 20, 640, 51, "columnNumber"], [793, 32, 640, 51], [794, 18, 640, 51], [794, 25, 640, 57], [794, 26, 640, 58], [794, 41, 641, 20], [794, 45, 641, 20, "_jsxDevRuntime"], [794, 59, 641, 20], [794, 60, 641, 20, "jsxDEV"], [794, 66, 641, 20], [794, 68, 641, 21, "_Text"], [794, 73, 641, 21], [794, 74, 641, 21, "default"], [794, 81, 641, 25], [795, 20, 641, 26, "style"], [795, 25, 641, 31], [795, 27, 641, 33, "styles"], [795, 33, 641, 39], [795, 34, 641, 40, "headerSubtitle"], [795, 48, 641, 55], [796, 20, 641, 55, "children"], [796, 28, 641, 55], [796, 30, 641, 56], [797, 18, 641, 71], [798, 20, 641, 71, "fileName"], [798, 28, 641, 71], [798, 30, 641, 71, "_jsxFileName"], [798, 42, 641, 71], [799, 20, 641, 71, "lineNumber"], [799, 30, 641, 71], [800, 20, 641, 71, "columnNumber"], [800, 32, 641, 71], [801, 18, 641, 71], [801, 25, 641, 77], [801, 26, 641, 78], [802, 16, 641, 78], [803, 18, 641, 78, "fileName"], [803, 26, 641, 78], [803, 28, 641, 78, "_jsxFileName"], [803, 40, 641, 78], [804, 18, 641, 78, "lineNumber"], [804, 28, 641, 78], [805, 18, 641, 78, "columnNumber"], [805, 30, 641, 78], [806, 16, 641, 78], [806, 23, 642, 24], [806, 24, 642, 25], [806, 26, 643, 19, "challengeCode"], [806, 39, 643, 32], [806, 56, 644, 20], [806, 60, 644, 20, "_jsxDevRuntime"], [806, 74, 644, 20], [806, 75, 644, 20, "jsxDEV"], [806, 81, 644, 20], [806, 83, 644, 21, "_View"], [806, 88, 644, 21], [806, 89, 644, 21, "default"], [806, 96, 644, 25], [807, 18, 644, 26, "style"], [807, 23, 644, 31], [807, 25, 644, 33, "styles"], [807, 31, 644, 39], [807, 32, 644, 40, "challengeRow"], [807, 44, 644, 53], [808, 18, 644, 53, "children"], [808, 26, 644, 53], [808, 42, 645, 22], [808, 46, 645, 22, "_jsxDevRuntime"], [808, 60, 645, 22], [808, 61, 645, 22, "jsxDEV"], [808, 67, 645, 22], [808, 69, 645, 23, "_lucideReactNative"], [808, 87, 645, 23], [808, 88, 645, 23, "Shield"], [808, 94, 645, 29], [809, 20, 645, 30, "size"], [809, 24, 645, 34], [809, 26, 645, 36], [809, 28, 645, 39], [810, 20, 645, 40, "color"], [810, 25, 645, 45], [810, 27, 645, 46], [811, 18, 645, 52], [812, 20, 645, 52, "fileName"], [812, 28, 645, 52], [812, 30, 645, 52, "_jsxFileName"], [812, 42, 645, 52], [813, 20, 645, 52, "lineNumber"], [813, 30, 645, 52], [814, 20, 645, 52, "columnNumber"], [814, 32, 645, 52], [815, 18, 645, 52], [815, 25, 645, 54], [815, 26, 645, 55], [815, 41, 646, 22], [815, 45, 646, 22, "_jsxDevRuntime"], [815, 59, 646, 22], [815, 60, 646, 22, "jsxDEV"], [815, 66, 646, 22], [815, 68, 646, 23, "_Text"], [815, 73, 646, 23], [815, 74, 646, 23, "default"], [815, 81, 646, 27], [816, 20, 646, 28, "style"], [816, 25, 646, 33], [816, 27, 646, 35, "styles"], [816, 33, 646, 41], [816, 34, 646, 42, "challengeCode"], [816, 47, 646, 56], [817, 20, 646, 56, "children"], [817, 28, 646, 56], [817, 30, 646, 58, "challengeCode"], [818, 18, 646, 71], [819, 20, 646, 71, "fileName"], [819, 28, 646, 71], [819, 30, 646, 71, "_jsxFileName"], [819, 42, 646, 71], [820, 20, 646, 71, "lineNumber"], [820, 30, 646, 71], [821, 20, 646, 71, "columnNumber"], [821, 32, 646, 71], [822, 18, 646, 71], [822, 25, 646, 78], [822, 26, 646, 79], [823, 16, 646, 79], [824, 18, 646, 79, "fileName"], [824, 26, 646, 79], [824, 28, 646, 79, "_jsxFileName"], [824, 40, 646, 79], [825, 18, 646, 79, "lineNumber"], [825, 28, 646, 79], [826, 18, 646, 79, "columnNumber"], [826, 30, 646, 79], [827, 16, 646, 79], [827, 23, 647, 26], [827, 24, 648, 19], [828, 14, 648, 19], [829, 16, 648, 19, "fileName"], [829, 24, 648, 19], [829, 26, 648, 19, "_jsxFileName"], [829, 38, 648, 19], [830, 16, 648, 19, "lineNumber"], [830, 26, 648, 19], [831, 16, 648, 19, "columnNumber"], [831, 28, 648, 19], [832, 14, 648, 19], [832, 21, 649, 22], [832, 22, 649, 23], [832, 37, 650, 16], [832, 41, 650, 16, "_jsxDevRuntime"], [832, 55, 650, 16], [832, 56, 650, 16, "jsxDEV"], [832, 62, 650, 16], [832, 64, 650, 17, "_TouchableOpacity"], [832, 81, 650, 17], [832, 82, 650, 17, "default"], [832, 89, 650, 33], [833, 16, 650, 34, "onPress"], [833, 23, 650, 41], [833, 25, 650, 43, "onCancel"], [833, 33, 650, 52], [834, 16, 650, 53, "style"], [834, 21, 650, 58], [834, 23, 650, 60, "styles"], [834, 29, 650, 66], [834, 30, 650, 67, "closeButton"], [834, 41, 650, 79], [835, 16, 650, 79, "children"], [835, 24, 650, 79], [835, 39, 651, 18], [835, 43, 651, 18, "_jsxDevRuntime"], [835, 57, 651, 18], [835, 58, 651, 18, "jsxDEV"], [835, 64, 651, 18], [835, 66, 651, 19, "_lucideReactNative"], [835, 84, 651, 19], [835, 85, 651, 19, "X"], [835, 86, 651, 20], [836, 18, 651, 21, "size"], [836, 22, 651, 25], [836, 24, 651, 27], [836, 26, 651, 30], [837, 18, 651, 31, "color"], [837, 23, 651, 36], [837, 25, 651, 37], [838, 16, 651, 43], [839, 18, 651, 43, "fileName"], [839, 26, 651, 43], [839, 28, 651, 43, "_jsxFileName"], [839, 40, 651, 43], [840, 18, 651, 43, "lineNumber"], [840, 28, 651, 43], [841, 18, 651, 43, "columnNumber"], [841, 30, 651, 43], [842, 16, 651, 43], [842, 23, 651, 45], [843, 14, 651, 46], [844, 16, 651, 46, "fileName"], [844, 24, 651, 46], [844, 26, 651, 46, "_jsxFileName"], [844, 38, 651, 46], [845, 16, 651, 46, "lineNumber"], [845, 26, 651, 46], [846, 16, 651, 46, "columnNumber"], [846, 28, 651, 46], [847, 14, 651, 46], [847, 21, 652, 34], [847, 22, 652, 35], [848, 12, 652, 35], [849, 14, 652, 35, "fileName"], [849, 22, 652, 35], [849, 24, 652, 35, "_jsxFileName"], [849, 36, 652, 35], [850, 14, 652, 35, "lineNumber"], [850, 24, 652, 35], [851, 14, 652, 35, "columnNumber"], [851, 26, 652, 35], [852, 12, 652, 35], [852, 19, 653, 20], [853, 10, 653, 21], [854, 12, 653, 21, "fileName"], [854, 20, 653, 21], [854, 22, 653, 21, "_jsxFileName"], [854, 34, 653, 21], [855, 12, 653, 21, "lineNumber"], [855, 22, 653, 21], [856, 12, 653, 21, "columnNumber"], [856, 24, 653, 21], [857, 10, 653, 21], [857, 17, 654, 18], [857, 18, 654, 19], [857, 33, 656, 12], [857, 37, 656, 12, "_jsxDevRuntime"], [857, 51, 656, 12], [857, 52, 656, 12, "jsxDEV"], [857, 58, 656, 12], [857, 60, 656, 13, "_View"], [857, 65, 656, 13], [857, 66, 656, 13, "default"], [857, 73, 656, 17], [858, 12, 656, 18, "style"], [858, 17, 656, 23], [858, 19, 656, 25, "styles"], [858, 25, 656, 31], [858, 26, 656, 32, "privacyNotice"], [858, 39, 656, 46], [859, 12, 656, 46, "children"], [859, 20, 656, 46], [859, 36, 657, 14], [859, 40, 657, 14, "_jsxDevRuntime"], [859, 54, 657, 14], [859, 55, 657, 14, "jsxDEV"], [859, 61, 657, 14], [859, 63, 657, 15, "_lucideReactNative"], [859, 81, 657, 15], [859, 82, 657, 15, "Shield"], [859, 88, 657, 21], [860, 14, 657, 22, "size"], [860, 18, 657, 26], [860, 20, 657, 28], [860, 22, 657, 31], [861, 14, 657, 32, "color"], [861, 19, 657, 37], [861, 21, 657, 38], [862, 12, 657, 47], [863, 14, 657, 47, "fileName"], [863, 22, 657, 47], [863, 24, 657, 47, "_jsxFileName"], [863, 36, 657, 47], [864, 14, 657, 47, "lineNumber"], [864, 24, 657, 47], [865, 14, 657, 47, "columnNumber"], [865, 26, 657, 47], [866, 12, 657, 47], [866, 19, 657, 49], [866, 20, 657, 50], [866, 35, 658, 14], [866, 39, 658, 14, "_jsxDevRuntime"], [866, 53, 658, 14], [866, 54, 658, 14, "jsxDEV"], [866, 60, 658, 14], [866, 62, 658, 15, "_Text"], [866, 67, 658, 15], [866, 68, 658, 15, "default"], [866, 75, 658, 19], [867, 14, 658, 20, "style"], [867, 19, 658, 25], [867, 21, 658, 27, "styles"], [867, 27, 658, 33], [867, 28, 658, 34, "privacyText"], [867, 39, 658, 46], [868, 14, 658, 46, "children"], [868, 22, 658, 46], [868, 24, 658, 47], [869, 12, 660, 14], [870, 14, 660, 14, "fileName"], [870, 22, 660, 14], [870, 24, 660, 14, "_jsxFileName"], [870, 36, 660, 14], [871, 14, 660, 14, "lineNumber"], [871, 24, 660, 14], [872, 14, 660, 14, "columnNumber"], [872, 26, 660, 14], [873, 12, 660, 14], [873, 19, 660, 20], [873, 20, 660, 21], [874, 10, 660, 21], [875, 12, 660, 21, "fileName"], [875, 20, 660, 21], [875, 22, 660, 21, "_jsxFileName"], [875, 34, 660, 21], [876, 12, 660, 21, "lineNumber"], [876, 22, 660, 21], [877, 12, 660, 21, "columnNumber"], [877, 24, 660, 21], [878, 10, 660, 21], [878, 17, 661, 18], [878, 18, 661, 19], [878, 33, 663, 12], [878, 37, 663, 12, "_jsxDevRuntime"], [878, 51, 663, 12], [878, 52, 663, 12, "jsxDEV"], [878, 58, 663, 12], [878, 60, 663, 13, "_View"], [878, 65, 663, 13], [878, 66, 663, 13, "default"], [878, 73, 663, 17], [879, 12, 663, 18, "style"], [879, 17, 663, 23], [879, 19, 663, 25, "styles"], [879, 25, 663, 31], [879, 26, 663, 32, "footer<PERSON><PERSON><PERSON>"], [879, 39, 663, 46], [880, 12, 663, 46, "children"], [880, 20, 663, 46], [880, 36, 664, 14], [880, 40, 664, 14, "_jsxDevRuntime"], [880, 54, 664, 14], [880, 55, 664, 14, "jsxDEV"], [880, 61, 664, 14], [880, 63, 664, 15, "_Text"], [880, 68, 664, 15], [880, 69, 664, 15, "default"], [880, 76, 664, 19], [881, 14, 664, 20, "style"], [881, 19, 664, 25], [881, 21, 664, 27, "styles"], [881, 27, 664, 33], [881, 28, 664, 34, "instruction"], [881, 39, 664, 46], [882, 14, 664, 46, "children"], [882, 22, 664, 46], [882, 24, 664, 47], [883, 12, 666, 14], [884, 14, 666, 14, "fileName"], [884, 22, 666, 14], [884, 24, 666, 14, "_jsxFileName"], [884, 36, 666, 14], [885, 14, 666, 14, "lineNumber"], [885, 24, 666, 14], [886, 14, 666, 14, "columnNumber"], [886, 26, 666, 14], [887, 12, 666, 14], [887, 19, 666, 20], [887, 20, 666, 21], [887, 35, 668, 14], [887, 39, 668, 14, "_jsxDevRuntime"], [887, 53, 668, 14], [887, 54, 668, 14, "jsxDEV"], [887, 60, 668, 14], [887, 62, 668, 15, "_TouchableOpacity"], [887, 79, 668, 15], [887, 80, 668, 15, "default"], [887, 87, 668, 31], [888, 14, 669, 16, "onPress"], [888, 21, 669, 23], [888, 23, 669, 25, "capturePhoto"], [888, 35, 669, 38], [889, 14, 670, 16, "disabled"], [889, 22, 670, 24], [889, 24, 670, 26, "processingState"], [889, 39, 670, 41], [889, 44, 670, 46], [889, 50, 670, 52], [889, 54, 670, 56], [889, 55, 670, 57, "isCameraReady"], [889, 68, 670, 71], [890, 14, 671, 16, "style"], [890, 19, 671, 21], [890, 21, 671, 23], [890, 22, 672, 18, "styles"], [890, 28, 672, 24], [890, 29, 672, 25, "shutterButton"], [890, 42, 672, 38], [890, 44, 673, 18, "processingState"], [890, 59, 673, 33], [890, 64, 673, 38], [890, 70, 673, 44], [890, 74, 673, 48, "styles"], [890, 80, 673, 54], [890, 81, 673, 55, "shutterButtonDisabled"], [890, 102, 673, 76], [890, 103, 674, 18], [891, 14, 674, 18, "children"], [891, 22, 674, 18], [891, 24, 676, 17, "processingState"], [891, 39, 676, 32], [891, 44, 676, 37], [891, 50, 676, 43], [891, 66, 677, 18], [891, 70, 677, 18, "_jsxDevRuntime"], [891, 84, 677, 18], [891, 85, 677, 18, "jsxDEV"], [891, 91, 677, 18], [891, 93, 677, 19, "_View"], [891, 98, 677, 19], [891, 99, 677, 19, "default"], [891, 106, 677, 23], [892, 16, 677, 24, "style"], [892, 21, 677, 29], [892, 23, 677, 31, "styles"], [892, 29, 677, 37], [892, 30, 677, 38, "shutterInner"], [893, 14, 677, 51], [894, 16, 677, 51, "fileName"], [894, 24, 677, 51], [894, 26, 677, 51, "_jsxFileName"], [894, 38, 677, 51], [895, 16, 677, 51, "lineNumber"], [895, 26, 677, 51], [896, 16, 677, 51, "columnNumber"], [896, 28, 677, 51], [897, 14, 677, 51], [897, 21, 677, 53], [897, 22, 677, 54], [897, 38, 679, 18], [897, 42, 679, 18, "_jsxDevRuntime"], [897, 56, 679, 18], [897, 57, 679, 18, "jsxDEV"], [897, 63, 679, 18], [897, 65, 679, 19, "_ActivityIndicator"], [897, 83, 679, 19], [897, 84, 679, 19, "default"], [897, 91, 679, 36], [898, 16, 679, 37, "size"], [898, 20, 679, 41], [898, 22, 679, 42], [898, 29, 679, 49], [899, 16, 679, 50, "color"], [899, 21, 679, 55], [899, 23, 679, 56], [900, 14, 679, 65], [901, 16, 679, 65, "fileName"], [901, 24, 679, 65], [901, 26, 679, 65, "_jsxFileName"], [901, 38, 679, 65], [902, 16, 679, 65, "lineNumber"], [902, 26, 679, 65], [903, 16, 679, 65, "columnNumber"], [903, 28, 679, 65], [904, 14, 679, 65], [904, 21, 679, 67], [905, 12, 680, 17], [906, 14, 680, 17, "fileName"], [906, 22, 680, 17], [906, 24, 680, 17, "_jsxFileName"], [906, 36, 680, 17], [907, 14, 680, 17, "lineNumber"], [907, 24, 680, 17], [908, 14, 680, 17, "columnNumber"], [908, 26, 680, 17], [909, 12, 680, 17], [909, 19, 681, 32], [909, 20, 681, 33], [909, 35, 682, 14], [909, 39, 682, 14, "_jsxDevRuntime"], [909, 53, 682, 14], [909, 54, 682, 14, "jsxDEV"], [909, 60, 682, 14], [909, 62, 682, 15, "_Text"], [909, 67, 682, 15], [909, 68, 682, 15, "default"], [909, 75, 682, 19], [910, 14, 682, 20, "style"], [910, 19, 682, 25], [910, 21, 682, 27, "styles"], [910, 27, 682, 33], [910, 28, 682, 34, "privacyNote"], [910, 39, 682, 46], [911, 14, 682, 46, "children"], [911, 22, 682, 46], [911, 24, 682, 47], [912, 12, 684, 14], [913, 14, 684, 14, "fileName"], [913, 22, 684, 14], [913, 24, 684, 14, "_jsxFileName"], [913, 36, 684, 14], [914, 14, 684, 14, "lineNumber"], [914, 24, 684, 14], [915, 14, 684, 14, "columnNumber"], [915, 26, 684, 14], [916, 12, 684, 14], [916, 19, 684, 20], [916, 20, 684, 21], [917, 10, 684, 21], [918, 12, 684, 21, "fileName"], [918, 20, 684, 21], [918, 22, 684, 21, "_jsxFileName"], [918, 34, 684, 21], [919, 12, 684, 21, "lineNumber"], [919, 22, 684, 21], [920, 12, 684, 21, "columnNumber"], [920, 24, 684, 21], [921, 10, 684, 21], [921, 17, 685, 18], [921, 18, 685, 19], [922, 8, 685, 19], [922, 23, 686, 12], [922, 24, 687, 9], [923, 6, 687, 9], [924, 8, 687, 9, "fileName"], [924, 16, 687, 9], [924, 18, 687, 9, "_jsxFileName"], [924, 30, 687, 9], [925, 8, 687, 9, "lineNumber"], [925, 18, 687, 9], [926, 8, 687, 9, "columnNumber"], [926, 20, 687, 9], [927, 6, 687, 9], [927, 13, 688, 12], [927, 14, 688, 13], [927, 29, 690, 6], [927, 33, 690, 6, "_jsxDevRuntime"], [927, 47, 690, 6], [927, 48, 690, 6, "jsxDEV"], [927, 54, 690, 6], [927, 56, 690, 7, "_Modal"], [927, 62, 690, 7], [927, 63, 690, 7, "default"], [927, 70, 690, 12], [928, 8, 691, 8, "visible"], [928, 15, 691, 15], [928, 17, 691, 17, "processingState"], [928, 32, 691, 32], [928, 37, 691, 37], [928, 43, 691, 43], [928, 47, 691, 47, "processingState"], [928, 62, 691, 62], [928, 67, 691, 67], [928, 74, 691, 75], [929, 8, 692, 8, "transparent"], [929, 19, 692, 19], [930, 8, 693, 8, "animationType"], [930, 21, 693, 21], [930, 23, 693, 22], [930, 29, 693, 28], [931, 8, 693, 28, "children"], [931, 16, 693, 28], [931, 31, 695, 8], [931, 35, 695, 8, "_jsxDevRuntime"], [931, 49, 695, 8], [931, 50, 695, 8, "jsxDEV"], [931, 56, 695, 8], [931, 58, 695, 9, "_View"], [931, 63, 695, 9], [931, 64, 695, 9, "default"], [931, 71, 695, 13], [932, 10, 695, 14, "style"], [932, 15, 695, 19], [932, 17, 695, 21, "styles"], [932, 23, 695, 27], [932, 24, 695, 28, "processingModal"], [932, 39, 695, 44], [933, 10, 695, 44, "children"], [933, 18, 695, 44], [933, 33, 696, 10], [933, 37, 696, 10, "_jsxDevRuntime"], [933, 51, 696, 10], [933, 52, 696, 10, "jsxDEV"], [933, 58, 696, 10], [933, 60, 696, 11, "_View"], [933, 65, 696, 11], [933, 66, 696, 11, "default"], [933, 73, 696, 15], [934, 12, 696, 16, "style"], [934, 17, 696, 21], [934, 19, 696, 23, "styles"], [934, 25, 696, 29], [934, 26, 696, 30, "processingContent"], [934, 43, 696, 48], [935, 12, 696, 48, "children"], [935, 20, 696, 48], [935, 36, 697, 12], [935, 40, 697, 12, "_jsxDevRuntime"], [935, 54, 697, 12], [935, 55, 697, 12, "jsxDEV"], [935, 61, 697, 12], [935, 63, 697, 13, "_ActivityIndicator"], [935, 81, 697, 13], [935, 82, 697, 13, "default"], [935, 89, 697, 30], [936, 14, 697, 31, "size"], [936, 18, 697, 35], [936, 20, 697, 36], [936, 27, 697, 43], [937, 14, 697, 44, "color"], [937, 19, 697, 49], [937, 21, 697, 50], [938, 12, 697, 59], [939, 14, 697, 59, "fileName"], [939, 22, 697, 59], [939, 24, 697, 59, "_jsxFileName"], [939, 36, 697, 59], [940, 14, 697, 59, "lineNumber"], [940, 24, 697, 59], [941, 14, 697, 59, "columnNumber"], [941, 26, 697, 59], [942, 12, 697, 59], [942, 19, 697, 61], [942, 20, 697, 62], [942, 35, 699, 12], [942, 39, 699, 12, "_jsxDevRuntime"], [942, 53, 699, 12], [942, 54, 699, 12, "jsxDEV"], [942, 60, 699, 12], [942, 62, 699, 13, "_Text"], [942, 67, 699, 13], [942, 68, 699, 13, "default"], [942, 75, 699, 17], [943, 14, 699, 18, "style"], [943, 19, 699, 23], [943, 21, 699, 25, "styles"], [943, 27, 699, 31], [943, 28, 699, 32, "processingTitle"], [943, 43, 699, 48], [944, 14, 699, 48, "children"], [944, 22, 699, 48], [944, 25, 700, 15, "processingState"], [944, 40, 700, 30], [944, 45, 700, 35], [944, 56, 700, 46], [944, 60, 700, 50], [944, 80, 700, 70], [944, 82, 701, 15, "processingState"], [944, 97, 701, 30], [944, 102, 701, 35], [944, 113, 701, 46], [944, 117, 701, 50], [944, 146, 701, 79], [944, 148, 702, 15, "processingState"], [944, 163, 702, 30], [944, 168, 702, 35], [944, 180, 702, 47], [944, 184, 702, 51], [944, 216, 702, 83], [944, 218, 703, 15, "processingState"], [944, 233, 703, 30], [944, 238, 703, 35], [944, 249, 703, 46], [944, 253, 703, 50], [944, 275, 703, 72], [945, 12, 703, 72], [946, 14, 703, 72, "fileName"], [946, 22, 703, 72], [946, 24, 703, 72, "_jsxFileName"], [946, 36, 703, 72], [947, 14, 703, 72, "lineNumber"], [947, 24, 703, 72], [948, 14, 703, 72, "columnNumber"], [948, 26, 703, 72], [949, 12, 703, 72], [949, 19, 704, 18], [949, 20, 704, 19], [949, 35, 705, 12], [949, 39, 705, 12, "_jsxDevRuntime"], [949, 53, 705, 12], [949, 54, 705, 12, "jsxDEV"], [949, 60, 705, 12], [949, 62, 705, 13, "_View"], [949, 67, 705, 13], [949, 68, 705, 13, "default"], [949, 75, 705, 17], [950, 14, 705, 18, "style"], [950, 19, 705, 23], [950, 21, 705, 25, "styles"], [950, 27, 705, 31], [950, 28, 705, 32, "progressBar"], [950, 39, 705, 44], [951, 14, 705, 44, "children"], [951, 22, 705, 44], [951, 37, 706, 14], [951, 41, 706, 14, "_jsxDevRuntime"], [951, 55, 706, 14], [951, 56, 706, 14, "jsxDEV"], [951, 62, 706, 14], [951, 64, 706, 15, "_View"], [951, 69, 706, 15], [951, 70, 706, 15, "default"], [951, 77, 706, 19], [952, 16, 707, 16, "style"], [952, 21, 707, 21], [952, 23, 707, 23], [952, 24, 708, 18, "styles"], [952, 30, 708, 24], [952, 31, 708, 25, "progressFill"], [952, 43, 708, 37], [952, 45, 709, 18], [953, 18, 709, 20, "width"], [953, 23, 709, 25], [953, 25, 709, 27], [953, 28, 709, 30, "processingProgress"], [953, 46, 709, 48], [954, 16, 709, 52], [954, 17, 709, 53], [955, 14, 710, 18], [956, 16, 710, 18, "fileName"], [956, 24, 710, 18], [956, 26, 710, 18, "_jsxFileName"], [956, 38, 710, 18], [957, 16, 710, 18, "lineNumber"], [957, 26, 710, 18], [958, 16, 710, 18, "columnNumber"], [958, 28, 710, 18], [959, 14, 710, 18], [959, 21, 711, 15], [960, 12, 711, 16], [961, 14, 711, 16, "fileName"], [961, 22, 711, 16], [961, 24, 711, 16, "_jsxFileName"], [961, 36, 711, 16], [962, 14, 711, 16, "lineNumber"], [962, 24, 711, 16], [963, 14, 711, 16, "columnNumber"], [963, 26, 711, 16], [964, 12, 711, 16], [964, 19, 712, 18], [964, 20, 712, 19], [964, 35, 713, 12], [964, 39, 713, 12, "_jsxDevRuntime"], [964, 53, 713, 12], [964, 54, 713, 12, "jsxDEV"], [964, 60, 713, 12], [964, 62, 713, 13, "_Text"], [964, 67, 713, 13], [964, 68, 713, 13, "default"], [964, 75, 713, 17], [965, 14, 713, 18, "style"], [965, 19, 713, 23], [965, 21, 713, 25, "styles"], [965, 27, 713, 31], [965, 28, 713, 32, "processingDescription"], [965, 49, 713, 54], [966, 14, 713, 54, "children"], [966, 22, 713, 54], [966, 25, 714, 15, "processingState"], [966, 40, 714, 30], [966, 45, 714, 35], [966, 56, 714, 46], [966, 60, 714, 50], [966, 89, 714, 79], [966, 91, 715, 15, "processingState"], [966, 106, 715, 30], [966, 111, 715, 35], [966, 122, 715, 46], [966, 126, 715, 50], [966, 164, 715, 88], [966, 166, 716, 15, "processingState"], [966, 181, 716, 30], [966, 186, 716, 35], [966, 198, 716, 47], [966, 202, 716, 51], [966, 247, 716, 96], [966, 249, 717, 15, "processingState"], [966, 264, 717, 30], [966, 269, 717, 35], [966, 280, 717, 46], [966, 284, 717, 50], [966, 325, 717, 91], [967, 12, 717, 91], [968, 14, 717, 91, "fileName"], [968, 22, 717, 91], [968, 24, 717, 91, "_jsxFileName"], [968, 36, 717, 91], [969, 14, 717, 91, "lineNumber"], [969, 24, 717, 91], [970, 14, 717, 91, "columnNumber"], [970, 26, 717, 91], [971, 12, 717, 91], [971, 19, 718, 18], [971, 20, 718, 19], [971, 22, 719, 13, "processingState"], [971, 37, 719, 28], [971, 42, 719, 33], [971, 53, 719, 44], [971, 70, 720, 14], [971, 74, 720, 14, "_jsxDevRuntime"], [971, 88, 720, 14], [971, 89, 720, 14, "jsxDEV"], [971, 95, 720, 14], [971, 97, 720, 15, "_lucideReactNative"], [971, 115, 720, 15], [971, 116, 720, 15, "CheckCircle"], [971, 127, 720, 26], [972, 14, 720, 27, "size"], [972, 18, 720, 31], [972, 20, 720, 33], [972, 22, 720, 36], [973, 14, 720, 37, "color"], [973, 19, 720, 42], [973, 21, 720, 43], [973, 30, 720, 52], [974, 14, 720, 53, "style"], [974, 19, 720, 58], [974, 21, 720, 60, "styles"], [974, 27, 720, 66], [974, 28, 720, 67, "successIcon"], [975, 12, 720, 79], [976, 14, 720, 79, "fileName"], [976, 22, 720, 79], [976, 24, 720, 79, "_jsxFileName"], [976, 36, 720, 79], [977, 14, 720, 79, "lineNumber"], [977, 24, 720, 79], [978, 14, 720, 79, "columnNumber"], [978, 26, 720, 79], [979, 12, 720, 79], [979, 19, 720, 81], [979, 20, 721, 13], [980, 10, 721, 13], [981, 12, 721, 13, "fileName"], [981, 20, 721, 13], [981, 22, 721, 13, "_jsxFileName"], [981, 34, 721, 13], [982, 12, 721, 13, "lineNumber"], [982, 22, 721, 13], [983, 12, 721, 13, "columnNumber"], [983, 24, 721, 13], [984, 10, 721, 13], [984, 17, 722, 16], [985, 8, 722, 17], [986, 10, 722, 17, "fileName"], [986, 18, 722, 17], [986, 20, 722, 17, "_jsxFileName"], [986, 32, 722, 17], [987, 10, 722, 17, "lineNumber"], [987, 20, 722, 17], [988, 10, 722, 17, "columnNumber"], [988, 22, 722, 17], [989, 8, 722, 17], [989, 15, 723, 14], [990, 6, 723, 15], [991, 8, 723, 15, "fileName"], [991, 16, 723, 15], [991, 18, 723, 15, "_jsxFileName"], [991, 30, 723, 15], [992, 8, 723, 15, "lineNumber"], [992, 18, 723, 15], [993, 8, 723, 15, "columnNumber"], [993, 20, 723, 15], [994, 6, 723, 15], [994, 13, 724, 13], [994, 14, 724, 14], [994, 29, 726, 6], [994, 33, 726, 6, "_jsxDevRuntime"], [994, 47, 726, 6], [994, 48, 726, 6, "jsxDEV"], [994, 54, 726, 6], [994, 56, 726, 7, "_Modal"], [994, 62, 726, 7], [994, 63, 726, 7, "default"], [994, 70, 726, 12], [995, 8, 727, 8, "visible"], [995, 15, 727, 15], [995, 17, 727, 17, "processingState"], [995, 32, 727, 32], [995, 37, 727, 37], [995, 44, 727, 45], [996, 8, 728, 8, "transparent"], [996, 19, 728, 19], [997, 8, 729, 8, "animationType"], [997, 21, 729, 21], [997, 23, 729, 22], [997, 29, 729, 28], [998, 8, 729, 28, "children"], [998, 16, 729, 28], [998, 31, 731, 8], [998, 35, 731, 8, "_jsxDevRuntime"], [998, 49, 731, 8], [998, 50, 731, 8, "jsxDEV"], [998, 56, 731, 8], [998, 58, 731, 9, "_View"], [998, 63, 731, 9], [998, 64, 731, 9, "default"], [998, 71, 731, 13], [999, 10, 731, 14, "style"], [999, 15, 731, 19], [999, 17, 731, 21, "styles"], [999, 23, 731, 27], [999, 24, 731, 28, "processingModal"], [999, 39, 731, 44], [1000, 10, 731, 44, "children"], [1000, 18, 731, 44], [1000, 33, 732, 10], [1000, 37, 732, 10, "_jsxDevRuntime"], [1000, 51, 732, 10], [1000, 52, 732, 10, "jsxDEV"], [1000, 58, 732, 10], [1000, 60, 732, 11, "_View"], [1000, 65, 732, 11], [1000, 66, 732, 11, "default"], [1000, 73, 732, 15], [1001, 12, 732, 16, "style"], [1001, 17, 732, 21], [1001, 19, 732, 23, "styles"], [1001, 25, 732, 29], [1001, 26, 732, 30, "errorContent"], [1001, 38, 732, 43], [1002, 12, 732, 43, "children"], [1002, 20, 732, 43], [1002, 36, 733, 12], [1002, 40, 733, 12, "_jsxDevRuntime"], [1002, 54, 733, 12], [1002, 55, 733, 12, "jsxDEV"], [1002, 61, 733, 12], [1002, 63, 733, 13, "_lucideReactNative"], [1002, 81, 733, 13], [1002, 82, 733, 13, "X"], [1002, 83, 733, 14], [1003, 14, 733, 15, "size"], [1003, 18, 733, 19], [1003, 20, 733, 21], [1003, 22, 733, 24], [1004, 14, 733, 25, "color"], [1004, 19, 733, 30], [1004, 21, 733, 31], [1005, 12, 733, 40], [1006, 14, 733, 40, "fileName"], [1006, 22, 733, 40], [1006, 24, 733, 40, "_jsxFileName"], [1006, 36, 733, 40], [1007, 14, 733, 40, "lineNumber"], [1007, 24, 733, 40], [1008, 14, 733, 40, "columnNumber"], [1008, 26, 733, 40], [1009, 12, 733, 40], [1009, 19, 733, 42], [1009, 20, 733, 43], [1009, 35, 734, 12], [1009, 39, 734, 12, "_jsxDevRuntime"], [1009, 53, 734, 12], [1009, 54, 734, 12, "jsxDEV"], [1009, 60, 734, 12], [1009, 62, 734, 13, "_Text"], [1009, 67, 734, 13], [1009, 68, 734, 13, "default"], [1009, 75, 734, 17], [1010, 14, 734, 18, "style"], [1010, 19, 734, 23], [1010, 21, 734, 25, "styles"], [1010, 27, 734, 31], [1010, 28, 734, 32, "errorTitle"], [1010, 38, 734, 43], [1011, 14, 734, 43, "children"], [1011, 22, 734, 43], [1011, 24, 734, 44], [1012, 12, 734, 61], [1013, 14, 734, 61, "fileName"], [1013, 22, 734, 61], [1013, 24, 734, 61, "_jsxFileName"], [1013, 36, 734, 61], [1014, 14, 734, 61, "lineNumber"], [1014, 24, 734, 61], [1015, 14, 734, 61, "columnNumber"], [1015, 26, 734, 61], [1016, 12, 734, 61], [1016, 19, 734, 67], [1016, 20, 734, 68], [1016, 35, 735, 12], [1016, 39, 735, 12, "_jsxDevRuntime"], [1016, 53, 735, 12], [1016, 54, 735, 12, "jsxDEV"], [1016, 60, 735, 12], [1016, 62, 735, 13, "_Text"], [1016, 67, 735, 13], [1016, 68, 735, 13, "default"], [1016, 75, 735, 17], [1017, 14, 735, 18, "style"], [1017, 19, 735, 23], [1017, 21, 735, 25, "styles"], [1017, 27, 735, 31], [1017, 28, 735, 32, "errorMessage"], [1017, 40, 735, 45], [1018, 14, 735, 45, "children"], [1018, 22, 735, 45], [1018, 24, 735, 47, "errorMessage"], [1019, 12, 735, 59], [1020, 14, 735, 59, "fileName"], [1020, 22, 735, 59], [1020, 24, 735, 59, "_jsxFileName"], [1020, 36, 735, 59], [1021, 14, 735, 59, "lineNumber"], [1021, 24, 735, 59], [1022, 14, 735, 59, "columnNumber"], [1022, 26, 735, 59], [1023, 12, 735, 59], [1023, 19, 735, 66], [1023, 20, 735, 67], [1023, 35, 736, 12], [1023, 39, 736, 12, "_jsxDevRuntime"], [1023, 53, 736, 12], [1023, 54, 736, 12, "jsxDEV"], [1023, 60, 736, 12], [1023, 62, 736, 13, "_TouchableOpacity"], [1023, 79, 736, 13], [1023, 80, 736, 13, "default"], [1023, 87, 736, 29], [1024, 14, 737, 14, "onPress"], [1024, 21, 737, 21], [1024, 23, 737, 23, "retryCapture"], [1024, 35, 737, 36], [1025, 14, 738, 14, "style"], [1025, 19, 738, 19], [1025, 21, 738, 21, "styles"], [1025, 27, 738, 27], [1025, 28, 738, 28, "primaryButton"], [1025, 41, 738, 42], [1026, 14, 738, 42, "children"], [1026, 22, 738, 42], [1026, 37, 740, 14], [1026, 41, 740, 14, "_jsxDevRuntime"], [1026, 55, 740, 14], [1026, 56, 740, 14, "jsxDEV"], [1026, 62, 740, 14], [1026, 64, 740, 15, "_Text"], [1026, 69, 740, 15], [1026, 70, 740, 15, "default"], [1026, 77, 740, 19], [1027, 16, 740, 20, "style"], [1027, 21, 740, 25], [1027, 23, 740, 27, "styles"], [1027, 29, 740, 33], [1027, 30, 740, 34, "primaryButtonText"], [1027, 47, 740, 52], [1028, 16, 740, 52, "children"], [1028, 24, 740, 52], [1028, 26, 740, 53], [1029, 14, 740, 62], [1030, 16, 740, 62, "fileName"], [1030, 24, 740, 62], [1030, 26, 740, 62, "_jsxFileName"], [1030, 38, 740, 62], [1031, 16, 740, 62, "lineNumber"], [1031, 26, 740, 62], [1032, 16, 740, 62, "columnNumber"], [1032, 28, 740, 62], [1033, 14, 740, 62], [1033, 21, 740, 68], [1034, 12, 740, 69], [1035, 14, 740, 69, "fileName"], [1035, 22, 740, 69], [1035, 24, 740, 69, "_jsxFileName"], [1035, 36, 740, 69], [1036, 14, 740, 69, "lineNumber"], [1036, 24, 740, 69], [1037, 14, 740, 69, "columnNumber"], [1037, 26, 740, 69], [1038, 12, 740, 69], [1038, 19, 741, 30], [1038, 20, 741, 31], [1038, 35, 742, 12], [1038, 39, 742, 12, "_jsxDevRuntime"], [1038, 53, 742, 12], [1038, 54, 742, 12, "jsxDEV"], [1038, 60, 742, 12], [1038, 62, 742, 13, "_TouchableOpacity"], [1038, 79, 742, 13], [1038, 80, 742, 13, "default"], [1038, 87, 742, 29], [1039, 14, 743, 14, "onPress"], [1039, 21, 743, 21], [1039, 23, 743, 23, "onCancel"], [1039, 31, 743, 32], [1040, 14, 744, 14, "style"], [1040, 19, 744, 19], [1040, 21, 744, 21, "styles"], [1040, 27, 744, 27], [1040, 28, 744, 28, "secondaryButton"], [1040, 43, 744, 44], [1041, 14, 744, 44, "children"], [1041, 22, 744, 44], [1041, 37, 746, 14], [1041, 41, 746, 14, "_jsxDevRuntime"], [1041, 55, 746, 14], [1041, 56, 746, 14, "jsxDEV"], [1041, 62, 746, 14], [1041, 64, 746, 15, "_Text"], [1041, 69, 746, 15], [1041, 70, 746, 15, "default"], [1041, 77, 746, 19], [1042, 16, 746, 20, "style"], [1042, 21, 746, 25], [1042, 23, 746, 27, "styles"], [1042, 29, 746, 33], [1042, 30, 746, 34, "secondaryButtonText"], [1042, 49, 746, 54], [1043, 16, 746, 54, "children"], [1043, 24, 746, 54], [1043, 26, 746, 55], [1044, 14, 746, 61], [1045, 16, 746, 61, "fileName"], [1045, 24, 746, 61], [1045, 26, 746, 61, "_jsxFileName"], [1045, 38, 746, 61], [1046, 16, 746, 61, "lineNumber"], [1046, 26, 746, 61], [1047, 16, 746, 61, "columnNumber"], [1047, 28, 746, 61], [1048, 14, 746, 61], [1048, 21, 746, 67], [1049, 12, 746, 68], [1050, 14, 746, 68, "fileName"], [1050, 22, 746, 68], [1050, 24, 746, 68, "_jsxFileName"], [1050, 36, 746, 68], [1051, 14, 746, 68, "lineNumber"], [1051, 24, 746, 68], [1052, 14, 746, 68, "columnNumber"], [1052, 26, 746, 68], [1053, 12, 746, 68], [1053, 19, 747, 30], [1053, 20, 747, 31], [1054, 10, 747, 31], [1055, 12, 747, 31, "fileName"], [1055, 20, 747, 31], [1055, 22, 747, 31, "_jsxFileName"], [1055, 34, 747, 31], [1056, 12, 747, 31, "lineNumber"], [1056, 22, 747, 31], [1057, 12, 747, 31, "columnNumber"], [1057, 24, 747, 31], [1058, 10, 747, 31], [1058, 17, 748, 16], [1059, 8, 748, 17], [1060, 10, 748, 17, "fileName"], [1060, 18, 748, 17], [1060, 20, 748, 17, "_jsxFileName"], [1060, 32, 748, 17], [1061, 10, 748, 17, "lineNumber"], [1061, 20, 748, 17], [1062, 10, 748, 17, "columnNumber"], [1062, 22, 748, 17], [1063, 8, 748, 17], [1063, 15, 749, 14], [1064, 6, 749, 15], [1065, 8, 749, 15, "fileName"], [1065, 16, 749, 15], [1065, 18, 749, 15, "_jsxFileName"], [1065, 30, 749, 15], [1066, 8, 749, 15, "lineNumber"], [1066, 18, 749, 15], [1067, 8, 749, 15, "columnNumber"], [1067, 20, 749, 15], [1068, 6, 749, 15], [1068, 13, 750, 13], [1068, 14, 750, 14], [1069, 4, 750, 14], [1070, 6, 750, 14, "fileName"], [1070, 14, 750, 14], [1070, 16, 750, 14, "_jsxFileName"], [1070, 28, 750, 14], [1071, 6, 750, 14, "lineNumber"], [1071, 16, 750, 14], [1072, 6, 750, 14, "columnNumber"], [1072, 18, 750, 14], [1073, 4, 750, 14], [1073, 11, 751, 10], [1073, 12, 751, 11], [1074, 2, 753, 0], [1075, 2, 753, 1, "_s"], [1075, 4, 753, 1], [1075, 5, 51, 24, "EchoCameraWeb"], [1075, 18, 51, 37], [1076, 4, 51, 37], [1076, 12, 58, 42, "useCameraPermissions"], [1076, 44, 58, 62], [1076, 46, 72, 19, "useUpload"], [1076, 64, 72, 28], [1077, 2, 72, 28], [1078, 2, 72, 28, "_c"], [1078, 4, 72, 28], [1078, 7, 51, 24, "EchoCameraWeb"], [1078, 20, 51, 37], [1079, 2, 754, 0], [1079, 8, 754, 6, "styles"], [1079, 14, 754, 12], [1079, 17, 754, 15, "StyleSheet"], [1079, 36, 754, 25], [1079, 37, 754, 26, "create"], [1079, 43, 754, 32], [1079, 44, 754, 33], [1080, 4, 755, 2, "container"], [1080, 13, 755, 11], [1080, 15, 755, 13], [1081, 6, 756, 4, "flex"], [1081, 10, 756, 8], [1081, 12, 756, 10], [1081, 13, 756, 11], [1082, 6, 757, 4, "backgroundColor"], [1082, 21, 757, 19], [1082, 23, 757, 21], [1083, 4, 758, 2], [1083, 5, 758, 3], [1084, 4, 759, 2, "cameraContainer"], [1084, 19, 759, 17], [1084, 21, 759, 19], [1085, 6, 760, 4, "flex"], [1085, 10, 760, 8], [1085, 12, 760, 10], [1085, 13, 760, 11], [1086, 6, 761, 4, "max<PERSON><PERSON><PERSON>"], [1086, 14, 761, 12], [1086, 16, 761, 14], [1086, 19, 761, 17], [1087, 6, 762, 4, "alignSelf"], [1087, 15, 762, 13], [1087, 17, 762, 15], [1087, 25, 762, 23], [1088, 6, 763, 4, "width"], [1088, 11, 763, 9], [1088, 13, 763, 11], [1089, 4, 764, 2], [1089, 5, 764, 3], [1090, 4, 765, 2, "camera"], [1090, 10, 765, 8], [1090, 12, 765, 10], [1091, 6, 766, 4, "flex"], [1091, 10, 766, 8], [1091, 12, 766, 10], [1092, 4, 767, 2], [1092, 5, 767, 3], [1093, 4, 768, 2, "headerOverlay"], [1093, 17, 768, 15], [1093, 19, 768, 17], [1094, 6, 769, 4, "position"], [1094, 14, 769, 12], [1094, 16, 769, 14], [1094, 26, 769, 24], [1095, 6, 770, 4, "top"], [1095, 9, 770, 7], [1095, 11, 770, 9], [1095, 12, 770, 10], [1096, 6, 771, 4, "left"], [1096, 10, 771, 8], [1096, 12, 771, 10], [1096, 13, 771, 11], [1097, 6, 772, 4, "right"], [1097, 11, 772, 9], [1097, 13, 772, 11], [1097, 14, 772, 12], [1098, 6, 773, 4, "backgroundColor"], [1098, 21, 773, 19], [1098, 23, 773, 21], [1098, 36, 773, 34], [1099, 6, 774, 4, "paddingTop"], [1099, 16, 774, 14], [1099, 18, 774, 16], [1099, 20, 774, 18], [1100, 6, 775, 4, "paddingHorizontal"], [1100, 23, 775, 21], [1100, 25, 775, 23], [1100, 27, 775, 25], [1101, 6, 776, 4, "paddingBottom"], [1101, 19, 776, 17], [1101, 21, 776, 19], [1102, 4, 777, 2], [1102, 5, 777, 3], [1103, 4, 778, 2, "headerContent"], [1103, 17, 778, 15], [1103, 19, 778, 17], [1104, 6, 779, 4, "flexDirection"], [1104, 19, 779, 17], [1104, 21, 779, 19], [1104, 26, 779, 24], [1105, 6, 780, 4, "justifyContent"], [1105, 20, 780, 18], [1105, 22, 780, 20], [1105, 37, 780, 35], [1106, 6, 781, 4, "alignItems"], [1106, 16, 781, 14], [1106, 18, 781, 16], [1107, 4, 782, 2], [1107, 5, 782, 3], [1108, 4, 783, 2, "headerLeft"], [1108, 14, 783, 12], [1108, 16, 783, 14], [1109, 6, 784, 4, "flex"], [1109, 10, 784, 8], [1109, 12, 784, 10], [1110, 4, 785, 2], [1110, 5, 785, 3], [1111, 4, 786, 2, "headerTitle"], [1111, 15, 786, 13], [1111, 17, 786, 15], [1112, 6, 787, 4, "fontSize"], [1112, 14, 787, 12], [1112, 16, 787, 14], [1112, 18, 787, 16], [1113, 6, 788, 4, "fontWeight"], [1113, 16, 788, 14], [1113, 18, 788, 16], [1113, 23, 788, 21], [1114, 6, 789, 4, "color"], [1114, 11, 789, 9], [1114, 13, 789, 11], [1114, 19, 789, 17], [1115, 6, 790, 4, "marginBottom"], [1115, 18, 790, 16], [1115, 20, 790, 18], [1116, 4, 791, 2], [1116, 5, 791, 3], [1117, 4, 792, 2, "subtitleRow"], [1117, 15, 792, 13], [1117, 17, 792, 15], [1118, 6, 793, 4, "flexDirection"], [1118, 19, 793, 17], [1118, 21, 793, 19], [1118, 26, 793, 24], [1119, 6, 794, 4, "alignItems"], [1119, 16, 794, 14], [1119, 18, 794, 16], [1119, 26, 794, 24], [1120, 6, 795, 4, "marginBottom"], [1120, 18, 795, 16], [1120, 20, 795, 18], [1121, 4, 796, 2], [1121, 5, 796, 3], [1122, 4, 797, 2, "webIcon"], [1122, 11, 797, 9], [1122, 13, 797, 11], [1123, 6, 798, 4, "fontSize"], [1123, 14, 798, 12], [1123, 16, 798, 14], [1123, 18, 798, 16], [1124, 6, 799, 4, "marginRight"], [1124, 17, 799, 15], [1124, 19, 799, 17], [1125, 4, 800, 2], [1125, 5, 800, 3], [1126, 4, 801, 2, "headerSubtitle"], [1126, 18, 801, 16], [1126, 20, 801, 18], [1127, 6, 802, 4, "fontSize"], [1127, 14, 802, 12], [1127, 16, 802, 14], [1127, 18, 802, 16], [1128, 6, 803, 4, "color"], [1128, 11, 803, 9], [1128, 13, 803, 11], [1128, 19, 803, 17], [1129, 6, 804, 4, "opacity"], [1129, 13, 804, 11], [1129, 15, 804, 13], [1130, 4, 805, 2], [1130, 5, 805, 3], [1131, 4, 806, 2, "challengeRow"], [1131, 16, 806, 14], [1131, 18, 806, 16], [1132, 6, 807, 4, "flexDirection"], [1132, 19, 807, 17], [1132, 21, 807, 19], [1132, 26, 807, 24], [1133, 6, 808, 4, "alignItems"], [1133, 16, 808, 14], [1133, 18, 808, 16], [1134, 4, 809, 2], [1134, 5, 809, 3], [1135, 4, 810, 2, "challengeCode"], [1135, 17, 810, 15], [1135, 19, 810, 17], [1136, 6, 811, 4, "fontSize"], [1136, 14, 811, 12], [1136, 16, 811, 14], [1136, 18, 811, 16], [1137, 6, 812, 4, "color"], [1137, 11, 812, 9], [1137, 13, 812, 11], [1137, 19, 812, 17], [1138, 6, 813, 4, "marginLeft"], [1138, 16, 813, 14], [1138, 18, 813, 16], [1138, 19, 813, 17], [1139, 6, 814, 4, "fontFamily"], [1139, 16, 814, 14], [1139, 18, 814, 16], [1140, 4, 815, 2], [1140, 5, 815, 3], [1141, 4, 816, 2, "closeButton"], [1141, 15, 816, 13], [1141, 17, 816, 15], [1142, 6, 817, 4, "padding"], [1142, 13, 817, 11], [1142, 15, 817, 13], [1143, 4, 818, 2], [1143, 5, 818, 3], [1144, 4, 819, 2, "privacyNotice"], [1144, 17, 819, 15], [1144, 19, 819, 17], [1145, 6, 820, 4, "position"], [1145, 14, 820, 12], [1145, 16, 820, 14], [1145, 26, 820, 24], [1146, 6, 821, 4, "top"], [1146, 9, 821, 7], [1146, 11, 821, 9], [1146, 14, 821, 12], [1147, 6, 822, 4, "left"], [1147, 10, 822, 8], [1147, 12, 822, 10], [1147, 14, 822, 12], [1148, 6, 823, 4, "right"], [1148, 11, 823, 9], [1148, 13, 823, 11], [1148, 15, 823, 13], [1149, 6, 824, 4, "backgroundColor"], [1149, 21, 824, 19], [1149, 23, 824, 21], [1149, 48, 824, 46], [1150, 6, 825, 4, "borderRadius"], [1150, 18, 825, 16], [1150, 20, 825, 18], [1150, 21, 825, 19], [1151, 6, 826, 4, "padding"], [1151, 13, 826, 11], [1151, 15, 826, 13], [1151, 17, 826, 15], [1152, 6, 827, 4, "flexDirection"], [1152, 19, 827, 17], [1152, 21, 827, 19], [1152, 26, 827, 24], [1153, 6, 828, 4, "alignItems"], [1153, 16, 828, 14], [1153, 18, 828, 16], [1154, 4, 829, 2], [1154, 5, 829, 3], [1155, 4, 830, 2, "privacyText"], [1155, 15, 830, 13], [1155, 17, 830, 15], [1156, 6, 831, 4, "color"], [1156, 11, 831, 9], [1156, 13, 831, 11], [1156, 19, 831, 17], [1157, 6, 832, 4, "fontSize"], [1157, 14, 832, 12], [1157, 16, 832, 14], [1157, 18, 832, 16], [1158, 6, 833, 4, "marginLeft"], [1158, 16, 833, 14], [1158, 18, 833, 16], [1158, 19, 833, 17], [1159, 6, 834, 4, "flex"], [1159, 10, 834, 8], [1159, 12, 834, 10], [1160, 4, 835, 2], [1160, 5, 835, 3], [1161, 4, 836, 2, "footer<PERSON><PERSON><PERSON>"], [1161, 17, 836, 15], [1161, 19, 836, 17], [1162, 6, 837, 4, "position"], [1162, 14, 837, 12], [1162, 16, 837, 14], [1162, 26, 837, 24], [1163, 6, 838, 4, "bottom"], [1163, 12, 838, 10], [1163, 14, 838, 12], [1163, 15, 838, 13], [1164, 6, 839, 4, "left"], [1164, 10, 839, 8], [1164, 12, 839, 10], [1164, 13, 839, 11], [1165, 6, 840, 4, "right"], [1165, 11, 840, 9], [1165, 13, 840, 11], [1165, 14, 840, 12], [1166, 6, 841, 4, "backgroundColor"], [1166, 21, 841, 19], [1166, 23, 841, 21], [1166, 36, 841, 34], [1167, 6, 842, 4, "paddingBottom"], [1167, 19, 842, 17], [1167, 21, 842, 19], [1167, 23, 842, 21], [1168, 6, 843, 4, "paddingTop"], [1168, 16, 843, 14], [1168, 18, 843, 16], [1168, 20, 843, 18], [1169, 6, 844, 4, "alignItems"], [1169, 16, 844, 14], [1169, 18, 844, 16], [1170, 4, 845, 2], [1170, 5, 845, 3], [1171, 4, 846, 2, "instruction"], [1171, 15, 846, 13], [1171, 17, 846, 15], [1172, 6, 847, 4, "fontSize"], [1172, 14, 847, 12], [1172, 16, 847, 14], [1172, 18, 847, 16], [1173, 6, 848, 4, "color"], [1173, 11, 848, 9], [1173, 13, 848, 11], [1173, 19, 848, 17], [1174, 6, 849, 4, "marginBottom"], [1174, 18, 849, 16], [1174, 20, 849, 18], [1175, 4, 850, 2], [1175, 5, 850, 3], [1176, 4, 851, 2, "shutterButton"], [1176, 17, 851, 15], [1176, 19, 851, 17], [1177, 6, 852, 4, "width"], [1177, 11, 852, 9], [1177, 13, 852, 11], [1177, 15, 852, 13], [1178, 6, 853, 4, "height"], [1178, 12, 853, 10], [1178, 14, 853, 12], [1178, 16, 853, 14], [1179, 6, 854, 4, "borderRadius"], [1179, 18, 854, 16], [1179, 20, 854, 18], [1179, 22, 854, 20], [1180, 6, 855, 4, "backgroundColor"], [1180, 21, 855, 19], [1180, 23, 855, 21], [1180, 29, 855, 27], [1181, 6, 856, 4, "justifyContent"], [1181, 20, 856, 18], [1181, 22, 856, 20], [1181, 30, 856, 28], [1182, 6, 857, 4, "alignItems"], [1182, 16, 857, 14], [1182, 18, 857, 16], [1182, 26, 857, 24], [1183, 6, 858, 4, "marginBottom"], [1183, 18, 858, 16], [1183, 20, 858, 18], [1183, 22, 858, 20], [1184, 6, 859, 4], [1184, 9, 859, 7, "Platform"], [1184, 26, 859, 15], [1184, 27, 859, 16, "select"], [1184, 33, 859, 22], [1184, 34, 859, 23], [1185, 8, 860, 6, "ios"], [1185, 11, 860, 9], [1185, 13, 860, 11], [1186, 10, 861, 8, "shadowColor"], [1186, 21, 861, 19], [1186, 23, 861, 21], [1186, 32, 861, 30], [1187, 10, 862, 8, "shadowOffset"], [1187, 22, 862, 20], [1187, 24, 862, 22], [1188, 12, 862, 24, "width"], [1188, 17, 862, 29], [1188, 19, 862, 31], [1188, 20, 862, 32], [1189, 12, 862, 34, "height"], [1189, 18, 862, 40], [1189, 20, 862, 42], [1190, 10, 862, 44], [1190, 11, 862, 45], [1191, 10, 863, 8, "shadowOpacity"], [1191, 23, 863, 21], [1191, 25, 863, 23], [1191, 28, 863, 26], [1192, 10, 864, 8, "shadowRadius"], [1192, 22, 864, 20], [1192, 24, 864, 22], [1193, 8, 865, 6], [1193, 9, 865, 7], [1194, 8, 866, 6, "android"], [1194, 15, 866, 13], [1194, 17, 866, 15], [1195, 10, 867, 8, "elevation"], [1195, 19, 867, 17], [1195, 21, 867, 19], [1196, 8, 868, 6], [1196, 9, 868, 7], [1197, 8, 869, 6, "web"], [1197, 11, 869, 9], [1197, 13, 869, 11], [1198, 10, 870, 8, "boxShadow"], [1198, 19, 870, 17], [1198, 21, 870, 19], [1199, 8, 871, 6], [1200, 6, 872, 4], [1200, 7, 872, 5], [1201, 4, 873, 2], [1201, 5, 873, 3], [1202, 4, 874, 2, "shutterButtonDisabled"], [1202, 25, 874, 23], [1202, 27, 874, 25], [1203, 6, 875, 4, "opacity"], [1203, 13, 875, 11], [1203, 15, 875, 13], [1204, 4, 876, 2], [1204, 5, 876, 3], [1205, 4, 877, 2, "shutterInner"], [1205, 16, 877, 14], [1205, 18, 877, 16], [1206, 6, 878, 4, "width"], [1206, 11, 878, 9], [1206, 13, 878, 11], [1206, 15, 878, 13], [1207, 6, 879, 4, "height"], [1207, 12, 879, 10], [1207, 14, 879, 12], [1207, 16, 879, 14], [1208, 6, 880, 4, "borderRadius"], [1208, 18, 880, 16], [1208, 20, 880, 18], [1208, 22, 880, 20], [1209, 6, 881, 4, "backgroundColor"], [1209, 21, 881, 19], [1209, 23, 881, 21], [1209, 29, 881, 27], [1210, 6, 882, 4, "borderWidth"], [1210, 17, 882, 15], [1210, 19, 882, 17], [1210, 20, 882, 18], [1211, 6, 883, 4, "borderColor"], [1211, 17, 883, 15], [1211, 19, 883, 17], [1212, 4, 884, 2], [1212, 5, 884, 3], [1213, 4, 885, 2, "privacyNote"], [1213, 15, 885, 13], [1213, 17, 885, 15], [1214, 6, 886, 4, "fontSize"], [1214, 14, 886, 12], [1214, 16, 886, 14], [1214, 18, 886, 16], [1215, 6, 887, 4, "color"], [1215, 11, 887, 9], [1215, 13, 887, 11], [1216, 4, 888, 2], [1216, 5, 888, 3], [1217, 4, 889, 2, "processingModal"], [1217, 19, 889, 17], [1217, 21, 889, 19], [1218, 6, 890, 4, "flex"], [1218, 10, 890, 8], [1218, 12, 890, 10], [1218, 13, 890, 11], [1219, 6, 891, 4, "backgroundColor"], [1219, 21, 891, 19], [1219, 23, 891, 21], [1219, 43, 891, 41], [1220, 6, 892, 4, "justifyContent"], [1220, 20, 892, 18], [1220, 22, 892, 20], [1220, 30, 892, 28], [1221, 6, 893, 4, "alignItems"], [1221, 16, 893, 14], [1221, 18, 893, 16], [1222, 4, 894, 2], [1222, 5, 894, 3], [1223, 4, 895, 2, "processingContent"], [1223, 21, 895, 19], [1223, 23, 895, 21], [1224, 6, 896, 4, "backgroundColor"], [1224, 21, 896, 19], [1224, 23, 896, 21], [1224, 29, 896, 27], [1225, 6, 897, 4, "borderRadius"], [1225, 18, 897, 16], [1225, 20, 897, 18], [1225, 22, 897, 20], [1226, 6, 898, 4, "padding"], [1226, 13, 898, 11], [1226, 15, 898, 13], [1226, 17, 898, 15], [1227, 6, 899, 4, "width"], [1227, 11, 899, 9], [1227, 13, 899, 11], [1227, 18, 899, 16], [1228, 6, 900, 4, "max<PERSON><PERSON><PERSON>"], [1228, 14, 900, 12], [1228, 16, 900, 14], [1228, 19, 900, 17], [1229, 6, 901, 4, "alignItems"], [1229, 16, 901, 14], [1229, 18, 901, 16], [1230, 4, 902, 2], [1230, 5, 902, 3], [1231, 4, 903, 2, "processingTitle"], [1231, 19, 903, 17], [1231, 21, 903, 19], [1232, 6, 904, 4, "fontSize"], [1232, 14, 904, 12], [1232, 16, 904, 14], [1232, 18, 904, 16], [1233, 6, 905, 4, "fontWeight"], [1233, 16, 905, 14], [1233, 18, 905, 16], [1233, 23, 905, 21], [1234, 6, 906, 4, "color"], [1234, 11, 906, 9], [1234, 13, 906, 11], [1234, 22, 906, 20], [1235, 6, 907, 4, "marginTop"], [1235, 15, 907, 13], [1235, 17, 907, 15], [1235, 19, 907, 17], [1236, 6, 908, 4, "marginBottom"], [1236, 18, 908, 16], [1236, 20, 908, 18], [1237, 4, 909, 2], [1237, 5, 909, 3], [1238, 4, 910, 2, "progressBar"], [1238, 15, 910, 13], [1238, 17, 910, 15], [1239, 6, 911, 4, "width"], [1239, 11, 911, 9], [1239, 13, 911, 11], [1239, 19, 911, 17], [1240, 6, 912, 4, "height"], [1240, 12, 912, 10], [1240, 14, 912, 12], [1240, 15, 912, 13], [1241, 6, 913, 4, "backgroundColor"], [1241, 21, 913, 19], [1241, 23, 913, 21], [1241, 32, 913, 30], [1242, 6, 914, 4, "borderRadius"], [1242, 18, 914, 16], [1242, 20, 914, 18], [1242, 21, 914, 19], [1243, 6, 915, 4, "overflow"], [1243, 14, 915, 12], [1243, 16, 915, 14], [1243, 24, 915, 22], [1244, 6, 916, 4, "marginBottom"], [1244, 18, 916, 16], [1244, 20, 916, 18], [1245, 4, 917, 2], [1245, 5, 917, 3], [1246, 4, 918, 2, "progressFill"], [1246, 16, 918, 14], [1246, 18, 918, 16], [1247, 6, 919, 4, "height"], [1247, 12, 919, 10], [1247, 14, 919, 12], [1247, 20, 919, 18], [1248, 6, 920, 4, "backgroundColor"], [1248, 21, 920, 19], [1248, 23, 920, 21], [1248, 32, 920, 30], [1249, 6, 921, 4, "borderRadius"], [1249, 18, 921, 16], [1249, 20, 921, 18], [1250, 4, 922, 2], [1250, 5, 922, 3], [1251, 4, 923, 2, "processingDescription"], [1251, 25, 923, 23], [1251, 27, 923, 25], [1252, 6, 924, 4, "fontSize"], [1252, 14, 924, 12], [1252, 16, 924, 14], [1252, 18, 924, 16], [1253, 6, 925, 4, "color"], [1253, 11, 925, 9], [1253, 13, 925, 11], [1253, 22, 925, 20], [1254, 6, 926, 4, "textAlign"], [1254, 15, 926, 13], [1254, 17, 926, 15], [1255, 4, 927, 2], [1255, 5, 927, 3], [1256, 4, 928, 2, "successIcon"], [1256, 15, 928, 13], [1256, 17, 928, 15], [1257, 6, 929, 4, "marginTop"], [1257, 15, 929, 13], [1257, 17, 929, 15], [1258, 4, 930, 2], [1258, 5, 930, 3], [1259, 4, 931, 2, "errorContent"], [1259, 16, 931, 14], [1259, 18, 931, 16], [1260, 6, 932, 4, "backgroundColor"], [1260, 21, 932, 19], [1260, 23, 932, 21], [1260, 29, 932, 27], [1261, 6, 933, 4, "borderRadius"], [1261, 18, 933, 16], [1261, 20, 933, 18], [1261, 22, 933, 20], [1262, 6, 934, 4, "padding"], [1262, 13, 934, 11], [1262, 15, 934, 13], [1262, 17, 934, 15], [1263, 6, 935, 4, "width"], [1263, 11, 935, 9], [1263, 13, 935, 11], [1263, 18, 935, 16], [1264, 6, 936, 4, "max<PERSON><PERSON><PERSON>"], [1264, 14, 936, 12], [1264, 16, 936, 14], [1264, 19, 936, 17], [1265, 6, 937, 4, "alignItems"], [1265, 16, 937, 14], [1265, 18, 937, 16], [1266, 4, 938, 2], [1266, 5, 938, 3], [1267, 4, 939, 2, "errorTitle"], [1267, 14, 939, 12], [1267, 16, 939, 14], [1268, 6, 940, 4, "fontSize"], [1268, 14, 940, 12], [1268, 16, 940, 14], [1268, 18, 940, 16], [1269, 6, 941, 4, "fontWeight"], [1269, 16, 941, 14], [1269, 18, 941, 16], [1269, 23, 941, 21], [1270, 6, 942, 4, "color"], [1270, 11, 942, 9], [1270, 13, 942, 11], [1270, 22, 942, 20], [1271, 6, 943, 4, "marginTop"], [1271, 15, 943, 13], [1271, 17, 943, 15], [1271, 19, 943, 17], [1272, 6, 944, 4, "marginBottom"], [1272, 18, 944, 16], [1272, 20, 944, 18], [1273, 4, 945, 2], [1273, 5, 945, 3], [1274, 4, 946, 2, "errorMessage"], [1274, 16, 946, 14], [1274, 18, 946, 16], [1275, 6, 947, 4, "fontSize"], [1275, 14, 947, 12], [1275, 16, 947, 14], [1275, 18, 947, 16], [1276, 6, 948, 4, "color"], [1276, 11, 948, 9], [1276, 13, 948, 11], [1276, 22, 948, 20], [1277, 6, 949, 4, "textAlign"], [1277, 15, 949, 13], [1277, 17, 949, 15], [1277, 25, 949, 23], [1278, 6, 950, 4, "marginBottom"], [1278, 18, 950, 16], [1278, 20, 950, 18], [1279, 4, 951, 2], [1279, 5, 951, 3], [1280, 4, 952, 2, "primaryButton"], [1280, 17, 952, 15], [1280, 19, 952, 17], [1281, 6, 953, 4, "backgroundColor"], [1281, 21, 953, 19], [1281, 23, 953, 21], [1281, 32, 953, 30], [1282, 6, 954, 4, "paddingHorizontal"], [1282, 23, 954, 21], [1282, 25, 954, 23], [1282, 27, 954, 25], [1283, 6, 955, 4, "paddingVertical"], [1283, 21, 955, 19], [1283, 23, 955, 21], [1283, 25, 955, 23], [1284, 6, 956, 4, "borderRadius"], [1284, 18, 956, 16], [1284, 20, 956, 18], [1284, 21, 956, 19], [1285, 6, 957, 4, "marginTop"], [1285, 15, 957, 13], [1285, 17, 957, 15], [1286, 4, 958, 2], [1286, 5, 958, 3], [1287, 4, 959, 2, "primaryButtonText"], [1287, 21, 959, 19], [1287, 23, 959, 21], [1288, 6, 960, 4, "color"], [1288, 11, 960, 9], [1288, 13, 960, 11], [1288, 19, 960, 17], [1289, 6, 961, 4, "fontSize"], [1289, 14, 961, 12], [1289, 16, 961, 14], [1289, 18, 961, 16], [1290, 6, 962, 4, "fontWeight"], [1290, 16, 962, 14], [1290, 18, 962, 16], [1291, 4, 963, 2], [1291, 5, 963, 3], [1292, 4, 964, 2, "secondaryButton"], [1292, 19, 964, 17], [1292, 21, 964, 19], [1293, 6, 965, 4, "paddingHorizontal"], [1293, 23, 965, 21], [1293, 25, 965, 23], [1293, 27, 965, 25], [1294, 6, 966, 4, "paddingVertical"], [1294, 21, 966, 19], [1294, 23, 966, 21], [1294, 25, 966, 23], [1295, 6, 967, 4, "marginTop"], [1295, 15, 967, 13], [1295, 17, 967, 15], [1296, 4, 968, 2], [1296, 5, 968, 3], [1297, 4, 969, 2, "secondaryButtonText"], [1297, 23, 969, 21], [1297, 25, 969, 23], [1298, 6, 970, 4, "color"], [1298, 11, 970, 9], [1298, 13, 970, 11], [1298, 22, 970, 20], [1299, 6, 971, 4, "fontSize"], [1299, 14, 971, 12], [1299, 16, 971, 14], [1300, 4, 972, 2], [1300, 5, 972, 3], [1301, 4, 973, 2, "permissionContent"], [1301, 21, 973, 19], [1301, 23, 973, 21], [1302, 6, 974, 4, "flex"], [1302, 10, 974, 8], [1302, 12, 974, 10], [1302, 13, 974, 11], [1303, 6, 975, 4, "justifyContent"], [1303, 20, 975, 18], [1303, 22, 975, 20], [1303, 30, 975, 28], [1304, 6, 976, 4, "alignItems"], [1304, 16, 976, 14], [1304, 18, 976, 16], [1304, 26, 976, 24], [1305, 6, 977, 4, "padding"], [1305, 13, 977, 11], [1305, 15, 977, 13], [1306, 4, 978, 2], [1306, 5, 978, 3], [1307, 4, 979, 2, "permissionTitle"], [1307, 19, 979, 17], [1307, 21, 979, 19], [1308, 6, 980, 4, "fontSize"], [1308, 14, 980, 12], [1308, 16, 980, 14], [1308, 18, 980, 16], [1309, 6, 981, 4, "fontWeight"], [1309, 16, 981, 14], [1309, 18, 981, 16], [1309, 23, 981, 21], [1310, 6, 982, 4, "color"], [1310, 11, 982, 9], [1310, 13, 982, 11], [1310, 22, 982, 20], [1311, 6, 983, 4, "marginTop"], [1311, 15, 983, 13], [1311, 17, 983, 15], [1311, 19, 983, 17], [1312, 6, 984, 4, "marginBottom"], [1312, 18, 984, 16], [1312, 20, 984, 18], [1313, 4, 985, 2], [1313, 5, 985, 3], [1314, 4, 986, 2, "permissionDescription"], [1314, 25, 986, 23], [1314, 27, 986, 25], [1315, 6, 987, 4, "fontSize"], [1315, 14, 987, 12], [1315, 16, 987, 14], [1315, 18, 987, 16], [1316, 6, 988, 4, "color"], [1316, 11, 988, 9], [1316, 13, 988, 11], [1316, 22, 988, 20], [1317, 6, 989, 4, "textAlign"], [1317, 15, 989, 13], [1317, 17, 989, 15], [1317, 25, 989, 23], [1318, 6, 990, 4, "marginBottom"], [1318, 18, 990, 16], [1318, 20, 990, 18], [1319, 4, 991, 2], [1319, 5, 991, 3], [1320, 4, 992, 2, "loadingText"], [1320, 15, 992, 13], [1320, 17, 992, 15], [1321, 6, 993, 4, "color"], [1321, 11, 993, 9], [1321, 13, 993, 11], [1321, 22, 993, 20], [1322, 6, 994, 4, "marginTop"], [1322, 15, 994, 13], [1322, 17, 994, 15], [1323, 4, 995, 2], [1323, 5, 995, 3], [1324, 4, 996, 2], [1325, 4, 997, 2, "blurZone"], [1325, 12, 997, 10], [1325, 14, 997, 12], [1326, 6, 998, 4, "position"], [1326, 14, 998, 12], [1326, 16, 998, 14], [1326, 26, 998, 24], [1327, 6, 999, 4, "overflow"], [1327, 14, 999, 12], [1327, 16, 999, 14], [1328, 4, 1000, 2], [1328, 5, 1000, 3], [1329, 4, 1001, 2, "previewChip"], [1329, 15, 1001, 13], [1329, 17, 1001, 15], [1330, 6, 1002, 4, "position"], [1330, 14, 1002, 12], [1330, 16, 1002, 14], [1330, 26, 1002, 24], [1331, 6, 1003, 4, "top"], [1331, 9, 1003, 7], [1331, 11, 1003, 9], [1331, 12, 1003, 10], [1332, 6, 1004, 4, "right"], [1332, 11, 1004, 9], [1332, 13, 1004, 11], [1332, 14, 1004, 12], [1333, 6, 1005, 4, "backgroundColor"], [1333, 21, 1005, 19], [1333, 23, 1005, 21], [1333, 40, 1005, 38], [1334, 6, 1006, 4, "paddingHorizontal"], [1334, 23, 1006, 21], [1334, 25, 1006, 23], [1334, 27, 1006, 25], [1335, 6, 1007, 4, "paddingVertical"], [1335, 21, 1007, 19], [1335, 23, 1007, 21], [1335, 24, 1007, 22], [1336, 6, 1008, 4, "borderRadius"], [1336, 18, 1008, 16], [1336, 20, 1008, 18], [1337, 4, 1009, 2], [1337, 5, 1009, 3], [1338, 4, 1010, 2, "previewChipText"], [1338, 19, 1010, 17], [1338, 21, 1010, 19], [1339, 6, 1011, 4, "color"], [1339, 11, 1011, 9], [1339, 13, 1011, 11], [1339, 19, 1011, 17], [1340, 6, 1012, 4, "fontSize"], [1340, 14, 1012, 12], [1340, 16, 1012, 14], [1340, 18, 1012, 16], [1341, 6, 1013, 4, "fontWeight"], [1341, 16, 1013, 14], [1341, 18, 1013, 16], [1342, 4, 1014, 2], [1343, 2, 1015, 0], [1343, 3, 1015, 1], [1343, 4, 1015, 2], [1344, 2, 1015, 3], [1344, 6, 1015, 3, "_c"], [1344, 8, 1015, 3], [1345, 2, 1015, 3, "$RefreshReg$"], [1345, 14, 1015, 3], [1345, 15, 1015, 3, "_c"], [1345, 17, 1015, 3], [1346, 0, 1015, 3], [1346, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "capturePhoto", "Promise$argument_0", "processImageWithFaceBlur", "browserDetections.map$argument_0", "faceDetections.map$argument_0", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;mCGE;wBCc,kCD;GHkC;mCKE;wBDY;OCI;gDC8B;YDO;8BDa;aCM;6CEc;YFO;oFGsB;UHM;8BIS;SJoD;uDDU;sBMC,wBN;OCC;GLe;6BWG;GXyB;kCYG;GZ8C;4BaE;mBCmD;SDE;GbO;uBeE;GfI;mCgBG;GhBM;YCE;GDK;oBiB2C;WjBG;yBkBC;WlBG;wBmBC;WnBI;CD4L"}}, "type": "js/module"}]}