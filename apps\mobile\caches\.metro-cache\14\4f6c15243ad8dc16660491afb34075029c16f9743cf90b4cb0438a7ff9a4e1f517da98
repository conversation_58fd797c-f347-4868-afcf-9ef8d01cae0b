{"dependencies": [{"name": "../Skia", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 31, "index": 31}}], "key": "5eRJ3Y/mp/EEiynYa3WwzXcSMXc=", "exportNames": ["*"]}}, {"name": "../types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 32}, "end": {"line": 2, "column": 34, "index": 66}}], "key": "SiqkZ9nARqNkdXfcIWbBgsKp5Yo=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.createPicture = void 0;\n  var _Skia = require(_dependencyMap[0], \"../Skia\");\n  var _types = require(_dependencyMap[1], \"../types\");\n  /**\n   * Memoizes and returns an SkPicture that can be drawn to another canvas.\n   * @param rect Picture bounds\n   * @param cb Callback for drawing to the canvas\n   * @returns SkPicture\n   */\n  const _worklet_2775832924769_init_data = {\n    code: \"function PictureJs1(cb,rect){const{Skia,isRect}=this.__closure;const recorder=Skia.PictureRecorder();let bounds;if(rect){bounds=isRect(rect)?rect:Skia.XYWHRect(0,0,rect.width,rect.height);}const canvas=recorder.beginRecording(bounds);cb(canvas);return recorder.finishRecordingAsPicture();}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\skia\\\\core\\\\Picture.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"PictureJs1\\\",\\\"cb\\\",\\\"rect\\\",\\\"Skia\\\",\\\"isRect\\\",\\\"__closure\\\",\\\"recorder\\\",\\\"PictureRecorder\\\",\\\"bounds\\\",\\\"XYWHRect\\\",\\\"width\\\",\\\"height\\\",\\\"canvas\\\",\\\"beginRecording\\\",\\\"finishRecordingAsPicture\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/skia/core/Picture.js\\\"],\\\"mappings\\\":\\\"AAS6B,QAAC,CAAAA,UAAQA,CAAAC,EAAK,CAAAC,IAAA,QAAAC,IAAA,CAAAC,MAAA,OAAAC,SAAA,CAGzC,KAAM,CAAAC,QAAQ,CAAGH,IAAI,CAACI,eAAe,CAAC,CAAC,CACvC,GAAI,CAAAC,MAAM,CACV,GAAIN,IAAI,CAAE,CACRM,MAAM,CAAGJ,MAAM,CAACF,IAAI,CAAC,CAAGA,IAAI,CAAGC,IAAI,CAACM,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAEP,IAAI,CAACQ,KAAK,CAAER,IAAI,CAACS,MAAM,CAAC,CAC7E,CACA,KAAM,CAAAC,MAAM,CAAGN,QAAQ,CAACO,cAAc,CAACL,MAAM,CAAC,CAC9CP,EAAE,CAACW,MAAM,CAAC,CACV,MAAO,CAAAN,QAAQ,CAACQ,wBAAwB,CAAC,CAAC,CAC5C\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const createPicture = exports.createPicture = function () {\n    const _e = [new global.Error(), -3, -27];\n    const PictureJs1 = function (cb, rect) {\n      const recorder = _Skia.Skia.PictureRecorder();\n      let bounds;\n      if (rect) {\n        bounds = (0, _types.isRect)(rect) ? rect : _Skia.Skia.XYWHRect(0, 0, rect.width, rect.height);\n      }\n      const canvas = recorder.beginRecording(bounds);\n      cb(canvas);\n      return recorder.finishRecordingAsPicture();\n    };\n    PictureJs1.__closure = {\n      Skia: _Skia.Skia,\n      isRect: _types.isRect\n    };\n    PictureJs1.__workletHash = 2775832924769;\n    PictureJs1.__initData = _worklet_2775832924769_init_data;\n    PictureJs1.__stackDetails = _e;\n    return PictureJs1;\n  }();\n});", "lineCount": 41, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Skia"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_types"], [7, 12, 2, 0], [7, 15, 2, 0, "require"], [7, 22, 2, 0], [7, 23, 2, 0, "_dependencyMap"], [7, 37, 2, 0], [8, 2, 4, 0], [9, 0, 5, 0], [10, 0, 6, 0], [11, 0, 7, 0], [12, 0, 8, 0], [13, 0, 9, 0], [14, 2, 4, 0], [14, 8, 4, 0, "_worklet_2775832924769_init_data"], [14, 40, 4, 0], [15, 4, 4, 0, "code"], [15, 8, 4, 0], [16, 4, 4, 0, "location"], [16, 12, 4, 0], [17, 4, 4, 0, "sourceMap"], [17, 13, 4, 0], [18, 4, 4, 0, "version"], [18, 11, 4, 0], [19, 2, 4, 0], [20, 2, 10, 7], [20, 8, 10, 13, "createPicture"], [20, 21, 10, 26], [20, 24, 10, 26, "exports"], [20, 31, 10, 26], [20, 32, 10, 26, "createPicture"], [20, 45, 10, 26], [20, 48, 10, 29], [21, 4, 10, 29], [21, 10, 10, 29, "_e"], [21, 12, 10, 29], [21, 20, 10, 29, "global"], [21, 26, 10, 29], [21, 27, 10, 29, "Error"], [21, 32, 10, 29], [22, 4, 10, 29], [22, 10, 10, 29, "PictureJs1"], [22, 20, 10, 29], [22, 32, 10, 29, "PictureJs1"], [22, 33, 10, 30, "cb"], [22, 35, 10, 32], [22, 37, 10, 34, "rect"], [22, 41, 10, 38], [22, 43, 10, 43], [23, 6, 13, 2], [23, 12, 13, 8, "recorder"], [23, 20, 13, 16], [23, 23, 13, 19, "Skia"], [23, 33, 13, 23], [23, 34, 13, 24, "PictureRecorder"], [23, 49, 13, 39], [23, 50, 13, 40], [23, 51, 13, 41], [24, 6, 14, 2], [24, 10, 14, 6, "bounds"], [24, 16, 14, 12], [25, 6, 15, 2], [25, 10, 15, 6, "rect"], [25, 14, 15, 10], [25, 16, 15, 12], [26, 8, 16, 4, "bounds"], [26, 14, 16, 10], [26, 17, 16, 13], [26, 21, 16, 13, "isRect"], [26, 34, 16, 19], [26, 36, 16, 20, "rect"], [26, 40, 16, 24], [26, 41, 16, 25], [26, 44, 16, 28, "rect"], [26, 48, 16, 32], [26, 51, 16, 35, "Skia"], [26, 61, 16, 39], [26, 62, 16, 40, "XYWHRect"], [26, 70, 16, 48], [26, 71, 16, 49], [26, 72, 16, 50], [26, 74, 16, 52], [26, 75, 16, 53], [26, 77, 16, 55, "rect"], [26, 81, 16, 59], [26, 82, 16, 60, "width"], [26, 87, 16, 65], [26, 89, 16, 67, "rect"], [26, 93, 16, 71], [26, 94, 16, 72, "height"], [26, 100, 16, 78], [26, 101, 16, 79], [27, 6, 17, 2], [28, 6, 18, 2], [28, 12, 18, 8, "canvas"], [28, 18, 18, 14], [28, 21, 18, 17, "recorder"], [28, 29, 18, 25], [28, 30, 18, 26, "beginRecording"], [28, 44, 18, 40], [28, 45, 18, 41, "bounds"], [28, 51, 18, 47], [28, 52, 18, 48], [29, 6, 19, 2, "cb"], [29, 8, 19, 4], [29, 9, 19, 5, "canvas"], [29, 15, 19, 11], [29, 16, 19, 12], [30, 6, 20, 2], [30, 13, 20, 9, "recorder"], [30, 21, 20, 17], [30, 22, 20, 18, "finishRecordingAsPicture"], [30, 46, 20, 42], [30, 47, 20, 43], [30, 48, 20, 44], [31, 4, 21, 0], [31, 5, 21, 1], [32, 4, 21, 1, "PictureJs1"], [32, 14, 21, 1], [32, 15, 21, 1, "__closure"], [32, 24, 21, 1], [33, 6, 21, 1, "Skia"], [33, 10, 21, 1], [33, 12, 13, 19, "Skia"], [33, 22, 13, 23], [34, 6, 13, 23, "isRect"], [34, 12, 13, 23], [34, 14, 16, 13, "isRect"], [35, 4, 16, 19], [36, 4, 16, 19, "PictureJs1"], [36, 14, 16, 19], [36, 15, 16, 19, "__workletHash"], [36, 28, 16, 19], [37, 4, 16, 19, "PictureJs1"], [37, 14, 16, 19], [37, 15, 16, 19, "__initData"], [37, 25, 16, 19], [37, 28, 16, 19, "_worklet_2775832924769_init_data"], [37, 60, 16, 19], [38, 4, 16, 19, "PictureJs1"], [38, 14, 16, 19], [38, 15, 16, 19, "__stackDetails"], [38, 29, 16, 19], [38, 32, 16, 19, "_e"], [38, 34, 16, 19], [39, 4, 16, 19], [39, 11, 16, 19, "PictureJs1"], [39, 21, 16, 19], [40, 2, 16, 19], [40, 3, 10, 29], [40, 5, 21, 1], [41, 0, 21, 2], [41, 3]], "functionMap": {"names": ["<global>", "createPicture"], "mappings": "AAA;6BCS;CDW"}}, "type": "js/module"}]}