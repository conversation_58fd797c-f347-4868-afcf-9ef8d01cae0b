{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        await processImageWithFaceBlur(photo.uri);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // Real face detection and blurring using browser APIs and CDN libraries\n    const processImageWithFaceBlur = async photoUri => {\n      try {\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        setProcessingProgress(60);\n\n        // Try multiple face detection approaches\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting face detection on image:', {\n          width: img.width,\n          height: img.height,\n          src: photoUri.substring(0, 50) + '...'\n        });\n\n        // Method 1: Try browser's native Face Detection API\n        try {\n          if ('FaceDetector' in window) {\n            console.log('[EchoCameraWeb] ✅ Browser Face Detection API available, attempting detection...');\n            const faceDetector = new window.FaceDetector({\n              maxDetectedFaces: 10,\n              fastMode: false\n            });\n            const browserDetections = await faceDetector.detect(img);\n            detectedFaces = browserDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.boundingBox.x + detection.boundingBox.width / 2) / img.width,\n                yCenter: (detection.boundingBox.y + detection.boundingBox.height / 2) / img.height,\n                width: detection.boundingBox.width / img.width,\n                height: detection.boundingBox.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] ✅ Browser Face Detection API found ${detectedFaces.length} faces`);\n          } else {\n            console.log('[EchoCameraWeb] ❌ Browser Face Detection API not available in this browser');\n            throw new Error('Browser Face Detection API not available');\n          }\n        } catch (browserError) {\n          console.warn('[EchoCameraWeb] ❌ Browser face detection failed, trying face-api.js from CDN:', browserError);\n\n          // Method 2: Try loading face-api.js from CDN\n          try {\n            // Load face-api.js from CDN if not already loaded\n            if (!window.faceapi) {\n              await new Promise((resolve, reject) => {\n                const script = document.createElement('script');\n                script.src = 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js';\n                script.onload = resolve;\n                script.onerror = reject;\n                document.head.appendChild(script);\n              });\n            }\n            const faceapi = window.faceapi;\n\n            // Load models from CDN\n            await Promise.all([faceapi.nets.tinyFaceDetector.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights'), faceapi.nets.faceLandmark68Net.loadFromUri('https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights')]);\n\n            // Detect faces\n            const faceDetections = await faceapi.detectAllFaces(img, new faceapi.TinyFaceDetectorOptions());\n            detectedFaces = faceDetections.map(detection => ({\n              boundingBox: {\n                xCenter: (detection.box.x + detection.box.width / 2) / img.width,\n                yCenter: (detection.box.y + detection.box.height / 2) / img.height,\n                width: detection.box.width / img.width,\n                height: detection.box.height / img.height\n              }\n            }));\n            console.log(`[EchoCameraWeb] ✅ face-api.js found ${detectedFaces.length} faces`);\n          } catch (faceApiError) {\n            console.warn('[EchoCameraWeb] ❌ face-api.js also failed:', faceApiError);\n\n            // Method 3: Fallback - Mock face detection for testing (assumes center face)\n            console.log('[EchoCameraWeb] 🧪 Using fallback mock face detection for testing...');\n            detectedFaces = [{\n              boundingBox: {\n                xCenter: 0.5,\n                // Center of image\n                yCenter: 0.4,\n                // Slightly above center (typical face position)\n                width: 0.3,\n                // 30% of image width\n                height: 0.4 // 40% of image height\n              }\n            }];\n            console.log(`[EchoCameraWeb] 🧪 Mock detection created 1 face at center of image`);\n          }\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ℹ️ No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(70);\n\n        // Apply blurring to each detected face\n        if (detectedFaces.length > 0) {\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n\n            // Convert normalized coordinates to pixel coordinates\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n\n            // Add some padding around the face\n            const padding = 0.2; // 20% padding\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎨 Blurring face ${index + 1} at (${Math.round(paddedX)}, ${Math.round(paddedY)}) size ${Math.round(paddedWidth)}x${Math.round(paddedHeight)}`);\n\n            // Get the face region image data\n            const faceImageData = ctx.getImageData(paddedX, paddedY, paddedWidth, paddedHeight);\n            const data = faceImageData.data;\n            console.log(`[EchoCameraWeb] 📊 Face region data: ${data.length} bytes, ${paddedWidth}x${paddedHeight} pixels`);\n\n            // Apply pixelation blur effect\n            const pixelSize = Math.max(12, Math.min(paddedWidth, paddedHeight) / 15); // Increased pixel size for more visible effect\n            console.log(`[EchoCameraWeb] 🔲 Using pixel size: ${pixelSize}px for blurring`);\n            for (let y = 0; y < paddedHeight; y += pixelSize) {\n              for (let x = 0; x < paddedWidth; x += pixelSize) {\n                // Get the color of the top-left pixel in this block\n                const pixelIndex = (y * paddedWidth + x) * 4;\n                const r = data[pixelIndex];\n                const g = data[pixelIndex + 1];\n                const b = data[pixelIndex + 2];\n                const a = data[pixelIndex + 3];\n\n                // Apply this color to the entire block\n                for (let dy = 0; dy < pixelSize && y + dy < paddedHeight; dy++) {\n                  for (let dx = 0; dx < pixelSize && x + dx < paddedWidth; dx++) {\n                    const blockPixelIndex = ((y + dy) * paddedWidth + (x + dx)) * 4;\n                    data[blockPixelIndex] = r;\n                    data[blockPixelIndex + 1] = g;\n                    data[blockPixelIndex + 2] = b;\n                    data[blockPixelIndex + 3] = a;\n                  }\n                }\n              }\n            }\n\n            // Put the blurred face region back on the canvas\n            ctx.putImageData(faceImageData, paddedX, paddedY);\n          });\n        } else {\n          console.log('[EchoCameraWeb] No faces detected - image will remain unmodified');\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] Processing complete:', result);\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 511,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 568,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 565,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 636,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 637,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 635,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 641,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 642,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 647,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 646,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 659,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 695,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 702,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 709,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 686,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 730,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 731,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 732,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 728,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 727,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 722,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 542,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1342, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [89, 4, 89, 2], [90, 4, 90, 2], [90, 10, 90, 8, "capturePhoto"], [90, 22, 90, 20], [90, 25, 90, 23], [90, 29, 90, 23, "useCallback"], [90, 47, 90, 34], [90, 49, 90, 35], [90, 61, 90, 47], [91, 6, 91, 4], [92, 6, 92, 4], [92, 12, 92, 10, "isDev"], [92, 17, 92, 15], [92, 20, 92, 18, "process"], [92, 27, 92, 25], [92, 28, 92, 26, "env"], [92, 31, 92, 29], [92, 32, 92, 30, "NODE_ENV"], [92, 40, 92, 38], [92, 45, 92, 43], [92, 58, 92, 56], [92, 62, 92, 60, "__DEV__"], [92, 69, 92, 67], [93, 6, 94, 4], [93, 10, 94, 8], [93, 11, 94, 9, "cameraRef"], [93, 20, 94, 18], [93, 21, 94, 19, "current"], [93, 28, 94, 26], [93, 32, 94, 30], [93, 33, 94, 31, "isDev"], [93, 38, 94, 36], [93, 40, 94, 38], [94, 8, 95, 6, "<PERSON><PERSON>"], [94, 22, 95, 11], [94, 23, 95, 12, "alert"], [94, 28, 95, 17], [94, 29, 95, 18], [94, 36, 95, 25], [94, 38, 95, 27], [94, 56, 95, 45], [94, 57, 95, 46], [95, 8, 96, 6], [96, 6, 97, 4], [97, 6, 98, 4], [97, 10, 98, 8], [98, 8, 99, 6, "setProcessingState"], [98, 26, 99, 24], [98, 27, 99, 25], [98, 38, 99, 36], [98, 39, 99, 37], [99, 8, 100, 6, "setProcessingProgress"], [99, 29, 100, 27], [99, 30, 100, 28], [99, 32, 100, 30], [99, 33, 100, 31], [100, 8, 101, 6], [101, 8, 102, 6], [102, 8, 103, 6], [103, 8, 104, 6], [103, 14, 104, 12], [103, 18, 104, 16, "Promise"], [103, 25, 104, 23], [103, 26, 104, 24, "resolve"], [103, 33, 104, 31], [103, 37, 104, 35, "setTimeout"], [103, 47, 104, 45], [103, 48, 104, 46, "resolve"], [103, 55, 104, 53], [103, 57, 104, 55], [103, 59, 104, 57], [103, 60, 104, 58], [103, 61, 104, 59], [104, 8, 105, 6], [105, 8, 106, 6], [105, 12, 106, 10, "photo"], [105, 17, 106, 15], [106, 8, 108, 6], [106, 12, 108, 10], [107, 10, 109, 8, "photo"], [107, 15, 109, 13], [107, 18, 109, 16], [107, 24, 109, 22, "cameraRef"], [107, 33, 109, 31], [107, 34, 109, 32, "current"], [107, 41, 109, 39], [107, 42, 109, 40, "takePictureAsync"], [107, 58, 109, 56], [107, 59, 109, 57], [108, 12, 110, 10, "quality"], [108, 19, 110, 17], [108, 21, 110, 19], [108, 24, 110, 22], [109, 12, 111, 10, "base64"], [109, 18, 111, 16], [109, 20, 111, 18], [109, 25, 111, 23], [110, 12, 112, 10, "skipProcessing"], [110, 26, 112, 24], [110, 28, 112, 26], [110, 32, 112, 30], [110, 33, 112, 32], [111, 10, 113, 8], [111, 11, 113, 9], [111, 12, 113, 10], [112, 8, 114, 6], [112, 9, 114, 7], [112, 10, 114, 8], [112, 17, 114, 15, "cameraError"], [112, 28, 114, 26], [112, 30, 114, 28], [113, 10, 115, 8, "console"], [113, 17, 115, 15], [113, 18, 115, 16, "log"], [113, 21, 115, 19], [113, 22, 115, 20], [113, 82, 115, 80], [113, 84, 115, 82, "cameraError"], [113, 95, 115, 93], [113, 96, 115, 94], [114, 10, 116, 8], [115, 10, 117, 8], [115, 14, 117, 12, "isDev"], [115, 19, 117, 17], [115, 21, 117, 19], [116, 12, 118, 10, "photo"], [116, 17, 118, 15], [116, 20, 118, 18], [117, 14, 119, 12, "uri"], [117, 17, 119, 15], [117, 19, 119, 17], [118, 12, 120, 10], [118, 13, 120, 11], [119, 10, 121, 8], [119, 11, 121, 9], [119, 17, 121, 15], [120, 12, 122, 10], [120, 18, 122, 16, "cameraError"], [120, 29, 122, 27], [121, 10, 123, 8], [122, 8, 124, 6], [123, 8, 125, 6], [123, 12, 125, 10], [123, 13, 125, 11, "photo"], [123, 18, 125, 16], [123, 20, 125, 18], [124, 10, 126, 8], [124, 16, 126, 14], [124, 20, 126, 18, "Error"], [124, 25, 126, 23], [124, 26, 126, 24], [124, 51, 126, 49], [124, 52, 126, 50], [125, 8, 127, 6], [126, 8, 128, 6, "console"], [126, 15, 128, 13], [126, 16, 128, 14, "log"], [126, 19, 128, 17], [126, 20, 128, 18], [126, 53, 128, 51], [126, 55, 128, 53, "photo"], [126, 60, 128, 58], [126, 61, 128, 59, "uri"], [126, 64, 128, 62], [126, 65, 128, 63], [127, 8, 129, 6, "setCapturedPhoto"], [127, 24, 129, 22], [127, 25, 129, 23, "photo"], [127, 30, 129, 28], [127, 31, 129, 29, "uri"], [127, 34, 129, 32], [127, 35, 129, 33], [128, 8, 130, 6, "setProcessingProgress"], [128, 29, 130, 27], [128, 30, 130, 28], [128, 32, 130, 30], [128, 33, 130, 31], [129, 8, 131, 6], [130, 8, 132, 6], [130, 14, 132, 12, "processImageWithFaceBlur"], [130, 38, 132, 36], [130, 39, 132, 37, "photo"], [130, 44, 132, 42], [130, 45, 132, 43, "uri"], [130, 48, 132, 46], [130, 49, 132, 47], [131, 6, 133, 4], [131, 7, 133, 5], [131, 8, 133, 6], [131, 15, 133, 13, "error"], [131, 20, 133, 18], [131, 22, 133, 20], [132, 8, 134, 6, "console"], [132, 15, 134, 13], [132, 16, 134, 14, "error"], [132, 21, 134, 19], [132, 22, 134, 20], [132, 54, 134, 52], [132, 56, 134, 54, "error"], [132, 61, 134, 59], [132, 62, 134, 60], [133, 8, 135, 6, "setErrorMessage"], [133, 23, 135, 21], [133, 24, 135, 22], [133, 68, 135, 66], [133, 69, 135, 67], [134, 8, 136, 6, "setProcessingState"], [134, 26, 136, 24], [134, 27, 136, 25], [134, 34, 136, 32], [134, 35, 136, 33], [135, 6, 137, 4], [136, 4, 138, 2], [136, 5, 138, 3], [136, 7, 138, 5], [136, 9, 138, 7], [136, 10, 138, 8], [137, 4, 139, 2], [138, 4, 140, 2], [138, 10, 140, 8, "processImageWithFaceBlur"], [138, 34, 140, 32], [138, 37, 140, 35], [138, 43, 140, 42, "photoUri"], [138, 51, 140, 58], [138, 55, 140, 63], [139, 6, 141, 4], [139, 10, 141, 8], [140, 8, 142, 6, "setProcessingState"], [140, 26, 142, 24], [140, 27, 142, 25], [140, 39, 142, 37], [140, 40, 142, 38], [141, 8, 143, 6, "setProcessingProgress"], [141, 29, 143, 27], [141, 30, 143, 28], [141, 32, 143, 30], [141, 33, 143, 31], [143, 8, 145, 6], [144, 8, 146, 6], [144, 14, 146, 12, "canvas"], [144, 20, 146, 18], [144, 23, 146, 21, "document"], [144, 31, 146, 29], [144, 32, 146, 30, "createElement"], [144, 45, 146, 43], [144, 46, 146, 44], [144, 54, 146, 52], [144, 55, 146, 53], [145, 8, 147, 6], [145, 14, 147, 12, "ctx"], [145, 17, 147, 15], [145, 20, 147, 18, "canvas"], [145, 26, 147, 24], [145, 27, 147, 25, "getContext"], [145, 37, 147, 35], [145, 38, 147, 36], [145, 42, 147, 40], [145, 43, 147, 41], [146, 8, 148, 6], [146, 12, 148, 10], [146, 13, 148, 11, "ctx"], [146, 16, 148, 14], [146, 18, 148, 16], [146, 24, 148, 22], [146, 28, 148, 26, "Error"], [146, 33, 148, 31], [146, 34, 148, 32], [146, 64, 148, 62], [146, 65, 148, 63], [148, 8, 150, 6], [149, 8, 151, 6], [149, 14, 151, 12, "img"], [149, 17, 151, 15], [149, 20, 151, 18], [149, 24, 151, 22, "Image"], [149, 29, 151, 27], [149, 30, 151, 28], [149, 31, 151, 29], [150, 8, 152, 6], [150, 14, 152, 12], [150, 18, 152, 16, "Promise"], [150, 25, 152, 23], [150, 26, 152, 24], [150, 27, 152, 25, "resolve"], [150, 34, 152, 32], [150, 36, 152, 34, "reject"], [150, 42, 152, 40], [150, 47, 152, 45], [151, 10, 153, 8, "img"], [151, 13, 153, 11], [151, 14, 153, 12, "onload"], [151, 20, 153, 18], [151, 23, 153, 21, "resolve"], [151, 30, 153, 28], [152, 10, 154, 8, "img"], [152, 13, 154, 11], [152, 14, 154, 12, "onerror"], [152, 21, 154, 19], [152, 24, 154, 22, "reject"], [152, 30, 154, 28], [153, 10, 155, 8, "img"], [153, 13, 155, 11], [153, 14, 155, 12, "src"], [153, 17, 155, 15], [153, 20, 155, 18, "photoUri"], [153, 28, 155, 26], [154, 8, 156, 6], [154, 9, 156, 7], [154, 10, 156, 8], [156, 8, 158, 6], [157, 8, 159, 6, "canvas"], [157, 14, 159, 12], [157, 15, 159, 13, "width"], [157, 20, 159, 18], [157, 23, 159, 21, "img"], [157, 26, 159, 24], [157, 27, 159, 25, "width"], [157, 32, 159, 30], [158, 8, 160, 6, "canvas"], [158, 14, 160, 12], [158, 15, 160, 13, "height"], [158, 21, 160, 19], [158, 24, 160, 22, "img"], [158, 27, 160, 25], [158, 28, 160, 26, "height"], [158, 34, 160, 32], [160, 8, 162, 6], [161, 8, 163, 6, "ctx"], [161, 11, 163, 9], [161, 12, 163, 10, "drawImage"], [161, 21, 163, 19], [161, 22, 163, 20, "img"], [161, 25, 163, 23], [161, 27, 163, 25], [161, 28, 163, 26], [161, 30, 163, 28], [161, 31, 163, 29], [161, 32, 163, 30], [162, 8, 165, 6, "setProcessingProgress"], [162, 29, 165, 27], [162, 30, 165, 28], [162, 32, 165, 30], [162, 33, 165, 31], [164, 8, 167, 6], [165, 8, 168, 6], [165, 12, 168, 10, "detectedFaces"], [165, 25, 168, 23], [165, 28, 168, 26], [165, 30, 168, 28], [166, 8, 170, 6, "console"], [166, 15, 170, 13], [166, 16, 170, 14, "log"], [166, 19, 170, 17], [166, 20, 170, 18], [166, 74, 170, 72], [166, 76, 170, 74], [167, 10, 171, 8, "width"], [167, 15, 171, 13], [167, 17, 171, 15, "img"], [167, 20, 171, 18], [167, 21, 171, 19, "width"], [167, 26, 171, 24], [168, 10, 172, 8, "height"], [168, 16, 172, 14], [168, 18, 172, 16, "img"], [168, 21, 172, 19], [168, 22, 172, 20, "height"], [168, 28, 172, 26], [169, 10, 173, 8, "src"], [169, 13, 173, 11], [169, 15, 173, 13, "photoUri"], [169, 23, 173, 21], [169, 24, 173, 22, "substring"], [169, 33, 173, 31], [169, 34, 173, 32], [169, 35, 173, 33], [169, 37, 173, 35], [169, 39, 173, 37], [169, 40, 173, 38], [169, 43, 173, 41], [170, 8, 174, 6], [170, 9, 174, 7], [170, 10, 174, 8], [172, 8, 176, 6], [173, 8, 177, 6], [173, 12, 177, 10], [174, 10, 178, 8], [174, 14, 178, 12], [174, 28, 178, 26], [174, 32, 178, 30, "window"], [174, 38, 178, 36], [174, 40, 178, 38], [175, 12, 179, 10, "console"], [175, 19, 179, 17], [175, 20, 179, 18, "log"], [175, 23, 179, 21], [175, 24, 179, 22], [175, 105, 179, 103], [175, 106, 179, 104], [176, 12, 180, 10], [176, 18, 180, 16, "faceDetector"], [176, 30, 180, 28], [176, 33, 180, 31], [176, 37, 180, 36, "window"], [176, 43, 180, 42], [176, 44, 180, 51, "FaceDetector"], [176, 56, 180, 63], [176, 57, 180, 64], [177, 14, 181, 12, "maxDetectedFaces"], [177, 30, 181, 28], [177, 32, 181, 30], [177, 34, 181, 32], [178, 14, 182, 12, "fastMode"], [178, 22, 182, 20], [178, 24, 182, 22], [179, 12, 183, 10], [179, 13, 183, 11], [179, 14, 183, 12], [180, 12, 185, 10], [180, 18, 185, 16, "browserDetections"], [180, 35, 185, 33], [180, 38, 185, 36], [180, 44, 185, 42, "faceDetector"], [180, 56, 185, 54], [180, 57, 185, 55, "detect"], [180, 63, 185, 61], [180, 64, 185, 62, "img"], [180, 67, 185, 65], [180, 68, 185, 66], [181, 12, 186, 10, "detectedFaces"], [181, 25, 186, 23], [181, 28, 186, 26, "browserDetections"], [181, 45, 186, 43], [181, 46, 186, 44, "map"], [181, 49, 186, 47], [181, 50, 186, 49, "detection"], [181, 59, 186, 63], [181, 64, 186, 69], [182, 14, 187, 12, "boundingBox"], [182, 25, 187, 23], [182, 27, 187, 25], [183, 16, 188, 14, "xCenter"], [183, 23, 188, 21], [183, 25, 188, 23], [183, 26, 188, 24, "detection"], [183, 35, 188, 33], [183, 36, 188, 34, "boundingBox"], [183, 47, 188, 45], [183, 48, 188, 46, "x"], [183, 49, 188, 47], [183, 52, 188, 50, "detection"], [183, 61, 188, 59], [183, 62, 188, 60, "boundingBox"], [183, 73, 188, 71], [183, 74, 188, 72, "width"], [183, 79, 188, 77], [183, 82, 188, 80], [183, 83, 188, 81], [183, 87, 188, 85, "img"], [183, 90, 188, 88], [183, 91, 188, 89, "width"], [183, 96, 188, 94], [184, 16, 189, 14, "yCenter"], [184, 23, 189, 21], [184, 25, 189, 23], [184, 26, 189, 24, "detection"], [184, 35, 189, 33], [184, 36, 189, 34, "boundingBox"], [184, 47, 189, 45], [184, 48, 189, 46, "y"], [184, 49, 189, 47], [184, 52, 189, 50, "detection"], [184, 61, 189, 59], [184, 62, 189, 60, "boundingBox"], [184, 73, 189, 71], [184, 74, 189, 72, "height"], [184, 80, 189, 78], [184, 83, 189, 81], [184, 84, 189, 82], [184, 88, 189, 86, "img"], [184, 91, 189, 89], [184, 92, 189, 90, "height"], [184, 98, 189, 96], [185, 16, 190, 14, "width"], [185, 21, 190, 19], [185, 23, 190, 21, "detection"], [185, 32, 190, 30], [185, 33, 190, 31, "boundingBox"], [185, 44, 190, 42], [185, 45, 190, 43, "width"], [185, 50, 190, 48], [185, 53, 190, 51, "img"], [185, 56, 190, 54], [185, 57, 190, 55, "width"], [185, 62, 190, 60], [186, 16, 191, 14, "height"], [186, 22, 191, 20], [186, 24, 191, 22, "detection"], [186, 33, 191, 31], [186, 34, 191, 32, "boundingBox"], [186, 45, 191, 43], [186, 46, 191, 44, "height"], [186, 52, 191, 50], [186, 55, 191, 53, "img"], [186, 58, 191, 56], [186, 59, 191, 57, "height"], [187, 14, 192, 12], [188, 12, 193, 10], [188, 13, 193, 11], [188, 14, 193, 12], [188, 15, 193, 13], [189, 12, 194, 10, "console"], [189, 19, 194, 17], [189, 20, 194, 18, "log"], [189, 23, 194, 21], [189, 24, 194, 22], [189, 78, 194, 76, "detectedFaces"], [189, 91, 194, 89], [189, 92, 194, 90, "length"], [189, 98, 194, 96], [189, 106, 194, 104], [189, 107, 194, 105], [190, 10, 195, 8], [190, 11, 195, 9], [190, 17, 195, 15], [191, 12, 196, 10, "console"], [191, 19, 196, 17], [191, 20, 196, 18, "log"], [191, 23, 196, 21], [191, 24, 196, 22], [191, 100, 196, 98], [191, 101, 196, 99], [192, 12, 197, 10], [192, 18, 197, 16], [192, 22, 197, 20, "Error"], [192, 27, 197, 25], [192, 28, 197, 26], [192, 70, 197, 68], [192, 71, 197, 69], [193, 10, 198, 8], [194, 8, 199, 6], [194, 9, 199, 7], [194, 10, 199, 8], [194, 17, 199, 15, "browserError"], [194, 29, 199, 27], [194, 31, 199, 29], [195, 10, 200, 8, "console"], [195, 17, 200, 15], [195, 18, 200, 16, "warn"], [195, 22, 200, 20], [195, 23, 200, 21], [195, 102, 200, 100], [195, 104, 200, 102, "browserError"], [195, 116, 200, 114], [195, 117, 200, 115], [197, 10, 202, 8], [198, 10, 203, 8], [198, 14, 203, 12], [199, 12, 204, 10], [200, 12, 205, 10], [200, 16, 205, 14], [200, 17, 205, 16, "window"], [200, 23, 205, 22], [200, 24, 205, 31, "<PERSON>ap<PERSON>"], [200, 31, 205, 38], [200, 33, 205, 40], [201, 14, 206, 12], [201, 20, 206, 18], [201, 24, 206, 22, "Promise"], [201, 31, 206, 29], [201, 32, 206, 30], [201, 33, 206, 31, "resolve"], [201, 40, 206, 38], [201, 42, 206, 40, "reject"], [201, 48, 206, 46], [201, 53, 206, 51], [202, 16, 207, 14], [202, 22, 207, 20, "script"], [202, 28, 207, 26], [202, 31, 207, 29, "document"], [202, 39, 207, 37], [202, 40, 207, 38, "createElement"], [202, 53, 207, 51], [202, 54, 207, 52], [202, 62, 207, 60], [202, 63, 207, 61], [203, 16, 208, 14, "script"], [203, 22, 208, 20], [203, 23, 208, 21, "src"], [203, 26, 208, 24], [203, 29, 208, 27], [203, 99, 208, 97], [204, 16, 209, 14, "script"], [204, 22, 209, 20], [204, 23, 209, 21, "onload"], [204, 29, 209, 27], [204, 32, 209, 30, "resolve"], [204, 39, 209, 37], [205, 16, 210, 14, "script"], [205, 22, 210, 20], [205, 23, 210, 21, "onerror"], [205, 30, 210, 28], [205, 33, 210, 31, "reject"], [205, 39, 210, 37], [206, 16, 211, 14, "document"], [206, 24, 211, 22], [206, 25, 211, 23, "head"], [206, 29, 211, 27], [206, 30, 211, 28, "append<PERSON><PERSON><PERSON>"], [206, 41, 211, 39], [206, 42, 211, 40, "script"], [206, 48, 211, 46], [206, 49, 211, 47], [207, 14, 212, 12], [207, 15, 212, 13], [207, 16, 212, 14], [208, 12, 213, 10], [209, 12, 215, 10], [209, 18, 215, 16, "<PERSON>ap<PERSON>"], [209, 25, 215, 23], [209, 28, 215, 27, "window"], [209, 34, 215, 33], [209, 35, 215, 42, "<PERSON>ap<PERSON>"], [209, 42, 215, 49], [211, 12, 217, 10], [212, 12, 218, 10], [212, 18, 218, 16, "Promise"], [212, 25, 218, 23], [212, 26, 218, 24, "all"], [212, 29, 218, 27], [212, 30, 218, 28], [212, 31, 219, 12, "<PERSON>ap<PERSON>"], [212, 38, 219, 19], [212, 39, 219, 20, "nets"], [212, 43, 219, 24], [212, 44, 219, 25, "tinyFaceDetector"], [212, 60, 219, 41], [212, 61, 219, 42, "loadFromUri"], [212, 72, 219, 53], [212, 73, 219, 54], [212, 130, 219, 111], [212, 131, 219, 112], [212, 133, 220, 12, "<PERSON>ap<PERSON>"], [212, 140, 220, 19], [212, 141, 220, 20, "nets"], [212, 145, 220, 24], [212, 146, 220, 25, "faceLandmark68Net"], [212, 163, 220, 42], [212, 164, 220, 43, "loadFromUri"], [212, 175, 220, 54], [212, 176, 220, 55], [212, 233, 220, 112], [212, 234, 220, 113], [212, 235, 221, 11], [212, 236, 221, 12], [214, 12, 223, 10], [215, 12, 224, 10], [215, 18, 224, 16, "faceDetections"], [215, 32, 224, 30], [215, 35, 224, 33], [215, 41, 224, 39, "<PERSON>ap<PERSON>"], [215, 48, 224, 46], [215, 49, 224, 47, "detectAllFaces"], [215, 63, 224, 61], [215, 64, 224, 62, "img"], [215, 67, 224, 65], [215, 69, 224, 67], [215, 73, 224, 71, "<PERSON>ap<PERSON>"], [215, 80, 224, 78], [215, 81, 224, 79, "TinyFaceDetectorOptions"], [215, 104, 224, 102], [215, 105, 224, 103], [215, 106, 224, 104], [215, 107, 224, 105], [216, 12, 226, 10, "detectedFaces"], [216, 25, 226, 23], [216, 28, 226, 26, "faceDetections"], [216, 42, 226, 40], [216, 43, 226, 41, "map"], [216, 46, 226, 44], [216, 47, 226, 46, "detection"], [216, 56, 226, 60], [216, 61, 226, 66], [217, 14, 227, 12, "boundingBox"], [217, 25, 227, 23], [217, 27, 227, 25], [218, 16, 228, 14, "xCenter"], [218, 23, 228, 21], [218, 25, 228, 23], [218, 26, 228, 24, "detection"], [218, 35, 228, 33], [218, 36, 228, 34, "box"], [218, 39, 228, 37], [218, 40, 228, 38, "x"], [218, 41, 228, 39], [218, 44, 228, 42, "detection"], [218, 53, 228, 51], [218, 54, 228, 52, "box"], [218, 57, 228, 55], [218, 58, 228, 56, "width"], [218, 63, 228, 61], [218, 66, 228, 64], [218, 67, 228, 65], [218, 71, 228, 69, "img"], [218, 74, 228, 72], [218, 75, 228, 73, "width"], [218, 80, 228, 78], [219, 16, 229, 14, "yCenter"], [219, 23, 229, 21], [219, 25, 229, 23], [219, 26, 229, 24, "detection"], [219, 35, 229, 33], [219, 36, 229, 34, "box"], [219, 39, 229, 37], [219, 40, 229, 38, "y"], [219, 41, 229, 39], [219, 44, 229, 42, "detection"], [219, 53, 229, 51], [219, 54, 229, 52, "box"], [219, 57, 229, 55], [219, 58, 229, 56, "height"], [219, 64, 229, 62], [219, 67, 229, 65], [219, 68, 229, 66], [219, 72, 229, 70, "img"], [219, 75, 229, 73], [219, 76, 229, 74, "height"], [219, 82, 229, 80], [220, 16, 230, 14, "width"], [220, 21, 230, 19], [220, 23, 230, 21, "detection"], [220, 32, 230, 30], [220, 33, 230, 31, "box"], [220, 36, 230, 34], [220, 37, 230, 35, "width"], [220, 42, 230, 40], [220, 45, 230, 43, "img"], [220, 48, 230, 46], [220, 49, 230, 47, "width"], [220, 54, 230, 52], [221, 16, 231, 14, "height"], [221, 22, 231, 20], [221, 24, 231, 22, "detection"], [221, 33, 231, 31], [221, 34, 231, 32, "box"], [221, 37, 231, 35], [221, 38, 231, 36, "height"], [221, 44, 231, 42], [221, 47, 231, 45, "img"], [221, 50, 231, 48], [221, 51, 231, 49, "height"], [222, 14, 232, 12], [223, 12, 233, 10], [223, 13, 233, 11], [223, 14, 233, 12], [223, 15, 233, 13], [224, 12, 235, 10, "console"], [224, 19, 235, 17], [224, 20, 235, 18, "log"], [224, 23, 235, 21], [224, 24, 235, 22], [224, 63, 235, 61, "detectedFaces"], [224, 76, 235, 74], [224, 77, 235, 75, "length"], [224, 83, 235, 81], [224, 91, 235, 89], [224, 92, 235, 90], [225, 10, 236, 8], [225, 11, 236, 9], [225, 12, 236, 10], [225, 19, 236, 17, "faceApiError"], [225, 31, 236, 29], [225, 33, 236, 31], [226, 12, 237, 10, "console"], [226, 19, 237, 17], [226, 20, 237, 18, "warn"], [226, 24, 237, 22], [226, 25, 237, 23], [226, 69, 237, 67], [226, 71, 237, 69, "faceApiError"], [226, 83, 237, 81], [226, 84, 237, 82], [228, 12, 239, 10], [229, 12, 240, 10, "console"], [229, 19, 240, 17], [229, 20, 240, 18, "log"], [229, 23, 240, 21], [229, 24, 240, 22], [229, 94, 240, 92], [229, 95, 240, 93], [230, 12, 241, 10, "detectedFaces"], [230, 25, 241, 23], [230, 28, 241, 26], [230, 29, 241, 27], [231, 14, 242, 12, "boundingBox"], [231, 25, 242, 23], [231, 27, 242, 25], [232, 16, 243, 14, "xCenter"], [232, 23, 243, 21], [232, 25, 243, 23], [232, 28, 243, 26], [233, 16, 243, 29], [234, 16, 244, 14, "yCenter"], [234, 23, 244, 21], [234, 25, 244, 23], [234, 28, 244, 26], [235, 16, 244, 29], [236, 16, 245, 14, "width"], [236, 21, 245, 19], [236, 23, 245, 21], [236, 26, 245, 24], [237, 16, 245, 29], [238, 16, 246, 14, "height"], [238, 22, 246, 20], [238, 24, 246, 22], [238, 27, 246, 25], [238, 28, 246, 29], [239, 14, 247, 12], [240, 12, 248, 10], [240, 13, 248, 11], [240, 14, 248, 12], [241, 12, 249, 10, "console"], [241, 19, 249, 17], [241, 20, 249, 18, "log"], [241, 23, 249, 21], [241, 24, 249, 22], [241, 93, 249, 91], [241, 94, 249, 92], [242, 10, 250, 8], [243, 8, 251, 6], [244, 8, 253, 6, "console"], [244, 15, 253, 13], [244, 16, 253, 14, "log"], [244, 19, 253, 17], [244, 20, 253, 18], [244, 72, 253, 70, "detectedFaces"], [244, 85, 253, 83], [244, 86, 253, 84, "length"], [244, 92, 253, 90], [244, 100, 253, 98], [244, 101, 253, 99], [245, 8, 254, 6], [245, 12, 254, 10, "detectedFaces"], [245, 25, 254, 23], [245, 26, 254, 24, "length"], [245, 32, 254, 30], [245, 35, 254, 33], [245, 36, 254, 34], [245, 38, 254, 36], [246, 10, 255, 8, "console"], [246, 17, 255, 15], [246, 18, 255, 16, "log"], [246, 21, 255, 19], [246, 22, 255, 20], [246, 66, 255, 64], [246, 68, 255, 66, "detectedFaces"], [246, 81, 255, 79], [246, 82, 255, 80, "map"], [246, 85, 255, 83], [246, 86, 255, 84], [246, 87, 255, 85, "face"], [246, 91, 255, 89], [246, 93, 255, 91, "i"], [246, 94, 255, 92], [246, 100, 255, 98], [247, 12, 256, 10, "faceNumber"], [247, 22, 256, 20], [247, 24, 256, 22, "i"], [247, 25, 256, 23], [247, 28, 256, 26], [247, 29, 256, 27], [248, 12, 257, 10, "centerX"], [248, 19, 257, 17], [248, 21, 257, 19, "face"], [248, 25, 257, 23], [248, 26, 257, 24, "boundingBox"], [248, 37, 257, 35], [248, 38, 257, 36, "xCenter"], [248, 45, 257, 43], [249, 12, 258, 10, "centerY"], [249, 19, 258, 17], [249, 21, 258, 19, "face"], [249, 25, 258, 23], [249, 26, 258, 24, "boundingBox"], [249, 37, 258, 35], [249, 38, 258, 36, "yCenter"], [249, 45, 258, 43], [250, 12, 259, 10, "width"], [250, 17, 259, 15], [250, 19, 259, 17, "face"], [250, 23, 259, 21], [250, 24, 259, 22, "boundingBox"], [250, 35, 259, 33], [250, 36, 259, 34, "width"], [250, 41, 259, 39], [251, 12, 260, 10, "height"], [251, 18, 260, 16], [251, 20, 260, 18, "face"], [251, 24, 260, 22], [251, 25, 260, 23, "boundingBox"], [251, 36, 260, 34], [251, 37, 260, 35, "height"], [252, 10, 261, 8], [252, 11, 261, 9], [252, 12, 261, 10], [252, 13, 261, 11], [252, 14, 261, 12], [253, 8, 262, 6], [253, 9, 262, 7], [253, 15, 262, 13], [254, 10, 263, 8, "console"], [254, 17, 263, 15], [254, 18, 263, 16, "log"], [254, 21, 263, 19], [254, 22, 263, 20], [254, 91, 263, 89], [254, 92, 263, 90], [255, 8, 264, 6], [256, 8, 266, 6, "setProcessingProgress"], [256, 29, 266, 27], [256, 30, 266, 28], [256, 32, 266, 30], [256, 33, 266, 31], [258, 8, 268, 6], [259, 8, 269, 6], [259, 12, 269, 10, "detectedFaces"], [259, 25, 269, 23], [259, 26, 269, 24, "length"], [259, 32, 269, 30], [259, 35, 269, 33], [259, 36, 269, 34], [259, 38, 269, 36], [260, 10, 270, 8, "detectedFaces"], [260, 23, 270, 21], [260, 24, 270, 22, "for<PERSON>ach"], [260, 31, 270, 29], [260, 32, 270, 30], [260, 33, 270, 31, "detection"], [260, 42, 270, 40], [260, 44, 270, 42, "index"], [260, 49, 270, 47], [260, 54, 270, 52], [261, 12, 271, 10], [261, 18, 271, 16, "bbox"], [261, 22, 271, 20], [261, 25, 271, 23, "detection"], [261, 34, 271, 32], [261, 35, 271, 33, "boundingBox"], [261, 46, 271, 44], [263, 12, 273, 10], [264, 12, 274, 10], [264, 18, 274, 16, "faceX"], [264, 23, 274, 21], [264, 26, 274, 24, "bbox"], [264, 30, 274, 28], [264, 31, 274, 29, "xCenter"], [264, 38, 274, 36], [264, 41, 274, 39, "img"], [264, 44, 274, 42], [264, 45, 274, 43, "width"], [264, 50, 274, 48], [264, 53, 274, 52, "bbox"], [264, 57, 274, 56], [264, 58, 274, 57, "width"], [264, 63, 274, 62], [264, 66, 274, 65, "img"], [264, 69, 274, 68], [264, 70, 274, 69, "width"], [264, 75, 274, 74], [264, 78, 274, 78], [264, 79, 274, 79], [265, 12, 275, 10], [265, 18, 275, 16, "faceY"], [265, 23, 275, 21], [265, 26, 275, 24, "bbox"], [265, 30, 275, 28], [265, 31, 275, 29, "yCenter"], [265, 38, 275, 36], [265, 41, 275, 39, "img"], [265, 44, 275, 42], [265, 45, 275, 43, "height"], [265, 51, 275, 49], [265, 54, 275, 53, "bbox"], [265, 58, 275, 57], [265, 59, 275, 58, "height"], [265, 65, 275, 64], [265, 68, 275, 67, "img"], [265, 71, 275, 70], [265, 72, 275, 71, "height"], [265, 78, 275, 77], [265, 81, 275, 81], [265, 82, 275, 82], [266, 12, 276, 10], [266, 18, 276, 16, "faceWidth"], [266, 27, 276, 25], [266, 30, 276, 28, "bbox"], [266, 34, 276, 32], [266, 35, 276, 33, "width"], [266, 40, 276, 38], [266, 43, 276, 41, "img"], [266, 46, 276, 44], [266, 47, 276, 45, "width"], [266, 52, 276, 50], [267, 12, 277, 10], [267, 18, 277, 16, "faceHeight"], [267, 28, 277, 26], [267, 31, 277, 29, "bbox"], [267, 35, 277, 33], [267, 36, 277, 34, "height"], [267, 42, 277, 40], [267, 45, 277, 43, "img"], [267, 48, 277, 46], [267, 49, 277, 47, "height"], [267, 55, 277, 53], [269, 12, 279, 10], [270, 12, 280, 10], [270, 18, 280, 16, "padding"], [270, 25, 280, 23], [270, 28, 280, 26], [270, 31, 280, 29], [270, 32, 280, 30], [270, 33, 280, 31], [271, 12, 281, 10], [271, 18, 281, 16, "paddedX"], [271, 25, 281, 23], [271, 28, 281, 26, "Math"], [271, 32, 281, 30], [271, 33, 281, 31, "max"], [271, 36, 281, 34], [271, 37, 281, 35], [271, 38, 281, 36], [271, 40, 281, 38, "faceX"], [271, 45, 281, 43], [271, 48, 281, 46, "faceWidth"], [271, 57, 281, 55], [271, 60, 281, 58, "padding"], [271, 67, 281, 65], [271, 68, 281, 66], [272, 12, 282, 10], [272, 18, 282, 16, "paddedY"], [272, 25, 282, 23], [272, 28, 282, 26, "Math"], [272, 32, 282, 30], [272, 33, 282, 31, "max"], [272, 36, 282, 34], [272, 37, 282, 35], [272, 38, 282, 36], [272, 40, 282, 38, "faceY"], [272, 45, 282, 43], [272, 48, 282, 46, "faceHeight"], [272, 58, 282, 56], [272, 61, 282, 59, "padding"], [272, 68, 282, 66], [272, 69, 282, 67], [273, 12, 283, 10], [273, 18, 283, 16, "<PERSON><PERSON><PERSON><PERSON>"], [273, 29, 283, 27], [273, 32, 283, 30, "Math"], [273, 36, 283, 34], [273, 37, 283, 35, "min"], [273, 40, 283, 38], [273, 41, 283, 39, "img"], [273, 44, 283, 42], [273, 45, 283, 43, "width"], [273, 50, 283, 48], [273, 53, 283, 51, "paddedX"], [273, 60, 283, 58], [273, 62, 283, 60, "faceWidth"], [273, 71, 283, 69], [273, 75, 283, 73], [273, 76, 283, 74], [273, 79, 283, 77], [273, 80, 283, 78], [273, 83, 283, 81, "padding"], [273, 90, 283, 88], [273, 91, 283, 89], [273, 92, 283, 90], [274, 12, 284, 10], [274, 18, 284, 16, "paddedHeight"], [274, 30, 284, 28], [274, 33, 284, 31, "Math"], [274, 37, 284, 35], [274, 38, 284, 36, "min"], [274, 41, 284, 39], [274, 42, 284, 40, "img"], [274, 45, 284, 43], [274, 46, 284, 44, "height"], [274, 52, 284, 50], [274, 55, 284, 53, "paddedY"], [274, 62, 284, 60], [274, 64, 284, 62, "faceHeight"], [274, 74, 284, 72], [274, 78, 284, 76], [274, 79, 284, 77], [274, 82, 284, 80], [274, 83, 284, 81], [274, 86, 284, 84, "padding"], [274, 93, 284, 91], [274, 94, 284, 92], [274, 95, 284, 93], [275, 12, 286, 10, "console"], [275, 19, 286, 17], [275, 20, 286, 18, "log"], [275, 23, 286, 21], [275, 24, 286, 22], [275, 60, 286, 58, "index"], [275, 65, 286, 63], [275, 68, 286, 66], [275, 69, 286, 67], [275, 77, 286, 75, "Math"], [275, 81, 286, 79], [275, 82, 286, 80, "round"], [275, 87, 286, 85], [275, 88, 286, 86, "paddedX"], [275, 95, 286, 93], [275, 96, 286, 94], [275, 101, 286, 99, "Math"], [275, 105, 286, 103], [275, 106, 286, 104, "round"], [275, 111, 286, 109], [275, 112, 286, 110, "paddedY"], [275, 119, 286, 117], [275, 120, 286, 118], [275, 130, 286, 128, "Math"], [275, 134, 286, 132], [275, 135, 286, 133, "round"], [275, 140, 286, 138], [275, 141, 286, 139, "<PERSON><PERSON><PERSON><PERSON>"], [275, 152, 286, 150], [275, 153, 286, 151], [275, 157, 286, 155, "Math"], [275, 161, 286, 159], [275, 162, 286, 160, "round"], [275, 167, 286, 165], [275, 168, 286, 166, "paddedHeight"], [275, 180, 286, 178], [275, 181, 286, 179], [275, 183, 286, 181], [275, 184, 286, 182], [277, 12, 288, 10], [278, 12, 289, 10], [278, 18, 289, 16, "faceImageData"], [278, 31, 289, 29], [278, 34, 289, 32, "ctx"], [278, 37, 289, 35], [278, 38, 289, 36, "getImageData"], [278, 50, 289, 48], [278, 51, 289, 49, "paddedX"], [278, 58, 289, 56], [278, 60, 289, 58, "paddedY"], [278, 67, 289, 65], [278, 69, 289, 67, "<PERSON><PERSON><PERSON><PERSON>"], [278, 80, 289, 78], [278, 82, 289, 80, "paddedHeight"], [278, 94, 289, 92], [278, 95, 289, 93], [279, 12, 290, 10], [279, 18, 290, 16, "data"], [279, 22, 290, 20], [279, 25, 290, 23, "faceImageData"], [279, 38, 290, 36], [279, 39, 290, 37, "data"], [279, 43, 290, 41], [280, 12, 292, 10, "console"], [280, 19, 292, 17], [280, 20, 292, 18, "log"], [280, 23, 292, 21], [280, 24, 292, 22], [280, 64, 292, 62, "data"], [280, 68, 292, 66], [280, 69, 292, 67, "length"], [280, 75, 292, 73], [280, 86, 292, 84, "<PERSON><PERSON><PERSON><PERSON>"], [280, 97, 292, 95], [280, 101, 292, 99, "paddedHeight"], [280, 113, 292, 111], [280, 122, 292, 120], [280, 123, 292, 121], [282, 12, 294, 10], [283, 12, 295, 10], [283, 18, 295, 16, "pixelSize"], [283, 27, 295, 25], [283, 30, 295, 28, "Math"], [283, 34, 295, 32], [283, 35, 295, 33, "max"], [283, 38, 295, 36], [283, 39, 295, 37], [283, 41, 295, 39], [283, 43, 295, 41, "Math"], [283, 47, 295, 45], [283, 48, 295, 46, "min"], [283, 51, 295, 49], [283, 52, 295, 50, "<PERSON><PERSON><PERSON><PERSON>"], [283, 63, 295, 61], [283, 65, 295, 63, "paddedHeight"], [283, 77, 295, 75], [283, 78, 295, 76], [283, 81, 295, 79], [283, 83, 295, 81], [283, 84, 295, 82], [283, 85, 295, 83], [283, 86, 295, 84], [284, 12, 296, 10, "console"], [284, 19, 296, 17], [284, 20, 296, 18, "log"], [284, 23, 296, 21], [284, 24, 296, 22], [284, 64, 296, 62, "pixelSize"], [284, 73, 296, 71], [284, 90, 296, 88], [284, 91, 296, 89], [285, 12, 297, 10], [285, 17, 297, 15], [285, 21, 297, 19, "y"], [285, 22, 297, 20], [285, 25, 297, 23], [285, 26, 297, 24], [285, 28, 297, 26, "y"], [285, 29, 297, 27], [285, 32, 297, 30, "paddedHeight"], [285, 44, 297, 42], [285, 46, 297, 44, "y"], [285, 47, 297, 45], [285, 51, 297, 49, "pixelSize"], [285, 60, 297, 58], [285, 62, 297, 60], [286, 14, 298, 12], [286, 19, 298, 17], [286, 23, 298, 21, "x"], [286, 24, 298, 22], [286, 27, 298, 25], [286, 28, 298, 26], [286, 30, 298, 28, "x"], [286, 31, 298, 29], [286, 34, 298, 32, "<PERSON><PERSON><PERSON><PERSON>"], [286, 45, 298, 43], [286, 47, 298, 45, "x"], [286, 48, 298, 46], [286, 52, 298, 50, "pixelSize"], [286, 61, 298, 59], [286, 63, 298, 61], [287, 16, 299, 14], [288, 16, 300, 14], [288, 22, 300, 20, "pixelIndex"], [288, 32, 300, 30], [288, 35, 300, 33], [288, 36, 300, 34, "y"], [288, 37, 300, 35], [288, 40, 300, 38, "<PERSON><PERSON><PERSON><PERSON>"], [288, 51, 300, 49], [288, 54, 300, 52, "x"], [288, 55, 300, 53], [288, 59, 300, 57], [288, 60, 300, 58], [289, 16, 301, 14], [289, 22, 301, 20, "r"], [289, 23, 301, 21], [289, 26, 301, 24, "data"], [289, 30, 301, 28], [289, 31, 301, 29, "pixelIndex"], [289, 41, 301, 39], [289, 42, 301, 40], [290, 16, 302, 14], [290, 22, 302, 20, "g"], [290, 23, 302, 21], [290, 26, 302, 24, "data"], [290, 30, 302, 28], [290, 31, 302, 29, "pixelIndex"], [290, 41, 302, 39], [290, 44, 302, 42], [290, 45, 302, 43], [290, 46, 302, 44], [291, 16, 303, 14], [291, 22, 303, 20, "b"], [291, 23, 303, 21], [291, 26, 303, 24, "data"], [291, 30, 303, 28], [291, 31, 303, 29, "pixelIndex"], [291, 41, 303, 39], [291, 44, 303, 42], [291, 45, 303, 43], [291, 46, 303, 44], [292, 16, 304, 14], [292, 22, 304, 20, "a"], [292, 23, 304, 21], [292, 26, 304, 24, "data"], [292, 30, 304, 28], [292, 31, 304, 29, "pixelIndex"], [292, 41, 304, 39], [292, 44, 304, 42], [292, 45, 304, 43], [292, 46, 304, 44], [294, 16, 306, 14], [295, 16, 307, 14], [295, 21, 307, 19], [295, 25, 307, 23, "dy"], [295, 27, 307, 25], [295, 30, 307, 28], [295, 31, 307, 29], [295, 33, 307, 31, "dy"], [295, 35, 307, 33], [295, 38, 307, 36, "pixelSize"], [295, 47, 307, 45], [295, 51, 307, 49, "y"], [295, 52, 307, 50], [295, 55, 307, 53, "dy"], [295, 57, 307, 55], [295, 60, 307, 58, "paddedHeight"], [295, 72, 307, 70], [295, 74, 307, 72, "dy"], [295, 76, 307, 74], [295, 78, 307, 76], [295, 80, 307, 78], [296, 18, 308, 16], [296, 23, 308, 21], [296, 27, 308, 25, "dx"], [296, 29, 308, 27], [296, 32, 308, 30], [296, 33, 308, 31], [296, 35, 308, 33, "dx"], [296, 37, 308, 35], [296, 40, 308, 38, "pixelSize"], [296, 49, 308, 47], [296, 53, 308, 51, "x"], [296, 54, 308, 52], [296, 57, 308, 55, "dx"], [296, 59, 308, 57], [296, 62, 308, 60, "<PERSON><PERSON><PERSON><PERSON>"], [296, 73, 308, 71], [296, 75, 308, 73, "dx"], [296, 77, 308, 75], [296, 79, 308, 77], [296, 81, 308, 79], [297, 20, 309, 18], [297, 26, 309, 24, "blockPixelIndex"], [297, 41, 309, 39], [297, 44, 309, 42], [297, 45, 309, 43], [297, 46, 309, 44, "y"], [297, 47, 309, 45], [297, 50, 309, 48, "dy"], [297, 52, 309, 50], [297, 56, 309, 54, "<PERSON><PERSON><PERSON><PERSON>"], [297, 67, 309, 65], [297, 71, 309, 69, "x"], [297, 72, 309, 70], [297, 75, 309, 73, "dx"], [297, 77, 309, 75], [297, 78, 309, 76], [297, 82, 309, 80], [297, 83, 309, 81], [298, 20, 310, 18, "data"], [298, 24, 310, 22], [298, 25, 310, 23, "blockPixelIndex"], [298, 40, 310, 38], [298, 41, 310, 39], [298, 44, 310, 42, "r"], [298, 45, 310, 43], [299, 20, 311, 18, "data"], [299, 24, 311, 22], [299, 25, 311, 23, "blockPixelIndex"], [299, 40, 311, 38], [299, 43, 311, 41], [299, 44, 311, 42], [299, 45, 311, 43], [299, 48, 311, 46, "g"], [299, 49, 311, 47], [300, 20, 312, 18, "data"], [300, 24, 312, 22], [300, 25, 312, 23, "blockPixelIndex"], [300, 40, 312, 38], [300, 43, 312, 41], [300, 44, 312, 42], [300, 45, 312, 43], [300, 48, 312, 46, "b"], [300, 49, 312, 47], [301, 20, 313, 18, "data"], [301, 24, 313, 22], [301, 25, 313, 23, "blockPixelIndex"], [301, 40, 313, 38], [301, 43, 313, 41], [301, 44, 313, 42], [301, 45, 313, 43], [301, 48, 313, 46, "a"], [301, 49, 313, 47], [302, 18, 314, 16], [303, 16, 315, 14], [304, 14, 316, 12], [305, 12, 317, 10], [307, 12, 319, 10], [308, 12, 320, 10, "ctx"], [308, 15, 320, 13], [308, 16, 320, 14, "putImageData"], [308, 28, 320, 26], [308, 29, 320, 27, "faceImageData"], [308, 42, 320, 40], [308, 44, 320, 42, "paddedX"], [308, 51, 320, 49], [308, 53, 320, 51, "paddedY"], [308, 60, 320, 58], [308, 61, 320, 59], [309, 10, 321, 8], [309, 11, 321, 9], [309, 12, 321, 10], [310, 8, 322, 6], [310, 9, 322, 7], [310, 15, 322, 13], [311, 10, 323, 8, "console"], [311, 17, 323, 15], [311, 18, 323, 16, "log"], [311, 21, 323, 19], [311, 22, 323, 20], [311, 88, 323, 86], [311, 89, 323, 87], [312, 8, 324, 6], [313, 8, 326, 6, "setProcessingProgress"], [313, 29, 326, 27], [313, 30, 326, 28], [313, 32, 326, 30], [313, 33, 326, 31], [315, 8, 328, 6], [316, 8, 329, 6], [316, 14, 329, 12, "blurredImageBlob"], [316, 30, 329, 28], [316, 33, 329, 31], [316, 39, 329, 37], [316, 43, 329, 41, "Promise"], [316, 50, 329, 48], [316, 51, 329, 56, "resolve"], [316, 58, 329, 63], [316, 62, 329, 68], [317, 10, 330, 8, "canvas"], [317, 16, 330, 14], [317, 17, 330, 15, "toBlob"], [317, 23, 330, 21], [317, 24, 330, 23, "blob"], [317, 28, 330, 27], [317, 32, 330, 32, "resolve"], [317, 39, 330, 39], [317, 40, 330, 40, "blob"], [317, 44, 330, 45], [317, 45, 330, 46], [317, 47, 330, 48], [317, 59, 330, 60], [317, 61, 330, 62], [317, 64, 330, 65], [317, 65, 330, 66], [318, 8, 331, 6], [318, 9, 331, 7], [318, 10, 331, 8], [319, 8, 333, 6], [319, 14, 333, 12, "blurredImageUrl"], [319, 29, 333, 27], [319, 32, 333, 30, "URL"], [319, 35, 333, 33], [319, 36, 333, 34, "createObjectURL"], [319, 51, 333, 49], [319, 52, 333, 50, "blurredImageBlob"], [319, 68, 333, 66], [319, 69, 333, 67], [320, 8, 335, 6, "setProcessingProgress"], [320, 29, 335, 27], [320, 30, 335, 28], [320, 33, 335, 31], [320, 34, 335, 32], [322, 8, 337, 6], [323, 8, 338, 6], [323, 14, 338, 12, "completeProcessing"], [323, 32, 338, 30], [323, 33, 338, 31, "blurredImageUrl"], [323, 48, 338, 46], [323, 49, 338, 47], [324, 6, 340, 4], [324, 7, 340, 5], [324, 8, 340, 6], [324, 15, 340, 13, "error"], [324, 20, 340, 18], [324, 22, 340, 20], [325, 8, 341, 6, "console"], [325, 15, 341, 13], [325, 16, 341, 14, "error"], [325, 21, 341, 19], [325, 22, 341, 20], [325, 57, 341, 55], [325, 59, 341, 57, "error"], [325, 64, 341, 62], [325, 65, 341, 63], [326, 8, 342, 6, "setErrorMessage"], [326, 23, 342, 21], [326, 24, 342, 22], [326, 50, 342, 48], [326, 51, 342, 49], [327, 8, 343, 6, "setProcessingState"], [327, 26, 343, 24], [327, 27, 343, 25], [327, 34, 343, 32], [327, 35, 343, 33], [328, 6, 344, 4], [329, 4, 345, 2], [329, 5, 345, 3], [331, 4, 347, 2], [332, 4, 348, 2], [332, 10, 348, 8, "completeProcessing"], [332, 28, 348, 26], [332, 31, 348, 29], [332, 37, 348, 36, "blurredImageUrl"], [332, 52, 348, 59], [332, 56, 348, 64], [333, 6, 349, 4], [333, 10, 349, 8], [334, 8, 350, 6, "setProcessingState"], [334, 26, 350, 24], [334, 27, 350, 25], [334, 37, 350, 35], [334, 38, 350, 36], [336, 8, 352, 6], [337, 8, 353, 6], [337, 14, 353, 12, "timestamp"], [337, 23, 353, 21], [337, 26, 353, 24, "Date"], [337, 30, 353, 28], [337, 31, 353, 29, "now"], [337, 34, 353, 32], [337, 35, 353, 33], [337, 36, 353, 34], [338, 8, 354, 6], [338, 14, 354, 12, "result"], [338, 20, 354, 18], [338, 23, 354, 21], [339, 10, 355, 8, "imageUrl"], [339, 18, 355, 16], [339, 20, 355, 18, "blurredImageUrl"], [339, 35, 355, 33], [340, 10, 356, 8, "localUri"], [340, 18, 356, 16], [340, 20, 356, 18, "blurredImageUrl"], [340, 35, 356, 33], [341, 10, 357, 8, "challengeCode"], [341, 23, 357, 21], [341, 25, 357, 23, "challengeCode"], [341, 38, 357, 36], [341, 42, 357, 40], [341, 44, 357, 42], [342, 10, 358, 8, "timestamp"], [342, 19, 358, 17], [343, 10, 359, 8, "jobId"], [343, 15, 359, 13], [343, 17, 359, 15], [343, 27, 359, 25, "timestamp"], [343, 36, 359, 34], [343, 38, 359, 36], [344, 10, 360, 8, "status"], [344, 16, 360, 14], [344, 18, 360, 16], [345, 8, 361, 6], [345, 9, 361, 7], [346, 8, 363, 6, "console"], [346, 15, 363, 13], [346, 16, 363, 14, "log"], [346, 19, 363, 17], [346, 20, 363, 18], [346, 58, 363, 56], [346, 60, 363, 58, "result"], [346, 66, 363, 64], [346, 67, 363, 65], [348, 8, 365, 6], [349, 8, 366, 6, "onComplete"], [349, 18, 366, 16], [349, 19, 366, 17, "result"], [349, 25, 366, 23], [349, 26, 366, 24], [350, 6, 368, 4], [350, 7, 368, 5], [350, 8, 368, 6], [350, 15, 368, 13, "error"], [350, 20, 368, 18], [350, 22, 368, 20], [351, 8, 369, 6, "console"], [351, 15, 369, 13], [351, 16, 369, 14, "error"], [351, 21, 369, 19], [351, 22, 369, 20], [351, 57, 369, 55], [351, 59, 369, 57, "error"], [351, 64, 369, 62], [351, 65, 369, 63], [352, 8, 370, 6, "setErrorMessage"], [352, 23, 370, 21], [352, 24, 370, 22], [352, 56, 370, 54], [352, 57, 370, 55], [353, 8, 371, 6, "setProcessingState"], [353, 26, 371, 24], [353, 27, 371, 25], [353, 34, 371, 32], [353, 35, 371, 33], [354, 6, 372, 4], [355, 4, 373, 2], [355, 5, 373, 3], [357, 4, 375, 2], [358, 4, 376, 2], [358, 10, 376, 8, "triggerServerProcessing"], [358, 33, 376, 31], [358, 36, 376, 34], [358, 42, 376, 34, "triggerServerProcessing"], [358, 43, 376, 41, "privateImageUrl"], [358, 58, 376, 64], [358, 60, 376, 66, "timestamp"], [358, 69, 376, 83], [358, 74, 376, 88], [359, 6, 377, 4], [359, 10, 377, 8], [360, 8, 378, 6, "console"], [360, 15, 378, 13], [360, 16, 378, 14, "log"], [360, 19, 378, 17], [360, 20, 378, 18], [360, 74, 378, 72], [360, 76, 378, 74, "privateImageUrl"], [360, 91, 378, 89], [360, 92, 378, 90], [361, 8, 379, 6, "setProcessingState"], [361, 26, 379, 24], [361, 27, 379, 25], [361, 39, 379, 37], [361, 40, 379, 38], [362, 8, 380, 6, "setProcessingProgress"], [362, 29, 380, 27], [362, 30, 380, 28], [362, 32, 380, 30], [362, 33, 380, 31], [363, 8, 382, 6], [363, 14, 382, 12, "requestBody"], [363, 25, 382, 23], [363, 28, 382, 26], [364, 10, 383, 8, "imageUrl"], [364, 18, 383, 16], [364, 20, 383, 18, "privateImageUrl"], [364, 35, 383, 33], [365, 10, 384, 8, "userId"], [365, 16, 384, 14], [366, 10, 385, 8, "requestId"], [366, 19, 385, 17], [367, 10, 386, 8, "timestamp"], [367, 19, 386, 17], [368, 10, 387, 8, "platform"], [368, 18, 387, 16], [368, 20, 387, 18], [369, 8, 388, 6], [369, 9, 388, 7], [370, 8, 390, 6, "console"], [370, 15, 390, 13], [370, 16, 390, 14, "log"], [370, 19, 390, 17], [370, 20, 390, 18], [370, 65, 390, 63], [370, 67, 390, 65, "requestBody"], [370, 78, 390, 76], [370, 79, 390, 77], [372, 8, 392, 6], [373, 8, 393, 6], [373, 14, 393, 12, "response"], [373, 22, 393, 20], [373, 25, 393, 23], [373, 31, 393, 29, "fetch"], [373, 36, 393, 34], [373, 37, 393, 35], [373, 40, 393, 38, "API_BASE_URL"], [373, 52, 393, 50], [373, 72, 393, 70], [373, 74, 393, 72], [374, 10, 394, 8, "method"], [374, 16, 394, 14], [374, 18, 394, 16], [374, 24, 394, 22], [375, 10, 395, 8, "headers"], [375, 17, 395, 15], [375, 19, 395, 17], [376, 12, 396, 10], [376, 26, 396, 24], [376, 28, 396, 26], [376, 46, 396, 44], [377, 12, 397, 10], [377, 27, 397, 25], [377, 29, 397, 27], [377, 39, 397, 37], [377, 45, 397, 43, "getAuthToken"], [377, 57, 397, 55], [377, 58, 397, 56], [377, 59, 397, 57], [378, 10, 398, 8], [378, 11, 398, 9], [379, 10, 399, 8, "body"], [379, 14, 399, 12], [379, 16, 399, 14, "JSON"], [379, 20, 399, 18], [379, 21, 399, 19, "stringify"], [379, 30, 399, 28], [379, 31, 399, 29, "requestBody"], [379, 42, 399, 40], [380, 8, 400, 6], [380, 9, 400, 7], [380, 10, 400, 8], [381, 8, 402, 6], [381, 12, 402, 10], [381, 13, 402, 11, "response"], [381, 21, 402, 19], [381, 22, 402, 20, "ok"], [381, 24, 402, 22], [381, 26, 402, 24], [382, 10, 403, 8], [382, 16, 403, 14, "errorText"], [382, 25, 403, 23], [382, 28, 403, 26], [382, 34, 403, 32, "response"], [382, 42, 403, 40], [382, 43, 403, 41, "text"], [382, 47, 403, 45], [382, 48, 403, 46], [382, 49, 403, 47], [383, 10, 404, 8, "console"], [383, 17, 404, 15], [383, 18, 404, 16, "error"], [383, 23, 404, 21], [383, 24, 404, 22], [383, 68, 404, 66], [383, 70, 404, 68, "response"], [383, 78, 404, 76], [383, 79, 404, 77, "status"], [383, 85, 404, 83], [383, 87, 404, 85, "errorText"], [383, 96, 404, 94], [383, 97, 404, 95], [384, 10, 405, 8], [384, 16, 405, 14], [384, 20, 405, 18, "Error"], [384, 25, 405, 23], [384, 26, 405, 24], [384, 48, 405, 46, "response"], [384, 56, 405, 54], [384, 57, 405, 55, "status"], [384, 63, 405, 61], [384, 67, 405, 65, "response"], [384, 75, 405, 73], [384, 76, 405, 74, "statusText"], [384, 86, 405, 84], [384, 88, 405, 86], [384, 89, 405, 87], [385, 8, 406, 6], [386, 8, 408, 6], [386, 14, 408, 12, "result"], [386, 20, 408, 18], [386, 23, 408, 21], [386, 29, 408, 27, "response"], [386, 37, 408, 35], [386, 38, 408, 36, "json"], [386, 42, 408, 40], [386, 43, 408, 41], [386, 44, 408, 42], [387, 8, 409, 6, "console"], [387, 15, 409, 13], [387, 16, 409, 14, "log"], [387, 19, 409, 17], [387, 20, 409, 18], [387, 68, 409, 66], [387, 70, 409, 68, "result"], [387, 76, 409, 74], [387, 77, 409, 75], [388, 8, 411, 6], [388, 12, 411, 10], [388, 13, 411, 11, "result"], [388, 19, 411, 17], [388, 20, 411, 18, "jobId"], [388, 25, 411, 23], [388, 27, 411, 25], [389, 10, 412, 8], [389, 16, 412, 14], [389, 20, 412, 18, "Error"], [389, 25, 412, 23], [389, 26, 412, 24], [389, 70, 412, 68], [389, 71, 412, 69], [390, 8, 413, 6], [392, 8, 415, 6], [393, 8, 416, 6], [393, 14, 416, 12, "pollForCompletion"], [393, 31, 416, 29], [393, 32, 416, 30, "result"], [393, 38, 416, 36], [393, 39, 416, 37, "jobId"], [393, 44, 416, 42], [393, 46, 416, 44, "timestamp"], [393, 55, 416, 53], [393, 56, 416, 54], [394, 6, 417, 4], [394, 7, 417, 5], [394, 8, 417, 6], [394, 15, 417, 13, "error"], [394, 20, 417, 18], [394, 22, 417, 20], [395, 8, 418, 6, "console"], [395, 15, 418, 13], [395, 16, 418, 14, "error"], [395, 21, 418, 19], [395, 22, 418, 20], [395, 57, 418, 55], [395, 59, 418, 57, "error"], [395, 64, 418, 62], [395, 65, 418, 63], [396, 8, 419, 6, "setErrorMessage"], [396, 23, 419, 21], [396, 24, 419, 22], [396, 52, 419, 50, "error"], [396, 57, 419, 55], [396, 58, 419, 56, "message"], [396, 65, 419, 63], [396, 67, 419, 65], [396, 68, 419, 66], [397, 8, 420, 6, "setProcessingState"], [397, 26, 420, 24], [397, 27, 420, 25], [397, 34, 420, 32], [397, 35, 420, 33], [398, 6, 421, 4], [399, 4, 422, 2], [399, 5, 422, 3], [400, 4, 423, 2], [401, 4, 424, 2], [401, 10, 424, 8, "pollForCompletion"], [401, 27, 424, 25], [401, 30, 424, 28], [401, 36, 424, 28, "pollForCompletion"], [401, 37, 424, 35, "jobId"], [401, 42, 424, 48], [401, 44, 424, 50, "timestamp"], [401, 53, 424, 67], [401, 55, 424, 69, "attempts"], [401, 63, 424, 77], [401, 66, 424, 80], [401, 67, 424, 81], [401, 72, 424, 86], [402, 6, 425, 4], [402, 12, 425, 10, "MAX_ATTEMPTS"], [402, 24, 425, 22], [402, 27, 425, 25], [402, 29, 425, 27], [402, 30, 425, 28], [402, 31, 425, 29], [403, 6, 426, 4], [403, 12, 426, 10, "POLL_INTERVAL"], [403, 25, 426, 23], [403, 28, 426, 26], [403, 32, 426, 30], [403, 33, 426, 31], [403, 34, 426, 32], [405, 6, 428, 4, "console"], [405, 13, 428, 11], [405, 14, 428, 12, "log"], [405, 17, 428, 15], [405, 18, 428, 16], [405, 53, 428, 51, "attempts"], [405, 61, 428, 59], [405, 64, 428, 62], [405, 65, 428, 63], [405, 69, 428, 67, "MAX_ATTEMPTS"], [405, 81, 428, 79], [405, 93, 428, 91, "jobId"], [405, 98, 428, 96], [405, 100, 428, 98], [405, 101, 428, 99], [406, 6, 430, 4], [406, 10, 430, 8, "attempts"], [406, 18, 430, 16], [406, 22, 430, 20, "MAX_ATTEMPTS"], [406, 34, 430, 32], [406, 36, 430, 34], [407, 8, 431, 6, "console"], [407, 15, 431, 13], [407, 16, 431, 14, "error"], [407, 21, 431, 19], [407, 22, 431, 20], [407, 75, 431, 73], [407, 76, 431, 74], [408, 8, 432, 6, "setErrorMessage"], [408, 23, 432, 21], [408, 24, 432, 22], [408, 63, 432, 61], [408, 64, 432, 62], [409, 8, 433, 6, "setProcessingState"], [409, 26, 433, 24], [409, 27, 433, 25], [409, 34, 433, 32], [409, 35, 433, 33], [410, 8, 434, 6], [411, 6, 435, 4], [412, 6, 437, 4], [412, 10, 437, 8], [413, 8, 438, 6], [413, 14, 438, 12, "response"], [413, 22, 438, 20], [413, 25, 438, 23], [413, 31, 438, 29, "fetch"], [413, 36, 438, 34], [413, 37, 438, 35], [413, 40, 438, 38, "API_BASE_URL"], [413, 52, 438, 50], [413, 75, 438, 73, "jobId"], [413, 80, 438, 78], [413, 82, 438, 80], [413, 84, 438, 82], [414, 10, 439, 8, "headers"], [414, 17, 439, 15], [414, 19, 439, 17], [415, 12, 440, 10], [415, 27, 440, 25], [415, 29, 440, 27], [415, 39, 440, 37], [415, 45, 440, 43, "getAuthToken"], [415, 57, 440, 55], [415, 58, 440, 56], [415, 59, 440, 57], [416, 10, 441, 8], [417, 8, 442, 6], [417, 9, 442, 7], [417, 10, 442, 8], [418, 8, 444, 6], [418, 12, 444, 10], [418, 13, 444, 11, "response"], [418, 21, 444, 19], [418, 22, 444, 20, "ok"], [418, 24, 444, 22], [418, 26, 444, 24], [419, 10, 445, 8], [419, 16, 445, 14], [419, 20, 445, 18, "Error"], [419, 25, 445, 23], [419, 26, 445, 24], [419, 34, 445, 32, "response"], [419, 42, 445, 40], [419, 43, 445, 41, "status"], [419, 49, 445, 47], [419, 54, 445, 52, "response"], [419, 62, 445, 60], [419, 63, 445, 61, "statusText"], [419, 73, 445, 71], [419, 75, 445, 73], [419, 76, 445, 74], [420, 8, 446, 6], [421, 8, 448, 6], [421, 14, 448, 12, "status"], [421, 20, 448, 18], [421, 23, 448, 21], [421, 29, 448, 27, "response"], [421, 37, 448, 35], [421, 38, 448, 36, "json"], [421, 42, 448, 40], [421, 43, 448, 41], [421, 44, 448, 42], [422, 8, 449, 6, "console"], [422, 15, 449, 13], [422, 16, 449, 14, "log"], [422, 19, 449, 17], [422, 20, 449, 18], [422, 54, 449, 52], [422, 56, 449, 54, "status"], [422, 62, 449, 60], [422, 63, 449, 61], [423, 8, 451, 6], [423, 12, 451, 10, "status"], [423, 18, 451, 16], [423, 19, 451, 17, "status"], [423, 25, 451, 23], [423, 30, 451, 28], [423, 41, 451, 39], [423, 43, 451, 41], [424, 10, 452, 8, "console"], [424, 17, 452, 15], [424, 18, 452, 16, "log"], [424, 21, 452, 19], [424, 22, 452, 20], [424, 73, 452, 71], [424, 74, 452, 72], [425, 10, 453, 8, "setProcessingProgress"], [425, 31, 453, 29], [425, 32, 453, 30], [425, 35, 453, 33], [425, 36, 453, 34], [426, 10, 454, 8, "setProcessingState"], [426, 28, 454, 26], [426, 29, 454, 27], [426, 40, 454, 38], [426, 41, 454, 39], [427, 10, 455, 8], [428, 10, 456, 8], [428, 16, 456, 14, "result"], [428, 22, 456, 20], [428, 25, 456, 23], [429, 12, 457, 10, "imageUrl"], [429, 20, 457, 18], [429, 22, 457, 20, "status"], [429, 28, 457, 26], [429, 29, 457, 27, "publicUrl"], [429, 38, 457, 36], [430, 12, 457, 38], [431, 12, 458, 10, "localUri"], [431, 20, 458, 18], [431, 22, 458, 20, "capturedPhoto"], [431, 35, 458, 33], [431, 39, 458, 37, "status"], [431, 45, 458, 43], [431, 46, 458, 44, "publicUrl"], [431, 55, 458, 53], [432, 12, 458, 55], [433, 12, 459, 10, "challengeCode"], [433, 25, 459, 23], [433, 27, 459, 25, "challengeCode"], [433, 40, 459, 38], [433, 44, 459, 42], [433, 46, 459, 44], [434, 12, 460, 10, "timestamp"], [434, 21, 460, 19], [435, 12, 461, 10, "processingStatus"], [435, 28, 461, 26], [435, 30, 461, 28], [436, 10, 462, 8], [436, 11, 462, 9], [437, 10, 463, 8, "console"], [437, 17, 463, 15], [437, 18, 463, 16, "log"], [437, 21, 463, 19], [437, 22, 463, 20], [437, 57, 463, 55], [437, 59, 463, 57, "result"], [437, 65, 463, 63], [437, 66, 463, 64], [438, 10, 464, 8, "onComplete"], [438, 20, 464, 18], [438, 21, 464, 19, "result"], [438, 27, 464, 25], [438, 28, 464, 26], [439, 10, 465, 8], [440, 8, 466, 6], [440, 9, 466, 7], [440, 15, 466, 13], [440, 19, 466, 17, "status"], [440, 25, 466, 23], [440, 26, 466, 24, "status"], [440, 32, 466, 30], [440, 37, 466, 35], [440, 45, 466, 43], [440, 47, 466, 45], [441, 10, 467, 8, "console"], [441, 17, 467, 15], [441, 18, 467, 16, "error"], [441, 23, 467, 21], [441, 24, 467, 22], [441, 60, 467, 58], [441, 62, 467, 60, "status"], [441, 68, 467, 66], [441, 69, 467, 67, "error"], [441, 74, 467, 72], [441, 75, 467, 73], [442, 10, 468, 8], [442, 16, 468, 14], [442, 20, 468, 18, "Error"], [442, 25, 468, 23], [442, 26, 468, 24, "status"], [442, 32, 468, 30], [442, 33, 468, 31, "error"], [442, 38, 468, 36], [442, 42, 468, 40], [442, 61, 468, 59], [442, 62, 468, 60], [443, 8, 469, 6], [443, 9, 469, 7], [443, 15, 469, 13], [444, 10, 470, 8], [445, 10, 471, 8], [445, 16, 471, 14, "progressValue"], [445, 29, 471, 27], [445, 32, 471, 30], [445, 34, 471, 32], [445, 37, 471, 36, "attempts"], [445, 45, 471, 44], [445, 48, 471, 47, "MAX_ATTEMPTS"], [445, 60, 471, 59], [445, 63, 471, 63], [445, 65, 471, 65], [446, 10, 472, 8, "console"], [446, 17, 472, 15], [446, 18, 472, 16, "log"], [446, 21, 472, 19], [446, 22, 472, 20], [446, 71, 472, 69, "progressValue"], [446, 84, 472, 82], [446, 87, 472, 85], [446, 88, 472, 86], [447, 10, 473, 8, "setProcessingProgress"], [447, 31, 473, 29], [447, 32, 473, 30, "progressValue"], [447, 45, 473, 43], [447, 46, 473, 44], [448, 10, 475, 8, "setTimeout"], [448, 20, 475, 18], [448, 21, 475, 19], [448, 27, 475, 25], [449, 12, 476, 10, "pollForCompletion"], [449, 29, 476, 27], [449, 30, 476, 28, "jobId"], [449, 35, 476, 33], [449, 37, 476, 35, "timestamp"], [449, 46, 476, 44], [449, 48, 476, 46, "attempts"], [449, 56, 476, 54], [449, 59, 476, 57], [449, 60, 476, 58], [449, 61, 476, 59], [450, 10, 477, 8], [450, 11, 477, 9], [450, 13, 477, 11, "POLL_INTERVAL"], [450, 26, 477, 24], [450, 27, 477, 25], [451, 8, 478, 6], [452, 6, 479, 4], [452, 7, 479, 5], [452, 8, 479, 6], [452, 15, 479, 13, "error"], [452, 20, 479, 18], [452, 22, 479, 20], [453, 8, 480, 6, "console"], [453, 15, 480, 13], [453, 16, 480, 14, "error"], [453, 21, 480, 19], [453, 22, 480, 20], [453, 54, 480, 52], [453, 56, 480, 54, "error"], [453, 61, 480, 59], [453, 62, 480, 60], [454, 8, 481, 6, "setErrorMessage"], [454, 23, 481, 21], [454, 24, 481, 22], [454, 62, 481, 60, "error"], [454, 67, 481, 65], [454, 68, 481, 66, "message"], [454, 75, 481, 73], [454, 77, 481, 75], [454, 78, 481, 76], [455, 8, 482, 6, "setProcessingState"], [455, 26, 482, 24], [455, 27, 482, 25], [455, 34, 482, 32], [455, 35, 482, 33], [456, 6, 483, 4], [457, 4, 484, 2], [457, 5, 484, 3], [458, 4, 485, 2], [459, 4, 486, 2], [459, 10, 486, 8, "getAuthToken"], [459, 22, 486, 20], [459, 25, 486, 23], [459, 31, 486, 23, "getAuthToken"], [459, 32, 486, 23], [459, 37, 486, 52], [460, 6, 487, 4], [461, 6, 488, 4], [462, 6, 489, 4], [462, 13, 489, 11], [462, 30, 489, 28], [463, 4, 490, 2], [463, 5, 490, 3], [465, 4, 492, 2], [466, 4, 493, 2], [466, 10, 493, 8, "retryCapture"], [466, 22, 493, 20], [466, 25, 493, 23], [466, 29, 493, 23, "useCallback"], [466, 47, 493, 34], [466, 49, 493, 35], [466, 55, 493, 41], [467, 6, 494, 4, "console"], [467, 13, 494, 11], [467, 14, 494, 12, "log"], [467, 17, 494, 15], [467, 18, 494, 16], [467, 55, 494, 53], [467, 56, 494, 54], [468, 6, 495, 4, "setProcessingState"], [468, 24, 495, 22], [468, 25, 495, 23], [468, 31, 495, 29], [468, 32, 495, 30], [469, 6, 496, 4, "setErrorMessage"], [469, 21, 496, 19], [469, 22, 496, 20], [469, 24, 496, 22], [469, 25, 496, 23], [470, 6, 497, 4, "setCapturedPhoto"], [470, 22, 497, 20], [470, 23, 497, 21], [470, 25, 497, 23], [470, 26, 497, 24], [471, 6, 498, 4, "setProcessingProgress"], [471, 27, 498, 25], [471, 28, 498, 26], [471, 29, 498, 27], [471, 30, 498, 28], [472, 4, 499, 2], [472, 5, 499, 3], [472, 7, 499, 5], [472, 9, 499, 7], [472, 10, 499, 8], [473, 4, 500, 2], [474, 4, 501, 2], [474, 8, 501, 2, "useEffect"], [474, 24, 501, 11], [474, 26, 501, 12], [474, 32, 501, 18], [475, 6, 502, 4, "console"], [475, 13, 502, 11], [475, 14, 502, 12, "log"], [475, 17, 502, 15], [475, 18, 502, 16], [475, 53, 502, 51], [475, 55, 502, 53, "permission"], [475, 65, 502, 63], [475, 66, 502, 64], [476, 6, 503, 4], [476, 10, 503, 8, "permission"], [476, 20, 503, 18], [476, 22, 503, 20], [477, 8, 504, 6, "console"], [477, 15, 504, 13], [477, 16, 504, 14, "log"], [477, 19, 504, 17], [477, 20, 504, 18], [477, 57, 504, 55], [477, 59, 504, 57, "permission"], [477, 69, 504, 67], [477, 70, 504, 68, "granted"], [477, 77, 504, 75], [477, 78, 504, 76], [478, 6, 505, 4], [479, 4, 506, 2], [479, 5, 506, 3], [479, 7, 506, 5], [479, 8, 506, 6, "permission"], [479, 18, 506, 16], [479, 19, 506, 17], [479, 20, 506, 18], [480, 4, 507, 2], [481, 4, 508, 2], [481, 8, 508, 6], [481, 9, 508, 7, "permission"], [481, 19, 508, 17], [481, 21, 508, 19], [482, 6, 509, 4, "console"], [482, 13, 509, 11], [482, 14, 509, 12, "log"], [482, 17, 509, 15], [482, 18, 509, 16], [482, 67, 509, 65], [482, 68, 509, 66], [483, 6, 510, 4], [483, 26, 511, 6], [483, 30, 511, 6, "_jsxDevRuntime"], [483, 44, 511, 6], [483, 45, 511, 6, "jsxDEV"], [483, 51, 511, 6], [483, 53, 511, 7, "_View"], [483, 58, 511, 7], [483, 59, 511, 7, "default"], [483, 66, 511, 11], [484, 8, 511, 12, "style"], [484, 13, 511, 17], [484, 15, 511, 19, "styles"], [484, 21, 511, 25], [484, 22, 511, 26, "container"], [484, 31, 511, 36], [485, 8, 511, 36, "children"], [485, 16, 511, 36], [485, 32, 512, 8], [485, 36, 512, 8, "_jsxDevRuntime"], [485, 50, 512, 8], [485, 51, 512, 8, "jsxDEV"], [485, 57, 512, 8], [485, 59, 512, 9, "_ActivityIndicator"], [485, 77, 512, 9], [485, 78, 512, 9, "default"], [485, 85, 512, 26], [486, 10, 512, 27, "size"], [486, 14, 512, 31], [486, 16, 512, 32], [486, 23, 512, 39], [487, 10, 512, 40, "color"], [487, 15, 512, 45], [487, 17, 512, 46], [488, 8, 512, 55], [489, 10, 512, 55, "fileName"], [489, 18, 512, 55], [489, 20, 512, 55, "_jsxFileName"], [489, 32, 512, 55], [490, 10, 512, 55, "lineNumber"], [490, 20, 512, 55], [491, 10, 512, 55, "columnNumber"], [491, 22, 512, 55], [492, 8, 512, 55], [492, 15, 512, 57], [492, 16, 512, 58], [492, 31, 513, 8], [492, 35, 513, 8, "_jsxDevRuntime"], [492, 49, 513, 8], [492, 50, 513, 8, "jsxDEV"], [492, 56, 513, 8], [492, 58, 513, 9, "_Text"], [492, 63, 513, 9], [492, 64, 513, 9, "default"], [492, 71, 513, 13], [493, 10, 513, 14, "style"], [493, 15, 513, 19], [493, 17, 513, 21, "styles"], [493, 23, 513, 27], [493, 24, 513, 28, "loadingText"], [493, 35, 513, 40], [494, 10, 513, 40, "children"], [494, 18, 513, 40], [494, 20, 513, 41], [495, 8, 513, 58], [496, 10, 513, 58, "fileName"], [496, 18, 513, 58], [496, 20, 513, 58, "_jsxFileName"], [496, 32, 513, 58], [497, 10, 513, 58, "lineNumber"], [497, 20, 513, 58], [498, 10, 513, 58, "columnNumber"], [498, 22, 513, 58], [499, 8, 513, 58], [499, 15, 513, 64], [499, 16, 513, 65], [500, 6, 513, 65], [501, 8, 513, 65, "fileName"], [501, 16, 513, 65], [501, 18, 513, 65, "_jsxFileName"], [501, 30, 513, 65], [502, 8, 513, 65, "lineNumber"], [502, 18, 513, 65], [503, 8, 513, 65, "columnNumber"], [503, 20, 513, 65], [504, 6, 513, 65], [504, 13, 514, 12], [504, 14, 514, 13], [505, 4, 516, 2], [506, 4, 517, 2], [506, 8, 517, 6], [506, 9, 517, 7, "permission"], [506, 19, 517, 17], [506, 20, 517, 18, "granted"], [506, 27, 517, 25], [506, 29, 517, 27], [507, 6, 518, 4, "console"], [507, 13, 518, 11], [507, 14, 518, 12, "log"], [507, 17, 518, 15], [507, 18, 518, 16], [507, 93, 518, 91], [507, 94, 518, 92], [508, 6, 519, 4], [508, 26, 520, 6], [508, 30, 520, 6, "_jsxDevRuntime"], [508, 44, 520, 6], [508, 45, 520, 6, "jsxDEV"], [508, 51, 520, 6], [508, 53, 520, 7, "_View"], [508, 58, 520, 7], [508, 59, 520, 7, "default"], [508, 66, 520, 11], [509, 8, 520, 12, "style"], [509, 13, 520, 17], [509, 15, 520, 19, "styles"], [509, 21, 520, 25], [509, 22, 520, 26, "container"], [509, 31, 520, 36], [510, 8, 520, 36, "children"], [510, 16, 520, 36], [510, 31, 521, 8], [510, 35, 521, 8, "_jsxDevRuntime"], [510, 49, 521, 8], [510, 50, 521, 8, "jsxDEV"], [510, 56, 521, 8], [510, 58, 521, 9, "_View"], [510, 63, 521, 9], [510, 64, 521, 9, "default"], [510, 71, 521, 13], [511, 10, 521, 14, "style"], [511, 15, 521, 19], [511, 17, 521, 21, "styles"], [511, 23, 521, 27], [511, 24, 521, 28, "permissionContent"], [511, 41, 521, 46], [512, 10, 521, 46, "children"], [512, 18, 521, 46], [512, 34, 522, 10], [512, 38, 522, 10, "_jsxDevRuntime"], [512, 52, 522, 10], [512, 53, 522, 10, "jsxDEV"], [512, 59, 522, 10], [512, 61, 522, 11, "_lucideReactNative"], [512, 79, 522, 11], [512, 80, 522, 11, "Camera"], [512, 86, 522, 21], [513, 12, 522, 22, "size"], [513, 16, 522, 26], [513, 18, 522, 28], [513, 20, 522, 31], [514, 12, 522, 32, "color"], [514, 17, 522, 37], [514, 19, 522, 38], [515, 10, 522, 47], [516, 12, 522, 47, "fileName"], [516, 20, 522, 47], [516, 22, 522, 47, "_jsxFileName"], [516, 34, 522, 47], [517, 12, 522, 47, "lineNumber"], [517, 22, 522, 47], [518, 12, 522, 47, "columnNumber"], [518, 24, 522, 47], [519, 10, 522, 47], [519, 17, 522, 49], [519, 18, 522, 50], [519, 33, 523, 10], [519, 37, 523, 10, "_jsxDevRuntime"], [519, 51, 523, 10], [519, 52, 523, 10, "jsxDEV"], [519, 58, 523, 10], [519, 60, 523, 11, "_Text"], [519, 65, 523, 11], [519, 66, 523, 11, "default"], [519, 73, 523, 15], [520, 12, 523, 16, "style"], [520, 17, 523, 21], [520, 19, 523, 23, "styles"], [520, 25, 523, 29], [520, 26, 523, 30, "permissionTitle"], [520, 41, 523, 46], [521, 12, 523, 46, "children"], [521, 20, 523, 46], [521, 22, 523, 47], [522, 10, 523, 73], [523, 12, 523, 73, "fileName"], [523, 20, 523, 73], [523, 22, 523, 73, "_jsxFileName"], [523, 34, 523, 73], [524, 12, 523, 73, "lineNumber"], [524, 22, 523, 73], [525, 12, 523, 73, "columnNumber"], [525, 24, 523, 73], [526, 10, 523, 73], [526, 17, 523, 79], [526, 18, 523, 80], [526, 33, 524, 10], [526, 37, 524, 10, "_jsxDevRuntime"], [526, 51, 524, 10], [526, 52, 524, 10, "jsxDEV"], [526, 58, 524, 10], [526, 60, 524, 11, "_Text"], [526, 65, 524, 11], [526, 66, 524, 11, "default"], [526, 73, 524, 15], [527, 12, 524, 16, "style"], [527, 17, 524, 21], [527, 19, 524, 23, "styles"], [527, 25, 524, 29], [527, 26, 524, 30, "permissionDescription"], [527, 47, 524, 52], [528, 12, 524, 52, "children"], [528, 20, 524, 52], [528, 22, 524, 53], [529, 10, 527, 10], [530, 12, 527, 10, "fileName"], [530, 20, 527, 10], [530, 22, 527, 10, "_jsxFileName"], [530, 34, 527, 10], [531, 12, 527, 10, "lineNumber"], [531, 22, 527, 10], [532, 12, 527, 10, "columnNumber"], [532, 24, 527, 10], [533, 10, 527, 10], [533, 17, 527, 16], [533, 18, 527, 17], [533, 33, 528, 10], [533, 37, 528, 10, "_jsxDevRuntime"], [533, 51, 528, 10], [533, 52, 528, 10, "jsxDEV"], [533, 58, 528, 10], [533, 60, 528, 11, "_TouchableOpacity"], [533, 77, 528, 11], [533, 78, 528, 11, "default"], [533, 85, 528, 27], [534, 12, 528, 28, "onPress"], [534, 19, 528, 35], [534, 21, 528, 37, "requestPermission"], [534, 38, 528, 55], [535, 12, 528, 56, "style"], [535, 17, 528, 61], [535, 19, 528, 63, "styles"], [535, 25, 528, 69], [535, 26, 528, 70, "primaryButton"], [535, 39, 528, 84], [536, 12, 528, 84, "children"], [536, 20, 528, 84], [536, 35, 529, 12], [536, 39, 529, 12, "_jsxDevRuntime"], [536, 53, 529, 12], [536, 54, 529, 12, "jsxDEV"], [536, 60, 529, 12], [536, 62, 529, 13, "_Text"], [536, 67, 529, 13], [536, 68, 529, 13, "default"], [536, 75, 529, 17], [537, 14, 529, 18, "style"], [537, 19, 529, 23], [537, 21, 529, 25, "styles"], [537, 27, 529, 31], [537, 28, 529, 32, "primaryButtonText"], [537, 45, 529, 50], [538, 14, 529, 50, "children"], [538, 22, 529, 50], [538, 24, 529, 51], [539, 12, 529, 67], [540, 14, 529, 67, "fileName"], [540, 22, 529, 67], [540, 24, 529, 67, "_jsxFileName"], [540, 36, 529, 67], [541, 14, 529, 67, "lineNumber"], [541, 24, 529, 67], [542, 14, 529, 67, "columnNumber"], [542, 26, 529, 67], [543, 12, 529, 67], [543, 19, 529, 73], [544, 10, 529, 74], [545, 12, 529, 74, "fileName"], [545, 20, 529, 74], [545, 22, 529, 74, "_jsxFileName"], [545, 34, 529, 74], [546, 12, 529, 74, "lineNumber"], [546, 22, 529, 74], [547, 12, 529, 74, "columnNumber"], [547, 24, 529, 74], [548, 10, 529, 74], [548, 17, 530, 28], [548, 18, 530, 29], [548, 33, 531, 10], [548, 37, 531, 10, "_jsxDevRuntime"], [548, 51, 531, 10], [548, 52, 531, 10, "jsxDEV"], [548, 58, 531, 10], [548, 60, 531, 11, "_TouchableOpacity"], [548, 77, 531, 11], [548, 78, 531, 11, "default"], [548, 85, 531, 27], [549, 12, 531, 28, "onPress"], [549, 19, 531, 35], [549, 21, 531, 37, "onCancel"], [549, 29, 531, 46], [550, 12, 531, 47, "style"], [550, 17, 531, 52], [550, 19, 531, 54, "styles"], [550, 25, 531, 60], [550, 26, 531, 61, "secondaryButton"], [550, 41, 531, 77], [551, 12, 531, 77, "children"], [551, 20, 531, 77], [551, 35, 532, 12], [551, 39, 532, 12, "_jsxDevRuntime"], [551, 53, 532, 12], [551, 54, 532, 12, "jsxDEV"], [551, 60, 532, 12], [551, 62, 532, 13, "_Text"], [551, 67, 532, 13], [551, 68, 532, 13, "default"], [551, 75, 532, 17], [552, 14, 532, 18, "style"], [552, 19, 532, 23], [552, 21, 532, 25, "styles"], [552, 27, 532, 31], [552, 28, 532, 32, "secondaryButtonText"], [552, 47, 532, 52], [553, 14, 532, 52, "children"], [553, 22, 532, 52], [553, 24, 532, 53], [554, 12, 532, 59], [555, 14, 532, 59, "fileName"], [555, 22, 532, 59], [555, 24, 532, 59, "_jsxFileName"], [555, 36, 532, 59], [556, 14, 532, 59, "lineNumber"], [556, 24, 532, 59], [557, 14, 532, 59, "columnNumber"], [557, 26, 532, 59], [558, 12, 532, 59], [558, 19, 532, 65], [559, 10, 532, 66], [560, 12, 532, 66, "fileName"], [560, 20, 532, 66], [560, 22, 532, 66, "_jsxFileName"], [560, 34, 532, 66], [561, 12, 532, 66, "lineNumber"], [561, 22, 532, 66], [562, 12, 532, 66, "columnNumber"], [562, 24, 532, 66], [563, 10, 532, 66], [563, 17, 533, 28], [563, 18, 533, 29], [564, 8, 533, 29], [565, 10, 533, 29, "fileName"], [565, 18, 533, 29], [565, 20, 533, 29, "_jsxFileName"], [565, 32, 533, 29], [566, 10, 533, 29, "lineNumber"], [566, 20, 533, 29], [567, 10, 533, 29, "columnNumber"], [567, 22, 533, 29], [568, 8, 533, 29], [568, 15, 534, 14], [569, 6, 534, 15], [570, 8, 534, 15, "fileName"], [570, 16, 534, 15], [570, 18, 534, 15, "_jsxFileName"], [570, 30, 534, 15], [571, 8, 534, 15, "lineNumber"], [571, 18, 534, 15], [572, 8, 534, 15, "columnNumber"], [572, 20, 534, 15], [573, 6, 534, 15], [573, 13, 535, 12], [573, 14, 535, 13], [574, 4, 537, 2], [575, 4, 538, 2], [576, 4, 539, 2, "console"], [576, 11, 539, 9], [576, 12, 539, 10, "log"], [576, 15, 539, 13], [576, 16, 539, 14], [576, 55, 539, 53], [576, 56, 539, 54], [577, 4, 541, 2], [577, 24, 542, 4], [577, 28, 542, 4, "_jsxDevRuntime"], [577, 42, 542, 4], [577, 43, 542, 4, "jsxDEV"], [577, 49, 542, 4], [577, 51, 542, 5, "_View"], [577, 56, 542, 5], [577, 57, 542, 5, "default"], [577, 64, 542, 9], [578, 6, 542, 10, "style"], [578, 11, 542, 15], [578, 13, 542, 17, "styles"], [578, 19, 542, 23], [578, 20, 542, 24, "container"], [578, 29, 542, 34], [579, 6, 542, 34, "children"], [579, 14, 542, 34], [579, 30, 544, 6], [579, 34, 544, 6, "_jsxDevRuntime"], [579, 48, 544, 6], [579, 49, 544, 6, "jsxDEV"], [579, 55, 544, 6], [579, 57, 544, 7, "_View"], [579, 62, 544, 7], [579, 63, 544, 7, "default"], [579, 70, 544, 11], [580, 8, 544, 12, "style"], [580, 13, 544, 17], [580, 15, 544, 19, "styles"], [580, 21, 544, 25], [580, 22, 544, 26, "cameraContainer"], [580, 37, 544, 42], [581, 8, 544, 43, "id"], [581, 10, 544, 45], [581, 12, 544, 46], [581, 29, 544, 63], [582, 8, 544, 63, "children"], [582, 16, 544, 63], [582, 32, 545, 8], [582, 36, 545, 8, "_jsxDevRuntime"], [582, 50, 545, 8], [582, 51, 545, 8, "jsxDEV"], [582, 57, 545, 8], [582, 59, 545, 9, "_expoCamera"], [582, 70, 545, 9], [582, 71, 545, 9, "CameraView"], [582, 81, 545, 19], [583, 10, 546, 10, "ref"], [583, 13, 546, 13], [583, 15, 546, 15, "cameraRef"], [583, 24, 546, 25], [584, 10, 547, 10, "style"], [584, 15, 547, 15], [584, 17, 547, 17], [584, 18, 547, 18, "styles"], [584, 24, 547, 24], [584, 25, 547, 25, "camera"], [584, 31, 547, 31], [584, 33, 547, 33], [585, 12, 547, 35, "backgroundColor"], [585, 27, 547, 50], [585, 29, 547, 52], [586, 10, 547, 62], [586, 11, 547, 63], [586, 12, 547, 65], [587, 10, 548, 10, "facing"], [587, 16, 548, 16], [587, 18, 548, 17], [587, 24, 548, 23], [588, 10, 549, 10, "onLayout"], [588, 18, 549, 18], [588, 20, 549, 21, "e"], [588, 21, 549, 22], [588, 25, 549, 27], [589, 12, 550, 12, "console"], [589, 19, 550, 19], [589, 20, 550, 20, "log"], [589, 23, 550, 23], [589, 24, 550, 24], [589, 56, 550, 56], [589, 58, 550, 58, "e"], [589, 59, 550, 59], [589, 60, 550, 60, "nativeEvent"], [589, 71, 550, 71], [589, 72, 550, 72, "layout"], [589, 78, 550, 78], [589, 79, 550, 79], [590, 12, 551, 12, "setViewSize"], [590, 23, 551, 23], [590, 24, 551, 24], [591, 14, 551, 26, "width"], [591, 19, 551, 31], [591, 21, 551, 33, "e"], [591, 22, 551, 34], [591, 23, 551, 35, "nativeEvent"], [591, 34, 551, 46], [591, 35, 551, 47, "layout"], [591, 41, 551, 53], [591, 42, 551, 54, "width"], [591, 47, 551, 59], [592, 14, 551, 61, "height"], [592, 20, 551, 67], [592, 22, 551, 69, "e"], [592, 23, 551, 70], [592, 24, 551, 71, "nativeEvent"], [592, 35, 551, 82], [592, 36, 551, 83, "layout"], [592, 42, 551, 89], [592, 43, 551, 90, "height"], [593, 12, 551, 97], [593, 13, 551, 98], [593, 14, 551, 99], [594, 10, 552, 10], [594, 11, 552, 12], [595, 10, 553, 10, "onCameraReady"], [595, 23, 553, 23], [595, 25, 553, 25, "onCameraReady"], [595, 26, 553, 25], [595, 31, 553, 31], [596, 12, 554, 12, "console"], [596, 19, 554, 19], [596, 20, 554, 20, "log"], [596, 23, 554, 23], [596, 24, 554, 24], [596, 55, 554, 55], [596, 56, 554, 56], [597, 12, 555, 12, "setIsCameraReady"], [597, 28, 555, 28], [597, 29, 555, 29], [597, 33, 555, 33], [597, 34, 555, 34], [597, 35, 555, 35], [597, 36, 555, 36], [598, 10, 556, 10], [598, 11, 556, 12], [599, 10, 557, 10, "onMountError"], [599, 22, 557, 22], [599, 24, 557, 25, "error"], [599, 29, 557, 30], [599, 33, 557, 35], [600, 12, 558, 12, "console"], [600, 19, 558, 19], [600, 20, 558, 20, "error"], [600, 25, 558, 25], [600, 26, 558, 26], [600, 63, 558, 63], [600, 65, 558, 65, "error"], [600, 70, 558, 70], [600, 71, 558, 71], [601, 12, 559, 12, "setErrorMessage"], [601, 27, 559, 27], [601, 28, 559, 28], [601, 57, 559, 57], [601, 58, 559, 58], [602, 12, 560, 12, "setProcessingState"], [602, 30, 560, 30], [602, 31, 560, 31], [602, 38, 560, 38], [602, 39, 560, 39], [603, 10, 561, 10], [604, 8, 561, 12], [605, 10, 561, 12, "fileName"], [605, 18, 561, 12], [605, 20, 561, 12, "_jsxFileName"], [605, 32, 561, 12], [606, 10, 561, 12, "lineNumber"], [606, 20, 561, 12], [607, 10, 561, 12, "columnNumber"], [607, 22, 561, 12], [608, 8, 561, 12], [608, 15, 562, 9], [608, 16, 562, 10], [608, 18, 564, 9], [608, 19, 564, 10, "isCameraReady"], [608, 32, 564, 23], [608, 49, 565, 10], [608, 53, 565, 10, "_jsxDevRuntime"], [608, 67, 565, 10], [608, 68, 565, 10, "jsxDEV"], [608, 74, 565, 10], [608, 76, 565, 11, "_View"], [608, 81, 565, 11], [608, 82, 565, 11, "default"], [608, 89, 565, 15], [609, 10, 565, 16, "style"], [609, 15, 565, 21], [609, 17, 565, 23], [609, 18, 565, 24, "StyleSheet"], [609, 37, 565, 34], [609, 38, 565, 35, "absoluteFill"], [609, 50, 565, 47], [609, 52, 565, 49], [610, 12, 565, 51, "backgroundColor"], [610, 27, 565, 66], [610, 29, 565, 68], [610, 49, 565, 88], [611, 12, 565, 90, "justifyContent"], [611, 26, 565, 104], [611, 28, 565, 106], [611, 36, 565, 114], [612, 12, 565, 116, "alignItems"], [612, 22, 565, 126], [612, 24, 565, 128], [612, 32, 565, 136], [613, 12, 565, 138, "zIndex"], [613, 18, 565, 144], [613, 20, 565, 146], [614, 10, 565, 151], [614, 11, 565, 152], [614, 12, 565, 154], [615, 10, 565, 154, "children"], [615, 18, 565, 154], [615, 33, 566, 12], [615, 37, 566, 12, "_jsxDevRuntime"], [615, 51, 566, 12], [615, 52, 566, 12, "jsxDEV"], [615, 58, 566, 12], [615, 60, 566, 13, "_View"], [615, 65, 566, 13], [615, 66, 566, 13, "default"], [615, 73, 566, 17], [616, 12, 566, 18, "style"], [616, 17, 566, 23], [616, 19, 566, 25], [617, 14, 566, 27, "backgroundColor"], [617, 29, 566, 42], [617, 31, 566, 44], [617, 51, 566, 64], [618, 14, 566, 66, "padding"], [618, 21, 566, 73], [618, 23, 566, 75], [618, 25, 566, 77], [619, 14, 566, 79, "borderRadius"], [619, 26, 566, 91], [619, 28, 566, 93], [619, 30, 566, 95], [620, 14, 566, 97, "alignItems"], [620, 24, 566, 107], [620, 26, 566, 109], [621, 12, 566, 118], [621, 13, 566, 120], [622, 12, 566, 120, "children"], [622, 20, 566, 120], [622, 36, 567, 14], [622, 40, 567, 14, "_jsxDevRuntime"], [622, 54, 567, 14], [622, 55, 567, 14, "jsxDEV"], [622, 61, 567, 14], [622, 63, 567, 15, "_ActivityIndicator"], [622, 81, 567, 15], [622, 82, 567, 15, "default"], [622, 89, 567, 32], [623, 14, 567, 33, "size"], [623, 18, 567, 37], [623, 20, 567, 38], [623, 27, 567, 45], [624, 14, 567, 46, "color"], [624, 19, 567, 51], [624, 21, 567, 52], [624, 30, 567, 61], [625, 14, 567, 62, "style"], [625, 19, 567, 67], [625, 21, 567, 69], [626, 16, 567, 71, "marginBottom"], [626, 28, 567, 83], [626, 30, 567, 85], [627, 14, 567, 88], [628, 12, 567, 90], [629, 14, 567, 90, "fileName"], [629, 22, 567, 90], [629, 24, 567, 90, "_jsxFileName"], [629, 36, 567, 90], [630, 14, 567, 90, "lineNumber"], [630, 24, 567, 90], [631, 14, 567, 90, "columnNumber"], [631, 26, 567, 90], [632, 12, 567, 90], [632, 19, 567, 92], [632, 20, 567, 93], [632, 35, 568, 14], [632, 39, 568, 14, "_jsxDevRuntime"], [632, 53, 568, 14], [632, 54, 568, 14, "jsxDEV"], [632, 60, 568, 14], [632, 62, 568, 15, "_Text"], [632, 67, 568, 15], [632, 68, 568, 15, "default"], [632, 75, 568, 19], [633, 14, 568, 20, "style"], [633, 19, 568, 25], [633, 21, 568, 27], [634, 16, 568, 29, "color"], [634, 21, 568, 34], [634, 23, 568, 36], [634, 29, 568, 42], [635, 16, 568, 44, "fontSize"], [635, 24, 568, 52], [635, 26, 568, 54], [635, 28, 568, 56], [636, 16, 568, 58, "fontWeight"], [636, 26, 568, 68], [636, 28, 568, 70], [637, 14, 568, 76], [637, 15, 568, 78], [638, 14, 568, 78, "children"], [638, 22, 568, 78], [638, 24, 568, 79], [639, 12, 568, 101], [640, 14, 568, 101, "fileName"], [640, 22, 568, 101], [640, 24, 568, 101, "_jsxFileName"], [640, 36, 568, 101], [641, 14, 568, 101, "lineNumber"], [641, 24, 568, 101], [642, 14, 568, 101, "columnNumber"], [642, 26, 568, 101], [643, 12, 568, 101], [643, 19, 568, 107], [643, 20, 568, 108], [643, 35, 569, 14], [643, 39, 569, 14, "_jsxDevRuntime"], [643, 53, 569, 14], [643, 54, 569, 14, "jsxDEV"], [643, 60, 569, 14], [643, 62, 569, 15, "_Text"], [643, 67, 569, 15], [643, 68, 569, 15, "default"], [643, 75, 569, 19], [644, 14, 569, 20, "style"], [644, 19, 569, 25], [644, 21, 569, 27], [645, 16, 569, 29, "color"], [645, 21, 569, 34], [645, 23, 569, 36], [645, 32, 569, 45], [646, 16, 569, 47, "fontSize"], [646, 24, 569, 55], [646, 26, 569, 57], [646, 28, 569, 59], [647, 16, 569, 61, "marginTop"], [647, 25, 569, 70], [647, 27, 569, 72], [648, 14, 569, 74], [648, 15, 569, 76], [649, 14, 569, 76, "children"], [649, 22, 569, 76], [649, 24, 569, 77], [650, 12, 569, 88], [651, 14, 569, 88, "fileName"], [651, 22, 569, 88], [651, 24, 569, 88, "_jsxFileName"], [651, 36, 569, 88], [652, 14, 569, 88, "lineNumber"], [652, 24, 569, 88], [653, 14, 569, 88, "columnNumber"], [653, 26, 569, 88], [654, 12, 569, 88], [654, 19, 569, 94], [654, 20, 569, 95], [655, 10, 569, 95], [656, 12, 569, 95, "fileName"], [656, 20, 569, 95], [656, 22, 569, 95, "_jsxFileName"], [656, 34, 569, 95], [657, 12, 569, 95, "lineNumber"], [657, 22, 569, 95], [658, 12, 569, 95, "columnNumber"], [658, 24, 569, 95], [659, 10, 569, 95], [659, 17, 570, 18], [660, 8, 570, 19], [661, 10, 570, 19, "fileName"], [661, 18, 570, 19], [661, 20, 570, 19, "_jsxFileName"], [661, 32, 570, 19], [662, 10, 570, 19, "lineNumber"], [662, 20, 570, 19], [663, 10, 570, 19, "columnNumber"], [663, 22, 570, 19], [664, 8, 570, 19], [664, 15, 571, 16], [664, 16, 572, 9], [664, 18, 575, 9, "isCameraReady"], [664, 31, 575, 22], [664, 35, 575, 26, "previewBlurEnabled"], [664, 53, 575, 44], [664, 57, 575, 48, "viewSize"], [664, 65, 575, 56], [664, 66, 575, 57, "width"], [664, 71, 575, 62], [664, 74, 575, 65], [664, 75, 575, 66], [664, 92, 576, 10], [664, 96, 576, 10, "_jsxDevRuntime"], [664, 110, 576, 10], [664, 111, 576, 10, "jsxDEV"], [664, 117, 576, 10], [664, 119, 576, 10, "_jsxDevRuntime"], [664, 133, 576, 10], [664, 134, 576, 10, "Fragment"], [664, 142, 576, 10], [665, 10, 576, 10, "children"], [665, 18, 576, 10], [665, 34, 578, 12], [665, 38, 578, 12, "_jsxDevRuntime"], [665, 52, 578, 12], [665, 53, 578, 12, "jsxDEV"], [665, 59, 578, 12], [665, 61, 578, 13, "_LiveFaceCanvas"], [665, 76, 578, 13], [665, 77, 578, 13, "default"], [665, 84, 578, 27], [666, 12, 578, 28, "containerId"], [666, 23, 578, 39], [666, 25, 578, 40], [666, 42, 578, 57], [667, 12, 578, 58, "width"], [667, 17, 578, 63], [667, 19, 578, 65, "viewSize"], [667, 27, 578, 73], [667, 28, 578, 74, "width"], [667, 33, 578, 80], [668, 12, 578, 81, "height"], [668, 18, 578, 87], [668, 20, 578, 89, "viewSize"], [668, 28, 578, 97], [668, 29, 578, 98, "height"], [669, 10, 578, 105], [670, 12, 578, 105, "fileName"], [670, 20, 578, 105], [670, 22, 578, 105, "_jsxFileName"], [670, 34, 578, 105], [671, 12, 578, 105, "lineNumber"], [671, 22, 578, 105], [672, 12, 578, 105, "columnNumber"], [672, 24, 578, 105], [673, 10, 578, 105], [673, 17, 578, 107], [673, 18, 578, 108], [673, 33, 579, 12], [673, 37, 579, 12, "_jsxDevRuntime"], [673, 51, 579, 12], [673, 52, 579, 12, "jsxDEV"], [673, 58, 579, 12], [673, 60, 579, 13, "_View"], [673, 65, 579, 13], [673, 66, 579, 13, "default"], [673, 73, 579, 17], [674, 12, 579, 18, "style"], [674, 17, 579, 23], [674, 19, 579, 25], [674, 20, 579, 26, "StyleSheet"], [674, 39, 579, 36], [674, 40, 579, 37, "absoluteFill"], [674, 52, 579, 49], [674, 54, 579, 51], [675, 14, 579, 53, "pointerEvents"], [675, 27, 579, 66], [675, 29, 579, 68], [676, 12, 579, 75], [676, 13, 579, 76], [676, 14, 579, 78], [677, 12, 579, 78, "children"], [677, 20, 579, 78], [677, 36, 581, 12], [677, 40, 581, 12, "_jsxDevRuntime"], [677, 54, 581, 12], [677, 55, 581, 12, "jsxDEV"], [677, 61, 581, 12], [677, 63, 581, 13, "_expoBlur"], [677, 72, 581, 13], [677, 73, 581, 13, "BlurView"], [677, 81, 581, 21], [678, 14, 581, 22, "intensity"], [678, 23, 581, 31], [678, 25, 581, 33], [678, 27, 581, 36], [679, 14, 581, 37, "tint"], [679, 18, 581, 41], [679, 20, 581, 42], [679, 26, 581, 48], [680, 14, 581, 49, "style"], [680, 19, 581, 54], [680, 21, 581, 56], [680, 22, 581, 57, "styles"], [680, 28, 581, 63], [680, 29, 581, 64, "blurZone"], [680, 37, 581, 72], [680, 39, 581, 74], [681, 16, 582, 14, "left"], [681, 20, 582, 18], [681, 22, 582, 20], [681, 23, 582, 21], [682, 16, 583, 14, "top"], [682, 19, 583, 17], [682, 21, 583, 19, "viewSize"], [682, 29, 583, 27], [682, 30, 583, 28, "height"], [682, 36, 583, 34], [682, 39, 583, 37], [682, 42, 583, 40], [683, 16, 584, 14, "width"], [683, 21, 584, 19], [683, 23, 584, 21, "viewSize"], [683, 31, 584, 29], [683, 32, 584, 30, "width"], [683, 37, 584, 35], [684, 16, 585, 14, "height"], [684, 22, 585, 20], [684, 24, 585, 22, "viewSize"], [684, 32, 585, 30], [684, 33, 585, 31, "height"], [684, 39, 585, 37], [684, 42, 585, 40], [684, 46, 585, 44], [685, 16, 586, 14, "borderRadius"], [685, 28, 586, 26], [685, 30, 586, 28], [686, 14, 587, 12], [686, 15, 587, 13], [687, 12, 587, 15], [688, 14, 587, 15, "fileName"], [688, 22, 587, 15], [688, 24, 587, 15, "_jsxFileName"], [688, 36, 587, 15], [689, 14, 587, 15, "lineNumber"], [689, 24, 587, 15], [690, 14, 587, 15, "columnNumber"], [690, 26, 587, 15], [691, 12, 587, 15], [691, 19, 587, 17], [691, 20, 587, 18], [691, 35, 589, 12], [691, 39, 589, 12, "_jsxDevRuntime"], [691, 53, 589, 12], [691, 54, 589, 12, "jsxDEV"], [691, 60, 589, 12], [691, 62, 589, 13, "_expoBlur"], [691, 71, 589, 13], [691, 72, 589, 13, "BlurView"], [691, 80, 589, 21], [692, 14, 589, 22, "intensity"], [692, 23, 589, 31], [692, 25, 589, 33], [692, 27, 589, 36], [693, 14, 589, 37, "tint"], [693, 18, 589, 41], [693, 20, 589, 42], [693, 26, 589, 48], [694, 14, 589, 49, "style"], [694, 19, 589, 54], [694, 21, 589, 56], [694, 22, 589, 57, "styles"], [694, 28, 589, 63], [694, 29, 589, 64, "blurZone"], [694, 37, 589, 72], [694, 39, 589, 74], [695, 16, 590, 14, "left"], [695, 20, 590, 18], [695, 22, 590, 20], [695, 23, 590, 21], [696, 16, 591, 14, "top"], [696, 19, 591, 17], [696, 21, 591, 19], [696, 22, 591, 20], [697, 16, 592, 14, "width"], [697, 21, 592, 19], [697, 23, 592, 21, "viewSize"], [697, 31, 592, 29], [697, 32, 592, 30, "width"], [697, 37, 592, 35], [698, 16, 593, 14, "height"], [698, 22, 593, 20], [698, 24, 593, 22, "viewSize"], [698, 32, 593, 30], [698, 33, 593, 31, "height"], [698, 39, 593, 37], [698, 42, 593, 40], [698, 45, 593, 43], [699, 16, 594, 14, "borderRadius"], [699, 28, 594, 26], [699, 30, 594, 28], [700, 14, 595, 12], [700, 15, 595, 13], [701, 12, 595, 15], [702, 14, 595, 15, "fileName"], [702, 22, 595, 15], [702, 24, 595, 15, "_jsxFileName"], [702, 36, 595, 15], [703, 14, 595, 15, "lineNumber"], [703, 24, 595, 15], [704, 14, 595, 15, "columnNumber"], [704, 26, 595, 15], [705, 12, 595, 15], [705, 19, 595, 17], [705, 20, 595, 18], [705, 35, 597, 12], [705, 39, 597, 12, "_jsxDevRuntime"], [705, 53, 597, 12], [705, 54, 597, 12, "jsxDEV"], [705, 60, 597, 12], [705, 62, 597, 13, "_expoBlur"], [705, 71, 597, 13], [705, 72, 597, 13, "BlurView"], [705, 80, 597, 21], [706, 14, 597, 22, "intensity"], [706, 23, 597, 31], [706, 25, 597, 33], [706, 27, 597, 36], [707, 14, 597, 37, "tint"], [707, 18, 597, 41], [707, 20, 597, 42], [707, 26, 597, 48], [708, 14, 597, 49, "style"], [708, 19, 597, 54], [708, 21, 597, 56], [708, 22, 597, 57, "styles"], [708, 28, 597, 63], [708, 29, 597, 64, "blurZone"], [708, 37, 597, 72], [708, 39, 597, 74], [709, 16, 598, 14, "left"], [709, 20, 598, 18], [709, 22, 598, 20, "viewSize"], [709, 30, 598, 28], [709, 31, 598, 29, "width"], [709, 36, 598, 34], [709, 39, 598, 37], [709, 42, 598, 40], [709, 45, 598, 44, "viewSize"], [709, 53, 598, 52], [709, 54, 598, 53, "width"], [709, 59, 598, 58], [709, 62, 598, 61], [709, 66, 598, 66], [710, 16, 599, 14, "top"], [710, 19, 599, 17], [710, 21, 599, 19, "viewSize"], [710, 29, 599, 27], [710, 30, 599, 28, "height"], [710, 36, 599, 34], [710, 39, 599, 37], [710, 43, 599, 41], [710, 46, 599, 45, "viewSize"], [710, 54, 599, 53], [710, 55, 599, 54, "width"], [710, 60, 599, 59], [710, 63, 599, 62], [710, 67, 599, 67], [711, 16, 600, 14, "width"], [711, 21, 600, 19], [711, 23, 600, 21, "viewSize"], [711, 31, 600, 29], [711, 32, 600, 30, "width"], [711, 37, 600, 35], [711, 40, 600, 38], [711, 43, 600, 41], [712, 16, 601, 14, "height"], [712, 22, 601, 20], [712, 24, 601, 22, "viewSize"], [712, 32, 601, 30], [712, 33, 601, 31, "width"], [712, 38, 601, 36], [712, 41, 601, 39], [712, 44, 601, 42], [713, 16, 602, 14, "borderRadius"], [713, 28, 602, 26], [713, 30, 602, 29, "viewSize"], [713, 38, 602, 37], [713, 39, 602, 38, "width"], [713, 44, 602, 43], [713, 47, 602, 46], [713, 50, 602, 49], [713, 53, 602, 53], [714, 14, 603, 12], [714, 15, 603, 13], [715, 12, 603, 15], [716, 14, 603, 15, "fileName"], [716, 22, 603, 15], [716, 24, 603, 15, "_jsxFileName"], [716, 36, 603, 15], [717, 14, 603, 15, "lineNumber"], [717, 24, 603, 15], [718, 14, 603, 15, "columnNumber"], [718, 26, 603, 15], [719, 12, 603, 15], [719, 19, 603, 17], [719, 20, 603, 18], [719, 35, 604, 12], [719, 39, 604, 12, "_jsxDevRuntime"], [719, 53, 604, 12], [719, 54, 604, 12, "jsxDEV"], [719, 60, 604, 12], [719, 62, 604, 13, "_expoBlur"], [719, 71, 604, 13], [719, 72, 604, 13, "BlurView"], [719, 80, 604, 21], [720, 14, 604, 22, "intensity"], [720, 23, 604, 31], [720, 25, 604, 33], [720, 27, 604, 36], [721, 14, 604, 37, "tint"], [721, 18, 604, 41], [721, 20, 604, 42], [721, 26, 604, 48], [722, 14, 604, 49, "style"], [722, 19, 604, 54], [722, 21, 604, 56], [722, 22, 604, 57, "styles"], [722, 28, 604, 63], [722, 29, 604, 64, "blurZone"], [722, 37, 604, 72], [722, 39, 604, 74], [723, 16, 605, 14, "left"], [723, 20, 605, 18], [723, 22, 605, 20, "viewSize"], [723, 30, 605, 28], [723, 31, 605, 29, "width"], [723, 36, 605, 34], [723, 39, 605, 37], [723, 42, 605, 40], [723, 45, 605, 44, "viewSize"], [723, 53, 605, 52], [723, 54, 605, 53, "width"], [723, 59, 605, 58], [723, 62, 605, 61], [723, 66, 605, 66], [724, 16, 606, 14, "top"], [724, 19, 606, 17], [724, 21, 606, 19, "viewSize"], [724, 29, 606, 27], [724, 30, 606, 28, "height"], [724, 36, 606, 34], [724, 39, 606, 37], [724, 42, 606, 40], [724, 45, 606, 44, "viewSize"], [724, 53, 606, 52], [724, 54, 606, 53, "width"], [724, 59, 606, 58], [724, 62, 606, 61], [724, 66, 606, 66], [725, 16, 607, 14, "width"], [725, 21, 607, 19], [725, 23, 607, 21, "viewSize"], [725, 31, 607, 29], [725, 32, 607, 30, "width"], [725, 37, 607, 35], [725, 40, 607, 38], [725, 43, 607, 41], [726, 16, 608, 14, "height"], [726, 22, 608, 20], [726, 24, 608, 22, "viewSize"], [726, 32, 608, 30], [726, 33, 608, 31, "width"], [726, 38, 608, 36], [726, 41, 608, 39], [726, 44, 608, 42], [727, 16, 609, 14, "borderRadius"], [727, 28, 609, 26], [727, 30, 609, 29, "viewSize"], [727, 38, 609, 37], [727, 39, 609, 38, "width"], [727, 44, 609, 43], [727, 47, 609, 46], [727, 50, 609, 49], [727, 53, 609, 53], [728, 14, 610, 12], [728, 15, 610, 13], [729, 12, 610, 15], [730, 14, 610, 15, "fileName"], [730, 22, 610, 15], [730, 24, 610, 15, "_jsxFileName"], [730, 36, 610, 15], [731, 14, 610, 15, "lineNumber"], [731, 24, 610, 15], [732, 14, 610, 15, "columnNumber"], [732, 26, 610, 15], [733, 12, 610, 15], [733, 19, 610, 17], [733, 20, 610, 18], [733, 35, 611, 12], [733, 39, 611, 12, "_jsxDevRuntime"], [733, 53, 611, 12], [733, 54, 611, 12, "jsxDEV"], [733, 60, 611, 12], [733, 62, 611, 13, "_expoBlur"], [733, 71, 611, 13], [733, 72, 611, 13, "BlurView"], [733, 80, 611, 21], [734, 14, 611, 22, "intensity"], [734, 23, 611, 31], [734, 25, 611, 33], [734, 27, 611, 36], [735, 14, 611, 37, "tint"], [735, 18, 611, 41], [735, 20, 611, 42], [735, 26, 611, 48], [736, 14, 611, 49, "style"], [736, 19, 611, 54], [736, 21, 611, 56], [736, 22, 611, 57, "styles"], [736, 28, 611, 63], [736, 29, 611, 64, "blurZone"], [736, 37, 611, 72], [736, 39, 611, 74], [737, 16, 612, 14, "left"], [737, 20, 612, 18], [737, 22, 612, 20, "viewSize"], [737, 30, 612, 28], [737, 31, 612, 29, "width"], [737, 36, 612, 34], [737, 39, 612, 37], [737, 42, 612, 40], [737, 45, 612, 44, "viewSize"], [737, 53, 612, 52], [737, 54, 612, 53, "width"], [737, 59, 612, 58], [737, 62, 612, 61], [737, 66, 612, 66], [738, 16, 613, 14, "top"], [738, 19, 613, 17], [738, 21, 613, 19, "viewSize"], [738, 29, 613, 27], [738, 30, 613, 28, "height"], [738, 36, 613, 34], [738, 39, 613, 37], [738, 42, 613, 40], [738, 45, 613, 44, "viewSize"], [738, 53, 613, 52], [738, 54, 613, 53, "width"], [738, 59, 613, 58], [738, 62, 613, 61], [738, 66, 613, 66], [739, 16, 614, 14, "width"], [739, 21, 614, 19], [739, 23, 614, 21, "viewSize"], [739, 31, 614, 29], [739, 32, 614, 30, "width"], [739, 37, 614, 35], [739, 40, 614, 38], [739, 43, 614, 41], [740, 16, 615, 14, "height"], [740, 22, 615, 20], [740, 24, 615, 22, "viewSize"], [740, 32, 615, 30], [740, 33, 615, 31, "width"], [740, 38, 615, 36], [740, 41, 615, 39], [740, 44, 615, 42], [741, 16, 616, 14, "borderRadius"], [741, 28, 616, 26], [741, 30, 616, 29, "viewSize"], [741, 38, 616, 37], [741, 39, 616, 38, "width"], [741, 44, 616, 43], [741, 47, 616, 46], [741, 50, 616, 49], [741, 53, 616, 53], [742, 14, 617, 12], [742, 15, 617, 13], [743, 12, 617, 15], [744, 14, 617, 15, "fileName"], [744, 22, 617, 15], [744, 24, 617, 15, "_jsxFileName"], [744, 36, 617, 15], [745, 14, 617, 15, "lineNumber"], [745, 24, 617, 15], [746, 14, 617, 15, "columnNumber"], [746, 26, 617, 15], [747, 12, 617, 15], [747, 19, 617, 17], [747, 20, 617, 18], [747, 22, 619, 13, "__DEV__"], [747, 29, 619, 20], [747, 46, 620, 14], [747, 50, 620, 14, "_jsxDevRuntime"], [747, 64, 620, 14], [747, 65, 620, 14, "jsxDEV"], [747, 71, 620, 14], [747, 73, 620, 15, "_View"], [747, 78, 620, 15], [747, 79, 620, 15, "default"], [747, 86, 620, 19], [748, 14, 620, 20, "style"], [748, 19, 620, 25], [748, 21, 620, 27, "styles"], [748, 27, 620, 33], [748, 28, 620, 34, "previewChip"], [748, 39, 620, 46], [749, 14, 620, 46, "children"], [749, 22, 620, 46], [749, 37, 621, 16], [749, 41, 621, 16, "_jsxDevRuntime"], [749, 55, 621, 16], [749, 56, 621, 16, "jsxDEV"], [749, 62, 621, 16], [749, 64, 621, 17, "_Text"], [749, 69, 621, 17], [749, 70, 621, 17, "default"], [749, 77, 621, 21], [750, 16, 621, 22, "style"], [750, 21, 621, 27], [750, 23, 621, 29, "styles"], [750, 29, 621, 35], [750, 30, 621, 36, "previewChipText"], [750, 45, 621, 52], [751, 16, 621, 52, "children"], [751, 24, 621, 52], [751, 26, 621, 53], [752, 14, 621, 73], [753, 16, 621, 73, "fileName"], [753, 24, 621, 73], [753, 26, 621, 73, "_jsxFileName"], [753, 38, 621, 73], [754, 16, 621, 73, "lineNumber"], [754, 26, 621, 73], [755, 16, 621, 73, "columnNumber"], [755, 28, 621, 73], [756, 14, 621, 73], [756, 21, 621, 79], [757, 12, 621, 80], [758, 14, 621, 80, "fileName"], [758, 22, 621, 80], [758, 24, 621, 80, "_jsxFileName"], [758, 36, 621, 80], [759, 14, 621, 80, "lineNumber"], [759, 24, 621, 80], [760, 14, 621, 80, "columnNumber"], [760, 26, 621, 80], [761, 12, 621, 80], [761, 19, 622, 20], [761, 20, 623, 13], [762, 10, 623, 13], [763, 12, 623, 13, "fileName"], [763, 20, 623, 13], [763, 22, 623, 13, "_jsxFileName"], [763, 34, 623, 13], [764, 12, 623, 13, "lineNumber"], [764, 22, 623, 13], [765, 12, 623, 13, "columnNumber"], [765, 24, 623, 13], [766, 10, 623, 13], [766, 17, 624, 18], [766, 18, 624, 19], [767, 8, 624, 19], [767, 23, 625, 12], [767, 24, 626, 9], [767, 26, 628, 9, "isCameraReady"], [767, 39, 628, 22], [767, 56, 629, 10], [767, 60, 629, 10, "_jsxDevRuntime"], [767, 74, 629, 10], [767, 75, 629, 10, "jsxDEV"], [767, 81, 629, 10], [767, 83, 629, 10, "_jsxDevRuntime"], [767, 97, 629, 10], [767, 98, 629, 10, "Fragment"], [767, 106, 629, 10], [768, 10, 629, 10, "children"], [768, 18, 629, 10], [768, 34, 631, 12], [768, 38, 631, 12, "_jsxDevRuntime"], [768, 52, 631, 12], [768, 53, 631, 12, "jsxDEV"], [768, 59, 631, 12], [768, 61, 631, 13, "_View"], [768, 66, 631, 13], [768, 67, 631, 13, "default"], [768, 74, 631, 17], [769, 12, 631, 18, "style"], [769, 17, 631, 23], [769, 19, 631, 25, "styles"], [769, 25, 631, 31], [769, 26, 631, 32, "headerOverlay"], [769, 39, 631, 46], [770, 12, 631, 46, "children"], [770, 20, 631, 46], [770, 35, 632, 14], [770, 39, 632, 14, "_jsxDevRuntime"], [770, 53, 632, 14], [770, 54, 632, 14, "jsxDEV"], [770, 60, 632, 14], [770, 62, 632, 15, "_View"], [770, 67, 632, 15], [770, 68, 632, 15, "default"], [770, 75, 632, 19], [771, 14, 632, 20, "style"], [771, 19, 632, 25], [771, 21, 632, 27, "styles"], [771, 27, 632, 33], [771, 28, 632, 34, "headerContent"], [771, 41, 632, 48], [772, 14, 632, 48, "children"], [772, 22, 632, 48], [772, 38, 633, 16], [772, 42, 633, 16, "_jsxDevRuntime"], [772, 56, 633, 16], [772, 57, 633, 16, "jsxDEV"], [772, 63, 633, 16], [772, 65, 633, 17, "_View"], [772, 70, 633, 17], [772, 71, 633, 17, "default"], [772, 78, 633, 21], [773, 16, 633, 22, "style"], [773, 21, 633, 27], [773, 23, 633, 29, "styles"], [773, 29, 633, 35], [773, 30, 633, 36, "headerLeft"], [773, 40, 633, 47], [774, 16, 633, 47, "children"], [774, 24, 633, 47], [774, 40, 634, 18], [774, 44, 634, 18, "_jsxDevRuntime"], [774, 58, 634, 18], [774, 59, 634, 18, "jsxDEV"], [774, 65, 634, 18], [774, 67, 634, 19, "_Text"], [774, 72, 634, 19], [774, 73, 634, 19, "default"], [774, 80, 634, 23], [775, 18, 634, 24, "style"], [775, 23, 634, 29], [775, 25, 634, 31, "styles"], [775, 31, 634, 37], [775, 32, 634, 38, "headerTitle"], [775, 43, 634, 50], [776, 18, 634, 50, "children"], [776, 26, 634, 50], [776, 28, 634, 51], [777, 16, 634, 62], [778, 18, 634, 62, "fileName"], [778, 26, 634, 62], [778, 28, 634, 62, "_jsxFileName"], [778, 40, 634, 62], [779, 18, 634, 62, "lineNumber"], [779, 28, 634, 62], [780, 18, 634, 62, "columnNumber"], [780, 30, 634, 62], [781, 16, 634, 62], [781, 23, 634, 68], [781, 24, 634, 69], [781, 39, 635, 18], [781, 43, 635, 18, "_jsxDevRuntime"], [781, 57, 635, 18], [781, 58, 635, 18, "jsxDEV"], [781, 64, 635, 18], [781, 66, 635, 19, "_View"], [781, 71, 635, 19], [781, 72, 635, 19, "default"], [781, 79, 635, 23], [782, 18, 635, 24, "style"], [782, 23, 635, 29], [782, 25, 635, 31, "styles"], [782, 31, 635, 37], [782, 32, 635, 38, "subtitleRow"], [782, 43, 635, 50], [783, 18, 635, 50, "children"], [783, 26, 635, 50], [783, 42, 636, 20], [783, 46, 636, 20, "_jsxDevRuntime"], [783, 60, 636, 20], [783, 61, 636, 20, "jsxDEV"], [783, 67, 636, 20], [783, 69, 636, 21, "_Text"], [783, 74, 636, 21], [783, 75, 636, 21, "default"], [783, 82, 636, 25], [784, 20, 636, 26, "style"], [784, 25, 636, 31], [784, 27, 636, 33, "styles"], [784, 33, 636, 39], [784, 34, 636, 40, "webIcon"], [784, 41, 636, 48], [785, 20, 636, 48, "children"], [785, 28, 636, 48], [785, 30, 636, 49], [786, 18, 636, 51], [787, 20, 636, 51, "fileName"], [787, 28, 636, 51], [787, 30, 636, 51, "_jsxFileName"], [787, 42, 636, 51], [788, 20, 636, 51, "lineNumber"], [788, 30, 636, 51], [789, 20, 636, 51, "columnNumber"], [789, 32, 636, 51], [790, 18, 636, 51], [790, 25, 636, 57], [790, 26, 636, 58], [790, 41, 637, 20], [790, 45, 637, 20, "_jsxDevRuntime"], [790, 59, 637, 20], [790, 60, 637, 20, "jsxDEV"], [790, 66, 637, 20], [790, 68, 637, 21, "_Text"], [790, 73, 637, 21], [790, 74, 637, 21, "default"], [790, 81, 637, 25], [791, 20, 637, 26, "style"], [791, 25, 637, 31], [791, 27, 637, 33, "styles"], [791, 33, 637, 39], [791, 34, 637, 40, "headerSubtitle"], [791, 48, 637, 55], [792, 20, 637, 55, "children"], [792, 28, 637, 55], [792, 30, 637, 56], [793, 18, 637, 71], [794, 20, 637, 71, "fileName"], [794, 28, 637, 71], [794, 30, 637, 71, "_jsxFileName"], [794, 42, 637, 71], [795, 20, 637, 71, "lineNumber"], [795, 30, 637, 71], [796, 20, 637, 71, "columnNumber"], [796, 32, 637, 71], [797, 18, 637, 71], [797, 25, 637, 77], [797, 26, 637, 78], [798, 16, 637, 78], [799, 18, 637, 78, "fileName"], [799, 26, 637, 78], [799, 28, 637, 78, "_jsxFileName"], [799, 40, 637, 78], [800, 18, 637, 78, "lineNumber"], [800, 28, 637, 78], [801, 18, 637, 78, "columnNumber"], [801, 30, 637, 78], [802, 16, 637, 78], [802, 23, 638, 24], [802, 24, 638, 25], [802, 26, 639, 19, "challengeCode"], [802, 39, 639, 32], [802, 56, 640, 20], [802, 60, 640, 20, "_jsxDevRuntime"], [802, 74, 640, 20], [802, 75, 640, 20, "jsxDEV"], [802, 81, 640, 20], [802, 83, 640, 21, "_View"], [802, 88, 640, 21], [802, 89, 640, 21, "default"], [802, 96, 640, 25], [803, 18, 640, 26, "style"], [803, 23, 640, 31], [803, 25, 640, 33, "styles"], [803, 31, 640, 39], [803, 32, 640, 40, "challengeRow"], [803, 44, 640, 53], [804, 18, 640, 53, "children"], [804, 26, 640, 53], [804, 42, 641, 22], [804, 46, 641, 22, "_jsxDevRuntime"], [804, 60, 641, 22], [804, 61, 641, 22, "jsxDEV"], [804, 67, 641, 22], [804, 69, 641, 23, "_lucideReactNative"], [804, 87, 641, 23], [804, 88, 641, 23, "Shield"], [804, 94, 641, 29], [805, 20, 641, 30, "size"], [805, 24, 641, 34], [805, 26, 641, 36], [805, 28, 641, 39], [806, 20, 641, 40, "color"], [806, 25, 641, 45], [806, 27, 641, 46], [807, 18, 641, 52], [808, 20, 641, 52, "fileName"], [808, 28, 641, 52], [808, 30, 641, 52, "_jsxFileName"], [808, 42, 641, 52], [809, 20, 641, 52, "lineNumber"], [809, 30, 641, 52], [810, 20, 641, 52, "columnNumber"], [810, 32, 641, 52], [811, 18, 641, 52], [811, 25, 641, 54], [811, 26, 641, 55], [811, 41, 642, 22], [811, 45, 642, 22, "_jsxDevRuntime"], [811, 59, 642, 22], [811, 60, 642, 22, "jsxDEV"], [811, 66, 642, 22], [811, 68, 642, 23, "_Text"], [811, 73, 642, 23], [811, 74, 642, 23, "default"], [811, 81, 642, 27], [812, 20, 642, 28, "style"], [812, 25, 642, 33], [812, 27, 642, 35, "styles"], [812, 33, 642, 41], [812, 34, 642, 42, "challengeCode"], [812, 47, 642, 56], [813, 20, 642, 56, "children"], [813, 28, 642, 56], [813, 30, 642, 58, "challengeCode"], [814, 18, 642, 71], [815, 20, 642, 71, "fileName"], [815, 28, 642, 71], [815, 30, 642, 71, "_jsxFileName"], [815, 42, 642, 71], [816, 20, 642, 71, "lineNumber"], [816, 30, 642, 71], [817, 20, 642, 71, "columnNumber"], [817, 32, 642, 71], [818, 18, 642, 71], [818, 25, 642, 78], [818, 26, 642, 79], [819, 16, 642, 79], [820, 18, 642, 79, "fileName"], [820, 26, 642, 79], [820, 28, 642, 79, "_jsxFileName"], [820, 40, 642, 79], [821, 18, 642, 79, "lineNumber"], [821, 28, 642, 79], [822, 18, 642, 79, "columnNumber"], [822, 30, 642, 79], [823, 16, 642, 79], [823, 23, 643, 26], [823, 24, 644, 19], [824, 14, 644, 19], [825, 16, 644, 19, "fileName"], [825, 24, 644, 19], [825, 26, 644, 19, "_jsxFileName"], [825, 38, 644, 19], [826, 16, 644, 19, "lineNumber"], [826, 26, 644, 19], [827, 16, 644, 19, "columnNumber"], [827, 28, 644, 19], [828, 14, 644, 19], [828, 21, 645, 22], [828, 22, 645, 23], [828, 37, 646, 16], [828, 41, 646, 16, "_jsxDevRuntime"], [828, 55, 646, 16], [828, 56, 646, 16, "jsxDEV"], [828, 62, 646, 16], [828, 64, 646, 17, "_TouchableOpacity"], [828, 81, 646, 17], [828, 82, 646, 17, "default"], [828, 89, 646, 33], [829, 16, 646, 34, "onPress"], [829, 23, 646, 41], [829, 25, 646, 43, "onCancel"], [829, 33, 646, 52], [830, 16, 646, 53, "style"], [830, 21, 646, 58], [830, 23, 646, 60, "styles"], [830, 29, 646, 66], [830, 30, 646, 67, "closeButton"], [830, 41, 646, 79], [831, 16, 646, 79, "children"], [831, 24, 646, 79], [831, 39, 647, 18], [831, 43, 647, 18, "_jsxDevRuntime"], [831, 57, 647, 18], [831, 58, 647, 18, "jsxDEV"], [831, 64, 647, 18], [831, 66, 647, 19, "_lucideReactNative"], [831, 84, 647, 19], [831, 85, 647, 19, "X"], [831, 86, 647, 20], [832, 18, 647, 21, "size"], [832, 22, 647, 25], [832, 24, 647, 27], [832, 26, 647, 30], [833, 18, 647, 31, "color"], [833, 23, 647, 36], [833, 25, 647, 37], [834, 16, 647, 43], [835, 18, 647, 43, "fileName"], [835, 26, 647, 43], [835, 28, 647, 43, "_jsxFileName"], [835, 40, 647, 43], [836, 18, 647, 43, "lineNumber"], [836, 28, 647, 43], [837, 18, 647, 43, "columnNumber"], [837, 30, 647, 43], [838, 16, 647, 43], [838, 23, 647, 45], [839, 14, 647, 46], [840, 16, 647, 46, "fileName"], [840, 24, 647, 46], [840, 26, 647, 46, "_jsxFileName"], [840, 38, 647, 46], [841, 16, 647, 46, "lineNumber"], [841, 26, 647, 46], [842, 16, 647, 46, "columnNumber"], [842, 28, 647, 46], [843, 14, 647, 46], [843, 21, 648, 34], [843, 22, 648, 35], [844, 12, 648, 35], [845, 14, 648, 35, "fileName"], [845, 22, 648, 35], [845, 24, 648, 35, "_jsxFileName"], [845, 36, 648, 35], [846, 14, 648, 35, "lineNumber"], [846, 24, 648, 35], [847, 14, 648, 35, "columnNumber"], [847, 26, 648, 35], [848, 12, 648, 35], [848, 19, 649, 20], [849, 10, 649, 21], [850, 12, 649, 21, "fileName"], [850, 20, 649, 21], [850, 22, 649, 21, "_jsxFileName"], [850, 34, 649, 21], [851, 12, 649, 21, "lineNumber"], [851, 22, 649, 21], [852, 12, 649, 21, "columnNumber"], [852, 24, 649, 21], [853, 10, 649, 21], [853, 17, 650, 18], [853, 18, 650, 19], [853, 33, 652, 12], [853, 37, 652, 12, "_jsxDevRuntime"], [853, 51, 652, 12], [853, 52, 652, 12, "jsxDEV"], [853, 58, 652, 12], [853, 60, 652, 13, "_View"], [853, 65, 652, 13], [853, 66, 652, 13, "default"], [853, 73, 652, 17], [854, 12, 652, 18, "style"], [854, 17, 652, 23], [854, 19, 652, 25, "styles"], [854, 25, 652, 31], [854, 26, 652, 32, "privacyNotice"], [854, 39, 652, 46], [855, 12, 652, 46, "children"], [855, 20, 652, 46], [855, 36, 653, 14], [855, 40, 653, 14, "_jsxDevRuntime"], [855, 54, 653, 14], [855, 55, 653, 14, "jsxDEV"], [855, 61, 653, 14], [855, 63, 653, 15, "_lucideReactNative"], [855, 81, 653, 15], [855, 82, 653, 15, "Shield"], [855, 88, 653, 21], [856, 14, 653, 22, "size"], [856, 18, 653, 26], [856, 20, 653, 28], [856, 22, 653, 31], [857, 14, 653, 32, "color"], [857, 19, 653, 37], [857, 21, 653, 38], [858, 12, 653, 47], [859, 14, 653, 47, "fileName"], [859, 22, 653, 47], [859, 24, 653, 47, "_jsxFileName"], [859, 36, 653, 47], [860, 14, 653, 47, "lineNumber"], [860, 24, 653, 47], [861, 14, 653, 47, "columnNumber"], [861, 26, 653, 47], [862, 12, 653, 47], [862, 19, 653, 49], [862, 20, 653, 50], [862, 35, 654, 14], [862, 39, 654, 14, "_jsxDevRuntime"], [862, 53, 654, 14], [862, 54, 654, 14, "jsxDEV"], [862, 60, 654, 14], [862, 62, 654, 15, "_Text"], [862, 67, 654, 15], [862, 68, 654, 15, "default"], [862, 75, 654, 19], [863, 14, 654, 20, "style"], [863, 19, 654, 25], [863, 21, 654, 27, "styles"], [863, 27, 654, 33], [863, 28, 654, 34, "privacyText"], [863, 39, 654, 46], [864, 14, 654, 46, "children"], [864, 22, 654, 46], [864, 24, 654, 47], [865, 12, 656, 14], [866, 14, 656, 14, "fileName"], [866, 22, 656, 14], [866, 24, 656, 14, "_jsxFileName"], [866, 36, 656, 14], [867, 14, 656, 14, "lineNumber"], [867, 24, 656, 14], [868, 14, 656, 14, "columnNumber"], [868, 26, 656, 14], [869, 12, 656, 14], [869, 19, 656, 20], [869, 20, 656, 21], [870, 10, 656, 21], [871, 12, 656, 21, "fileName"], [871, 20, 656, 21], [871, 22, 656, 21, "_jsxFileName"], [871, 34, 656, 21], [872, 12, 656, 21, "lineNumber"], [872, 22, 656, 21], [873, 12, 656, 21, "columnNumber"], [873, 24, 656, 21], [874, 10, 656, 21], [874, 17, 657, 18], [874, 18, 657, 19], [874, 33, 659, 12], [874, 37, 659, 12, "_jsxDevRuntime"], [874, 51, 659, 12], [874, 52, 659, 12, "jsxDEV"], [874, 58, 659, 12], [874, 60, 659, 13, "_View"], [874, 65, 659, 13], [874, 66, 659, 13, "default"], [874, 73, 659, 17], [875, 12, 659, 18, "style"], [875, 17, 659, 23], [875, 19, 659, 25, "styles"], [875, 25, 659, 31], [875, 26, 659, 32, "footer<PERSON><PERSON><PERSON>"], [875, 39, 659, 46], [876, 12, 659, 46, "children"], [876, 20, 659, 46], [876, 36, 660, 14], [876, 40, 660, 14, "_jsxDevRuntime"], [876, 54, 660, 14], [876, 55, 660, 14, "jsxDEV"], [876, 61, 660, 14], [876, 63, 660, 15, "_Text"], [876, 68, 660, 15], [876, 69, 660, 15, "default"], [876, 76, 660, 19], [877, 14, 660, 20, "style"], [877, 19, 660, 25], [877, 21, 660, 27, "styles"], [877, 27, 660, 33], [877, 28, 660, 34, "instruction"], [877, 39, 660, 46], [878, 14, 660, 46, "children"], [878, 22, 660, 46], [878, 24, 660, 47], [879, 12, 662, 14], [880, 14, 662, 14, "fileName"], [880, 22, 662, 14], [880, 24, 662, 14, "_jsxFileName"], [880, 36, 662, 14], [881, 14, 662, 14, "lineNumber"], [881, 24, 662, 14], [882, 14, 662, 14, "columnNumber"], [882, 26, 662, 14], [883, 12, 662, 14], [883, 19, 662, 20], [883, 20, 662, 21], [883, 35, 664, 14], [883, 39, 664, 14, "_jsxDevRuntime"], [883, 53, 664, 14], [883, 54, 664, 14, "jsxDEV"], [883, 60, 664, 14], [883, 62, 664, 15, "_TouchableOpacity"], [883, 79, 664, 15], [883, 80, 664, 15, "default"], [883, 87, 664, 31], [884, 14, 665, 16, "onPress"], [884, 21, 665, 23], [884, 23, 665, 25, "capturePhoto"], [884, 35, 665, 38], [885, 14, 666, 16, "disabled"], [885, 22, 666, 24], [885, 24, 666, 26, "processingState"], [885, 39, 666, 41], [885, 44, 666, 46], [885, 50, 666, 52], [885, 54, 666, 56], [885, 55, 666, 57, "isCameraReady"], [885, 68, 666, 71], [886, 14, 667, 16, "style"], [886, 19, 667, 21], [886, 21, 667, 23], [886, 22, 668, 18, "styles"], [886, 28, 668, 24], [886, 29, 668, 25, "shutterButton"], [886, 42, 668, 38], [886, 44, 669, 18, "processingState"], [886, 59, 669, 33], [886, 64, 669, 38], [886, 70, 669, 44], [886, 74, 669, 48, "styles"], [886, 80, 669, 54], [886, 81, 669, 55, "shutterButtonDisabled"], [886, 102, 669, 76], [886, 103, 670, 18], [887, 14, 670, 18, "children"], [887, 22, 670, 18], [887, 24, 672, 17, "processingState"], [887, 39, 672, 32], [887, 44, 672, 37], [887, 50, 672, 43], [887, 66, 673, 18], [887, 70, 673, 18, "_jsxDevRuntime"], [887, 84, 673, 18], [887, 85, 673, 18, "jsxDEV"], [887, 91, 673, 18], [887, 93, 673, 19, "_View"], [887, 98, 673, 19], [887, 99, 673, 19, "default"], [887, 106, 673, 23], [888, 16, 673, 24, "style"], [888, 21, 673, 29], [888, 23, 673, 31, "styles"], [888, 29, 673, 37], [888, 30, 673, 38, "shutterInner"], [889, 14, 673, 51], [890, 16, 673, 51, "fileName"], [890, 24, 673, 51], [890, 26, 673, 51, "_jsxFileName"], [890, 38, 673, 51], [891, 16, 673, 51, "lineNumber"], [891, 26, 673, 51], [892, 16, 673, 51, "columnNumber"], [892, 28, 673, 51], [893, 14, 673, 51], [893, 21, 673, 53], [893, 22, 673, 54], [893, 38, 675, 18], [893, 42, 675, 18, "_jsxDevRuntime"], [893, 56, 675, 18], [893, 57, 675, 18, "jsxDEV"], [893, 63, 675, 18], [893, 65, 675, 19, "_ActivityIndicator"], [893, 83, 675, 19], [893, 84, 675, 19, "default"], [893, 91, 675, 36], [894, 16, 675, 37, "size"], [894, 20, 675, 41], [894, 22, 675, 42], [894, 29, 675, 49], [895, 16, 675, 50, "color"], [895, 21, 675, 55], [895, 23, 675, 56], [896, 14, 675, 65], [897, 16, 675, 65, "fileName"], [897, 24, 675, 65], [897, 26, 675, 65, "_jsxFileName"], [897, 38, 675, 65], [898, 16, 675, 65, "lineNumber"], [898, 26, 675, 65], [899, 16, 675, 65, "columnNumber"], [899, 28, 675, 65], [900, 14, 675, 65], [900, 21, 675, 67], [901, 12, 676, 17], [902, 14, 676, 17, "fileName"], [902, 22, 676, 17], [902, 24, 676, 17, "_jsxFileName"], [902, 36, 676, 17], [903, 14, 676, 17, "lineNumber"], [903, 24, 676, 17], [904, 14, 676, 17, "columnNumber"], [904, 26, 676, 17], [905, 12, 676, 17], [905, 19, 677, 32], [905, 20, 677, 33], [905, 35, 678, 14], [905, 39, 678, 14, "_jsxDevRuntime"], [905, 53, 678, 14], [905, 54, 678, 14, "jsxDEV"], [905, 60, 678, 14], [905, 62, 678, 15, "_Text"], [905, 67, 678, 15], [905, 68, 678, 15, "default"], [905, 75, 678, 19], [906, 14, 678, 20, "style"], [906, 19, 678, 25], [906, 21, 678, 27, "styles"], [906, 27, 678, 33], [906, 28, 678, 34, "privacyNote"], [906, 39, 678, 46], [907, 14, 678, 46, "children"], [907, 22, 678, 46], [907, 24, 678, 47], [908, 12, 680, 14], [909, 14, 680, 14, "fileName"], [909, 22, 680, 14], [909, 24, 680, 14, "_jsxFileName"], [909, 36, 680, 14], [910, 14, 680, 14, "lineNumber"], [910, 24, 680, 14], [911, 14, 680, 14, "columnNumber"], [911, 26, 680, 14], [912, 12, 680, 14], [912, 19, 680, 20], [912, 20, 680, 21], [913, 10, 680, 21], [914, 12, 680, 21, "fileName"], [914, 20, 680, 21], [914, 22, 680, 21, "_jsxFileName"], [914, 34, 680, 21], [915, 12, 680, 21, "lineNumber"], [915, 22, 680, 21], [916, 12, 680, 21, "columnNumber"], [916, 24, 680, 21], [917, 10, 680, 21], [917, 17, 681, 18], [917, 18, 681, 19], [918, 8, 681, 19], [918, 23, 682, 12], [918, 24, 683, 9], [919, 6, 683, 9], [920, 8, 683, 9, "fileName"], [920, 16, 683, 9], [920, 18, 683, 9, "_jsxFileName"], [920, 30, 683, 9], [921, 8, 683, 9, "lineNumber"], [921, 18, 683, 9], [922, 8, 683, 9, "columnNumber"], [922, 20, 683, 9], [923, 6, 683, 9], [923, 13, 684, 12], [923, 14, 684, 13], [923, 29, 686, 6], [923, 33, 686, 6, "_jsxDevRuntime"], [923, 47, 686, 6], [923, 48, 686, 6, "jsxDEV"], [923, 54, 686, 6], [923, 56, 686, 7, "_Modal"], [923, 62, 686, 7], [923, 63, 686, 7, "default"], [923, 70, 686, 12], [924, 8, 687, 8, "visible"], [924, 15, 687, 15], [924, 17, 687, 17, "processingState"], [924, 32, 687, 32], [924, 37, 687, 37], [924, 43, 687, 43], [924, 47, 687, 47, "processingState"], [924, 62, 687, 62], [924, 67, 687, 67], [924, 74, 687, 75], [925, 8, 688, 8, "transparent"], [925, 19, 688, 19], [926, 8, 689, 8, "animationType"], [926, 21, 689, 21], [926, 23, 689, 22], [926, 29, 689, 28], [927, 8, 689, 28, "children"], [927, 16, 689, 28], [927, 31, 691, 8], [927, 35, 691, 8, "_jsxDevRuntime"], [927, 49, 691, 8], [927, 50, 691, 8, "jsxDEV"], [927, 56, 691, 8], [927, 58, 691, 9, "_View"], [927, 63, 691, 9], [927, 64, 691, 9, "default"], [927, 71, 691, 13], [928, 10, 691, 14, "style"], [928, 15, 691, 19], [928, 17, 691, 21, "styles"], [928, 23, 691, 27], [928, 24, 691, 28, "processingModal"], [928, 39, 691, 44], [929, 10, 691, 44, "children"], [929, 18, 691, 44], [929, 33, 692, 10], [929, 37, 692, 10, "_jsxDevRuntime"], [929, 51, 692, 10], [929, 52, 692, 10, "jsxDEV"], [929, 58, 692, 10], [929, 60, 692, 11, "_View"], [929, 65, 692, 11], [929, 66, 692, 11, "default"], [929, 73, 692, 15], [930, 12, 692, 16, "style"], [930, 17, 692, 21], [930, 19, 692, 23, "styles"], [930, 25, 692, 29], [930, 26, 692, 30, "processingContent"], [930, 43, 692, 48], [931, 12, 692, 48, "children"], [931, 20, 692, 48], [931, 36, 693, 12], [931, 40, 693, 12, "_jsxDevRuntime"], [931, 54, 693, 12], [931, 55, 693, 12, "jsxDEV"], [931, 61, 693, 12], [931, 63, 693, 13, "_ActivityIndicator"], [931, 81, 693, 13], [931, 82, 693, 13, "default"], [931, 89, 693, 30], [932, 14, 693, 31, "size"], [932, 18, 693, 35], [932, 20, 693, 36], [932, 27, 693, 43], [933, 14, 693, 44, "color"], [933, 19, 693, 49], [933, 21, 693, 50], [934, 12, 693, 59], [935, 14, 693, 59, "fileName"], [935, 22, 693, 59], [935, 24, 693, 59, "_jsxFileName"], [935, 36, 693, 59], [936, 14, 693, 59, "lineNumber"], [936, 24, 693, 59], [937, 14, 693, 59, "columnNumber"], [937, 26, 693, 59], [938, 12, 693, 59], [938, 19, 693, 61], [938, 20, 693, 62], [938, 35, 695, 12], [938, 39, 695, 12, "_jsxDevRuntime"], [938, 53, 695, 12], [938, 54, 695, 12, "jsxDEV"], [938, 60, 695, 12], [938, 62, 695, 13, "_Text"], [938, 67, 695, 13], [938, 68, 695, 13, "default"], [938, 75, 695, 17], [939, 14, 695, 18, "style"], [939, 19, 695, 23], [939, 21, 695, 25, "styles"], [939, 27, 695, 31], [939, 28, 695, 32, "processingTitle"], [939, 43, 695, 48], [940, 14, 695, 48, "children"], [940, 22, 695, 48], [940, 25, 696, 15, "processingState"], [940, 40, 696, 30], [940, 45, 696, 35], [940, 56, 696, 46], [940, 60, 696, 50], [940, 80, 696, 70], [940, 82, 697, 15, "processingState"], [940, 97, 697, 30], [940, 102, 697, 35], [940, 113, 697, 46], [940, 117, 697, 50], [940, 146, 697, 79], [940, 148, 698, 15, "processingState"], [940, 163, 698, 30], [940, 168, 698, 35], [940, 180, 698, 47], [940, 184, 698, 51], [940, 216, 698, 83], [940, 218, 699, 15, "processingState"], [940, 233, 699, 30], [940, 238, 699, 35], [940, 249, 699, 46], [940, 253, 699, 50], [940, 275, 699, 72], [941, 12, 699, 72], [942, 14, 699, 72, "fileName"], [942, 22, 699, 72], [942, 24, 699, 72, "_jsxFileName"], [942, 36, 699, 72], [943, 14, 699, 72, "lineNumber"], [943, 24, 699, 72], [944, 14, 699, 72, "columnNumber"], [944, 26, 699, 72], [945, 12, 699, 72], [945, 19, 700, 18], [945, 20, 700, 19], [945, 35, 701, 12], [945, 39, 701, 12, "_jsxDevRuntime"], [945, 53, 701, 12], [945, 54, 701, 12, "jsxDEV"], [945, 60, 701, 12], [945, 62, 701, 13, "_View"], [945, 67, 701, 13], [945, 68, 701, 13, "default"], [945, 75, 701, 17], [946, 14, 701, 18, "style"], [946, 19, 701, 23], [946, 21, 701, 25, "styles"], [946, 27, 701, 31], [946, 28, 701, 32, "progressBar"], [946, 39, 701, 44], [947, 14, 701, 44, "children"], [947, 22, 701, 44], [947, 37, 702, 14], [947, 41, 702, 14, "_jsxDevRuntime"], [947, 55, 702, 14], [947, 56, 702, 14, "jsxDEV"], [947, 62, 702, 14], [947, 64, 702, 15, "_View"], [947, 69, 702, 15], [947, 70, 702, 15, "default"], [947, 77, 702, 19], [948, 16, 703, 16, "style"], [948, 21, 703, 21], [948, 23, 703, 23], [948, 24, 704, 18, "styles"], [948, 30, 704, 24], [948, 31, 704, 25, "progressFill"], [948, 43, 704, 37], [948, 45, 705, 18], [949, 18, 705, 20, "width"], [949, 23, 705, 25], [949, 25, 705, 27], [949, 28, 705, 30, "processingProgress"], [949, 46, 705, 48], [950, 16, 705, 52], [950, 17, 705, 53], [951, 14, 706, 18], [952, 16, 706, 18, "fileName"], [952, 24, 706, 18], [952, 26, 706, 18, "_jsxFileName"], [952, 38, 706, 18], [953, 16, 706, 18, "lineNumber"], [953, 26, 706, 18], [954, 16, 706, 18, "columnNumber"], [954, 28, 706, 18], [955, 14, 706, 18], [955, 21, 707, 15], [956, 12, 707, 16], [957, 14, 707, 16, "fileName"], [957, 22, 707, 16], [957, 24, 707, 16, "_jsxFileName"], [957, 36, 707, 16], [958, 14, 707, 16, "lineNumber"], [958, 24, 707, 16], [959, 14, 707, 16, "columnNumber"], [959, 26, 707, 16], [960, 12, 707, 16], [960, 19, 708, 18], [960, 20, 708, 19], [960, 35, 709, 12], [960, 39, 709, 12, "_jsxDevRuntime"], [960, 53, 709, 12], [960, 54, 709, 12, "jsxDEV"], [960, 60, 709, 12], [960, 62, 709, 13, "_Text"], [960, 67, 709, 13], [960, 68, 709, 13, "default"], [960, 75, 709, 17], [961, 14, 709, 18, "style"], [961, 19, 709, 23], [961, 21, 709, 25, "styles"], [961, 27, 709, 31], [961, 28, 709, 32, "processingDescription"], [961, 49, 709, 54], [962, 14, 709, 54, "children"], [962, 22, 709, 54], [962, 25, 710, 15, "processingState"], [962, 40, 710, 30], [962, 45, 710, 35], [962, 56, 710, 46], [962, 60, 710, 50], [962, 89, 710, 79], [962, 91, 711, 15, "processingState"], [962, 106, 711, 30], [962, 111, 711, 35], [962, 122, 711, 46], [962, 126, 711, 50], [962, 164, 711, 88], [962, 166, 712, 15, "processingState"], [962, 181, 712, 30], [962, 186, 712, 35], [962, 198, 712, 47], [962, 202, 712, 51], [962, 247, 712, 96], [962, 249, 713, 15, "processingState"], [962, 264, 713, 30], [962, 269, 713, 35], [962, 280, 713, 46], [962, 284, 713, 50], [962, 325, 713, 91], [963, 12, 713, 91], [964, 14, 713, 91, "fileName"], [964, 22, 713, 91], [964, 24, 713, 91, "_jsxFileName"], [964, 36, 713, 91], [965, 14, 713, 91, "lineNumber"], [965, 24, 713, 91], [966, 14, 713, 91, "columnNumber"], [966, 26, 713, 91], [967, 12, 713, 91], [967, 19, 714, 18], [967, 20, 714, 19], [967, 22, 715, 13, "processingState"], [967, 37, 715, 28], [967, 42, 715, 33], [967, 53, 715, 44], [967, 70, 716, 14], [967, 74, 716, 14, "_jsxDevRuntime"], [967, 88, 716, 14], [967, 89, 716, 14, "jsxDEV"], [967, 95, 716, 14], [967, 97, 716, 15, "_lucideReactNative"], [967, 115, 716, 15], [967, 116, 716, 15, "CheckCircle"], [967, 127, 716, 26], [968, 14, 716, 27, "size"], [968, 18, 716, 31], [968, 20, 716, 33], [968, 22, 716, 36], [969, 14, 716, 37, "color"], [969, 19, 716, 42], [969, 21, 716, 43], [969, 30, 716, 52], [970, 14, 716, 53, "style"], [970, 19, 716, 58], [970, 21, 716, 60, "styles"], [970, 27, 716, 66], [970, 28, 716, 67, "successIcon"], [971, 12, 716, 79], [972, 14, 716, 79, "fileName"], [972, 22, 716, 79], [972, 24, 716, 79, "_jsxFileName"], [972, 36, 716, 79], [973, 14, 716, 79, "lineNumber"], [973, 24, 716, 79], [974, 14, 716, 79, "columnNumber"], [974, 26, 716, 79], [975, 12, 716, 79], [975, 19, 716, 81], [975, 20, 717, 13], [976, 10, 717, 13], [977, 12, 717, 13, "fileName"], [977, 20, 717, 13], [977, 22, 717, 13, "_jsxFileName"], [977, 34, 717, 13], [978, 12, 717, 13, "lineNumber"], [978, 22, 717, 13], [979, 12, 717, 13, "columnNumber"], [979, 24, 717, 13], [980, 10, 717, 13], [980, 17, 718, 16], [981, 8, 718, 17], [982, 10, 718, 17, "fileName"], [982, 18, 718, 17], [982, 20, 718, 17, "_jsxFileName"], [982, 32, 718, 17], [983, 10, 718, 17, "lineNumber"], [983, 20, 718, 17], [984, 10, 718, 17, "columnNumber"], [984, 22, 718, 17], [985, 8, 718, 17], [985, 15, 719, 14], [986, 6, 719, 15], [987, 8, 719, 15, "fileName"], [987, 16, 719, 15], [987, 18, 719, 15, "_jsxFileName"], [987, 30, 719, 15], [988, 8, 719, 15, "lineNumber"], [988, 18, 719, 15], [989, 8, 719, 15, "columnNumber"], [989, 20, 719, 15], [990, 6, 719, 15], [990, 13, 720, 13], [990, 14, 720, 14], [990, 29, 722, 6], [990, 33, 722, 6, "_jsxDevRuntime"], [990, 47, 722, 6], [990, 48, 722, 6, "jsxDEV"], [990, 54, 722, 6], [990, 56, 722, 7, "_Modal"], [990, 62, 722, 7], [990, 63, 722, 7, "default"], [990, 70, 722, 12], [991, 8, 723, 8, "visible"], [991, 15, 723, 15], [991, 17, 723, 17, "processingState"], [991, 32, 723, 32], [991, 37, 723, 37], [991, 44, 723, 45], [992, 8, 724, 8, "transparent"], [992, 19, 724, 19], [993, 8, 725, 8, "animationType"], [993, 21, 725, 21], [993, 23, 725, 22], [993, 29, 725, 28], [994, 8, 725, 28, "children"], [994, 16, 725, 28], [994, 31, 727, 8], [994, 35, 727, 8, "_jsxDevRuntime"], [994, 49, 727, 8], [994, 50, 727, 8, "jsxDEV"], [994, 56, 727, 8], [994, 58, 727, 9, "_View"], [994, 63, 727, 9], [994, 64, 727, 9, "default"], [994, 71, 727, 13], [995, 10, 727, 14, "style"], [995, 15, 727, 19], [995, 17, 727, 21, "styles"], [995, 23, 727, 27], [995, 24, 727, 28, "processingModal"], [995, 39, 727, 44], [996, 10, 727, 44, "children"], [996, 18, 727, 44], [996, 33, 728, 10], [996, 37, 728, 10, "_jsxDevRuntime"], [996, 51, 728, 10], [996, 52, 728, 10, "jsxDEV"], [996, 58, 728, 10], [996, 60, 728, 11, "_View"], [996, 65, 728, 11], [996, 66, 728, 11, "default"], [996, 73, 728, 15], [997, 12, 728, 16, "style"], [997, 17, 728, 21], [997, 19, 728, 23, "styles"], [997, 25, 728, 29], [997, 26, 728, 30, "errorContent"], [997, 38, 728, 43], [998, 12, 728, 43, "children"], [998, 20, 728, 43], [998, 36, 729, 12], [998, 40, 729, 12, "_jsxDevRuntime"], [998, 54, 729, 12], [998, 55, 729, 12, "jsxDEV"], [998, 61, 729, 12], [998, 63, 729, 13, "_lucideReactNative"], [998, 81, 729, 13], [998, 82, 729, 13, "X"], [998, 83, 729, 14], [999, 14, 729, 15, "size"], [999, 18, 729, 19], [999, 20, 729, 21], [999, 22, 729, 24], [1000, 14, 729, 25, "color"], [1000, 19, 729, 30], [1000, 21, 729, 31], [1001, 12, 729, 40], [1002, 14, 729, 40, "fileName"], [1002, 22, 729, 40], [1002, 24, 729, 40, "_jsxFileName"], [1002, 36, 729, 40], [1003, 14, 729, 40, "lineNumber"], [1003, 24, 729, 40], [1004, 14, 729, 40, "columnNumber"], [1004, 26, 729, 40], [1005, 12, 729, 40], [1005, 19, 729, 42], [1005, 20, 729, 43], [1005, 35, 730, 12], [1005, 39, 730, 12, "_jsxDevRuntime"], [1005, 53, 730, 12], [1005, 54, 730, 12, "jsxDEV"], [1005, 60, 730, 12], [1005, 62, 730, 13, "_Text"], [1005, 67, 730, 13], [1005, 68, 730, 13, "default"], [1005, 75, 730, 17], [1006, 14, 730, 18, "style"], [1006, 19, 730, 23], [1006, 21, 730, 25, "styles"], [1006, 27, 730, 31], [1006, 28, 730, 32, "errorTitle"], [1006, 38, 730, 43], [1007, 14, 730, 43, "children"], [1007, 22, 730, 43], [1007, 24, 730, 44], [1008, 12, 730, 61], [1009, 14, 730, 61, "fileName"], [1009, 22, 730, 61], [1009, 24, 730, 61, "_jsxFileName"], [1009, 36, 730, 61], [1010, 14, 730, 61, "lineNumber"], [1010, 24, 730, 61], [1011, 14, 730, 61, "columnNumber"], [1011, 26, 730, 61], [1012, 12, 730, 61], [1012, 19, 730, 67], [1012, 20, 730, 68], [1012, 35, 731, 12], [1012, 39, 731, 12, "_jsxDevRuntime"], [1012, 53, 731, 12], [1012, 54, 731, 12, "jsxDEV"], [1012, 60, 731, 12], [1012, 62, 731, 13, "_Text"], [1012, 67, 731, 13], [1012, 68, 731, 13, "default"], [1012, 75, 731, 17], [1013, 14, 731, 18, "style"], [1013, 19, 731, 23], [1013, 21, 731, 25, "styles"], [1013, 27, 731, 31], [1013, 28, 731, 32, "errorMessage"], [1013, 40, 731, 45], [1014, 14, 731, 45, "children"], [1014, 22, 731, 45], [1014, 24, 731, 47, "errorMessage"], [1015, 12, 731, 59], [1016, 14, 731, 59, "fileName"], [1016, 22, 731, 59], [1016, 24, 731, 59, "_jsxFileName"], [1016, 36, 731, 59], [1017, 14, 731, 59, "lineNumber"], [1017, 24, 731, 59], [1018, 14, 731, 59, "columnNumber"], [1018, 26, 731, 59], [1019, 12, 731, 59], [1019, 19, 731, 66], [1019, 20, 731, 67], [1019, 35, 732, 12], [1019, 39, 732, 12, "_jsxDevRuntime"], [1019, 53, 732, 12], [1019, 54, 732, 12, "jsxDEV"], [1019, 60, 732, 12], [1019, 62, 732, 13, "_TouchableOpacity"], [1019, 79, 732, 13], [1019, 80, 732, 13, "default"], [1019, 87, 732, 29], [1020, 14, 733, 14, "onPress"], [1020, 21, 733, 21], [1020, 23, 733, 23, "retryCapture"], [1020, 35, 733, 36], [1021, 14, 734, 14, "style"], [1021, 19, 734, 19], [1021, 21, 734, 21, "styles"], [1021, 27, 734, 27], [1021, 28, 734, 28, "primaryButton"], [1021, 41, 734, 42], [1022, 14, 734, 42, "children"], [1022, 22, 734, 42], [1022, 37, 736, 14], [1022, 41, 736, 14, "_jsxDevRuntime"], [1022, 55, 736, 14], [1022, 56, 736, 14, "jsxDEV"], [1022, 62, 736, 14], [1022, 64, 736, 15, "_Text"], [1022, 69, 736, 15], [1022, 70, 736, 15, "default"], [1022, 77, 736, 19], [1023, 16, 736, 20, "style"], [1023, 21, 736, 25], [1023, 23, 736, 27, "styles"], [1023, 29, 736, 33], [1023, 30, 736, 34, "primaryButtonText"], [1023, 47, 736, 52], [1024, 16, 736, 52, "children"], [1024, 24, 736, 52], [1024, 26, 736, 53], [1025, 14, 736, 62], [1026, 16, 736, 62, "fileName"], [1026, 24, 736, 62], [1026, 26, 736, 62, "_jsxFileName"], [1026, 38, 736, 62], [1027, 16, 736, 62, "lineNumber"], [1027, 26, 736, 62], [1028, 16, 736, 62, "columnNumber"], [1028, 28, 736, 62], [1029, 14, 736, 62], [1029, 21, 736, 68], [1030, 12, 736, 69], [1031, 14, 736, 69, "fileName"], [1031, 22, 736, 69], [1031, 24, 736, 69, "_jsxFileName"], [1031, 36, 736, 69], [1032, 14, 736, 69, "lineNumber"], [1032, 24, 736, 69], [1033, 14, 736, 69, "columnNumber"], [1033, 26, 736, 69], [1034, 12, 736, 69], [1034, 19, 737, 30], [1034, 20, 737, 31], [1034, 35, 738, 12], [1034, 39, 738, 12, "_jsxDevRuntime"], [1034, 53, 738, 12], [1034, 54, 738, 12, "jsxDEV"], [1034, 60, 738, 12], [1034, 62, 738, 13, "_TouchableOpacity"], [1034, 79, 738, 13], [1034, 80, 738, 13, "default"], [1034, 87, 738, 29], [1035, 14, 739, 14, "onPress"], [1035, 21, 739, 21], [1035, 23, 739, 23, "onCancel"], [1035, 31, 739, 32], [1036, 14, 740, 14, "style"], [1036, 19, 740, 19], [1036, 21, 740, 21, "styles"], [1036, 27, 740, 27], [1036, 28, 740, 28, "secondaryButton"], [1036, 43, 740, 44], [1037, 14, 740, 44, "children"], [1037, 22, 740, 44], [1037, 37, 742, 14], [1037, 41, 742, 14, "_jsxDevRuntime"], [1037, 55, 742, 14], [1037, 56, 742, 14, "jsxDEV"], [1037, 62, 742, 14], [1037, 64, 742, 15, "_Text"], [1037, 69, 742, 15], [1037, 70, 742, 15, "default"], [1037, 77, 742, 19], [1038, 16, 742, 20, "style"], [1038, 21, 742, 25], [1038, 23, 742, 27, "styles"], [1038, 29, 742, 33], [1038, 30, 742, 34, "secondaryButtonText"], [1038, 49, 742, 54], [1039, 16, 742, 54, "children"], [1039, 24, 742, 54], [1039, 26, 742, 55], [1040, 14, 742, 61], [1041, 16, 742, 61, "fileName"], [1041, 24, 742, 61], [1041, 26, 742, 61, "_jsxFileName"], [1041, 38, 742, 61], [1042, 16, 742, 61, "lineNumber"], [1042, 26, 742, 61], [1043, 16, 742, 61, "columnNumber"], [1043, 28, 742, 61], [1044, 14, 742, 61], [1044, 21, 742, 67], [1045, 12, 742, 68], [1046, 14, 742, 68, "fileName"], [1046, 22, 742, 68], [1046, 24, 742, 68, "_jsxFileName"], [1046, 36, 742, 68], [1047, 14, 742, 68, "lineNumber"], [1047, 24, 742, 68], [1048, 14, 742, 68, "columnNumber"], [1048, 26, 742, 68], [1049, 12, 742, 68], [1049, 19, 743, 30], [1049, 20, 743, 31], [1050, 10, 743, 31], [1051, 12, 743, 31, "fileName"], [1051, 20, 743, 31], [1051, 22, 743, 31, "_jsxFileName"], [1051, 34, 743, 31], [1052, 12, 743, 31, "lineNumber"], [1052, 22, 743, 31], [1053, 12, 743, 31, "columnNumber"], [1053, 24, 743, 31], [1054, 10, 743, 31], [1054, 17, 744, 16], [1055, 8, 744, 17], [1056, 10, 744, 17, "fileName"], [1056, 18, 744, 17], [1056, 20, 744, 17, "_jsxFileName"], [1056, 32, 744, 17], [1057, 10, 744, 17, "lineNumber"], [1057, 20, 744, 17], [1058, 10, 744, 17, "columnNumber"], [1058, 22, 744, 17], [1059, 8, 744, 17], [1059, 15, 745, 14], [1060, 6, 745, 15], [1061, 8, 745, 15, "fileName"], [1061, 16, 745, 15], [1061, 18, 745, 15, "_jsxFileName"], [1061, 30, 745, 15], [1062, 8, 745, 15, "lineNumber"], [1062, 18, 745, 15], [1063, 8, 745, 15, "columnNumber"], [1063, 20, 745, 15], [1064, 6, 745, 15], [1064, 13, 746, 13], [1064, 14, 746, 14], [1065, 4, 746, 14], [1066, 6, 746, 14, "fileName"], [1066, 14, 746, 14], [1066, 16, 746, 14, "_jsxFileName"], [1066, 28, 746, 14], [1067, 6, 746, 14, "lineNumber"], [1067, 16, 746, 14], [1068, 6, 746, 14, "columnNumber"], [1068, 18, 746, 14], [1069, 4, 746, 14], [1069, 11, 747, 10], [1069, 12, 747, 11], [1070, 2, 749, 0], [1071, 2, 749, 1, "_s"], [1071, 4, 749, 1], [1071, 5, 51, 24, "EchoCameraWeb"], [1071, 18, 51, 37], [1072, 4, 51, 37], [1072, 12, 58, 42, "useCameraPermissions"], [1072, 44, 58, 62], [1072, 46, 72, 19, "useUpload"], [1072, 64, 72, 28], [1073, 2, 72, 28], [1074, 2, 72, 28, "_c"], [1074, 4, 72, 28], [1074, 7, 51, 24, "EchoCameraWeb"], [1074, 20, 51, 37], [1075, 2, 750, 0], [1075, 8, 750, 6, "styles"], [1075, 14, 750, 12], [1075, 17, 750, 15, "StyleSheet"], [1075, 36, 750, 25], [1075, 37, 750, 26, "create"], [1075, 43, 750, 32], [1075, 44, 750, 33], [1076, 4, 751, 2, "container"], [1076, 13, 751, 11], [1076, 15, 751, 13], [1077, 6, 752, 4, "flex"], [1077, 10, 752, 8], [1077, 12, 752, 10], [1077, 13, 752, 11], [1078, 6, 753, 4, "backgroundColor"], [1078, 21, 753, 19], [1078, 23, 753, 21], [1079, 4, 754, 2], [1079, 5, 754, 3], [1080, 4, 755, 2, "cameraContainer"], [1080, 19, 755, 17], [1080, 21, 755, 19], [1081, 6, 756, 4, "flex"], [1081, 10, 756, 8], [1081, 12, 756, 10], [1081, 13, 756, 11], [1082, 6, 757, 4, "max<PERSON><PERSON><PERSON>"], [1082, 14, 757, 12], [1082, 16, 757, 14], [1082, 19, 757, 17], [1083, 6, 758, 4, "alignSelf"], [1083, 15, 758, 13], [1083, 17, 758, 15], [1083, 25, 758, 23], [1084, 6, 759, 4, "width"], [1084, 11, 759, 9], [1084, 13, 759, 11], [1085, 4, 760, 2], [1085, 5, 760, 3], [1086, 4, 761, 2, "camera"], [1086, 10, 761, 8], [1086, 12, 761, 10], [1087, 6, 762, 4, "flex"], [1087, 10, 762, 8], [1087, 12, 762, 10], [1088, 4, 763, 2], [1088, 5, 763, 3], [1089, 4, 764, 2, "headerOverlay"], [1089, 17, 764, 15], [1089, 19, 764, 17], [1090, 6, 765, 4, "position"], [1090, 14, 765, 12], [1090, 16, 765, 14], [1090, 26, 765, 24], [1091, 6, 766, 4, "top"], [1091, 9, 766, 7], [1091, 11, 766, 9], [1091, 12, 766, 10], [1092, 6, 767, 4, "left"], [1092, 10, 767, 8], [1092, 12, 767, 10], [1092, 13, 767, 11], [1093, 6, 768, 4, "right"], [1093, 11, 768, 9], [1093, 13, 768, 11], [1093, 14, 768, 12], [1094, 6, 769, 4, "backgroundColor"], [1094, 21, 769, 19], [1094, 23, 769, 21], [1094, 36, 769, 34], [1095, 6, 770, 4, "paddingTop"], [1095, 16, 770, 14], [1095, 18, 770, 16], [1095, 20, 770, 18], [1096, 6, 771, 4, "paddingHorizontal"], [1096, 23, 771, 21], [1096, 25, 771, 23], [1096, 27, 771, 25], [1097, 6, 772, 4, "paddingBottom"], [1097, 19, 772, 17], [1097, 21, 772, 19], [1098, 4, 773, 2], [1098, 5, 773, 3], [1099, 4, 774, 2, "headerContent"], [1099, 17, 774, 15], [1099, 19, 774, 17], [1100, 6, 775, 4, "flexDirection"], [1100, 19, 775, 17], [1100, 21, 775, 19], [1100, 26, 775, 24], [1101, 6, 776, 4, "justifyContent"], [1101, 20, 776, 18], [1101, 22, 776, 20], [1101, 37, 776, 35], [1102, 6, 777, 4, "alignItems"], [1102, 16, 777, 14], [1102, 18, 777, 16], [1103, 4, 778, 2], [1103, 5, 778, 3], [1104, 4, 779, 2, "headerLeft"], [1104, 14, 779, 12], [1104, 16, 779, 14], [1105, 6, 780, 4, "flex"], [1105, 10, 780, 8], [1105, 12, 780, 10], [1106, 4, 781, 2], [1106, 5, 781, 3], [1107, 4, 782, 2, "headerTitle"], [1107, 15, 782, 13], [1107, 17, 782, 15], [1108, 6, 783, 4, "fontSize"], [1108, 14, 783, 12], [1108, 16, 783, 14], [1108, 18, 783, 16], [1109, 6, 784, 4, "fontWeight"], [1109, 16, 784, 14], [1109, 18, 784, 16], [1109, 23, 784, 21], [1110, 6, 785, 4, "color"], [1110, 11, 785, 9], [1110, 13, 785, 11], [1110, 19, 785, 17], [1111, 6, 786, 4, "marginBottom"], [1111, 18, 786, 16], [1111, 20, 786, 18], [1112, 4, 787, 2], [1112, 5, 787, 3], [1113, 4, 788, 2, "subtitleRow"], [1113, 15, 788, 13], [1113, 17, 788, 15], [1114, 6, 789, 4, "flexDirection"], [1114, 19, 789, 17], [1114, 21, 789, 19], [1114, 26, 789, 24], [1115, 6, 790, 4, "alignItems"], [1115, 16, 790, 14], [1115, 18, 790, 16], [1115, 26, 790, 24], [1116, 6, 791, 4, "marginBottom"], [1116, 18, 791, 16], [1116, 20, 791, 18], [1117, 4, 792, 2], [1117, 5, 792, 3], [1118, 4, 793, 2, "webIcon"], [1118, 11, 793, 9], [1118, 13, 793, 11], [1119, 6, 794, 4, "fontSize"], [1119, 14, 794, 12], [1119, 16, 794, 14], [1119, 18, 794, 16], [1120, 6, 795, 4, "marginRight"], [1120, 17, 795, 15], [1120, 19, 795, 17], [1121, 4, 796, 2], [1121, 5, 796, 3], [1122, 4, 797, 2, "headerSubtitle"], [1122, 18, 797, 16], [1122, 20, 797, 18], [1123, 6, 798, 4, "fontSize"], [1123, 14, 798, 12], [1123, 16, 798, 14], [1123, 18, 798, 16], [1124, 6, 799, 4, "color"], [1124, 11, 799, 9], [1124, 13, 799, 11], [1124, 19, 799, 17], [1125, 6, 800, 4, "opacity"], [1125, 13, 800, 11], [1125, 15, 800, 13], [1126, 4, 801, 2], [1126, 5, 801, 3], [1127, 4, 802, 2, "challengeRow"], [1127, 16, 802, 14], [1127, 18, 802, 16], [1128, 6, 803, 4, "flexDirection"], [1128, 19, 803, 17], [1128, 21, 803, 19], [1128, 26, 803, 24], [1129, 6, 804, 4, "alignItems"], [1129, 16, 804, 14], [1129, 18, 804, 16], [1130, 4, 805, 2], [1130, 5, 805, 3], [1131, 4, 806, 2, "challengeCode"], [1131, 17, 806, 15], [1131, 19, 806, 17], [1132, 6, 807, 4, "fontSize"], [1132, 14, 807, 12], [1132, 16, 807, 14], [1132, 18, 807, 16], [1133, 6, 808, 4, "color"], [1133, 11, 808, 9], [1133, 13, 808, 11], [1133, 19, 808, 17], [1134, 6, 809, 4, "marginLeft"], [1134, 16, 809, 14], [1134, 18, 809, 16], [1134, 19, 809, 17], [1135, 6, 810, 4, "fontFamily"], [1135, 16, 810, 14], [1135, 18, 810, 16], [1136, 4, 811, 2], [1136, 5, 811, 3], [1137, 4, 812, 2, "closeButton"], [1137, 15, 812, 13], [1137, 17, 812, 15], [1138, 6, 813, 4, "padding"], [1138, 13, 813, 11], [1138, 15, 813, 13], [1139, 4, 814, 2], [1139, 5, 814, 3], [1140, 4, 815, 2, "privacyNotice"], [1140, 17, 815, 15], [1140, 19, 815, 17], [1141, 6, 816, 4, "position"], [1141, 14, 816, 12], [1141, 16, 816, 14], [1141, 26, 816, 24], [1142, 6, 817, 4, "top"], [1142, 9, 817, 7], [1142, 11, 817, 9], [1142, 14, 817, 12], [1143, 6, 818, 4, "left"], [1143, 10, 818, 8], [1143, 12, 818, 10], [1143, 14, 818, 12], [1144, 6, 819, 4, "right"], [1144, 11, 819, 9], [1144, 13, 819, 11], [1144, 15, 819, 13], [1145, 6, 820, 4, "backgroundColor"], [1145, 21, 820, 19], [1145, 23, 820, 21], [1145, 48, 820, 46], [1146, 6, 821, 4, "borderRadius"], [1146, 18, 821, 16], [1146, 20, 821, 18], [1146, 21, 821, 19], [1147, 6, 822, 4, "padding"], [1147, 13, 822, 11], [1147, 15, 822, 13], [1147, 17, 822, 15], [1148, 6, 823, 4, "flexDirection"], [1148, 19, 823, 17], [1148, 21, 823, 19], [1148, 26, 823, 24], [1149, 6, 824, 4, "alignItems"], [1149, 16, 824, 14], [1149, 18, 824, 16], [1150, 4, 825, 2], [1150, 5, 825, 3], [1151, 4, 826, 2, "privacyText"], [1151, 15, 826, 13], [1151, 17, 826, 15], [1152, 6, 827, 4, "color"], [1152, 11, 827, 9], [1152, 13, 827, 11], [1152, 19, 827, 17], [1153, 6, 828, 4, "fontSize"], [1153, 14, 828, 12], [1153, 16, 828, 14], [1153, 18, 828, 16], [1154, 6, 829, 4, "marginLeft"], [1154, 16, 829, 14], [1154, 18, 829, 16], [1154, 19, 829, 17], [1155, 6, 830, 4, "flex"], [1155, 10, 830, 8], [1155, 12, 830, 10], [1156, 4, 831, 2], [1156, 5, 831, 3], [1157, 4, 832, 2, "footer<PERSON><PERSON><PERSON>"], [1157, 17, 832, 15], [1157, 19, 832, 17], [1158, 6, 833, 4, "position"], [1158, 14, 833, 12], [1158, 16, 833, 14], [1158, 26, 833, 24], [1159, 6, 834, 4, "bottom"], [1159, 12, 834, 10], [1159, 14, 834, 12], [1159, 15, 834, 13], [1160, 6, 835, 4, "left"], [1160, 10, 835, 8], [1160, 12, 835, 10], [1160, 13, 835, 11], [1161, 6, 836, 4, "right"], [1161, 11, 836, 9], [1161, 13, 836, 11], [1161, 14, 836, 12], [1162, 6, 837, 4, "backgroundColor"], [1162, 21, 837, 19], [1162, 23, 837, 21], [1162, 36, 837, 34], [1163, 6, 838, 4, "paddingBottom"], [1163, 19, 838, 17], [1163, 21, 838, 19], [1163, 23, 838, 21], [1164, 6, 839, 4, "paddingTop"], [1164, 16, 839, 14], [1164, 18, 839, 16], [1164, 20, 839, 18], [1165, 6, 840, 4, "alignItems"], [1165, 16, 840, 14], [1165, 18, 840, 16], [1166, 4, 841, 2], [1166, 5, 841, 3], [1167, 4, 842, 2, "instruction"], [1167, 15, 842, 13], [1167, 17, 842, 15], [1168, 6, 843, 4, "fontSize"], [1168, 14, 843, 12], [1168, 16, 843, 14], [1168, 18, 843, 16], [1169, 6, 844, 4, "color"], [1169, 11, 844, 9], [1169, 13, 844, 11], [1169, 19, 844, 17], [1170, 6, 845, 4, "marginBottom"], [1170, 18, 845, 16], [1170, 20, 845, 18], [1171, 4, 846, 2], [1171, 5, 846, 3], [1172, 4, 847, 2, "shutterButton"], [1172, 17, 847, 15], [1172, 19, 847, 17], [1173, 6, 848, 4, "width"], [1173, 11, 848, 9], [1173, 13, 848, 11], [1173, 15, 848, 13], [1174, 6, 849, 4, "height"], [1174, 12, 849, 10], [1174, 14, 849, 12], [1174, 16, 849, 14], [1175, 6, 850, 4, "borderRadius"], [1175, 18, 850, 16], [1175, 20, 850, 18], [1175, 22, 850, 20], [1176, 6, 851, 4, "backgroundColor"], [1176, 21, 851, 19], [1176, 23, 851, 21], [1176, 29, 851, 27], [1177, 6, 852, 4, "justifyContent"], [1177, 20, 852, 18], [1177, 22, 852, 20], [1177, 30, 852, 28], [1178, 6, 853, 4, "alignItems"], [1178, 16, 853, 14], [1178, 18, 853, 16], [1178, 26, 853, 24], [1179, 6, 854, 4, "marginBottom"], [1179, 18, 854, 16], [1179, 20, 854, 18], [1179, 22, 854, 20], [1180, 6, 855, 4], [1180, 9, 855, 7, "Platform"], [1180, 26, 855, 15], [1180, 27, 855, 16, "select"], [1180, 33, 855, 22], [1180, 34, 855, 23], [1181, 8, 856, 6, "ios"], [1181, 11, 856, 9], [1181, 13, 856, 11], [1182, 10, 857, 8, "shadowColor"], [1182, 21, 857, 19], [1182, 23, 857, 21], [1182, 32, 857, 30], [1183, 10, 858, 8, "shadowOffset"], [1183, 22, 858, 20], [1183, 24, 858, 22], [1184, 12, 858, 24, "width"], [1184, 17, 858, 29], [1184, 19, 858, 31], [1184, 20, 858, 32], [1185, 12, 858, 34, "height"], [1185, 18, 858, 40], [1185, 20, 858, 42], [1186, 10, 858, 44], [1186, 11, 858, 45], [1187, 10, 859, 8, "shadowOpacity"], [1187, 23, 859, 21], [1187, 25, 859, 23], [1187, 28, 859, 26], [1188, 10, 860, 8, "shadowRadius"], [1188, 22, 860, 20], [1188, 24, 860, 22], [1189, 8, 861, 6], [1189, 9, 861, 7], [1190, 8, 862, 6, "android"], [1190, 15, 862, 13], [1190, 17, 862, 15], [1191, 10, 863, 8, "elevation"], [1191, 19, 863, 17], [1191, 21, 863, 19], [1192, 8, 864, 6], [1192, 9, 864, 7], [1193, 8, 865, 6, "web"], [1193, 11, 865, 9], [1193, 13, 865, 11], [1194, 10, 866, 8, "boxShadow"], [1194, 19, 866, 17], [1194, 21, 866, 19], [1195, 8, 867, 6], [1196, 6, 868, 4], [1196, 7, 868, 5], [1197, 4, 869, 2], [1197, 5, 869, 3], [1198, 4, 870, 2, "shutterButtonDisabled"], [1198, 25, 870, 23], [1198, 27, 870, 25], [1199, 6, 871, 4, "opacity"], [1199, 13, 871, 11], [1199, 15, 871, 13], [1200, 4, 872, 2], [1200, 5, 872, 3], [1201, 4, 873, 2, "shutterInner"], [1201, 16, 873, 14], [1201, 18, 873, 16], [1202, 6, 874, 4, "width"], [1202, 11, 874, 9], [1202, 13, 874, 11], [1202, 15, 874, 13], [1203, 6, 875, 4, "height"], [1203, 12, 875, 10], [1203, 14, 875, 12], [1203, 16, 875, 14], [1204, 6, 876, 4, "borderRadius"], [1204, 18, 876, 16], [1204, 20, 876, 18], [1204, 22, 876, 20], [1205, 6, 877, 4, "backgroundColor"], [1205, 21, 877, 19], [1205, 23, 877, 21], [1205, 29, 877, 27], [1206, 6, 878, 4, "borderWidth"], [1206, 17, 878, 15], [1206, 19, 878, 17], [1206, 20, 878, 18], [1207, 6, 879, 4, "borderColor"], [1207, 17, 879, 15], [1207, 19, 879, 17], [1208, 4, 880, 2], [1208, 5, 880, 3], [1209, 4, 881, 2, "privacyNote"], [1209, 15, 881, 13], [1209, 17, 881, 15], [1210, 6, 882, 4, "fontSize"], [1210, 14, 882, 12], [1210, 16, 882, 14], [1210, 18, 882, 16], [1211, 6, 883, 4, "color"], [1211, 11, 883, 9], [1211, 13, 883, 11], [1212, 4, 884, 2], [1212, 5, 884, 3], [1213, 4, 885, 2, "processingModal"], [1213, 19, 885, 17], [1213, 21, 885, 19], [1214, 6, 886, 4, "flex"], [1214, 10, 886, 8], [1214, 12, 886, 10], [1214, 13, 886, 11], [1215, 6, 887, 4, "backgroundColor"], [1215, 21, 887, 19], [1215, 23, 887, 21], [1215, 43, 887, 41], [1216, 6, 888, 4, "justifyContent"], [1216, 20, 888, 18], [1216, 22, 888, 20], [1216, 30, 888, 28], [1217, 6, 889, 4, "alignItems"], [1217, 16, 889, 14], [1217, 18, 889, 16], [1218, 4, 890, 2], [1218, 5, 890, 3], [1219, 4, 891, 2, "processingContent"], [1219, 21, 891, 19], [1219, 23, 891, 21], [1220, 6, 892, 4, "backgroundColor"], [1220, 21, 892, 19], [1220, 23, 892, 21], [1220, 29, 892, 27], [1221, 6, 893, 4, "borderRadius"], [1221, 18, 893, 16], [1221, 20, 893, 18], [1221, 22, 893, 20], [1222, 6, 894, 4, "padding"], [1222, 13, 894, 11], [1222, 15, 894, 13], [1222, 17, 894, 15], [1223, 6, 895, 4, "width"], [1223, 11, 895, 9], [1223, 13, 895, 11], [1223, 18, 895, 16], [1224, 6, 896, 4, "max<PERSON><PERSON><PERSON>"], [1224, 14, 896, 12], [1224, 16, 896, 14], [1224, 19, 896, 17], [1225, 6, 897, 4, "alignItems"], [1225, 16, 897, 14], [1225, 18, 897, 16], [1226, 4, 898, 2], [1226, 5, 898, 3], [1227, 4, 899, 2, "processingTitle"], [1227, 19, 899, 17], [1227, 21, 899, 19], [1228, 6, 900, 4, "fontSize"], [1228, 14, 900, 12], [1228, 16, 900, 14], [1228, 18, 900, 16], [1229, 6, 901, 4, "fontWeight"], [1229, 16, 901, 14], [1229, 18, 901, 16], [1229, 23, 901, 21], [1230, 6, 902, 4, "color"], [1230, 11, 902, 9], [1230, 13, 902, 11], [1230, 22, 902, 20], [1231, 6, 903, 4, "marginTop"], [1231, 15, 903, 13], [1231, 17, 903, 15], [1231, 19, 903, 17], [1232, 6, 904, 4, "marginBottom"], [1232, 18, 904, 16], [1232, 20, 904, 18], [1233, 4, 905, 2], [1233, 5, 905, 3], [1234, 4, 906, 2, "progressBar"], [1234, 15, 906, 13], [1234, 17, 906, 15], [1235, 6, 907, 4, "width"], [1235, 11, 907, 9], [1235, 13, 907, 11], [1235, 19, 907, 17], [1236, 6, 908, 4, "height"], [1236, 12, 908, 10], [1236, 14, 908, 12], [1236, 15, 908, 13], [1237, 6, 909, 4, "backgroundColor"], [1237, 21, 909, 19], [1237, 23, 909, 21], [1237, 32, 909, 30], [1238, 6, 910, 4, "borderRadius"], [1238, 18, 910, 16], [1238, 20, 910, 18], [1238, 21, 910, 19], [1239, 6, 911, 4, "overflow"], [1239, 14, 911, 12], [1239, 16, 911, 14], [1239, 24, 911, 22], [1240, 6, 912, 4, "marginBottom"], [1240, 18, 912, 16], [1240, 20, 912, 18], [1241, 4, 913, 2], [1241, 5, 913, 3], [1242, 4, 914, 2, "progressFill"], [1242, 16, 914, 14], [1242, 18, 914, 16], [1243, 6, 915, 4, "height"], [1243, 12, 915, 10], [1243, 14, 915, 12], [1243, 20, 915, 18], [1244, 6, 916, 4, "backgroundColor"], [1244, 21, 916, 19], [1244, 23, 916, 21], [1244, 32, 916, 30], [1245, 6, 917, 4, "borderRadius"], [1245, 18, 917, 16], [1245, 20, 917, 18], [1246, 4, 918, 2], [1246, 5, 918, 3], [1247, 4, 919, 2, "processingDescription"], [1247, 25, 919, 23], [1247, 27, 919, 25], [1248, 6, 920, 4, "fontSize"], [1248, 14, 920, 12], [1248, 16, 920, 14], [1248, 18, 920, 16], [1249, 6, 921, 4, "color"], [1249, 11, 921, 9], [1249, 13, 921, 11], [1249, 22, 921, 20], [1250, 6, 922, 4, "textAlign"], [1250, 15, 922, 13], [1250, 17, 922, 15], [1251, 4, 923, 2], [1251, 5, 923, 3], [1252, 4, 924, 2, "successIcon"], [1252, 15, 924, 13], [1252, 17, 924, 15], [1253, 6, 925, 4, "marginTop"], [1253, 15, 925, 13], [1253, 17, 925, 15], [1254, 4, 926, 2], [1254, 5, 926, 3], [1255, 4, 927, 2, "errorContent"], [1255, 16, 927, 14], [1255, 18, 927, 16], [1256, 6, 928, 4, "backgroundColor"], [1256, 21, 928, 19], [1256, 23, 928, 21], [1256, 29, 928, 27], [1257, 6, 929, 4, "borderRadius"], [1257, 18, 929, 16], [1257, 20, 929, 18], [1257, 22, 929, 20], [1258, 6, 930, 4, "padding"], [1258, 13, 930, 11], [1258, 15, 930, 13], [1258, 17, 930, 15], [1259, 6, 931, 4, "width"], [1259, 11, 931, 9], [1259, 13, 931, 11], [1259, 18, 931, 16], [1260, 6, 932, 4, "max<PERSON><PERSON><PERSON>"], [1260, 14, 932, 12], [1260, 16, 932, 14], [1260, 19, 932, 17], [1261, 6, 933, 4, "alignItems"], [1261, 16, 933, 14], [1261, 18, 933, 16], [1262, 4, 934, 2], [1262, 5, 934, 3], [1263, 4, 935, 2, "errorTitle"], [1263, 14, 935, 12], [1263, 16, 935, 14], [1264, 6, 936, 4, "fontSize"], [1264, 14, 936, 12], [1264, 16, 936, 14], [1264, 18, 936, 16], [1265, 6, 937, 4, "fontWeight"], [1265, 16, 937, 14], [1265, 18, 937, 16], [1265, 23, 937, 21], [1266, 6, 938, 4, "color"], [1266, 11, 938, 9], [1266, 13, 938, 11], [1266, 22, 938, 20], [1267, 6, 939, 4, "marginTop"], [1267, 15, 939, 13], [1267, 17, 939, 15], [1267, 19, 939, 17], [1268, 6, 940, 4, "marginBottom"], [1268, 18, 940, 16], [1268, 20, 940, 18], [1269, 4, 941, 2], [1269, 5, 941, 3], [1270, 4, 942, 2, "errorMessage"], [1270, 16, 942, 14], [1270, 18, 942, 16], [1271, 6, 943, 4, "fontSize"], [1271, 14, 943, 12], [1271, 16, 943, 14], [1271, 18, 943, 16], [1272, 6, 944, 4, "color"], [1272, 11, 944, 9], [1272, 13, 944, 11], [1272, 22, 944, 20], [1273, 6, 945, 4, "textAlign"], [1273, 15, 945, 13], [1273, 17, 945, 15], [1273, 25, 945, 23], [1274, 6, 946, 4, "marginBottom"], [1274, 18, 946, 16], [1274, 20, 946, 18], [1275, 4, 947, 2], [1275, 5, 947, 3], [1276, 4, 948, 2, "primaryButton"], [1276, 17, 948, 15], [1276, 19, 948, 17], [1277, 6, 949, 4, "backgroundColor"], [1277, 21, 949, 19], [1277, 23, 949, 21], [1277, 32, 949, 30], [1278, 6, 950, 4, "paddingHorizontal"], [1278, 23, 950, 21], [1278, 25, 950, 23], [1278, 27, 950, 25], [1279, 6, 951, 4, "paddingVertical"], [1279, 21, 951, 19], [1279, 23, 951, 21], [1279, 25, 951, 23], [1280, 6, 952, 4, "borderRadius"], [1280, 18, 952, 16], [1280, 20, 952, 18], [1280, 21, 952, 19], [1281, 6, 953, 4, "marginTop"], [1281, 15, 953, 13], [1281, 17, 953, 15], [1282, 4, 954, 2], [1282, 5, 954, 3], [1283, 4, 955, 2, "primaryButtonText"], [1283, 21, 955, 19], [1283, 23, 955, 21], [1284, 6, 956, 4, "color"], [1284, 11, 956, 9], [1284, 13, 956, 11], [1284, 19, 956, 17], [1285, 6, 957, 4, "fontSize"], [1285, 14, 957, 12], [1285, 16, 957, 14], [1285, 18, 957, 16], [1286, 6, 958, 4, "fontWeight"], [1286, 16, 958, 14], [1286, 18, 958, 16], [1287, 4, 959, 2], [1287, 5, 959, 3], [1288, 4, 960, 2, "secondaryButton"], [1288, 19, 960, 17], [1288, 21, 960, 19], [1289, 6, 961, 4, "paddingHorizontal"], [1289, 23, 961, 21], [1289, 25, 961, 23], [1289, 27, 961, 25], [1290, 6, 962, 4, "paddingVertical"], [1290, 21, 962, 19], [1290, 23, 962, 21], [1290, 25, 962, 23], [1291, 6, 963, 4, "marginTop"], [1291, 15, 963, 13], [1291, 17, 963, 15], [1292, 4, 964, 2], [1292, 5, 964, 3], [1293, 4, 965, 2, "secondaryButtonText"], [1293, 23, 965, 21], [1293, 25, 965, 23], [1294, 6, 966, 4, "color"], [1294, 11, 966, 9], [1294, 13, 966, 11], [1294, 22, 966, 20], [1295, 6, 967, 4, "fontSize"], [1295, 14, 967, 12], [1295, 16, 967, 14], [1296, 4, 968, 2], [1296, 5, 968, 3], [1297, 4, 969, 2, "permissionContent"], [1297, 21, 969, 19], [1297, 23, 969, 21], [1298, 6, 970, 4, "flex"], [1298, 10, 970, 8], [1298, 12, 970, 10], [1298, 13, 970, 11], [1299, 6, 971, 4, "justifyContent"], [1299, 20, 971, 18], [1299, 22, 971, 20], [1299, 30, 971, 28], [1300, 6, 972, 4, "alignItems"], [1300, 16, 972, 14], [1300, 18, 972, 16], [1300, 26, 972, 24], [1301, 6, 973, 4, "padding"], [1301, 13, 973, 11], [1301, 15, 973, 13], [1302, 4, 974, 2], [1302, 5, 974, 3], [1303, 4, 975, 2, "permissionTitle"], [1303, 19, 975, 17], [1303, 21, 975, 19], [1304, 6, 976, 4, "fontSize"], [1304, 14, 976, 12], [1304, 16, 976, 14], [1304, 18, 976, 16], [1305, 6, 977, 4, "fontWeight"], [1305, 16, 977, 14], [1305, 18, 977, 16], [1305, 23, 977, 21], [1306, 6, 978, 4, "color"], [1306, 11, 978, 9], [1306, 13, 978, 11], [1306, 22, 978, 20], [1307, 6, 979, 4, "marginTop"], [1307, 15, 979, 13], [1307, 17, 979, 15], [1307, 19, 979, 17], [1308, 6, 980, 4, "marginBottom"], [1308, 18, 980, 16], [1308, 20, 980, 18], [1309, 4, 981, 2], [1309, 5, 981, 3], [1310, 4, 982, 2, "permissionDescription"], [1310, 25, 982, 23], [1310, 27, 982, 25], [1311, 6, 983, 4, "fontSize"], [1311, 14, 983, 12], [1311, 16, 983, 14], [1311, 18, 983, 16], [1312, 6, 984, 4, "color"], [1312, 11, 984, 9], [1312, 13, 984, 11], [1312, 22, 984, 20], [1313, 6, 985, 4, "textAlign"], [1313, 15, 985, 13], [1313, 17, 985, 15], [1313, 25, 985, 23], [1314, 6, 986, 4, "marginBottom"], [1314, 18, 986, 16], [1314, 20, 986, 18], [1315, 4, 987, 2], [1315, 5, 987, 3], [1316, 4, 988, 2, "loadingText"], [1316, 15, 988, 13], [1316, 17, 988, 15], [1317, 6, 989, 4, "color"], [1317, 11, 989, 9], [1317, 13, 989, 11], [1317, 22, 989, 20], [1318, 6, 990, 4, "marginTop"], [1318, 15, 990, 13], [1318, 17, 990, 15], [1319, 4, 991, 2], [1319, 5, 991, 3], [1320, 4, 992, 2], [1321, 4, 993, 2, "blurZone"], [1321, 12, 993, 10], [1321, 14, 993, 12], [1322, 6, 994, 4, "position"], [1322, 14, 994, 12], [1322, 16, 994, 14], [1322, 26, 994, 24], [1323, 6, 995, 4, "overflow"], [1323, 14, 995, 12], [1323, 16, 995, 14], [1324, 4, 996, 2], [1324, 5, 996, 3], [1325, 4, 997, 2, "previewChip"], [1325, 15, 997, 13], [1325, 17, 997, 15], [1326, 6, 998, 4, "position"], [1326, 14, 998, 12], [1326, 16, 998, 14], [1326, 26, 998, 24], [1327, 6, 999, 4, "top"], [1327, 9, 999, 7], [1327, 11, 999, 9], [1327, 12, 999, 10], [1328, 6, 1000, 4, "right"], [1328, 11, 1000, 9], [1328, 13, 1000, 11], [1328, 14, 1000, 12], [1329, 6, 1001, 4, "backgroundColor"], [1329, 21, 1001, 19], [1329, 23, 1001, 21], [1329, 40, 1001, 38], [1330, 6, 1002, 4, "paddingHorizontal"], [1330, 23, 1002, 21], [1330, 25, 1002, 23], [1330, 27, 1002, 25], [1331, 6, 1003, 4, "paddingVertical"], [1331, 21, 1003, 19], [1331, 23, 1003, 21], [1331, 24, 1003, 22], [1332, 6, 1004, 4, "borderRadius"], [1332, 18, 1004, 16], [1332, 20, 1004, 18], [1333, 4, 1005, 2], [1333, 5, 1005, 3], [1334, 4, 1006, 2, "previewChipText"], [1334, 19, 1006, 17], [1334, 21, 1006, 19], [1335, 6, 1007, 4, "color"], [1335, 11, 1007, 9], [1335, 13, 1007, 11], [1335, 19, 1007, 17], [1336, 6, 1008, 4, "fontSize"], [1336, 14, 1008, 12], [1336, 16, 1008, 14], [1336, 18, 1008, 16], [1337, 6, 1009, 4, "fontWeight"], [1337, 16, 1009, 14], [1337, 18, 1009, 16], [1338, 4, 1010, 2], [1339, 2, 1011, 0], [1339, 3, 1011, 1], [1339, 4, 1011, 2], [1340, 2, 1011, 3], [1340, 6, 1011, 3, "_c"], [1340, 8, 1011, 3], [1341, 2, 1011, 3, "$RefreshReg$"], [1341, 14, 1011, 3], [1341, 15, 1011, 3, "_c"], [1341, 17, 1011, 3], [1342, 0, 1011, 3], [1342, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "capturePhoto", "Promise$argument_0", "processImageWithFaceBlur", "browserDetections.map$argument_0", "faceDetections.map$argument_0", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;mCGE;wBCc,kCD;GHkC;mCKE;wBDY;OCI;gDC8B;YDO;8BDa;aCM;6CEc;YFO;oFGsB;UHM;8BIS;SJmD;uDDQ;sBMC,wBN;OCC;GLc;6BWG;GXyB;kCYG;GZ8C;4BaE;mBCmD;SDE;GbO;uBeE;GfI;mCgBG;GhBM;YCE;GDK;oBiB2C;WjBG;yBkBC;WlBG;wBmBC;WnBI;CD4L"}}, "type": "js/module"}]}