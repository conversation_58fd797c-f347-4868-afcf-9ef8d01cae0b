{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  'use strict';\n\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.SequencedTransition = SequencedTransition;\n  function SequencedTransition(name, transitionData) {\n    const {\n      translateX,\n      translateY,\n      scaleX,\n      scaleY,\n      reversed\n    } = transitionData;\n    const scaleValue = reversed ? `1,${scaleX}` : `${scaleY},1`;\n    const sequencedTransition = {\n      name,\n      style: {\n        0: {\n          transform: [{\n            translateX: `${translateX}px`,\n            translateY: `${translateY}px`,\n            scale: `${scaleX},${scaleY}`\n          }]\n        },\n        50: {\n          transform: [{\n            translateX: reversed ? `${translateX}px` : '0px',\n            translateY: reversed ? '0px' : `${translateY}px`,\n            scale: scaleValue\n          }]\n        },\n        100: {\n          transform: [{\n            translateX: '0px',\n            translateY: '0px',\n            scale: '1,1'\n          }]\n        }\n      },\n      duration: 300\n    };\n    return sequencedTransition;\n  }\n});", "lineCount": 46, "map": [[2, 2, 1, 0], [2, 14, 1, 12], [4, 2, 1, 13, "Object"], [4, 8, 1, 13], [4, 9, 1, 13, "defineProperty"], [4, 23, 1, 13], [4, 24, 1, 13, "exports"], [4, 31, 1, 13], [5, 4, 1, 13, "value"], [5, 9, 1, 13], [6, 2, 1, 13], [7, 2, 1, 13, "exports"], [7, 9, 1, 13], [7, 10, 1, 13, "SequencedTransition"], [7, 29, 1, 13], [7, 32, 1, 13, "SequencedTransition"], [7, 51, 1, 13], [8, 2, 3, 7], [8, 11, 3, 16, "SequencedTransition"], [8, 30, 3, 35, "SequencedTransition"], [8, 31, 3, 36, "name"], [8, 35, 3, 40], [8, 37, 3, 42, "transitionData"], [8, 51, 3, 56], [8, 53, 3, 58], [9, 4, 4, 2], [9, 10, 4, 8], [10, 6, 5, 4, "translateX"], [10, 16, 5, 14], [11, 6, 6, 4, "translateY"], [11, 16, 6, 14], [12, 6, 7, 4, "scaleX"], [12, 12, 7, 10], [13, 6, 8, 4, "scaleY"], [13, 12, 8, 10], [14, 6, 9, 4, "reversed"], [15, 4, 10, 2], [15, 5, 10, 3], [15, 8, 10, 6, "transitionData"], [15, 22, 10, 20], [16, 4, 11, 2], [16, 10, 11, 8, "scaleValue"], [16, 20, 11, 18], [16, 23, 11, 21, "reversed"], [16, 31, 11, 29], [16, 34, 11, 32], [16, 39, 11, 37, "scaleX"], [16, 45, 11, 43], [16, 47, 11, 45], [16, 50, 11, 48], [16, 53, 11, 51, "scaleY"], [16, 59, 11, 57], [16, 63, 11, 61], [17, 4, 12, 2], [17, 10, 12, 8, "sequencedTransition"], [17, 29, 12, 27], [17, 32, 12, 30], [18, 6, 13, 4, "name"], [18, 10, 13, 8], [19, 6, 14, 4, "style"], [19, 11, 14, 9], [19, 13, 14, 11], [20, 8, 15, 6], [20, 9, 15, 7], [20, 11, 15, 9], [21, 10, 16, 8, "transform"], [21, 19, 16, 17], [21, 21, 16, 19], [21, 22, 16, 20], [22, 12, 17, 10, "translateX"], [22, 22, 17, 20], [22, 24, 17, 22], [22, 27, 17, 25, "translateX"], [22, 37, 17, 35], [22, 41, 17, 39], [23, 12, 18, 10, "translateY"], [23, 22, 18, 20], [23, 24, 18, 22], [23, 27, 18, 25, "translateY"], [23, 37, 18, 35], [23, 41, 18, 39], [24, 12, 19, 10, "scale"], [24, 17, 19, 15], [24, 19, 19, 17], [24, 22, 19, 20, "scaleX"], [24, 28, 19, 26], [24, 32, 19, 30, "scaleY"], [24, 38, 19, 36], [25, 10, 20, 8], [25, 11, 20, 9], [26, 8, 21, 6], [26, 9, 21, 7], [27, 8, 22, 6], [27, 10, 22, 8], [27, 12, 22, 10], [28, 10, 23, 8, "transform"], [28, 19, 23, 17], [28, 21, 23, 19], [28, 22, 23, 20], [29, 12, 24, 10, "translateX"], [29, 22, 24, 20], [29, 24, 24, 22, "reversed"], [29, 32, 24, 30], [29, 35, 24, 33], [29, 38, 24, 36, "translateX"], [29, 48, 24, 46], [29, 52, 24, 50], [29, 55, 24, 53], [29, 60, 24, 58], [30, 12, 25, 10, "translateY"], [30, 22, 25, 20], [30, 24, 25, 22, "reversed"], [30, 32, 25, 30], [30, 35, 25, 33], [30, 40, 25, 38], [30, 43, 25, 41], [30, 46, 25, 44, "translateY"], [30, 56, 25, 54], [30, 60, 25, 58], [31, 12, 26, 10, "scale"], [31, 17, 26, 15], [31, 19, 26, 17, "scaleValue"], [32, 10, 27, 8], [32, 11, 27, 9], [33, 8, 28, 6], [33, 9, 28, 7], [34, 8, 29, 6], [34, 11, 29, 9], [34, 13, 29, 11], [35, 10, 30, 8, "transform"], [35, 19, 30, 17], [35, 21, 30, 19], [35, 22, 30, 20], [36, 12, 31, 10, "translateX"], [36, 22, 31, 20], [36, 24, 31, 22], [36, 29, 31, 27], [37, 12, 32, 10, "translateY"], [37, 22, 32, 20], [37, 24, 32, 22], [37, 29, 32, 27], [38, 12, 33, 10, "scale"], [38, 17, 33, 15], [38, 19, 33, 17], [39, 10, 34, 8], [39, 11, 34, 9], [40, 8, 35, 6], [41, 6, 36, 4], [41, 7, 36, 5], [42, 6, 37, 4, "duration"], [42, 14, 37, 12], [42, 16, 37, 14], [43, 4, 38, 2], [43, 5, 38, 3], [44, 4, 39, 2], [44, 11, 39, 9, "sequencedTransition"], [44, 30, 39, 28], [45, 2, 40, 0], [46, 0, 40, 1], [46, 3]], "functionMap": {"names": ["<global>", "SequencedTransition"], "mappings": "AAA;OCE;CDqC"}}, "type": "js/module"}]}