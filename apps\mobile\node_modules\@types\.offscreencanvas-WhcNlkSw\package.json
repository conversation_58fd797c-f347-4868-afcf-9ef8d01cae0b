{"name": "@types/offscreencanvas", "version": "2019.3.0", "description": "TypeScript definitions for offscreencanvas-browser", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/kayahr", "githubUsername": "kayahr"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/offscreencanvas"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "056f77b7b89302825048174c0427ed47126998207221760a9f2c9b47e0bacead", "typeScriptVersion": "2.0"}