{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "expo/virtual/env", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dgHc21cgR+buKc7O3/dChhD5JJk=", "exportNames": ["*"]}}, {"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 11, "column": 0, "index": 294}, "end": {"line": 11, "column": 72, "index": 366}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/View", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "4kYBDC6LJJXoH7P9rWDi3vkLVB0=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Text", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "JKIzsQ5YQ0gDj0MIyY0Q7F1zJtU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/StyleSheet", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "MK7+k1V+KnvCVW7Kj2k/ydtjmVU=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/TouchableOpacity", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PnQOoa8QGKpV5+issz6ikk463eg=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Alert", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "PEUC6jrQVoAGZ2qYkvimljMOyJI=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Dimensions", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "ySrYx/xxJL+A+Ie+sLy/r/EEnF8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/ActivityIndicator", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "bSAkUkqZq0shBb5bU6kCYXi4ciA=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Modal", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "Ezhl/vznHrlq0iqGXODlZeJLO5I=", "exportNames": ["*"]}}, {"name": "expo-camera", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 22, "column": 0, "index": 513}, "end": {"line": 22, "column": 75, "index": 588}}], "key": "F1dQUupkokZUlGC/IdrmVB3l6lo=", "exportNames": ["*"]}}, {"name": "lucide-react-native", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 23, "column": 0, "index": 590}, "end": {"line": 23, "column": 91, "index": 681}}], "key": "R6DNGWV8kRjeiQkq473H83LKevI=", "exportNames": ["*"]}}, {"name": "expo-blur", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 24, "column": 0, "index": 683}, "end": {"line": 24, "column": 37, "index": 720}}], "key": "3HxJxrk2pK5/vFxREdpI4kaDJ7Q=", "exportNames": ["*"]}}, {"name": "./web/LiveFaceCanvas", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 25, "column": 0, "index": 722}, "end": {"line": 25, "column": 50, "index": 772}}], "key": "jzhPOvnOBVjAPneI1nUgzfyLMr8=", "exportNames": ["*"]}}, {"name": "@/utils/useUpload", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 26, "column": 0, "index": 774}, "end": {"line": 26, "column": 42, "index": 816}}], "key": "fmgUBhSYAJBo9LhumboFTPrCXjE=", "exportNames": ["*"]}}, {"name": "@/utils/cameraChallenge", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 28, "column": 0, "index": 879}, "end": {"line": 28, "column": 61, "index": 940}}], "key": "jMo8F5acJdP5TETAQjUma2qAlb8=", "exportNames": ["*"]}}, {"name": "react-native-web/dist/exports/Platform", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "dV3bI3NOD8bfMzaIniMaFGy/nn8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = EchoCameraWeb;\n  var _env2 = require(_dependencyMap[1], \"expo/virtual/env\");\n  var _react = _interopRequireWildcard(require(_dependencyMap[2], \"react\"));\n  var _View = _interopRequireDefault(require(_dependencyMap[3], \"react-native-web/dist/exports/View\"));\n  var _Text = _interopRequireDefault(require(_dependencyMap[4], \"react-native-web/dist/exports/Text\"));\n  var _StyleSheet = _interopRequireDefault(require(_dependencyMap[5], \"react-native-web/dist/exports/StyleSheet\"));\n  var _TouchableOpacity = _interopRequireDefault(require(_dependencyMap[6], \"react-native-web/dist/exports/TouchableOpacity\"));\n  var _Alert = _interopRequireDefault(require(_dependencyMap[7], \"react-native-web/dist/exports/Alert\"));\n  var _Dimensions = _interopRequireDefault(require(_dependencyMap[8], \"react-native-web/dist/exports/Dimensions\"));\n  var _ActivityIndicator = _interopRequireDefault(require(_dependencyMap[9], \"react-native-web/dist/exports/ActivityIndicator\"));\n  var _Modal = _interopRequireDefault(require(_dependencyMap[10], \"react-native-web/dist/exports/Modal\"));\n  var _expoCamera = require(_dependencyMap[11], \"expo-camera\");\n  var _lucideReactNative = require(_dependencyMap[12], \"lucide-react-native\");\n  var _expoBlur = require(_dependencyMap[13], \"expo-blur\");\n  var _LiveFaceCanvas = _interopRequireDefault(require(_dependencyMap[14], \"./web/LiveFaceCanvas\"));\n  var _useUpload = _interopRequireDefault(require(_dependencyMap[15], \"@/utils/useUpload\"));\n  var _cameraChallenge = require(_dependencyMap[16], \"@/utils/cameraChallenge\");\n  var _Platform = _interopRequireDefault(require(_dependencyMap[17], \"react-native-web/dist/exports/Platform\"));\n  var _jsxDevRuntime = require(_dependencyMap[18], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\EchoCameraWeb.tsx\",\n    _s = $RefreshSig$();\n  /**\r\n   * Echo Camera Web Implementation\r\n   * Server-side face blurring for web platform\r\n   * \r\n   * Workflow:\r\n   * 1. Capture unblurred photo with expo-camera\r\n   * 2. Upload to private Supabase bucket\r\n   * 3. Server processes and blurs faces\r\n   * 4. Final blurred image available in public bucket\r\n   */\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  const {\n    width: screenWidth,\n    height: screenHeight\n  } = _Dimensions.default.get('window');\n  const API_BASE_URL = (_env2.env.EXPO_PUBLIC_BASE_URL || _env2.env.EXPO_PUBLIC_PROXY_BASE_URL || _env2.env.EXPO_PUBLIC_HOST || '').replace(/\\/$/, '');\n\n  // Processing states for server-side blur\n\n  function EchoCameraWeb({\n    userId,\n    requestId,\n    onComplete,\n    onCancel\n  }) {\n    _s();\n    const cameraRef = (0, _react.useRef)(null);\n    const [permission, requestPermission] = (0, _expoCamera.useCameraPermissions)();\n\n    // State management\n    const [processingState, setProcessingState] = (0, _react.useState)('idle');\n    const [challengeCode, setChallengeCode] = (0, _react.useState)(null);\n    const [processingProgress, setProcessingProgress] = (0, _react.useState)(0);\n    const [errorMessage, setErrorMessage] = (0, _react.useState)(null);\n    const [capturedPhoto, setCapturedPhoto] = (0, _react.useState)(null);\n    // Live preview blur overlay state\n    const [previewBlurEnabled, setPreviewBlurEnabled] = (0, _react.useState)(true);\n    const [viewSize, setViewSize] = (0, _react.useState)({\n      width: 0,\n      height: 0\n    });\n    // Camera ready state - CRITICAL for showing/hiding loading UI\n    const [isCameraReady, setIsCameraReady] = (0, _react.useState)(false);\n    const [upload] = (0, _useUpload.default)();\n    // Fetch challenge code on mount\n    (0, _react.useEffect)(() => {\n      const controller = new AbortController();\n      (async () => {\n        try {\n          const code = await (0, _cameraChallenge.fetchChallengeCode)({\n            userId,\n            requestId,\n            signal: controller.signal\n          });\n          setChallengeCode(code);\n        } catch (e) {\n          console.warn('[EchoCameraWeb] Challenge code fetch failed:', e);\n          setChallengeCode(`ECHO-${Date.now().toString(36).toUpperCase()}`);\n        }\n      })();\n      return () => controller.abort();\n    }, [userId, requestId]);\n\n    // NEW: TensorFlow.js face detection\n    const loadTensorFlowFaceDetection = async () => {\n      console.log('[EchoCameraWeb] 📦 Loading TensorFlow.js face detection...');\n\n      // Load TensorFlow.js\n      if (!window.tf) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n\n      // Load BlazeFace model\n      if (!window.blazeface) {\n        await new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = 'https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js';\n          script.onload = resolve;\n          script.onerror = reject;\n          document.head.appendChild(script);\n        });\n      }\n      console.log('[EchoCameraWeb] ✅ TensorFlow.js and BlazeFace loaded');\n    };\n    const detectFacesWithTensorFlow = async img => {\n      try {\n        const blazeface = window.blazeface;\n        const tf = window.tf;\n        console.log('[EchoCameraWeb] 🔍 Loading BlazeFace model...');\n        const model = await blazeface.load();\n        console.log('[EchoCameraWeb] ✅ BlazeFace model loaded, detecting faces...');\n\n        // Convert image to tensor\n        const tensor = tf.browser.fromPixels(img);\n\n        // Detect faces with lower confidence threshold to catch more faces\n        const predictions = await model.estimateFaces(tensor, false, 0.7); // Lower threshold from default 0.9\n\n        // Clean up tensor\n        tensor.dispose();\n        console.log(`[EchoCameraWeb] 🎯 BlazeFace detected ${predictions.length} faces`);\n\n        // Convert predictions to our format\n        const faces = predictions.map((prediction, index) => {\n          const [x, y] = prediction.topLeft;\n          const [x2, y2] = prediction.bottomRight;\n          const width = x2 - x;\n          const height = y2 - y;\n          console.log(`[EchoCameraWeb] 👤 Face ${index + 1}:`, {\n            topLeft: [x, y],\n            bottomRight: [x2, y2],\n            size: [width, height]\n          });\n          return {\n            boundingBox: {\n              xCenter: (x + width / 2) / img.width,\n              yCenter: (y + height / 2) / img.height,\n              width: width / img.width,\n              height: height / img.height\n            }\n          };\n        });\n        return faces;\n      } catch (error) {\n        console.error('[EchoCameraWeb] ❌ TensorFlow face detection failed:', error);\n        return [];\n      }\n    };\n    const detectFacesHeuristic = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🧠 Running improved heuristic face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n\n      // Improved face detection with multiple criteria\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 15; // Smaller blocks for better precision\n\n      console.log(`[EchoCameraWeb] 📊 Analyzing image in ${blockSize}px blocks...`);\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // Overlap blocks\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // More sensitive face detection criteria\n          if (analysis.skinRatio > 0.15 &&\n          // Lower skin ratio threshold\n          analysis.hasVariation && analysis.brightness > 0.15 &&\n          // Lower brightness threshold\n          analysis.brightness < 0.9) {\n            // Higher max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize * 1.8 / img.width,\n                height: blockSize * 2.2 / img.height\n              },\n              confidence: analysis.skinRatio * analysis.variation\n            });\n            console.log(`[EchoCameraWeb] 🎯 Found face candidate at (${Math.round(x)}, ${Math.round(y)}) - skin: ${(analysis.skinRatio * 100).toFixed(1)}%, variation: ${analysis.variation.toFixed(2)}, brightness: ${analysis.brightness.toFixed(2)}`);\n          }\n        }\n      }\n\n      // Sort by confidence and merge overlapping detections\n      faces.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🔍 Heuristic detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 3); // Max 3 faces\n    };\n    const detectFacesAggressive = (img, ctx) => {\n      console.log('[EchoCameraWeb] 🚨 Running aggressive face detection...');\n\n      // Get image data for analysis\n      const imageData = ctx.getImageData(0, 0, img.width, img.height);\n      const data = imageData.data;\n      const faces = [];\n      const blockSize = Math.min(img.width, img.height) / 6; // Larger blocks for aggressive detection\n\n      for (let y = 0; y < img.height - blockSize; y += blockSize / 2) {\n        // More overlap\n        for (let x = 0; x < img.width - blockSize; x += blockSize / 2) {\n          const analysis = analyzeRegionForFace(data, x, y, blockSize, img.width, img.height);\n\n          // Very relaxed criteria - catch anything that might be a face\n          if (analysis.skinRatio > 0.08 &&\n          // Very low skin ratio\n          analysis.brightness > 0.1 &&\n          // Very low brightness threshold\n          analysis.brightness < 0.95) {\n            // High max brightness\n\n            faces.push({\n              boundingBox: {\n                xCenter: (x + blockSize / 2) / img.width,\n                yCenter: (y + blockSize / 2) / img.height,\n                width: blockSize / img.width,\n                height: blockSize / img.height\n              },\n              confidence: 0.4 // Lower confidence for aggressive detection\n            });\n          }\n        }\n      }\n\n      // Merge overlapping detections\n      const mergedFaces = mergeFaceDetections(faces);\n      console.log(`[EchoCameraWeb] 🚨 Aggressive detection: ${faces.length} candidates → ${mergedFaces.length} merged faces`);\n      return mergedFaces.slice(0, 5); // Allow more faces in aggressive mode\n    };\n    const analyzeRegionForFace = (data, startX, startY, size, imageWidth, imageHeight) => {\n      let skinPixels = 0;\n      let totalPixels = 0;\n      let totalBrightness = 0;\n      let colorVariations = 0;\n      let prevR = 0,\n        prevG = 0,\n        prevB = 0;\n      for (let y = startY; y < startY + size && y < imageHeight; y++) {\n        for (let x = startX; x < startX + size && x < imageWidth; x++) {\n          const index = (y * imageWidth + x) * 4;\n          const r = data[index];\n          const g = data[index + 1];\n          const b = data[index + 2];\n\n          // Count skin pixels\n          if (isSkinTone(r, g, b)) {\n            skinPixels++;\n          }\n\n          // Calculate brightness\n          const brightness = (r + g + b) / (3 * 255);\n          totalBrightness += brightness;\n\n          // Calculate color variation (indicates features like eyes, mouth)\n          if (totalPixels > 0) {\n            const colorDiff = Math.abs(r - prevR) + Math.abs(g - prevG) + Math.abs(b - prevB);\n            if (colorDiff > 30) {\n              // Threshold for significant color change\n              colorVariations++;\n            }\n          }\n          prevR = r;\n          prevG = g;\n          prevB = b;\n          totalPixels++;\n        }\n      }\n      return {\n        skinRatio: skinPixels / totalPixels,\n        brightness: totalBrightness / totalPixels,\n        variation: colorVariations / totalPixels,\n        hasVariation: colorVariations > totalPixels * 0.1 // At least 10% variation\n      };\n    };\n    const isSkinTone = (r, g, b) => {\n      // Simple skin tone detection algorithm\n      return r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15 && Math.max(r, g, b) - Math.min(r, g, b) > 15;\n    };\n    const mergeFaceDetections = faces => {\n      if (faces.length <= 1) return faces;\n      const merged = [];\n      const used = new Set();\n      for (let i = 0; i < faces.length; i++) {\n        if (used.has(i)) continue;\n        let currentFace = faces[i];\n        used.add(i);\n\n        // Check for overlapping faces\n        for (let j = i + 1; j < faces.length; j++) {\n          if (used.has(j)) continue;\n          const overlap = calculateOverlap(currentFace.boundingBox, faces[j].boundingBox);\n          if (overlap > 0.3) {\n            // 30% overlap threshold\n            // Merge the faces by taking the larger bounding box\n            currentFace = mergeTwoFaces(currentFace, faces[j]);\n            used.add(j);\n          }\n        }\n        merged.push(currentFace);\n      }\n      return merged;\n    };\n    const calculateOverlap = (box1, box2) => {\n      const x1 = Math.max(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const y1 = Math.max(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const x2 = Math.min(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const y2 = Math.min(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      if (x2 <= x1 || y2 <= y1) return 0;\n      const overlapArea = (x2 - x1) * (y2 - y1);\n      const box1Area = box1.width * box1.height;\n      const box2Area = box2.width * box2.height;\n      return overlapArea / Math.min(box1Area, box2Area);\n    };\n    const mergeTwoFaces = (face1, face2) => {\n      const box1 = face1.boundingBox;\n      const box2 = face2.boundingBox;\n      const left = Math.min(box1.xCenter - box1.width / 2, box2.xCenter - box2.width / 2);\n      const right = Math.max(box1.xCenter + box1.width / 2, box2.xCenter + box2.width / 2);\n      const top = Math.min(box1.yCenter - box1.height / 2, box2.yCenter - box2.height / 2);\n      const bottom = Math.max(box1.yCenter + box1.height / 2, box2.yCenter + box2.height / 2);\n      return {\n        boundingBox: {\n          xCenter: (left + right) / 2,\n          yCenter: (top + bottom) / 2,\n          width: right - left,\n          height: bottom - top\n        }\n      };\n    };\n\n    // NEW: Advanced blur functions\n    const applyStrongBlur = (ctx, x, y, width, height) => {\n      // Ensure coordinates are valid\n      const canvasWidth = ctx.canvas.width;\n      const canvasHeight = ctx.canvas.height;\n      const clampedX = Math.max(0, Math.min(Math.floor(x), canvasWidth - 1));\n      const clampedY = Math.max(0, Math.min(Math.floor(y), canvasHeight - 1));\n      const clampedWidth = Math.min(Math.floor(width), canvasWidth - clampedX);\n      const clampedHeight = Math.min(Math.floor(height), canvasHeight - clampedY);\n      if (clampedWidth <= 0 || clampedHeight <= 0) {\n        console.warn(`[EchoCameraWeb] ⚠️ Invalid blur region dimensions:`, {\n          original: {\n            x,\n            y,\n            width,\n            height\n          },\n          canvas: {\n            width: canvasWidth,\n            height: canvasHeight\n          },\n          clamped: {\n            x: clampedX,\n            y: clampedY,\n            width: clampedWidth,\n            height: clampedHeight\n          }\n        });\n        return;\n      }\n\n      // Get the region to blur\n      const imageData = ctx.getImageData(clampedX, clampedY, clampedWidth, clampedHeight);\n      const data = imageData.data;\n\n      // Apply heavy pixelation\n      const pixelSize = Math.max(30, Math.min(clampedWidth, clampedHeight) / 5);\n      for (let py = 0; py < clampedHeight; py += pixelSize) {\n        for (let px = 0; px < clampedWidth; px += pixelSize) {\n          // Get average color of the block\n          let r = 0,\n            g = 0,\n            b = 0,\n            count = 0;\n          for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n            for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n              const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n              r += data[index];\n              g += data[index + 1];\n              b += data[index + 2];\n              count++;\n            }\n          }\n          if (count > 0) {\n            r = Math.floor(r / count);\n            g = Math.floor(g / count);\n            b = Math.floor(b / count);\n\n            // Apply averaged color to entire block\n            for (let dy = 0; dy < pixelSize && py + dy < clampedHeight; dy++) {\n              for (let dx = 0; dx < pixelSize && px + dx < clampedWidth; dx++) {\n                const index = ((py + dy) * clampedWidth + (px + dx)) * 4;\n                data[index] = r;\n                data[index + 1] = g;\n                data[index + 2] = b;\n                // Keep original alpha\n              }\n            }\n          }\n        }\n      }\n\n      // Apply additional blur passes\n      for (let i = 0; i < 3; i++) {\n        applySimpleBlur(data, clampedWidth, clampedHeight);\n      }\n\n      // Put the blurred data back\n      ctx.putImageData(imageData, clampedX, clampedY);\n\n      // Add an additional privacy overlay for extra security\n      ctx.fillStyle = 'rgba(128, 128, 128, 0.2)';\n      ctx.fillRect(clampedX, clampedY, clampedWidth, clampedHeight);\n    };\n    const applySimpleBlur = (data, width, height) => {\n      const original = new Uint8ClampedArray(data);\n      for (let y = 1; y < height - 1; y++) {\n        for (let x = 1; x < width - 1; x++) {\n          const index = (y * width + x) * 4;\n\n          // Average with surrounding pixels\n          let r = 0,\n            g = 0,\n            b = 0;\n          for (let dy = -1; dy <= 1; dy++) {\n            for (let dx = -1; dx <= 1; dx++) {\n              const neighborIndex = ((y + dy) * width + (x + dx)) * 4;\n              r += original[neighborIndex];\n              g += original[neighborIndex + 1];\n              b += original[neighborIndex + 2];\n            }\n          }\n          data[index] = r / 9;\n          data[index + 1] = g / 9;\n          data[index + 2] = b / 9;\n        }\n      }\n    };\n    const applyFallbackFaceBlur = (ctx, imgWidth, imgHeight) => {\n      console.log('[EchoCameraWeb] 🔄 Applying fallback face blur to common face areas...');\n\n      // Blur common face areas (center-upper region, typical selfie positions)\n      const areas = [\n      // Center face area\n      {\n        x: imgWidth * 0.25,\n        y: imgHeight * 0.15,\n        w: imgWidth * 0.5,\n        h: imgHeight * 0.5\n      },\n      // Left side face area\n      {\n        x: imgWidth * 0.1,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      },\n      // Right side face area\n      {\n        x: imgWidth * 0.55,\n        y: imgHeight * 0.2,\n        w: imgWidth * 0.35,\n        h: imgHeight * 0.4\n      }];\n      areas.forEach((area, index) => {\n        console.log(`[EchoCameraWeb] 🎯 Blurring fallback area ${index + 1}:`, area);\n        applyStrongBlur(ctx, area.x, area.y, area.w, area.h);\n      });\n    };\n\n    // Capture photo\n    const capturePhoto = (0, _react.useCallback)(async () => {\n      // Development fallback for testing without camera\n      const isDev = process.env.NODE_ENV === 'development' || __DEV__;\n      if (!cameraRef.current && !isDev) {\n        _Alert.default.alert('Error', 'Camera not ready');\n        return;\n      }\n      try {\n        setProcessingState('capturing');\n        setProcessingProgress(10);\n        // Force live preview blur on capture for UX consistency\n        // (Server-side still performs the real face blurring)\n        // Small delay to ensure overlay is visible in preview at click time\n        await new Promise(resolve => setTimeout(resolve, 80));\n        // Capture the photo\n        let photo;\n        try {\n          photo = await cameraRef.current.takePictureAsync({\n            quality: 0.9,\n            base64: false,\n            skipProcessing: true // Important: Get raw image for server processing\n          });\n        } catch (cameraError) {\n          console.log('[EchoCameraWeb] Camera capture failed, using dev fallback:', cameraError);\n          // Development fallback - use a placeholder image\n          if (isDev) {\n            photo = {\n              uri: 'https://via.placeholder.com/600x800/3B82F6/FFFFFF?text=Privacy+Protected+Photo'\n            };\n          } else {\n            throw cameraError;\n          }\n        }\n        if (!photo) {\n          throw new Error('Failed to capture photo');\n        }\n        console.log('[EchoCameraWeb] 📸 Photo captured:', photo.uri);\n        setCapturedPhoto(photo.uri);\n        setProcessingProgress(25);\n        // Process image with client-side face blurring\n        console.log('[EchoCameraWeb] 🚀 Starting face blur processing...');\n        await processImageWithFaceBlur(photo.uri);\n        console.log('[EchoCameraWeb] ✅ Face blur processing completed!');\n      } catch (error) {\n        console.error('[EchoCameraWeb] Capture error:', error);\n        setErrorMessage('Failed to capture photo. Please try again.');\n        setProcessingState('error');\n      }\n    }, []);\n    // NEW: Robust face detection and blurring system\n    const processImageWithFaceBlur = async photoUri => {\n      console.log('[EchoCameraWeb] 🚀 ENTRY: Starting face blur processing system...');\n      try {\n        console.log('[EchoCameraWeb] 🚀 Starting NEW face blur processing system...');\n        setProcessingState('processing');\n        setProcessingProgress(40);\n\n        // Create a canvas to process the image\n        const canvas = document.createElement('canvas');\n        const ctx = canvas.getContext('2d');\n        if (!ctx) throw new Error('Canvas context not available');\n\n        // Load the image\n        const img = new Image();\n        await new Promise((resolve, reject) => {\n          img.onload = resolve;\n          img.onerror = reject;\n          img.src = photoUri;\n        });\n\n        // Set canvas size to match image\n        canvas.width = img.width;\n        canvas.height = img.height;\n        console.log('[EchoCameraWeb] 📐 Canvas setup:', {\n          width: img.width,\n          height: img.height\n        });\n\n        // Draw the original image\n        ctx.drawImage(img, 0, 0);\n        console.log('[EchoCameraWeb] 🖼️ Original image drawn to canvas');\n        setProcessingProgress(60);\n\n        // NEW: Robust face detection with TensorFlow.js BlazeFace\n        let detectedFaces = [];\n        console.log('[EchoCameraWeb] 🔍 Starting TensorFlow.js face detection...');\n\n        // Strategy 1: Try TensorFlow.js BlazeFace (most reliable)\n        try {\n          console.log('[EchoCameraWeb] 🔄 Loading TensorFlow.js and BlazeFace...');\n          await loadTensorFlowFaceDetection();\n          console.log('[EchoCameraWeb] ✅ TensorFlow.js loaded, starting face detection...');\n          detectedFaces = await detectFacesWithTensorFlow(img);\n          console.log(`[EchoCameraWeb] ✅ TensorFlow BlazeFace found ${detectedFaces.length} faces`);\n        } catch (tensorFlowError) {\n          console.warn('[EchoCameraWeb] ❌ TensorFlow failed:', tensorFlowError);\n          console.warn('[EchoCameraWeb] ❌ TensorFlow error details:', {\n            message: tensorFlowError.message,\n            stack: tensorFlowError.stack\n          });\n\n          // Strategy 2: Use intelligent heuristic detection\n          console.log('[EchoCameraWeb] 🧠 Falling back to heuristic face detection...');\n          detectedFaces = detectFacesHeuristic(img, ctx);\n          console.log(`[EchoCameraWeb] 🧠 Heuristic detection found ${detectedFaces.length} faces`);\n        }\n\n        // Strategy 3: If still no faces found, use aggressive detection\n        if (detectedFaces.length === 0) {\n          console.log('[EchoCameraWeb] 🔍 No faces found, trying aggressive detection...');\n          detectedFaces = detectFacesAggressive(img, ctx);\n          console.log(`[EchoCameraWeb] 🔍 Aggressive detection found ${detectedFaces.length} faces`);\n        }\n        console.log(`[EchoCameraWeb] ✅ FACE DETECTION COMPLETE: Found ${detectedFaces.length} faces`);\n        if (detectedFaces.length > 0) {\n          console.log('[EchoCameraWeb] 🎯 Face detection details:', detectedFaces.map((face, i) => ({\n            faceNumber: i + 1,\n            centerX: face.boundingBox.xCenter,\n            centerY: face.boundingBox.yCenter,\n            width: face.boundingBox.width,\n            height: face.boundingBox.height\n          })));\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected by any method');\n          console.log('[EchoCameraWeb] 🛡️ Applying privacy-first fallback: center region blur');\n\n          // Privacy-first fallback: blur the center region where faces are most likely\n          const centerX = img.width * 0.3;\n          const centerY = img.height * 0.2;\n          const centerWidth = img.width * 0.4;\n          const centerHeight = img.height * 0.6;\n          detectedFaces = [{\n            boundingBox: {\n              xCenter: 0.5,\n              yCenter: 0.5,\n              width: 0.4,\n              height: 0.6\n            },\n            confidence: 0.3\n          }];\n          console.log('[EchoCameraWeb] 🛡️ Applied privacy fallback - center region will be blurred');\n        }\n        setProcessingProgress(70);\n\n        // NEW: Apply advanced blurring to each detected face\n        if (detectedFaces.length > 0) {\n          console.log(`[EchoCameraWeb] 🎨 Applying blur to ${detectedFaces.length} detected faces...`);\n          detectedFaces.forEach((detection, index) => {\n            const bbox = detection.boundingBox;\n            console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Raw detection data for face ${index + 1}:`, {\n              bbox,\n              imageSize: {\n                width: img.width,\n                height: img.height\n              }\n            });\n\n            // Convert normalized coordinates to pixel coordinates with generous padding\n            const faceX = bbox.xCenter * img.width - bbox.width * img.width / 2;\n            const faceY = bbox.yCenter * img.height - bbox.height * img.height / 2;\n            const faceWidth = bbox.width * img.width;\n            const faceHeight = bbox.height * img.height;\n            console.log(`[EchoCameraWeb] 🔍 DEBUGGING: Calculated face coordinates:`, {\n              faceX,\n              faceY,\n              faceWidth,\n              faceHeight,\n              isValid: faceX >= 0 && faceY >= 0 && faceWidth > 0 && faceHeight > 0\n            });\n\n            // Add generous padding around the face (50% padding)\n            const padding = 0.5;\n            const paddedX = Math.max(0, faceX - faceWidth * padding);\n            const paddedY = Math.max(0, faceY - faceHeight * padding);\n            const paddedWidth = Math.min(img.width - paddedX, faceWidth * (1 + 2 * padding));\n            const paddedHeight = Math.min(img.height - paddedY, faceHeight * (1 + 2 * padding));\n            console.log(`[EchoCameraWeb] 🎯 Blurring face ${index + 1}:`, {\n              original: {\n                x: Math.round(faceX),\n                y: Math.round(faceY),\n                w: Math.round(faceWidth),\n                h: Math.round(faceHeight)\n              },\n              padded: {\n                x: Math.round(paddedX),\n                y: Math.round(paddedY),\n                w: Math.round(paddedWidth),\n                h: Math.round(paddedHeight)\n              }\n            });\n\n            // DEBUGGING: Check canvas state before blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state before blur:`, {\n              width: canvas.width,\n              height: canvas.height,\n              contextValid: !!ctx\n            });\n\n            // Apply multiple blur effects for maximum privacy\n            applyStrongBlur(ctx, paddedX, paddedY, paddedWidth, paddedHeight);\n\n            // DEBUGGING: Check canvas state after blur\n            console.log(`[EchoCameraWeb] 🔍 Canvas state after blur - checking if blur was applied...`);\n\n            // Verify blur was applied by checking a few pixels\n            const testImageData = ctx.getImageData(paddedX + 10, paddedY + 10, 10, 10);\n            console.log(`[EchoCameraWeb] 🔍 Sample pixels after blur:`, {\n              firstPixel: [testImageData.data[0], testImageData.data[1], testImageData.data[2]],\n              secondPixel: [testImageData.data[4], testImageData.data[5], testImageData.data[6]]\n            });\n            console.log(`[EchoCameraWeb] ✅ Face ${index + 1} strongly blurred!`);\n          });\n          console.log(`[EchoCameraWeb] 🎉 All ${detectedFaces.length} faces have been strongly blurred!`);\n        } else {\n          console.log('[EchoCameraWeb] ⚠️ No faces detected - applying fallback blur to likely face areas...');\n          // Apply fallback blur to common face areas\n          applyFallbackFaceBlur(ctx, img.width, img.height);\n        }\n        setProcessingProgress(90);\n\n        // Convert canvas to blob and create URL\n        console.log('[EchoCameraWeb] 📸 Converting processed canvas to image blob...');\n        const blurredImageBlob = await new Promise(resolve => {\n          canvas.toBlob(blob => resolve(blob), 'image/jpeg', 0.9);\n        });\n        const blurredImageUrl = URL.createObjectURL(blurredImageBlob);\n        console.log('[EchoCameraWeb] ✅ Blurred image URL created:', blurredImageUrl.substring(0, 50) + '...');\n\n        // CRITICAL: Update the captured photo state with the blurred version\n        setCapturedPhoto(blurredImageUrl);\n        console.log('[EchoCameraWeb] 🔄 Updated capturedPhoto state with blurred image');\n        setProcessingProgress(100);\n\n        // Complete the processing\n        await completeProcessing(blurredImageUrl);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage('Failed to process photo.');\n        setProcessingState('error');\n      }\n    };\n\n    // Complete processing with blurred image\n    const completeProcessing = async blurredImageUrl => {\n      try {\n        setProcessingState('complete');\n\n        // Generate a mock job result\n        const timestamp = Date.now();\n        const result = {\n          imageUrl: blurredImageUrl,\n          localUri: blurredImageUrl,\n          challengeCode: challengeCode || '',\n          timestamp,\n          jobId: `client-${timestamp}`,\n          status: 'completed'\n        };\n        console.log('[EchoCameraWeb] 🎉 Processing complete! Calling onComplete with blurred image:', {\n          imageUrl: blurredImageUrl.substring(0, 50) + '...',\n          timestamp,\n          jobId: result.jobId\n        });\n\n        // Call the completion callback\n        onComplete(result);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Completion error:', error);\n        setErrorMessage('Failed to complete processing.');\n        setProcessingState('error');\n      }\n    };\n\n    // Trigger server-side face blurring (now unused but kept for compatibility)\n    const triggerServerProcessing = async (privateImageUrl, timestamp) => {\n      try {\n        console.log('[EchoCameraWeb] Starting server-side processing for:', privateImageUrl);\n        setProcessingState('processing');\n        setProcessingProgress(70);\n        const requestBody = {\n          imageUrl: privateImageUrl,\n          userId,\n          requestId,\n          timestamp,\n          platform: 'web'\n        };\n        console.log('[EchoCameraWeb] Sending processing request:', requestBody);\n\n        // Call backend API to process image\n        const response = await fetch(`${API_BASE_URL}/api/process-image`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${await getAuthToken()}`\n          },\n          body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n          const errorText = await response.text();\n          console.error('[EchoCameraWeb] Processing request failed:', response.status, errorText);\n          throw new Error(`Processing failed: ${response.status} ${response.statusText}`);\n        }\n        const result = await response.json();\n        console.log('[EchoCameraWeb] Processing request successful:', result);\n        if (!result.jobId) {\n          throw new Error('No job ID returned from processing request');\n        }\n\n        // Poll for processing completion\n        await pollForCompletion(result.jobId, timestamp);\n      } catch (error) {\n        console.error('[EchoCameraWeb] Processing error:', error);\n        setErrorMessage(`Failed to process image: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Poll for server-side processing completion\n    const pollForCompletion = async (jobId, timestamp, attempts = 0) => {\n      const MAX_ATTEMPTS = 30; // 30 seconds max\n      const POLL_INTERVAL = 1000; // 1 second\n\n      console.log(`[EchoCameraWeb] Polling attempt ${attempts + 1}/${MAX_ATTEMPTS} for job ${jobId}`);\n      if (attempts >= MAX_ATTEMPTS) {\n        console.error('[EchoCameraWeb] Processing timeout after 30 seconds');\n        setErrorMessage('Processing timeout. Please try again.');\n        setProcessingState('error');\n        return;\n      }\n      try {\n        const response = await fetch(`${API_BASE_URL}/api/process-status/${jobId}`, {\n          headers: {\n            'Authorization': `Bearer ${await getAuthToken()}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const status = await response.json();\n        console.log(`[EchoCameraWeb] Status response:`, status);\n        if (status.status === 'completed') {\n          console.log('[EchoCameraWeb] Processing completed successfully');\n          setProcessingProgress(100);\n          setProcessingState('completed');\n          // Return the processed image URL\n          const result = {\n            imageUrl: status.publicUrl,\n            // Blurred image in public bucket\n            localUri: capturedPhoto || status.publicUrl,\n            // Fallback to publicUrl if no localUri\n            challengeCode: challengeCode || '',\n            timestamp,\n            processingStatus: 'completed'\n          };\n          console.log('[EchoCameraWeb] Returning result:', result);\n          onComplete(result);\n          // Removed redundant Alert - parent component will handle UI update\n        } else if (status.status === 'failed') {\n          console.error('[EchoCameraWeb] Processing failed:', status.error);\n          throw new Error(status.error || 'Processing failed');\n        } else {\n          // Still processing, update progress and poll again\n          const progressValue = 70 + attempts / MAX_ATTEMPTS * 25;\n          console.log(`[EchoCameraWeb] Still processing... Progress: ${progressValue}%`);\n          setProcessingProgress(progressValue);\n          setTimeout(() => {\n            pollForCompletion(jobId, timestamp, attempts + 1);\n          }, POLL_INTERVAL);\n        }\n      } catch (error) {\n        console.error('[EchoCameraWeb] Polling error:', error);\n        setErrorMessage(`Failed to check processing status: ${error.message}`);\n        setProcessingState('error');\n      }\n    };\n    // Get auth token helper\n    const getAuthToken = async () => {\n      // Implement based on your auth system\n      // This is a placeholder\n      return 'user-auth-token';\n    };\n\n    // Retry mechanism for failed operations\n    const retryCapture = (0, _react.useCallback)(() => {\n      console.log('[EchoCameraWeb] Retrying capture...');\n      setProcessingState('idle');\n      setErrorMessage('');\n      setCapturedPhoto('');\n      setProcessingProgress(0);\n    }, []);\n    // Debug logging\n    (0, _react.useEffect)(() => {\n      console.log('[EchoCameraWeb] Permission state:', permission);\n      if (permission) {\n        console.log('[EchoCameraWeb] Permission granted:', permission.granted);\n      }\n    }, [permission]);\n    // Handle permission states\n    if (!permission) {\n      console.log('[EchoCameraWeb] Waiting for permission state...');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n          size: \"large\",\n          color: \"#3B82F6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 900,\n          columnNumber: 9\n        }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n          style: styles.loadingText,\n          children: \"Loading camera...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 901,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 899,\n        columnNumber: 7\n      }, this);\n    }\n    if (!permission.granted) {\n      console.log('[EchoCameraWeb] Camera permission not granted, showing permission request');\n      return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.container,\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.permissionContent,\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Camera, {\n            size: 64,\n            color: \"#3B82F6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 910,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionTitle,\n            children: \"Camera Permission Required\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 911,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n            style: styles.permissionDescription,\n            children: \"Echo needs camera access to capture photos. Your privacy is protected - faces will be automatically blurred after capture.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 912,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: requestPermission,\n            style: styles.primaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.primaryButtonText,\n              children: \"Grant Permission\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 917,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 916,\n            columnNumber: 11\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n            onPress: onCancel,\n            style: styles.secondaryButton,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.secondaryButtonText,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 920,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 919,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 909,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 908,\n        columnNumber: 7\n      }, this);\n    }\n    // Main camera UI with processing states\n    console.log('[EchoCameraWeb] Rendering camera view');\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n      style: styles.container,\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n        style: styles.cameraContainer,\n        id: \"echo-web-camera\",\n        children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoCamera.CameraView, {\n          ref: cameraRef,\n          style: [styles.camera, {\n            backgroundColor: '#1a1a1a'\n          }],\n          facing: \"back\",\n          onLayout: e => {\n            console.log('[EchoCameraWeb] Camera layout:', e.nativeEvent.layout);\n            setViewSize({\n              width: e.nativeEvent.layout.width,\n              height: e.nativeEvent.layout.height\n            });\n          },\n          onCameraReady: () => {\n            console.log('[EchoCameraWeb] Camera ready!');\n            setIsCameraReady(true); // CRITICAL: Update state when camera is ready\n          },\n          onMountError: error => {\n            console.error('[EchoCameraWeb] Camera mount error:', error);\n            setErrorMessage('Camera failed to initialize');\n            setProcessingState('error');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 9\n        }, this), !isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: [_StyleSheet.default.absoluteFill, {\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            justifyContent: 'center',\n            alignItems: 'center',\n            zIndex: 1000\n          }],\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: {\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              padding: 24,\n              borderRadius: 16,\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\",\n              style: {\n                marginBottom: 16\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 955,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#fff',\n                fontSize: 18,\n                fontWeight: '600'\n              },\n              children: \"Initializing Camera...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 956,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: {\n                color: '#9CA3AF',\n                fontSize: 14,\n                marginTop: 8\n              },\n              children: \"Please wait\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 957,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 954,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 953,\n          columnNumber: 11\n        }, this), isCameraReady && previewBlurEnabled && viewSize.width > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_LiveFaceCanvas.default, {\n            containerId: \"echo-web-camera\",\n            width: viewSize.width,\n            height: viewSize.height\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 966,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: [_StyleSheet.default.absoluteFill, {\n              pointerEvents: 'none'\n            }],\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 60,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: viewSize.height * 0.1,\n                width: viewSize.width,\n                height: viewSize.height * 0.75,\n                borderRadius: 20\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 40,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: 0,\n                top: 0,\n                width: viewSize.width,\n                height: viewSize.height * 0.9,\n                borderRadius: 30\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.5 - viewSize.width * 0.35,\n                top: viewSize.height * 0.35 - viewSize.width * 0.35,\n                width: viewSize.width * 0.7,\n                height: viewSize.width * 0.7,\n                borderRadius: viewSize.width * 0.7 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 985,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.2 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 992,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_expoBlur.BlurView, {\n              intensity: 50,\n              tint: \"dark\",\n              style: [styles.blurZone, {\n                left: viewSize.width * 0.8 - viewSize.width * 0.25,\n                top: viewSize.height * 0.4 - viewSize.width * 0.25,\n                width: viewSize.width * 0.5,\n                height: viewSize.width * 0.5,\n                borderRadius: viewSize.width * 0.5 / 2\n              }]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 13\n            }, this), __DEV__ && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.previewChip,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.previewChipText,\n                children: \"Live Privacy Preview\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1009,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1008,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 967,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true), isCameraReady && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n          children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.headerOverlay,\n            children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.headerContent,\n              children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.headerLeft,\n                children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                  style: styles.headerTitle,\n                  children: \"Echo Camera\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1022,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.subtitleRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.webIcon,\n                    children: \"??\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1024,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.headerSubtitle,\n                    children: \"Web Camera Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1025,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1023,\n                  columnNumber: 19\n                }, this), challengeCode && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                  style: styles.challengeRow,\n                  children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n                    size: 14,\n                    color: \"#fff\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1029,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                    style: styles.challengeCode,\n                    children: challengeCode\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1030,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1021,\n                columnNumber: 17\n              }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n                onPress: onCancel,\n                style: styles.closeButton,\n                children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n                  size: 24,\n                  color: \"#fff\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1035,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1034,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1020,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1019,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.privacyNotice,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.Shield, {\n              size: 16,\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1041,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyText,\n              children: \"For your privacy, faces will be automatically blurred after upload\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1042,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1040,\n            columnNumber: 13\n          }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.footerOverlay,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.instruction,\n              children: \"Center the subject and click to capture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1048,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: capturePhoto,\n              disabled: processingState !== 'idle' || !isCameraReady,\n              style: [styles.shutterButton, processingState !== 'idle' && styles.shutterButtonDisabled],\n              children: processingState === 'idle' ? /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: styles.shutterInner\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1061,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n                size: \"small\",\n                color: \"#3B82F6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1063,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1052,\n              columnNumber: 15\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.privacyNote,\n              children: \"?? Server-side privacy protection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1066,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1047,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 932,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState !== 'idle' && processingState !== 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.processingContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_ActivityIndicator.default, {\n              size: \"large\",\n              color: \"#3B82F6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1081,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingTitle,\n              children: [processingState === 'capturing' && 'Capturing Photo...', processingState === 'uploading' && 'Uploading for Processing...', processingState === 'processing' && 'Applying Privacy Protection...', processingState === 'completed' && 'Processing Complete!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1083,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n              style: styles.progressBar,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n                style: [styles.progressFill, {\n                  width: `${processingProgress}%`\n                }]\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1090,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1089,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.processingDescription,\n              children: [processingState === 'capturing' && 'Getting your photo ready...', processingState === 'uploading' && 'Securely uploading to our servers...', processingState === 'processing' && 'Detecting and blurring faces for privacy...', processingState === 'completed' && 'Your photo is ready with faces blurred!']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1097,\n              columnNumber: 13\n            }, this), processingState === 'completed' && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.CheckCircle, {\n              size: 48,\n              color: \"#10B981\",\n              style: styles.successIcon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1104,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1080,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1079,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1074,\n        columnNumber: 7\n      }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Modal.default, {\n        visible: processingState === 'error',\n        transparent: true,\n        animationType: \"fade\",\n        children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n          style: styles.processingModal,\n          children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_View.default, {\n            style: styles.errorContent,\n            children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_lucideReactNative.X, {\n              size: 48,\n              color: \"#EF4444\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1117,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorTitle,\n              children: \"Processing Failed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1118,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n              style: styles.errorMessage,\n              children: errorMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1119,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: retryCapture,\n              style: styles.primaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.primaryButtonText,\n                children: \"Try Again\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1124,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1120,\n              columnNumber: 13\n            }, this), /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_TouchableOpacity.default, {\n              onPress: onCancel,\n              style: styles.secondaryButton,\n              children: /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_Text.default, {\n                style: styles.secondaryButtonText,\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1130,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1126,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1116,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1115,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1110,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 930,\n      columnNumber: 5\n    }, this);\n  }\n  _s(EchoCameraWeb, \"VqB78QfG+xzRnHxbs1KKargRvfc=\", false, function () {\n    return [_expoCamera.useCameraPermissions, _useUpload.default];\n  });\n  _c = EchoCameraWeb;\n  const styles = _StyleSheet.default.create({\n    container: {\n      flex: 1,\n      backgroundColor: '#000'\n    },\n    cameraContainer: {\n      flex: 1,\n      maxWidth: 600,\n      alignSelf: 'center',\n      width: '100%'\n    },\n    camera: {\n      flex: 1\n    },\n    headerOverlay: {\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingTop: 50,\n      paddingHorizontal: 20,\n      paddingBottom: 20\n    },\n    headerContent: {\n      flexDirection: 'row',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start'\n    },\n    headerLeft: {\n      flex: 1\n    },\n    headerTitle: {\n      fontSize: 24,\n      fontWeight: '700',\n      color: '#fff',\n      marginBottom: 4\n    },\n    subtitleRow: {\n      flexDirection: 'row',\n      alignItems: 'center',\n      marginBottom: 8\n    },\n    webIcon: {\n      fontSize: 14,\n      marginRight: 6\n    },\n    headerSubtitle: {\n      fontSize: 14,\n      color: '#fff',\n      opacity: 0.9\n    },\n    challengeRow: {\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    challengeCode: {\n      fontSize: 12,\n      color: '#fff',\n      marginLeft: 6,\n      fontFamily: 'monospace'\n    },\n    closeButton: {\n      padding: 8\n    },\n    privacyNotice: {\n      position: 'absolute',\n      top: 140,\n      left: 20,\n      right: 20,\n      backgroundColor: 'rgba(59, 130, 246, 0.1)',\n      borderRadius: 8,\n      padding: 12,\n      flexDirection: 'row',\n      alignItems: 'center'\n    },\n    privacyText: {\n      color: '#fff',\n      fontSize: 13,\n      marginLeft: 8,\n      flex: 1\n    },\n    footerOverlay: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      right: 0,\n      backgroundColor: 'transparent',\n      paddingBottom: 40,\n      paddingTop: 30,\n      alignItems: 'center'\n    },\n    instruction: {\n      fontSize: 16,\n      color: '#fff',\n      marginBottom: 20\n    },\n    shutterButton: {\n      width: 72,\n      height: 72,\n      borderRadius: 36,\n      backgroundColor: '#fff',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginBottom: 16,\n      ..._Platform.default.select({\n        ios: {\n          shadowColor: '#3B82F6',\n          shadowOffset: {\n            width: 0,\n            height: 0\n          },\n          shadowOpacity: 0.5,\n          shadowRadius: 20\n        },\n        android: {\n          elevation: 10\n        },\n        web: {\n          boxShadow: '0px 0px 20px rgba(59, 130, 246, 0.35)'\n        }\n      })\n    },\n    shutterButtonDisabled: {\n      opacity: 0.5\n    },\n    shutterInner: {\n      width: 64,\n      height: 64,\n      borderRadius: 32,\n      backgroundColor: '#fff',\n      borderWidth: 3,\n      borderColor: '#3B82F6'\n    },\n    privacyNote: {\n      fontSize: 12,\n      color: '#9CA3AF'\n    },\n    processingModal: {\n      flex: 1,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      justifyContent: 'center',\n      alignItems: 'center'\n    },\n    processingContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    processingTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 20\n    },\n    progressBar: {\n      width: '100%',\n      height: 6,\n      backgroundColor: '#E5E7EB',\n      borderRadius: 3,\n      overflow: 'hidden',\n      marginBottom: 16\n    },\n    progressFill: {\n      height: '100%',\n      backgroundColor: '#3B82F6',\n      borderRadius: 3\n    },\n    processingDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center'\n    },\n    successIcon: {\n      marginTop: 16\n    },\n    errorContent: {\n      backgroundColor: '#fff',\n      borderRadius: 16,\n      padding: 32,\n      width: '90%',\n      maxWidth: 400,\n      alignItems: 'center'\n    },\n    errorTitle: {\n      fontSize: 18,\n      fontWeight: '600',\n      color: '#EF4444',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    errorMessage: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    primaryButton: {\n      backgroundColor: '#3B82F6',\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      borderRadius: 8,\n      marginTop: 8\n    },\n    primaryButtonText: {\n      color: '#fff',\n      fontSize: 16,\n      fontWeight: '600'\n    },\n    secondaryButton: {\n      paddingHorizontal: 24,\n      paddingVertical: 12,\n      marginTop: 8\n    },\n    secondaryButtonText: {\n      color: '#6B7280',\n      fontSize: 16\n    },\n    permissionContent: {\n      flex: 1,\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 32\n    },\n    permissionTitle: {\n      fontSize: 20,\n      fontWeight: '600',\n      color: '#111827',\n      marginTop: 16,\n      marginBottom: 8\n    },\n    permissionDescription: {\n      fontSize: 14,\n      color: '#6B7280',\n      textAlign: 'center',\n      marginBottom: 24\n    },\n    loadingText: {\n      color: '#6B7280',\n      marginTop: 16\n    },\n    // Live blur overlay\n    blurZone: {\n      position: 'absolute',\n      overflow: 'hidden'\n    },\n    previewChip: {\n      position: 'absolute',\n      top: 8,\n      right: 8,\n      backgroundColor: 'rgba(0,0,0,0.5)',\n      paddingHorizontal: 10,\n      paddingVertical: 6,\n      borderRadius: 12\n    },\n    previewChipText: {\n      color: '#fff',\n      fontSize: 11,\n      fontWeight: '600'\n    }\n  });\n  var _c;\n  $RefreshReg$(_c, \"EchoCameraWeb\");\n});", "lineCount": 1745, "map": [[8, 2, 11, 0], [8, 6, 11, 0, "_react"], [8, 12, 11, 0], [8, 15, 11, 0, "_interopRequireWildcard"], [8, 38, 11, 0], [8, 39, 11, 0, "require"], [8, 46, 11, 0], [8, 47, 11, 0, "_dependencyMap"], [8, 61, 11, 0], [9, 2, 11, 72], [9, 6, 11, 72, "_View"], [9, 11, 11, 72], [9, 14, 11, 72, "_interopRequireDefault"], [9, 36, 11, 72], [9, 37, 11, 72, "require"], [9, 44, 11, 72], [9, 45, 11, 72, "_dependencyMap"], [9, 59, 11, 72], [10, 2, 11, 72], [10, 6, 11, 72, "_Text"], [10, 11, 11, 72], [10, 14, 11, 72, "_interopRequireDefault"], [10, 36, 11, 72], [10, 37, 11, 72, "require"], [10, 44, 11, 72], [10, 45, 11, 72, "_dependencyMap"], [10, 59, 11, 72], [11, 2, 11, 72], [11, 6, 11, 72, "_StyleSheet"], [11, 17, 11, 72], [11, 20, 11, 72, "_interopRequireDefault"], [11, 42, 11, 72], [11, 43, 11, 72, "require"], [11, 50, 11, 72], [11, 51, 11, 72, "_dependencyMap"], [11, 65, 11, 72], [12, 2, 11, 72], [12, 6, 11, 72, "_TouchableOpacity"], [12, 23, 11, 72], [12, 26, 11, 72, "_interopRequireDefault"], [12, 48, 11, 72], [12, 49, 11, 72, "require"], [12, 56, 11, 72], [12, 57, 11, 72, "_dependencyMap"], [12, 71, 11, 72], [13, 2, 11, 72], [13, 6, 11, 72, "_<PERSON><PERSON>"], [13, 12, 11, 72], [13, 15, 11, 72, "_interopRequireDefault"], [13, 37, 11, 72], [13, 38, 11, 72, "require"], [13, 45, 11, 72], [13, 46, 11, 72, "_dependencyMap"], [13, 60, 11, 72], [14, 2, 11, 72], [14, 6, 11, 72, "_Dimensions"], [14, 17, 11, 72], [14, 20, 11, 72, "_interopRequireDefault"], [14, 42, 11, 72], [14, 43, 11, 72, "require"], [14, 50, 11, 72], [14, 51, 11, 72, "_dependencyMap"], [14, 65, 11, 72], [15, 2, 11, 72], [15, 6, 11, 72, "_ActivityIndicator"], [15, 24, 11, 72], [15, 27, 11, 72, "_interopRequireDefault"], [15, 49, 11, 72], [15, 50, 11, 72, "require"], [15, 57, 11, 72], [15, 58, 11, 72, "_dependencyMap"], [15, 72, 11, 72], [16, 2, 11, 72], [16, 6, 11, 72, "_Modal"], [16, 12, 11, 72], [16, 15, 11, 72, "_interopRequireDefault"], [16, 37, 11, 72], [16, 38, 11, 72, "require"], [16, 45, 11, 72], [16, 46, 11, 72, "_dependencyMap"], [16, 60, 11, 72], [17, 2, 22, 0], [17, 6, 22, 0, "_expoCamera"], [17, 17, 22, 0], [17, 20, 22, 0, "require"], [17, 27, 22, 0], [17, 28, 22, 0, "_dependencyMap"], [17, 42, 22, 0], [18, 2, 23, 0], [18, 6, 23, 0, "_lucideReactNative"], [18, 24, 23, 0], [18, 27, 23, 0, "require"], [18, 34, 23, 0], [18, 35, 23, 0, "_dependencyMap"], [18, 49, 23, 0], [19, 2, 24, 0], [19, 6, 24, 0, "_expoBlur"], [19, 15, 24, 0], [19, 18, 24, 0, "require"], [19, 25, 24, 0], [19, 26, 24, 0, "_dependencyMap"], [19, 40, 24, 0], [20, 2, 25, 0], [20, 6, 25, 0, "_LiveFaceCanvas"], [20, 21, 25, 0], [20, 24, 25, 0, "_interopRequireDefault"], [20, 46, 25, 0], [20, 47, 25, 0, "require"], [20, 54, 25, 0], [20, 55, 25, 0, "_dependencyMap"], [20, 69, 25, 0], [21, 2, 26, 0], [21, 6, 26, 0, "_useUpload"], [21, 16, 26, 0], [21, 19, 26, 0, "_interopRequireDefault"], [21, 41, 26, 0], [21, 42, 26, 0, "require"], [21, 49, 26, 0], [21, 50, 26, 0, "_dependencyMap"], [21, 64, 26, 0], [22, 2, 28, 0], [22, 6, 28, 0, "_cameraChallenge"], [22, 22, 28, 0], [22, 25, 28, 0, "require"], [22, 32, 28, 0], [22, 33, 28, 0, "_dependencyMap"], [22, 47, 28, 0], [23, 2, 28, 61], [23, 6, 28, 61, "_Platform"], [23, 15, 28, 61], [23, 18, 28, 61, "_interopRequireDefault"], [23, 40, 28, 61], [23, 41, 28, 61, "require"], [23, 48, 28, 61], [23, 49, 28, 61, "_dependencyMap"], [23, 63, 28, 61], [24, 2, 28, 61], [24, 6, 28, 61, "_jsxDevRuntime"], [24, 20, 28, 61], [24, 23, 28, 61, "require"], [24, 30, 28, 61], [24, 31, 28, 61, "_dependencyMap"], [24, 45, 28, 61], [25, 2, 28, 61], [25, 6, 28, 61, "_jsxFileName"], [25, 18, 28, 61], [26, 4, 28, 61, "_s"], [26, 6, 28, 61], [26, 9, 28, 61, "$RefreshSig$"], [26, 21, 28, 61], [27, 2, 1, 0], [28, 0, 2, 0], [29, 0, 3, 0], [30, 0, 4, 0], [31, 0, 5, 0], [32, 0, 6, 0], [33, 0, 7, 0], [34, 0, 8, 0], [35, 0, 9, 0], [36, 0, 10, 0], [37, 2, 1, 0], [37, 11, 1, 0, "_interopRequireWildcard"], [37, 35, 1, 0, "e"], [37, 36, 1, 0], [37, 38, 1, 0, "t"], [37, 39, 1, 0], [37, 68, 1, 0, "WeakMap"], [37, 75, 1, 0], [37, 81, 1, 0, "r"], [37, 82, 1, 0], [37, 89, 1, 0, "WeakMap"], [37, 96, 1, 0], [37, 100, 1, 0, "n"], [37, 101, 1, 0], [37, 108, 1, 0, "WeakMap"], [37, 115, 1, 0], [37, 127, 1, 0, "_interopRequireWildcard"], [37, 150, 1, 0], [37, 162, 1, 0, "_interopRequireWildcard"], [37, 163, 1, 0, "e"], [37, 164, 1, 0], [37, 166, 1, 0, "t"], [37, 167, 1, 0], [37, 176, 1, 0, "t"], [37, 177, 1, 0], [37, 181, 1, 0, "e"], [37, 182, 1, 0], [37, 186, 1, 0, "e"], [37, 187, 1, 0], [37, 188, 1, 0, "__esModule"], [37, 198, 1, 0], [37, 207, 1, 0, "e"], [37, 208, 1, 0], [37, 214, 1, 0, "o"], [37, 215, 1, 0], [37, 217, 1, 0, "i"], [37, 218, 1, 0], [37, 220, 1, 0, "f"], [37, 221, 1, 0], [37, 226, 1, 0, "__proto__"], [37, 235, 1, 0], [37, 243, 1, 0, "default"], [37, 250, 1, 0], [37, 252, 1, 0, "e"], [37, 253, 1, 0], [37, 270, 1, 0, "e"], [37, 271, 1, 0], [37, 294, 1, 0, "e"], [37, 295, 1, 0], [37, 320, 1, 0, "e"], [37, 321, 1, 0], [37, 330, 1, 0, "f"], [37, 331, 1, 0], [37, 337, 1, 0, "o"], [37, 338, 1, 0], [37, 341, 1, 0, "t"], [37, 342, 1, 0], [37, 345, 1, 0, "n"], [37, 346, 1, 0], [37, 349, 1, 0, "r"], [37, 350, 1, 0], [37, 358, 1, 0, "o"], [37, 359, 1, 0], [37, 360, 1, 0, "has"], [37, 363, 1, 0], [37, 364, 1, 0, "e"], [37, 365, 1, 0], [37, 375, 1, 0, "o"], [37, 376, 1, 0], [37, 377, 1, 0, "get"], [37, 380, 1, 0], [37, 381, 1, 0, "e"], [37, 382, 1, 0], [37, 385, 1, 0, "o"], [37, 386, 1, 0], [37, 387, 1, 0, "set"], [37, 390, 1, 0], [37, 391, 1, 0, "e"], [37, 392, 1, 0], [37, 394, 1, 0, "f"], [37, 395, 1, 0], [37, 411, 1, 0, "t"], [37, 412, 1, 0], [37, 416, 1, 0, "e"], [37, 417, 1, 0], [37, 433, 1, 0, "t"], [37, 434, 1, 0], [37, 441, 1, 0, "hasOwnProperty"], [37, 455, 1, 0], [37, 456, 1, 0, "call"], [37, 460, 1, 0], [37, 461, 1, 0, "e"], [37, 462, 1, 0], [37, 464, 1, 0, "t"], [37, 465, 1, 0], [37, 472, 1, 0, "i"], [37, 473, 1, 0], [37, 477, 1, 0, "o"], [37, 478, 1, 0], [37, 481, 1, 0, "Object"], [37, 487, 1, 0], [37, 488, 1, 0, "defineProperty"], [37, 502, 1, 0], [37, 507, 1, 0, "Object"], [37, 513, 1, 0], [37, 514, 1, 0, "getOwnPropertyDescriptor"], [37, 538, 1, 0], [37, 539, 1, 0, "e"], [37, 540, 1, 0], [37, 542, 1, 0, "t"], [37, 543, 1, 0], [37, 550, 1, 0, "i"], [37, 551, 1, 0], [37, 552, 1, 0, "get"], [37, 555, 1, 0], [37, 559, 1, 0, "i"], [37, 560, 1, 0], [37, 561, 1, 0, "set"], [37, 564, 1, 0], [37, 568, 1, 0, "o"], [37, 569, 1, 0], [37, 570, 1, 0, "f"], [37, 571, 1, 0], [37, 573, 1, 0, "t"], [37, 574, 1, 0], [37, 576, 1, 0, "i"], [37, 577, 1, 0], [37, 581, 1, 0, "f"], [37, 582, 1, 0], [37, 583, 1, 0, "t"], [37, 584, 1, 0], [37, 588, 1, 0, "e"], [37, 589, 1, 0], [37, 590, 1, 0, "t"], [37, 591, 1, 0], [37, 602, 1, 0, "f"], [37, 603, 1, 0], [37, 608, 1, 0, "e"], [37, 609, 1, 0], [37, 611, 1, 0, "t"], [37, 612, 1, 0], [38, 2, 30, 0], [38, 8, 30, 6], [39, 4, 30, 8, "width"], [39, 9, 30, 13], [39, 11, 30, 15, "screenWidth"], [39, 22, 30, 26], [40, 4, 30, 28, "height"], [40, 10, 30, 34], [40, 12, 30, 36, "screenHeight"], [41, 2, 30, 49], [41, 3, 30, 50], [41, 6, 30, 53, "Dimensions"], [41, 25, 30, 63], [41, 26, 30, 64, "get"], [41, 29, 30, 67], [41, 30, 30, 68], [41, 38, 30, 76], [41, 39, 30, 77], [42, 2, 31, 0], [42, 8, 31, 6, "API_BASE_URL"], [42, 20, 31, 18], [42, 23, 31, 21], [42, 24, 32, 2, "_env2"], [42, 29, 32, 2], [42, 30, 32, 2, "env"], [42, 33, 32, 2], [42, 34, 32, 2, "EXPO_PUBLIC_BASE_URL"], [42, 54, 32, 2], [42, 58, 32, 2, "_env2"], [42, 63, 32, 2], [42, 64, 32, 2, "env"], [42, 67, 32, 2], [42, 68, 32, 2, "EXPO_PUBLIC_PROXY_BASE_URL"], [42, 94, 33, 40], [42, 98, 33, 40, "_env2"], [42, 103, 33, 40], [42, 104, 33, 40, "env"], [42, 107, 33, 40], [42, 108, 33, 40, "EXPO_PUBLIC_HOST"], [42, 124, 34, 30], [42, 128, 35, 2], [42, 130, 35, 4], [42, 132, 36, 2, "replace"], [42, 139, 36, 9], [42, 140, 36, 10], [42, 145, 36, 15], [42, 147, 36, 17], [42, 149, 36, 19], [42, 150, 36, 20], [44, 2, 49, 0], [46, 2, 51, 15], [46, 11, 51, 24, "EchoCameraWeb"], [46, 24, 51, 37, "EchoCameraWeb"], [46, 25, 51, 38], [47, 4, 52, 2, "userId"], [47, 10, 52, 8], [48, 4, 53, 2, "requestId"], [48, 13, 53, 11], [49, 4, 54, 2, "onComplete"], [49, 14, 54, 12], [50, 4, 55, 2, "onCancel"], [51, 2, 56, 20], [51, 3, 56, 21], [51, 5, 56, 23], [52, 4, 56, 23, "_s"], [52, 6, 56, 23], [53, 4, 57, 2], [53, 10, 57, 8, "cameraRef"], [53, 19, 57, 17], [53, 22, 57, 20], [53, 26, 57, 20, "useRef"], [53, 39, 57, 26], [53, 41, 57, 39], [53, 45, 57, 43], [53, 46, 57, 44], [54, 4, 58, 2], [54, 10, 58, 8], [54, 11, 58, 9, "permission"], [54, 21, 58, 19], [54, 23, 58, 21, "requestPermission"], [54, 40, 58, 38], [54, 41, 58, 39], [54, 44, 58, 42], [54, 48, 58, 42, "useCameraPermissions"], [54, 80, 58, 62], [54, 82, 58, 63], [54, 83, 58, 64], [56, 4, 60, 2], [57, 4, 61, 2], [57, 10, 61, 8], [57, 11, 61, 9, "processingState"], [57, 26, 61, 24], [57, 28, 61, 26, "setProcessingState"], [57, 46, 61, 44], [57, 47, 61, 45], [57, 50, 61, 48], [57, 54, 61, 48, "useState"], [57, 69, 61, 56], [57, 71, 61, 74], [57, 77, 61, 80], [57, 78, 61, 81], [58, 4, 62, 2], [58, 10, 62, 8], [58, 11, 62, 9, "challengeCode"], [58, 24, 62, 22], [58, 26, 62, 24, "setChallengeCode"], [58, 42, 62, 40], [58, 43, 62, 41], [58, 46, 62, 44], [58, 50, 62, 44, "useState"], [58, 65, 62, 52], [58, 67, 62, 68], [58, 71, 62, 72], [58, 72, 62, 73], [59, 4, 63, 2], [59, 10, 63, 8], [59, 11, 63, 9, "processingProgress"], [59, 29, 63, 27], [59, 31, 63, 29, "setProcessingProgress"], [59, 52, 63, 50], [59, 53, 63, 51], [59, 56, 63, 54], [59, 60, 63, 54, "useState"], [59, 75, 63, 62], [59, 77, 63, 63], [59, 78, 63, 64], [59, 79, 63, 65], [60, 4, 64, 2], [60, 10, 64, 8], [60, 11, 64, 9, "errorMessage"], [60, 23, 64, 21], [60, 25, 64, 23, "setErrorMessage"], [60, 40, 64, 38], [60, 41, 64, 39], [60, 44, 64, 42], [60, 48, 64, 42, "useState"], [60, 63, 64, 50], [60, 65, 64, 66], [60, 69, 64, 70], [60, 70, 64, 71], [61, 4, 65, 2], [61, 10, 65, 8], [61, 11, 65, 9, "capturedPhoto"], [61, 24, 65, 22], [61, 26, 65, 24, "setCapturedPhoto"], [61, 42, 65, 40], [61, 43, 65, 41], [61, 46, 65, 44], [61, 50, 65, 44, "useState"], [61, 65, 65, 52], [61, 67, 65, 68], [61, 71, 65, 72], [61, 72, 65, 73], [62, 4, 66, 2], [63, 4, 67, 2], [63, 10, 67, 8], [63, 11, 67, 9, "previewBlurEnabled"], [63, 29, 67, 27], [63, 31, 67, 29, "setPreviewBlurEnabled"], [63, 52, 67, 50], [63, 53, 67, 51], [63, 56, 67, 54], [63, 60, 67, 54, "useState"], [63, 75, 67, 62], [63, 77, 67, 63], [63, 81, 67, 67], [63, 82, 67, 68], [64, 4, 68, 2], [64, 10, 68, 8], [64, 11, 68, 9, "viewSize"], [64, 19, 68, 17], [64, 21, 68, 19, "setViewSize"], [64, 32, 68, 30], [64, 33, 68, 31], [64, 36, 68, 34], [64, 40, 68, 34, "useState"], [64, 55, 68, 42], [64, 57, 68, 43], [65, 6, 68, 45, "width"], [65, 11, 68, 50], [65, 13, 68, 52], [65, 14, 68, 53], [66, 6, 68, 55, "height"], [66, 12, 68, 61], [66, 14, 68, 63], [67, 4, 68, 65], [67, 5, 68, 66], [67, 6, 68, 67], [68, 4, 69, 2], [69, 4, 70, 2], [69, 10, 70, 8], [69, 11, 70, 9, "isCameraReady"], [69, 24, 70, 22], [69, 26, 70, 24, "setIsCameraReady"], [69, 42, 70, 40], [69, 43, 70, 41], [69, 46, 70, 44], [69, 50, 70, 44, "useState"], [69, 65, 70, 52], [69, 67, 70, 53], [69, 72, 70, 58], [69, 73, 70, 59], [70, 4, 72, 2], [70, 10, 72, 8], [70, 11, 72, 9, "upload"], [70, 17, 72, 15], [70, 18, 72, 16], [70, 21, 72, 19], [70, 25, 72, 19, "useUpload"], [70, 43, 72, 28], [70, 45, 72, 29], [70, 46, 72, 30], [71, 4, 73, 2], [72, 4, 74, 2], [72, 8, 74, 2, "useEffect"], [72, 24, 74, 11], [72, 26, 74, 12], [72, 32, 74, 18], [73, 6, 75, 4], [73, 12, 75, 10, "controller"], [73, 22, 75, 20], [73, 25, 75, 23], [73, 29, 75, 27, "AbortController"], [73, 44, 75, 42], [73, 45, 75, 43], [73, 46, 75, 44], [74, 6, 77, 4], [74, 7, 77, 5], [74, 19, 77, 17], [75, 8, 78, 6], [75, 12, 78, 10], [76, 10, 79, 8], [76, 16, 79, 14, "code"], [76, 20, 79, 18], [76, 23, 79, 21], [76, 29, 79, 27], [76, 33, 79, 27, "fetchChallengeCode"], [76, 68, 79, 45], [76, 70, 79, 46], [77, 12, 79, 48, "userId"], [77, 18, 79, 54], [78, 12, 79, 56, "requestId"], [78, 21, 79, 65], [79, 12, 79, 67, "signal"], [79, 18, 79, 73], [79, 20, 79, 75, "controller"], [79, 30, 79, 85], [79, 31, 79, 86, "signal"], [80, 10, 79, 93], [80, 11, 79, 94], [80, 12, 79, 95], [81, 10, 80, 8, "setChallengeCode"], [81, 26, 80, 24], [81, 27, 80, 25, "code"], [81, 31, 80, 29], [81, 32, 80, 30], [82, 8, 81, 6], [82, 9, 81, 7], [82, 10, 81, 8], [82, 17, 81, 15, "e"], [82, 18, 81, 16], [82, 20, 81, 18], [83, 10, 82, 8, "console"], [83, 17, 82, 15], [83, 18, 82, 16, "warn"], [83, 22, 82, 20], [83, 23, 82, 21], [83, 69, 82, 67], [83, 71, 82, 69, "e"], [83, 72, 82, 70], [83, 73, 82, 71], [84, 10, 83, 8, "setChallengeCode"], [84, 26, 83, 24], [84, 27, 83, 25], [84, 35, 83, 33, "Date"], [84, 39, 83, 37], [84, 40, 83, 38, "now"], [84, 43, 83, 41], [84, 44, 83, 42], [84, 45, 83, 43], [84, 46, 83, 44, "toString"], [84, 54, 83, 52], [84, 55, 83, 53], [84, 57, 83, 55], [84, 58, 83, 56], [84, 59, 83, 57, "toUpperCase"], [84, 70, 83, 68], [84, 71, 83, 69], [84, 72, 83, 70], [84, 74, 83, 72], [84, 75, 83, 73], [85, 8, 84, 6], [86, 6, 85, 4], [86, 7, 85, 5], [86, 9, 85, 7], [86, 10, 85, 8], [87, 6, 87, 4], [87, 13, 87, 11], [87, 19, 87, 17, "controller"], [87, 29, 87, 27], [87, 30, 87, 28, "abort"], [87, 35, 87, 33], [87, 36, 87, 34], [87, 37, 87, 35], [88, 4, 88, 2], [88, 5, 88, 3], [88, 7, 88, 5], [88, 8, 88, 6, "userId"], [88, 14, 88, 12], [88, 16, 88, 14, "requestId"], [88, 25, 88, 23], [88, 26, 88, 24], [88, 27, 88, 25], [90, 4, 90, 2], [91, 4, 91, 2], [91, 10, 91, 8, "loadTensorFlowFaceDetection"], [91, 37, 91, 35], [91, 40, 91, 38], [91, 46, 91, 38, "loadTensorFlowFaceDetection"], [91, 47, 91, 38], [91, 52, 91, 50], [92, 6, 92, 4, "console"], [92, 13, 92, 11], [92, 14, 92, 12, "log"], [92, 17, 92, 15], [92, 18, 92, 16], [92, 78, 92, 76], [92, 79, 92, 77], [94, 6, 94, 4], [95, 6, 95, 4], [95, 10, 95, 8], [95, 11, 95, 10, "window"], [95, 17, 95, 16], [95, 18, 95, 25, "tf"], [95, 20, 95, 27], [95, 22, 95, 29], [96, 8, 96, 6], [96, 14, 96, 12], [96, 18, 96, 16, "Promise"], [96, 25, 96, 23], [96, 26, 96, 24], [96, 27, 96, 25, "resolve"], [96, 34, 96, 32], [96, 36, 96, 34, "reject"], [96, 42, 96, 40], [96, 47, 96, 45], [97, 10, 97, 8], [97, 16, 97, 14, "script"], [97, 22, 97, 20], [97, 25, 97, 23, "document"], [97, 33, 97, 31], [97, 34, 97, 32, "createElement"], [97, 47, 97, 45], [97, 48, 97, 46], [97, 56, 97, 54], [97, 57, 97, 55], [98, 10, 98, 8, "script"], [98, 16, 98, 14], [98, 17, 98, 15, "src"], [98, 20, 98, 18], [98, 23, 98, 21], [98, 92, 98, 90], [99, 10, 99, 8, "script"], [99, 16, 99, 14], [99, 17, 99, 15, "onload"], [99, 23, 99, 21], [99, 26, 99, 24, "resolve"], [99, 33, 99, 31], [100, 10, 100, 8, "script"], [100, 16, 100, 14], [100, 17, 100, 15, "onerror"], [100, 24, 100, 22], [100, 27, 100, 25, "reject"], [100, 33, 100, 31], [101, 10, 101, 8, "document"], [101, 18, 101, 16], [101, 19, 101, 17, "head"], [101, 23, 101, 21], [101, 24, 101, 22, "append<PERSON><PERSON><PERSON>"], [101, 35, 101, 33], [101, 36, 101, 34, "script"], [101, 42, 101, 40], [101, 43, 101, 41], [102, 8, 102, 6], [102, 9, 102, 7], [102, 10, 102, 8], [103, 6, 103, 4], [105, 6, 105, 4], [106, 6, 106, 4], [106, 10, 106, 8], [106, 11, 106, 10, "window"], [106, 17, 106, 16], [106, 18, 106, 25, "blazeface"], [106, 27, 106, 34], [106, 29, 106, 36], [107, 8, 107, 6], [107, 14, 107, 12], [107, 18, 107, 16, "Promise"], [107, 25, 107, 23], [107, 26, 107, 24], [107, 27, 107, 25, "resolve"], [107, 34, 107, 32], [107, 36, 107, 34, "reject"], [107, 42, 107, 40], [107, 47, 107, 45], [108, 10, 108, 8], [108, 16, 108, 14, "script"], [108, 22, 108, 20], [108, 25, 108, 23, "document"], [108, 33, 108, 31], [108, 34, 108, 32, "createElement"], [108, 47, 108, 45], [108, 48, 108, 46], [108, 56, 108, 54], [108, 57, 108, 55], [109, 10, 109, 8, "script"], [109, 16, 109, 14], [109, 17, 109, 15, "src"], [109, 20, 109, 18], [109, 23, 109, 21], [109, 106, 109, 104], [110, 10, 110, 8, "script"], [110, 16, 110, 14], [110, 17, 110, 15, "onload"], [110, 23, 110, 21], [110, 26, 110, 24, "resolve"], [110, 33, 110, 31], [111, 10, 111, 8, "script"], [111, 16, 111, 14], [111, 17, 111, 15, "onerror"], [111, 24, 111, 22], [111, 27, 111, 25, "reject"], [111, 33, 111, 31], [112, 10, 112, 8, "document"], [112, 18, 112, 16], [112, 19, 112, 17, "head"], [112, 23, 112, 21], [112, 24, 112, 22, "append<PERSON><PERSON><PERSON>"], [112, 35, 112, 33], [112, 36, 112, 34, "script"], [112, 42, 112, 40], [112, 43, 112, 41], [113, 8, 113, 6], [113, 9, 113, 7], [113, 10, 113, 8], [114, 6, 114, 4], [115, 6, 116, 4, "console"], [115, 13, 116, 11], [115, 14, 116, 12, "log"], [115, 17, 116, 15], [115, 18, 116, 16], [115, 72, 116, 70], [115, 73, 116, 71], [116, 4, 117, 2], [116, 5, 117, 3], [117, 4, 119, 2], [117, 10, 119, 8, "detectFacesWithTensorFlow"], [117, 35, 119, 33], [117, 38, 119, 36], [117, 44, 119, 43, "img"], [117, 47, 119, 64], [117, 51, 119, 69], [118, 6, 120, 4], [118, 10, 120, 8], [119, 8, 121, 6], [119, 14, 121, 12, "blazeface"], [119, 23, 121, 21], [119, 26, 121, 25, "window"], [119, 32, 121, 31], [119, 33, 121, 40, "blazeface"], [119, 42, 121, 49], [120, 8, 122, 6], [120, 14, 122, 12, "tf"], [120, 16, 122, 14], [120, 19, 122, 18, "window"], [120, 25, 122, 24], [120, 26, 122, 33, "tf"], [120, 28, 122, 35], [121, 8, 124, 6, "console"], [121, 15, 124, 13], [121, 16, 124, 14, "log"], [121, 19, 124, 17], [121, 20, 124, 18], [121, 67, 124, 65], [121, 68, 124, 66], [122, 8, 125, 6], [122, 14, 125, 12, "model"], [122, 19, 125, 17], [122, 22, 125, 20], [122, 28, 125, 26, "blazeface"], [122, 37, 125, 35], [122, 38, 125, 36, "load"], [122, 42, 125, 40], [122, 43, 125, 41], [122, 44, 125, 42], [123, 8, 126, 6, "console"], [123, 15, 126, 13], [123, 16, 126, 14, "log"], [123, 19, 126, 17], [123, 20, 126, 18], [123, 82, 126, 80], [123, 83, 126, 81], [125, 8, 128, 6], [126, 8, 129, 6], [126, 14, 129, 12, "tensor"], [126, 20, 129, 18], [126, 23, 129, 21, "tf"], [126, 25, 129, 23], [126, 26, 129, 24, "browser"], [126, 33, 129, 31], [126, 34, 129, 32, "fromPixels"], [126, 44, 129, 42], [126, 45, 129, 43, "img"], [126, 48, 129, 46], [126, 49, 129, 47], [128, 8, 131, 6], [129, 8, 132, 6], [129, 14, 132, 12, "predictions"], [129, 25, 132, 23], [129, 28, 132, 26], [129, 34, 132, 32, "model"], [129, 39, 132, 37], [129, 40, 132, 38, "estimateFaces"], [129, 53, 132, 51], [129, 54, 132, 52, "tensor"], [129, 60, 132, 58], [129, 62, 132, 60], [129, 67, 132, 65], [129, 69, 132, 67], [129, 72, 132, 70], [129, 73, 132, 71], [129, 74, 132, 72], [129, 75, 132, 73], [131, 8, 134, 6], [132, 8, 135, 6, "tensor"], [132, 14, 135, 12], [132, 15, 135, 13, "dispose"], [132, 22, 135, 20], [132, 23, 135, 21], [132, 24, 135, 22], [133, 8, 137, 6, "console"], [133, 15, 137, 13], [133, 16, 137, 14, "log"], [133, 19, 137, 17], [133, 20, 137, 18], [133, 61, 137, 59, "predictions"], [133, 72, 137, 70], [133, 73, 137, 71, "length"], [133, 79, 137, 77], [133, 87, 137, 85], [133, 88, 137, 86], [135, 8, 139, 6], [136, 8, 140, 6], [136, 14, 140, 12, "faces"], [136, 19, 140, 17], [136, 22, 140, 20, "predictions"], [136, 33, 140, 31], [136, 34, 140, 32, "map"], [136, 37, 140, 35], [136, 38, 140, 36], [136, 39, 140, 37, "prediction"], [136, 49, 140, 52], [136, 51, 140, 54, "index"], [136, 56, 140, 67], [136, 61, 140, 72], [137, 10, 141, 8], [137, 16, 141, 14], [137, 17, 141, 15, "x"], [137, 18, 141, 16], [137, 20, 141, 18, "y"], [137, 21, 141, 19], [137, 22, 141, 20], [137, 25, 141, 23, "prediction"], [137, 35, 141, 33], [137, 36, 141, 34, "topLeft"], [137, 43, 141, 41], [138, 10, 142, 8], [138, 16, 142, 14], [138, 17, 142, 15, "x2"], [138, 19, 142, 17], [138, 21, 142, 19, "y2"], [138, 23, 142, 21], [138, 24, 142, 22], [138, 27, 142, 25, "prediction"], [138, 37, 142, 35], [138, 38, 142, 36, "bottomRight"], [138, 49, 142, 47], [139, 10, 143, 8], [139, 16, 143, 14, "width"], [139, 21, 143, 19], [139, 24, 143, 22, "x2"], [139, 26, 143, 24], [139, 29, 143, 27, "x"], [139, 30, 143, 28], [140, 10, 144, 8], [140, 16, 144, 14, "height"], [140, 22, 144, 20], [140, 25, 144, 23, "y2"], [140, 27, 144, 25], [140, 30, 144, 28, "y"], [140, 31, 144, 29], [141, 10, 146, 8, "console"], [141, 17, 146, 15], [141, 18, 146, 16, "log"], [141, 21, 146, 19], [141, 22, 146, 20], [141, 49, 146, 47, "index"], [141, 54, 146, 52], [141, 57, 146, 55], [141, 58, 146, 56], [141, 61, 146, 59], [141, 63, 146, 61], [142, 12, 147, 10, "topLeft"], [142, 19, 147, 17], [142, 21, 147, 19], [142, 22, 147, 20, "x"], [142, 23, 147, 21], [142, 25, 147, 23, "y"], [142, 26, 147, 24], [142, 27, 147, 25], [143, 12, 148, 10, "bottomRight"], [143, 23, 148, 21], [143, 25, 148, 23], [143, 26, 148, 24, "x2"], [143, 28, 148, 26], [143, 30, 148, 28, "y2"], [143, 32, 148, 30], [143, 33, 148, 31], [144, 12, 149, 10, "size"], [144, 16, 149, 14], [144, 18, 149, 16], [144, 19, 149, 17, "width"], [144, 24, 149, 22], [144, 26, 149, 24, "height"], [144, 32, 149, 30], [145, 10, 150, 8], [145, 11, 150, 9], [145, 12, 150, 10], [146, 10, 152, 8], [146, 17, 152, 15], [147, 12, 153, 10, "boundingBox"], [147, 23, 153, 21], [147, 25, 153, 23], [148, 14, 154, 12, "xCenter"], [148, 21, 154, 19], [148, 23, 154, 21], [148, 24, 154, 22, "x"], [148, 25, 154, 23], [148, 28, 154, 26, "width"], [148, 33, 154, 31], [148, 36, 154, 34], [148, 37, 154, 35], [148, 41, 154, 39, "img"], [148, 44, 154, 42], [148, 45, 154, 43, "width"], [148, 50, 154, 48], [149, 14, 155, 12, "yCenter"], [149, 21, 155, 19], [149, 23, 155, 21], [149, 24, 155, 22, "y"], [149, 25, 155, 23], [149, 28, 155, 26, "height"], [149, 34, 155, 32], [149, 37, 155, 35], [149, 38, 155, 36], [149, 42, 155, 40, "img"], [149, 45, 155, 43], [149, 46, 155, 44, "height"], [149, 52, 155, 50], [150, 14, 156, 12, "width"], [150, 19, 156, 17], [150, 21, 156, 19, "width"], [150, 26, 156, 24], [150, 29, 156, 27, "img"], [150, 32, 156, 30], [150, 33, 156, 31, "width"], [150, 38, 156, 36], [151, 14, 157, 12, "height"], [151, 20, 157, 18], [151, 22, 157, 20, "height"], [151, 28, 157, 26], [151, 31, 157, 29, "img"], [151, 34, 157, 32], [151, 35, 157, 33, "height"], [152, 12, 158, 10], [153, 10, 159, 8], [153, 11, 159, 9], [154, 8, 160, 6], [154, 9, 160, 7], [154, 10, 160, 8], [155, 8, 162, 6], [155, 15, 162, 13, "faces"], [155, 20, 162, 18], [156, 6, 163, 4], [156, 7, 163, 5], [156, 8, 163, 6], [156, 15, 163, 13, "error"], [156, 20, 163, 18], [156, 22, 163, 20], [157, 8, 164, 6, "console"], [157, 15, 164, 13], [157, 16, 164, 14, "error"], [157, 21, 164, 19], [157, 22, 164, 20], [157, 75, 164, 73], [157, 77, 164, 75, "error"], [157, 82, 164, 80], [157, 83, 164, 81], [158, 8, 165, 6], [158, 15, 165, 13], [158, 17, 165, 15], [159, 6, 166, 4], [160, 4, 167, 2], [160, 5, 167, 3], [161, 4, 169, 2], [161, 10, 169, 8, "detectFacesHeuristic"], [161, 30, 169, 28], [161, 33, 169, 31, "detectFacesHeuristic"], [161, 34, 169, 32, "img"], [161, 37, 169, 53], [161, 39, 169, 55, "ctx"], [161, 42, 169, 84], [161, 47, 169, 89], [162, 6, 170, 4, "console"], [162, 13, 170, 11], [162, 14, 170, 12, "log"], [162, 17, 170, 15], [162, 18, 170, 16], [162, 83, 170, 81], [162, 84, 170, 82], [164, 6, 172, 4], [165, 6, 173, 4], [165, 12, 173, 10, "imageData"], [165, 21, 173, 19], [165, 24, 173, 22, "ctx"], [165, 27, 173, 25], [165, 28, 173, 26, "getImageData"], [165, 40, 173, 38], [165, 41, 173, 39], [165, 42, 173, 40], [165, 44, 173, 42], [165, 45, 173, 43], [165, 47, 173, 45, "img"], [165, 50, 173, 48], [165, 51, 173, 49, "width"], [165, 56, 173, 54], [165, 58, 173, 56, "img"], [165, 61, 173, 59], [165, 62, 173, 60, "height"], [165, 68, 173, 66], [165, 69, 173, 67], [166, 6, 174, 4], [166, 12, 174, 10, "data"], [166, 16, 174, 14], [166, 19, 174, 17, "imageData"], [166, 28, 174, 26], [166, 29, 174, 27, "data"], [166, 33, 174, 31], [168, 6, 176, 4], [169, 6, 177, 4], [169, 12, 177, 10, "faces"], [169, 17, 177, 15], [169, 20, 177, 18], [169, 22, 177, 20], [170, 6, 178, 4], [170, 12, 178, 10, "blockSize"], [170, 21, 178, 19], [170, 24, 178, 22, "Math"], [170, 28, 178, 26], [170, 29, 178, 27, "min"], [170, 32, 178, 30], [170, 33, 178, 31, "img"], [170, 36, 178, 34], [170, 37, 178, 35, "width"], [170, 42, 178, 40], [170, 44, 178, 42, "img"], [170, 47, 178, 45], [170, 48, 178, 46, "height"], [170, 54, 178, 52], [170, 55, 178, 53], [170, 58, 178, 56], [170, 60, 178, 58], [170, 61, 178, 59], [170, 62, 178, 60], [172, 6, 180, 4, "console"], [172, 13, 180, 11], [172, 14, 180, 12, "log"], [172, 17, 180, 15], [172, 18, 180, 16], [172, 59, 180, 57, "blockSize"], [172, 68, 180, 66], [172, 82, 180, 80], [172, 83, 180, 81], [173, 6, 182, 4], [173, 11, 182, 9], [173, 15, 182, 13, "y"], [173, 16, 182, 14], [173, 19, 182, 17], [173, 20, 182, 18], [173, 22, 182, 20, "y"], [173, 23, 182, 21], [173, 26, 182, 24, "img"], [173, 29, 182, 27], [173, 30, 182, 28, "height"], [173, 36, 182, 34], [173, 39, 182, 37, "blockSize"], [173, 48, 182, 46], [173, 50, 182, 48, "y"], [173, 51, 182, 49], [173, 55, 182, 53, "blockSize"], [173, 64, 182, 62], [173, 67, 182, 65], [173, 68, 182, 66], [173, 70, 182, 68], [174, 8, 182, 70], [175, 8, 183, 6], [175, 13, 183, 11], [175, 17, 183, 15, "x"], [175, 18, 183, 16], [175, 21, 183, 19], [175, 22, 183, 20], [175, 24, 183, 22, "x"], [175, 25, 183, 23], [175, 28, 183, 26, "img"], [175, 31, 183, 29], [175, 32, 183, 30, "width"], [175, 37, 183, 35], [175, 40, 183, 38, "blockSize"], [175, 49, 183, 47], [175, 51, 183, 49, "x"], [175, 52, 183, 50], [175, 56, 183, 54, "blockSize"], [175, 65, 183, 63], [175, 68, 183, 66], [175, 69, 183, 67], [175, 71, 183, 69], [176, 10, 184, 8], [176, 16, 184, 14, "analysis"], [176, 24, 184, 22], [176, 27, 184, 25, "analyzeRegionForFace"], [176, 47, 184, 45], [176, 48, 184, 46, "data"], [176, 52, 184, 50], [176, 54, 184, 52, "x"], [176, 55, 184, 53], [176, 57, 184, 55, "y"], [176, 58, 184, 56], [176, 60, 184, 58, "blockSize"], [176, 69, 184, 67], [176, 71, 184, 69, "img"], [176, 74, 184, 72], [176, 75, 184, 73, "width"], [176, 80, 184, 78], [176, 82, 184, 80, "img"], [176, 85, 184, 83], [176, 86, 184, 84, "height"], [176, 92, 184, 90], [176, 93, 184, 91], [178, 10, 186, 8], [179, 10, 187, 8], [179, 14, 187, 12, "analysis"], [179, 22, 187, 20], [179, 23, 187, 21, "skinRatio"], [179, 32, 187, 30], [179, 35, 187, 33], [179, 39, 187, 37], [180, 10, 187, 42], [181, 10, 188, 12, "analysis"], [181, 18, 188, 20], [181, 19, 188, 21, "hasVariation"], [181, 31, 188, 33], [181, 35, 189, 12, "analysis"], [181, 43, 189, 20], [181, 44, 189, 21, "brightness"], [181, 54, 189, 31], [181, 57, 189, 34], [181, 61, 189, 38], [182, 10, 189, 43], [183, 10, 190, 12, "analysis"], [183, 18, 190, 20], [183, 19, 190, 21, "brightness"], [183, 29, 190, 31], [183, 32, 190, 34], [183, 35, 190, 37], [183, 37, 190, 39], [184, 12, 190, 43], [186, 12, 192, 10, "faces"], [186, 17, 192, 15], [186, 18, 192, 16, "push"], [186, 22, 192, 20], [186, 23, 192, 21], [187, 14, 193, 12, "boundingBox"], [187, 25, 193, 23], [187, 27, 193, 25], [188, 16, 194, 14, "xCenter"], [188, 23, 194, 21], [188, 25, 194, 23], [188, 26, 194, 24, "x"], [188, 27, 194, 25], [188, 30, 194, 28, "blockSize"], [188, 39, 194, 37], [188, 42, 194, 40], [188, 43, 194, 41], [188, 47, 194, 45, "img"], [188, 50, 194, 48], [188, 51, 194, 49, "width"], [188, 56, 194, 54], [189, 16, 195, 14, "yCenter"], [189, 23, 195, 21], [189, 25, 195, 23], [189, 26, 195, 24, "y"], [189, 27, 195, 25], [189, 30, 195, 28, "blockSize"], [189, 39, 195, 37], [189, 42, 195, 40], [189, 43, 195, 41], [189, 47, 195, 45, "img"], [189, 50, 195, 48], [189, 51, 195, 49, "height"], [189, 57, 195, 55], [190, 16, 196, 14, "width"], [190, 21, 196, 19], [190, 23, 196, 22, "blockSize"], [190, 32, 196, 31], [190, 35, 196, 34], [190, 38, 196, 37], [190, 41, 196, 41, "img"], [190, 44, 196, 44], [190, 45, 196, 45, "width"], [190, 50, 196, 50], [191, 16, 197, 14, "height"], [191, 22, 197, 20], [191, 24, 197, 23, "blockSize"], [191, 33, 197, 32], [191, 36, 197, 35], [191, 39, 197, 38], [191, 42, 197, 42, "img"], [191, 45, 197, 45], [191, 46, 197, 46, "height"], [192, 14, 198, 12], [192, 15, 198, 13], [193, 14, 199, 12, "confidence"], [193, 24, 199, 22], [193, 26, 199, 24, "analysis"], [193, 34, 199, 32], [193, 35, 199, 33, "skinRatio"], [193, 44, 199, 42], [193, 47, 199, 45, "analysis"], [193, 55, 199, 53], [193, 56, 199, 54, "variation"], [194, 12, 200, 10], [194, 13, 200, 11], [194, 14, 200, 12], [195, 12, 202, 10, "console"], [195, 19, 202, 17], [195, 20, 202, 18, "log"], [195, 23, 202, 21], [195, 24, 202, 22], [195, 71, 202, 69, "Math"], [195, 75, 202, 73], [195, 76, 202, 74, "round"], [195, 81, 202, 79], [195, 82, 202, 80, "x"], [195, 83, 202, 81], [195, 84, 202, 82], [195, 89, 202, 87, "Math"], [195, 93, 202, 91], [195, 94, 202, 92, "round"], [195, 99, 202, 97], [195, 100, 202, 98, "y"], [195, 101, 202, 99], [195, 102, 202, 100], [195, 115, 202, 113], [195, 116, 202, 114, "analysis"], [195, 124, 202, 122], [195, 125, 202, 123, "skinRatio"], [195, 134, 202, 132], [195, 137, 202, 135], [195, 140, 202, 138], [195, 142, 202, 140, "toFixed"], [195, 149, 202, 147], [195, 150, 202, 148], [195, 151, 202, 149], [195, 152, 202, 150], [195, 169, 202, 167, "analysis"], [195, 177, 202, 175], [195, 178, 202, 176, "variation"], [195, 187, 202, 185], [195, 188, 202, 186, "toFixed"], [195, 195, 202, 193], [195, 196, 202, 194], [195, 197, 202, 195], [195, 198, 202, 196], [195, 215, 202, 213, "analysis"], [195, 223, 202, 221], [195, 224, 202, 222, "brightness"], [195, 234, 202, 232], [195, 235, 202, 233, "toFixed"], [195, 242, 202, 240], [195, 243, 202, 241], [195, 244, 202, 242], [195, 245, 202, 243], [195, 247, 202, 245], [195, 248, 202, 246], [196, 10, 203, 8], [197, 8, 204, 6], [198, 6, 205, 4], [200, 6, 207, 4], [201, 6, 208, 4, "faces"], [201, 11, 208, 9], [201, 12, 208, 10, "sort"], [201, 16, 208, 14], [201, 17, 208, 15], [201, 18, 208, 16, "a"], [201, 19, 208, 17], [201, 21, 208, 19, "b"], [201, 22, 208, 20], [201, 27, 208, 25], [201, 28, 208, 26, "b"], [201, 29, 208, 27], [201, 30, 208, 28, "confidence"], [201, 40, 208, 38], [201, 44, 208, 42], [201, 45, 208, 43], [201, 50, 208, 48, "a"], [201, 51, 208, 49], [201, 52, 208, 50, "confidence"], [201, 62, 208, 60], [201, 66, 208, 64], [201, 67, 208, 65], [201, 68, 208, 66], [201, 69, 208, 67], [202, 6, 209, 4], [202, 12, 209, 10, "mergedFaces"], [202, 23, 209, 21], [202, 26, 209, 24, "mergeFaceDetections"], [202, 45, 209, 43], [202, 46, 209, 44, "faces"], [202, 51, 209, 49], [202, 52, 209, 50], [203, 6, 211, 4, "console"], [203, 13, 211, 11], [203, 14, 211, 12, "log"], [203, 17, 211, 15], [203, 18, 211, 16], [203, 61, 211, 59, "faces"], [203, 66, 211, 64], [203, 67, 211, 65, "length"], [203, 73, 211, 71], [203, 90, 211, 88, "mergedFaces"], [203, 101, 211, 99], [203, 102, 211, 100, "length"], [203, 108, 211, 106], [203, 123, 211, 121], [203, 124, 211, 122], [204, 6, 212, 4], [204, 13, 212, 11, "mergedFaces"], [204, 24, 212, 22], [204, 25, 212, 23, "slice"], [204, 30, 212, 28], [204, 31, 212, 29], [204, 32, 212, 30], [204, 34, 212, 32], [204, 35, 212, 33], [204, 36, 212, 34], [204, 37, 212, 35], [204, 38, 212, 36], [205, 4, 213, 2], [205, 5, 213, 3], [206, 4, 215, 2], [206, 10, 215, 8, "detectFacesAggressive"], [206, 31, 215, 29], [206, 34, 215, 32, "detectFacesAggressive"], [206, 35, 215, 33, "img"], [206, 38, 215, 54], [206, 40, 215, 56, "ctx"], [206, 43, 215, 85], [206, 48, 215, 90], [207, 6, 216, 4, "console"], [207, 13, 216, 11], [207, 14, 216, 12, "log"], [207, 17, 216, 15], [207, 18, 216, 16], [207, 75, 216, 73], [207, 76, 216, 74], [209, 6, 218, 4], [210, 6, 219, 4], [210, 12, 219, 10, "imageData"], [210, 21, 219, 19], [210, 24, 219, 22, "ctx"], [210, 27, 219, 25], [210, 28, 219, 26, "getImageData"], [210, 40, 219, 38], [210, 41, 219, 39], [210, 42, 219, 40], [210, 44, 219, 42], [210, 45, 219, 43], [210, 47, 219, 45, "img"], [210, 50, 219, 48], [210, 51, 219, 49, "width"], [210, 56, 219, 54], [210, 58, 219, 56, "img"], [210, 61, 219, 59], [210, 62, 219, 60, "height"], [210, 68, 219, 66], [210, 69, 219, 67], [211, 6, 220, 4], [211, 12, 220, 10, "data"], [211, 16, 220, 14], [211, 19, 220, 17, "imageData"], [211, 28, 220, 26], [211, 29, 220, 27, "data"], [211, 33, 220, 31], [212, 6, 222, 4], [212, 12, 222, 10, "faces"], [212, 17, 222, 15], [212, 20, 222, 18], [212, 22, 222, 20], [213, 6, 223, 4], [213, 12, 223, 10, "blockSize"], [213, 21, 223, 19], [213, 24, 223, 22, "Math"], [213, 28, 223, 26], [213, 29, 223, 27, "min"], [213, 32, 223, 30], [213, 33, 223, 31, "img"], [213, 36, 223, 34], [213, 37, 223, 35, "width"], [213, 42, 223, 40], [213, 44, 223, 42, "img"], [213, 47, 223, 45], [213, 48, 223, 46, "height"], [213, 54, 223, 52], [213, 55, 223, 53], [213, 58, 223, 56], [213, 59, 223, 57], [213, 60, 223, 58], [213, 61, 223, 59], [215, 6, 225, 4], [215, 11, 225, 9], [215, 15, 225, 13, "y"], [215, 16, 225, 14], [215, 19, 225, 17], [215, 20, 225, 18], [215, 22, 225, 20, "y"], [215, 23, 225, 21], [215, 26, 225, 24, "img"], [215, 29, 225, 27], [215, 30, 225, 28, "height"], [215, 36, 225, 34], [215, 39, 225, 37, "blockSize"], [215, 48, 225, 46], [215, 50, 225, 48, "y"], [215, 51, 225, 49], [215, 55, 225, 53, "blockSize"], [215, 64, 225, 62], [215, 67, 225, 65], [215, 68, 225, 66], [215, 70, 225, 68], [216, 8, 225, 70], [217, 8, 226, 6], [217, 13, 226, 11], [217, 17, 226, 15, "x"], [217, 18, 226, 16], [217, 21, 226, 19], [217, 22, 226, 20], [217, 24, 226, 22, "x"], [217, 25, 226, 23], [217, 28, 226, 26, "img"], [217, 31, 226, 29], [217, 32, 226, 30, "width"], [217, 37, 226, 35], [217, 40, 226, 38, "blockSize"], [217, 49, 226, 47], [217, 51, 226, 49, "x"], [217, 52, 226, 50], [217, 56, 226, 54, "blockSize"], [217, 65, 226, 63], [217, 68, 226, 66], [217, 69, 226, 67], [217, 71, 226, 69], [218, 10, 227, 8], [218, 16, 227, 14, "analysis"], [218, 24, 227, 22], [218, 27, 227, 25, "analyzeRegionForFace"], [218, 47, 227, 45], [218, 48, 227, 46, "data"], [218, 52, 227, 50], [218, 54, 227, 52, "x"], [218, 55, 227, 53], [218, 57, 227, 55, "y"], [218, 58, 227, 56], [218, 60, 227, 58, "blockSize"], [218, 69, 227, 67], [218, 71, 227, 69, "img"], [218, 74, 227, 72], [218, 75, 227, 73, "width"], [218, 80, 227, 78], [218, 82, 227, 80, "img"], [218, 85, 227, 83], [218, 86, 227, 84, "height"], [218, 92, 227, 90], [218, 93, 227, 91], [220, 10, 229, 8], [221, 10, 230, 8], [221, 14, 230, 12, "analysis"], [221, 22, 230, 20], [221, 23, 230, 21, "skinRatio"], [221, 32, 230, 30], [221, 35, 230, 33], [221, 39, 230, 37], [222, 10, 230, 42], [223, 10, 231, 12, "analysis"], [223, 18, 231, 20], [223, 19, 231, 21, "brightness"], [223, 29, 231, 31], [223, 32, 231, 34], [223, 35, 231, 37], [224, 10, 231, 42], [225, 10, 232, 12, "analysis"], [225, 18, 232, 20], [225, 19, 232, 21, "brightness"], [225, 29, 232, 31], [225, 32, 232, 34], [225, 36, 232, 38], [225, 38, 232, 40], [226, 12, 232, 43], [228, 12, 234, 10, "faces"], [228, 17, 234, 15], [228, 18, 234, 16, "push"], [228, 22, 234, 20], [228, 23, 234, 21], [229, 14, 235, 12, "boundingBox"], [229, 25, 235, 23], [229, 27, 235, 25], [230, 16, 236, 14, "xCenter"], [230, 23, 236, 21], [230, 25, 236, 23], [230, 26, 236, 24, "x"], [230, 27, 236, 25], [230, 30, 236, 28, "blockSize"], [230, 39, 236, 37], [230, 42, 236, 40], [230, 43, 236, 41], [230, 47, 236, 45, "img"], [230, 50, 236, 48], [230, 51, 236, 49, "width"], [230, 56, 236, 54], [231, 16, 237, 14, "yCenter"], [231, 23, 237, 21], [231, 25, 237, 23], [231, 26, 237, 24, "y"], [231, 27, 237, 25], [231, 30, 237, 28, "blockSize"], [231, 39, 237, 37], [231, 42, 237, 40], [231, 43, 237, 41], [231, 47, 237, 45, "img"], [231, 50, 237, 48], [231, 51, 237, 49, "height"], [231, 57, 237, 55], [232, 16, 238, 14, "width"], [232, 21, 238, 19], [232, 23, 238, 21, "blockSize"], [232, 32, 238, 30], [232, 35, 238, 33, "img"], [232, 38, 238, 36], [232, 39, 238, 37, "width"], [232, 44, 238, 42], [233, 16, 239, 14, "height"], [233, 22, 239, 20], [233, 24, 239, 22, "blockSize"], [233, 33, 239, 31], [233, 36, 239, 34, "img"], [233, 39, 239, 37], [233, 40, 239, 38, "height"], [234, 14, 240, 12], [234, 15, 240, 13], [235, 14, 241, 12, "confidence"], [235, 24, 241, 22], [235, 26, 241, 24], [235, 29, 241, 27], [235, 30, 241, 28], [236, 12, 242, 10], [236, 13, 242, 11], [236, 14, 242, 12], [237, 10, 243, 8], [238, 8, 244, 6], [239, 6, 245, 4], [241, 6, 247, 4], [242, 6, 248, 4], [242, 12, 248, 10, "mergedFaces"], [242, 23, 248, 21], [242, 26, 248, 24, "mergeFaceDetections"], [242, 45, 248, 43], [242, 46, 248, 44, "faces"], [242, 51, 248, 49], [242, 52, 248, 50], [243, 6, 249, 4, "console"], [243, 13, 249, 11], [243, 14, 249, 12, "log"], [243, 17, 249, 15], [243, 18, 249, 16], [243, 62, 249, 60, "faces"], [243, 67, 249, 65], [243, 68, 249, 66, "length"], [243, 74, 249, 72], [243, 91, 249, 89, "mergedFaces"], [243, 102, 249, 100], [243, 103, 249, 101, "length"], [243, 109, 249, 107], [243, 124, 249, 122], [243, 125, 249, 123], [244, 6, 250, 4], [244, 13, 250, 11, "mergedFaces"], [244, 24, 250, 22], [244, 25, 250, 23, "slice"], [244, 30, 250, 28], [244, 31, 250, 29], [244, 32, 250, 30], [244, 34, 250, 32], [244, 35, 250, 33], [244, 36, 250, 34], [244, 37, 250, 35], [244, 38, 250, 36], [245, 4, 251, 2], [245, 5, 251, 3], [246, 4, 253, 2], [246, 10, 253, 8, "analyzeRegionForFace"], [246, 30, 253, 28], [246, 33, 253, 31, "analyzeRegionForFace"], [246, 34, 253, 32, "data"], [246, 38, 253, 55], [246, 40, 253, 57, "startX"], [246, 46, 253, 71], [246, 48, 253, 73, "startY"], [246, 54, 253, 87], [246, 56, 253, 89, "size"], [246, 60, 253, 101], [246, 62, 253, 103, "imageWidth"], [246, 72, 253, 121], [246, 74, 253, 123, "imageHeight"], [246, 85, 253, 142], [246, 90, 253, 147], [247, 6, 254, 4], [247, 10, 254, 8, "skinPixels"], [247, 20, 254, 18], [247, 23, 254, 21], [247, 24, 254, 22], [248, 6, 255, 4], [248, 10, 255, 8, "totalPixels"], [248, 21, 255, 19], [248, 24, 255, 22], [248, 25, 255, 23], [249, 6, 256, 4], [249, 10, 256, 8, "totalBrightness"], [249, 25, 256, 23], [249, 28, 256, 26], [249, 29, 256, 27], [250, 6, 257, 4], [250, 10, 257, 8, "colorVariations"], [250, 25, 257, 23], [250, 28, 257, 26], [250, 29, 257, 27], [251, 6, 258, 4], [251, 10, 258, 8, "prevR"], [251, 15, 258, 13], [251, 18, 258, 16], [251, 19, 258, 17], [252, 8, 258, 19, "prevG"], [252, 13, 258, 24], [252, 16, 258, 27], [252, 17, 258, 28], [253, 8, 258, 30, "prevB"], [253, 13, 258, 35], [253, 16, 258, 38], [253, 17, 258, 39], [254, 6, 260, 4], [254, 11, 260, 9], [254, 15, 260, 13, "y"], [254, 16, 260, 14], [254, 19, 260, 17, "startY"], [254, 25, 260, 23], [254, 27, 260, 25, "y"], [254, 28, 260, 26], [254, 31, 260, 29, "startY"], [254, 37, 260, 35], [254, 40, 260, 38, "size"], [254, 44, 260, 42], [254, 48, 260, 46, "y"], [254, 49, 260, 47], [254, 52, 260, 50, "imageHeight"], [254, 63, 260, 61], [254, 65, 260, 63, "y"], [254, 66, 260, 64], [254, 68, 260, 66], [254, 70, 260, 68], [255, 8, 261, 6], [255, 13, 261, 11], [255, 17, 261, 15, "x"], [255, 18, 261, 16], [255, 21, 261, 19, "startX"], [255, 27, 261, 25], [255, 29, 261, 27, "x"], [255, 30, 261, 28], [255, 33, 261, 31, "startX"], [255, 39, 261, 37], [255, 42, 261, 40, "size"], [255, 46, 261, 44], [255, 50, 261, 48, "x"], [255, 51, 261, 49], [255, 54, 261, 52, "imageWidth"], [255, 64, 261, 62], [255, 66, 261, 64, "x"], [255, 67, 261, 65], [255, 69, 261, 67], [255, 71, 261, 69], [256, 10, 262, 8], [256, 16, 262, 14, "index"], [256, 21, 262, 19], [256, 24, 262, 22], [256, 25, 262, 23, "y"], [256, 26, 262, 24], [256, 29, 262, 27, "imageWidth"], [256, 39, 262, 37], [256, 42, 262, 40, "x"], [256, 43, 262, 41], [256, 47, 262, 45], [256, 48, 262, 46], [257, 10, 263, 8], [257, 16, 263, 14, "r"], [257, 17, 263, 15], [257, 20, 263, 18, "data"], [257, 24, 263, 22], [257, 25, 263, 23, "index"], [257, 30, 263, 28], [257, 31, 263, 29], [258, 10, 264, 8], [258, 16, 264, 14, "g"], [258, 17, 264, 15], [258, 20, 264, 18, "data"], [258, 24, 264, 22], [258, 25, 264, 23, "index"], [258, 30, 264, 28], [258, 33, 264, 31], [258, 34, 264, 32], [258, 35, 264, 33], [259, 10, 265, 8], [259, 16, 265, 14, "b"], [259, 17, 265, 15], [259, 20, 265, 18, "data"], [259, 24, 265, 22], [259, 25, 265, 23, "index"], [259, 30, 265, 28], [259, 33, 265, 31], [259, 34, 265, 32], [259, 35, 265, 33], [261, 10, 267, 8], [262, 10, 268, 8], [262, 14, 268, 12, "isSkinTone"], [262, 24, 268, 22], [262, 25, 268, 23, "r"], [262, 26, 268, 24], [262, 28, 268, 26, "g"], [262, 29, 268, 27], [262, 31, 268, 29, "b"], [262, 32, 268, 30], [262, 33, 268, 31], [262, 35, 268, 33], [263, 12, 269, 10, "skinPixels"], [263, 22, 269, 20], [263, 24, 269, 22], [264, 10, 270, 8], [266, 10, 272, 8], [267, 10, 273, 8], [267, 16, 273, 14, "brightness"], [267, 26, 273, 24], [267, 29, 273, 27], [267, 30, 273, 28, "r"], [267, 31, 273, 29], [267, 34, 273, 32, "g"], [267, 35, 273, 33], [267, 38, 273, 36, "b"], [267, 39, 273, 37], [267, 44, 273, 42], [267, 45, 273, 43], [267, 48, 273, 46], [267, 51, 273, 49], [267, 52, 273, 50], [268, 10, 274, 8, "totalBrightness"], [268, 25, 274, 23], [268, 29, 274, 27, "brightness"], [268, 39, 274, 37], [270, 10, 276, 8], [271, 10, 277, 8], [271, 14, 277, 12, "totalPixels"], [271, 25, 277, 23], [271, 28, 277, 26], [271, 29, 277, 27], [271, 31, 277, 29], [272, 12, 278, 10], [272, 18, 278, 16, "colorDiff"], [272, 27, 278, 25], [272, 30, 278, 28, "Math"], [272, 34, 278, 32], [272, 35, 278, 33, "abs"], [272, 38, 278, 36], [272, 39, 278, 37, "r"], [272, 40, 278, 38], [272, 43, 278, 41, "prevR"], [272, 48, 278, 46], [272, 49, 278, 47], [272, 52, 278, 50, "Math"], [272, 56, 278, 54], [272, 57, 278, 55, "abs"], [272, 60, 278, 58], [272, 61, 278, 59, "g"], [272, 62, 278, 60], [272, 65, 278, 63, "prevG"], [272, 70, 278, 68], [272, 71, 278, 69], [272, 74, 278, 72, "Math"], [272, 78, 278, 76], [272, 79, 278, 77, "abs"], [272, 82, 278, 80], [272, 83, 278, 81, "b"], [272, 84, 278, 82], [272, 87, 278, 85, "prevB"], [272, 92, 278, 90], [272, 93, 278, 91], [273, 12, 279, 10], [273, 16, 279, 14, "colorDiff"], [273, 25, 279, 23], [273, 28, 279, 26], [273, 30, 279, 28], [273, 32, 279, 30], [274, 14, 279, 32], [275, 14, 280, 12, "colorVariations"], [275, 29, 280, 27], [275, 31, 280, 29], [276, 12, 281, 10], [277, 10, 282, 8], [278, 10, 284, 8, "prevR"], [278, 15, 284, 13], [278, 18, 284, 16, "r"], [278, 19, 284, 17], [279, 10, 284, 19, "prevG"], [279, 15, 284, 24], [279, 18, 284, 27, "g"], [279, 19, 284, 28], [280, 10, 284, 30, "prevB"], [280, 15, 284, 35], [280, 18, 284, 38, "b"], [280, 19, 284, 39], [281, 10, 285, 8, "totalPixels"], [281, 21, 285, 19], [281, 23, 285, 21], [282, 8, 286, 6], [283, 6, 287, 4], [284, 6, 289, 4], [284, 13, 289, 11], [285, 8, 290, 6, "skinRatio"], [285, 17, 290, 15], [285, 19, 290, 17, "skinPixels"], [285, 29, 290, 27], [285, 32, 290, 30, "totalPixels"], [285, 43, 290, 41], [286, 8, 291, 6, "brightness"], [286, 18, 291, 16], [286, 20, 291, 18, "totalBrightness"], [286, 35, 291, 33], [286, 38, 291, 36, "totalPixels"], [286, 49, 291, 47], [287, 8, 292, 6, "variation"], [287, 17, 292, 15], [287, 19, 292, 17, "colorVariations"], [287, 34, 292, 32], [287, 37, 292, 35, "totalPixels"], [287, 48, 292, 46], [288, 8, 293, 6, "hasVariation"], [288, 20, 293, 18], [288, 22, 293, 20, "colorVariations"], [288, 37, 293, 35], [288, 40, 293, 38, "totalPixels"], [288, 51, 293, 49], [288, 54, 293, 52], [288, 57, 293, 55], [288, 58, 293, 56], [289, 6, 294, 4], [289, 7, 294, 5], [290, 4, 295, 2], [290, 5, 295, 3], [291, 4, 297, 2], [291, 10, 297, 8, "isSkinTone"], [291, 20, 297, 18], [291, 23, 297, 21, "isSkinTone"], [291, 24, 297, 22, "r"], [291, 25, 297, 31], [291, 27, 297, 33, "g"], [291, 28, 297, 42], [291, 30, 297, 44, "b"], [291, 31, 297, 53], [291, 36, 297, 58], [292, 6, 298, 4], [293, 6, 299, 4], [293, 13, 300, 6, "r"], [293, 14, 300, 7], [293, 17, 300, 10], [293, 19, 300, 12], [293, 23, 300, 16, "g"], [293, 24, 300, 17], [293, 27, 300, 20], [293, 29, 300, 22], [293, 33, 300, 26, "b"], [293, 34, 300, 27], [293, 37, 300, 30], [293, 39, 300, 32], [293, 43, 301, 6, "r"], [293, 44, 301, 7], [293, 47, 301, 10, "g"], [293, 48, 301, 11], [293, 52, 301, 15, "r"], [293, 53, 301, 16], [293, 56, 301, 19, "b"], [293, 57, 301, 20], [293, 61, 302, 6, "Math"], [293, 65, 302, 10], [293, 66, 302, 11, "abs"], [293, 69, 302, 14], [293, 70, 302, 15, "r"], [293, 71, 302, 16], [293, 74, 302, 19, "g"], [293, 75, 302, 20], [293, 76, 302, 21], [293, 79, 302, 24], [293, 81, 302, 26], [293, 85, 303, 6, "Math"], [293, 89, 303, 10], [293, 90, 303, 11, "max"], [293, 93, 303, 14], [293, 94, 303, 15, "r"], [293, 95, 303, 16], [293, 97, 303, 18, "g"], [293, 98, 303, 19], [293, 100, 303, 21, "b"], [293, 101, 303, 22], [293, 102, 303, 23], [293, 105, 303, 26, "Math"], [293, 109, 303, 30], [293, 110, 303, 31, "min"], [293, 113, 303, 34], [293, 114, 303, 35, "r"], [293, 115, 303, 36], [293, 117, 303, 38, "g"], [293, 118, 303, 39], [293, 120, 303, 41, "b"], [293, 121, 303, 42], [293, 122, 303, 43], [293, 125, 303, 46], [293, 127, 303, 48], [294, 4, 305, 2], [294, 5, 305, 3], [295, 4, 307, 2], [295, 10, 307, 8, "mergeFaceDetections"], [295, 29, 307, 27], [295, 32, 307, 31, "faces"], [295, 37, 307, 43], [295, 41, 307, 48], [296, 6, 308, 4], [296, 10, 308, 8, "faces"], [296, 15, 308, 13], [296, 16, 308, 14, "length"], [296, 22, 308, 20], [296, 26, 308, 24], [296, 27, 308, 25], [296, 29, 308, 27], [296, 36, 308, 34, "faces"], [296, 41, 308, 39], [297, 6, 310, 4], [297, 12, 310, 10, "merged"], [297, 18, 310, 16], [297, 21, 310, 19], [297, 23, 310, 21], [298, 6, 311, 4], [298, 12, 311, 10, "used"], [298, 16, 311, 14], [298, 19, 311, 17], [298, 23, 311, 21, "Set"], [298, 26, 311, 24], [298, 27, 311, 25], [298, 28, 311, 26], [299, 6, 313, 4], [299, 11, 313, 9], [299, 15, 313, 13, "i"], [299, 16, 313, 14], [299, 19, 313, 17], [299, 20, 313, 18], [299, 22, 313, 20, "i"], [299, 23, 313, 21], [299, 26, 313, 24, "faces"], [299, 31, 313, 29], [299, 32, 313, 30, "length"], [299, 38, 313, 36], [299, 40, 313, 38, "i"], [299, 41, 313, 39], [299, 43, 313, 41], [299, 45, 313, 43], [300, 8, 314, 6], [300, 12, 314, 10, "used"], [300, 16, 314, 14], [300, 17, 314, 15, "has"], [300, 20, 314, 18], [300, 21, 314, 19, "i"], [300, 22, 314, 20], [300, 23, 314, 21], [300, 25, 314, 23], [301, 8, 316, 6], [301, 12, 316, 10, "currentFace"], [301, 23, 316, 21], [301, 26, 316, 24, "faces"], [301, 31, 316, 29], [301, 32, 316, 30, "i"], [301, 33, 316, 31], [301, 34, 316, 32], [302, 8, 317, 6, "used"], [302, 12, 317, 10], [302, 13, 317, 11, "add"], [302, 16, 317, 14], [302, 17, 317, 15, "i"], [302, 18, 317, 16], [302, 19, 317, 17], [304, 8, 319, 6], [305, 8, 320, 6], [305, 13, 320, 11], [305, 17, 320, 15, "j"], [305, 18, 320, 16], [305, 21, 320, 19, "i"], [305, 22, 320, 20], [305, 25, 320, 23], [305, 26, 320, 24], [305, 28, 320, 26, "j"], [305, 29, 320, 27], [305, 32, 320, 30, "faces"], [305, 37, 320, 35], [305, 38, 320, 36, "length"], [305, 44, 320, 42], [305, 46, 320, 44, "j"], [305, 47, 320, 45], [305, 49, 320, 47], [305, 51, 320, 49], [306, 10, 321, 8], [306, 14, 321, 12, "used"], [306, 18, 321, 16], [306, 19, 321, 17, "has"], [306, 22, 321, 20], [306, 23, 321, 21, "j"], [306, 24, 321, 22], [306, 25, 321, 23], [306, 27, 321, 25], [307, 10, 323, 8], [307, 16, 323, 14, "overlap"], [307, 23, 323, 21], [307, 26, 323, 24, "calculateOverlap"], [307, 42, 323, 40], [307, 43, 323, 41, "currentFace"], [307, 54, 323, 52], [307, 55, 323, 53, "boundingBox"], [307, 66, 323, 64], [307, 68, 323, 66, "faces"], [307, 73, 323, 71], [307, 74, 323, 72, "j"], [307, 75, 323, 73], [307, 76, 323, 74], [307, 77, 323, 75, "boundingBox"], [307, 88, 323, 86], [307, 89, 323, 87], [308, 10, 324, 8], [308, 14, 324, 12, "overlap"], [308, 21, 324, 19], [308, 24, 324, 22], [308, 27, 324, 25], [308, 29, 324, 27], [309, 12, 324, 29], [310, 12, 325, 10], [311, 12, 326, 10, "currentFace"], [311, 23, 326, 21], [311, 26, 326, 24, "mergeTwoFaces"], [311, 39, 326, 37], [311, 40, 326, 38, "currentFace"], [311, 51, 326, 49], [311, 53, 326, 51, "faces"], [311, 58, 326, 56], [311, 59, 326, 57, "j"], [311, 60, 326, 58], [311, 61, 326, 59], [311, 62, 326, 60], [312, 12, 327, 10, "used"], [312, 16, 327, 14], [312, 17, 327, 15, "add"], [312, 20, 327, 18], [312, 21, 327, 19, "j"], [312, 22, 327, 20], [312, 23, 327, 21], [313, 10, 328, 8], [314, 8, 329, 6], [315, 8, 331, 6, "merged"], [315, 14, 331, 12], [315, 15, 331, 13, "push"], [315, 19, 331, 17], [315, 20, 331, 18, "currentFace"], [315, 31, 331, 29], [315, 32, 331, 30], [316, 6, 332, 4], [317, 6, 334, 4], [317, 13, 334, 11, "merged"], [317, 19, 334, 17], [318, 4, 335, 2], [318, 5, 335, 3], [319, 4, 337, 2], [319, 10, 337, 8, "calculateOverlap"], [319, 26, 337, 24], [319, 29, 337, 27, "calculateOverlap"], [319, 30, 337, 28, "box1"], [319, 34, 337, 37], [319, 36, 337, 39, "box2"], [319, 40, 337, 48], [319, 45, 337, 53], [320, 6, 338, 4], [320, 12, 338, 10, "x1"], [320, 14, 338, 12], [320, 17, 338, 15, "Math"], [320, 21, 338, 19], [320, 22, 338, 20, "max"], [320, 25, 338, 23], [320, 26, 338, 24, "box1"], [320, 30, 338, 28], [320, 31, 338, 29, "xCenter"], [320, 38, 338, 36], [320, 41, 338, 39, "box1"], [320, 45, 338, 43], [320, 46, 338, 44, "width"], [320, 51, 338, 49], [320, 54, 338, 50], [320, 55, 338, 51], [320, 57, 338, 53, "box2"], [320, 61, 338, 57], [320, 62, 338, 58, "xCenter"], [320, 69, 338, 65], [320, 72, 338, 68, "box2"], [320, 76, 338, 72], [320, 77, 338, 73, "width"], [320, 82, 338, 78], [320, 85, 338, 79], [320, 86, 338, 80], [320, 87, 338, 81], [321, 6, 339, 4], [321, 12, 339, 10, "y1"], [321, 14, 339, 12], [321, 17, 339, 15, "Math"], [321, 21, 339, 19], [321, 22, 339, 20, "max"], [321, 25, 339, 23], [321, 26, 339, 24, "box1"], [321, 30, 339, 28], [321, 31, 339, 29, "yCenter"], [321, 38, 339, 36], [321, 41, 339, 39, "box1"], [321, 45, 339, 43], [321, 46, 339, 44, "height"], [321, 52, 339, 50], [321, 55, 339, 51], [321, 56, 339, 52], [321, 58, 339, 54, "box2"], [321, 62, 339, 58], [321, 63, 339, 59, "yCenter"], [321, 70, 339, 66], [321, 73, 339, 69, "box2"], [321, 77, 339, 73], [321, 78, 339, 74, "height"], [321, 84, 339, 80], [321, 87, 339, 81], [321, 88, 339, 82], [321, 89, 339, 83], [322, 6, 340, 4], [322, 12, 340, 10, "x2"], [322, 14, 340, 12], [322, 17, 340, 15, "Math"], [322, 21, 340, 19], [322, 22, 340, 20, "min"], [322, 25, 340, 23], [322, 26, 340, 24, "box1"], [322, 30, 340, 28], [322, 31, 340, 29, "xCenter"], [322, 38, 340, 36], [322, 41, 340, 39, "box1"], [322, 45, 340, 43], [322, 46, 340, 44, "width"], [322, 51, 340, 49], [322, 54, 340, 50], [322, 55, 340, 51], [322, 57, 340, 53, "box2"], [322, 61, 340, 57], [322, 62, 340, 58, "xCenter"], [322, 69, 340, 65], [322, 72, 340, 68, "box2"], [322, 76, 340, 72], [322, 77, 340, 73, "width"], [322, 82, 340, 78], [322, 85, 340, 79], [322, 86, 340, 80], [322, 87, 340, 81], [323, 6, 341, 4], [323, 12, 341, 10, "y2"], [323, 14, 341, 12], [323, 17, 341, 15, "Math"], [323, 21, 341, 19], [323, 22, 341, 20, "min"], [323, 25, 341, 23], [323, 26, 341, 24, "box1"], [323, 30, 341, 28], [323, 31, 341, 29, "yCenter"], [323, 38, 341, 36], [323, 41, 341, 39, "box1"], [323, 45, 341, 43], [323, 46, 341, 44, "height"], [323, 52, 341, 50], [323, 55, 341, 51], [323, 56, 341, 52], [323, 58, 341, 54, "box2"], [323, 62, 341, 58], [323, 63, 341, 59, "yCenter"], [323, 70, 341, 66], [323, 73, 341, 69, "box2"], [323, 77, 341, 73], [323, 78, 341, 74, "height"], [323, 84, 341, 80], [323, 87, 341, 81], [323, 88, 341, 82], [323, 89, 341, 83], [324, 6, 343, 4], [324, 10, 343, 8, "x2"], [324, 12, 343, 10], [324, 16, 343, 14, "x1"], [324, 18, 343, 16], [324, 22, 343, 20, "y2"], [324, 24, 343, 22], [324, 28, 343, 26, "y1"], [324, 30, 343, 28], [324, 32, 343, 30], [324, 39, 343, 37], [324, 40, 343, 38], [325, 6, 345, 4], [325, 12, 345, 10, "overlapArea"], [325, 23, 345, 21], [325, 26, 345, 24], [325, 27, 345, 25, "x2"], [325, 29, 345, 27], [325, 32, 345, 30, "x1"], [325, 34, 345, 32], [325, 39, 345, 37, "y2"], [325, 41, 345, 39], [325, 44, 345, 42, "y1"], [325, 46, 345, 44], [325, 47, 345, 45], [326, 6, 346, 4], [326, 12, 346, 10, "box1Area"], [326, 20, 346, 18], [326, 23, 346, 21, "box1"], [326, 27, 346, 25], [326, 28, 346, 26, "width"], [326, 33, 346, 31], [326, 36, 346, 34, "box1"], [326, 40, 346, 38], [326, 41, 346, 39, "height"], [326, 47, 346, 45], [327, 6, 347, 4], [327, 12, 347, 10, "box2Area"], [327, 20, 347, 18], [327, 23, 347, 21, "box2"], [327, 27, 347, 25], [327, 28, 347, 26, "width"], [327, 33, 347, 31], [327, 36, 347, 34, "box2"], [327, 40, 347, 38], [327, 41, 347, 39, "height"], [327, 47, 347, 45], [328, 6, 349, 4], [328, 13, 349, 11, "overlapArea"], [328, 24, 349, 22], [328, 27, 349, 25, "Math"], [328, 31, 349, 29], [328, 32, 349, 30, "min"], [328, 35, 349, 33], [328, 36, 349, 34, "box1Area"], [328, 44, 349, 42], [328, 46, 349, 44, "box2Area"], [328, 54, 349, 52], [328, 55, 349, 53], [329, 4, 350, 2], [329, 5, 350, 3], [330, 4, 352, 2], [330, 10, 352, 8, "mergeTwoFaces"], [330, 23, 352, 21], [330, 26, 352, 24, "mergeTwoFaces"], [330, 27, 352, 25, "face1"], [330, 32, 352, 35], [330, 34, 352, 37, "face2"], [330, 39, 352, 47], [330, 44, 352, 52], [331, 6, 353, 4], [331, 12, 353, 10, "box1"], [331, 16, 353, 14], [331, 19, 353, 17, "face1"], [331, 24, 353, 22], [331, 25, 353, 23, "boundingBox"], [331, 36, 353, 34], [332, 6, 354, 4], [332, 12, 354, 10, "box2"], [332, 16, 354, 14], [332, 19, 354, 17, "face2"], [332, 24, 354, 22], [332, 25, 354, 23, "boundingBox"], [332, 36, 354, 34], [333, 6, 356, 4], [333, 12, 356, 10, "left"], [333, 16, 356, 14], [333, 19, 356, 17, "Math"], [333, 23, 356, 21], [333, 24, 356, 22, "min"], [333, 27, 356, 25], [333, 28, 356, 26, "box1"], [333, 32, 356, 30], [333, 33, 356, 31, "xCenter"], [333, 40, 356, 38], [333, 43, 356, 41, "box1"], [333, 47, 356, 45], [333, 48, 356, 46, "width"], [333, 53, 356, 51], [333, 56, 356, 52], [333, 57, 356, 53], [333, 59, 356, 55, "box2"], [333, 63, 356, 59], [333, 64, 356, 60, "xCenter"], [333, 71, 356, 67], [333, 74, 356, 70, "box2"], [333, 78, 356, 74], [333, 79, 356, 75, "width"], [333, 84, 356, 80], [333, 87, 356, 81], [333, 88, 356, 82], [333, 89, 356, 83], [334, 6, 357, 4], [334, 12, 357, 10, "right"], [334, 17, 357, 15], [334, 20, 357, 18, "Math"], [334, 24, 357, 22], [334, 25, 357, 23, "max"], [334, 28, 357, 26], [334, 29, 357, 27, "box1"], [334, 33, 357, 31], [334, 34, 357, 32, "xCenter"], [334, 41, 357, 39], [334, 44, 357, 42, "box1"], [334, 48, 357, 46], [334, 49, 357, 47, "width"], [334, 54, 357, 52], [334, 57, 357, 53], [334, 58, 357, 54], [334, 60, 357, 56, "box2"], [334, 64, 357, 60], [334, 65, 357, 61, "xCenter"], [334, 72, 357, 68], [334, 75, 357, 71, "box2"], [334, 79, 357, 75], [334, 80, 357, 76, "width"], [334, 85, 357, 81], [334, 88, 357, 82], [334, 89, 357, 83], [334, 90, 357, 84], [335, 6, 358, 4], [335, 12, 358, 10, "top"], [335, 15, 358, 13], [335, 18, 358, 16, "Math"], [335, 22, 358, 20], [335, 23, 358, 21, "min"], [335, 26, 358, 24], [335, 27, 358, 25, "box1"], [335, 31, 358, 29], [335, 32, 358, 30, "yCenter"], [335, 39, 358, 37], [335, 42, 358, 40, "box1"], [335, 46, 358, 44], [335, 47, 358, 45, "height"], [335, 53, 358, 51], [335, 56, 358, 52], [335, 57, 358, 53], [335, 59, 358, 55, "box2"], [335, 63, 358, 59], [335, 64, 358, 60, "yCenter"], [335, 71, 358, 67], [335, 74, 358, 70, "box2"], [335, 78, 358, 74], [335, 79, 358, 75, "height"], [335, 85, 358, 81], [335, 88, 358, 82], [335, 89, 358, 83], [335, 90, 358, 84], [336, 6, 359, 4], [336, 12, 359, 10, "bottom"], [336, 18, 359, 16], [336, 21, 359, 19, "Math"], [336, 25, 359, 23], [336, 26, 359, 24, "max"], [336, 29, 359, 27], [336, 30, 359, 28, "box1"], [336, 34, 359, 32], [336, 35, 359, 33, "yCenter"], [336, 42, 359, 40], [336, 45, 359, 43, "box1"], [336, 49, 359, 47], [336, 50, 359, 48, "height"], [336, 56, 359, 54], [336, 59, 359, 55], [336, 60, 359, 56], [336, 62, 359, 58, "box2"], [336, 66, 359, 62], [336, 67, 359, 63, "yCenter"], [336, 74, 359, 70], [336, 77, 359, 73, "box2"], [336, 81, 359, 77], [336, 82, 359, 78, "height"], [336, 88, 359, 84], [336, 91, 359, 85], [336, 92, 359, 86], [336, 93, 359, 87], [337, 6, 361, 4], [337, 13, 361, 11], [338, 8, 362, 6, "boundingBox"], [338, 19, 362, 17], [338, 21, 362, 19], [339, 10, 363, 8, "xCenter"], [339, 17, 363, 15], [339, 19, 363, 17], [339, 20, 363, 18, "left"], [339, 24, 363, 22], [339, 27, 363, 25, "right"], [339, 32, 363, 30], [339, 36, 363, 34], [339, 37, 363, 35], [340, 10, 364, 8, "yCenter"], [340, 17, 364, 15], [340, 19, 364, 17], [340, 20, 364, 18, "top"], [340, 23, 364, 21], [340, 26, 364, 24, "bottom"], [340, 32, 364, 30], [340, 36, 364, 34], [340, 37, 364, 35], [341, 10, 365, 8, "width"], [341, 15, 365, 13], [341, 17, 365, 15, "right"], [341, 22, 365, 20], [341, 25, 365, 23, "left"], [341, 29, 365, 27], [342, 10, 366, 8, "height"], [342, 16, 366, 14], [342, 18, 366, 16, "bottom"], [342, 24, 366, 22], [342, 27, 366, 25, "top"], [343, 8, 367, 6], [344, 6, 368, 4], [344, 7, 368, 5], [345, 4, 369, 2], [345, 5, 369, 3], [347, 4, 371, 2], [348, 4, 372, 2], [348, 10, 372, 8, "applyStrongBlur"], [348, 25, 372, 23], [348, 28, 372, 26, "applyStrongBlur"], [348, 29, 372, 27, "ctx"], [348, 32, 372, 56], [348, 34, 372, 58, "x"], [348, 35, 372, 67], [348, 37, 372, 69, "y"], [348, 38, 372, 78], [348, 40, 372, 80, "width"], [348, 45, 372, 93], [348, 47, 372, 95, "height"], [348, 53, 372, 109], [348, 58, 372, 114], [349, 6, 373, 4], [350, 6, 374, 4], [350, 12, 374, 10, "canvasWidth"], [350, 23, 374, 21], [350, 26, 374, 24, "ctx"], [350, 29, 374, 27], [350, 30, 374, 28, "canvas"], [350, 36, 374, 34], [350, 37, 374, 35, "width"], [350, 42, 374, 40], [351, 6, 375, 4], [351, 12, 375, 10, "canvasHeight"], [351, 24, 375, 22], [351, 27, 375, 25, "ctx"], [351, 30, 375, 28], [351, 31, 375, 29, "canvas"], [351, 37, 375, 35], [351, 38, 375, 36, "height"], [351, 44, 375, 42], [352, 6, 377, 4], [352, 12, 377, 10, "clampedX"], [352, 20, 377, 18], [352, 23, 377, 21, "Math"], [352, 27, 377, 25], [352, 28, 377, 26, "max"], [352, 31, 377, 29], [352, 32, 377, 30], [352, 33, 377, 31], [352, 35, 377, 33, "Math"], [352, 39, 377, 37], [352, 40, 377, 38, "min"], [352, 43, 377, 41], [352, 44, 377, 42, "Math"], [352, 48, 377, 46], [352, 49, 377, 47, "floor"], [352, 54, 377, 52], [352, 55, 377, 53, "x"], [352, 56, 377, 54], [352, 57, 377, 55], [352, 59, 377, 57, "canvasWidth"], [352, 70, 377, 68], [352, 73, 377, 71], [352, 74, 377, 72], [352, 75, 377, 73], [352, 76, 377, 74], [353, 6, 378, 4], [353, 12, 378, 10, "clampedY"], [353, 20, 378, 18], [353, 23, 378, 21, "Math"], [353, 27, 378, 25], [353, 28, 378, 26, "max"], [353, 31, 378, 29], [353, 32, 378, 30], [353, 33, 378, 31], [353, 35, 378, 33, "Math"], [353, 39, 378, 37], [353, 40, 378, 38, "min"], [353, 43, 378, 41], [353, 44, 378, 42, "Math"], [353, 48, 378, 46], [353, 49, 378, 47, "floor"], [353, 54, 378, 52], [353, 55, 378, 53, "y"], [353, 56, 378, 54], [353, 57, 378, 55], [353, 59, 378, 57, "canvasHeight"], [353, 71, 378, 69], [353, 74, 378, 72], [353, 75, 378, 73], [353, 76, 378, 74], [353, 77, 378, 75], [354, 6, 379, 4], [354, 12, 379, 10, "<PERSON><PERSON><PERSON><PERSON>"], [354, 24, 379, 22], [354, 27, 379, 25, "Math"], [354, 31, 379, 29], [354, 32, 379, 30, "min"], [354, 35, 379, 33], [354, 36, 379, 34, "Math"], [354, 40, 379, 38], [354, 41, 379, 39, "floor"], [354, 46, 379, 44], [354, 47, 379, 45, "width"], [354, 52, 379, 50], [354, 53, 379, 51], [354, 55, 379, 53, "canvasWidth"], [354, 66, 379, 64], [354, 69, 379, 67, "clampedX"], [354, 77, 379, 75], [354, 78, 379, 76], [355, 6, 380, 4], [355, 12, 380, 10, "clampedHeight"], [355, 25, 380, 23], [355, 28, 380, 26, "Math"], [355, 32, 380, 30], [355, 33, 380, 31, "min"], [355, 36, 380, 34], [355, 37, 380, 35, "Math"], [355, 41, 380, 39], [355, 42, 380, 40, "floor"], [355, 47, 380, 45], [355, 48, 380, 46, "height"], [355, 54, 380, 52], [355, 55, 380, 53], [355, 57, 380, 55, "canvasHeight"], [355, 69, 380, 67], [355, 72, 380, 70, "clampedY"], [355, 80, 380, 78], [355, 81, 380, 79], [356, 6, 382, 4], [356, 10, 382, 8, "<PERSON><PERSON><PERSON><PERSON>"], [356, 22, 382, 20], [356, 26, 382, 24], [356, 27, 382, 25], [356, 31, 382, 29, "clampedHeight"], [356, 44, 382, 42], [356, 48, 382, 46], [356, 49, 382, 47], [356, 51, 382, 49], [357, 8, 383, 6, "console"], [357, 15, 383, 13], [357, 16, 383, 14, "warn"], [357, 20, 383, 18], [357, 21, 383, 19], [357, 73, 383, 71], [357, 75, 383, 73], [358, 10, 384, 8, "original"], [358, 18, 384, 16], [358, 20, 384, 18], [359, 12, 384, 20, "x"], [359, 13, 384, 21], [360, 12, 384, 23, "y"], [360, 13, 384, 24], [361, 12, 384, 26, "width"], [361, 17, 384, 31], [362, 12, 384, 33, "height"], [363, 10, 384, 40], [363, 11, 384, 41], [364, 10, 385, 8, "canvas"], [364, 16, 385, 14], [364, 18, 385, 16], [365, 12, 385, 18, "width"], [365, 17, 385, 23], [365, 19, 385, 25, "canvasWidth"], [365, 30, 385, 36], [366, 12, 385, 38, "height"], [366, 18, 385, 44], [366, 20, 385, 46, "canvasHeight"], [367, 10, 385, 59], [367, 11, 385, 60], [368, 10, 386, 8, "clamped"], [368, 17, 386, 15], [368, 19, 386, 17], [369, 12, 386, 19, "x"], [369, 13, 386, 20], [369, 15, 386, 22, "clampedX"], [369, 23, 386, 30], [370, 12, 386, 32, "y"], [370, 13, 386, 33], [370, 15, 386, 35, "clampedY"], [370, 23, 386, 43], [371, 12, 386, 45, "width"], [371, 17, 386, 50], [371, 19, 386, 52, "<PERSON><PERSON><PERSON><PERSON>"], [371, 31, 386, 64], [372, 12, 386, 66, "height"], [372, 18, 386, 72], [372, 20, 386, 74, "clampedHeight"], [373, 10, 386, 88], [374, 8, 387, 6], [374, 9, 387, 7], [374, 10, 387, 8], [375, 8, 388, 6], [376, 6, 389, 4], [378, 6, 391, 4], [379, 6, 392, 4], [379, 12, 392, 10, "imageData"], [379, 21, 392, 19], [379, 24, 392, 22, "ctx"], [379, 27, 392, 25], [379, 28, 392, 26, "getImageData"], [379, 40, 392, 38], [379, 41, 392, 39, "clampedX"], [379, 49, 392, 47], [379, 51, 392, 49, "clampedY"], [379, 59, 392, 57], [379, 61, 392, 59, "<PERSON><PERSON><PERSON><PERSON>"], [379, 73, 392, 71], [379, 75, 392, 73, "clampedHeight"], [379, 88, 392, 86], [379, 89, 392, 87], [380, 6, 393, 4], [380, 12, 393, 10, "data"], [380, 16, 393, 14], [380, 19, 393, 17, "imageData"], [380, 28, 393, 26], [380, 29, 393, 27, "data"], [380, 33, 393, 31], [382, 6, 395, 4], [383, 6, 396, 4], [383, 12, 396, 10, "pixelSize"], [383, 21, 396, 19], [383, 24, 396, 22, "Math"], [383, 28, 396, 26], [383, 29, 396, 27, "max"], [383, 32, 396, 30], [383, 33, 396, 31], [383, 35, 396, 33], [383, 37, 396, 35, "Math"], [383, 41, 396, 39], [383, 42, 396, 40, "min"], [383, 45, 396, 43], [383, 46, 396, 44, "<PERSON><PERSON><PERSON><PERSON>"], [383, 58, 396, 56], [383, 60, 396, 58, "clampedHeight"], [383, 73, 396, 71], [383, 74, 396, 72], [383, 77, 396, 75], [383, 78, 396, 76], [383, 79, 396, 77], [384, 6, 398, 4], [384, 11, 398, 9], [384, 15, 398, 13, "py"], [384, 17, 398, 15], [384, 20, 398, 18], [384, 21, 398, 19], [384, 23, 398, 21, "py"], [384, 25, 398, 23], [384, 28, 398, 26, "clampedHeight"], [384, 41, 398, 39], [384, 43, 398, 41, "py"], [384, 45, 398, 43], [384, 49, 398, 47, "pixelSize"], [384, 58, 398, 56], [384, 60, 398, 58], [385, 8, 399, 6], [385, 13, 399, 11], [385, 17, 399, 15, "px"], [385, 19, 399, 17], [385, 22, 399, 20], [385, 23, 399, 21], [385, 25, 399, 23, "px"], [385, 27, 399, 25], [385, 30, 399, 28, "<PERSON><PERSON><PERSON><PERSON>"], [385, 42, 399, 40], [385, 44, 399, 42, "px"], [385, 46, 399, 44], [385, 50, 399, 48, "pixelSize"], [385, 59, 399, 57], [385, 61, 399, 59], [386, 10, 400, 8], [387, 10, 401, 8], [387, 14, 401, 12, "r"], [387, 15, 401, 13], [387, 18, 401, 16], [387, 19, 401, 17], [388, 12, 401, 19, "g"], [388, 13, 401, 20], [388, 16, 401, 23], [388, 17, 401, 24], [389, 12, 401, 26, "b"], [389, 13, 401, 27], [389, 16, 401, 30], [389, 17, 401, 31], [390, 12, 401, 33, "count"], [390, 17, 401, 38], [390, 20, 401, 41], [390, 21, 401, 42], [391, 10, 403, 8], [391, 15, 403, 13], [391, 19, 403, 17, "dy"], [391, 21, 403, 19], [391, 24, 403, 22], [391, 25, 403, 23], [391, 27, 403, 25, "dy"], [391, 29, 403, 27], [391, 32, 403, 30, "pixelSize"], [391, 41, 403, 39], [391, 45, 403, 43, "py"], [391, 47, 403, 45], [391, 50, 403, 48, "dy"], [391, 52, 403, 50], [391, 55, 403, 53, "clampedHeight"], [391, 68, 403, 66], [391, 70, 403, 68, "dy"], [391, 72, 403, 70], [391, 74, 403, 72], [391, 76, 403, 74], [392, 12, 404, 10], [392, 17, 404, 15], [392, 21, 404, 19, "dx"], [392, 23, 404, 21], [392, 26, 404, 24], [392, 27, 404, 25], [392, 29, 404, 27, "dx"], [392, 31, 404, 29], [392, 34, 404, 32, "pixelSize"], [392, 43, 404, 41], [392, 47, 404, 45, "px"], [392, 49, 404, 47], [392, 52, 404, 50, "dx"], [392, 54, 404, 52], [392, 57, 404, 55, "<PERSON><PERSON><PERSON><PERSON>"], [392, 69, 404, 67], [392, 71, 404, 69, "dx"], [392, 73, 404, 71], [392, 75, 404, 73], [392, 77, 404, 75], [393, 14, 405, 12], [393, 20, 405, 18, "index"], [393, 25, 405, 23], [393, 28, 405, 26], [393, 29, 405, 27], [393, 30, 405, 28, "py"], [393, 32, 405, 30], [393, 35, 405, 33, "dy"], [393, 37, 405, 35], [393, 41, 405, 39, "<PERSON><PERSON><PERSON><PERSON>"], [393, 53, 405, 51], [393, 57, 405, 55, "px"], [393, 59, 405, 57], [393, 62, 405, 60, "dx"], [393, 64, 405, 62], [393, 65, 405, 63], [393, 69, 405, 67], [393, 70, 405, 68], [394, 14, 406, 12, "r"], [394, 15, 406, 13], [394, 19, 406, 17, "data"], [394, 23, 406, 21], [394, 24, 406, 22, "index"], [394, 29, 406, 27], [394, 30, 406, 28], [395, 14, 407, 12, "g"], [395, 15, 407, 13], [395, 19, 407, 17, "data"], [395, 23, 407, 21], [395, 24, 407, 22, "index"], [395, 29, 407, 27], [395, 32, 407, 30], [395, 33, 407, 31], [395, 34, 407, 32], [396, 14, 408, 12, "b"], [396, 15, 408, 13], [396, 19, 408, 17, "data"], [396, 23, 408, 21], [396, 24, 408, 22, "index"], [396, 29, 408, 27], [396, 32, 408, 30], [396, 33, 408, 31], [396, 34, 408, 32], [397, 14, 409, 12, "count"], [397, 19, 409, 17], [397, 21, 409, 19], [398, 12, 410, 10], [399, 10, 411, 8], [400, 10, 413, 8], [400, 14, 413, 12, "count"], [400, 19, 413, 17], [400, 22, 413, 20], [400, 23, 413, 21], [400, 25, 413, 23], [401, 12, 414, 10, "r"], [401, 13, 414, 11], [401, 16, 414, 14, "Math"], [401, 20, 414, 18], [401, 21, 414, 19, "floor"], [401, 26, 414, 24], [401, 27, 414, 25, "r"], [401, 28, 414, 26], [401, 31, 414, 29, "count"], [401, 36, 414, 34], [401, 37, 414, 35], [402, 12, 415, 10, "g"], [402, 13, 415, 11], [402, 16, 415, 14, "Math"], [402, 20, 415, 18], [402, 21, 415, 19, "floor"], [402, 26, 415, 24], [402, 27, 415, 25, "g"], [402, 28, 415, 26], [402, 31, 415, 29, "count"], [402, 36, 415, 34], [402, 37, 415, 35], [403, 12, 416, 10, "b"], [403, 13, 416, 11], [403, 16, 416, 14, "Math"], [403, 20, 416, 18], [403, 21, 416, 19, "floor"], [403, 26, 416, 24], [403, 27, 416, 25, "b"], [403, 28, 416, 26], [403, 31, 416, 29, "count"], [403, 36, 416, 34], [403, 37, 416, 35], [405, 12, 418, 10], [406, 12, 419, 10], [406, 17, 419, 15], [406, 21, 419, 19, "dy"], [406, 23, 419, 21], [406, 26, 419, 24], [406, 27, 419, 25], [406, 29, 419, 27, "dy"], [406, 31, 419, 29], [406, 34, 419, 32, "pixelSize"], [406, 43, 419, 41], [406, 47, 419, 45, "py"], [406, 49, 419, 47], [406, 52, 419, 50, "dy"], [406, 54, 419, 52], [406, 57, 419, 55, "clampedHeight"], [406, 70, 419, 68], [406, 72, 419, 70, "dy"], [406, 74, 419, 72], [406, 76, 419, 74], [406, 78, 419, 76], [407, 14, 420, 12], [407, 19, 420, 17], [407, 23, 420, 21, "dx"], [407, 25, 420, 23], [407, 28, 420, 26], [407, 29, 420, 27], [407, 31, 420, 29, "dx"], [407, 33, 420, 31], [407, 36, 420, 34, "pixelSize"], [407, 45, 420, 43], [407, 49, 420, 47, "px"], [407, 51, 420, 49], [407, 54, 420, 52, "dx"], [407, 56, 420, 54], [407, 59, 420, 57, "<PERSON><PERSON><PERSON><PERSON>"], [407, 71, 420, 69], [407, 73, 420, 71, "dx"], [407, 75, 420, 73], [407, 77, 420, 75], [407, 79, 420, 77], [408, 16, 421, 14], [408, 22, 421, 20, "index"], [408, 27, 421, 25], [408, 30, 421, 28], [408, 31, 421, 29], [408, 32, 421, 30, "py"], [408, 34, 421, 32], [408, 37, 421, 35, "dy"], [408, 39, 421, 37], [408, 43, 421, 41, "<PERSON><PERSON><PERSON><PERSON>"], [408, 55, 421, 53], [408, 59, 421, 57, "px"], [408, 61, 421, 59], [408, 64, 421, 62, "dx"], [408, 66, 421, 64], [408, 67, 421, 65], [408, 71, 421, 69], [408, 72, 421, 70], [409, 16, 422, 14, "data"], [409, 20, 422, 18], [409, 21, 422, 19, "index"], [409, 26, 422, 24], [409, 27, 422, 25], [409, 30, 422, 28, "r"], [409, 31, 422, 29], [410, 16, 423, 14, "data"], [410, 20, 423, 18], [410, 21, 423, 19, "index"], [410, 26, 423, 24], [410, 29, 423, 27], [410, 30, 423, 28], [410, 31, 423, 29], [410, 34, 423, 32, "g"], [410, 35, 423, 33], [411, 16, 424, 14, "data"], [411, 20, 424, 18], [411, 21, 424, 19, "index"], [411, 26, 424, 24], [411, 29, 424, 27], [411, 30, 424, 28], [411, 31, 424, 29], [411, 34, 424, 32, "b"], [411, 35, 424, 33], [412, 16, 425, 14], [413, 14, 426, 12], [414, 12, 427, 10], [415, 10, 428, 8], [416, 8, 429, 6], [417, 6, 430, 4], [419, 6, 432, 4], [420, 6, 433, 4], [420, 11, 433, 9], [420, 15, 433, 13, "i"], [420, 16, 433, 14], [420, 19, 433, 17], [420, 20, 433, 18], [420, 22, 433, 20, "i"], [420, 23, 433, 21], [420, 26, 433, 24], [420, 27, 433, 25], [420, 29, 433, 27, "i"], [420, 30, 433, 28], [420, 32, 433, 30], [420, 34, 433, 32], [421, 8, 434, 6, "applySimpleBlur"], [421, 23, 434, 21], [421, 24, 434, 22, "data"], [421, 28, 434, 26], [421, 30, 434, 28, "<PERSON><PERSON><PERSON><PERSON>"], [421, 42, 434, 40], [421, 44, 434, 42, "clampedHeight"], [421, 57, 434, 55], [421, 58, 434, 56], [422, 6, 435, 4], [424, 6, 437, 4], [425, 6, 438, 4, "ctx"], [425, 9, 438, 7], [425, 10, 438, 8, "putImageData"], [425, 22, 438, 20], [425, 23, 438, 21, "imageData"], [425, 32, 438, 30], [425, 34, 438, 32, "clampedX"], [425, 42, 438, 40], [425, 44, 438, 42, "clampedY"], [425, 52, 438, 50], [425, 53, 438, 51], [427, 6, 440, 4], [428, 6, 441, 4, "ctx"], [428, 9, 441, 7], [428, 10, 441, 8, "fillStyle"], [428, 19, 441, 17], [428, 22, 441, 20], [428, 48, 441, 46], [429, 6, 442, 4, "ctx"], [429, 9, 442, 7], [429, 10, 442, 8, "fillRect"], [429, 18, 442, 16], [429, 19, 442, 17, "clampedX"], [429, 27, 442, 25], [429, 29, 442, 27, "clampedY"], [429, 37, 442, 35], [429, 39, 442, 37, "<PERSON><PERSON><PERSON><PERSON>"], [429, 51, 442, 49], [429, 53, 442, 51, "clampedHeight"], [429, 66, 442, 64], [429, 67, 442, 65], [430, 4, 443, 2], [430, 5, 443, 3], [431, 4, 445, 2], [431, 10, 445, 8, "applySimpleBlur"], [431, 25, 445, 23], [431, 28, 445, 26, "applySimpleBlur"], [431, 29, 445, 27, "data"], [431, 33, 445, 50], [431, 35, 445, 52, "width"], [431, 40, 445, 65], [431, 42, 445, 67, "height"], [431, 48, 445, 81], [431, 53, 445, 86], [432, 6, 446, 4], [432, 12, 446, 10, "original"], [432, 20, 446, 18], [432, 23, 446, 21], [432, 27, 446, 25, "Uint8ClampedArray"], [432, 44, 446, 42], [432, 45, 446, 43, "data"], [432, 49, 446, 47], [432, 50, 446, 48], [433, 6, 448, 4], [433, 11, 448, 9], [433, 15, 448, 13, "y"], [433, 16, 448, 14], [433, 19, 448, 17], [433, 20, 448, 18], [433, 22, 448, 20, "y"], [433, 23, 448, 21], [433, 26, 448, 24, "height"], [433, 32, 448, 30], [433, 35, 448, 33], [433, 36, 448, 34], [433, 38, 448, 36, "y"], [433, 39, 448, 37], [433, 41, 448, 39], [433, 43, 448, 41], [434, 8, 449, 6], [434, 13, 449, 11], [434, 17, 449, 15, "x"], [434, 18, 449, 16], [434, 21, 449, 19], [434, 22, 449, 20], [434, 24, 449, 22, "x"], [434, 25, 449, 23], [434, 28, 449, 26, "width"], [434, 33, 449, 31], [434, 36, 449, 34], [434, 37, 449, 35], [434, 39, 449, 37, "x"], [434, 40, 449, 38], [434, 42, 449, 40], [434, 44, 449, 42], [435, 10, 450, 8], [435, 16, 450, 14, "index"], [435, 21, 450, 19], [435, 24, 450, 22], [435, 25, 450, 23, "y"], [435, 26, 450, 24], [435, 29, 450, 27, "width"], [435, 34, 450, 32], [435, 37, 450, 35, "x"], [435, 38, 450, 36], [435, 42, 450, 40], [435, 43, 450, 41], [437, 10, 452, 8], [438, 10, 453, 8], [438, 14, 453, 12, "r"], [438, 15, 453, 13], [438, 18, 453, 16], [438, 19, 453, 17], [439, 12, 453, 19, "g"], [439, 13, 453, 20], [439, 16, 453, 23], [439, 17, 453, 24], [440, 12, 453, 26, "b"], [440, 13, 453, 27], [440, 16, 453, 30], [440, 17, 453, 31], [441, 10, 454, 8], [441, 15, 454, 13], [441, 19, 454, 17, "dy"], [441, 21, 454, 19], [441, 24, 454, 22], [441, 25, 454, 23], [441, 26, 454, 24], [441, 28, 454, 26, "dy"], [441, 30, 454, 28], [441, 34, 454, 32], [441, 35, 454, 33], [441, 37, 454, 35, "dy"], [441, 39, 454, 37], [441, 41, 454, 39], [441, 43, 454, 41], [442, 12, 455, 10], [442, 17, 455, 15], [442, 21, 455, 19, "dx"], [442, 23, 455, 21], [442, 26, 455, 24], [442, 27, 455, 25], [442, 28, 455, 26], [442, 30, 455, 28, "dx"], [442, 32, 455, 30], [442, 36, 455, 34], [442, 37, 455, 35], [442, 39, 455, 37, "dx"], [442, 41, 455, 39], [442, 43, 455, 41], [442, 45, 455, 43], [443, 14, 456, 12], [443, 20, 456, 18, "neighborIndex"], [443, 33, 456, 31], [443, 36, 456, 34], [443, 37, 456, 35], [443, 38, 456, 36, "y"], [443, 39, 456, 37], [443, 42, 456, 40, "dy"], [443, 44, 456, 42], [443, 48, 456, 46, "width"], [443, 53, 456, 51], [443, 57, 456, 55, "x"], [443, 58, 456, 56], [443, 61, 456, 59, "dx"], [443, 63, 456, 61], [443, 64, 456, 62], [443, 68, 456, 66], [443, 69, 456, 67], [444, 14, 457, 12, "r"], [444, 15, 457, 13], [444, 19, 457, 17, "original"], [444, 27, 457, 25], [444, 28, 457, 26, "neighborIndex"], [444, 41, 457, 39], [444, 42, 457, 40], [445, 14, 458, 12, "g"], [445, 15, 458, 13], [445, 19, 458, 17, "original"], [445, 27, 458, 25], [445, 28, 458, 26, "neighborIndex"], [445, 41, 458, 39], [445, 44, 458, 42], [445, 45, 458, 43], [445, 46, 458, 44], [446, 14, 459, 12, "b"], [446, 15, 459, 13], [446, 19, 459, 17, "original"], [446, 27, 459, 25], [446, 28, 459, 26, "neighborIndex"], [446, 41, 459, 39], [446, 44, 459, 42], [446, 45, 459, 43], [446, 46, 459, 44], [447, 12, 460, 10], [448, 10, 461, 8], [449, 10, 463, 8, "data"], [449, 14, 463, 12], [449, 15, 463, 13, "index"], [449, 20, 463, 18], [449, 21, 463, 19], [449, 24, 463, 22, "r"], [449, 25, 463, 23], [449, 28, 463, 26], [449, 29, 463, 27], [450, 10, 464, 8, "data"], [450, 14, 464, 12], [450, 15, 464, 13, "index"], [450, 20, 464, 18], [450, 23, 464, 21], [450, 24, 464, 22], [450, 25, 464, 23], [450, 28, 464, 26, "g"], [450, 29, 464, 27], [450, 32, 464, 30], [450, 33, 464, 31], [451, 10, 465, 8, "data"], [451, 14, 465, 12], [451, 15, 465, 13, "index"], [451, 20, 465, 18], [451, 23, 465, 21], [451, 24, 465, 22], [451, 25, 465, 23], [451, 28, 465, 26, "b"], [451, 29, 465, 27], [451, 32, 465, 30], [451, 33, 465, 31], [452, 8, 466, 6], [453, 6, 467, 4], [454, 4, 468, 2], [454, 5, 468, 3], [455, 4, 470, 2], [455, 10, 470, 8, "applyFallbackFaceBlur"], [455, 31, 470, 29], [455, 34, 470, 32, "applyFallbackFaceBlur"], [455, 35, 470, 33, "ctx"], [455, 38, 470, 62], [455, 40, 470, 64, "imgWidth"], [455, 48, 470, 80], [455, 50, 470, 82, "imgHeight"], [455, 59, 470, 99], [455, 64, 470, 104], [456, 6, 471, 4, "console"], [456, 13, 471, 11], [456, 14, 471, 12, "log"], [456, 17, 471, 15], [456, 18, 471, 16], [456, 90, 471, 88], [456, 91, 471, 89], [458, 6, 473, 4], [459, 6, 474, 4], [459, 12, 474, 10, "areas"], [459, 17, 474, 15], [459, 20, 474, 18], [460, 6, 475, 6], [461, 6, 476, 6], [462, 8, 476, 8, "x"], [462, 9, 476, 9], [462, 11, 476, 11, "imgWidth"], [462, 19, 476, 19], [462, 22, 476, 22], [462, 26, 476, 26], [463, 8, 476, 28, "y"], [463, 9, 476, 29], [463, 11, 476, 31, "imgHeight"], [463, 20, 476, 40], [463, 23, 476, 43], [463, 27, 476, 47], [464, 8, 476, 49, "w"], [464, 9, 476, 50], [464, 11, 476, 52, "imgWidth"], [464, 19, 476, 60], [464, 22, 476, 63], [464, 25, 476, 66], [465, 8, 476, 68, "h"], [465, 9, 476, 69], [465, 11, 476, 71, "imgHeight"], [465, 20, 476, 80], [465, 23, 476, 83], [466, 6, 476, 87], [466, 7, 476, 88], [467, 6, 477, 6], [468, 6, 478, 6], [469, 8, 478, 8, "x"], [469, 9, 478, 9], [469, 11, 478, 11, "imgWidth"], [469, 19, 478, 19], [469, 22, 478, 22], [469, 25, 478, 25], [470, 8, 478, 27, "y"], [470, 9, 478, 28], [470, 11, 478, 30, "imgHeight"], [470, 20, 478, 39], [470, 23, 478, 42], [470, 26, 478, 45], [471, 8, 478, 47, "w"], [471, 9, 478, 48], [471, 11, 478, 50, "imgWidth"], [471, 19, 478, 58], [471, 22, 478, 61], [471, 26, 478, 65], [472, 8, 478, 67, "h"], [472, 9, 478, 68], [472, 11, 478, 70, "imgHeight"], [472, 20, 478, 79], [472, 23, 478, 82], [473, 6, 478, 86], [473, 7, 478, 87], [474, 6, 479, 6], [475, 6, 480, 6], [476, 8, 480, 8, "x"], [476, 9, 480, 9], [476, 11, 480, 11, "imgWidth"], [476, 19, 480, 19], [476, 22, 480, 22], [476, 26, 480, 26], [477, 8, 480, 28, "y"], [477, 9, 480, 29], [477, 11, 480, 31, "imgHeight"], [477, 20, 480, 40], [477, 23, 480, 43], [477, 26, 480, 46], [478, 8, 480, 48, "w"], [478, 9, 480, 49], [478, 11, 480, 51, "imgWidth"], [478, 19, 480, 59], [478, 22, 480, 62], [478, 26, 480, 66], [479, 8, 480, 68, "h"], [479, 9, 480, 69], [479, 11, 480, 71, "imgHeight"], [479, 20, 480, 80], [479, 23, 480, 83], [480, 6, 480, 87], [480, 7, 480, 88], [480, 8, 481, 5], [481, 6, 483, 4, "areas"], [481, 11, 483, 9], [481, 12, 483, 10, "for<PERSON>ach"], [481, 19, 483, 17], [481, 20, 483, 18], [481, 21, 483, 19, "area"], [481, 25, 483, 23], [481, 27, 483, 25, "index"], [481, 32, 483, 30], [481, 37, 483, 35], [482, 8, 484, 6, "console"], [482, 15, 484, 13], [482, 16, 484, 14, "log"], [482, 19, 484, 17], [482, 20, 484, 18], [482, 65, 484, 63, "index"], [482, 70, 484, 68], [482, 73, 484, 71], [482, 74, 484, 72], [482, 77, 484, 75], [482, 79, 484, 77, "area"], [482, 83, 484, 81], [482, 84, 484, 82], [483, 8, 485, 6, "applyStrongBlur"], [483, 23, 485, 21], [483, 24, 485, 22, "ctx"], [483, 27, 485, 25], [483, 29, 485, 27, "area"], [483, 33, 485, 31], [483, 34, 485, 32, "x"], [483, 35, 485, 33], [483, 37, 485, 35, "area"], [483, 41, 485, 39], [483, 42, 485, 40, "y"], [483, 43, 485, 41], [483, 45, 485, 43, "area"], [483, 49, 485, 47], [483, 50, 485, 48, "w"], [483, 51, 485, 49], [483, 53, 485, 51, "area"], [483, 57, 485, 55], [483, 58, 485, 56, "h"], [483, 59, 485, 57], [483, 60, 485, 58], [484, 6, 486, 4], [484, 7, 486, 5], [484, 8, 486, 6], [485, 4, 487, 2], [485, 5, 487, 3], [487, 4, 489, 2], [488, 4, 490, 2], [488, 10, 490, 8, "capturePhoto"], [488, 22, 490, 20], [488, 25, 490, 23], [488, 29, 490, 23, "useCallback"], [488, 47, 490, 34], [488, 49, 490, 35], [488, 61, 490, 47], [489, 6, 491, 4], [490, 6, 492, 4], [490, 12, 492, 10, "isDev"], [490, 17, 492, 15], [490, 20, 492, 18, "process"], [490, 27, 492, 25], [490, 28, 492, 26, "env"], [490, 31, 492, 29], [490, 32, 492, 30, "NODE_ENV"], [490, 40, 492, 38], [490, 45, 492, 43], [490, 58, 492, 56], [490, 62, 492, 60, "__DEV__"], [490, 69, 492, 67], [491, 6, 494, 4], [491, 10, 494, 8], [491, 11, 494, 9, "cameraRef"], [491, 20, 494, 18], [491, 21, 494, 19, "current"], [491, 28, 494, 26], [491, 32, 494, 30], [491, 33, 494, 31, "isDev"], [491, 38, 494, 36], [491, 40, 494, 38], [492, 8, 495, 6, "<PERSON><PERSON>"], [492, 22, 495, 11], [492, 23, 495, 12, "alert"], [492, 28, 495, 17], [492, 29, 495, 18], [492, 36, 495, 25], [492, 38, 495, 27], [492, 56, 495, 45], [492, 57, 495, 46], [493, 8, 496, 6], [494, 6, 497, 4], [495, 6, 498, 4], [495, 10, 498, 8], [496, 8, 499, 6, "setProcessingState"], [496, 26, 499, 24], [496, 27, 499, 25], [496, 38, 499, 36], [496, 39, 499, 37], [497, 8, 500, 6, "setProcessingProgress"], [497, 29, 500, 27], [497, 30, 500, 28], [497, 32, 500, 30], [497, 33, 500, 31], [498, 8, 501, 6], [499, 8, 502, 6], [500, 8, 503, 6], [501, 8, 504, 6], [501, 14, 504, 12], [501, 18, 504, 16, "Promise"], [501, 25, 504, 23], [501, 26, 504, 24, "resolve"], [501, 33, 504, 31], [501, 37, 504, 35, "setTimeout"], [501, 47, 504, 45], [501, 48, 504, 46, "resolve"], [501, 55, 504, 53], [501, 57, 504, 55], [501, 59, 504, 57], [501, 60, 504, 58], [501, 61, 504, 59], [502, 8, 505, 6], [503, 8, 506, 6], [503, 12, 506, 10, "photo"], [503, 17, 506, 15], [504, 8, 508, 6], [504, 12, 508, 10], [505, 10, 509, 8, "photo"], [505, 15, 509, 13], [505, 18, 509, 16], [505, 24, 509, 22, "cameraRef"], [505, 33, 509, 31], [505, 34, 509, 32, "current"], [505, 41, 509, 39], [505, 42, 509, 40, "takePictureAsync"], [505, 58, 509, 56], [505, 59, 509, 57], [506, 12, 510, 10, "quality"], [506, 19, 510, 17], [506, 21, 510, 19], [506, 24, 510, 22], [507, 12, 511, 10, "base64"], [507, 18, 511, 16], [507, 20, 511, 18], [507, 25, 511, 23], [508, 12, 512, 10, "skipProcessing"], [508, 26, 512, 24], [508, 28, 512, 26], [508, 32, 512, 30], [508, 33, 512, 32], [509, 10, 513, 8], [509, 11, 513, 9], [509, 12, 513, 10], [510, 8, 514, 6], [510, 9, 514, 7], [510, 10, 514, 8], [510, 17, 514, 15, "cameraError"], [510, 28, 514, 26], [510, 30, 514, 28], [511, 10, 515, 8, "console"], [511, 17, 515, 15], [511, 18, 515, 16, "log"], [511, 21, 515, 19], [511, 22, 515, 20], [511, 82, 515, 80], [511, 84, 515, 82, "cameraError"], [511, 95, 515, 93], [511, 96, 515, 94], [512, 10, 516, 8], [513, 10, 517, 8], [513, 14, 517, 12, "isDev"], [513, 19, 517, 17], [513, 21, 517, 19], [514, 12, 518, 10, "photo"], [514, 17, 518, 15], [514, 20, 518, 18], [515, 14, 519, 12, "uri"], [515, 17, 519, 15], [515, 19, 519, 17], [516, 12, 520, 10], [516, 13, 520, 11], [517, 10, 521, 8], [517, 11, 521, 9], [517, 17, 521, 15], [518, 12, 522, 10], [518, 18, 522, 16, "cameraError"], [518, 29, 522, 27], [519, 10, 523, 8], [520, 8, 524, 6], [521, 8, 525, 6], [521, 12, 525, 10], [521, 13, 525, 11, "photo"], [521, 18, 525, 16], [521, 20, 525, 18], [522, 10, 526, 8], [522, 16, 526, 14], [522, 20, 526, 18, "Error"], [522, 25, 526, 23], [522, 26, 526, 24], [522, 51, 526, 49], [522, 52, 526, 50], [523, 8, 527, 6], [524, 8, 528, 6, "console"], [524, 15, 528, 13], [524, 16, 528, 14, "log"], [524, 19, 528, 17], [524, 20, 528, 18], [524, 56, 528, 54], [524, 58, 528, 56, "photo"], [524, 63, 528, 61], [524, 64, 528, 62, "uri"], [524, 67, 528, 65], [524, 68, 528, 66], [525, 8, 529, 6, "setCapturedPhoto"], [525, 24, 529, 22], [525, 25, 529, 23, "photo"], [525, 30, 529, 28], [525, 31, 529, 29, "uri"], [525, 34, 529, 32], [525, 35, 529, 33], [526, 8, 530, 6, "setProcessingProgress"], [526, 29, 530, 27], [526, 30, 530, 28], [526, 32, 530, 30], [526, 33, 530, 31], [527, 8, 531, 6], [528, 8, 532, 6, "console"], [528, 15, 532, 13], [528, 16, 532, 14, "log"], [528, 19, 532, 17], [528, 20, 532, 18], [528, 73, 532, 71], [528, 74, 532, 72], [529, 8, 533, 6], [529, 14, 533, 12, "processImageWithFaceBlur"], [529, 38, 533, 36], [529, 39, 533, 37, "photo"], [529, 44, 533, 42], [529, 45, 533, 43, "uri"], [529, 48, 533, 46], [529, 49, 533, 47], [530, 8, 534, 6, "console"], [530, 15, 534, 13], [530, 16, 534, 14, "log"], [530, 19, 534, 17], [530, 20, 534, 18], [530, 71, 534, 69], [530, 72, 534, 70], [531, 6, 535, 4], [531, 7, 535, 5], [531, 8, 535, 6], [531, 15, 535, 13, "error"], [531, 20, 535, 18], [531, 22, 535, 20], [532, 8, 536, 6, "console"], [532, 15, 536, 13], [532, 16, 536, 14, "error"], [532, 21, 536, 19], [532, 22, 536, 20], [532, 54, 536, 52], [532, 56, 536, 54, "error"], [532, 61, 536, 59], [532, 62, 536, 60], [533, 8, 537, 6, "setErrorMessage"], [533, 23, 537, 21], [533, 24, 537, 22], [533, 68, 537, 66], [533, 69, 537, 67], [534, 8, 538, 6, "setProcessingState"], [534, 26, 538, 24], [534, 27, 538, 25], [534, 34, 538, 32], [534, 35, 538, 33], [535, 6, 539, 4], [536, 4, 540, 2], [536, 5, 540, 3], [536, 7, 540, 5], [536, 9, 540, 7], [536, 10, 540, 8], [537, 4, 541, 2], [538, 4, 542, 2], [538, 10, 542, 8, "processImageWithFaceBlur"], [538, 34, 542, 32], [538, 37, 542, 35], [538, 43, 542, 42, "photoUri"], [538, 51, 542, 58], [538, 55, 542, 63], [539, 6, 543, 4, "console"], [539, 13, 543, 11], [539, 14, 543, 12, "log"], [539, 17, 543, 15], [539, 18, 543, 16], [539, 85, 543, 83], [539, 86, 543, 84], [540, 6, 544, 4], [540, 10, 544, 8], [541, 8, 545, 6, "console"], [541, 15, 545, 13], [541, 16, 545, 14, "log"], [541, 19, 545, 17], [541, 20, 545, 18], [541, 84, 545, 82], [541, 85, 545, 83], [542, 8, 546, 6, "setProcessingState"], [542, 26, 546, 24], [542, 27, 546, 25], [542, 39, 546, 37], [542, 40, 546, 38], [543, 8, 547, 6, "setProcessingProgress"], [543, 29, 547, 27], [543, 30, 547, 28], [543, 32, 547, 30], [543, 33, 547, 31], [545, 8, 549, 6], [546, 8, 550, 6], [546, 14, 550, 12, "canvas"], [546, 20, 550, 18], [546, 23, 550, 21, "document"], [546, 31, 550, 29], [546, 32, 550, 30, "createElement"], [546, 45, 550, 43], [546, 46, 550, 44], [546, 54, 550, 52], [546, 55, 550, 53], [547, 8, 551, 6], [547, 14, 551, 12, "ctx"], [547, 17, 551, 15], [547, 20, 551, 18, "canvas"], [547, 26, 551, 24], [547, 27, 551, 25, "getContext"], [547, 37, 551, 35], [547, 38, 551, 36], [547, 42, 551, 40], [547, 43, 551, 41], [548, 8, 552, 6], [548, 12, 552, 10], [548, 13, 552, 11, "ctx"], [548, 16, 552, 14], [548, 18, 552, 16], [548, 24, 552, 22], [548, 28, 552, 26, "Error"], [548, 33, 552, 31], [548, 34, 552, 32], [548, 64, 552, 62], [548, 65, 552, 63], [550, 8, 554, 6], [551, 8, 555, 6], [551, 14, 555, 12, "img"], [551, 17, 555, 15], [551, 20, 555, 18], [551, 24, 555, 22, "Image"], [551, 29, 555, 27], [551, 30, 555, 28], [551, 31, 555, 29], [552, 8, 556, 6], [552, 14, 556, 12], [552, 18, 556, 16, "Promise"], [552, 25, 556, 23], [552, 26, 556, 24], [552, 27, 556, 25, "resolve"], [552, 34, 556, 32], [552, 36, 556, 34, "reject"], [552, 42, 556, 40], [552, 47, 556, 45], [553, 10, 557, 8, "img"], [553, 13, 557, 11], [553, 14, 557, 12, "onload"], [553, 20, 557, 18], [553, 23, 557, 21, "resolve"], [553, 30, 557, 28], [554, 10, 558, 8, "img"], [554, 13, 558, 11], [554, 14, 558, 12, "onerror"], [554, 21, 558, 19], [554, 24, 558, 22, "reject"], [554, 30, 558, 28], [555, 10, 559, 8, "img"], [555, 13, 559, 11], [555, 14, 559, 12, "src"], [555, 17, 559, 15], [555, 20, 559, 18, "photoUri"], [555, 28, 559, 26], [556, 8, 560, 6], [556, 9, 560, 7], [556, 10, 560, 8], [558, 8, 562, 6], [559, 8, 563, 6, "canvas"], [559, 14, 563, 12], [559, 15, 563, 13, "width"], [559, 20, 563, 18], [559, 23, 563, 21, "img"], [559, 26, 563, 24], [559, 27, 563, 25, "width"], [559, 32, 563, 30], [560, 8, 564, 6, "canvas"], [560, 14, 564, 12], [560, 15, 564, 13, "height"], [560, 21, 564, 19], [560, 24, 564, 22, "img"], [560, 27, 564, 25], [560, 28, 564, 26, "height"], [560, 34, 564, 32], [561, 8, 565, 6, "console"], [561, 15, 565, 13], [561, 16, 565, 14, "log"], [561, 19, 565, 17], [561, 20, 565, 18], [561, 54, 565, 52], [561, 56, 565, 54], [562, 10, 565, 56, "width"], [562, 15, 565, 61], [562, 17, 565, 63, "img"], [562, 20, 565, 66], [562, 21, 565, 67, "width"], [562, 26, 565, 72], [563, 10, 565, 74, "height"], [563, 16, 565, 80], [563, 18, 565, 82, "img"], [563, 21, 565, 85], [563, 22, 565, 86, "height"], [564, 8, 565, 93], [564, 9, 565, 94], [564, 10, 565, 95], [566, 8, 567, 6], [567, 8, 568, 6, "ctx"], [567, 11, 568, 9], [567, 12, 568, 10, "drawImage"], [567, 21, 568, 19], [567, 22, 568, 20, "img"], [567, 25, 568, 23], [567, 27, 568, 25], [567, 28, 568, 26], [567, 30, 568, 28], [567, 31, 568, 29], [567, 32, 568, 30], [568, 8, 569, 6, "console"], [568, 15, 569, 13], [568, 16, 569, 14, "log"], [568, 19, 569, 17], [568, 20, 569, 18], [568, 72, 569, 70], [568, 73, 569, 71], [569, 8, 571, 6, "setProcessingProgress"], [569, 29, 571, 27], [569, 30, 571, 28], [569, 32, 571, 30], [569, 33, 571, 31], [571, 8, 573, 6], [572, 8, 574, 6], [572, 12, 574, 10, "detectedFaces"], [572, 25, 574, 23], [572, 28, 574, 26], [572, 30, 574, 28], [573, 8, 576, 6, "console"], [573, 15, 576, 13], [573, 16, 576, 14, "log"], [573, 19, 576, 17], [573, 20, 576, 18], [573, 81, 576, 79], [573, 82, 576, 80], [575, 8, 578, 6], [576, 8, 579, 6], [576, 12, 579, 10], [577, 10, 580, 8, "console"], [577, 17, 580, 15], [577, 18, 580, 16, "log"], [577, 21, 580, 19], [577, 22, 580, 20], [577, 81, 580, 79], [577, 82, 580, 80], [578, 10, 581, 8], [578, 16, 581, 14, "loadTensorFlowFaceDetection"], [578, 43, 581, 41], [578, 44, 581, 42], [578, 45, 581, 43], [579, 10, 582, 8, "console"], [579, 17, 582, 15], [579, 18, 582, 16, "log"], [579, 21, 582, 19], [579, 22, 582, 20], [579, 90, 582, 88], [579, 91, 582, 89], [580, 10, 583, 8, "detectedFaces"], [580, 23, 583, 21], [580, 26, 583, 24], [580, 32, 583, 30, "detectFacesWithTensorFlow"], [580, 57, 583, 55], [580, 58, 583, 56, "img"], [580, 61, 583, 59], [580, 62, 583, 60], [581, 10, 584, 8, "console"], [581, 17, 584, 15], [581, 18, 584, 16, "log"], [581, 21, 584, 19], [581, 22, 584, 20], [581, 70, 584, 68, "detectedFaces"], [581, 83, 584, 81], [581, 84, 584, 82, "length"], [581, 90, 584, 88], [581, 98, 584, 96], [581, 99, 584, 97], [582, 8, 585, 6], [582, 9, 585, 7], [582, 10, 585, 8], [582, 17, 585, 15, "tensorFlowError"], [582, 32, 585, 30], [582, 34, 585, 32], [583, 10, 586, 8, "console"], [583, 17, 586, 15], [583, 18, 586, 16, "warn"], [583, 22, 586, 20], [583, 23, 586, 21], [583, 61, 586, 59], [583, 63, 586, 61, "tensorFlowError"], [583, 78, 586, 76], [583, 79, 586, 77], [584, 10, 587, 8, "console"], [584, 17, 587, 15], [584, 18, 587, 16, "warn"], [584, 22, 587, 20], [584, 23, 587, 21], [584, 68, 587, 66], [584, 70, 587, 68], [585, 12, 588, 10, "message"], [585, 19, 588, 17], [585, 21, 588, 19, "tensorFlowError"], [585, 36, 588, 34], [585, 37, 588, 35, "message"], [585, 44, 588, 42], [586, 12, 589, 10, "stack"], [586, 17, 589, 15], [586, 19, 589, 17, "tensorFlowError"], [586, 34, 589, 32], [586, 35, 589, 33, "stack"], [587, 10, 590, 8], [587, 11, 590, 9], [587, 12, 590, 10], [589, 10, 592, 8], [590, 10, 593, 8, "console"], [590, 17, 593, 15], [590, 18, 593, 16, "log"], [590, 21, 593, 19], [590, 22, 593, 20], [590, 86, 593, 84], [590, 87, 593, 85], [591, 10, 594, 8, "detectedFaces"], [591, 23, 594, 21], [591, 26, 594, 24, "detectFacesHeuristic"], [591, 46, 594, 44], [591, 47, 594, 45, "img"], [591, 50, 594, 48], [591, 52, 594, 50, "ctx"], [591, 55, 594, 53], [591, 56, 594, 54], [592, 10, 595, 8, "console"], [592, 17, 595, 15], [592, 18, 595, 16, "log"], [592, 21, 595, 19], [592, 22, 595, 20], [592, 70, 595, 68, "detectedFaces"], [592, 83, 595, 81], [592, 84, 595, 82, "length"], [592, 90, 595, 88], [592, 98, 595, 96], [592, 99, 595, 97], [593, 8, 596, 6], [595, 8, 598, 6], [596, 8, 599, 6], [596, 12, 599, 10, "detectedFaces"], [596, 25, 599, 23], [596, 26, 599, 24, "length"], [596, 32, 599, 30], [596, 37, 599, 35], [596, 38, 599, 36], [596, 40, 599, 38], [597, 10, 600, 8, "console"], [597, 17, 600, 15], [597, 18, 600, 16, "log"], [597, 21, 600, 19], [597, 22, 600, 20], [597, 89, 600, 87], [597, 90, 600, 88], [598, 10, 601, 8, "detectedFaces"], [598, 23, 601, 21], [598, 26, 601, 24, "detectFacesAggressive"], [598, 47, 601, 45], [598, 48, 601, 46, "img"], [598, 51, 601, 49], [598, 53, 601, 51, "ctx"], [598, 56, 601, 54], [598, 57, 601, 55], [599, 10, 602, 8, "console"], [599, 17, 602, 15], [599, 18, 602, 16, "log"], [599, 21, 602, 19], [599, 22, 602, 20], [599, 71, 602, 69, "detectedFaces"], [599, 84, 602, 82], [599, 85, 602, 83, "length"], [599, 91, 602, 89], [599, 99, 602, 97], [599, 100, 602, 98], [600, 8, 603, 6], [601, 8, 605, 6, "console"], [601, 15, 605, 13], [601, 16, 605, 14, "log"], [601, 19, 605, 17], [601, 20, 605, 18], [601, 72, 605, 70, "detectedFaces"], [601, 85, 605, 83], [601, 86, 605, 84, "length"], [601, 92, 605, 90], [601, 100, 605, 98], [601, 101, 605, 99], [602, 8, 606, 6], [602, 12, 606, 10, "detectedFaces"], [602, 25, 606, 23], [602, 26, 606, 24, "length"], [602, 32, 606, 30], [602, 35, 606, 33], [602, 36, 606, 34], [602, 38, 606, 36], [603, 10, 607, 8, "console"], [603, 17, 607, 15], [603, 18, 607, 16, "log"], [603, 21, 607, 19], [603, 22, 607, 20], [603, 66, 607, 64], [603, 68, 607, 66, "detectedFaces"], [603, 81, 607, 79], [603, 82, 607, 80, "map"], [603, 85, 607, 83], [603, 86, 607, 84], [603, 87, 607, 85, "face"], [603, 91, 607, 89], [603, 93, 607, 91, "i"], [603, 94, 607, 92], [603, 100, 607, 98], [604, 12, 608, 10, "faceNumber"], [604, 22, 608, 20], [604, 24, 608, 22, "i"], [604, 25, 608, 23], [604, 28, 608, 26], [604, 29, 608, 27], [605, 12, 609, 10, "centerX"], [605, 19, 609, 17], [605, 21, 609, 19, "face"], [605, 25, 609, 23], [605, 26, 609, 24, "boundingBox"], [605, 37, 609, 35], [605, 38, 609, 36, "xCenter"], [605, 45, 609, 43], [606, 12, 610, 10, "centerY"], [606, 19, 610, 17], [606, 21, 610, 19, "face"], [606, 25, 610, 23], [606, 26, 610, 24, "boundingBox"], [606, 37, 610, 35], [606, 38, 610, 36, "yCenter"], [606, 45, 610, 43], [607, 12, 611, 10, "width"], [607, 17, 611, 15], [607, 19, 611, 17, "face"], [607, 23, 611, 21], [607, 24, 611, 22, "boundingBox"], [607, 35, 611, 33], [607, 36, 611, 34, "width"], [607, 41, 611, 39], [608, 12, 612, 10, "height"], [608, 18, 612, 16], [608, 20, 612, 18, "face"], [608, 24, 612, 22], [608, 25, 612, 23, "boundingBox"], [608, 36, 612, 34], [608, 37, 612, 35, "height"], [609, 10, 613, 8], [609, 11, 613, 9], [609, 12, 613, 10], [609, 13, 613, 11], [609, 14, 613, 12], [610, 8, 614, 6], [610, 9, 614, 7], [610, 15, 614, 13], [611, 10, 615, 8, "console"], [611, 17, 615, 15], [611, 18, 615, 16, "log"], [611, 21, 615, 19], [611, 22, 615, 20], [611, 74, 615, 72], [611, 75, 615, 73], [612, 10, 616, 8, "console"], [612, 17, 616, 15], [612, 18, 616, 16, "log"], [612, 21, 616, 19], [612, 22, 616, 20], [612, 95, 616, 93], [612, 96, 616, 94], [614, 10, 618, 8], [615, 10, 619, 8], [615, 16, 619, 14, "centerX"], [615, 23, 619, 21], [615, 26, 619, 24, "img"], [615, 29, 619, 27], [615, 30, 619, 28, "width"], [615, 35, 619, 33], [615, 38, 619, 36], [615, 41, 619, 39], [616, 10, 620, 8], [616, 16, 620, 14, "centerY"], [616, 23, 620, 21], [616, 26, 620, 24, "img"], [616, 29, 620, 27], [616, 30, 620, 28, "height"], [616, 36, 620, 34], [616, 39, 620, 37], [616, 42, 620, 40], [617, 10, 621, 8], [617, 16, 621, 14, "centerWidth"], [617, 27, 621, 25], [617, 30, 621, 28, "img"], [617, 33, 621, 31], [617, 34, 621, 32, "width"], [617, 39, 621, 37], [617, 42, 621, 40], [617, 45, 621, 43], [618, 10, 622, 8], [618, 16, 622, 14, "centerHeight"], [618, 28, 622, 26], [618, 31, 622, 29, "img"], [618, 34, 622, 32], [618, 35, 622, 33, "height"], [618, 41, 622, 39], [618, 44, 622, 42], [618, 47, 622, 45], [619, 10, 624, 8, "detectedFaces"], [619, 23, 624, 21], [619, 26, 624, 24], [619, 27, 624, 25], [620, 12, 625, 10, "boundingBox"], [620, 23, 625, 21], [620, 25, 625, 23], [621, 14, 626, 12, "xCenter"], [621, 21, 626, 19], [621, 23, 626, 21], [621, 26, 626, 24], [622, 14, 627, 12, "yCenter"], [622, 21, 627, 19], [622, 23, 627, 21], [622, 26, 627, 24], [623, 14, 628, 12, "width"], [623, 19, 628, 17], [623, 21, 628, 19], [623, 24, 628, 22], [624, 14, 629, 12, "height"], [624, 20, 629, 18], [624, 22, 629, 20], [625, 12, 630, 10], [625, 13, 630, 11], [626, 12, 631, 10, "confidence"], [626, 22, 631, 20], [626, 24, 631, 22], [627, 10, 632, 8], [627, 11, 632, 9], [627, 12, 632, 10], [628, 10, 634, 8, "console"], [628, 17, 634, 15], [628, 18, 634, 16, "log"], [628, 21, 634, 19], [628, 22, 634, 20], [628, 100, 634, 98], [628, 101, 634, 99], [629, 8, 635, 6], [630, 8, 637, 6, "setProcessingProgress"], [630, 29, 637, 27], [630, 30, 637, 28], [630, 32, 637, 30], [630, 33, 637, 31], [632, 8, 639, 6], [633, 8, 640, 6], [633, 12, 640, 10, "detectedFaces"], [633, 25, 640, 23], [633, 26, 640, 24, "length"], [633, 32, 640, 30], [633, 35, 640, 33], [633, 36, 640, 34], [633, 38, 640, 36], [634, 10, 641, 8, "console"], [634, 17, 641, 15], [634, 18, 641, 16, "log"], [634, 21, 641, 19], [634, 22, 641, 20], [634, 61, 641, 59, "detectedFaces"], [634, 74, 641, 72], [634, 75, 641, 73, "length"], [634, 81, 641, 79], [634, 101, 641, 99], [634, 102, 641, 100], [635, 10, 643, 8, "detectedFaces"], [635, 23, 643, 21], [635, 24, 643, 22, "for<PERSON>ach"], [635, 31, 643, 29], [635, 32, 643, 30], [635, 33, 643, 31, "detection"], [635, 42, 643, 40], [635, 44, 643, 42, "index"], [635, 49, 643, 47], [635, 54, 643, 52], [636, 12, 644, 10], [636, 18, 644, 16, "bbox"], [636, 22, 644, 20], [636, 25, 644, 23, "detection"], [636, 34, 644, 32], [636, 35, 644, 33, "boundingBox"], [636, 46, 644, 44], [637, 12, 646, 10, "console"], [637, 19, 646, 17], [637, 20, 646, 18, "log"], [637, 23, 646, 21], [637, 24, 646, 22], [637, 85, 646, 83, "index"], [637, 90, 646, 88], [637, 93, 646, 91], [637, 94, 646, 92], [637, 97, 646, 95], [637, 99, 646, 97], [638, 14, 647, 12, "bbox"], [638, 18, 647, 16], [639, 14, 648, 12, "imageSize"], [639, 23, 648, 21], [639, 25, 648, 23], [640, 16, 648, 25, "width"], [640, 21, 648, 30], [640, 23, 648, 32, "img"], [640, 26, 648, 35], [640, 27, 648, 36, "width"], [640, 32, 648, 41], [641, 16, 648, 43, "height"], [641, 22, 648, 49], [641, 24, 648, 51, "img"], [641, 27, 648, 54], [641, 28, 648, 55, "height"], [642, 14, 648, 62], [643, 12, 649, 10], [643, 13, 649, 11], [643, 14, 649, 12], [645, 12, 651, 10], [646, 12, 652, 10], [646, 18, 652, 16, "faceX"], [646, 23, 652, 21], [646, 26, 652, 24, "bbox"], [646, 30, 652, 28], [646, 31, 652, 29, "xCenter"], [646, 38, 652, 36], [646, 41, 652, 39, "img"], [646, 44, 652, 42], [646, 45, 652, 43, "width"], [646, 50, 652, 48], [646, 53, 652, 52, "bbox"], [646, 57, 652, 56], [646, 58, 652, 57, "width"], [646, 63, 652, 62], [646, 66, 652, 65, "img"], [646, 69, 652, 68], [646, 70, 652, 69, "width"], [646, 75, 652, 74], [646, 78, 652, 78], [646, 79, 652, 79], [647, 12, 653, 10], [647, 18, 653, 16, "faceY"], [647, 23, 653, 21], [647, 26, 653, 24, "bbox"], [647, 30, 653, 28], [647, 31, 653, 29, "yCenter"], [647, 38, 653, 36], [647, 41, 653, 39, "img"], [647, 44, 653, 42], [647, 45, 653, 43, "height"], [647, 51, 653, 49], [647, 54, 653, 53, "bbox"], [647, 58, 653, 57], [647, 59, 653, 58, "height"], [647, 65, 653, 64], [647, 68, 653, 67, "img"], [647, 71, 653, 70], [647, 72, 653, 71, "height"], [647, 78, 653, 77], [647, 81, 653, 81], [647, 82, 653, 82], [648, 12, 654, 10], [648, 18, 654, 16, "faceWidth"], [648, 27, 654, 25], [648, 30, 654, 28, "bbox"], [648, 34, 654, 32], [648, 35, 654, 33, "width"], [648, 40, 654, 38], [648, 43, 654, 41, "img"], [648, 46, 654, 44], [648, 47, 654, 45, "width"], [648, 52, 654, 50], [649, 12, 655, 10], [649, 18, 655, 16, "faceHeight"], [649, 28, 655, 26], [649, 31, 655, 29, "bbox"], [649, 35, 655, 33], [649, 36, 655, 34, "height"], [649, 42, 655, 40], [649, 45, 655, 43, "img"], [649, 48, 655, 46], [649, 49, 655, 47, "height"], [649, 55, 655, 53], [650, 12, 657, 10, "console"], [650, 19, 657, 17], [650, 20, 657, 18, "log"], [650, 23, 657, 21], [650, 24, 657, 22], [650, 84, 657, 82], [650, 86, 657, 84], [651, 14, 658, 12, "faceX"], [651, 19, 658, 17], [652, 14, 658, 19, "faceY"], [652, 19, 658, 24], [653, 14, 658, 26, "faceWidth"], [653, 23, 658, 35], [654, 14, 658, 37, "faceHeight"], [654, 24, 658, 47], [655, 14, 659, 12, "<PERSON><PERSON><PERSON><PERSON>"], [655, 21, 659, 19], [655, 23, 659, 21, "faceX"], [655, 28, 659, 26], [655, 32, 659, 30], [655, 33, 659, 31], [655, 37, 659, 35, "faceY"], [655, 42, 659, 40], [655, 46, 659, 44], [655, 47, 659, 45], [655, 51, 659, 49, "faceWidth"], [655, 60, 659, 58], [655, 63, 659, 61], [655, 64, 659, 62], [655, 68, 659, 66, "faceHeight"], [655, 78, 659, 76], [655, 81, 659, 79], [656, 12, 660, 10], [656, 13, 660, 11], [656, 14, 660, 12], [658, 12, 662, 10], [659, 12, 663, 10], [659, 18, 663, 16, "padding"], [659, 25, 663, 23], [659, 28, 663, 26], [659, 31, 663, 29], [660, 12, 664, 10], [660, 18, 664, 16, "paddedX"], [660, 25, 664, 23], [660, 28, 664, 26, "Math"], [660, 32, 664, 30], [660, 33, 664, 31, "max"], [660, 36, 664, 34], [660, 37, 664, 35], [660, 38, 664, 36], [660, 40, 664, 38, "faceX"], [660, 45, 664, 43], [660, 48, 664, 46, "faceWidth"], [660, 57, 664, 55], [660, 60, 664, 58, "padding"], [660, 67, 664, 65], [660, 68, 664, 66], [661, 12, 665, 10], [661, 18, 665, 16, "paddedY"], [661, 25, 665, 23], [661, 28, 665, 26, "Math"], [661, 32, 665, 30], [661, 33, 665, 31, "max"], [661, 36, 665, 34], [661, 37, 665, 35], [661, 38, 665, 36], [661, 40, 665, 38, "faceY"], [661, 45, 665, 43], [661, 48, 665, 46, "faceHeight"], [661, 58, 665, 56], [661, 61, 665, 59, "padding"], [661, 68, 665, 66], [661, 69, 665, 67], [662, 12, 666, 10], [662, 18, 666, 16, "<PERSON><PERSON><PERSON><PERSON>"], [662, 29, 666, 27], [662, 32, 666, 30, "Math"], [662, 36, 666, 34], [662, 37, 666, 35, "min"], [662, 40, 666, 38], [662, 41, 666, 39, "img"], [662, 44, 666, 42], [662, 45, 666, 43, "width"], [662, 50, 666, 48], [662, 53, 666, 51, "paddedX"], [662, 60, 666, 58], [662, 62, 666, 60, "faceWidth"], [662, 71, 666, 69], [662, 75, 666, 73], [662, 76, 666, 74], [662, 79, 666, 77], [662, 80, 666, 78], [662, 83, 666, 81, "padding"], [662, 90, 666, 88], [662, 91, 666, 89], [662, 92, 666, 90], [663, 12, 667, 10], [663, 18, 667, 16, "paddedHeight"], [663, 30, 667, 28], [663, 33, 667, 31, "Math"], [663, 37, 667, 35], [663, 38, 667, 36, "min"], [663, 41, 667, 39], [663, 42, 667, 40, "img"], [663, 45, 667, 43], [663, 46, 667, 44, "height"], [663, 52, 667, 50], [663, 55, 667, 53, "paddedY"], [663, 62, 667, 60], [663, 64, 667, 62, "faceHeight"], [663, 74, 667, 72], [663, 78, 667, 76], [663, 79, 667, 77], [663, 82, 667, 80], [663, 83, 667, 81], [663, 86, 667, 84, "padding"], [663, 93, 667, 91], [663, 94, 667, 92], [663, 95, 667, 93], [664, 12, 669, 10, "console"], [664, 19, 669, 17], [664, 20, 669, 18, "log"], [664, 23, 669, 21], [664, 24, 669, 22], [664, 60, 669, 58, "index"], [664, 65, 669, 63], [664, 68, 669, 66], [664, 69, 669, 67], [664, 72, 669, 70], [664, 74, 669, 72], [665, 14, 670, 12, "original"], [665, 22, 670, 20], [665, 24, 670, 22], [666, 16, 670, 24, "x"], [666, 17, 670, 25], [666, 19, 670, 27, "Math"], [666, 23, 670, 31], [666, 24, 670, 32, "round"], [666, 29, 670, 37], [666, 30, 670, 38, "faceX"], [666, 35, 670, 43], [666, 36, 670, 44], [667, 16, 670, 46, "y"], [667, 17, 670, 47], [667, 19, 670, 49, "Math"], [667, 23, 670, 53], [667, 24, 670, 54, "round"], [667, 29, 670, 59], [667, 30, 670, 60, "faceY"], [667, 35, 670, 65], [667, 36, 670, 66], [668, 16, 670, 68, "w"], [668, 17, 670, 69], [668, 19, 670, 71, "Math"], [668, 23, 670, 75], [668, 24, 670, 76, "round"], [668, 29, 670, 81], [668, 30, 670, 82, "faceWidth"], [668, 39, 670, 91], [668, 40, 670, 92], [669, 16, 670, 94, "h"], [669, 17, 670, 95], [669, 19, 670, 97, "Math"], [669, 23, 670, 101], [669, 24, 670, 102, "round"], [669, 29, 670, 107], [669, 30, 670, 108, "faceHeight"], [669, 40, 670, 118], [670, 14, 670, 120], [670, 15, 670, 121], [671, 14, 671, 12, "padded"], [671, 20, 671, 18], [671, 22, 671, 20], [672, 16, 671, 22, "x"], [672, 17, 671, 23], [672, 19, 671, 25, "Math"], [672, 23, 671, 29], [672, 24, 671, 30, "round"], [672, 29, 671, 35], [672, 30, 671, 36, "paddedX"], [672, 37, 671, 43], [672, 38, 671, 44], [673, 16, 671, 46, "y"], [673, 17, 671, 47], [673, 19, 671, 49, "Math"], [673, 23, 671, 53], [673, 24, 671, 54, "round"], [673, 29, 671, 59], [673, 30, 671, 60, "paddedY"], [673, 37, 671, 67], [673, 38, 671, 68], [674, 16, 671, 70, "w"], [674, 17, 671, 71], [674, 19, 671, 73, "Math"], [674, 23, 671, 77], [674, 24, 671, 78, "round"], [674, 29, 671, 83], [674, 30, 671, 84, "<PERSON><PERSON><PERSON><PERSON>"], [674, 41, 671, 95], [674, 42, 671, 96], [675, 16, 671, 98, "h"], [675, 17, 671, 99], [675, 19, 671, 101, "Math"], [675, 23, 671, 105], [675, 24, 671, 106, "round"], [675, 29, 671, 111], [675, 30, 671, 112, "paddedHeight"], [675, 42, 671, 124], [676, 14, 671, 126], [677, 12, 672, 10], [677, 13, 672, 11], [677, 14, 672, 12], [679, 12, 674, 10], [680, 12, 675, 10, "console"], [680, 19, 675, 17], [680, 20, 675, 18, "log"], [680, 23, 675, 21], [680, 24, 675, 22], [680, 70, 675, 68], [680, 72, 675, 70], [681, 14, 676, 12, "width"], [681, 19, 676, 17], [681, 21, 676, 19, "canvas"], [681, 27, 676, 25], [681, 28, 676, 26, "width"], [681, 33, 676, 31], [682, 14, 677, 12, "height"], [682, 20, 677, 18], [682, 22, 677, 20, "canvas"], [682, 28, 677, 26], [682, 29, 677, 27, "height"], [682, 35, 677, 33], [683, 14, 678, 12, "contextValid"], [683, 26, 678, 24], [683, 28, 678, 26], [683, 29, 678, 27], [683, 30, 678, 28, "ctx"], [684, 12, 679, 10], [684, 13, 679, 11], [684, 14, 679, 12], [686, 12, 681, 10], [687, 12, 682, 10, "applyStrongBlur"], [687, 27, 682, 25], [687, 28, 682, 26, "ctx"], [687, 31, 682, 29], [687, 33, 682, 31, "paddedX"], [687, 40, 682, 38], [687, 42, 682, 40, "paddedY"], [687, 49, 682, 47], [687, 51, 682, 49, "<PERSON><PERSON><PERSON><PERSON>"], [687, 62, 682, 60], [687, 64, 682, 62, "paddedHeight"], [687, 76, 682, 74], [687, 77, 682, 75], [689, 12, 684, 10], [690, 12, 685, 10, "console"], [690, 19, 685, 17], [690, 20, 685, 18, "log"], [690, 23, 685, 21], [690, 24, 685, 22], [690, 102, 685, 100], [690, 103, 685, 101], [692, 12, 687, 10], [693, 12, 688, 10], [693, 18, 688, 16, "testImageData"], [693, 31, 688, 29], [693, 34, 688, 32, "ctx"], [693, 37, 688, 35], [693, 38, 688, 36, "getImageData"], [693, 50, 688, 48], [693, 51, 688, 49, "paddedX"], [693, 58, 688, 56], [693, 61, 688, 59], [693, 63, 688, 61], [693, 65, 688, 63, "paddedY"], [693, 72, 688, 70], [693, 75, 688, 73], [693, 77, 688, 75], [693, 79, 688, 77], [693, 81, 688, 79], [693, 83, 688, 81], [693, 85, 688, 83], [693, 86, 688, 84], [694, 12, 689, 10, "console"], [694, 19, 689, 17], [694, 20, 689, 18, "log"], [694, 23, 689, 21], [694, 24, 689, 22], [694, 70, 689, 68], [694, 72, 689, 70], [695, 14, 690, 12, "firstPixel"], [695, 24, 690, 22], [695, 26, 690, 24], [695, 27, 690, 25, "testImageData"], [695, 40, 690, 38], [695, 41, 690, 39, "data"], [695, 45, 690, 43], [695, 46, 690, 44], [695, 47, 690, 45], [695, 48, 690, 46], [695, 50, 690, 48, "testImageData"], [695, 63, 690, 61], [695, 64, 690, 62, "data"], [695, 68, 690, 66], [695, 69, 690, 67], [695, 70, 690, 68], [695, 71, 690, 69], [695, 73, 690, 71, "testImageData"], [695, 86, 690, 84], [695, 87, 690, 85, "data"], [695, 91, 690, 89], [695, 92, 690, 90], [695, 93, 690, 91], [695, 94, 690, 92], [695, 95, 690, 93], [696, 14, 691, 12, "secondPixel"], [696, 25, 691, 23], [696, 27, 691, 25], [696, 28, 691, 26, "testImageData"], [696, 41, 691, 39], [696, 42, 691, 40, "data"], [696, 46, 691, 44], [696, 47, 691, 45], [696, 48, 691, 46], [696, 49, 691, 47], [696, 51, 691, 49, "testImageData"], [696, 64, 691, 62], [696, 65, 691, 63, "data"], [696, 69, 691, 67], [696, 70, 691, 68], [696, 71, 691, 69], [696, 72, 691, 70], [696, 74, 691, 72, "testImageData"], [696, 87, 691, 85], [696, 88, 691, 86, "data"], [696, 92, 691, 90], [696, 93, 691, 91], [696, 94, 691, 92], [696, 95, 691, 93], [697, 12, 692, 10], [697, 13, 692, 11], [697, 14, 692, 12], [698, 12, 694, 10, "console"], [698, 19, 694, 17], [698, 20, 694, 18, "log"], [698, 23, 694, 21], [698, 24, 694, 22], [698, 50, 694, 48, "index"], [698, 55, 694, 53], [698, 58, 694, 56], [698, 59, 694, 57], [698, 79, 694, 77], [698, 80, 694, 78], [699, 10, 695, 8], [699, 11, 695, 9], [699, 12, 695, 10], [700, 10, 697, 8, "console"], [700, 17, 697, 15], [700, 18, 697, 16, "log"], [700, 21, 697, 19], [700, 22, 697, 20], [700, 48, 697, 46, "detectedFaces"], [700, 61, 697, 59], [700, 62, 697, 60, "length"], [700, 68, 697, 66], [700, 104, 697, 102], [700, 105, 697, 103], [701, 8, 698, 6], [701, 9, 698, 7], [701, 15, 698, 13], [702, 10, 699, 8, "console"], [702, 17, 699, 15], [702, 18, 699, 16, "log"], [702, 21, 699, 19], [702, 22, 699, 20], [702, 109, 699, 107], [702, 110, 699, 108], [703, 10, 700, 8], [704, 10, 701, 8, "applyFallbackFaceBlur"], [704, 31, 701, 29], [704, 32, 701, 30, "ctx"], [704, 35, 701, 33], [704, 37, 701, 35, "img"], [704, 40, 701, 38], [704, 41, 701, 39, "width"], [704, 46, 701, 44], [704, 48, 701, 46, "img"], [704, 51, 701, 49], [704, 52, 701, 50, "height"], [704, 58, 701, 56], [704, 59, 701, 57], [705, 8, 702, 6], [706, 8, 704, 6, "setProcessingProgress"], [706, 29, 704, 27], [706, 30, 704, 28], [706, 32, 704, 30], [706, 33, 704, 31], [708, 8, 706, 6], [709, 8, 707, 6, "console"], [709, 15, 707, 13], [709, 16, 707, 14, "log"], [709, 19, 707, 17], [709, 20, 707, 18], [709, 85, 707, 83], [709, 86, 707, 84], [710, 8, 708, 6], [710, 14, 708, 12, "blurredImageBlob"], [710, 30, 708, 28], [710, 33, 708, 31], [710, 39, 708, 37], [710, 43, 708, 41, "Promise"], [710, 50, 708, 48], [710, 51, 708, 56, "resolve"], [710, 58, 708, 63], [710, 62, 708, 68], [711, 10, 709, 8, "canvas"], [711, 16, 709, 14], [711, 17, 709, 15, "toBlob"], [711, 23, 709, 21], [711, 24, 709, 23, "blob"], [711, 28, 709, 27], [711, 32, 709, 32, "resolve"], [711, 39, 709, 39], [711, 40, 709, 40, "blob"], [711, 44, 709, 45], [711, 45, 709, 46], [711, 47, 709, 48], [711, 59, 709, 60], [711, 61, 709, 62], [711, 64, 709, 65], [711, 65, 709, 66], [712, 8, 710, 6], [712, 9, 710, 7], [712, 10, 710, 8], [713, 8, 712, 6], [713, 14, 712, 12, "blurredImageUrl"], [713, 29, 712, 27], [713, 32, 712, 30, "URL"], [713, 35, 712, 33], [713, 36, 712, 34, "createObjectURL"], [713, 51, 712, 49], [713, 52, 712, 50, "blurredImageBlob"], [713, 68, 712, 66], [713, 69, 712, 67], [714, 8, 713, 6, "console"], [714, 15, 713, 13], [714, 16, 713, 14, "log"], [714, 19, 713, 17], [714, 20, 713, 18], [714, 66, 713, 64], [714, 68, 713, 66, "blurredImageUrl"], [714, 83, 713, 81], [714, 84, 713, 82, "substring"], [714, 93, 713, 91], [714, 94, 713, 92], [714, 95, 713, 93], [714, 97, 713, 95], [714, 99, 713, 97], [714, 100, 713, 98], [714, 103, 713, 101], [714, 108, 713, 106], [714, 109, 713, 107], [716, 8, 715, 6], [717, 8, 716, 6, "setCapturedPhoto"], [717, 24, 716, 22], [717, 25, 716, 23, "blurredImageUrl"], [717, 40, 716, 38], [717, 41, 716, 39], [718, 8, 717, 6, "console"], [718, 15, 717, 13], [718, 16, 717, 14, "log"], [718, 19, 717, 17], [718, 20, 717, 18], [718, 87, 717, 85], [718, 88, 717, 86], [719, 8, 719, 6, "setProcessingProgress"], [719, 29, 719, 27], [719, 30, 719, 28], [719, 33, 719, 31], [719, 34, 719, 32], [721, 8, 721, 6], [722, 8, 722, 6], [722, 14, 722, 12, "completeProcessing"], [722, 32, 722, 30], [722, 33, 722, 31, "blurredImageUrl"], [722, 48, 722, 46], [722, 49, 722, 47], [723, 6, 724, 4], [723, 7, 724, 5], [723, 8, 724, 6], [723, 15, 724, 13, "error"], [723, 20, 724, 18], [723, 22, 724, 20], [724, 8, 725, 6, "console"], [724, 15, 725, 13], [724, 16, 725, 14, "error"], [724, 21, 725, 19], [724, 22, 725, 20], [724, 57, 725, 55], [724, 59, 725, 57, "error"], [724, 64, 725, 62], [724, 65, 725, 63], [725, 8, 726, 6, "setErrorMessage"], [725, 23, 726, 21], [725, 24, 726, 22], [725, 50, 726, 48], [725, 51, 726, 49], [726, 8, 727, 6, "setProcessingState"], [726, 26, 727, 24], [726, 27, 727, 25], [726, 34, 727, 32], [726, 35, 727, 33], [727, 6, 728, 4], [728, 4, 729, 2], [728, 5, 729, 3], [730, 4, 731, 2], [731, 4, 732, 2], [731, 10, 732, 8, "completeProcessing"], [731, 28, 732, 26], [731, 31, 732, 29], [731, 37, 732, 36, "blurredImageUrl"], [731, 52, 732, 59], [731, 56, 732, 64], [732, 6, 733, 4], [732, 10, 733, 8], [733, 8, 734, 6, "setProcessingState"], [733, 26, 734, 24], [733, 27, 734, 25], [733, 37, 734, 35], [733, 38, 734, 36], [735, 8, 736, 6], [736, 8, 737, 6], [736, 14, 737, 12, "timestamp"], [736, 23, 737, 21], [736, 26, 737, 24, "Date"], [736, 30, 737, 28], [736, 31, 737, 29, "now"], [736, 34, 737, 32], [736, 35, 737, 33], [736, 36, 737, 34], [737, 8, 738, 6], [737, 14, 738, 12, "result"], [737, 20, 738, 18], [737, 23, 738, 21], [738, 10, 739, 8, "imageUrl"], [738, 18, 739, 16], [738, 20, 739, 18, "blurredImageUrl"], [738, 35, 739, 33], [739, 10, 740, 8, "localUri"], [739, 18, 740, 16], [739, 20, 740, 18, "blurredImageUrl"], [739, 35, 740, 33], [740, 10, 741, 8, "challengeCode"], [740, 23, 741, 21], [740, 25, 741, 23, "challengeCode"], [740, 38, 741, 36], [740, 42, 741, 40], [740, 44, 741, 42], [741, 10, 742, 8, "timestamp"], [741, 19, 742, 17], [742, 10, 743, 8, "jobId"], [742, 15, 743, 13], [742, 17, 743, 15], [742, 27, 743, 25, "timestamp"], [742, 36, 743, 34], [742, 38, 743, 36], [743, 10, 744, 8, "status"], [743, 16, 744, 14], [743, 18, 744, 16], [744, 8, 745, 6], [744, 9, 745, 7], [745, 8, 747, 6, "console"], [745, 15, 747, 13], [745, 16, 747, 14, "log"], [745, 19, 747, 17], [745, 20, 747, 18], [745, 100, 747, 98], [745, 102, 747, 100], [746, 10, 748, 8, "imageUrl"], [746, 18, 748, 16], [746, 20, 748, 18, "blurredImageUrl"], [746, 35, 748, 33], [746, 36, 748, 34, "substring"], [746, 45, 748, 43], [746, 46, 748, 44], [746, 47, 748, 45], [746, 49, 748, 47], [746, 51, 748, 49], [746, 52, 748, 50], [746, 55, 748, 53], [746, 60, 748, 58], [747, 10, 749, 8, "timestamp"], [747, 19, 749, 17], [748, 10, 750, 8, "jobId"], [748, 15, 750, 13], [748, 17, 750, 15, "result"], [748, 23, 750, 21], [748, 24, 750, 22, "jobId"], [749, 8, 751, 6], [749, 9, 751, 7], [749, 10, 751, 8], [751, 8, 753, 6], [752, 8, 754, 6, "onComplete"], [752, 18, 754, 16], [752, 19, 754, 17, "result"], [752, 25, 754, 23], [752, 26, 754, 24], [753, 6, 756, 4], [753, 7, 756, 5], [753, 8, 756, 6], [753, 15, 756, 13, "error"], [753, 20, 756, 18], [753, 22, 756, 20], [754, 8, 757, 6, "console"], [754, 15, 757, 13], [754, 16, 757, 14, "error"], [754, 21, 757, 19], [754, 22, 757, 20], [754, 57, 757, 55], [754, 59, 757, 57, "error"], [754, 64, 757, 62], [754, 65, 757, 63], [755, 8, 758, 6, "setErrorMessage"], [755, 23, 758, 21], [755, 24, 758, 22], [755, 56, 758, 54], [755, 57, 758, 55], [756, 8, 759, 6, "setProcessingState"], [756, 26, 759, 24], [756, 27, 759, 25], [756, 34, 759, 32], [756, 35, 759, 33], [757, 6, 760, 4], [758, 4, 761, 2], [758, 5, 761, 3], [760, 4, 763, 2], [761, 4, 764, 2], [761, 10, 764, 8, "triggerServerProcessing"], [761, 33, 764, 31], [761, 36, 764, 34], [761, 42, 764, 34, "triggerServerProcessing"], [761, 43, 764, 41, "privateImageUrl"], [761, 58, 764, 64], [761, 60, 764, 66, "timestamp"], [761, 69, 764, 83], [761, 74, 764, 88], [762, 6, 765, 4], [762, 10, 765, 8], [763, 8, 766, 6, "console"], [763, 15, 766, 13], [763, 16, 766, 14, "log"], [763, 19, 766, 17], [763, 20, 766, 18], [763, 74, 766, 72], [763, 76, 766, 74, "privateImageUrl"], [763, 91, 766, 89], [763, 92, 766, 90], [764, 8, 767, 6, "setProcessingState"], [764, 26, 767, 24], [764, 27, 767, 25], [764, 39, 767, 37], [764, 40, 767, 38], [765, 8, 768, 6, "setProcessingProgress"], [765, 29, 768, 27], [765, 30, 768, 28], [765, 32, 768, 30], [765, 33, 768, 31], [766, 8, 770, 6], [766, 14, 770, 12, "requestBody"], [766, 25, 770, 23], [766, 28, 770, 26], [767, 10, 771, 8, "imageUrl"], [767, 18, 771, 16], [767, 20, 771, 18, "privateImageUrl"], [767, 35, 771, 33], [768, 10, 772, 8, "userId"], [768, 16, 772, 14], [769, 10, 773, 8, "requestId"], [769, 19, 773, 17], [770, 10, 774, 8, "timestamp"], [770, 19, 774, 17], [771, 10, 775, 8, "platform"], [771, 18, 775, 16], [771, 20, 775, 18], [772, 8, 776, 6], [772, 9, 776, 7], [773, 8, 778, 6, "console"], [773, 15, 778, 13], [773, 16, 778, 14, "log"], [773, 19, 778, 17], [773, 20, 778, 18], [773, 65, 778, 63], [773, 67, 778, 65, "requestBody"], [773, 78, 778, 76], [773, 79, 778, 77], [775, 8, 780, 6], [776, 8, 781, 6], [776, 14, 781, 12, "response"], [776, 22, 781, 20], [776, 25, 781, 23], [776, 31, 781, 29, "fetch"], [776, 36, 781, 34], [776, 37, 781, 35], [776, 40, 781, 38, "API_BASE_URL"], [776, 52, 781, 50], [776, 72, 781, 70], [776, 74, 781, 72], [777, 10, 782, 8, "method"], [777, 16, 782, 14], [777, 18, 782, 16], [777, 24, 782, 22], [778, 10, 783, 8, "headers"], [778, 17, 783, 15], [778, 19, 783, 17], [779, 12, 784, 10], [779, 26, 784, 24], [779, 28, 784, 26], [779, 46, 784, 44], [780, 12, 785, 10], [780, 27, 785, 25], [780, 29, 785, 27], [780, 39, 785, 37], [780, 45, 785, 43, "getAuthToken"], [780, 57, 785, 55], [780, 58, 785, 56], [780, 59, 785, 57], [781, 10, 786, 8], [781, 11, 786, 9], [782, 10, 787, 8, "body"], [782, 14, 787, 12], [782, 16, 787, 14, "JSON"], [782, 20, 787, 18], [782, 21, 787, 19, "stringify"], [782, 30, 787, 28], [782, 31, 787, 29, "requestBody"], [782, 42, 787, 40], [783, 8, 788, 6], [783, 9, 788, 7], [783, 10, 788, 8], [784, 8, 790, 6], [784, 12, 790, 10], [784, 13, 790, 11, "response"], [784, 21, 790, 19], [784, 22, 790, 20, "ok"], [784, 24, 790, 22], [784, 26, 790, 24], [785, 10, 791, 8], [785, 16, 791, 14, "errorText"], [785, 25, 791, 23], [785, 28, 791, 26], [785, 34, 791, 32, "response"], [785, 42, 791, 40], [785, 43, 791, 41, "text"], [785, 47, 791, 45], [785, 48, 791, 46], [785, 49, 791, 47], [786, 10, 792, 8, "console"], [786, 17, 792, 15], [786, 18, 792, 16, "error"], [786, 23, 792, 21], [786, 24, 792, 22], [786, 68, 792, 66], [786, 70, 792, 68, "response"], [786, 78, 792, 76], [786, 79, 792, 77, "status"], [786, 85, 792, 83], [786, 87, 792, 85, "errorText"], [786, 96, 792, 94], [786, 97, 792, 95], [787, 10, 793, 8], [787, 16, 793, 14], [787, 20, 793, 18, "Error"], [787, 25, 793, 23], [787, 26, 793, 24], [787, 48, 793, 46, "response"], [787, 56, 793, 54], [787, 57, 793, 55, "status"], [787, 63, 793, 61], [787, 67, 793, 65, "response"], [787, 75, 793, 73], [787, 76, 793, 74, "statusText"], [787, 86, 793, 84], [787, 88, 793, 86], [787, 89, 793, 87], [788, 8, 794, 6], [789, 8, 796, 6], [789, 14, 796, 12, "result"], [789, 20, 796, 18], [789, 23, 796, 21], [789, 29, 796, 27, "response"], [789, 37, 796, 35], [789, 38, 796, 36, "json"], [789, 42, 796, 40], [789, 43, 796, 41], [789, 44, 796, 42], [790, 8, 797, 6, "console"], [790, 15, 797, 13], [790, 16, 797, 14, "log"], [790, 19, 797, 17], [790, 20, 797, 18], [790, 68, 797, 66], [790, 70, 797, 68, "result"], [790, 76, 797, 74], [790, 77, 797, 75], [791, 8, 799, 6], [791, 12, 799, 10], [791, 13, 799, 11, "result"], [791, 19, 799, 17], [791, 20, 799, 18, "jobId"], [791, 25, 799, 23], [791, 27, 799, 25], [792, 10, 800, 8], [792, 16, 800, 14], [792, 20, 800, 18, "Error"], [792, 25, 800, 23], [792, 26, 800, 24], [792, 70, 800, 68], [792, 71, 800, 69], [793, 8, 801, 6], [795, 8, 803, 6], [796, 8, 804, 6], [796, 14, 804, 12, "pollForCompletion"], [796, 31, 804, 29], [796, 32, 804, 30, "result"], [796, 38, 804, 36], [796, 39, 804, 37, "jobId"], [796, 44, 804, 42], [796, 46, 804, 44, "timestamp"], [796, 55, 804, 53], [796, 56, 804, 54], [797, 6, 805, 4], [797, 7, 805, 5], [797, 8, 805, 6], [797, 15, 805, 13, "error"], [797, 20, 805, 18], [797, 22, 805, 20], [798, 8, 806, 6, "console"], [798, 15, 806, 13], [798, 16, 806, 14, "error"], [798, 21, 806, 19], [798, 22, 806, 20], [798, 57, 806, 55], [798, 59, 806, 57, "error"], [798, 64, 806, 62], [798, 65, 806, 63], [799, 8, 807, 6, "setErrorMessage"], [799, 23, 807, 21], [799, 24, 807, 22], [799, 52, 807, 50, "error"], [799, 57, 807, 55], [799, 58, 807, 56, "message"], [799, 65, 807, 63], [799, 67, 807, 65], [799, 68, 807, 66], [800, 8, 808, 6, "setProcessingState"], [800, 26, 808, 24], [800, 27, 808, 25], [800, 34, 808, 32], [800, 35, 808, 33], [801, 6, 809, 4], [802, 4, 810, 2], [802, 5, 810, 3], [803, 4, 811, 2], [804, 4, 812, 2], [804, 10, 812, 8, "pollForCompletion"], [804, 27, 812, 25], [804, 30, 812, 28], [804, 36, 812, 28, "pollForCompletion"], [804, 37, 812, 35, "jobId"], [804, 42, 812, 48], [804, 44, 812, 50, "timestamp"], [804, 53, 812, 67], [804, 55, 812, 69, "attempts"], [804, 63, 812, 77], [804, 66, 812, 80], [804, 67, 812, 81], [804, 72, 812, 86], [805, 6, 813, 4], [805, 12, 813, 10, "MAX_ATTEMPTS"], [805, 24, 813, 22], [805, 27, 813, 25], [805, 29, 813, 27], [805, 30, 813, 28], [805, 31, 813, 29], [806, 6, 814, 4], [806, 12, 814, 10, "POLL_INTERVAL"], [806, 25, 814, 23], [806, 28, 814, 26], [806, 32, 814, 30], [806, 33, 814, 31], [806, 34, 814, 32], [808, 6, 816, 4, "console"], [808, 13, 816, 11], [808, 14, 816, 12, "log"], [808, 17, 816, 15], [808, 18, 816, 16], [808, 53, 816, 51, "attempts"], [808, 61, 816, 59], [808, 64, 816, 62], [808, 65, 816, 63], [808, 69, 816, 67, "MAX_ATTEMPTS"], [808, 81, 816, 79], [808, 93, 816, 91, "jobId"], [808, 98, 816, 96], [808, 100, 816, 98], [808, 101, 816, 99], [809, 6, 818, 4], [809, 10, 818, 8, "attempts"], [809, 18, 818, 16], [809, 22, 818, 20, "MAX_ATTEMPTS"], [809, 34, 818, 32], [809, 36, 818, 34], [810, 8, 819, 6, "console"], [810, 15, 819, 13], [810, 16, 819, 14, "error"], [810, 21, 819, 19], [810, 22, 819, 20], [810, 75, 819, 73], [810, 76, 819, 74], [811, 8, 820, 6, "setErrorMessage"], [811, 23, 820, 21], [811, 24, 820, 22], [811, 63, 820, 61], [811, 64, 820, 62], [812, 8, 821, 6, "setProcessingState"], [812, 26, 821, 24], [812, 27, 821, 25], [812, 34, 821, 32], [812, 35, 821, 33], [813, 8, 822, 6], [814, 6, 823, 4], [815, 6, 825, 4], [815, 10, 825, 8], [816, 8, 826, 6], [816, 14, 826, 12, "response"], [816, 22, 826, 20], [816, 25, 826, 23], [816, 31, 826, 29, "fetch"], [816, 36, 826, 34], [816, 37, 826, 35], [816, 40, 826, 38, "API_BASE_URL"], [816, 52, 826, 50], [816, 75, 826, 73, "jobId"], [816, 80, 826, 78], [816, 82, 826, 80], [816, 84, 826, 82], [817, 10, 827, 8, "headers"], [817, 17, 827, 15], [817, 19, 827, 17], [818, 12, 828, 10], [818, 27, 828, 25], [818, 29, 828, 27], [818, 39, 828, 37], [818, 45, 828, 43, "getAuthToken"], [818, 57, 828, 55], [818, 58, 828, 56], [818, 59, 828, 57], [819, 10, 829, 8], [820, 8, 830, 6], [820, 9, 830, 7], [820, 10, 830, 8], [821, 8, 832, 6], [821, 12, 832, 10], [821, 13, 832, 11, "response"], [821, 21, 832, 19], [821, 22, 832, 20, "ok"], [821, 24, 832, 22], [821, 26, 832, 24], [822, 10, 833, 8], [822, 16, 833, 14], [822, 20, 833, 18, "Error"], [822, 25, 833, 23], [822, 26, 833, 24], [822, 34, 833, 32, "response"], [822, 42, 833, 40], [822, 43, 833, 41, "status"], [822, 49, 833, 47], [822, 54, 833, 52, "response"], [822, 62, 833, 60], [822, 63, 833, 61, "statusText"], [822, 73, 833, 71], [822, 75, 833, 73], [822, 76, 833, 74], [823, 8, 834, 6], [824, 8, 836, 6], [824, 14, 836, 12, "status"], [824, 20, 836, 18], [824, 23, 836, 21], [824, 29, 836, 27, "response"], [824, 37, 836, 35], [824, 38, 836, 36, "json"], [824, 42, 836, 40], [824, 43, 836, 41], [824, 44, 836, 42], [825, 8, 837, 6, "console"], [825, 15, 837, 13], [825, 16, 837, 14, "log"], [825, 19, 837, 17], [825, 20, 837, 18], [825, 54, 837, 52], [825, 56, 837, 54, "status"], [825, 62, 837, 60], [825, 63, 837, 61], [826, 8, 839, 6], [826, 12, 839, 10, "status"], [826, 18, 839, 16], [826, 19, 839, 17, "status"], [826, 25, 839, 23], [826, 30, 839, 28], [826, 41, 839, 39], [826, 43, 839, 41], [827, 10, 840, 8, "console"], [827, 17, 840, 15], [827, 18, 840, 16, "log"], [827, 21, 840, 19], [827, 22, 840, 20], [827, 73, 840, 71], [827, 74, 840, 72], [828, 10, 841, 8, "setProcessingProgress"], [828, 31, 841, 29], [828, 32, 841, 30], [828, 35, 841, 33], [828, 36, 841, 34], [829, 10, 842, 8, "setProcessingState"], [829, 28, 842, 26], [829, 29, 842, 27], [829, 40, 842, 38], [829, 41, 842, 39], [830, 10, 843, 8], [831, 10, 844, 8], [831, 16, 844, 14, "result"], [831, 22, 844, 20], [831, 25, 844, 23], [832, 12, 845, 10, "imageUrl"], [832, 20, 845, 18], [832, 22, 845, 20, "status"], [832, 28, 845, 26], [832, 29, 845, 27, "publicUrl"], [832, 38, 845, 36], [833, 12, 845, 38], [834, 12, 846, 10, "localUri"], [834, 20, 846, 18], [834, 22, 846, 20, "capturedPhoto"], [834, 35, 846, 33], [834, 39, 846, 37, "status"], [834, 45, 846, 43], [834, 46, 846, 44, "publicUrl"], [834, 55, 846, 53], [835, 12, 846, 55], [836, 12, 847, 10, "challengeCode"], [836, 25, 847, 23], [836, 27, 847, 25, "challengeCode"], [836, 40, 847, 38], [836, 44, 847, 42], [836, 46, 847, 44], [837, 12, 848, 10, "timestamp"], [837, 21, 848, 19], [838, 12, 849, 10, "processingStatus"], [838, 28, 849, 26], [838, 30, 849, 28], [839, 10, 850, 8], [839, 11, 850, 9], [840, 10, 851, 8, "console"], [840, 17, 851, 15], [840, 18, 851, 16, "log"], [840, 21, 851, 19], [840, 22, 851, 20], [840, 57, 851, 55], [840, 59, 851, 57, "result"], [840, 65, 851, 63], [840, 66, 851, 64], [841, 10, 852, 8, "onComplete"], [841, 20, 852, 18], [841, 21, 852, 19, "result"], [841, 27, 852, 25], [841, 28, 852, 26], [842, 10, 853, 8], [843, 8, 854, 6], [843, 9, 854, 7], [843, 15, 854, 13], [843, 19, 854, 17, "status"], [843, 25, 854, 23], [843, 26, 854, 24, "status"], [843, 32, 854, 30], [843, 37, 854, 35], [843, 45, 854, 43], [843, 47, 854, 45], [844, 10, 855, 8, "console"], [844, 17, 855, 15], [844, 18, 855, 16, "error"], [844, 23, 855, 21], [844, 24, 855, 22], [844, 60, 855, 58], [844, 62, 855, 60, "status"], [844, 68, 855, 66], [844, 69, 855, 67, "error"], [844, 74, 855, 72], [844, 75, 855, 73], [845, 10, 856, 8], [845, 16, 856, 14], [845, 20, 856, 18, "Error"], [845, 25, 856, 23], [845, 26, 856, 24, "status"], [845, 32, 856, 30], [845, 33, 856, 31, "error"], [845, 38, 856, 36], [845, 42, 856, 40], [845, 61, 856, 59], [845, 62, 856, 60], [846, 8, 857, 6], [846, 9, 857, 7], [846, 15, 857, 13], [847, 10, 858, 8], [848, 10, 859, 8], [848, 16, 859, 14, "progressValue"], [848, 29, 859, 27], [848, 32, 859, 30], [848, 34, 859, 32], [848, 37, 859, 36, "attempts"], [848, 45, 859, 44], [848, 48, 859, 47, "MAX_ATTEMPTS"], [848, 60, 859, 59], [848, 63, 859, 63], [848, 65, 859, 65], [849, 10, 860, 8, "console"], [849, 17, 860, 15], [849, 18, 860, 16, "log"], [849, 21, 860, 19], [849, 22, 860, 20], [849, 71, 860, 69, "progressValue"], [849, 84, 860, 82], [849, 87, 860, 85], [849, 88, 860, 86], [850, 10, 861, 8, "setProcessingProgress"], [850, 31, 861, 29], [850, 32, 861, 30, "progressValue"], [850, 45, 861, 43], [850, 46, 861, 44], [851, 10, 863, 8, "setTimeout"], [851, 20, 863, 18], [851, 21, 863, 19], [851, 27, 863, 25], [852, 12, 864, 10, "pollForCompletion"], [852, 29, 864, 27], [852, 30, 864, 28, "jobId"], [852, 35, 864, 33], [852, 37, 864, 35, "timestamp"], [852, 46, 864, 44], [852, 48, 864, 46, "attempts"], [852, 56, 864, 54], [852, 59, 864, 57], [852, 60, 864, 58], [852, 61, 864, 59], [853, 10, 865, 8], [853, 11, 865, 9], [853, 13, 865, 11, "POLL_INTERVAL"], [853, 26, 865, 24], [853, 27, 865, 25], [854, 8, 866, 6], [855, 6, 867, 4], [855, 7, 867, 5], [855, 8, 867, 6], [855, 15, 867, 13, "error"], [855, 20, 867, 18], [855, 22, 867, 20], [856, 8, 868, 6, "console"], [856, 15, 868, 13], [856, 16, 868, 14, "error"], [856, 21, 868, 19], [856, 22, 868, 20], [856, 54, 868, 52], [856, 56, 868, 54, "error"], [856, 61, 868, 59], [856, 62, 868, 60], [857, 8, 869, 6, "setErrorMessage"], [857, 23, 869, 21], [857, 24, 869, 22], [857, 62, 869, 60, "error"], [857, 67, 869, 65], [857, 68, 869, 66, "message"], [857, 75, 869, 73], [857, 77, 869, 75], [857, 78, 869, 76], [858, 8, 870, 6, "setProcessingState"], [858, 26, 870, 24], [858, 27, 870, 25], [858, 34, 870, 32], [858, 35, 870, 33], [859, 6, 871, 4], [860, 4, 872, 2], [860, 5, 872, 3], [861, 4, 873, 2], [862, 4, 874, 2], [862, 10, 874, 8, "getAuthToken"], [862, 22, 874, 20], [862, 25, 874, 23], [862, 31, 874, 23, "getAuthToken"], [862, 32, 874, 23], [862, 37, 874, 52], [863, 6, 875, 4], [864, 6, 876, 4], [865, 6, 877, 4], [865, 13, 877, 11], [865, 30, 877, 28], [866, 4, 878, 2], [866, 5, 878, 3], [868, 4, 880, 2], [869, 4, 881, 2], [869, 10, 881, 8, "retryCapture"], [869, 22, 881, 20], [869, 25, 881, 23], [869, 29, 881, 23, "useCallback"], [869, 47, 881, 34], [869, 49, 881, 35], [869, 55, 881, 41], [870, 6, 882, 4, "console"], [870, 13, 882, 11], [870, 14, 882, 12, "log"], [870, 17, 882, 15], [870, 18, 882, 16], [870, 55, 882, 53], [870, 56, 882, 54], [871, 6, 883, 4, "setProcessingState"], [871, 24, 883, 22], [871, 25, 883, 23], [871, 31, 883, 29], [871, 32, 883, 30], [872, 6, 884, 4, "setErrorMessage"], [872, 21, 884, 19], [872, 22, 884, 20], [872, 24, 884, 22], [872, 25, 884, 23], [873, 6, 885, 4, "setCapturedPhoto"], [873, 22, 885, 20], [873, 23, 885, 21], [873, 25, 885, 23], [873, 26, 885, 24], [874, 6, 886, 4, "setProcessingProgress"], [874, 27, 886, 25], [874, 28, 886, 26], [874, 29, 886, 27], [874, 30, 886, 28], [875, 4, 887, 2], [875, 5, 887, 3], [875, 7, 887, 5], [875, 9, 887, 7], [875, 10, 887, 8], [876, 4, 888, 2], [877, 4, 889, 2], [877, 8, 889, 2, "useEffect"], [877, 24, 889, 11], [877, 26, 889, 12], [877, 32, 889, 18], [878, 6, 890, 4, "console"], [878, 13, 890, 11], [878, 14, 890, 12, "log"], [878, 17, 890, 15], [878, 18, 890, 16], [878, 53, 890, 51], [878, 55, 890, 53, "permission"], [878, 65, 890, 63], [878, 66, 890, 64], [879, 6, 891, 4], [879, 10, 891, 8, "permission"], [879, 20, 891, 18], [879, 22, 891, 20], [880, 8, 892, 6, "console"], [880, 15, 892, 13], [880, 16, 892, 14, "log"], [880, 19, 892, 17], [880, 20, 892, 18], [880, 57, 892, 55], [880, 59, 892, 57, "permission"], [880, 69, 892, 67], [880, 70, 892, 68, "granted"], [880, 77, 892, 75], [880, 78, 892, 76], [881, 6, 893, 4], [882, 4, 894, 2], [882, 5, 894, 3], [882, 7, 894, 5], [882, 8, 894, 6, "permission"], [882, 18, 894, 16], [882, 19, 894, 17], [882, 20, 894, 18], [883, 4, 895, 2], [884, 4, 896, 2], [884, 8, 896, 6], [884, 9, 896, 7, "permission"], [884, 19, 896, 17], [884, 21, 896, 19], [885, 6, 897, 4, "console"], [885, 13, 897, 11], [885, 14, 897, 12, "log"], [885, 17, 897, 15], [885, 18, 897, 16], [885, 67, 897, 65], [885, 68, 897, 66], [886, 6, 898, 4], [886, 26, 899, 6], [886, 30, 899, 6, "_jsxDevRuntime"], [886, 44, 899, 6], [886, 45, 899, 6, "jsxDEV"], [886, 51, 899, 6], [886, 53, 899, 7, "_View"], [886, 58, 899, 7], [886, 59, 899, 7, "default"], [886, 66, 899, 11], [887, 8, 899, 12, "style"], [887, 13, 899, 17], [887, 15, 899, 19, "styles"], [887, 21, 899, 25], [887, 22, 899, 26, "container"], [887, 31, 899, 36], [888, 8, 899, 36, "children"], [888, 16, 899, 36], [888, 32, 900, 8], [888, 36, 900, 8, "_jsxDevRuntime"], [888, 50, 900, 8], [888, 51, 900, 8, "jsxDEV"], [888, 57, 900, 8], [888, 59, 900, 9, "_ActivityIndicator"], [888, 77, 900, 9], [888, 78, 900, 9, "default"], [888, 85, 900, 26], [889, 10, 900, 27, "size"], [889, 14, 900, 31], [889, 16, 900, 32], [889, 23, 900, 39], [890, 10, 900, 40, "color"], [890, 15, 900, 45], [890, 17, 900, 46], [891, 8, 900, 55], [892, 10, 900, 55, "fileName"], [892, 18, 900, 55], [892, 20, 900, 55, "_jsxFileName"], [892, 32, 900, 55], [893, 10, 900, 55, "lineNumber"], [893, 20, 900, 55], [894, 10, 900, 55, "columnNumber"], [894, 22, 900, 55], [895, 8, 900, 55], [895, 15, 900, 57], [895, 16, 900, 58], [895, 31, 901, 8], [895, 35, 901, 8, "_jsxDevRuntime"], [895, 49, 901, 8], [895, 50, 901, 8, "jsxDEV"], [895, 56, 901, 8], [895, 58, 901, 9, "_Text"], [895, 63, 901, 9], [895, 64, 901, 9, "default"], [895, 71, 901, 13], [896, 10, 901, 14, "style"], [896, 15, 901, 19], [896, 17, 901, 21, "styles"], [896, 23, 901, 27], [896, 24, 901, 28, "loadingText"], [896, 35, 901, 40], [897, 10, 901, 40, "children"], [897, 18, 901, 40], [897, 20, 901, 41], [898, 8, 901, 58], [899, 10, 901, 58, "fileName"], [899, 18, 901, 58], [899, 20, 901, 58, "_jsxFileName"], [899, 32, 901, 58], [900, 10, 901, 58, "lineNumber"], [900, 20, 901, 58], [901, 10, 901, 58, "columnNumber"], [901, 22, 901, 58], [902, 8, 901, 58], [902, 15, 901, 64], [902, 16, 901, 65], [903, 6, 901, 65], [904, 8, 901, 65, "fileName"], [904, 16, 901, 65], [904, 18, 901, 65, "_jsxFileName"], [904, 30, 901, 65], [905, 8, 901, 65, "lineNumber"], [905, 18, 901, 65], [906, 8, 901, 65, "columnNumber"], [906, 20, 901, 65], [907, 6, 901, 65], [907, 13, 902, 12], [907, 14, 902, 13], [908, 4, 904, 2], [909, 4, 905, 2], [909, 8, 905, 6], [909, 9, 905, 7, "permission"], [909, 19, 905, 17], [909, 20, 905, 18, "granted"], [909, 27, 905, 25], [909, 29, 905, 27], [910, 6, 906, 4, "console"], [910, 13, 906, 11], [910, 14, 906, 12, "log"], [910, 17, 906, 15], [910, 18, 906, 16], [910, 93, 906, 91], [910, 94, 906, 92], [911, 6, 907, 4], [911, 26, 908, 6], [911, 30, 908, 6, "_jsxDevRuntime"], [911, 44, 908, 6], [911, 45, 908, 6, "jsxDEV"], [911, 51, 908, 6], [911, 53, 908, 7, "_View"], [911, 58, 908, 7], [911, 59, 908, 7, "default"], [911, 66, 908, 11], [912, 8, 908, 12, "style"], [912, 13, 908, 17], [912, 15, 908, 19, "styles"], [912, 21, 908, 25], [912, 22, 908, 26, "container"], [912, 31, 908, 36], [913, 8, 908, 36, "children"], [913, 16, 908, 36], [913, 31, 909, 8], [913, 35, 909, 8, "_jsxDevRuntime"], [913, 49, 909, 8], [913, 50, 909, 8, "jsxDEV"], [913, 56, 909, 8], [913, 58, 909, 9, "_View"], [913, 63, 909, 9], [913, 64, 909, 9, "default"], [913, 71, 909, 13], [914, 10, 909, 14, "style"], [914, 15, 909, 19], [914, 17, 909, 21, "styles"], [914, 23, 909, 27], [914, 24, 909, 28, "permissionContent"], [914, 41, 909, 46], [915, 10, 909, 46, "children"], [915, 18, 909, 46], [915, 34, 910, 10], [915, 38, 910, 10, "_jsxDevRuntime"], [915, 52, 910, 10], [915, 53, 910, 10, "jsxDEV"], [915, 59, 910, 10], [915, 61, 910, 11, "_lucideReactNative"], [915, 79, 910, 11], [915, 80, 910, 11, "Camera"], [915, 86, 910, 21], [916, 12, 910, 22, "size"], [916, 16, 910, 26], [916, 18, 910, 28], [916, 20, 910, 31], [917, 12, 910, 32, "color"], [917, 17, 910, 37], [917, 19, 910, 38], [918, 10, 910, 47], [919, 12, 910, 47, "fileName"], [919, 20, 910, 47], [919, 22, 910, 47, "_jsxFileName"], [919, 34, 910, 47], [920, 12, 910, 47, "lineNumber"], [920, 22, 910, 47], [921, 12, 910, 47, "columnNumber"], [921, 24, 910, 47], [922, 10, 910, 47], [922, 17, 910, 49], [922, 18, 910, 50], [922, 33, 911, 10], [922, 37, 911, 10, "_jsxDevRuntime"], [922, 51, 911, 10], [922, 52, 911, 10, "jsxDEV"], [922, 58, 911, 10], [922, 60, 911, 11, "_Text"], [922, 65, 911, 11], [922, 66, 911, 11, "default"], [922, 73, 911, 15], [923, 12, 911, 16, "style"], [923, 17, 911, 21], [923, 19, 911, 23, "styles"], [923, 25, 911, 29], [923, 26, 911, 30, "permissionTitle"], [923, 41, 911, 46], [924, 12, 911, 46, "children"], [924, 20, 911, 46], [924, 22, 911, 47], [925, 10, 911, 73], [926, 12, 911, 73, "fileName"], [926, 20, 911, 73], [926, 22, 911, 73, "_jsxFileName"], [926, 34, 911, 73], [927, 12, 911, 73, "lineNumber"], [927, 22, 911, 73], [928, 12, 911, 73, "columnNumber"], [928, 24, 911, 73], [929, 10, 911, 73], [929, 17, 911, 79], [929, 18, 911, 80], [929, 33, 912, 10], [929, 37, 912, 10, "_jsxDevRuntime"], [929, 51, 912, 10], [929, 52, 912, 10, "jsxDEV"], [929, 58, 912, 10], [929, 60, 912, 11, "_Text"], [929, 65, 912, 11], [929, 66, 912, 11, "default"], [929, 73, 912, 15], [930, 12, 912, 16, "style"], [930, 17, 912, 21], [930, 19, 912, 23, "styles"], [930, 25, 912, 29], [930, 26, 912, 30, "permissionDescription"], [930, 47, 912, 52], [931, 12, 912, 52, "children"], [931, 20, 912, 52], [931, 22, 912, 53], [932, 10, 915, 10], [933, 12, 915, 10, "fileName"], [933, 20, 915, 10], [933, 22, 915, 10, "_jsxFileName"], [933, 34, 915, 10], [934, 12, 915, 10, "lineNumber"], [934, 22, 915, 10], [935, 12, 915, 10, "columnNumber"], [935, 24, 915, 10], [936, 10, 915, 10], [936, 17, 915, 16], [936, 18, 915, 17], [936, 33, 916, 10], [936, 37, 916, 10, "_jsxDevRuntime"], [936, 51, 916, 10], [936, 52, 916, 10, "jsxDEV"], [936, 58, 916, 10], [936, 60, 916, 11, "_TouchableOpacity"], [936, 77, 916, 11], [936, 78, 916, 11, "default"], [936, 85, 916, 27], [937, 12, 916, 28, "onPress"], [937, 19, 916, 35], [937, 21, 916, 37, "requestPermission"], [937, 38, 916, 55], [938, 12, 916, 56, "style"], [938, 17, 916, 61], [938, 19, 916, 63, "styles"], [938, 25, 916, 69], [938, 26, 916, 70, "primaryButton"], [938, 39, 916, 84], [939, 12, 916, 84, "children"], [939, 20, 916, 84], [939, 35, 917, 12], [939, 39, 917, 12, "_jsxDevRuntime"], [939, 53, 917, 12], [939, 54, 917, 12, "jsxDEV"], [939, 60, 917, 12], [939, 62, 917, 13, "_Text"], [939, 67, 917, 13], [939, 68, 917, 13, "default"], [939, 75, 917, 17], [940, 14, 917, 18, "style"], [940, 19, 917, 23], [940, 21, 917, 25, "styles"], [940, 27, 917, 31], [940, 28, 917, 32, "primaryButtonText"], [940, 45, 917, 50], [941, 14, 917, 50, "children"], [941, 22, 917, 50], [941, 24, 917, 51], [942, 12, 917, 67], [943, 14, 917, 67, "fileName"], [943, 22, 917, 67], [943, 24, 917, 67, "_jsxFileName"], [943, 36, 917, 67], [944, 14, 917, 67, "lineNumber"], [944, 24, 917, 67], [945, 14, 917, 67, "columnNumber"], [945, 26, 917, 67], [946, 12, 917, 67], [946, 19, 917, 73], [947, 10, 917, 74], [948, 12, 917, 74, "fileName"], [948, 20, 917, 74], [948, 22, 917, 74, "_jsxFileName"], [948, 34, 917, 74], [949, 12, 917, 74, "lineNumber"], [949, 22, 917, 74], [950, 12, 917, 74, "columnNumber"], [950, 24, 917, 74], [951, 10, 917, 74], [951, 17, 918, 28], [951, 18, 918, 29], [951, 33, 919, 10], [951, 37, 919, 10, "_jsxDevRuntime"], [951, 51, 919, 10], [951, 52, 919, 10, "jsxDEV"], [951, 58, 919, 10], [951, 60, 919, 11, "_TouchableOpacity"], [951, 77, 919, 11], [951, 78, 919, 11, "default"], [951, 85, 919, 27], [952, 12, 919, 28, "onPress"], [952, 19, 919, 35], [952, 21, 919, 37, "onCancel"], [952, 29, 919, 46], [953, 12, 919, 47, "style"], [953, 17, 919, 52], [953, 19, 919, 54, "styles"], [953, 25, 919, 60], [953, 26, 919, 61, "secondaryButton"], [953, 41, 919, 77], [954, 12, 919, 77, "children"], [954, 20, 919, 77], [954, 35, 920, 12], [954, 39, 920, 12, "_jsxDevRuntime"], [954, 53, 920, 12], [954, 54, 920, 12, "jsxDEV"], [954, 60, 920, 12], [954, 62, 920, 13, "_Text"], [954, 67, 920, 13], [954, 68, 920, 13, "default"], [954, 75, 920, 17], [955, 14, 920, 18, "style"], [955, 19, 920, 23], [955, 21, 920, 25, "styles"], [955, 27, 920, 31], [955, 28, 920, 32, "secondaryButtonText"], [955, 47, 920, 52], [956, 14, 920, 52, "children"], [956, 22, 920, 52], [956, 24, 920, 53], [957, 12, 920, 59], [958, 14, 920, 59, "fileName"], [958, 22, 920, 59], [958, 24, 920, 59, "_jsxFileName"], [958, 36, 920, 59], [959, 14, 920, 59, "lineNumber"], [959, 24, 920, 59], [960, 14, 920, 59, "columnNumber"], [960, 26, 920, 59], [961, 12, 920, 59], [961, 19, 920, 65], [962, 10, 920, 66], [963, 12, 920, 66, "fileName"], [963, 20, 920, 66], [963, 22, 920, 66, "_jsxFileName"], [963, 34, 920, 66], [964, 12, 920, 66, "lineNumber"], [964, 22, 920, 66], [965, 12, 920, 66, "columnNumber"], [965, 24, 920, 66], [966, 10, 920, 66], [966, 17, 921, 28], [966, 18, 921, 29], [967, 8, 921, 29], [968, 10, 921, 29, "fileName"], [968, 18, 921, 29], [968, 20, 921, 29, "_jsxFileName"], [968, 32, 921, 29], [969, 10, 921, 29, "lineNumber"], [969, 20, 921, 29], [970, 10, 921, 29, "columnNumber"], [970, 22, 921, 29], [971, 8, 921, 29], [971, 15, 922, 14], [972, 6, 922, 15], [973, 8, 922, 15, "fileName"], [973, 16, 922, 15], [973, 18, 922, 15, "_jsxFileName"], [973, 30, 922, 15], [974, 8, 922, 15, "lineNumber"], [974, 18, 922, 15], [975, 8, 922, 15, "columnNumber"], [975, 20, 922, 15], [976, 6, 922, 15], [976, 13, 923, 12], [976, 14, 923, 13], [977, 4, 925, 2], [978, 4, 926, 2], [979, 4, 927, 2, "console"], [979, 11, 927, 9], [979, 12, 927, 10, "log"], [979, 15, 927, 13], [979, 16, 927, 14], [979, 55, 927, 53], [979, 56, 927, 54], [980, 4, 929, 2], [980, 24, 930, 4], [980, 28, 930, 4, "_jsxDevRuntime"], [980, 42, 930, 4], [980, 43, 930, 4, "jsxDEV"], [980, 49, 930, 4], [980, 51, 930, 5, "_View"], [980, 56, 930, 5], [980, 57, 930, 5, "default"], [980, 64, 930, 9], [981, 6, 930, 10, "style"], [981, 11, 930, 15], [981, 13, 930, 17, "styles"], [981, 19, 930, 23], [981, 20, 930, 24, "container"], [981, 29, 930, 34], [982, 6, 930, 34, "children"], [982, 14, 930, 34], [982, 30, 932, 6], [982, 34, 932, 6, "_jsxDevRuntime"], [982, 48, 932, 6], [982, 49, 932, 6, "jsxDEV"], [982, 55, 932, 6], [982, 57, 932, 7, "_View"], [982, 62, 932, 7], [982, 63, 932, 7, "default"], [982, 70, 932, 11], [983, 8, 932, 12, "style"], [983, 13, 932, 17], [983, 15, 932, 19, "styles"], [983, 21, 932, 25], [983, 22, 932, 26, "cameraContainer"], [983, 37, 932, 42], [984, 8, 932, 43, "id"], [984, 10, 932, 45], [984, 12, 932, 46], [984, 29, 932, 63], [985, 8, 932, 63, "children"], [985, 16, 932, 63], [985, 32, 933, 8], [985, 36, 933, 8, "_jsxDevRuntime"], [985, 50, 933, 8], [985, 51, 933, 8, "jsxDEV"], [985, 57, 933, 8], [985, 59, 933, 9, "_expoCamera"], [985, 70, 933, 9], [985, 71, 933, 9, "CameraView"], [985, 81, 933, 19], [986, 10, 934, 10, "ref"], [986, 13, 934, 13], [986, 15, 934, 15, "cameraRef"], [986, 24, 934, 25], [987, 10, 935, 10, "style"], [987, 15, 935, 15], [987, 17, 935, 17], [987, 18, 935, 18, "styles"], [987, 24, 935, 24], [987, 25, 935, 25, "camera"], [987, 31, 935, 31], [987, 33, 935, 33], [988, 12, 935, 35, "backgroundColor"], [988, 27, 935, 50], [988, 29, 935, 52], [989, 10, 935, 62], [989, 11, 935, 63], [989, 12, 935, 65], [990, 10, 936, 10, "facing"], [990, 16, 936, 16], [990, 18, 936, 17], [990, 24, 936, 23], [991, 10, 937, 10, "onLayout"], [991, 18, 937, 18], [991, 20, 937, 21, "e"], [991, 21, 937, 22], [991, 25, 937, 27], [992, 12, 938, 12, "console"], [992, 19, 938, 19], [992, 20, 938, 20, "log"], [992, 23, 938, 23], [992, 24, 938, 24], [992, 56, 938, 56], [992, 58, 938, 58, "e"], [992, 59, 938, 59], [992, 60, 938, 60, "nativeEvent"], [992, 71, 938, 71], [992, 72, 938, 72, "layout"], [992, 78, 938, 78], [992, 79, 938, 79], [993, 12, 939, 12, "setViewSize"], [993, 23, 939, 23], [993, 24, 939, 24], [994, 14, 939, 26, "width"], [994, 19, 939, 31], [994, 21, 939, 33, "e"], [994, 22, 939, 34], [994, 23, 939, 35, "nativeEvent"], [994, 34, 939, 46], [994, 35, 939, 47, "layout"], [994, 41, 939, 53], [994, 42, 939, 54, "width"], [994, 47, 939, 59], [995, 14, 939, 61, "height"], [995, 20, 939, 67], [995, 22, 939, 69, "e"], [995, 23, 939, 70], [995, 24, 939, 71, "nativeEvent"], [995, 35, 939, 82], [995, 36, 939, 83, "layout"], [995, 42, 939, 89], [995, 43, 939, 90, "height"], [996, 12, 939, 97], [996, 13, 939, 98], [996, 14, 939, 99], [997, 10, 940, 10], [997, 11, 940, 12], [998, 10, 941, 10, "onCameraReady"], [998, 23, 941, 23], [998, 25, 941, 25, "onCameraReady"], [998, 26, 941, 25], [998, 31, 941, 31], [999, 12, 942, 12, "console"], [999, 19, 942, 19], [999, 20, 942, 20, "log"], [999, 23, 942, 23], [999, 24, 942, 24], [999, 55, 942, 55], [999, 56, 942, 56], [1000, 12, 943, 12, "setIsCameraReady"], [1000, 28, 943, 28], [1000, 29, 943, 29], [1000, 33, 943, 33], [1000, 34, 943, 34], [1000, 35, 943, 35], [1000, 36, 943, 36], [1001, 10, 944, 10], [1001, 11, 944, 12], [1002, 10, 945, 10, "onMountError"], [1002, 22, 945, 22], [1002, 24, 945, 25, "error"], [1002, 29, 945, 30], [1002, 33, 945, 35], [1003, 12, 946, 12, "console"], [1003, 19, 946, 19], [1003, 20, 946, 20, "error"], [1003, 25, 946, 25], [1003, 26, 946, 26], [1003, 63, 946, 63], [1003, 65, 946, 65, "error"], [1003, 70, 946, 70], [1003, 71, 946, 71], [1004, 12, 947, 12, "setErrorMessage"], [1004, 27, 947, 27], [1004, 28, 947, 28], [1004, 57, 947, 57], [1004, 58, 947, 58], [1005, 12, 948, 12, "setProcessingState"], [1005, 30, 948, 30], [1005, 31, 948, 31], [1005, 38, 948, 38], [1005, 39, 948, 39], [1006, 10, 949, 10], [1007, 8, 949, 12], [1008, 10, 949, 12, "fileName"], [1008, 18, 949, 12], [1008, 20, 949, 12, "_jsxFileName"], [1008, 32, 949, 12], [1009, 10, 949, 12, "lineNumber"], [1009, 20, 949, 12], [1010, 10, 949, 12, "columnNumber"], [1010, 22, 949, 12], [1011, 8, 949, 12], [1011, 15, 950, 9], [1011, 16, 950, 10], [1011, 18, 952, 9], [1011, 19, 952, 10, "isCameraReady"], [1011, 32, 952, 23], [1011, 49, 953, 10], [1011, 53, 953, 10, "_jsxDevRuntime"], [1011, 67, 953, 10], [1011, 68, 953, 10, "jsxDEV"], [1011, 74, 953, 10], [1011, 76, 953, 11, "_View"], [1011, 81, 953, 11], [1011, 82, 953, 11, "default"], [1011, 89, 953, 15], [1012, 10, 953, 16, "style"], [1012, 15, 953, 21], [1012, 17, 953, 23], [1012, 18, 953, 24, "StyleSheet"], [1012, 37, 953, 34], [1012, 38, 953, 35, "absoluteFill"], [1012, 50, 953, 47], [1012, 52, 953, 49], [1013, 12, 953, 51, "backgroundColor"], [1013, 27, 953, 66], [1013, 29, 953, 68], [1013, 49, 953, 88], [1014, 12, 953, 90, "justifyContent"], [1014, 26, 953, 104], [1014, 28, 953, 106], [1014, 36, 953, 114], [1015, 12, 953, 116, "alignItems"], [1015, 22, 953, 126], [1015, 24, 953, 128], [1015, 32, 953, 136], [1016, 12, 953, 138, "zIndex"], [1016, 18, 953, 144], [1016, 20, 953, 146], [1017, 10, 953, 151], [1017, 11, 953, 152], [1017, 12, 953, 154], [1018, 10, 953, 154, "children"], [1018, 18, 953, 154], [1018, 33, 954, 12], [1018, 37, 954, 12, "_jsxDevRuntime"], [1018, 51, 954, 12], [1018, 52, 954, 12, "jsxDEV"], [1018, 58, 954, 12], [1018, 60, 954, 13, "_View"], [1018, 65, 954, 13], [1018, 66, 954, 13, "default"], [1018, 73, 954, 17], [1019, 12, 954, 18, "style"], [1019, 17, 954, 23], [1019, 19, 954, 25], [1020, 14, 954, 27, "backgroundColor"], [1020, 29, 954, 42], [1020, 31, 954, 44], [1020, 51, 954, 64], [1021, 14, 954, 66, "padding"], [1021, 21, 954, 73], [1021, 23, 954, 75], [1021, 25, 954, 77], [1022, 14, 954, 79, "borderRadius"], [1022, 26, 954, 91], [1022, 28, 954, 93], [1022, 30, 954, 95], [1023, 14, 954, 97, "alignItems"], [1023, 24, 954, 107], [1023, 26, 954, 109], [1024, 12, 954, 118], [1024, 13, 954, 120], [1025, 12, 954, 120, "children"], [1025, 20, 954, 120], [1025, 36, 955, 14], [1025, 40, 955, 14, "_jsxDevRuntime"], [1025, 54, 955, 14], [1025, 55, 955, 14, "jsxDEV"], [1025, 61, 955, 14], [1025, 63, 955, 15, "_ActivityIndicator"], [1025, 81, 955, 15], [1025, 82, 955, 15, "default"], [1025, 89, 955, 32], [1026, 14, 955, 33, "size"], [1026, 18, 955, 37], [1026, 20, 955, 38], [1026, 27, 955, 45], [1027, 14, 955, 46, "color"], [1027, 19, 955, 51], [1027, 21, 955, 52], [1027, 30, 955, 61], [1028, 14, 955, 62, "style"], [1028, 19, 955, 67], [1028, 21, 955, 69], [1029, 16, 955, 71, "marginBottom"], [1029, 28, 955, 83], [1029, 30, 955, 85], [1030, 14, 955, 88], [1031, 12, 955, 90], [1032, 14, 955, 90, "fileName"], [1032, 22, 955, 90], [1032, 24, 955, 90, "_jsxFileName"], [1032, 36, 955, 90], [1033, 14, 955, 90, "lineNumber"], [1033, 24, 955, 90], [1034, 14, 955, 90, "columnNumber"], [1034, 26, 955, 90], [1035, 12, 955, 90], [1035, 19, 955, 92], [1035, 20, 955, 93], [1035, 35, 956, 14], [1035, 39, 956, 14, "_jsxDevRuntime"], [1035, 53, 956, 14], [1035, 54, 956, 14, "jsxDEV"], [1035, 60, 956, 14], [1035, 62, 956, 15, "_Text"], [1035, 67, 956, 15], [1035, 68, 956, 15, "default"], [1035, 75, 956, 19], [1036, 14, 956, 20, "style"], [1036, 19, 956, 25], [1036, 21, 956, 27], [1037, 16, 956, 29, "color"], [1037, 21, 956, 34], [1037, 23, 956, 36], [1037, 29, 956, 42], [1038, 16, 956, 44, "fontSize"], [1038, 24, 956, 52], [1038, 26, 956, 54], [1038, 28, 956, 56], [1039, 16, 956, 58, "fontWeight"], [1039, 26, 956, 68], [1039, 28, 956, 70], [1040, 14, 956, 76], [1040, 15, 956, 78], [1041, 14, 956, 78, "children"], [1041, 22, 956, 78], [1041, 24, 956, 79], [1042, 12, 956, 101], [1043, 14, 956, 101, "fileName"], [1043, 22, 956, 101], [1043, 24, 956, 101, "_jsxFileName"], [1043, 36, 956, 101], [1044, 14, 956, 101, "lineNumber"], [1044, 24, 956, 101], [1045, 14, 956, 101, "columnNumber"], [1045, 26, 956, 101], [1046, 12, 956, 101], [1046, 19, 956, 107], [1046, 20, 956, 108], [1046, 35, 957, 14], [1046, 39, 957, 14, "_jsxDevRuntime"], [1046, 53, 957, 14], [1046, 54, 957, 14, "jsxDEV"], [1046, 60, 957, 14], [1046, 62, 957, 15, "_Text"], [1046, 67, 957, 15], [1046, 68, 957, 15, "default"], [1046, 75, 957, 19], [1047, 14, 957, 20, "style"], [1047, 19, 957, 25], [1047, 21, 957, 27], [1048, 16, 957, 29, "color"], [1048, 21, 957, 34], [1048, 23, 957, 36], [1048, 32, 957, 45], [1049, 16, 957, 47, "fontSize"], [1049, 24, 957, 55], [1049, 26, 957, 57], [1049, 28, 957, 59], [1050, 16, 957, 61, "marginTop"], [1050, 25, 957, 70], [1050, 27, 957, 72], [1051, 14, 957, 74], [1051, 15, 957, 76], [1052, 14, 957, 76, "children"], [1052, 22, 957, 76], [1052, 24, 957, 77], [1053, 12, 957, 88], [1054, 14, 957, 88, "fileName"], [1054, 22, 957, 88], [1054, 24, 957, 88, "_jsxFileName"], [1054, 36, 957, 88], [1055, 14, 957, 88, "lineNumber"], [1055, 24, 957, 88], [1056, 14, 957, 88, "columnNumber"], [1056, 26, 957, 88], [1057, 12, 957, 88], [1057, 19, 957, 94], [1057, 20, 957, 95], [1058, 10, 957, 95], [1059, 12, 957, 95, "fileName"], [1059, 20, 957, 95], [1059, 22, 957, 95, "_jsxFileName"], [1059, 34, 957, 95], [1060, 12, 957, 95, "lineNumber"], [1060, 22, 957, 95], [1061, 12, 957, 95, "columnNumber"], [1061, 24, 957, 95], [1062, 10, 957, 95], [1062, 17, 958, 18], [1063, 8, 958, 19], [1064, 10, 958, 19, "fileName"], [1064, 18, 958, 19], [1064, 20, 958, 19, "_jsxFileName"], [1064, 32, 958, 19], [1065, 10, 958, 19, "lineNumber"], [1065, 20, 958, 19], [1066, 10, 958, 19, "columnNumber"], [1066, 22, 958, 19], [1067, 8, 958, 19], [1067, 15, 959, 16], [1067, 16, 960, 9], [1067, 18, 963, 9, "isCameraReady"], [1067, 31, 963, 22], [1067, 35, 963, 26, "previewBlurEnabled"], [1067, 53, 963, 44], [1067, 57, 963, 48, "viewSize"], [1067, 65, 963, 56], [1067, 66, 963, 57, "width"], [1067, 71, 963, 62], [1067, 74, 963, 65], [1067, 75, 963, 66], [1067, 92, 964, 10], [1067, 96, 964, 10, "_jsxDevRuntime"], [1067, 110, 964, 10], [1067, 111, 964, 10, "jsxDEV"], [1067, 117, 964, 10], [1067, 119, 964, 10, "_jsxDevRuntime"], [1067, 133, 964, 10], [1067, 134, 964, 10, "Fragment"], [1067, 142, 964, 10], [1068, 10, 964, 10, "children"], [1068, 18, 964, 10], [1068, 34, 966, 12], [1068, 38, 966, 12, "_jsxDevRuntime"], [1068, 52, 966, 12], [1068, 53, 966, 12, "jsxDEV"], [1068, 59, 966, 12], [1068, 61, 966, 13, "_LiveFaceCanvas"], [1068, 76, 966, 13], [1068, 77, 966, 13, "default"], [1068, 84, 966, 27], [1069, 12, 966, 28, "containerId"], [1069, 23, 966, 39], [1069, 25, 966, 40], [1069, 42, 966, 57], [1070, 12, 966, 58, "width"], [1070, 17, 966, 63], [1070, 19, 966, 65, "viewSize"], [1070, 27, 966, 73], [1070, 28, 966, 74, "width"], [1070, 33, 966, 80], [1071, 12, 966, 81, "height"], [1071, 18, 966, 87], [1071, 20, 966, 89, "viewSize"], [1071, 28, 966, 97], [1071, 29, 966, 98, "height"], [1072, 10, 966, 105], [1073, 12, 966, 105, "fileName"], [1073, 20, 966, 105], [1073, 22, 966, 105, "_jsxFileName"], [1073, 34, 966, 105], [1074, 12, 966, 105, "lineNumber"], [1074, 22, 966, 105], [1075, 12, 966, 105, "columnNumber"], [1075, 24, 966, 105], [1076, 10, 966, 105], [1076, 17, 966, 107], [1076, 18, 966, 108], [1076, 33, 967, 12], [1076, 37, 967, 12, "_jsxDevRuntime"], [1076, 51, 967, 12], [1076, 52, 967, 12, "jsxDEV"], [1076, 58, 967, 12], [1076, 60, 967, 13, "_View"], [1076, 65, 967, 13], [1076, 66, 967, 13, "default"], [1076, 73, 967, 17], [1077, 12, 967, 18, "style"], [1077, 17, 967, 23], [1077, 19, 967, 25], [1077, 20, 967, 26, "StyleSheet"], [1077, 39, 967, 36], [1077, 40, 967, 37, "absoluteFill"], [1077, 52, 967, 49], [1077, 54, 967, 51], [1078, 14, 967, 53, "pointerEvents"], [1078, 27, 967, 66], [1078, 29, 967, 68], [1079, 12, 967, 75], [1079, 13, 967, 76], [1079, 14, 967, 78], [1080, 12, 967, 78, "children"], [1080, 20, 967, 78], [1080, 36, 969, 12], [1080, 40, 969, 12, "_jsxDevRuntime"], [1080, 54, 969, 12], [1080, 55, 969, 12, "jsxDEV"], [1080, 61, 969, 12], [1080, 63, 969, 13, "_expoBlur"], [1080, 72, 969, 13], [1080, 73, 969, 13, "BlurView"], [1080, 81, 969, 21], [1081, 14, 969, 22, "intensity"], [1081, 23, 969, 31], [1081, 25, 969, 33], [1081, 27, 969, 36], [1082, 14, 969, 37, "tint"], [1082, 18, 969, 41], [1082, 20, 969, 42], [1082, 26, 969, 48], [1083, 14, 969, 49, "style"], [1083, 19, 969, 54], [1083, 21, 969, 56], [1083, 22, 969, 57, "styles"], [1083, 28, 969, 63], [1083, 29, 969, 64, "blurZone"], [1083, 37, 969, 72], [1083, 39, 969, 74], [1084, 16, 970, 14, "left"], [1084, 20, 970, 18], [1084, 22, 970, 20], [1084, 23, 970, 21], [1085, 16, 971, 14, "top"], [1085, 19, 971, 17], [1085, 21, 971, 19, "viewSize"], [1085, 29, 971, 27], [1085, 30, 971, 28, "height"], [1085, 36, 971, 34], [1085, 39, 971, 37], [1085, 42, 971, 40], [1086, 16, 972, 14, "width"], [1086, 21, 972, 19], [1086, 23, 972, 21, "viewSize"], [1086, 31, 972, 29], [1086, 32, 972, 30, "width"], [1086, 37, 972, 35], [1087, 16, 973, 14, "height"], [1087, 22, 973, 20], [1087, 24, 973, 22, "viewSize"], [1087, 32, 973, 30], [1087, 33, 973, 31, "height"], [1087, 39, 973, 37], [1087, 42, 973, 40], [1087, 46, 973, 44], [1088, 16, 974, 14, "borderRadius"], [1088, 28, 974, 26], [1088, 30, 974, 28], [1089, 14, 975, 12], [1089, 15, 975, 13], [1090, 12, 975, 15], [1091, 14, 975, 15, "fileName"], [1091, 22, 975, 15], [1091, 24, 975, 15, "_jsxFileName"], [1091, 36, 975, 15], [1092, 14, 975, 15, "lineNumber"], [1092, 24, 975, 15], [1093, 14, 975, 15, "columnNumber"], [1093, 26, 975, 15], [1094, 12, 975, 15], [1094, 19, 975, 17], [1094, 20, 975, 18], [1094, 35, 977, 12], [1094, 39, 977, 12, "_jsxDevRuntime"], [1094, 53, 977, 12], [1094, 54, 977, 12, "jsxDEV"], [1094, 60, 977, 12], [1094, 62, 977, 13, "_expoBlur"], [1094, 71, 977, 13], [1094, 72, 977, 13, "BlurView"], [1094, 80, 977, 21], [1095, 14, 977, 22, "intensity"], [1095, 23, 977, 31], [1095, 25, 977, 33], [1095, 27, 977, 36], [1096, 14, 977, 37, "tint"], [1096, 18, 977, 41], [1096, 20, 977, 42], [1096, 26, 977, 48], [1097, 14, 977, 49, "style"], [1097, 19, 977, 54], [1097, 21, 977, 56], [1097, 22, 977, 57, "styles"], [1097, 28, 977, 63], [1097, 29, 977, 64, "blurZone"], [1097, 37, 977, 72], [1097, 39, 977, 74], [1098, 16, 978, 14, "left"], [1098, 20, 978, 18], [1098, 22, 978, 20], [1098, 23, 978, 21], [1099, 16, 979, 14, "top"], [1099, 19, 979, 17], [1099, 21, 979, 19], [1099, 22, 979, 20], [1100, 16, 980, 14, "width"], [1100, 21, 980, 19], [1100, 23, 980, 21, "viewSize"], [1100, 31, 980, 29], [1100, 32, 980, 30, "width"], [1100, 37, 980, 35], [1101, 16, 981, 14, "height"], [1101, 22, 981, 20], [1101, 24, 981, 22, "viewSize"], [1101, 32, 981, 30], [1101, 33, 981, 31, "height"], [1101, 39, 981, 37], [1101, 42, 981, 40], [1101, 45, 981, 43], [1102, 16, 982, 14, "borderRadius"], [1102, 28, 982, 26], [1102, 30, 982, 28], [1103, 14, 983, 12], [1103, 15, 983, 13], [1104, 12, 983, 15], [1105, 14, 983, 15, "fileName"], [1105, 22, 983, 15], [1105, 24, 983, 15, "_jsxFileName"], [1105, 36, 983, 15], [1106, 14, 983, 15, "lineNumber"], [1106, 24, 983, 15], [1107, 14, 983, 15, "columnNumber"], [1107, 26, 983, 15], [1108, 12, 983, 15], [1108, 19, 983, 17], [1108, 20, 983, 18], [1108, 35, 985, 12], [1108, 39, 985, 12, "_jsxDevRuntime"], [1108, 53, 985, 12], [1108, 54, 985, 12, "jsxDEV"], [1108, 60, 985, 12], [1108, 62, 985, 13, "_expoBlur"], [1108, 71, 985, 13], [1108, 72, 985, 13, "BlurView"], [1108, 80, 985, 21], [1109, 14, 985, 22, "intensity"], [1109, 23, 985, 31], [1109, 25, 985, 33], [1109, 27, 985, 36], [1110, 14, 985, 37, "tint"], [1110, 18, 985, 41], [1110, 20, 985, 42], [1110, 26, 985, 48], [1111, 14, 985, 49, "style"], [1111, 19, 985, 54], [1111, 21, 985, 56], [1111, 22, 985, 57, "styles"], [1111, 28, 985, 63], [1111, 29, 985, 64, "blurZone"], [1111, 37, 985, 72], [1111, 39, 985, 74], [1112, 16, 986, 14, "left"], [1112, 20, 986, 18], [1112, 22, 986, 20, "viewSize"], [1112, 30, 986, 28], [1112, 31, 986, 29, "width"], [1112, 36, 986, 34], [1112, 39, 986, 37], [1112, 42, 986, 40], [1112, 45, 986, 44, "viewSize"], [1112, 53, 986, 52], [1112, 54, 986, 53, "width"], [1112, 59, 986, 58], [1112, 62, 986, 61], [1112, 66, 986, 66], [1113, 16, 987, 14, "top"], [1113, 19, 987, 17], [1113, 21, 987, 19, "viewSize"], [1113, 29, 987, 27], [1113, 30, 987, 28, "height"], [1113, 36, 987, 34], [1113, 39, 987, 37], [1113, 43, 987, 41], [1113, 46, 987, 45, "viewSize"], [1113, 54, 987, 53], [1113, 55, 987, 54, "width"], [1113, 60, 987, 59], [1113, 63, 987, 62], [1113, 67, 987, 67], [1114, 16, 988, 14, "width"], [1114, 21, 988, 19], [1114, 23, 988, 21, "viewSize"], [1114, 31, 988, 29], [1114, 32, 988, 30, "width"], [1114, 37, 988, 35], [1114, 40, 988, 38], [1114, 43, 988, 41], [1115, 16, 989, 14, "height"], [1115, 22, 989, 20], [1115, 24, 989, 22, "viewSize"], [1115, 32, 989, 30], [1115, 33, 989, 31, "width"], [1115, 38, 989, 36], [1115, 41, 989, 39], [1115, 44, 989, 42], [1116, 16, 990, 14, "borderRadius"], [1116, 28, 990, 26], [1116, 30, 990, 29, "viewSize"], [1116, 38, 990, 37], [1116, 39, 990, 38, "width"], [1116, 44, 990, 43], [1116, 47, 990, 46], [1116, 50, 990, 49], [1116, 53, 990, 53], [1117, 14, 991, 12], [1117, 15, 991, 13], [1118, 12, 991, 15], [1119, 14, 991, 15, "fileName"], [1119, 22, 991, 15], [1119, 24, 991, 15, "_jsxFileName"], [1119, 36, 991, 15], [1120, 14, 991, 15, "lineNumber"], [1120, 24, 991, 15], [1121, 14, 991, 15, "columnNumber"], [1121, 26, 991, 15], [1122, 12, 991, 15], [1122, 19, 991, 17], [1122, 20, 991, 18], [1122, 35, 992, 12], [1122, 39, 992, 12, "_jsxDevRuntime"], [1122, 53, 992, 12], [1122, 54, 992, 12, "jsxDEV"], [1122, 60, 992, 12], [1122, 62, 992, 13, "_expoBlur"], [1122, 71, 992, 13], [1122, 72, 992, 13, "BlurView"], [1122, 80, 992, 21], [1123, 14, 992, 22, "intensity"], [1123, 23, 992, 31], [1123, 25, 992, 33], [1123, 27, 992, 36], [1124, 14, 992, 37, "tint"], [1124, 18, 992, 41], [1124, 20, 992, 42], [1124, 26, 992, 48], [1125, 14, 992, 49, "style"], [1125, 19, 992, 54], [1125, 21, 992, 56], [1125, 22, 992, 57, "styles"], [1125, 28, 992, 63], [1125, 29, 992, 64, "blurZone"], [1125, 37, 992, 72], [1125, 39, 992, 74], [1126, 16, 993, 14, "left"], [1126, 20, 993, 18], [1126, 22, 993, 20, "viewSize"], [1126, 30, 993, 28], [1126, 31, 993, 29, "width"], [1126, 36, 993, 34], [1126, 39, 993, 37], [1126, 42, 993, 40], [1126, 45, 993, 44, "viewSize"], [1126, 53, 993, 52], [1126, 54, 993, 53, "width"], [1126, 59, 993, 58], [1126, 62, 993, 61], [1126, 66, 993, 66], [1127, 16, 994, 14, "top"], [1127, 19, 994, 17], [1127, 21, 994, 19, "viewSize"], [1127, 29, 994, 27], [1127, 30, 994, 28, "height"], [1127, 36, 994, 34], [1127, 39, 994, 37], [1127, 42, 994, 40], [1127, 45, 994, 44, "viewSize"], [1127, 53, 994, 52], [1127, 54, 994, 53, "width"], [1127, 59, 994, 58], [1127, 62, 994, 61], [1127, 66, 994, 66], [1128, 16, 995, 14, "width"], [1128, 21, 995, 19], [1128, 23, 995, 21, "viewSize"], [1128, 31, 995, 29], [1128, 32, 995, 30, "width"], [1128, 37, 995, 35], [1128, 40, 995, 38], [1128, 43, 995, 41], [1129, 16, 996, 14, "height"], [1129, 22, 996, 20], [1129, 24, 996, 22, "viewSize"], [1129, 32, 996, 30], [1129, 33, 996, 31, "width"], [1129, 38, 996, 36], [1129, 41, 996, 39], [1129, 44, 996, 42], [1130, 16, 997, 14, "borderRadius"], [1130, 28, 997, 26], [1130, 30, 997, 29, "viewSize"], [1130, 38, 997, 37], [1130, 39, 997, 38, "width"], [1130, 44, 997, 43], [1130, 47, 997, 46], [1130, 50, 997, 49], [1130, 53, 997, 53], [1131, 14, 998, 12], [1131, 15, 998, 13], [1132, 12, 998, 15], [1133, 14, 998, 15, "fileName"], [1133, 22, 998, 15], [1133, 24, 998, 15, "_jsxFileName"], [1133, 36, 998, 15], [1134, 14, 998, 15, "lineNumber"], [1134, 24, 998, 15], [1135, 14, 998, 15, "columnNumber"], [1135, 26, 998, 15], [1136, 12, 998, 15], [1136, 19, 998, 17], [1136, 20, 998, 18], [1136, 35, 999, 12], [1136, 39, 999, 12, "_jsxDevRuntime"], [1136, 53, 999, 12], [1136, 54, 999, 12, "jsxDEV"], [1136, 60, 999, 12], [1136, 62, 999, 13, "_expoBlur"], [1136, 71, 999, 13], [1136, 72, 999, 13, "BlurView"], [1136, 80, 999, 21], [1137, 14, 999, 22, "intensity"], [1137, 23, 999, 31], [1137, 25, 999, 33], [1137, 27, 999, 36], [1138, 14, 999, 37, "tint"], [1138, 18, 999, 41], [1138, 20, 999, 42], [1138, 26, 999, 48], [1139, 14, 999, 49, "style"], [1139, 19, 999, 54], [1139, 21, 999, 56], [1139, 22, 999, 57, "styles"], [1139, 28, 999, 63], [1139, 29, 999, 64, "blurZone"], [1139, 37, 999, 72], [1139, 39, 999, 74], [1140, 16, 1000, 14, "left"], [1140, 20, 1000, 18], [1140, 22, 1000, 20, "viewSize"], [1140, 30, 1000, 28], [1140, 31, 1000, 29, "width"], [1140, 36, 1000, 34], [1140, 39, 1000, 37], [1140, 42, 1000, 40], [1140, 45, 1000, 44, "viewSize"], [1140, 53, 1000, 52], [1140, 54, 1000, 53, "width"], [1140, 59, 1000, 58], [1140, 62, 1000, 61], [1140, 66, 1000, 66], [1141, 16, 1001, 14, "top"], [1141, 19, 1001, 17], [1141, 21, 1001, 19, "viewSize"], [1141, 29, 1001, 27], [1141, 30, 1001, 28, "height"], [1141, 36, 1001, 34], [1141, 39, 1001, 37], [1141, 42, 1001, 40], [1141, 45, 1001, 44, "viewSize"], [1141, 53, 1001, 52], [1141, 54, 1001, 53, "width"], [1141, 59, 1001, 58], [1141, 62, 1001, 61], [1141, 66, 1001, 66], [1142, 16, 1002, 14, "width"], [1142, 21, 1002, 19], [1142, 23, 1002, 21, "viewSize"], [1142, 31, 1002, 29], [1142, 32, 1002, 30, "width"], [1142, 37, 1002, 35], [1142, 40, 1002, 38], [1142, 43, 1002, 41], [1143, 16, 1003, 14, "height"], [1143, 22, 1003, 20], [1143, 24, 1003, 22, "viewSize"], [1143, 32, 1003, 30], [1143, 33, 1003, 31, "width"], [1143, 38, 1003, 36], [1143, 41, 1003, 39], [1143, 44, 1003, 42], [1144, 16, 1004, 14, "borderRadius"], [1144, 28, 1004, 26], [1144, 30, 1004, 29, "viewSize"], [1144, 38, 1004, 37], [1144, 39, 1004, 38, "width"], [1144, 44, 1004, 43], [1144, 47, 1004, 46], [1144, 50, 1004, 49], [1144, 53, 1004, 53], [1145, 14, 1005, 12], [1145, 15, 1005, 13], [1146, 12, 1005, 15], [1147, 14, 1005, 15, "fileName"], [1147, 22, 1005, 15], [1147, 24, 1005, 15, "_jsxFileName"], [1147, 36, 1005, 15], [1148, 14, 1005, 15, "lineNumber"], [1148, 24, 1005, 15], [1149, 14, 1005, 15, "columnNumber"], [1149, 26, 1005, 15], [1150, 12, 1005, 15], [1150, 19, 1005, 17], [1150, 20, 1005, 18], [1150, 22, 1007, 13, "__DEV__"], [1150, 29, 1007, 20], [1150, 46, 1008, 14], [1150, 50, 1008, 14, "_jsxDevRuntime"], [1150, 64, 1008, 14], [1150, 65, 1008, 14, "jsxDEV"], [1150, 71, 1008, 14], [1150, 73, 1008, 15, "_View"], [1150, 78, 1008, 15], [1150, 79, 1008, 15, "default"], [1150, 86, 1008, 19], [1151, 14, 1008, 20, "style"], [1151, 19, 1008, 25], [1151, 21, 1008, 27, "styles"], [1151, 27, 1008, 33], [1151, 28, 1008, 34, "previewChip"], [1151, 39, 1008, 46], [1152, 14, 1008, 46, "children"], [1152, 22, 1008, 46], [1152, 37, 1009, 16], [1152, 41, 1009, 16, "_jsxDevRuntime"], [1152, 55, 1009, 16], [1152, 56, 1009, 16, "jsxDEV"], [1152, 62, 1009, 16], [1152, 64, 1009, 17, "_Text"], [1152, 69, 1009, 17], [1152, 70, 1009, 17, "default"], [1152, 77, 1009, 21], [1153, 16, 1009, 22, "style"], [1153, 21, 1009, 27], [1153, 23, 1009, 29, "styles"], [1153, 29, 1009, 35], [1153, 30, 1009, 36, "previewChipText"], [1153, 45, 1009, 52], [1154, 16, 1009, 52, "children"], [1154, 24, 1009, 52], [1154, 26, 1009, 53], [1155, 14, 1009, 73], [1156, 16, 1009, 73, "fileName"], [1156, 24, 1009, 73], [1156, 26, 1009, 73, "_jsxFileName"], [1156, 38, 1009, 73], [1157, 16, 1009, 73, "lineNumber"], [1157, 26, 1009, 73], [1158, 16, 1009, 73, "columnNumber"], [1158, 28, 1009, 73], [1159, 14, 1009, 73], [1159, 21, 1009, 79], [1160, 12, 1009, 80], [1161, 14, 1009, 80, "fileName"], [1161, 22, 1009, 80], [1161, 24, 1009, 80, "_jsxFileName"], [1161, 36, 1009, 80], [1162, 14, 1009, 80, "lineNumber"], [1162, 24, 1009, 80], [1163, 14, 1009, 80, "columnNumber"], [1163, 26, 1009, 80], [1164, 12, 1009, 80], [1164, 19, 1010, 20], [1164, 20, 1011, 13], [1165, 10, 1011, 13], [1166, 12, 1011, 13, "fileName"], [1166, 20, 1011, 13], [1166, 22, 1011, 13, "_jsxFileName"], [1166, 34, 1011, 13], [1167, 12, 1011, 13, "lineNumber"], [1167, 22, 1011, 13], [1168, 12, 1011, 13, "columnNumber"], [1168, 24, 1011, 13], [1169, 10, 1011, 13], [1169, 17, 1012, 18], [1169, 18, 1012, 19], [1170, 8, 1012, 19], [1170, 23, 1013, 12], [1170, 24, 1014, 9], [1170, 26, 1016, 9, "isCameraReady"], [1170, 39, 1016, 22], [1170, 56, 1017, 10], [1170, 60, 1017, 10, "_jsxDevRuntime"], [1170, 74, 1017, 10], [1170, 75, 1017, 10, "jsxDEV"], [1170, 81, 1017, 10], [1170, 83, 1017, 10, "_jsxDevRuntime"], [1170, 97, 1017, 10], [1170, 98, 1017, 10, "Fragment"], [1170, 106, 1017, 10], [1171, 10, 1017, 10, "children"], [1171, 18, 1017, 10], [1171, 34, 1019, 12], [1171, 38, 1019, 12, "_jsxDevRuntime"], [1171, 52, 1019, 12], [1171, 53, 1019, 12, "jsxDEV"], [1171, 59, 1019, 12], [1171, 61, 1019, 13, "_View"], [1171, 66, 1019, 13], [1171, 67, 1019, 13, "default"], [1171, 74, 1019, 17], [1172, 12, 1019, 18, "style"], [1172, 17, 1019, 23], [1172, 19, 1019, 25, "styles"], [1172, 25, 1019, 31], [1172, 26, 1019, 32, "headerOverlay"], [1172, 39, 1019, 46], [1173, 12, 1019, 46, "children"], [1173, 20, 1019, 46], [1173, 35, 1020, 14], [1173, 39, 1020, 14, "_jsxDevRuntime"], [1173, 53, 1020, 14], [1173, 54, 1020, 14, "jsxDEV"], [1173, 60, 1020, 14], [1173, 62, 1020, 15, "_View"], [1173, 67, 1020, 15], [1173, 68, 1020, 15, "default"], [1173, 75, 1020, 19], [1174, 14, 1020, 20, "style"], [1174, 19, 1020, 25], [1174, 21, 1020, 27, "styles"], [1174, 27, 1020, 33], [1174, 28, 1020, 34, "headerContent"], [1174, 41, 1020, 48], [1175, 14, 1020, 48, "children"], [1175, 22, 1020, 48], [1175, 38, 1021, 16], [1175, 42, 1021, 16, "_jsxDevRuntime"], [1175, 56, 1021, 16], [1175, 57, 1021, 16, "jsxDEV"], [1175, 63, 1021, 16], [1175, 65, 1021, 17, "_View"], [1175, 70, 1021, 17], [1175, 71, 1021, 17, "default"], [1175, 78, 1021, 21], [1176, 16, 1021, 22, "style"], [1176, 21, 1021, 27], [1176, 23, 1021, 29, "styles"], [1176, 29, 1021, 35], [1176, 30, 1021, 36, "headerLeft"], [1176, 40, 1021, 47], [1177, 16, 1021, 47, "children"], [1177, 24, 1021, 47], [1177, 40, 1022, 18], [1177, 44, 1022, 18, "_jsxDevRuntime"], [1177, 58, 1022, 18], [1177, 59, 1022, 18, "jsxDEV"], [1177, 65, 1022, 18], [1177, 67, 1022, 19, "_Text"], [1177, 72, 1022, 19], [1177, 73, 1022, 19, "default"], [1177, 80, 1022, 23], [1178, 18, 1022, 24, "style"], [1178, 23, 1022, 29], [1178, 25, 1022, 31, "styles"], [1178, 31, 1022, 37], [1178, 32, 1022, 38, "headerTitle"], [1178, 43, 1022, 50], [1179, 18, 1022, 50, "children"], [1179, 26, 1022, 50], [1179, 28, 1022, 51], [1180, 16, 1022, 62], [1181, 18, 1022, 62, "fileName"], [1181, 26, 1022, 62], [1181, 28, 1022, 62, "_jsxFileName"], [1181, 40, 1022, 62], [1182, 18, 1022, 62, "lineNumber"], [1182, 28, 1022, 62], [1183, 18, 1022, 62, "columnNumber"], [1183, 30, 1022, 62], [1184, 16, 1022, 62], [1184, 23, 1022, 68], [1184, 24, 1022, 69], [1184, 39, 1023, 18], [1184, 43, 1023, 18, "_jsxDevRuntime"], [1184, 57, 1023, 18], [1184, 58, 1023, 18, "jsxDEV"], [1184, 64, 1023, 18], [1184, 66, 1023, 19, "_View"], [1184, 71, 1023, 19], [1184, 72, 1023, 19, "default"], [1184, 79, 1023, 23], [1185, 18, 1023, 24, "style"], [1185, 23, 1023, 29], [1185, 25, 1023, 31, "styles"], [1185, 31, 1023, 37], [1185, 32, 1023, 38, "subtitleRow"], [1185, 43, 1023, 50], [1186, 18, 1023, 50, "children"], [1186, 26, 1023, 50], [1186, 42, 1024, 20], [1186, 46, 1024, 20, "_jsxDevRuntime"], [1186, 60, 1024, 20], [1186, 61, 1024, 20, "jsxDEV"], [1186, 67, 1024, 20], [1186, 69, 1024, 21, "_Text"], [1186, 74, 1024, 21], [1186, 75, 1024, 21, "default"], [1186, 82, 1024, 25], [1187, 20, 1024, 26, "style"], [1187, 25, 1024, 31], [1187, 27, 1024, 33, "styles"], [1187, 33, 1024, 39], [1187, 34, 1024, 40, "webIcon"], [1187, 41, 1024, 48], [1188, 20, 1024, 48, "children"], [1188, 28, 1024, 48], [1188, 30, 1024, 49], [1189, 18, 1024, 51], [1190, 20, 1024, 51, "fileName"], [1190, 28, 1024, 51], [1190, 30, 1024, 51, "_jsxFileName"], [1190, 42, 1024, 51], [1191, 20, 1024, 51, "lineNumber"], [1191, 30, 1024, 51], [1192, 20, 1024, 51, "columnNumber"], [1192, 32, 1024, 51], [1193, 18, 1024, 51], [1193, 25, 1024, 57], [1193, 26, 1024, 58], [1193, 41, 1025, 20], [1193, 45, 1025, 20, "_jsxDevRuntime"], [1193, 59, 1025, 20], [1193, 60, 1025, 20, "jsxDEV"], [1193, 66, 1025, 20], [1193, 68, 1025, 21, "_Text"], [1193, 73, 1025, 21], [1193, 74, 1025, 21, "default"], [1193, 81, 1025, 25], [1194, 20, 1025, 26, "style"], [1194, 25, 1025, 31], [1194, 27, 1025, 33, "styles"], [1194, 33, 1025, 39], [1194, 34, 1025, 40, "headerSubtitle"], [1194, 48, 1025, 55], [1195, 20, 1025, 55, "children"], [1195, 28, 1025, 55], [1195, 30, 1025, 56], [1196, 18, 1025, 71], [1197, 20, 1025, 71, "fileName"], [1197, 28, 1025, 71], [1197, 30, 1025, 71, "_jsxFileName"], [1197, 42, 1025, 71], [1198, 20, 1025, 71, "lineNumber"], [1198, 30, 1025, 71], [1199, 20, 1025, 71, "columnNumber"], [1199, 32, 1025, 71], [1200, 18, 1025, 71], [1200, 25, 1025, 77], [1200, 26, 1025, 78], [1201, 16, 1025, 78], [1202, 18, 1025, 78, "fileName"], [1202, 26, 1025, 78], [1202, 28, 1025, 78, "_jsxFileName"], [1202, 40, 1025, 78], [1203, 18, 1025, 78, "lineNumber"], [1203, 28, 1025, 78], [1204, 18, 1025, 78, "columnNumber"], [1204, 30, 1025, 78], [1205, 16, 1025, 78], [1205, 23, 1026, 24], [1205, 24, 1026, 25], [1205, 26, 1027, 19, "challengeCode"], [1205, 39, 1027, 32], [1205, 56, 1028, 20], [1205, 60, 1028, 20, "_jsxDevRuntime"], [1205, 74, 1028, 20], [1205, 75, 1028, 20, "jsxDEV"], [1205, 81, 1028, 20], [1205, 83, 1028, 21, "_View"], [1205, 88, 1028, 21], [1205, 89, 1028, 21, "default"], [1205, 96, 1028, 25], [1206, 18, 1028, 26, "style"], [1206, 23, 1028, 31], [1206, 25, 1028, 33, "styles"], [1206, 31, 1028, 39], [1206, 32, 1028, 40, "challengeRow"], [1206, 44, 1028, 53], [1207, 18, 1028, 53, "children"], [1207, 26, 1028, 53], [1207, 42, 1029, 22], [1207, 46, 1029, 22, "_jsxDevRuntime"], [1207, 60, 1029, 22], [1207, 61, 1029, 22, "jsxDEV"], [1207, 67, 1029, 22], [1207, 69, 1029, 23, "_lucideReactNative"], [1207, 87, 1029, 23], [1207, 88, 1029, 23, "Shield"], [1207, 94, 1029, 29], [1208, 20, 1029, 30, "size"], [1208, 24, 1029, 34], [1208, 26, 1029, 36], [1208, 28, 1029, 39], [1209, 20, 1029, 40, "color"], [1209, 25, 1029, 45], [1209, 27, 1029, 46], [1210, 18, 1029, 52], [1211, 20, 1029, 52, "fileName"], [1211, 28, 1029, 52], [1211, 30, 1029, 52, "_jsxFileName"], [1211, 42, 1029, 52], [1212, 20, 1029, 52, "lineNumber"], [1212, 30, 1029, 52], [1213, 20, 1029, 52, "columnNumber"], [1213, 32, 1029, 52], [1214, 18, 1029, 52], [1214, 25, 1029, 54], [1214, 26, 1029, 55], [1214, 41, 1030, 22], [1214, 45, 1030, 22, "_jsxDevRuntime"], [1214, 59, 1030, 22], [1214, 60, 1030, 22, "jsxDEV"], [1214, 66, 1030, 22], [1214, 68, 1030, 23, "_Text"], [1214, 73, 1030, 23], [1214, 74, 1030, 23, "default"], [1214, 81, 1030, 27], [1215, 20, 1030, 28, "style"], [1215, 25, 1030, 33], [1215, 27, 1030, 35, "styles"], [1215, 33, 1030, 41], [1215, 34, 1030, 42, "challengeCode"], [1215, 47, 1030, 56], [1216, 20, 1030, 56, "children"], [1216, 28, 1030, 56], [1216, 30, 1030, 58, "challengeCode"], [1217, 18, 1030, 71], [1218, 20, 1030, 71, "fileName"], [1218, 28, 1030, 71], [1218, 30, 1030, 71, "_jsxFileName"], [1218, 42, 1030, 71], [1219, 20, 1030, 71, "lineNumber"], [1219, 30, 1030, 71], [1220, 20, 1030, 71, "columnNumber"], [1220, 32, 1030, 71], [1221, 18, 1030, 71], [1221, 25, 1030, 78], [1221, 26, 1030, 79], [1222, 16, 1030, 79], [1223, 18, 1030, 79, "fileName"], [1223, 26, 1030, 79], [1223, 28, 1030, 79, "_jsxFileName"], [1223, 40, 1030, 79], [1224, 18, 1030, 79, "lineNumber"], [1224, 28, 1030, 79], [1225, 18, 1030, 79, "columnNumber"], [1225, 30, 1030, 79], [1226, 16, 1030, 79], [1226, 23, 1031, 26], [1226, 24, 1032, 19], [1227, 14, 1032, 19], [1228, 16, 1032, 19, "fileName"], [1228, 24, 1032, 19], [1228, 26, 1032, 19, "_jsxFileName"], [1228, 38, 1032, 19], [1229, 16, 1032, 19, "lineNumber"], [1229, 26, 1032, 19], [1230, 16, 1032, 19, "columnNumber"], [1230, 28, 1032, 19], [1231, 14, 1032, 19], [1231, 21, 1033, 22], [1231, 22, 1033, 23], [1231, 37, 1034, 16], [1231, 41, 1034, 16, "_jsxDevRuntime"], [1231, 55, 1034, 16], [1231, 56, 1034, 16, "jsxDEV"], [1231, 62, 1034, 16], [1231, 64, 1034, 17, "_TouchableOpacity"], [1231, 81, 1034, 17], [1231, 82, 1034, 17, "default"], [1231, 89, 1034, 33], [1232, 16, 1034, 34, "onPress"], [1232, 23, 1034, 41], [1232, 25, 1034, 43, "onCancel"], [1232, 33, 1034, 52], [1233, 16, 1034, 53, "style"], [1233, 21, 1034, 58], [1233, 23, 1034, 60, "styles"], [1233, 29, 1034, 66], [1233, 30, 1034, 67, "closeButton"], [1233, 41, 1034, 79], [1234, 16, 1034, 79, "children"], [1234, 24, 1034, 79], [1234, 39, 1035, 18], [1234, 43, 1035, 18, "_jsxDevRuntime"], [1234, 57, 1035, 18], [1234, 58, 1035, 18, "jsxDEV"], [1234, 64, 1035, 18], [1234, 66, 1035, 19, "_lucideReactNative"], [1234, 84, 1035, 19], [1234, 85, 1035, 19, "X"], [1234, 86, 1035, 20], [1235, 18, 1035, 21, "size"], [1235, 22, 1035, 25], [1235, 24, 1035, 27], [1235, 26, 1035, 30], [1236, 18, 1035, 31, "color"], [1236, 23, 1035, 36], [1236, 25, 1035, 37], [1237, 16, 1035, 43], [1238, 18, 1035, 43, "fileName"], [1238, 26, 1035, 43], [1238, 28, 1035, 43, "_jsxFileName"], [1238, 40, 1035, 43], [1239, 18, 1035, 43, "lineNumber"], [1239, 28, 1035, 43], [1240, 18, 1035, 43, "columnNumber"], [1240, 30, 1035, 43], [1241, 16, 1035, 43], [1241, 23, 1035, 45], [1242, 14, 1035, 46], [1243, 16, 1035, 46, "fileName"], [1243, 24, 1035, 46], [1243, 26, 1035, 46, "_jsxFileName"], [1243, 38, 1035, 46], [1244, 16, 1035, 46, "lineNumber"], [1244, 26, 1035, 46], [1245, 16, 1035, 46, "columnNumber"], [1245, 28, 1035, 46], [1246, 14, 1035, 46], [1246, 21, 1036, 34], [1246, 22, 1036, 35], [1247, 12, 1036, 35], [1248, 14, 1036, 35, "fileName"], [1248, 22, 1036, 35], [1248, 24, 1036, 35, "_jsxFileName"], [1248, 36, 1036, 35], [1249, 14, 1036, 35, "lineNumber"], [1249, 24, 1036, 35], [1250, 14, 1036, 35, "columnNumber"], [1250, 26, 1036, 35], [1251, 12, 1036, 35], [1251, 19, 1037, 20], [1252, 10, 1037, 21], [1253, 12, 1037, 21, "fileName"], [1253, 20, 1037, 21], [1253, 22, 1037, 21, "_jsxFileName"], [1253, 34, 1037, 21], [1254, 12, 1037, 21, "lineNumber"], [1254, 22, 1037, 21], [1255, 12, 1037, 21, "columnNumber"], [1255, 24, 1037, 21], [1256, 10, 1037, 21], [1256, 17, 1038, 18], [1256, 18, 1038, 19], [1256, 33, 1040, 12], [1256, 37, 1040, 12, "_jsxDevRuntime"], [1256, 51, 1040, 12], [1256, 52, 1040, 12, "jsxDEV"], [1256, 58, 1040, 12], [1256, 60, 1040, 13, "_View"], [1256, 65, 1040, 13], [1256, 66, 1040, 13, "default"], [1256, 73, 1040, 17], [1257, 12, 1040, 18, "style"], [1257, 17, 1040, 23], [1257, 19, 1040, 25, "styles"], [1257, 25, 1040, 31], [1257, 26, 1040, 32, "privacyNotice"], [1257, 39, 1040, 46], [1258, 12, 1040, 46, "children"], [1258, 20, 1040, 46], [1258, 36, 1041, 14], [1258, 40, 1041, 14, "_jsxDevRuntime"], [1258, 54, 1041, 14], [1258, 55, 1041, 14, "jsxDEV"], [1258, 61, 1041, 14], [1258, 63, 1041, 15, "_lucideReactNative"], [1258, 81, 1041, 15], [1258, 82, 1041, 15, "Shield"], [1258, 88, 1041, 21], [1259, 14, 1041, 22, "size"], [1259, 18, 1041, 26], [1259, 20, 1041, 28], [1259, 22, 1041, 31], [1260, 14, 1041, 32, "color"], [1260, 19, 1041, 37], [1260, 21, 1041, 38], [1261, 12, 1041, 47], [1262, 14, 1041, 47, "fileName"], [1262, 22, 1041, 47], [1262, 24, 1041, 47, "_jsxFileName"], [1262, 36, 1041, 47], [1263, 14, 1041, 47, "lineNumber"], [1263, 24, 1041, 47], [1264, 14, 1041, 47, "columnNumber"], [1264, 26, 1041, 47], [1265, 12, 1041, 47], [1265, 19, 1041, 49], [1265, 20, 1041, 50], [1265, 35, 1042, 14], [1265, 39, 1042, 14, "_jsxDevRuntime"], [1265, 53, 1042, 14], [1265, 54, 1042, 14, "jsxDEV"], [1265, 60, 1042, 14], [1265, 62, 1042, 15, "_Text"], [1265, 67, 1042, 15], [1265, 68, 1042, 15, "default"], [1265, 75, 1042, 19], [1266, 14, 1042, 20, "style"], [1266, 19, 1042, 25], [1266, 21, 1042, 27, "styles"], [1266, 27, 1042, 33], [1266, 28, 1042, 34, "privacyText"], [1266, 39, 1042, 46], [1267, 14, 1042, 46, "children"], [1267, 22, 1042, 46], [1267, 24, 1042, 47], [1268, 12, 1044, 14], [1269, 14, 1044, 14, "fileName"], [1269, 22, 1044, 14], [1269, 24, 1044, 14, "_jsxFileName"], [1269, 36, 1044, 14], [1270, 14, 1044, 14, "lineNumber"], [1270, 24, 1044, 14], [1271, 14, 1044, 14, "columnNumber"], [1271, 26, 1044, 14], [1272, 12, 1044, 14], [1272, 19, 1044, 20], [1272, 20, 1044, 21], [1273, 10, 1044, 21], [1274, 12, 1044, 21, "fileName"], [1274, 20, 1044, 21], [1274, 22, 1044, 21, "_jsxFileName"], [1274, 34, 1044, 21], [1275, 12, 1044, 21, "lineNumber"], [1275, 22, 1044, 21], [1276, 12, 1044, 21, "columnNumber"], [1276, 24, 1044, 21], [1277, 10, 1044, 21], [1277, 17, 1045, 18], [1277, 18, 1045, 19], [1277, 33, 1047, 12], [1277, 37, 1047, 12, "_jsxDevRuntime"], [1277, 51, 1047, 12], [1277, 52, 1047, 12, "jsxDEV"], [1277, 58, 1047, 12], [1277, 60, 1047, 13, "_View"], [1277, 65, 1047, 13], [1277, 66, 1047, 13, "default"], [1277, 73, 1047, 17], [1278, 12, 1047, 18, "style"], [1278, 17, 1047, 23], [1278, 19, 1047, 25, "styles"], [1278, 25, 1047, 31], [1278, 26, 1047, 32, "footer<PERSON><PERSON><PERSON>"], [1278, 39, 1047, 46], [1279, 12, 1047, 46, "children"], [1279, 20, 1047, 46], [1279, 36, 1048, 14], [1279, 40, 1048, 14, "_jsxDevRuntime"], [1279, 54, 1048, 14], [1279, 55, 1048, 14, "jsxDEV"], [1279, 61, 1048, 14], [1279, 63, 1048, 15, "_Text"], [1279, 68, 1048, 15], [1279, 69, 1048, 15, "default"], [1279, 76, 1048, 19], [1280, 14, 1048, 20, "style"], [1280, 19, 1048, 25], [1280, 21, 1048, 27, "styles"], [1280, 27, 1048, 33], [1280, 28, 1048, 34, "instruction"], [1280, 39, 1048, 46], [1281, 14, 1048, 46, "children"], [1281, 22, 1048, 46], [1281, 24, 1048, 47], [1282, 12, 1050, 14], [1283, 14, 1050, 14, "fileName"], [1283, 22, 1050, 14], [1283, 24, 1050, 14, "_jsxFileName"], [1283, 36, 1050, 14], [1284, 14, 1050, 14, "lineNumber"], [1284, 24, 1050, 14], [1285, 14, 1050, 14, "columnNumber"], [1285, 26, 1050, 14], [1286, 12, 1050, 14], [1286, 19, 1050, 20], [1286, 20, 1050, 21], [1286, 35, 1052, 14], [1286, 39, 1052, 14, "_jsxDevRuntime"], [1286, 53, 1052, 14], [1286, 54, 1052, 14, "jsxDEV"], [1286, 60, 1052, 14], [1286, 62, 1052, 15, "_TouchableOpacity"], [1286, 79, 1052, 15], [1286, 80, 1052, 15, "default"], [1286, 87, 1052, 31], [1287, 14, 1053, 16, "onPress"], [1287, 21, 1053, 23], [1287, 23, 1053, 25, "capturePhoto"], [1287, 35, 1053, 38], [1288, 14, 1054, 16, "disabled"], [1288, 22, 1054, 24], [1288, 24, 1054, 26, "processingState"], [1288, 39, 1054, 41], [1288, 44, 1054, 46], [1288, 50, 1054, 52], [1288, 54, 1054, 56], [1288, 55, 1054, 57, "isCameraReady"], [1288, 68, 1054, 71], [1289, 14, 1055, 16, "style"], [1289, 19, 1055, 21], [1289, 21, 1055, 23], [1289, 22, 1056, 18, "styles"], [1289, 28, 1056, 24], [1289, 29, 1056, 25, "shutterButton"], [1289, 42, 1056, 38], [1289, 44, 1057, 18, "processingState"], [1289, 59, 1057, 33], [1289, 64, 1057, 38], [1289, 70, 1057, 44], [1289, 74, 1057, 48, "styles"], [1289, 80, 1057, 54], [1289, 81, 1057, 55, "shutterButtonDisabled"], [1289, 102, 1057, 76], [1289, 103, 1058, 18], [1290, 14, 1058, 18, "children"], [1290, 22, 1058, 18], [1290, 24, 1060, 17, "processingState"], [1290, 39, 1060, 32], [1290, 44, 1060, 37], [1290, 50, 1060, 43], [1290, 66, 1061, 18], [1290, 70, 1061, 18, "_jsxDevRuntime"], [1290, 84, 1061, 18], [1290, 85, 1061, 18, "jsxDEV"], [1290, 91, 1061, 18], [1290, 93, 1061, 19, "_View"], [1290, 98, 1061, 19], [1290, 99, 1061, 19, "default"], [1290, 106, 1061, 23], [1291, 16, 1061, 24, "style"], [1291, 21, 1061, 29], [1291, 23, 1061, 31, "styles"], [1291, 29, 1061, 37], [1291, 30, 1061, 38, "shutterInner"], [1292, 14, 1061, 51], [1293, 16, 1061, 51, "fileName"], [1293, 24, 1061, 51], [1293, 26, 1061, 51, "_jsxFileName"], [1293, 38, 1061, 51], [1294, 16, 1061, 51, "lineNumber"], [1294, 26, 1061, 51], [1295, 16, 1061, 51, "columnNumber"], [1295, 28, 1061, 51], [1296, 14, 1061, 51], [1296, 21, 1061, 53], [1296, 22, 1061, 54], [1296, 38, 1063, 18], [1296, 42, 1063, 18, "_jsxDevRuntime"], [1296, 56, 1063, 18], [1296, 57, 1063, 18, "jsxDEV"], [1296, 63, 1063, 18], [1296, 65, 1063, 19, "_ActivityIndicator"], [1296, 83, 1063, 19], [1296, 84, 1063, 19, "default"], [1296, 91, 1063, 36], [1297, 16, 1063, 37, "size"], [1297, 20, 1063, 41], [1297, 22, 1063, 42], [1297, 29, 1063, 49], [1298, 16, 1063, 50, "color"], [1298, 21, 1063, 55], [1298, 23, 1063, 56], [1299, 14, 1063, 65], [1300, 16, 1063, 65, "fileName"], [1300, 24, 1063, 65], [1300, 26, 1063, 65, "_jsxFileName"], [1300, 38, 1063, 65], [1301, 16, 1063, 65, "lineNumber"], [1301, 26, 1063, 65], [1302, 16, 1063, 65, "columnNumber"], [1302, 28, 1063, 65], [1303, 14, 1063, 65], [1303, 21, 1063, 67], [1304, 12, 1064, 17], [1305, 14, 1064, 17, "fileName"], [1305, 22, 1064, 17], [1305, 24, 1064, 17, "_jsxFileName"], [1305, 36, 1064, 17], [1306, 14, 1064, 17, "lineNumber"], [1306, 24, 1064, 17], [1307, 14, 1064, 17, "columnNumber"], [1307, 26, 1064, 17], [1308, 12, 1064, 17], [1308, 19, 1065, 32], [1308, 20, 1065, 33], [1308, 35, 1066, 14], [1308, 39, 1066, 14, "_jsxDevRuntime"], [1308, 53, 1066, 14], [1308, 54, 1066, 14, "jsxDEV"], [1308, 60, 1066, 14], [1308, 62, 1066, 15, "_Text"], [1308, 67, 1066, 15], [1308, 68, 1066, 15, "default"], [1308, 75, 1066, 19], [1309, 14, 1066, 20, "style"], [1309, 19, 1066, 25], [1309, 21, 1066, 27, "styles"], [1309, 27, 1066, 33], [1309, 28, 1066, 34, "privacyNote"], [1309, 39, 1066, 46], [1310, 14, 1066, 46, "children"], [1310, 22, 1066, 46], [1310, 24, 1066, 47], [1311, 12, 1068, 14], [1312, 14, 1068, 14, "fileName"], [1312, 22, 1068, 14], [1312, 24, 1068, 14, "_jsxFileName"], [1312, 36, 1068, 14], [1313, 14, 1068, 14, "lineNumber"], [1313, 24, 1068, 14], [1314, 14, 1068, 14, "columnNumber"], [1314, 26, 1068, 14], [1315, 12, 1068, 14], [1315, 19, 1068, 20], [1315, 20, 1068, 21], [1316, 10, 1068, 21], [1317, 12, 1068, 21, "fileName"], [1317, 20, 1068, 21], [1317, 22, 1068, 21, "_jsxFileName"], [1317, 34, 1068, 21], [1318, 12, 1068, 21, "lineNumber"], [1318, 22, 1068, 21], [1319, 12, 1068, 21, "columnNumber"], [1319, 24, 1068, 21], [1320, 10, 1068, 21], [1320, 17, 1069, 18], [1320, 18, 1069, 19], [1321, 8, 1069, 19], [1321, 23, 1070, 12], [1321, 24, 1071, 9], [1322, 6, 1071, 9], [1323, 8, 1071, 9, "fileName"], [1323, 16, 1071, 9], [1323, 18, 1071, 9, "_jsxFileName"], [1323, 30, 1071, 9], [1324, 8, 1071, 9, "lineNumber"], [1324, 18, 1071, 9], [1325, 8, 1071, 9, "columnNumber"], [1325, 20, 1071, 9], [1326, 6, 1071, 9], [1326, 13, 1072, 12], [1326, 14, 1072, 13], [1326, 29, 1074, 6], [1326, 33, 1074, 6, "_jsxDevRuntime"], [1326, 47, 1074, 6], [1326, 48, 1074, 6, "jsxDEV"], [1326, 54, 1074, 6], [1326, 56, 1074, 7, "_Modal"], [1326, 62, 1074, 7], [1326, 63, 1074, 7, "default"], [1326, 70, 1074, 12], [1327, 8, 1075, 8, "visible"], [1327, 15, 1075, 15], [1327, 17, 1075, 17, "processingState"], [1327, 32, 1075, 32], [1327, 37, 1075, 37], [1327, 43, 1075, 43], [1327, 47, 1075, 47, "processingState"], [1327, 62, 1075, 62], [1327, 67, 1075, 67], [1327, 74, 1075, 75], [1328, 8, 1076, 8, "transparent"], [1328, 19, 1076, 19], [1329, 8, 1077, 8, "animationType"], [1329, 21, 1077, 21], [1329, 23, 1077, 22], [1329, 29, 1077, 28], [1330, 8, 1077, 28, "children"], [1330, 16, 1077, 28], [1330, 31, 1079, 8], [1330, 35, 1079, 8, "_jsxDevRuntime"], [1330, 49, 1079, 8], [1330, 50, 1079, 8, "jsxDEV"], [1330, 56, 1079, 8], [1330, 58, 1079, 9, "_View"], [1330, 63, 1079, 9], [1330, 64, 1079, 9, "default"], [1330, 71, 1079, 13], [1331, 10, 1079, 14, "style"], [1331, 15, 1079, 19], [1331, 17, 1079, 21, "styles"], [1331, 23, 1079, 27], [1331, 24, 1079, 28, "processingModal"], [1331, 39, 1079, 44], [1332, 10, 1079, 44, "children"], [1332, 18, 1079, 44], [1332, 33, 1080, 10], [1332, 37, 1080, 10, "_jsxDevRuntime"], [1332, 51, 1080, 10], [1332, 52, 1080, 10, "jsxDEV"], [1332, 58, 1080, 10], [1332, 60, 1080, 11, "_View"], [1332, 65, 1080, 11], [1332, 66, 1080, 11, "default"], [1332, 73, 1080, 15], [1333, 12, 1080, 16, "style"], [1333, 17, 1080, 21], [1333, 19, 1080, 23, "styles"], [1333, 25, 1080, 29], [1333, 26, 1080, 30, "processingContent"], [1333, 43, 1080, 48], [1334, 12, 1080, 48, "children"], [1334, 20, 1080, 48], [1334, 36, 1081, 12], [1334, 40, 1081, 12, "_jsxDevRuntime"], [1334, 54, 1081, 12], [1334, 55, 1081, 12, "jsxDEV"], [1334, 61, 1081, 12], [1334, 63, 1081, 13, "_ActivityIndicator"], [1334, 81, 1081, 13], [1334, 82, 1081, 13, "default"], [1334, 89, 1081, 30], [1335, 14, 1081, 31, "size"], [1335, 18, 1081, 35], [1335, 20, 1081, 36], [1335, 27, 1081, 43], [1336, 14, 1081, 44, "color"], [1336, 19, 1081, 49], [1336, 21, 1081, 50], [1337, 12, 1081, 59], [1338, 14, 1081, 59, "fileName"], [1338, 22, 1081, 59], [1338, 24, 1081, 59, "_jsxFileName"], [1338, 36, 1081, 59], [1339, 14, 1081, 59, "lineNumber"], [1339, 24, 1081, 59], [1340, 14, 1081, 59, "columnNumber"], [1340, 26, 1081, 59], [1341, 12, 1081, 59], [1341, 19, 1081, 61], [1341, 20, 1081, 62], [1341, 35, 1083, 12], [1341, 39, 1083, 12, "_jsxDevRuntime"], [1341, 53, 1083, 12], [1341, 54, 1083, 12, "jsxDEV"], [1341, 60, 1083, 12], [1341, 62, 1083, 13, "_Text"], [1341, 67, 1083, 13], [1341, 68, 1083, 13, "default"], [1341, 75, 1083, 17], [1342, 14, 1083, 18, "style"], [1342, 19, 1083, 23], [1342, 21, 1083, 25, "styles"], [1342, 27, 1083, 31], [1342, 28, 1083, 32, "processingTitle"], [1342, 43, 1083, 48], [1343, 14, 1083, 48, "children"], [1343, 22, 1083, 48], [1343, 25, 1084, 15, "processingState"], [1343, 40, 1084, 30], [1343, 45, 1084, 35], [1343, 56, 1084, 46], [1343, 60, 1084, 50], [1343, 80, 1084, 70], [1343, 82, 1085, 15, "processingState"], [1343, 97, 1085, 30], [1343, 102, 1085, 35], [1343, 113, 1085, 46], [1343, 117, 1085, 50], [1343, 146, 1085, 79], [1343, 148, 1086, 15, "processingState"], [1343, 163, 1086, 30], [1343, 168, 1086, 35], [1343, 180, 1086, 47], [1343, 184, 1086, 51], [1343, 216, 1086, 83], [1343, 218, 1087, 15, "processingState"], [1343, 233, 1087, 30], [1343, 238, 1087, 35], [1343, 249, 1087, 46], [1343, 253, 1087, 50], [1343, 275, 1087, 72], [1344, 12, 1087, 72], [1345, 14, 1087, 72, "fileName"], [1345, 22, 1087, 72], [1345, 24, 1087, 72, "_jsxFileName"], [1345, 36, 1087, 72], [1346, 14, 1087, 72, "lineNumber"], [1346, 24, 1087, 72], [1347, 14, 1087, 72, "columnNumber"], [1347, 26, 1087, 72], [1348, 12, 1087, 72], [1348, 19, 1088, 18], [1348, 20, 1088, 19], [1348, 35, 1089, 12], [1348, 39, 1089, 12, "_jsxDevRuntime"], [1348, 53, 1089, 12], [1348, 54, 1089, 12, "jsxDEV"], [1348, 60, 1089, 12], [1348, 62, 1089, 13, "_View"], [1348, 67, 1089, 13], [1348, 68, 1089, 13, "default"], [1348, 75, 1089, 17], [1349, 14, 1089, 18, "style"], [1349, 19, 1089, 23], [1349, 21, 1089, 25, "styles"], [1349, 27, 1089, 31], [1349, 28, 1089, 32, "progressBar"], [1349, 39, 1089, 44], [1350, 14, 1089, 44, "children"], [1350, 22, 1089, 44], [1350, 37, 1090, 14], [1350, 41, 1090, 14, "_jsxDevRuntime"], [1350, 55, 1090, 14], [1350, 56, 1090, 14, "jsxDEV"], [1350, 62, 1090, 14], [1350, 64, 1090, 15, "_View"], [1350, 69, 1090, 15], [1350, 70, 1090, 15, "default"], [1350, 77, 1090, 19], [1351, 16, 1091, 16, "style"], [1351, 21, 1091, 21], [1351, 23, 1091, 23], [1351, 24, 1092, 18, "styles"], [1351, 30, 1092, 24], [1351, 31, 1092, 25, "progressFill"], [1351, 43, 1092, 37], [1351, 45, 1093, 18], [1352, 18, 1093, 20, "width"], [1352, 23, 1093, 25], [1352, 25, 1093, 27], [1352, 28, 1093, 30, "processingProgress"], [1352, 46, 1093, 48], [1353, 16, 1093, 52], [1353, 17, 1093, 53], [1354, 14, 1094, 18], [1355, 16, 1094, 18, "fileName"], [1355, 24, 1094, 18], [1355, 26, 1094, 18, "_jsxFileName"], [1355, 38, 1094, 18], [1356, 16, 1094, 18, "lineNumber"], [1356, 26, 1094, 18], [1357, 16, 1094, 18, "columnNumber"], [1357, 28, 1094, 18], [1358, 14, 1094, 18], [1358, 21, 1095, 15], [1359, 12, 1095, 16], [1360, 14, 1095, 16, "fileName"], [1360, 22, 1095, 16], [1360, 24, 1095, 16, "_jsxFileName"], [1360, 36, 1095, 16], [1361, 14, 1095, 16, "lineNumber"], [1361, 24, 1095, 16], [1362, 14, 1095, 16, "columnNumber"], [1362, 26, 1095, 16], [1363, 12, 1095, 16], [1363, 19, 1096, 18], [1363, 20, 1096, 19], [1363, 35, 1097, 12], [1363, 39, 1097, 12, "_jsxDevRuntime"], [1363, 53, 1097, 12], [1363, 54, 1097, 12, "jsxDEV"], [1363, 60, 1097, 12], [1363, 62, 1097, 13, "_Text"], [1363, 67, 1097, 13], [1363, 68, 1097, 13, "default"], [1363, 75, 1097, 17], [1364, 14, 1097, 18, "style"], [1364, 19, 1097, 23], [1364, 21, 1097, 25, "styles"], [1364, 27, 1097, 31], [1364, 28, 1097, 32, "processingDescription"], [1364, 49, 1097, 54], [1365, 14, 1097, 54, "children"], [1365, 22, 1097, 54], [1365, 25, 1098, 15, "processingState"], [1365, 40, 1098, 30], [1365, 45, 1098, 35], [1365, 56, 1098, 46], [1365, 60, 1098, 50], [1365, 89, 1098, 79], [1365, 91, 1099, 15, "processingState"], [1365, 106, 1099, 30], [1365, 111, 1099, 35], [1365, 122, 1099, 46], [1365, 126, 1099, 50], [1365, 164, 1099, 88], [1365, 166, 1100, 15, "processingState"], [1365, 181, 1100, 30], [1365, 186, 1100, 35], [1365, 198, 1100, 47], [1365, 202, 1100, 51], [1365, 247, 1100, 96], [1365, 249, 1101, 15, "processingState"], [1365, 264, 1101, 30], [1365, 269, 1101, 35], [1365, 280, 1101, 46], [1365, 284, 1101, 50], [1365, 325, 1101, 91], [1366, 12, 1101, 91], [1367, 14, 1101, 91, "fileName"], [1367, 22, 1101, 91], [1367, 24, 1101, 91, "_jsxFileName"], [1367, 36, 1101, 91], [1368, 14, 1101, 91, "lineNumber"], [1368, 24, 1101, 91], [1369, 14, 1101, 91, "columnNumber"], [1369, 26, 1101, 91], [1370, 12, 1101, 91], [1370, 19, 1102, 18], [1370, 20, 1102, 19], [1370, 22, 1103, 13, "processingState"], [1370, 37, 1103, 28], [1370, 42, 1103, 33], [1370, 53, 1103, 44], [1370, 70, 1104, 14], [1370, 74, 1104, 14, "_jsxDevRuntime"], [1370, 88, 1104, 14], [1370, 89, 1104, 14, "jsxDEV"], [1370, 95, 1104, 14], [1370, 97, 1104, 15, "_lucideReactNative"], [1370, 115, 1104, 15], [1370, 116, 1104, 15, "CheckCircle"], [1370, 127, 1104, 26], [1371, 14, 1104, 27, "size"], [1371, 18, 1104, 31], [1371, 20, 1104, 33], [1371, 22, 1104, 36], [1372, 14, 1104, 37, "color"], [1372, 19, 1104, 42], [1372, 21, 1104, 43], [1372, 30, 1104, 52], [1373, 14, 1104, 53, "style"], [1373, 19, 1104, 58], [1373, 21, 1104, 60, "styles"], [1373, 27, 1104, 66], [1373, 28, 1104, 67, "successIcon"], [1374, 12, 1104, 79], [1375, 14, 1104, 79, "fileName"], [1375, 22, 1104, 79], [1375, 24, 1104, 79, "_jsxFileName"], [1375, 36, 1104, 79], [1376, 14, 1104, 79, "lineNumber"], [1376, 24, 1104, 79], [1377, 14, 1104, 79, "columnNumber"], [1377, 26, 1104, 79], [1378, 12, 1104, 79], [1378, 19, 1104, 81], [1378, 20, 1105, 13], [1379, 10, 1105, 13], [1380, 12, 1105, 13, "fileName"], [1380, 20, 1105, 13], [1380, 22, 1105, 13, "_jsxFileName"], [1380, 34, 1105, 13], [1381, 12, 1105, 13, "lineNumber"], [1381, 22, 1105, 13], [1382, 12, 1105, 13, "columnNumber"], [1382, 24, 1105, 13], [1383, 10, 1105, 13], [1383, 17, 1106, 16], [1384, 8, 1106, 17], [1385, 10, 1106, 17, "fileName"], [1385, 18, 1106, 17], [1385, 20, 1106, 17, "_jsxFileName"], [1385, 32, 1106, 17], [1386, 10, 1106, 17, "lineNumber"], [1386, 20, 1106, 17], [1387, 10, 1106, 17, "columnNumber"], [1387, 22, 1106, 17], [1388, 8, 1106, 17], [1388, 15, 1107, 14], [1389, 6, 1107, 15], [1390, 8, 1107, 15, "fileName"], [1390, 16, 1107, 15], [1390, 18, 1107, 15, "_jsxFileName"], [1390, 30, 1107, 15], [1391, 8, 1107, 15, "lineNumber"], [1391, 18, 1107, 15], [1392, 8, 1107, 15, "columnNumber"], [1392, 20, 1107, 15], [1393, 6, 1107, 15], [1393, 13, 1108, 13], [1393, 14, 1108, 14], [1393, 29, 1110, 6], [1393, 33, 1110, 6, "_jsxDevRuntime"], [1393, 47, 1110, 6], [1393, 48, 1110, 6, "jsxDEV"], [1393, 54, 1110, 6], [1393, 56, 1110, 7, "_Modal"], [1393, 62, 1110, 7], [1393, 63, 1110, 7, "default"], [1393, 70, 1110, 12], [1394, 8, 1111, 8, "visible"], [1394, 15, 1111, 15], [1394, 17, 1111, 17, "processingState"], [1394, 32, 1111, 32], [1394, 37, 1111, 37], [1394, 44, 1111, 45], [1395, 8, 1112, 8, "transparent"], [1395, 19, 1112, 19], [1396, 8, 1113, 8, "animationType"], [1396, 21, 1113, 21], [1396, 23, 1113, 22], [1396, 29, 1113, 28], [1397, 8, 1113, 28, "children"], [1397, 16, 1113, 28], [1397, 31, 1115, 8], [1397, 35, 1115, 8, "_jsxDevRuntime"], [1397, 49, 1115, 8], [1397, 50, 1115, 8, "jsxDEV"], [1397, 56, 1115, 8], [1397, 58, 1115, 9, "_View"], [1397, 63, 1115, 9], [1397, 64, 1115, 9, "default"], [1397, 71, 1115, 13], [1398, 10, 1115, 14, "style"], [1398, 15, 1115, 19], [1398, 17, 1115, 21, "styles"], [1398, 23, 1115, 27], [1398, 24, 1115, 28, "processingModal"], [1398, 39, 1115, 44], [1399, 10, 1115, 44, "children"], [1399, 18, 1115, 44], [1399, 33, 1116, 10], [1399, 37, 1116, 10, "_jsxDevRuntime"], [1399, 51, 1116, 10], [1399, 52, 1116, 10, "jsxDEV"], [1399, 58, 1116, 10], [1399, 60, 1116, 11, "_View"], [1399, 65, 1116, 11], [1399, 66, 1116, 11, "default"], [1399, 73, 1116, 15], [1400, 12, 1116, 16, "style"], [1400, 17, 1116, 21], [1400, 19, 1116, 23, "styles"], [1400, 25, 1116, 29], [1400, 26, 1116, 30, "errorContent"], [1400, 38, 1116, 43], [1401, 12, 1116, 43, "children"], [1401, 20, 1116, 43], [1401, 36, 1117, 12], [1401, 40, 1117, 12, "_jsxDevRuntime"], [1401, 54, 1117, 12], [1401, 55, 1117, 12, "jsxDEV"], [1401, 61, 1117, 12], [1401, 63, 1117, 13, "_lucideReactNative"], [1401, 81, 1117, 13], [1401, 82, 1117, 13, "X"], [1401, 83, 1117, 14], [1402, 14, 1117, 15, "size"], [1402, 18, 1117, 19], [1402, 20, 1117, 21], [1402, 22, 1117, 24], [1403, 14, 1117, 25, "color"], [1403, 19, 1117, 30], [1403, 21, 1117, 31], [1404, 12, 1117, 40], [1405, 14, 1117, 40, "fileName"], [1405, 22, 1117, 40], [1405, 24, 1117, 40, "_jsxFileName"], [1405, 36, 1117, 40], [1406, 14, 1117, 40, "lineNumber"], [1406, 24, 1117, 40], [1407, 14, 1117, 40, "columnNumber"], [1407, 26, 1117, 40], [1408, 12, 1117, 40], [1408, 19, 1117, 42], [1408, 20, 1117, 43], [1408, 35, 1118, 12], [1408, 39, 1118, 12, "_jsxDevRuntime"], [1408, 53, 1118, 12], [1408, 54, 1118, 12, "jsxDEV"], [1408, 60, 1118, 12], [1408, 62, 1118, 13, "_Text"], [1408, 67, 1118, 13], [1408, 68, 1118, 13, "default"], [1408, 75, 1118, 17], [1409, 14, 1118, 18, "style"], [1409, 19, 1118, 23], [1409, 21, 1118, 25, "styles"], [1409, 27, 1118, 31], [1409, 28, 1118, 32, "errorTitle"], [1409, 38, 1118, 43], [1410, 14, 1118, 43, "children"], [1410, 22, 1118, 43], [1410, 24, 1118, 44], [1411, 12, 1118, 61], [1412, 14, 1118, 61, "fileName"], [1412, 22, 1118, 61], [1412, 24, 1118, 61, "_jsxFileName"], [1412, 36, 1118, 61], [1413, 14, 1118, 61, "lineNumber"], [1413, 24, 1118, 61], [1414, 14, 1118, 61, "columnNumber"], [1414, 26, 1118, 61], [1415, 12, 1118, 61], [1415, 19, 1118, 67], [1415, 20, 1118, 68], [1415, 35, 1119, 12], [1415, 39, 1119, 12, "_jsxDevRuntime"], [1415, 53, 1119, 12], [1415, 54, 1119, 12, "jsxDEV"], [1415, 60, 1119, 12], [1415, 62, 1119, 13, "_Text"], [1415, 67, 1119, 13], [1415, 68, 1119, 13, "default"], [1415, 75, 1119, 17], [1416, 14, 1119, 18, "style"], [1416, 19, 1119, 23], [1416, 21, 1119, 25, "styles"], [1416, 27, 1119, 31], [1416, 28, 1119, 32, "errorMessage"], [1416, 40, 1119, 45], [1417, 14, 1119, 45, "children"], [1417, 22, 1119, 45], [1417, 24, 1119, 47, "errorMessage"], [1418, 12, 1119, 59], [1419, 14, 1119, 59, "fileName"], [1419, 22, 1119, 59], [1419, 24, 1119, 59, "_jsxFileName"], [1419, 36, 1119, 59], [1420, 14, 1119, 59, "lineNumber"], [1420, 24, 1119, 59], [1421, 14, 1119, 59, "columnNumber"], [1421, 26, 1119, 59], [1422, 12, 1119, 59], [1422, 19, 1119, 66], [1422, 20, 1119, 67], [1422, 35, 1120, 12], [1422, 39, 1120, 12, "_jsxDevRuntime"], [1422, 53, 1120, 12], [1422, 54, 1120, 12, "jsxDEV"], [1422, 60, 1120, 12], [1422, 62, 1120, 13, "_TouchableOpacity"], [1422, 79, 1120, 13], [1422, 80, 1120, 13, "default"], [1422, 87, 1120, 29], [1423, 14, 1121, 14, "onPress"], [1423, 21, 1121, 21], [1423, 23, 1121, 23, "retryCapture"], [1423, 35, 1121, 36], [1424, 14, 1122, 14, "style"], [1424, 19, 1122, 19], [1424, 21, 1122, 21, "styles"], [1424, 27, 1122, 27], [1424, 28, 1122, 28, "primaryButton"], [1424, 41, 1122, 42], [1425, 14, 1122, 42, "children"], [1425, 22, 1122, 42], [1425, 37, 1124, 14], [1425, 41, 1124, 14, "_jsxDevRuntime"], [1425, 55, 1124, 14], [1425, 56, 1124, 14, "jsxDEV"], [1425, 62, 1124, 14], [1425, 64, 1124, 15, "_Text"], [1425, 69, 1124, 15], [1425, 70, 1124, 15, "default"], [1425, 77, 1124, 19], [1426, 16, 1124, 20, "style"], [1426, 21, 1124, 25], [1426, 23, 1124, 27, "styles"], [1426, 29, 1124, 33], [1426, 30, 1124, 34, "primaryButtonText"], [1426, 47, 1124, 52], [1427, 16, 1124, 52, "children"], [1427, 24, 1124, 52], [1427, 26, 1124, 53], [1428, 14, 1124, 62], [1429, 16, 1124, 62, "fileName"], [1429, 24, 1124, 62], [1429, 26, 1124, 62, "_jsxFileName"], [1429, 38, 1124, 62], [1430, 16, 1124, 62, "lineNumber"], [1430, 26, 1124, 62], [1431, 16, 1124, 62, "columnNumber"], [1431, 28, 1124, 62], [1432, 14, 1124, 62], [1432, 21, 1124, 68], [1433, 12, 1124, 69], [1434, 14, 1124, 69, "fileName"], [1434, 22, 1124, 69], [1434, 24, 1124, 69, "_jsxFileName"], [1434, 36, 1124, 69], [1435, 14, 1124, 69, "lineNumber"], [1435, 24, 1124, 69], [1436, 14, 1124, 69, "columnNumber"], [1436, 26, 1124, 69], [1437, 12, 1124, 69], [1437, 19, 1125, 30], [1437, 20, 1125, 31], [1437, 35, 1126, 12], [1437, 39, 1126, 12, "_jsxDevRuntime"], [1437, 53, 1126, 12], [1437, 54, 1126, 12, "jsxDEV"], [1437, 60, 1126, 12], [1437, 62, 1126, 13, "_TouchableOpacity"], [1437, 79, 1126, 13], [1437, 80, 1126, 13, "default"], [1437, 87, 1126, 29], [1438, 14, 1127, 14, "onPress"], [1438, 21, 1127, 21], [1438, 23, 1127, 23, "onCancel"], [1438, 31, 1127, 32], [1439, 14, 1128, 14, "style"], [1439, 19, 1128, 19], [1439, 21, 1128, 21, "styles"], [1439, 27, 1128, 27], [1439, 28, 1128, 28, "secondaryButton"], [1439, 43, 1128, 44], [1440, 14, 1128, 44, "children"], [1440, 22, 1128, 44], [1440, 37, 1130, 14], [1440, 41, 1130, 14, "_jsxDevRuntime"], [1440, 55, 1130, 14], [1440, 56, 1130, 14, "jsxDEV"], [1440, 62, 1130, 14], [1440, 64, 1130, 15, "_Text"], [1440, 69, 1130, 15], [1440, 70, 1130, 15, "default"], [1440, 77, 1130, 19], [1441, 16, 1130, 20, "style"], [1441, 21, 1130, 25], [1441, 23, 1130, 27, "styles"], [1441, 29, 1130, 33], [1441, 30, 1130, 34, "secondaryButtonText"], [1441, 49, 1130, 54], [1442, 16, 1130, 54, "children"], [1442, 24, 1130, 54], [1442, 26, 1130, 55], [1443, 14, 1130, 61], [1444, 16, 1130, 61, "fileName"], [1444, 24, 1130, 61], [1444, 26, 1130, 61, "_jsxFileName"], [1444, 38, 1130, 61], [1445, 16, 1130, 61, "lineNumber"], [1445, 26, 1130, 61], [1446, 16, 1130, 61, "columnNumber"], [1446, 28, 1130, 61], [1447, 14, 1130, 61], [1447, 21, 1130, 67], [1448, 12, 1130, 68], [1449, 14, 1130, 68, "fileName"], [1449, 22, 1130, 68], [1449, 24, 1130, 68, "_jsxFileName"], [1449, 36, 1130, 68], [1450, 14, 1130, 68, "lineNumber"], [1450, 24, 1130, 68], [1451, 14, 1130, 68, "columnNumber"], [1451, 26, 1130, 68], [1452, 12, 1130, 68], [1452, 19, 1131, 30], [1452, 20, 1131, 31], [1453, 10, 1131, 31], [1454, 12, 1131, 31, "fileName"], [1454, 20, 1131, 31], [1454, 22, 1131, 31, "_jsxFileName"], [1454, 34, 1131, 31], [1455, 12, 1131, 31, "lineNumber"], [1455, 22, 1131, 31], [1456, 12, 1131, 31, "columnNumber"], [1456, 24, 1131, 31], [1457, 10, 1131, 31], [1457, 17, 1132, 16], [1458, 8, 1132, 17], [1459, 10, 1132, 17, "fileName"], [1459, 18, 1132, 17], [1459, 20, 1132, 17, "_jsxFileName"], [1459, 32, 1132, 17], [1460, 10, 1132, 17, "lineNumber"], [1460, 20, 1132, 17], [1461, 10, 1132, 17, "columnNumber"], [1461, 22, 1132, 17], [1462, 8, 1132, 17], [1462, 15, 1133, 14], [1463, 6, 1133, 15], [1464, 8, 1133, 15, "fileName"], [1464, 16, 1133, 15], [1464, 18, 1133, 15, "_jsxFileName"], [1464, 30, 1133, 15], [1465, 8, 1133, 15, "lineNumber"], [1465, 18, 1133, 15], [1466, 8, 1133, 15, "columnNumber"], [1466, 20, 1133, 15], [1467, 6, 1133, 15], [1467, 13, 1134, 13], [1467, 14, 1134, 14], [1468, 4, 1134, 14], [1469, 6, 1134, 14, "fileName"], [1469, 14, 1134, 14], [1469, 16, 1134, 14, "_jsxFileName"], [1469, 28, 1134, 14], [1470, 6, 1134, 14, "lineNumber"], [1470, 16, 1134, 14], [1471, 6, 1134, 14, "columnNumber"], [1471, 18, 1134, 14], [1472, 4, 1134, 14], [1472, 11, 1135, 10], [1472, 12, 1135, 11], [1473, 2, 1137, 0], [1474, 2, 1137, 1, "_s"], [1474, 4, 1137, 1], [1474, 5, 51, 24, "EchoCameraWeb"], [1474, 18, 51, 37], [1475, 4, 51, 37], [1475, 12, 58, 42, "useCameraPermissions"], [1475, 44, 58, 62], [1475, 46, 72, 19, "useUpload"], [1475, 64, 72, 28], [1476, 2, 72, 28], [1477, 2, 72, 28, "_c"], [1477, 4, 72, 28], [1477, 7, 51, 24, "EchoCameraWeb"], [1477, 20, 51, 37], [1478, 2, 1138, 0], [1478, 8, 1138, 6, "styles"], [1478, 14, 1138, 12], [1478, 17, 1138, 15, "StyleSheet"], [1478, 36, 1138, 25], [1478, 37, 1138, 26, "create"], [1478, 43, 1138, 32], [1478, 44, 1138, 33], [1479, 4, 1139, 2, "container"], [1479, 13, 1139, 11], [1479, 15, 1139, 13], [1480, 6, 1140, 4, "flex"], [1480, 10, 1140, 8], [1480, 12, 1140, 10], [1480, 13, 1140, 11], [1481, 6, 1141, 4, "backgroundColor"], [1481, 21, 1141, 19], [1481, 23, 1141, 21], [1482, 4, 1142, 2], [1482, 5, 1142, 3], [1483, 4, 1143, 2, "cameraContainer"], [1483, 19, 1143, 17], [1483, 21, 1143, 19], [1484, 6, 1144, 4, "flex"], [1484, 10, 1144, 8], [1484, 12, 1144, 10], [1484, 13, 1144, 11], [1485, 6, 1145, 4, "max<PERSON><PERSON><PERSON>"], [1485, 14, 1145, 12], [1485, 16, 1145, 14], [1485, 19, 1145, 17], [1486, 6, 1146, 4, "alignSelf"], [1486, 15, 1146, 13], [1486, 17, 1146, 15], [1486, 25, 1146, 23], [1487, 6, 1147, 4, "width"], [1487, 11, 1147, 9], [1487, 13, 1147, 11], [1488, 4, 1148, 2], [1488, 5, 1148, 3], [1489, 4, 1149, 2, "camera"], [1489, 10, 1149, 8], [1489, 12, 1149, 10], [1490, 6, 1150, 4, "flex"], [1490, 10, 1150, 8], [1490, 12, 1150, 10], [1491, 4, 1151, 2], [1491, 5, 1151, 3], [1492, 4, 1152, 2, "headerOverlay"], [1492, 17, 1152, 15], [1492, 19, 1152, 17], [1493, 6, 1153, 4, "position"], [1493, 14, 1153, 12], [1493, 16, 1153, 14], [1493, 26, 1153, 24], [1494, 6, 1154, 4, "top"], [1494, 9, 1154, 7], [1494, 11, 1154, 9], [1494, 12, 1154, 10], [1495, 6, 1155, 4, "left"], [1495, 10, 1155, 8], [1495, 12, 1155, 10], [1495, 13, 1155, 11], [1496, 6, 1156, 4, "right"], [1496, 11, 1156, 9], [1496, 13, 1156, 11], [1496, 14, 1156, 12], [1497, 6, 1157, 4, "backgroundColor"], [1497, 21, 1157, 19], [1497, 23, 1157, 21], [1497, 36, 1157, 34], [1498, 6, 1158, 4, "paddingTop"], [1498, 16, 1158, 14], [1498, 18, 1158, 16], [1498, 20, 1158, 18], [1499, 6, 1159, 4, "paddingHorizontal"], [1499, 23, 1159, 21], [1499, 25, 1159, 23], [1499, 27, 1159, 25], [1500, 6, 1160, 4, "paddingBottom"], [1500, 19, 1160, 17], [1500, 21, 1160, 19], [1501, 4, 1161, 2], [1501, 5, 1161, 3], [1502, 4, 1162, 2, "headerContent"], [1502, 17, 1162, 15], [1502, 19, 1162, 17], [1503, 6, 1163, 4, "flexDirection"], [1503, 19, 1163, 17], [1503, 21, 1163, 19], [1503, 26, 1163, 24], [1504, 6, 1164, 4, "justifyContent"], [1504, 20, 1164, 18], [1504, 22, 1164, 20], [1504, 37, 1164, 35], [1505, 6, 1165, 4, "alignItems"], [1505, 16, 1165, 14], [1505, 18, 1165, 16], [1506, 4, 1166, 2], [1506, 5, 1166, 3], [1507, 4, 1167, 2, "headerLeft"], [1507, 14, 1167, 12], [1507, 16, 1167, 14], [1508, 6, 1168, 4, "flex"], [1508, 10, 1168, 8], [1508, 12, 1168, 10], [1509, 4, 1169, 2], [1509, 5, 1169, 3], [1510, 4, 1170, 2, "headerTitle"], [1510, 15, 1170, 13], [1510, 17, 1170, 15], [1511, 6, 1171, 4, "fontSize"], [1511, 14, 1171, 12], [1511, 16, 1171, 14], [1511, 18, 1171, 16], [1512, 6, 1172, 4, "fontWeight"], [1512, 16, 1172, 14], [1512, 18, 1172, 16], [1512, 23, 1172, 21], [1513, 6, 1173, 4, "color"], [1513, 11, 1173, 9], [1513, 13, 1173, 11], [1513, 19, 1173, 17], [1514, 6, 1174, 4, "marginBottom"], [1514, 18, 1174, 16], [1514, 20, 1174, 18], [1515, 4, 1175, 2], [1515, 5, 1175, 3], [1516, 4, 1176, 2, "subtitleRow"], [1516, 15, 1176, 13], [1516, 17, 1176, 15], [1517, 6, 1177, 4, "flexDirection"], [1517, 19, 1177, 17], [1517, 21, 1177, 19], [1517, 26, 1177, 24], [1518, 6, 1178, 4, "alignItems"], [1518, 16, 1178, 14], [1518, 18, 1178, 16], [1518, 26, 1178, 24], [1519, 6, 1179, 4, "marginBottom"], [1519, 18, 1179, 16], [1519, 20, 1179, 18], [1520, 4, 1180, 2], [1520, 5, 1180, 3], [1521, 4, 1181, 2, "webIcon"], [1521, 11, 1181, 9], [1521, 13, 1181, 11], [1522, 6, 1182, 4, "fontSize"], [1522, 14, 1182, 12], [1522, 16, 1182, 14], [1522, 18, 1182, 16], [1523, 6, 1183, 4, "marginRight"], [1523, 17, 1183, 15], [1523, 19, 1183, 17], [1524, 4, 1184, 2], [1524, 5, 1184, 3], [1525, 4, 1185, 2, "headerSubtitle"], [1525, 18, 1185, 16], [1525, 20, 1185, 18], [1526, 6, 1186, 4, "fontSize"], [1526, 14, 1186, 12], [1526, 16, 1186, 14], [1526, 18, 1186, 16], [1527, 6, 1187, 4, "color"], [1527, 11, 1187, 9], [1527, 13, 1187, 11], [1527, 19, 1187, 17], [1528, 6, 1188, 4, "opacity"], [1528, 13, 1188, 11], [1528, 15, 1188, 13], [1529, 4, 1189, 2], [1529, 5, 1189, 3], [1530, 4, 1190, 2, "challengeRow"], [1530, 16, 1190, 14], [1530, 18, 1190, 16], [1531, 6, 1191, 4, "flexDirection"], [1531, 19, 1191, 17], [1531, 21, 1191, 19], [1531, 26, 1191, 24], [1532, 6, 1192, 4, "alignItems"], [1532, 16, 1192, 14], [1532, 18, 1192, 16], [1533, 4, 1193, 2], [1533, 5, 1193, 3], [1534, 4, 1194, 2, "challengeCode"], [1534, 17, 1194, 15], [1534, 19, 1194, 17], [1535, 6, 1195, 4, "fontSize"], [1535, 14, 1195, 12], [1535, 16, 1195, 14], [1535, 18, 1195, 16], [1536, 6, 1196, 4, "color"], [1536, 11, 1196, 9], [1536, 13, 1196, 11], [1536, 19, 1196, 17], [1537, 6, 1197, 4, "marginLeft"], [1537, 16, 1197, 14], [1537, 18, 1197, 16], [1537, 19, 1197, 17], [1538, 6, 1198, 4, "fontFamily"], [1538, 16, 1198, 14], [1538, 18, 1198, 16], [1539, 4, 1199, 2], [1539, 5, 1199, 3], [1540, 4, 1200, 2, "closeButton"], [1540, 15, 1200, 13], [1540, 17, 1200, 15], [1541, 6, 1201, 4, "padding"], [1541, 13, 1201, 11], [1541, 15, 1201, 13], [1542, 4, 1202, 2], [1542, 5, 1202, 3], [1543, 4, 1203, 2, "privacyNotice"], [1543, 17, 1203, 15], [1543, 19, 1203, 17], [1544, 6, 1204, 4, "position"], [1544, 14, 1204, 12], [1544, 16, 1204, 14], [1544, 26, 1204, 24], [1545, 6, 1205, 4, "top"], [1545, 9, 1205, 7], [1545, 11, 1205, 9], [1545, 14, 1205, 12], [1546, 6, 1206, 4, "left"], [1546, 10, 1206, 8], [1546, 12, 1206, 10], [1546, 14, 1206, 12], [1547, 6, 1207, 4, "right"], [1547, 11, 1207, 9], [1547, 13, 1207, 11], [1547, 15, 1207, 13], [1548, 6, 1208, 4, "backgroundColor"], [1548, 21, 1208, 19], [1548, 23, 1208, 21], [1548, 48, 1208, 46], [1549, 6, 1209, 4, "borderRadius"], [1549, 18, 1209, 16], [1549, 20, 1209, 18], [1549, 21, 1209, 19], [1550, 6, 1210, 4, "padding"], [1550, 13, 1210, 11], [1550, 15, 1210, 13], [1550, 17, 1210, 15], [1551, 6, 1211, 4, "flexDirection"], [1551, 19, 1211, 17], [1551, 21, 1211, 19], [1551, 26, 1211, 24], [1552, 6, 1212, 4, "alignItems"], [1552, 16, 1212, 14], [1552, 18, 1212, 16], [1553, 4, 1213, 2], [1553, 5, 1213, 3], [1554, 4, 1214, 2, "privacyText"], [1554, 15, 1214, 13], [1554, 17, 1214, 15], [1555, 6, 1215, 4, "color"], [1555, 11, 1215, 9], [1555, 13, 1215, 11], [1555, 19, 1215, 17], [1556, 6, 1216, 4, "fontSize"], [1556, 14, 1216, 12], [1556, 16, 1216, 14], [1556, 18, 1216, 16], [1557, 6, 1217, 4, "marginLeft"], [1557, 16, 1217, 14], [1557, 18, 1217, 16], [1557, 19, 1217, 17], [1558, 6, 1218, 4, "flex"], [1558, 10, 1218, 8], [1558, 12, 1218, 10], [1559, 4, 1219, 2], [1559, 5, 1219, 3], [1560, 4, 1220, 2, "footer<PERSON><PERSON><PERSON>"], [1560, 17, 1220, 15], [1560, 19, 1220, 17], [1561, 6, 1221, 4, "position"], [1561, 14, 1221, 12], [1561, 16, 1221, 14], [1561, 26, 1221, 24], [1562, 6, 1222, 4, "bottom"], [1562, 12, 1222, 10], [1562, 14, 1222, 12], [1562, 15, 1222, 13], [1563, 6, 1223, 4, "left"], [1563, 10, 1223, 8], [1563, 12, 1223, 10], [1563, 13, 1223, 11], [1564, 6, 1224, 4, "right"], [1564, 11, 1224, 9], [1564, 13, 1224, 11], [1564, 14, 1224, 12], [1565, 6, 1225, 4, "backgroundColor"], [1565, 21, 1225, 19], [1565, 23, 1225, 21], [1565, 36, 1225, 34], [1566, 6, 1226, 4, "paddingBottom"], [1566, 19, 1226, 17], [1566, 21, 1226, 19], [1566, 23, 1226, 21], [1567, 6, 1227, 4, "paddingTop"], [1567, 16, 1227, 14], [1567, 18, 1227, 16], [1567, 20, 1227, 18], [1568, 6, 1228, 4, "alignItems"], [1568, 16, 1228, 14], [1568, 18, 1228, 16], [1569, 4, 1229, 2], [1569, 5, 1229, 3], [1570, 4, 1230, 2, "instruction"], [1570, 15, 1230, 13], [1570, 17, 1230, 15], [1571, 6, 1231, 4, "fontSize"], [1571, 14, 1231, 12], [1571, 16, 1231, 14], [1571, 18, 1231, 16], [1572, 6, 1232, 4, "color"], [1572, 11, 1232, 9], [1572, 13, 1232, 11], [1572, 19, 1232, 17], [1573, 6, 1233, 4, "marginBottom"], [1573, 18, 1233, 16], [1573, 20, 1233, 18], [1574, 4, 1234, 2], [1574, 5, 1234, 3], [1575, 4, 1235, 2, "shutterButton"], [1575, 17, 1235, 15], [1575, 19, 1235, 17], [1576, 6, 1236, 4, "width"], [1576, 11, 1236, 9], [1576, 13, 1236, 11], [1576, 15, 1236, 13], [1577, 6, 1237, 4, "height"], [1577, 12, 1237, 10], [1577, 14, 1237, 12], [1577, 16, 1237, 14], [1578, 6, 1238, 4, "borderRadius"], [1578, 18, 1238, 16], [1578, 20, 1238, 18], [1578, 22, 1238, 20], [1579, 6, 1239, 4, "backgroundColor"], [1579, 21, 1239, 19], [1579, 23, 1239, 21], [1579, 29, 1239, 27], [1580, 6, 1240, 4, "justifyContent"], [1580, 20, 1240, 18], [1580, 22, 1240, 20], [1580, 30, 1240, 28], [1581, 6, 1241, 4, "alignItems"], [1581, 16, 1241, 14], [1581, 18, 1241, 16], [1581, 26, 1241, 24], [1582, 6, 1242, 4, "marginBottom"], [1582, 18, 1242, 16], [1582, 20, 1242, 18], [1582, 22, 1242, 20], [1583, 6, 1243, 4], [1583, 9, 1243, 7, "Platform"], [1583, 26, 1243, 15], [1583, 27, 1243, 16, "select"], [1583, 33, 1243, 22], [1583, 34, 1243, 23], [1584, 8, 1244, 6, "ios"], [1584, 11, 1244, 9], [1584, 13, 1244, 11], [1585, 10, 1245, 8, "shadowColor"], [1585, 21, 1245, 19], [1585, 23, 1245, 21], [1585, 32, 1245, 30], [1586, 10, 1246, 8, "shadowOffset"], [1586, 22, 1246, 20], [1586, 24, 1246, 22], [1587, 12, 1246, 24, "width"], [1587, 17, 1246, 29], [1587, 19, 1246, 31], [1587, 20, 1246, 32], [1588, 12, 1246, 34, "height"], [1588, 18, 1246, 40], [1588, 20, 1246, 42], [1589, 10, 1246, 44], [1589, 11, 1246, 45], [1590, 10, 1247, 8, "shadowOpacity"], [1590, 23, 1247, 21], [1590, 25, 1247, 23], [1590, 28, 1247, 26], [1591, 10, 1248, 8, "shadowRadius"], [1591, 22, 1248, 20], [1591, 24, 1248, 22], [1592, 8, 1249, 6], [1592, 9, 1249, 7], [1593, 8, 1250, 6, "android"], [1593, 15, 1250, 13], [1593, 17, 1250, 15], [1594, 10, 1251, 8, "elevation"], [1594, 19, 1251, 17], [1594, 21, 1251, 19], [1595, 8, 1252, 6], [1595, 9, 1252, 7], [1596, 8, 1253, 6, "web"], [1596, 11, 1253, 9], [1596, 13, 1253, 11], [1597, 10, 1254, 8, "boxShadow"], [1597, 19, 1254, 17], [1597, 21, 1254, 19], [1598, 8, 1255, 6], [1599, 6, 1256, 4], [1599, 7, 1256, 5], [1600, 4, 1257, 2], [1600, 5, 1257, 3], [1601, 4, 1258, 2, "shutterButtonDisabled"], [1601, 25, 1258, 23], [1601, 27, 1258, 25], [1602, 6, 1259, 4, "opacity"], [1602, 13, 1259, 11], [1602, 15, 1259, 13], [1603, 4, 1260, 2], [1603, 5, 1260, 3], [1604, 4, 1261, 2, "shutterInner"], [1604, 16, 1261, 14], [1604, 18, 1261, 16], [1605, 6, 1262, 4, "width"], [1605, 11, 1262, 9], [1605, 13, 1262, 11], [1605, 15, 1262, 13], [1606, 6, 1263, 4, "height"], [1606, 12, 1263, 10], [1606, 14, 1263, 12], [1606, 16, 1263, 14], [1607, 6, 1264, 4, "borderRadius"], [1607, 18, 1264, 16], [1607, 20, 1264, 18], [1607, 22, 1264, 20], [1608, 6, 1265, 4, "backgroundColor"], [1608, 21, 1265, 19], [1608, 23, 1265, 21], [1608, 29, 1265, 27], [1609, 6, 1266, 4, "borderWidth"], [1609, 17, 1266, 15], [1609, 19, 1266, 17], [1609, 20, 1266, 18], [1610, 6, 1267, 4, "borderColor"], [1610, 17, 1267, 15], [1610, 19, 1267, 17], [1611, 4, 1268, 2], [1611, 5, 1268, 3], [1612, 4, 1269, 2, "privacyNote"], [1612, 15, 1269, 13], [1612, 17, 1269, 15], [1613, 6, 1270, 4, "fontSize"], [1613, 14, 1270, 12], [1613, 16, 1270, 14], [1613, 18, 1270, 16], [1614, 6, 1271, 4, "color"], [1614, 11, 1271, 9], [1614, 13, 1271, 11], [1615, 4, 1272, 2], [1615, 5, 1272, 3], [1616, 4, 1273, 2, "processingModal"], [1616, 19, 1273, 17], [1616, 21, 1273, 19], [1617, 6, 1274, 4, "flex"], [1617, 10, 1274, 8], [1617, 12, 1274, 10], [1617, 13, 1274, 11], [1618, 6, 1275, 4, "backgroundColor"], [1618, 21, 1275, 19], [1618, 23, 1275, 21], [1618, 43, 1275, 41], [1619, 6, 1276, 4, "justifyContent"], [1619, 20, 1276, 18], [1619, 22, 1276, 20], [1619, 30, 1276, 28], [1620, 6, 1277, 4, "alignItems"], [1620, 16, 1277, 14], [1620, 18, 1277, 16], [1621, 4, 1278, 2], [1621, 5, 1278, 3], [1622, 4, 1279, 2, "processingContent"], [1622, 21, 1279, 19], [1622, 23, 1279, 21], [1623, 6, 1280, 4, "backgroundColor"], [1623, 21, 1280, 19], [1623, 23, 1280, 21], [1623, 29, 1280, 27], [1624, 6, 1281, 4, "borderRadius"], [1624, 18, 1281, 16], [1624, 20, 1281, 18], [1624, 22, 1281, 20], [1625, 6, 1282, 4, "padding"], [1625, 13, 1282, 11], [1625, 15, 1282, 13], [1625, 17, 1282, 15], [1626, 6, 1283, 4, "width"], [1626, 11, 1283, 9], [1626, 13, 1283, 11], [1626, 18, 1283, 16], [1627, 6, 1284, 4, "max<PERSON><PERSON><PERSON>"], [1627, 14, 1284, 12], [1627, 16, 1284, 14], [1627, 19, 1284, 17], [1628, 6, 1285, 4, "alignItems"], [1628, 16, 1285, 14], [1628, 18, 1285, 16], [1629, 4, 1286, 2], [1629, 5, 1286, 3], [1630, 4, 1287, 2, "processingTitle"], [1630, 19, 1287, 17], [1630, 21, 1287, 19], [1631, 6, 1288, 4, "fontSize"], [1631, 14, 1288, 12], [1631, 16, 1288, 14], [1631, 18, 1288, 16], [1632, 6, 1289, 4, "fontWeight"], [1632, 16, 1289, 14], [1632, 18, 1289, 16], [1632, 23, 1289, 21], [1633, 6, 1290, 4, "color"], [1633, 11, 1290, 9], [1633, 13, 1290, 11], [1633, 22, 1290, 20], [1634, 6, 1291, 4, "marginTop"], [1634, 15, 1291, 13], [1634, 17, 1291, 15], [1634, 19, 1291, 17], [1635, 6, 1292, 4, "marginBottom"], [1635, 18, 1292, 16], [1635, 20, 1292, 18], [1636, 4, 1293, 2], [1636, 5, 1293, 3], [1637, 4, 1294, 2, "progressBar"], [1637, 15, 1294, 13], [1637, 17, 1294, 15], [1638, 6, 1295, 4, "width"], [1638, 11, 1295, 9], [1638, 13, 1295, 11], [1638, 19, 1295, 17], [1639, 6, 1296, 4, "height"], [1639, 12, 1296, 10], [1639, 14, 1296, 12], [1639, 15, 1296, 13], [1640, 6, 1297, 4, "backgroundColor"], [1640, 21, 1297, 19], [1640, 23, 1297, 21], [1640, 32, 1297, 30], [1641, 6, 1298, 4, "borderRadius"], [1641, 18, 1298, 16], [1641, 20, 1298, 18], [1641, 21, 1298, 19], [1642, 6, 1299, 4, "overflow"], [1642, 14, 1299, 12], [1642, 16, 1299, 14], [1642, 24, 1299, 22], [1643, 6, 1300, 4, "marginBottom"], [1643, 18, 1300, 16], [1643, 20, 1300, 18], [1644, 4, 1301, 2], [1644, 5, 1301, 3], [1645, 4, 1302, 2, "progressFill"], [1645, 16, 1302, 14], [1645, 18, 1302, 16], [1646, 6, 1303, 4, "height"], [1646, 12, 1303, 10], [1646, 14, 1303, 12], [1646, 20, 1303, 18], [1647, 6, 1304, 4, "backgroundColor"], [1647, 21, 1304, 19], [1647, 23, 1304, 21], [1647, 32, 1304, 30], [1648, 6, 1305, 4, "borderRadius"], [1648, 18, 1305, 16], [1648, 20, 1305, 18], [1649, 4, 1306, 2], [1649, 5, 1306, 3], [1650, 4, 1307, 2, "processingDescription"], [1650, 25, 1307, 23], [1650, 27, 1307, 25], [1651, 6, 1308, 4, "fontSize"], [1651, 14, 1308, 12], [1651, 16, 1308, 14], [1651, 18, 1308, 16], [1652, 6, 1309, 4, "color"], [1652, 11, 1309, 9], [1652, 13, 1309, 11], [1652, 22, 1309, 20], [1653, 6, 1310, 4, "textAlign"], [1653, 15, 1310, 13], [1653, 17, 1310, 15], [1654, 4, 1311, 2], [1654, 5, 1311, 3], [1655, 4, 1312, 2, "successIcon"], [1655, 15, 1312, 13], [1655, 17, 1312, 15], [1656, 6, 1313, 4, "marginTop"], [1656, 15, 1313, 13], [1656, 17, 1313, 15], [1657, 4, 1314, 2], [1657, 5, 1314, 3], [1658, 4, 1315, 2, "errorContent"], [1658, 16, 1315, 14], [1658, 18, 1315, 16], [1659, 6, 1316, 4, "backgroundColor"], [1659, 21, 1316, 19], [1659, 23, 1316, 21], [1659, 29, 1316, 27], [1660, 6, 1317, 4, "borderRadius"], [1660, 18, 1317, 16], [1660, 20, 1317, 18], [1660, 22, 1317, 20], [1661, 6, 1318, 4, "padding"], [1661, 13, 1318, 11], [1661, 15, 1318, 13], [1661, 17, 1318, 15], [1662, 6, 1319, 4, "width"], [1662, 11, 1319, 9], [1662, 13, 1319, 11], [1662, 18, 1319, 16], [1663, 6, 1320, 4, "max<PERSON><PERSON><PERSON>"], [1663, 14, 1320, 12], [1663, 16, 1320, 14], [1663, 19, 1320, 17], [1664, 6, 1321, 4, "alignItems"], [1664, 16, 1321, 14], [1664, 18, 1321, 16], [1665, 4, 1322, 2], [1665, 5, 1322, 3], [1666, 4, 1323, 2, "errorTitle"], [1666, 14, 1323, 12], [1666, 16, 1323, 14], [1667, 6, 1324, 4, "fontSize"], [1667, 14, 1324, 12], [1667, 16, 1324, 14], [1667, 18, 1324, 16], [1668, 6, 1325, 4, "fontWeight"], [1668, 16, 1325, 14], [1668, 18, 1325, 16], [1668, 23, 1325, 21], [1669, 6, 1326, 4, "color"], [1669, 11, 1326, 9], [1669, 13, 1326, 11], [1669, 22, 1326, 20], [1670, 6, 1327, 4, "marginTop"], [1670, 15, 1327, 13], [1670, 17, 1327, 15], [1670, 19, 1327, 17], [1671, 6, 1328, 4, "marginBottom"], [1671, 18, 1328, 16], [1671, 20, 1328, 18], [1672, 4, 1329, 2], [1672, 5, 1329, 3], [1673, 4, 1330, 2, "errorMessage"], [1673, 16, 1330, 14], [1673, 18, 1330, 16], [1674, 6, 1331, 4, "fontSize"], [1674, 14, 1331, 12], [1674, 16, 1331, 14], [1674, 18, 1331, 16], [1675, 6, 1332, 4, "color"], [1675, 11, 1332, 9], [1675, 13, 1332, 11], [1675, 22, 1332, 20], [1676, 6, 1333, 4, "textAlign"], [1676, 15, 1333, 13], [1676, 17, 1333, 15], [1676, 25, 1333, 23], [1677, 6, 1334, 4, "marginBottom"], [1677, 18, 1334, 16], [1677, 20, 1334, 18], [1678, 4, 1335, 2], [1678, 5, 1335, 3], [1679, 4, 1336, 2, "primaryButton"], [1679, 17, 1336, 15], [1679, 19, 1336, 17], [1680, 6, 1337, 4, "backgroundColor"], [1680, 21, 1337, 19], [1680, 23, 1337, 21], [1680, 32, 1337, 30], [1681, 6, 1338, 4, "paddingHorizontal"], [1681, 23, 1338, 21], [1681, 25, 1338, 23], [1681, 27, 1338, 25], [1682, 6, 1339, 4, "paddingVertical"], [1682, 21, 1339, 19], [1682, 23, 1339, 21], [1682, 25, 1339, 23], [1683, 6, 1340, 4, "borderRadius"], [1683, 18, 1340, 16], [1683, 20, 1340, 18], [1683, 21, 1340, 19], [1684, 6, 1341, 4, "marginTop"], [1684, 15, 1341, 13], [1684, 17, 1341, 15], [1685, 4, 1342, 2], [1685, 5, 1342, 3], [1686, 4, 1343, 2, "primaryButtonText"], [1686, 21, 1343, 19], [1686, 23, 1343, 21], [1687, 6, 1344, 4, "color"], [1687, 11, 1344, 9], [1687, 13, 1344, 11], [1687, 19, 1344, 17], [1688, 6, 1345, 4, "fontSize"], [1688, 14, 1345, 12], [1688, 16, 1345, 14], [1688, 18, 1345, 16], [1689, 6, 1346, 4, "fontWeight"], [1689, 16, 1346, 14], [1689, 18, 1346, 16], [1690, 4, 1347, 2], [1690, 5, 1347, 3], [1691, 4, 1348, 2, "secondaryButton"], [1691, 19, 1348, 17], [1691, 21, 1348, 19], [1692, 6, 1349, 4, "paddingHorizontal"], [1692, 23, 1349, 21], [1692, 25, 1349, 23], [1692, 27, 1349, 25], [1693, 6, 1350, 4, "paddingVertical"], [1693, 21, 1350, 19], [1693, 23, 1350, 21], [1693, 25, 1350, 23], [1694, 6, 1351, 4, "marginTop"], [1694, 15, 1351, 13], [1694, 17, 1351, 15], [1695, 4, 1352, 2], [1695, 5, 1352, 3], [1696, 4, 1353, 2, "secondaryButtonText"], [1696, 23, 1353, 21], [1696, 25, 1353, 23], [1697, 6, 1354, 4, "color"], [1697, 11, 1354, 9], [1697, 13, 1354, 11], [1697, 22, 1354, 20], [1698, 6, 1355, 4, "fontSize"], [1698, 14, 1355, 12], [1698, 16, 1355, 14], [1699, 4, 1356, 2], [1699, 5, 1356, 3], [1700, 4, 1357, 2, "permissionContent"], [1700, 21, 1357, 19], [1700, 23, 1357, 21], [1701, 6, 1358, 4, "flex"], [1701, 10, 1358, 8], [1701, 12, 1358, 10], [1701, 13, 1358, 11], [1702, 6, 1359, 4, "justifyContent"], [1702, 20, 1359, 18], [1702, 22, 1359, 20], [1702, 30, 1359, 28], [1703, 6, 1360, 4, "alignItems"], [1703, 16, 1360, 14], [1703, 18, 1360, 16], [1703, 26, 1360, 24], [1704, 6, 1361, 4, "padding"], [1704, 13, 1361, 11], [1704, 15, 1361, 13], [1705, 4, 1362, 2], [1705, 5, 1362, 3], [1706, 4, 1363, 2, "permissionTitle"], [1706, 19, 1363, 17], [1706, 21, 1363, 19], [1707, 6, 1364, 4, "fontSize"], [1707, 14, 1364, 12], [1707, 16, 1364, 14], [1707, 18, 1364, 16], [1708, 6, 1365, 4, "fontWeight"], [1708, 16, 1365, 14], [1708, 18, 1365, 16], [1708, 23, 1365, 21], [1709, 6, 1366, 4, "color"], [1709, 11, 1366, 9], [1709, 13, 1366, 11], [1709, 22, 1366, 20], [1710, 6, 1367, 4, "marginTop"], [1710, 15, 1367, 13], [1710, 17, 1367, 15], [1710, 19, 1367, 17], [1711, 6, 1368, 4, "marginBottom"], [1711, 18, 1368, 16], [1711, 20, 1368, 18], [1712, 4, 1369, 2], [1712, 5, 1369, 3], [1713, 4, 1370, 2, "permissionDescription"], [1713, 25, 1370, 23], [1713, 27, 1370, 25], [1714, 6, 1371, 4, "fontSize"], [1714, 14, 1371, 12], [1714, 16, 1371, 14], [1714, 18, 1371, 16], [1715, 6, 1372, 4, "color"], [1715, 11, 1372, 9], [1715, 13, 1372, 11], [1715, 22, 1372, 20], [1716, 6, 1373, 4, "textAlign"], [1716, 15, 1373, 13], [1716, 17, 1373, 15], [1716, 25, 1373, 23], [1717, 6, 1374, 4, "marginBottom"], [1717, 18, 1374, 16], [1717, 20, 1374, 18], [1718, 4, 1375, 2], [1718, 5, 1375, 3], [1719, 4, 1376, 2, "loadingText"], [1719, 15, 1376, 13], [1719, 17, 1376, 15], [1720, 6, 1377, 4, "color"], [1720, 11, 1377, 9], [1720, 13, 1377, 11], [1720, 22, 1377, 20], [1721, 6, 1378, 4, "marginTop"], [1721, 15, 1378, 13], [1721, 17, 1378, 15], [1722, 4, 1379, 2], [1722, 5, 1379, 3], [1723, 4, 1380, 2], [1724, 4, 1381, 2, "blurZone"], [1724, 12, 1381, 10], [1724, 14, 1381, 12], [1725, 6, 1382, 4, "position"], [1725, 14, 1382, 12], [1725, 16, 1382, 14], [1725, 26, 1382, 24], [1726, 6, 1383, 4, "overflow"], [1726, 14, 1383, 12], [1726, 16, 1383, 14], [1727, 4, 1384, 2], [1727, 5, 1384, 3], [1728, 4, 1385, 2, "previewChip"], [1728, 15, 1385, 13], [1728, 17, 1385, 15], [1729, 6, 1386, 4, "position"], [1729, 14, 1386, 12], [1729, 16, 1386, 14], [1729, 26, 1386, 24], [1730, 6, 1387, 4, "top"], [1730, 9, 1387, 7], [1730, 11, 1387, 9], [1730, 12, 1387, 10], [1731, 6, 1388, 4, "right"], [1731, 11, 1388, 9], [1731, 13, 1388, 11], [1731, 14, 1388, 12], [1732, 6, 1389, 4, "backgroundColor"], [1732, 21, 1389, 19], [1732, 23, 1389, 21], [1732, 40, 1389, 38], [1733, 6, 1390, 4, "paddingHorizontal"], [1733, 23, 1390, 21], [1733, 25, 1390, 23], [1733, 27, 1390, 25], [1734, 6, 1391, 4, "paddingVertical"], [1734, 21, 1391, 19], [1734, 23, 1391, 21], [1734, 24, 1391, 22], [1735, 6, 1392, 4, "borderRadius"], [1735, 18, 1392, 16], [1735, 20, 1392, 18], [1736, 4, 1393, 2], [1736, 5, 1393, 3], [1737, 4, 1394, 2, "previewChipText"], [1737, 19, 1394, 17], [1737, 21, 1394, 19], [1738, 6, 1395, 4, "color"], [1738, 11, 1395, 9], [1738, 13, 1395, 11], [1738, 19, 1395, 17], [1739, 6, 1396, 4, "fontSize"], [1739, 14, 1396, 12], [1739, 16, 1396, 14], [1739, 18, 1396, 16], [1740, 6, 1397, 4, "fontWeight"], [1740, 16, 1397, 14], [1740, 18, 1397, 16], [1741, 4, 1398, 2], [1742, 2, 1399, 0], [1742, 3, 1399, 1], [1742, 4, 1399, 2], [1743, 2, 1399, 3], [1743, 6, 1399, 3, "_c"], [1743, 8, 1399, 3], [1744, 2, 1399, 3, "$RefreshReg$"], [1744, 14, 1399, 3], [1744, 15, 1399, 3, "_c"], [1744, 17, 1399, 3], [1745, 0, 1399, 3], [1745, 3]], "functionMap": {"names": ["<global>", "EchoCameraWeb", "useEffect$argument_0", "<anonymous>", "loadTensorFlowFaceDetection", "Promise$argument_0", "detectFacesWithTensorFlow", "predictions.map$argument_0", "detectFacesHeuristic", "faces.sort$argument_0", "detectFacesAggressive", "analyzeRegionForFace", "isSkinTone", "mergeFaceDetections", "calculateOverlap", "mergeTwoFaces", "applyStrongBlur", "applySimpleBlur", "applyFallbackFaceBlur", "areas.forEach$argument_0", "capturePhoto", "processImageWithFaceBlur", "detectedFaces.map$argument_0", "detectedFaces.forEach$argument_0", "canvas.toBlob$argument_0", "completeProcessing", "triggerServerProcessing", "pollForCompletion", "setTimeout$argument_0", "getAuthToken", "retryCapture", "CameraView.props.onLayout", "CameraView.props.onCameraReady", "CameraView.props.onMountError"], "mappings": "AAA;eCkD;YCuB;KCG;KDQ;WCE,wBD;GDC;sCGG;wBCK;ODM;wBCK;ODM;GHI;oCKE;oCCqB;ODoB;GLO;+BOE;eCuC,mDD;GPK;gCSE;GToC;+BUE;GV0C;qBWE;GXQ;8BYE;GZ4B;2BaE;Gba;wBcE;GdiB;0BeG;GfuE;0BgBE;GhBuB;gCiBE;kBCa;KDG;GjBC;mCmBG;wBfc,kCe;GnBoC;mCoBE;wBhBc;OgBI;oFC+C;UDM;8BE8B;SFoD;uDhBa;sBmBC,wBnB;OgBC;GpBmB;6BwBG;GxB6B;kCyBG;GzB8C;4B0BE;mBCmD;SDE;G1BO;uB4BE;G5BI;mC6BG;G7BM;YCE;GDK;oB8B2C;W9BG;yB+BC;W/BG;wBgCC;WhCI;CD4L"}}, "type": "js/module"}]}