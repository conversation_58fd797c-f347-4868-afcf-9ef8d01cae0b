{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = range;\n  function range(ids, coords, minX, minY, maxX, maxY, nodeSize) {\n    const stack = [0, ids.length - 1, 0];\n    const result = [];\n    let x, y;\n    while (stack.length) {\n      const axis = stack.pop();\n      const right = stack.pop();\n      const left = stack.pop();\n      if (right - left <= nodeSize) {\n        for (let i = left; i <= right; i++) {\n          x = coords[2 * i];\n          y = coords[2 * i + 1];\n          if (x >= minX && x <= maxX && y >= minY && y <= maxY) result.push(ids[i]);\n        }\n        continue;\n      }\n      const m = Math.floor((left + right) / 2);\n      x = coords[2 * m];\n      y = coords[2 * m + 1];\n      if (x >= minX && x <= maxX && y >= minY && y <= maxY) result.push(ids[m]);\n      const nextAxis = (axis + 1) % 2;\n      if (axis === 0 ? minX <= x : minY <= y) {\n        stack.push(left);\n        stack.push(m - 1);\n        stack.push(nextAxis);\n      }\n      if (axis === 0 ? maxX >= x : maxY >= y) {\n        stack.push(m + 1);\n        stack.push(right);\n        stack.push(nextAxis);\n      }\n    }\n    return result;\n  }\n});", "lineCount": 40, "map": [[6, 2, 2, 15], [6, 11, 2, 24, "range"], [6, 16, 2, 29, "range"], [6, 17, 2, 30, "ids"], [6, 20, 2, 33], [6, 22, 2, 35, "coords"], [6, 28, 2, 41], [6, 30, 2, 43, "minX"], [6, 34, 2, 47], [6, 36, 2, 49, "minY"], [6, 40, 2, 53], [6, 42, 2, 55, "maxX"], [6, 46, 2, 59], [6, 48, 2, 61, "maxY"], [6, 52, 2, 65], [6, 54, 2, 67, "nodeSize"], [6, 62, 2, 75], [6, 64, 2, 77], [7, 4, 3, 4], [7, 10, 3, 10, "stack"], [7, 15, 3, 15], [7, 18, 3, 18], [7, 19, 3, 19], [7, 20, 3, 20], [7, 22, 3, 22, "ids"], [7, 25, 3, 25], [7, 26, 3, 26, "length"], [7, 32, 3, 32], [7, 35, 3, 35], [7, 36, 3, 36], [7, 38, 3, 38], [7, 39, 3, 39], [7, 40, 3, 40], [8, 4, 4, 4], [8, 10, 4, 10, "result"], [8, 16, 4, 16], [8, 19, 4, 19], [8, 21, 4, 21], [9, 4, 5, 4], [9, 8, 5, 8, "x"], [9, 9, 5, 9], [9, 11, 5, 11, "y"], [9, 12, 5, 12], [10, 4, 7, 4], [10, 11, 7, 11, "stack"], [10, 16, 7, 16], [10, 17, 7, 17, "length"], [10, 23, 7, 23], [10, 25, 7, 25], [11, 6, 8, 8], [11, 12, 8, 14, "axis"], [11, 16, 8, 18], [11, 19, 8, 21, "stack"], [11, 24, 8, 26], [11, 25, 8, 27, "pop"], [11, 28, 8, 30], [11, 29, 8, 31], [11, 30, 8, 32], [12, 6, 9, 8], [12, 12, 9, 14, "right"], [12, 17, 9, 19], [12, 20, 9, 22, "stack"], [12, 25, 9, 27], [12, 26, 9, 28, "pop"], [12, 29, 9, 31], [12, 30, 9, 32], [12, 31, 9, 33], [13, 6, 10, 8], [13, 12, 10, 14, "left"], [13, 16, 10, 18], [13, 19, 10, 21, "stack"], [13, 24, 10, 26], [13, 25, 10, 27, "pop"], [13, 28, 10, 30], [13, 29, 10, 31], [13, 30, 10, 32], [14, 6, 12, 8], [14, 10, 12, 12, "right"], [14, 15, 12, 17], [14, 18, 12, 20, "left"], [14, 22, 12, 24], [14, 26, 12, 28, "nodeSize"], [14, 34, 12, 36], [14, 36, 12, 38], [15, 8, 13, 12], [15, 13, 13, 17], [15, 17, 13, 21, "i"], [15, 18, 13, 22], [15, 21, 13, 25, "left"], [15, 25, 13, 29], [15, 27, 13, 31, "i"], [15, 28, 13, 32], [15, 32, 13, 36, "right"], [15, 37, 13, 41], [15, 39, 13, 43, "i"], [15, 40, 13, 44], [15, 42, 13, 46], [15, 44, 13, 48], [16, 10, 14, 16, "x"], [16, 11, 14, 17], [16, 14, 14, 20, "coords"], [16, 20, 14, 26], [16, 21, 14, 27], [16, 22, 14, 28], [16, 25, 14, 31, "i"], [16, 26, 14, 32], [16, 27, 14, 33], [17, 10, 15, 16, "y"], [17, 11, 15, 17], [17, 14, 15, 20, "coords"], [17, 20, 15, 26], [17, 21, 15, 27], [17, 22, 15, 28], [17, 25, 15, 31, "i"], [17, 26, 15, 32], [17, 29, 15, 35], [17, 30, 15, 36], [17, 31, 15, 37], [18, 10, 16, 16], [18, 14, 16, 20, "x"], [18, 15, 16, 21], [18, 19, 16, 25, "minX"], [18, 23, 16, 29], [18, 27, 16, 33, "x"], [18, 28, 16, 34], [18, 32, 16, 38, "maxX"], [18, 36, 16, 42], [18, 40, 16, 46, "y"], [18, 41, 16, 47], [18, 45, 16, 51, "minY"], [18, 49, 16, 55], [18, 53, 16, 59, "y"], [18, 54, 16, 60], [18, 58, 16, 64, "maxY"], [18, 62, 16, 68], [18, 64, 16, 70, "result"], [18, 70, 16, 76], [18, 71, 16, 77, "push"], [18, 75, 16, 81], [18, 76, 16, 82, "ids"], [18, 79, 16, 85], [18, 80, 16, 86, "i"], [18, 81, 16, 87], [18, 82, 16, 88], [18, 83, 16, 89], [19, 8, 17, 12], [20, 8, 18, 12], [21, 6, 19, 8], [22, 6, 21, 8], [22, 12, 21, 14, "m"], [22, 13, 21, 15], [22, 16, 21, 18, "Math"], [22, 20, 21, 22], [22, 21, 21, 23, "floor"], [22, 26, 21, 28], [22, 27, 21, 29], [22, 28, 21, 30, "left"], [22, 32, 21, 34], [22, 35, 21, 37, "right"], [22, 40, 21, 42], [22, 44, 21, 46], [22, 45, 21, 47], [22, 46, 21, 48], [23, 6, 23, 8, "x"], [23, 7, 23, 9], [23, 10, 23, 12, "coords"], [23, 16, 23, 18], [23, 17, 23, 19], [23, 18, 23, 20], [23, 21, 23, 23, "m"], [23, 22, 23, 24], [23, 23, 23, 25], [24, 6, 24, 8, "y"], [24, 7, 24, 9], [24, 10, 24, 12, "coords"], [24, 16, 24, 18], [24, 17, 24, 19], [24, 18, 24, 20], [24, 21, 24, 23, "m"], [24, 22, 24, 24], [24, 25, 24, 27], [24, 26, 24, 28], [24, 27, 24, 29], [25, 6, 26, 8], [25, 10, 26, 12, "x"], [25, 11, 26, 13], [25, 15, 26, 17, "minX"], [25, 19, 26, 21], [25, 23, 26, 25, "x"], [25, 24, 26, 26], [25, 28, 26, 30, "maxX"], [25, 32, 26, 34], [25, 36, 26, 38, "y"], [25, 37, 26, 39], [25, 41, 26, 43, "minY"], [25, 45, 26, 47], [25, 49, 26, 51, "y"], [25, 50, 26, 52], [25, 54, 26, 56, "maxY"], [25, 58, 26, 60], [25, 60, 26, 62, "result"], [25, 66, 26, 68], [25, 67, 26, 69, "push"], [25, 71, 26, 73], [25, 72, 26, 74, "ids"], [25, 75, 26, 77], [25, 76, 26, 78, "m"], [25, 77, 26, 79], [25, 78, 26, 80], [25, 79, 26, 81], [26, 6, 28, 8], [26, 12, 28, 14, "nextAxis"], [26, 20, 28, 22], [26, 23, 28, 25], [26, 24, 28, 26, "axis"], [26, 28, 28, 30], [26, 31, 28, 33], [26, 32, 28, 34], [26, 36, 28, 38], [26, 37, 28, 39], [27, 6, 30, 8], [27, 10, 30, 12, "axis"], [27, 14, 30, 16], [27, 19, 30, 21], [27, 20, 30, 22], [27, 23, 30, 25, "minX"], [27, 27, 30, 29], [27, 31, 30, 33, "x"], [27, 32, 30, 34], [27, 35, 30, 37, "minY"], [27, 39, 30, 41], [27, 43, 30, 45, "y"], [27, 44, 30, 46], [27, 46, 30, 48], [28, 8, 31, 12, "stack"], [28, 13, 31, 17], [28, 14, 31, 18, "push"], [28, 18, 31, 22], [28, 19, 31, 23, "left"], [28, 23, 31, 27], [28, 24, 31, 28], [29, 8, 32, 12, "stack"], [29, 13, 32, 17], [29, 14, 32, 18, "push"], [29, 18, 32, 22], [29, 19, 32, 23, "m"], [29, 20, 32, 24], [29, 23, 32, 27], [29, 24, 32, 28], [29, 25, 32, 29], [30, 8, 33, 12, "stack"], [30, 13, 33, 17], [30, 14, 33, 18, "push"], [30, 18, 33, 22], [30, 19, 33, 23, "nextAxis"], [30, 27, 33, 31], [30, 28, 33, 32], [31, 6, 34, 8], [32, 6, 35, 8], [32, 10, 35, 12, "axis"], [32, 14, 35, 16], [32, 19, 35, 21], [32, 20, 35, 22], [32, 23, 35, 25, "maxX"], [32, 27, 35, 29], [32, 31, 35, 33, "x"], [32, 32, 35, 34], [32, 35, 35, 37, "maxY"], [32, 39, 35, 41], [32, 43, 35, 45, "y"], [32, 44, 35, 46], [32, 46, 35, 48], [33, 8, 36, 12, "stack"], [33, 13, 36, 17], [33, 14, 36, 18, "push"], [33, 18, 36, 22], [33, 19, 36, 23, "m"], [33, 20, 36, 24], [33, 23, 36, 27], [33, 24, 36, 28], [33, 25, 36, 29], [34, 8, 37, 12, "stack"], [34, 13, 37, 17], [34, 14, 37, 18, "push"], [34, 18, 37, 22], [34, 19, 37, 23, "right"], [34, 24, 37, 28], [34, 25, 37, 29], [35, 8, 38, 12, "stack"], [35, 13, 38, 17], [35, 14, 38, 18, "push"], [35, 18, 38, 22], [35, 19, 38, 23, "nextAxis"], [35, 27, 38, 31], [35, 28, 38, 32], [36, 6, 39, 8], [37, 4, 40, 4], [38, 4, 42, 4], [38, 11, 42, 11, "result"], [38, 17, 42, 17], [39, 2, 43, 0], [40, 0, 43, 1], [40, 3]], "functionMap": {"names": ["<global>", "range"], "mappings": "AAA;eCC;CDyC"}}, "type": "js/module"}]}