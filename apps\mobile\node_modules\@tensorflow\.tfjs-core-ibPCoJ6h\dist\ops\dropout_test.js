"use strict";
/**
 * @license
 * Copyright 2018 Google Inc. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var _this = this;
Object.defineProperty(exports, "__esModule", { value: true });
var tf = require("../index");
var jasmine_util_1 = require("../jasmine_util");
var test_util_1 = require("../test_util");
var tensor_ops_1 = require("./tensor_ops");
jasmine_util_1.describeWithFlags('dropout', jasmine_util_1.ALL_ENVS, function () {
    it('x 1d array, rate 0', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, rate, output, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    x = tensor_ops_1.tensor1d([1, 2, 2, 1]);
                    rate = 0;
                    output = tf.dropout(x, rate);
                    expect(output.dtype).toEqual(x.dtype);
                    expect(output.shape).toEqual(x.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, x.data()];
                case 1:
                    _b = [_c.sent()];
                    return [4 /*yield*/, output.data()];
                case 2:
                    _a.apply(void 0, _b.concat([_c.sent()]));
                    return [2 /*return*/];
            }
        });
    }); });
    it('x 1d array, rate 0.75', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, rate, output, xValues, outputValues, i;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tensor_ops_1.tensor1d([1, 2, 2, 1]);
                    rate = 0.75;
                    output = tf.dropout(x, rate);
                    expect(output.dtype).toEqual(x.dtype);
                    expect(output.shape).toEqual(x.shape);
                    return [4 /*yield*/, x.data()];
                case 1:
                    xValues = _a.sent();
                    return [4 /*yield*/, output.data()];
                case 2:
                    outputValues = _a.sent();
                    for (i = 0; i < xValues.length; i++) {
                        if (outputValues[i] !== 0) {
                            expect(outputValues[i]).toBeCloseTo(1 / (1 - rate) * xValues[i]);
                        }
                    }
                    return [2 /*return*/];
            }
        });
    }); });
    it('x 2d array, rate 0', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, rate, output, _a, _b;
        return __generator(this, function (_c) {
            switch (_c.label) {
                case 0:
                    x = tensor_ops_1.tensor2d([1, 5, 2, 4, 3, 6], [2, 3]);
                    rate = 0;
                    output = tf.dropout(x, rate);
                    expect(output.dtype).toEqual(x.dtype);
                    expect(output.shape).toEqual(x.shape);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, x.data()];
                case 1:
                    _b = [_c.sent()];
                    return [4 /*yield*/, output.data()];
                case 2:
                    _a.apply(void 0, _b.concat([_c.sent()]));
                    return [2 /*return*/];
            }
        });
    }); });
    it('x 2d array, rate 0.75', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, rate, output, xValues, outputValues, i;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tensor_ops_1.tensor2d([1, 5, 2, 4, 3, 6], [2, 3]);
                    rate = 0.75;
                    output = tf.dropout(x, rate);
                    expect(output.dtype).toEqual(x.dtype);
                    expect(output.shape).toEqual(x.shape);
                    return [4 /*yield*/, x.data()];
                case 1:
                    xValues = _a.sent();
                    return [4 /*yield*/, output.data()];
                case 2:
                    outputValues = _a.sent();
                    for (i = 0; i < xValues.length; i++) {
                        if (outputValues[i] !== 0) {
                            expect(outputValues[i]).toBeCloseTo(1 / (1 - rate) * xValues[i]);
                        }
                    }
                    return [2 /*return*/];
            }
        });
    }); });
    it('x 1d array, rate 0.75, with noise shape length = 1', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, rate, noiseShape, output, xValues, outputValues, maskedOutput, i;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tensor_ops_1.tensor1d([1, 2, 2, 1]);
                    rate = 0.75;
                    noiseShape = [1];
                    output = tf.dropout(x, rate, noiseShape);
                    expect(output.dtype).toEqual(x.dtype);
                    expect(output.shape).toEqual(x.shape);
                    return [4 /*yield*/, x.data()];
                case 1:
                    xValues = _a.sent();
                    return [4 /*yield*/, output.data()];
                case 2:
                    outputValues = _a.sent();
                    maskedOutput = outputValues[0];
                    for (i = 0; i < xValues.length; i++) {
                        if (maskedOutput === 0) {
                            expect(outputValues[i]).toBe(maskedOutput);
                        }
                        if (outputValues[i] !== 0) {
                            expect(outputValues[i]).toBeCloseTo(1 / (1 - rate) * xValues[i]);
                        }
                    }
                    return [2 /*return*/];
            }
        });
    }); });
    it('x 2d array, rate 0.75, with noise shape length = 2', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, rate, noiseShape, output, xValues, outputValues, i, maskedOutput, j;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tensor_ops_1.tensor2d([1, 5, 2, 4, 3, 6], [2, 3]);
                    rate = 0.75;
                    noiseShape = [2, 1];
                    output = tf.dropout(x, rate, noiseShape);
                    expect(output.dtype).toEqual(x.dtype);
                    expect(output.shape).toEqual(x.shape);
                    return [4 /*yield*/, x.data()];
                case 1:
                    xValues = _a.sent();
                    return [4 /*yield*/, output.data()];
                case 2:
                    outputValues = _a.sent();
                    for (i = 0; i < x.shape[0]; i++) {
                        maskedOutput = outputValues[i * x.shape[1]];
                        if (maskedOutput !== 0) {
                            expect(maskedOutput)
                                .toBeCloseTo(1 / (1 - rate) * xValues[i * x.shape[1]]);
                        }
                        else {
                            for (j = 0; j < x.shape[1]; j++) {
                                expect(outputValues[i * x.shape[1] + j]).toBe(maskedOutput);
                            }
                        }
                    }
                    return [2 /*return*/];
            }
        });
    }); });
    it('broadcast noise shape', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, rate, noiseShape, output, xValues, outputValues, i, maskedOutput, j;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tensor_ops_1.tensor2d([1, 5, 2, 4, 3, 6], [2, 3]);
                    rate = 0.75;
                    noiseShape = [1];
                    output = tf.dropout(x, rate, noiseShape);
                    expect(output.dtype).toEqual(x.dtype);
                    expect(output.shape).toEqual(x.shape);
                    return [4 /*yield*/, x.data()];
                case 1:
                    xValues = _a.sent();
                    return [4 /*yield*/, output.data()];
                case 2:
                    outputValues = _a.sent();
                    for (i = 0; i < x.shape[0]; i++) {
                        maskedOutput = outputValues[i * x.shape[1]];
                        if (maskedOutput !== 0) {
                            expect(maskedOutput)
                                .toBeCloseTo(1 / (1 - rate) * xValues[i * x.shape[1]]);
                        }
                        else {
                            for (j = 0; j < x.shape[1]; j++) {
                                expect(outputValues[i * x.shape[1] + j]).toBe(maskedOutput);
                            }
                        }
                    }
                    return [2 /*return*/];
            }
        });
    }); });
    it('x 1d array, rate 0.75, with seed', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, rate, seed, output, xValues, outputValues, i;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    x = tensor_ops_1.tensor1d([1, 2, 2, 1]);
                    rate = 0.75;
                    seed = 23;
                    output = tf.dropout(x, rate, null, seed);
                    expect(output.dtype).toEqual(x.dtype);
                    expect(output.shape).toEqual(x.shape);
                    return [4 /*yield*/, x.data()];
                case 1:
                    xValues = _a.sent();
                    return [4 /*yield*/, output.data()];
                case 2:
                    outputValues = _a.sent();
                    for (i = 0; i < xValues.length; i++) {
                        if (outputValues[i] !== 0) {
                            expect(outputValues[i]).toBeCloseTo(1 / (1 - rate) * xValues[i]);
                        }
                    }
                    return [2 /*return*/];
            }
        });
    }); });
    it('x TensorLike object', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, rate, output, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    x = [1.0, 2.0, 2.0, 1.0];
                    rate = 0;
                    output = tf.dropout(x, rate);
                    expect(output.dtype).toEqual('float32');
                    expect(output.shape).toEqual([4]);
                    _a = test_util_1.expectArraysClose;
                    return [4 /*yield*/, output.data()];
                case 1:
                    _a.apply(void 0, [_b.sent(), x]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('throws when x.dtype != float32', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, rate;
        return __generator(this, function (_a) {
            x = tensor_ops_1.tensor1d([1, 2, 2, 1], 'int32');
            rate = 0.75;
            expect(function () { return tf.dropout(x, rate); }).toThrowError();
            return [2 /*return*/];
        });
    }); });
    it('throws when rate is not in the range [0, 1)', function () { return __awaiter(_this, void 0, void 0, function () {
        var x, rate;
        return __generator(this, function (_a) {
            x = tensor_ops_1.tensor1d([1, 2, 2, 1]);
            rate = 1.5;
            expect(function () { return tf.dropout(x, rate); }).toThrowError();
            return [2 /*return*/];
        });
    }); });
});
//# sourceMappingURL=dropout_test.js.map