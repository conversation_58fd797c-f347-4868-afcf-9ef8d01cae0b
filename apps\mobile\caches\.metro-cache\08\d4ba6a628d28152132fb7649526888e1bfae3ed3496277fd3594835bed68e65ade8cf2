{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkTextStyle = void 0;\n  class JsiSkTextStyle {\n    static toTextStyle(value) {\n      return {\n        backgroundColor: value.backgroundColor,\n        color: value.color,\n        decoration: value.decoration,\n        decorationColor: value.decorationColor,\n        decorationStyle: value.decorationStyle ? {\n          value: value.decorationStyle\n        } : undefined,\n        decorationThickness: value.decorationThickness,\n        fontFamilies: value.fontFamilies,\n        fontSize: value.fontSize,\n        fontStyle: value.fontStyle ? {\n          slant: value.fontStyle.slant ? {\n            value: value.fontStyle.slant\n          } : undefined,\n          weight: value.fontStyle.weight ? {\n            value: value.fontStyle.weight\n          } : undefined,\n          width: value.fontStyle.width ? {\n            value: value.fontStyle.width\n          } : undefined\n        } : undefined,\n        fontFeatures: value.fontFeatures,\n        foregroundColor: value.foregroundColor,\n        fontVariations: value.fontVariations,\n        halfLeading: value.halfLeading,\n        heightMultiplier: value.heightMultiplier,\n        letterSpacing: value.letterSpacing,\n        locale: value.locale,\n        shadows: value.shadows ? value.shadows.map(shadow => ({\n          blurRadius: shadow.blurRadius,\n          color: shadow.color,\n          offset: shadow.offset ? [shadow.offset.x, shadow.offset.y] : undefined\n        })) : undefined,\n        textBaseline: value.textBaseline ? {\n          value: value.textBaseline\n        } : undefined,\n        wordSpacing: value.wordSpacing\n      };\n    }\n  }\n  exports.JsiSkTextStyle = JsiSkTextStyle;\n});", "lineCount": 50, "map": [[6, 2, 1, 7], [6, 8, 1, 13, "JsiSkTextStyle"], [6, 22, 1, 27], [6, 23, 1, 28], [7, 4, 2, 2], [7, 11, 2, 9, "toTextStyle"], [7, 22, 2, 20, "toTextStyle"], [7, 23, 2, 21, "value"], [7, 28, 2, 26], [7, 30, 2, 28], [8, 6, 3, 4], [8, 13, 3, 11], [9, 8, 4, 6, "backgroundColor"], [9, 23, 4, 21], [9, 25, 4, 23, "value"], [9, 30, 4, 28], [9, 31, 4, 29, "backgroundColor"], [9, 46, 4, 44], [10, 8, 5, 6, "color"], [10, 13, 5, 11], [10, 15, 5, 13, "value"], [10, 20, 5, 18], [10, 21, 5, 19, "color"], [10, 26, 5, 24], [11, 8, 6, 6, "decoration"], [11, 18, 6, 16], [11, 20, 6, 18, "value"], [11, 25, 6, 23], [11, 26, 6, 24, "decoration"], [11, 36, 6, 34], [12, 8, 7, 6, "decorationColor"], [12, 23, 7, 21], [12, 25, 7, 23, "value"], [12, 30, 7, 28], [12, 31, 7, 29, "decorationColor"], [12, 46, 7, 44], [13, 8, 8, 6, "decorationStyle"], [13, 23, 8, 21], [13, 25, 8, 23, "value"], [13, 30, 8, 28], [13, 31, 8, 29, "decorationStyle"], [13, 46, 8, 44], [13, 49, 8, 47], [14, 10, 9, 8, "value"], [14, 15, 9, 13], [14, 17, 9, 15, "value"], [14, 22, 9, 20], [14, 23, 9, 21, "decorationStyle"], [15, 8, 10, 6], [15, 9, 10, 7], [15, 12, 10, 10, "undefined"], [15, 21, 10, 19], [16, 8, 11, 6, "decorationThickness"], [16, 27, 11, 25], [16, 29, 11, 27, "value"], [16, 34, 11, 32], [16, 35, 11, 33, "decorationThickness"], [16, 54, 11, 52], [17, 8, 12, 6, "fontFamilies"], [17, 20, 12, 18], [17, 22, 12, 20, "value"], [17, 27, 12, 25], [17, 28, 12, 26, "fontFamilies"], [17, 40, 12, 38], [18, 8, 13, 6, "fontSize"], [18, 16, 13, 14], [18, 18, 13, 16, "value"], [18, 23, 13, 21], [18, 24, 13, 22, "fontSize"], [18, 32, 13, 30], [19, 8, 14, 6, "fontStyle"], [19, 17, 14, 15], [19, 19, 14, 17, "value"], [19, 24, 14, 22], [19, 25, 14, 23, "fontStyle"], [19, 34, 14, 32], [19, 37, 14, 35], [20, 10, 15, 8, "slant"], [20, 15, 15, 13], [20, 17, 15, 15, "value"], [20, 22, 15, 20], [20, 23, 15, 21, "fontStyle"], [20, 32, 15, 30], [20, 33, 15, 31, "slant"], [20, 38, 15, 36], [20, 41, 15, 39], [21, 12, 16, 10, "value"], [21, 17, 16, 15], [21, 19, 16, 17, "value"], [21, 24, 16, 22], [21, 25, 16, 23, "fontStyle"], [21, 34, 16, 32], [21, 35, 16, 33, "slant"], [22, 10, 17, 8], [22, 11, 17, 9], [22, 14, 17, 12, "undefined"], [22, 23, 17, 21], [23, 10, 18, 8, "weight"], [23, 16, 18, 14], [23, 18, 18, 16, "value"], [23, 23, 18, 21], [23, 24, 18, 22, "fontStyle"], [23, 33, 18, 31], [23, 34, 18, 32, "weight"], [23, 40, 18, 38], [23, 43, 18, 41], [24, 12, 19, 10, "value"], [24, 17, 19, 15], [24, 19, 19, 17, "value"], [24, 24, 19, 22], [24, 25, 19, 23, "fontStyle"], [24, 34, 19, 32], [24, 35, 19, 33, "weight"], [25, 10, 20, 8], [25, 11, 20, 9], [25, 14, 20, 12, "undefined"], [25, 23, 20, 21], [26, 10, 21, 8, "width"], [26, 15, 21, 13], [26, 17, 21, 15, "value"], [26, 22, 21, 20], [26, 23, 21, 21, "fontStyle"], [26, 32, 21, 30], [26, 33, 21, 31, "width"], [26, 38, 21, 36], [26, 41, 21, 39], [27, 12, 22, 10, "value"], [27, 17, 22, 15], [27, 19, 22, 17, "value"], [27, 24, 22, 22], [27, 25, 22, 23, "fontStyle"], [27, 34, 22, 32], [27, 35, 22, 33, "width"], [28, 10, 23, 8], [28, 11, 23, 9], [28, 14, 23, 12, "undefined"], [29, 8, 24, 6], [29, 9, 24, 7], [29, 12, 24, 10, "undefined"], [29, 21, 24, 19], [30, 8, 25, 6, "fontFeatures"], [30, 20, 25, 18], [30, 22, 25, 20, "value"], [30, 27, 25, 25], [30, 28, 25, 26, "fontFeatures"], [30, 40, 25, 38], [31, 8, 26, 6, "foregroundColor"], [31, 23, 26, 21], [31, 25, 26, 23, "value"], [31, 30, 26, 28], [31, 31, 26, 29, "foregroundColor"], [31, 46, 26, 44], [32, 8, 27, 6, "fontVariations"], [32, 22, 27, 20], [32, 24, 27, 22, "value"], [32, 29, 27, 27], [32, 30, 27, 28, "fontVariations"], [32, 44, 27, 42], [33, 8, 28, 6, "halfLeading"], [33, 19, 28, 17], [33, 21, 28, 19, "value"], [33, 26, 28, 24], [33, 27, 28, 25, "halfLeading"], [33, 38, 28, 36], [34, 8, 29, 6, "heightMultiplier"], [34, 24, 29, 22], [34, 26, 29, 24, "value"], [34, 31, 29, 29], [34, 32, 29, 30, "heightMultiplier"], [34, 48, 29, 46], [35, 8, 30, 6, "letterSpacing"], [35, 21, 30, 19], [35, 23, 30, 21, "value"], [35, 28, 30, 26], [35, 29, 30, 27, "letterSpacing"], [35, 42, 30, 40], [36, 8, 31, 6, "locale"], [36, 14, 31, 12], [36, 16, 31, 14, "value"], [36, 21, 31, 19], [36, 22, 31, 20, "locale"], [36, 28, 31, 26], [37, 8, 32, 6, "shadows"], [37, 15, 32, 13], [37, 17, 32, 15, "value"], [37, 22, 32, 20], [37, 23, 32, 21, "shadows"], [37, 30, 32, 28], [37, 33, 32, 31, "value"], [37, 38, 32, 36], [37, 39, 32, 37, "shadows"], [37, 46, 32, 44], [37, 47, 32, 45, "map"], [37, 50, 32, 48], [37, 51, 32, 49, "shadow"], [37, 57, 32, 55], [37, 62, 32, 60], [38, 10, 33, 8, "blurRadius"], [38, 20, 33, 18], [38, 22, 33, 20, "shadow"], [38, 28, 33, 26], [38, 29, 33, 27, "blurRadius"], [38, 39, 33, 37], [39, 10, 34, 8, "color"], [39, 15, 34, 13], [39, 17, 34, 15, "shadow"], [39, 23, 34, 21], [39, 24, 34, 22, "color"], [39, 29, 34, 27], [40, 10, 35, 8, "offset"], [40, 16, 35, 14], [40, 18, 35, 16, "shadow"], [40, 24, 35, 22], [40, 25, 35, 23, "offset"], [40, 31, 35, 29], [40, 34, 35, 32], [40, 35, 35, 33, "shadow"], [40, 41, 35, 39], [40, 42, 35, 40, "offset"], [40, 48, 35, 46], [40, 49, 35, 47, "x"], [40, 50, 35, 48], [40, 52, 35, 50, "shadow"], [40, 58, 35, 56], [40, 59, 35, 57, "offset"], [40, 65, 35, 63], [40, 66, 35, 64, "y"], [40, 67, 35, 65], [40, 68, 35, 66], [40, 71, 35, 69, "undefined"], [41, 8, 36, 6], [41, 9, 36, 7], [41, 10, 36, 8], [41, 11, 36, 9], [41, 14, 36, 12, "undefined"], [41, 23, 36, 21], [42, 8, 37, 6, "textBaseline"], [42, 20, 37, 18], [42, 22, 37, 20, "value"], [42, 27, 37, 25], [42, 28, 37, 26, "textBaseline"], [42, 40, 37, 38], [42, 43, 37, 41], [43, 10, 38, 8, "value"], [43, 15, 38, 13], [43, 17, 38, 15, "value"], [43, 22, 38, 20], [43, 23, 38, 21, "textBaseline"], [44, 8, 39, 6], [44, 9, 39, 7], [44, 12, 39, 10, "undefined"], [44, 21, 39, 19], [45, 8, 40, 6, "wordSpacing"], [45, 19, 40, 17], [45, 21, 40, 19, "value"], [45, 26, 40, 24], [45, 27, 40, 25, "wordSpacing"], [46, 6, 41, 4], [46, 7, 41, 5], [47, 4, 42, 2], [48, 2, 43, 0], [49, 2, 43, 1, "exports"], [49, 9, 43, 1], [49, 10, 43, 1, "JsiSkTextStyle"], [49, 24, 43, 1], [49, 27, 43, 1, "JsiSkTextStyle"], [49, 41, 43, 1], [50, 0, 43, 1], [50, 3]], "functionMap": {"names": ["<global>", "JsiSkTextStyle", "toTextStyle", "value.shadows.map$argument_0"], "mappings": "AAA,OC;ECC;iDC8B;QDI;GDM;CDC"}}, "type": "js/module"}]}