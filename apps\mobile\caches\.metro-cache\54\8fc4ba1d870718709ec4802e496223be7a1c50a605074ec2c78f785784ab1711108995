{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 30, "index": 30}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}, {"name": "./JsiSkData", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 31}, "end": {"line": 2, "column": 40, "index": 71}}], "key": "c+biP0KCYKcLc6xq5NFUJB5wGKM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkDataFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  var _JsiSkData = require(_dependencyMap[1], \"./JsiSkData\");\n  class JsiSkDataFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    fromURI(uri) {\n      return fetch(uri).then(response => response.arrayBuffer()).then(data => new _JsiSkData.JsiSkData(this.CanvasKit, data));\n    }\n    /**\n     * Creates a new Data object from a byte array.\n     * @param bytes An array of bytes representing the data\n     */\n    fromBytes(bytes) {\n      return new _JsiSkData.JsiSkData(this.CanvasKit, bytes);\n    }\n    /**\n     * Creates a new Data object from a base64 encoded string.\n     * @param base64 A Base64 encoded string representing the data\n     */\n    fromBase64(base64) {\n      const bytes = Uint8Array.from(atob(base64), c => c.charCodeAt(0));\n      return this.fromBytes(bytes);\n    }\n  }\n  exports.JsiSkDataFactory = JsiSkDataFactory;\n});", "lineCount": 32, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_JsiSkData"], [7, 16, 2, 0], [7, 19, 2, 0, "require"], [7, 26, 2, 0], [7, 27, 2, 0, "_dependencyMap"], [7, 41, 2, 0], [8, 2, 3, 7], [8, 8, 3, 13, "JsiSkDataFactory"], [8, 24, 3, 29], [8, 33, 3, 38, "Host"], [8, 43, 3, 42], [8, 44, 3, 43], [9, 4, 4, 2, "constructor"], [9, 15, 4, 13, "constructor"], [9, 16, 4, 14, "CanvasKit"], [9, 25, 4, 23], [9, 27, 4, 25], [10, 6, 5, 4], [10, 11, 5, 9], [10, 12, 5, 10, "CanvasKit"], [10, 21, 5, 19], [10, 22, 5, 20], [11, 4, 6, 2], [12, 4, 7, 2, "fromURI"], [12, 11, 7, 9, "fromURI"], [12, 12, 7, 10, "uri"], [12, 15, 7, 13], [12, 17, 7, 15], [13, 6, 8, 4], [13, 13, 8, 11, "fetch"], [13, 18, 8, 16], [13, 19, 8, 17, "uri"], [13, 22, 8, 20], [13, 23, 8, 21], [13, 24, 8, 22, "then"], [13, 28, 8, 26], [13, 29, 8, 27, "response"], [13, 37, 8, 35], [13, 41, 8, 39, "response"], [13, 49, 8, 47], [13, 50, 8, 48, "arrayBuffer"], [13, 61, 8, 59], [13, 62, 8, 60], [13, 63, 8, 61], [13, 64, 8, 62], [13, 65, 8, 63, "then"], [13, 69, 8, 67], [13, 70, 8, 68, "data"], [13, 74, 8, 72], [13, 78, 8, 76], [13, 82, 8, 80, "JsiSkData"], [13, 102, 8, 89], [13, 103, 8, 90], [13, 107, 8, 94], [13, 108, 8, 95, "CanvasKit"], [13, 117, 8, 104], [13, 119, 8, 106, "data"], [13, 123, 8, 110], [13, 124, 8, 111], [13, 125, 8, 112], [14, 4, 9, 2], [15, 4, 10, 2], [16, 0, 11, 0], [17, 0, 12, 0], [18, 0, 13, 0], [19, 4, 14, 2, "fromBytes"], [19, 13, 14, 11, "fromBytes"], [19, 14, 14, 12, "bytes"], [19, 19, 14, 17], [19, 21, 14, 19], [20, 6, 15, 4], [20, 13, 15, 11], [20, 17, 15, 15, "JsiSkData"], [20, 37, 15, 24], [20, 38, 15, 25], [20, 42, 15, 29], [20, 43, 15, 30, "CanvasKit"], [20, 52, 15, 39], [20, 54, 15, 41, "bytes"], [20, 59, 15, 46], [20, 60, 15, 47], [21, 4, 16, 2], [22, 4, 17, 2], [23, 0, 18, 0], [24, 0, 19, 0], [25, 0, 20, 0], [26, 4, 21, 2, "fromBase64"], [26, 14, 21, 12, "fromBase64"], [26, 15, 21, 13, "base64"], [26, 21, 21, 19], [26, 23, 21, 21], [27, 6, 22, 4], [27, 12, 22, 10, "bytes"], [27, 17, 22, 15], [27, 20, 22, 18, "Uint8Array"], [27, 30, 22, 28], [27, 31, 22, 29, "from"], [27, 35, 22, 33], [27, 36, 22, 34, "atob"], [27, 40, 22, 38], [27, 41, 22, 39, "base64"], [27, 47, 22, 45], [27, 48, 22, 46], [27, 50, 22, 48, "c"], [27, 51, 22, 49], [27, 55, 22, 53, "c"], [27, 56, 22, 54], [27, 57, 22, 55, "charCodeAt"], [27, 67, 22, 65], [27, 68, 22, 66], [27, 69, 22, 67], [27, 70, 22, 68], [27, 71, 22, 69], [28, 6, 23, 4], [28, 13, 23, 11], [28, 17, 23, 15], [28, 18, 23, 16, "fromBytes"], [28, 27, 23, 25], [28, 28, 23, 26, "bytes"], [28, 33, 23, 31], [28, 34, 23, 32], [29, 4, 24, 2], [30, 2, 25, 0], [31, 2, 25, 1, "exports"], [31, 9, 25, 1], [31, 10, 25, 1, "JsiSkDataFactory"], [31, 26, 25, 1], [31, 29, 25, 1, "JsiSkDataFactory"], [31, 45, 25, 1], [32, 0, 25, 1], [32, 3]], "functionMap": {"names": ["<global>", "JsiSkDataFactory", "constructor", "fromURI", "fetch.then$argument_0", "fetch.then.then$argument_0", "fromBytes", "fromBase64", "Uint8Array.from$argument_1"], "mappings": "AAA;OCE;ECC;GDE;EEC;2BCC,kCD,OE,2CF;GFC;EKK;GLE;EMK;gDCC,oBD;GNE;CDC"}}, "type": "js/module"}]}