{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = TensorFlowFaceCanvas;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[1], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\web\\\\TensorFlowFaceCanvas.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  // TensorFlow.js BlazeFace implementation for reliable cross-browser face detection\n  function TensorFlowFaceCanvas({\n    containerId,\n    width,\n    height\n  }) {\n    _s();\n    const canvasRef = (0, _react.useRef)(null);\n    const rafRef = (0, _react.useRef)(null);\n    const modelRef = (0, _react.useRef)(null);\n    const debugCounterRef = (0, _react.useRef)(0);\n    const [isLoading, setIsLoading] = (0, _react.useState)(true);\n    const [error, setError] = (0, _react.useState)(null);\n    (0, _react.useEffect)(() => {\n      console.log('[TensorFlowFaceCanvas] 🚀 STARTING INITIALIZATION...', {\n        containerId,\n        width,\n        height\n      });\n      console.log('[TensorFlowFaceCanvas] 🔧 Component mounted and running!');\n      const container = document.getElementById(containerId);\n      if (!container) {\n        console.error('[TensorFlowFaceCanvas] Container not found:', containerId);\n        setError('Container not found');\n        return;\n      }\n      const video = container.querySelector('video');\n      if (!video) {\n        console.error('[TensorFlowFaceCanvas] Video element not found in container');\n        setError('Video element not found');\n        return;\n      }\n\n      // Resize canvas\n      const canvas = canvasRef.current;\n      if (!canvas) {\n        console.error('[TensorFlowFaceCanvas] Canvas ref not available');\n        setError('Canvas not available');\n        return;\n      }\n      canvas.width = width;\n      canvas.height = height;\n      const ctx = canvas.getContext('2d');\n      if (!ctx) {\n        console.error('[TensorFlowFaceCanvas] Canvas context not available');\n        setError('Canvas context not available');\n        return;\n      }\n      let running = true;\n      debugCounterRef.current = 0;\n      const loadTensorFlowAndBlazeFace = async () => {\n        try {\n          console.log('[TensorFlowFaceCanvas] Loading TensorFlow.js...');\n\n          // Load TensorFlow.js\n          if (!window.tf) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.10.0/dist/tf.min.js');\n            console.log('[TensorFlowFaceCanvas] TensorFlow.js loaded');\n          }\n\n          // Load BlazeFace\n          if (!window.blazeface) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js');\n            console.log('[TensorFlowFaceCanvas] BlazeFace loaded');\n          }\n\n          // Initialize BlazeFace model\n          console.log('[TensorFlowFaceCanvas] Initializing BlazeFace model...');\n          const blazeface = window.blazeface;\n          modelRef.current = await blazeface.load();\n          console.log('[TensorFlowFaceCanvas] ✅ BlazeFace model loaded successfully');\n          setIsLoading(false);\n          setError(null);\n        } catch (error) {\n          console.error('[TensorFlowFaceCanvas] ❌ Failed to load TensorFlow/BlazeFace:', error);\n          setError(`Failed to load face detection: ${error.message}`);\n          setIsLoading(false);\n        }\n      };\n      const loadScript = src => {\n        return new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = src;\n          script.async = true;\n          script.onload = () => resolve();\n          script.onerror = () => reject(new Error(`Failed to load script: ${src}`));\n          document.head.appendChild(script);\n        });\n      };\n      const applyFallbackBlur = (ctx, videoW, videoH, canvasW, canvasH) => {\n        // Apply blur to common face areas when no faces are detected\n        const scale = Math.max(canvasW / videoW, canvasH / videoH);\n        const scaledW = videoW * scale;\n        const scaledH = videoH * scale;\n        const offsetX = (canvasW - scaledW) / 2;\n        const offsetY = (canvasH - scaledH) / 2;\n\n        // Common face areas (center-upper region for selfies)\n        const faceAreas = [{\n          x: 0.25,\n          y: 0.15,\n          w: 0.5,\n          h: 0.5\n        },\n        // Center face\n        {\n          x: 0.1,\n          y: 0.2,\n          w: 0.35,\n          h: 0.4\n        },\n        // Left side\n        {\n          x: 0.55,\n          y: 0.2,\n          w: 0.35,\n          h: 0.4\n        } // Right side\n        ];\n        faceAreas.forEach(area => {\n          const x = area.x * scaledW + offsetX;\n          const y = area.y * scaledH + offsetY;\n          const w = area.w * scaledW;\n          const h = area.h * scaledH;\n          ctx.save();\n          ctx.beginPath();\n          ctx.ellipse(x + w / 2, y + h / 2, w / 2, h / 2, 0, 0, Math.PI * 2);\n          ctx.clip();\n          ctx.filter = 'blur(25px)';\n          ctx.drawImage(video, 0, 0, canvasW, canvasH);\n          ctx.restore();\n        });\n      };\n      const loop = async () => {\n        if (!running) return;\n        rafRef.current = requestAnimationFrame(loop);\n        if (!modelRef.current) {\n          // Log occasionally that model is not ready\n          debugCounterRef.current++;\n          if (debugCounterRef.current % 60 === 0) {\n            // Every ~1 second at 60fps\n            console.log('[TensorFlowFaceCanvas] Waiting for BlazeFace model to load...');\n          }\n          return;\n        }\n        try {\n          const videoW = video.videoWidth || width;\n          const videoH = video.videoHeight || height;\n\n          // Clear canvas\n          ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n          // Log video status occasionally\n          debugCounterRef.current++;\n          if (debugCounterRef.current % 300 === 0) {\n            // Every ~5 seconds\n            console.log('[TensorFlowFaceCanvas] Video status:', {\n              dimensions: `${videoW}x${videoH}`,\n              readyState: video.readyState,\n              paused: video.paused,\n              currentTime: video.currentTime\n            });\n          }\n          if (videoW === 0 || videoH === 0) {\n            if (debugCounterRef.current % 60 === 0) {\n              console.log('[TensorFlowFaceCanvas] Video not ready - dimensions:', videoW, 'x', videoH);\n            }\n            return;\n          }\n\n          // Detect faces using BlazeFace\n          const tf = window.tf;\n          const tensor = tf.browser.fromPixels(video);\n          const predictions = await modelRef.current.estimateFaces(tensor, false, 0.6); // Lower threshold for better detection\n          tensor.dispose(); // Clean up tensor\n\n          // Debug logging for face detection\n          if (predictions.length > 0) {\n            console.log(`[TensorFlowFaceCanvas] 🎯 Detected ${predictions.length} face(s) at frame ${debugCounterRef.current}`);\n          } else if (debugCounterRef.current % 120 === 0) {\n            // Every ~2 seconds\n            console.log(`[TensorFlowFaceCanvas] No faces detected (frame ${debugCounterRef.current})`);\n          }\n          if (predictions.length === 0) {\n            // Apply fallback blur to common face areas\n            if (debugCounterRef.current > 60) {\n              // Only after model has had time to initialize\n              applyFallbackBlur(ctx, videoW, videoH, width, height);\n            }\n            return;\n          }\n\n          // Compute scale from video to canvas\n          const scale = Math.max(width / videoW, height / videoH);\n          const scaledW = videoW * scale;\n          const scaledH = videoH * scale;\n          const offsetX = (width - scaledW) / 2;\n          const offsetY = (height - scaledH) / 2;\n\n          // Draw blur for each detected face\n          predictions.forEach((prediction, index) => {\n            const [x1, y1] = prediction.topLeft;\n            const [x2, y2] = prediction.bottomRight;\n            if (debugCounterRef.current % 60 === 0) {\n              console.log(`[TensorFlowFaceCanvas] Face ${index + 1} raw coords:`, {\n                x1,\n                y1,\n                x2,\n                y2\n              });\n            }\n\n            // Validate coordinates\n            if (x1 >= x2 || y1 >= y2) {\n              console.warn(`[TensorFlowFaceCanvas] Invalid face coordinates for face ${index + 1}`);\n              return;\n            }\n\n            // Calculate face dimensions with safety checks\n            const rawFaceWidth = Math.abs(x2 - x1);\n            const rawFaceHeight = Math.abs(y2 - y1);\n            if (rawFaceWidth <= 0 || rawFaceHeight <= 0) {\n              console.warn(`[TensorFlowFaceCanvas] Invalid face dimensions for face ${index + 1}`);\n              return;\n            }\n\n            // Expand the bounding box for better coverage\n            const centerX = (x1 + x2) / 2;\n            const centerY = (y1 + y2) / 2;\n            const faceWidth = rawFaceWidth * 1.4; // Expand by 40%\n            const faceHeight = rawFaceHeight * 1.6; // Expand by 60%\n\n            const expandedX1 = centerX - faceWidth / 2;\n            const expandedY1 = centerY - faceHeight / 2;\n\n            // Map to canvas coordinates\n            const canvasX = expandedX1 * scale + offsetX;\n            const canvasY = expandedY1 * scale + offsetY;\n            const canvasW = faceWidth * scale;\n            const canvasH = faceHeight * scale;\n\n            // Ensure positive radii for ellipse\n            const radiusX = Math.max(canvasW / 2, 5);\n            const radiusY = Math.max(canvasH / 2, 5);\n\n            // Draw blurred oval patch\n            ctx.save();\n            ctx.beginPath();\n            ctx.ellipse(canvasX + canvasW / 2, canvasY + canvasH / 2, radiusX, radiusY, 0, 0, Math.PI * 2);\n            ctx.clip();\n            ctx.filter = 'blur(30px)';\n            ctx.drawImage(video, 0, 0, width, height);\n            ctx.restore();\n            if (debugCounterRef.current % 60 === 0) {\n              console.log(`[TensorFlowFaceCanvas] Blurred face ${index + 1} at (${Math.round(canvasX)}, ${Math.round(canvasY)}) with radii (${Math.round(radiusX)}, ${Math.round(radiusY)})`);\n            }\n          });\n        } catch (error) {\n          // Log errors but keep the loop running\n          if (debugCounterRef.current % 300 === 0) {\n            console.error('[TensorFlowFaceCanvas] Detection error:', error);\n          }\n        }\n      };\n\n      // Start loading and then begin the loop\n      loadTensorFlowAndBlazeFace().then(() => {\n        if (running) {\n          rafRef.current = requestAnimationFrame(loop);\n        }\n      });\n      return () => {\n        running = false;\n        if (rafRef.current) {\n          cancelAnimationFrame(rafRef.current);\n        }\n      };\n    }, [containerId, width, height]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          position: 'absolute',\n          left: 0,\n          top: 0,\n          width,\n          height,\n          pointerEvents: 'none',\n          zIndex: 10\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 7\n      }, this), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(0,0,0,0.7)',\n          color: 'white',\n          padding: '5px 10px',\n          borderRadius: '4px',\n          fontSize: '12px',\n          zIndex: 20\n        },\n        children: \"\\uD83D\\uDD04 Loading TensorFlow face detection...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), !isLoading && !error && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(16, 185, 129, 0.8)',\n          color: 'white',\n          padding: '5px 10px',\n          borderRadius: '4px',\n          fontSize: '12px',\n          zIndex: 20\n        },\n        children: \"\\uD83E\\uDD16 TensorFlow Face Detection Active\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this), error && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(255,0,0,0.7)',\n          color: 'white',\n          padding: '5px 10px',\n          borderRadius: '4px',\n          fontSize: '12px',\n          zIndex: 20\n        },\n        children: [\"Face detection error: \", error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  _s(TensorFlowFaceCanvas, \"6DhjEcQmE6gVnJWZHklLharJPbw=\");\n  _c = TensorFlowFaceCanvas;\n  var _c;\n  $RefreshReg$(_c, \"TensorFlowFaceCanvas\");\n});", "lineCount": 363, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "_interopRequireWildcard"], [6, 38, 1, 0], [6, 39, 1, 0, "require"], [6, 46, 1, 0], [6, 47, 1, 0, "_dependencyMap"], [6, 61, 1, 0], [7, 2, 1, 59], [7, 6, 1, 59, "_jsxDevRuntime"], [7, 20, 1, 59], [7, 23, 1, 59, "require"], [7, 30, 1, 59], [7, 31, 1, 59, "_dependencyMap"], [7, 45, 1, 59], [8, 2, 1, 59], [8, 6, 1, 59, "_jsxFileName"], [8, 18, 1, 59], [9, 4, 1, 59, "_s"], [9, 6, 1, 59], [9, 9, 1, 59, "$RefreshSig$"], [9, 21, 1, 59], [10, 2, 1, 59], [10, 11, 1, 59, "_interopRequireWildcard"], [10, 35, 1, 59, "e"], [10, 36, 1, 59], [10, 38, 1, 59, "t"], [10, 39, 1, 59], [10, 68, 1, 59, "WeakMap"], [10, 75, 1, 59], [10, 81, 1, 59, "r"], [10, 82, 1, 59], [10, 89, 1, 59, "WeakMap"], [10, 96, 1, 59], [10, 100, 1, 59, "n"], [10, 101, 1, 59], [10, 108, 1, 59, "WeakMap"], [10, 115, 1, 59], [10, 127, 1, 59, "_interopRequireWildcard"], [10, 150, 1, 59], [10, 162, 1, 59, "_interopRequireWildcard"], [10, 163, 1, 59, "e"], [10, 164, 1, 59], [10, 166, 1, 59, "t"], [10, 167, 1, 59], [10, 176, 1, 59, "t"], [10, 177, 1, 59], [10, 181, 1, 59, "e"], [10, 182, 1, 59], [10, 186, 1, 59, "e"], [10, 187, 1, 59], [10, 188, 1, 59, "__esModule"], [10, 198, 1, 59], [10, 207, 1, 59, "e"], [10, 208, 1, 59], [10, 214, 1, 59, "o"], [10, 215, 1, 59], [10, 217, 1, 59, "i"], [10, 218, 1, 59], [10, 220, 1, 59, "f"], [10, 221, 1, 59], [10, 226, 1, 59, "__proto__"], [10, 235, 1, 59], [10, 243, 1, 59, "default"], [10, 250, 1, 59], [10, 252, 1, 59, "e"], [10, 253, 1, 59], [10, 270, 1, 59, "e"], [10, 271, 1, 59], [10, 294, 1, 59, "e"], [10, 295, 1, 59], [10, 320, 1, 59, "e"], [10, 321, 1, 59], [10, 330, 1, 59, "f"], [10, 331, 1, 59], [10, 337, 1, 59, "o"], [10, 338, 1, 59], [10, 341, 1, 59, "t"], [10, 342, 1, 59], [10, 345, 1, 59, "n"], [10, 346, 1, 59], [10, 349, 1, 59, "r"], [10, 350, 1, 59], [10, 358, 1, 59, "o"], [10, 359, 1, 59], [10, 360, 1, 59, "has"], [10, 363, 1, 59], [10, 364, 1, 59, "e"], [10, 365, 1, 59], [10, 375, 1, 59, "o"], [10, 376, 1, 59], [10, 377, 1, 59, "get"], [10, 380, 1, 59], [10, 381, 1, 59, "e"], [10, 382, 1, 59], [10, 385, 1, 59, "o"], [10, 386, 1, 59], [10, 387, 1, 59, "set"], [10, 390, 1, 59], [10, 391, 1, 59, "e"], [10, 392, 1, 59], [10, 394, 1, 59, "f"], [10, 395, 1, 59], [10, 411, 1, 59, "t"], [10, 412, 1, 59], [10, 416, 1, 59, "e"], [10, 417, 1, 59], [10, 433, 1, 59, "t"], [10, 434, 1, 59], [10, 441, 1, 59, "hasOwnProperty"], [10, 455, 1, 59], [10, 456, 1, 59, "call"], [10, 460, 1, 59], [10, 461, 1, 59, "e"], [10, 462, 1, 59], [10, 464, 1, 59, "t"], [10, 465, 1, 59], [10, 472, 1, 59, "i"], [10, 473, 1, 59], [10, 477, 1, 59, "o"], [10, 478, 1, 59], [10, 481, 1, 59, "Object"], [10, 487, 1, 59], [10, 488, 1, 59, "defineProperty"], [10, 502, 1, 59], [10, 507, 1, 59, "Object"], [10, 513, 1, 59], [10, 514, 1, 59, "getOwnPropertyDescriptor"], [10, 538, 1, 59], [10, 539, 1, 59, "e"], [10, 540, 1, 59], [10, 542, 1, 59, "t"], [10, 543, 1, 59], [10, 550, 1, 59, "i"], [10, 551, 1, 59], [10, 552, 1, 59, "get"], [10, 555, 1, 59], [10, 559, 1, 59, "i"], [10, 560, 1, 59], [10, 561, 1, 59, "set"], [10, 564, 1, 59], [10, 568, 1, 59, "o"], [10, 569, 1, 59], [10, 570, 1, 59, "f"], [10, 571, 1, 59], [10, 573, 1, 59, "t"], [10, 574, 1, 59], [10, 576, 1, 59, "i"], [10, 577, 1, 59], [10, 581, 1, 59, "f"], [10, 582, 1, 59], [10, 583, 1, 59, "t"], [10, 584, 1, 59], [10, 588, 1, 59, "e"], [10, 589, 1, 59], [10, 590, 1, 59, "t"], [10, 591, 1, 59], [10, 602, 1, 59, "f"], [10, 603, 1, 59], [10, 608, 1, 59, "e"], [10, 609, 1, 59], [10, 611, 1, 59, "t"], [10, 612, 1, 59], [11, 2, 9, 0], [12, 2, 10, 15], [12, 11, 10, 24, "TensorFlowFaceCanvas"], [12, 31, 10, 44, "TensorFlowFaceCanvas"], [12, 32, 10, 45], [13, 4, 10, 47, "containerId"], [13, 15, 10, 58], [14, 4, 10, 60, "width"], [14, 9, 10, 65], [15, 4, 10, 67, "height"], [16, 2, 10, 101], [16, 3, 10, 102], [16, 5, 10, 104], [17, 4, 10, 104, "_s"], [17, 6, 10, 104], [18, 4, 11, 2], [18, 10, 11, 8, "canvasRef"], [18, 19, 11, 17], [18, 22, 11, 20], [18, 26, 11, 20, "useRef"], [18, 39, 11, 26], [18, 41, 11, 53], [18, 45, 11, 57], [18, 46, 11, 58], [19, 4, 12, 2], [19, 10, 12, 8, "rafRef"], [19, 16, 12, 14], [19, 19, 12, 17], [19, 23, 12, 17, "useRef"], [19, 36, 12, 23], [19, 38, 12, 39], [19, 42, 12, 43], [19, 43, 12, 44], [20, 4, 13, 2], [20, 10, 13, 8, "modelRef"], [20, 18, 13, 16], [20, 21, 13, 19], [20, 25, 13, 19, "useRef"], [20, 38, 13, 25], [20, 40, 13, 38], [20, 44, 13, 42], [20, 45, 13, 43], [21, 4, 14, 2], [21, 10, 14, 8, "debugCounterRef"], [21, 25, 14, 23], [21, 28, 14, 26], [21, 32, 14, 26, "useRef"], [21, 45, 14, 32], [21, 47, 14, 41], [21, 48, 14, 42], [21, 49, 14, 43], [22, 4, 15, 2], [22, 10, 15, 8], [22, 11, 15, 9, "isLoading"], [22, 20, 15, 18], [22, 22, 15, 20, "setIsLoading"], [22, 34, 15, 32], [22, 35, 15, 33], [22, 38, 15, 36], [22, 42, 15, 36, "useState"], [22, 57, 15, 44], [22, 59, 15, 45], [22, 63, 15, 49], [22, 64, 15, 50], [23, 4, 16, 2], [23, 10, 16, 8], [23, 11, 16, 9, "error"], [23, 16, 16, 14], [23, 18, 16, 16, "setError"], [23, 26, 16, 24], [23, 27, 16, 25], [23, 30, 16, 28], [23, 34, 16, 28, "useState"], [23, 49, 16, 36], [23, 51, 16, 52], [23, 55, 16, 56], [23, 56, 16, 57], [24, 4, 18, 2], [24, 8, 18, 2, "useEffect"], [24, 24, 18, 11], [24, 26, 18, 12], [24, 32, 18, 18], [25, 6, 19, 4, "console"], [25, 13, 19, 11], [25, 14, 19, 12, "log"], [25, 17, 19, 15], [25, 18, 19, 16], [25, 72, 19, 70], [25, 74, 19, 72], [26, 8, 19, 74, "containerId"], [26, 19, 19, 85], [27, 8, 19, 87, "width"], [27, 13, 19, 92], [28, 8, 19, 94, "height"], [29, 6, 19, 101], [29, 7, 19, 102], [29, 8, 19, 103], [30, 6, 20, 4, "console"], [30, 13, 20, 11], [30, 14, 20, 12, "log"], [30, 17, 20, 15], [30, 18, 20, 16], [30, 76, 20, 74], [30, 77, 20, 75], [31, 6, 22, 4], [31, 12, 22, 10, "container"], [31, 21, 22, 19], [31, 24, 22, 22, "document"], [31, 32, 22, 30], [31, 33, 22, 31, "getElementById"], [31, 47, 22, 45], [31, 48, 22, 46, "containerId"], [31, 59, 22, 57], [31, 60, 22, 58], [32, 6, 23, 4], [32, 10, 23, 8], [32, 11, 23, 9, "container"], [32, 20, 23, 18], [32, 22, 23, 20], [33, 8, 24, 6, "console"], [33, 15, 24, 13], [33, 16, 24, 14, "error"], [33, 21, 24, 19], [33, 22, 24, 20], [33, 67, 24, 65], [33, 69, 24, 67, "containerId"], [33, 80, 24, 78], [33, 81, 24, 79], [34, 8, 25, 6, "setError"], [34, 16, 25, 14], [34, 17, 25, 15], [34, 38, 25, 36], [34, 39, 25, 37], [35, 8, 26, 6], [36, 6, 27, 4], [37, 6, 29, 4], [37, 12, 29, 10, "video"], [37, 17, 29, 40], [37, 20, 29, 43, "container"], [37, 29, 29, 52], [37, 30, 29, 53, "querySelector"], [37, 43, 29, 66], [37, 44, 29, 67], [37, 51, 29, 74], [37, 52, 29, 75], [38, 6, 30, 4], [38, 10, 30, 8], [38, 11, 30, 9, "video"], [38, 16, 30, 14], [38, 18, 30, 16], [39, 8, 31, 6, "console"], [39, 15, 31, 13], [39, 16, 31, 14, "error"], [39, 21, 31, 19], [39, 22, 31, 20], [39, 83, 31, 81], [39, 84, 31, 82], [40, 8, 32, 6, "setError"], [40, 16, 32, 14], [40, 17, 32, 15], [40, 42, 32, 40], [40, 43, 32, 41], [41, 8, 33, 6], [42, 6, 34, 4], [44, 6, 36, 4], [45, 6, 37, 4], [45, 12, 37, 10, "canvas"], [45, 18, 37, 16], [45, 21, 37, 19, "canvasRef"], [45, 30, 37, 28], [45, 31, 37, 29, "current"], [45, 38, 37, 36], [46, 6, 38, 4], [46, 10, 38, 8], [46, 11, 38, 9, "canvas"], [46, 17, 38, 15], [46, 19, 38, 17], [47, 8, 39, 6, "console"], [47, 15, 39, 13], [47, 16, 39, 14, "error"], [47, 21, 39, 19], [47, 22, 39, 20], [47, 71, 39, 69], [47, 72, 39, 70], [48, 8, 40, 6, "setError"], [48, 16, 40, 14], [48, 17, 40, 15], [48, 39, 40, 37], [48, 40, 40, 38], [49, 8, 41, 6], [50, 6, 42, 4], [51, 6, 43, 4, "canvas"], [51, 12, 43, 10], [51, 13, 43, 11, "width"], [51, 18, 43, 16], [51, 21, 43, 19, "width"], [51, 26, 43, 24], [52, 6, 44, 4, "canvas"], [52, 12, 44, 10], [52, 13, 44, 11, "height"], [52, 19, 44, 17], [52, 22, 44, 20, "height"], [52, 28, 44, 26], [53, 6, 46, 4], [53, 12, 46, 10, "ctx"], [53, 15, 46, 13], [53, 18, 46, 16, "canvas"], [53, 24, 46, 22], [53, 25, 46, 23, "getContext"], [53, 35, 46, 33], [53, 36, 46, 34], [53, 40, 46, 38], [53, 41, 46, 39], [54, 6, 47, 4], [54, 10, 47, 8], [54, 11, 47, 9, "ctx"], [54, 14, 47, 12], [54, 16, 47, 14], [55, 8, 48, 6, "console"], [55, 15, 48, 13], [55, 16, 48, 14, "error"], [55, 21, 48, 19], [55, 22, 48, 20], [55, 75, 48, 73], [55, 76, 48, 74], [56, 8, 49, 6, "setError"], [56, 16, 49, 14], [56, 17, 49, 15], [56, 47, 49, 45], [56, 48, 49, 46], [57, 8, 50, 6], [58, 6, 51, 4], [59, 6, 53, 4], [59, 10, 53, 8, "running"], [59, 17, 53, 15], [59, 20, 53, 18], [59, 24, 53, 22], [60, 6, 54, 4, "debugCounterRef"], [60, 21, 54, 19], [60, 22, 54, 20, "current"], [60, 29, 54, 27], [60, 32, 54, 30], [60, 33, 54, 31], [61, 6, 56, 4], [61, 12, 56, 10, "loadTensorFlowAndBlazeFace"], [61, 38, 56, 36], [61, 41, 56, 39], [61, 47, 56, 39, "loadTensorFlowAndBlazeFace"], [61, 48, 56, 39], [61, 53, 56, 51], [62, 8, 57, 6], [62, 12, 57, 10], [63, 10, 58, 8, "console"], [63, 17, 58, 15], [63, 18, 58, 16, "log"], [63, 21, 58, 19], [63, 22, 58, 20], [63, 71, 58, 69], [63, 72, 58, 70], [65, 10, 60, 8], [66, 10, 61, 8], [66, 14, 61, 12], [66, 15, 61, 14, "window"], [66, 21, 61, 20], [66, 22, 61, 29, "tf"], [66, 24, 61, 31], [66, 26, 61, 33], [67, 12, 62, 10], [67, 18, 62, 16, "loadScript"], [67, 28, 62, 26], [67, 29, 62, 27], [67, 98, 62, 96], [67, 99, 62, 97], [68, 12, 63, 10, "console"], [68, 19, 63, 17], [68, 20, 63, 18, "log"], [68, 23, 63, 21], [68, 24, 63, 22], [68, 69, 63, 67], [68, 70, 63, 68], [69, 10, 64, 8], [71, 10, 66, 8], [72, 10, 67, 8], [72, 14, 67, 12], [72, 15, 67, 14, "window"], [72, 21, 67, 20], [72, 22, 67, 29, "blazeface"], [72, 31, 67, 38], [72, 33, 67, 40], [73, 12, 68, 10], [73, 18, 68, 16, "loadScript"], [73, 28, 68, 26], [73, 29, 68, 27], [73, 112, 68, 110], [73, 113, 68, 111], [74, 12, 69, 10, "console"], [74, 19, 69, 17], [74, 20, 69, 18, "log"], [74, 23, 69, 21], [74, 24, 69, 22], [74, 65, 69, 63], [74, 66, 69, 64], [75, 10, 70, 8], [77, 10, 72, 8], [78, 10, 73, 8, "console"], [78, 17, 73, 15], [78, 18, 73, 16, "log"], [78, 21, 73, 19], [78, 22, 73, 20], [78, 78, 73, 76], [78, 79, 73, 77], [79, 10, 74, 8], [79, 16, 74, 14, "blazeface"], [79, 25, 74, 23], [79, 28, 74, 27, "window"], [79, 34, 74, 33], [79, 35, 74, 42, "blazeface"], [79, 44, 74, 51], [80, 10, 75, 8, "modelRef"], [80, 18, 75, 16], [80, 19, 75, 17, "current"], [80, 26, 75, 24], [80, 29, 75, 27], [80, 35, 75, 33, "blazeface"], [80, 44, 75, 42], [80, 45, 75, 43, "load"], [80, 49, 75, 47], [80, 50, 75, 48], [80, 51, 75, 49], [81, 10, 76, 8, "console"], [81, 17, 76, 15], [81, 18, 76, 16, "log"], [81, 21, 76, 19], [81, 22, 76, 20], [81, 84, 76, 82], [81, 85, 76, 83], [82, 10, 78, 8, "setIsLoading"], [82, 22, 78, 20], [82, 23, 78, 21], [82, 28, 78, 26], [82, 29, 78, 27], [83, 10, 79, 8, "setError"], [83, 18, 79, 16], [83, 19, 79, 17], [83, 23, 79, 21], [83, 24, 79, 22], [84, 8, 81, 6], [84, 9, 81, 7], [84, 10, 81, 8], [84, 17, 81, 15, "error"], [84, 22, 81, 20], [84, 24, 81, 22], [85, 10, 82, 8, "console"], [85, 17, 82, 15], [85, 18, 82, 16, "error"], [85, 23, 82, 21], [85, 24, 82, 22], [85, 87, 82, 85], [85, 89, 82, 87, "error"], [85, 94, 82, 92], [85, 95, 82, 93], [86, 10, 83, 8, "setError"], [86, 18, 83, 16], [86, 19, 83, 17], [86, 53, 83, 51, "error"], [86, 58, 83, 56], [86, 59, 83, 57, "message"], [86, 66, 83, 64], [86, 68, 83, 66], [86, 69, 83, 67], [87, 10, 84, 8, "setIsLoading"], [87, 22, 84, 20], [87, 23, 84, 21], [87, 28, 84, 26], [87, 29, 84, 27], [88, 8, 85, 6], [89, 6, 86, 4], [89, 7, 86, 5], [90, 6, 88, 4], [90, 12, 88, 10, "loadScript"], [90, 22, 88, 20], [90, 25, 88, 24, "src"], [90, 28, 88, 35], [90, 32, 88, 55], [91, 8, 89, 6], [91, 15, 89, 13], [91, 19, 89, 17, "Promise"], [91, 26, 89, 24], [91, 27, 89, 25], [91, 28, 89, 26, "resolve"], [91, 35, 89, 33], [91, 37, 89, 35, "reject"], [91, 43, 89, 41], [91, 48, 89, 46], [92, 10, 90, 8], [92, 16, 90, 14, "script"], [92, 22, 90, 20], [92, 25, 90, 23, "document"], [92, 33, 90, 31], [92, 34, 90, 32, "createElement"], [92, 47, 90, 45], [92, 48, 90, 46], [92, 56, 90, 54], [92, 57, 90, 55], [93, 10, 91, 8, "script"], [93, 16, 91, 14], [93, 17, 91, 15, "src"], [93, 20, 91, 18], [93, 23, 91, 21, "src"], [93, 26, 91, 24], [94, 10, 92, 8, "script"], [94, 16, 92, 14], [94, 17, 92, 15, "async"], [94, 22, 92, 20], [94, 25, 92, 23], [94, 29, 92, 27], [95, 10, 93, 8, "script"], [95, 16, 93, 14], [95, 17, 93, 15, "onload"], [95, 23, 93, 21], [95, 26, 93, 24], [95, 32, 93, 30, "resolve"], [95, 39, 93, 37], [95, 40, 93, 38], [95, 41, 93, 39], [96, 10, 94, 8, "script"], [96, 16, 94, 14], [96, 17, 94, 15, "onerror"], [96, 24, 94, 22], [96, 27, 94, 25], [96, 33, 94, 31, "reject"], [96, 39, 94, 37], [96, 40, 94, 38], [96, 44, 94, 42, "Error"], [96, 49, 94, 47], [96, 50, 94, 48], [96, 76, 94, 74, "src"], [96, 79, 94, 77], [96, 81, 94, 79], [96, 82, 94, 80], [96, 83, 94, 81], [97, 10, 95, 8, "document"], [97, 18, 95, 16], [97, 19, 95, 17, "head"], [97, 23, 95, 21], [97, 24, 95, 22, "append<PERSON><PERSON><PERSON>"], [97, 35, 95, 33], [97, 36, 95, 34, "script"], [97, 42, 95, 40], [97, 43, 95, 41], [98, 8, 96, 6], [98, 9, 96, 7], [98, 10, 96, 8], [99, 6, 97, 4], [99, 7, 97, 5], [100, 6, 99, 4], [100, 12, 99, 10, "applyFallbackBlur"], [100, 29, 99, 27], [100, 32, 99, 30, "applyFallbackBlur"], [100, 33, 99, 31, "ctx"], [100, 36, 99, 60], [100, 38, 99, 62, "videoW"], [100, 44, 99, 76], [100, 46, 99, 78, "videoH"], [100, 52, 99, 92], [100, 54, 99, 94, "canvasW"], [100, 61, 99, 109], [100, 63, 99, 111, "canvasH"], [100, 70, 99, 126], [100, 75, 99, 131], [101, 8, 100, 6], [102, 8, 101, 6], [102, 14, 101, 12, "scale"], [102, 19, 101, 17], [102, 22, 101, 20, "Math"], [102, 26, 101, 24], [102, 27, 101, 25, "max"], [102, 30, 101, 28], [102, 31, 101, 29, "canvasW"], [102, 38, 101, 36], [102, 41, 101, 39, "videoW"], [102, 47, 101, 45], [102, 49, 101, 47, "canvasH"], [102, 56, 101, 54], [102, 59, 101, 57, "videoH"], [102, 65, 101, 63], [102, 66, 101, 64], [103, 8, 102, 6], [103, 14, 102, 12, "scaledW"], [103, 21, 102, 19], [103, 24, 102, 22, "videoW"], [103, 30, 102, 28], [103, 33, 102, 31, "scale"], [103, 38, 102, 36], [104, 8, 103, 6], [104, 14, 103, 12, "scaledH"], [104, 21, 103, 19], [104, 24, 103, 22, "videoH"], [104, 30, 103, 28], [104, 33, 103, 31, "scale"], [104, 38, 103, 36], [105, 8, 104, 6], [105, 14, 104, 12, "offsetX"], [105, 21, 104, 19], [105, 24, 104, 22], [105, 25, 104, 23, "canvasW"], [105, 32, 104, 30], [105, 35, 104, 33, "scaledW"], [105, 42, 104, 40], [105, 46, 104, 44], [105, 47, 104, 45], [106, 8, 105, 6], [106, 14, 105, 12, "offsetY"], [106, 21, 105, 19], [106, 24, 105, 22], [106, 25, 105, 23, "canvasH"], [106, 32, 105, 30], [106, 35, 105, 33, "scaledH"], [106, 42, 105, 40], [106, 46, 105, 44], [106, 47, 105, 45], [108, 8, 107, 6], [109, 8, 108, 6], [109, 14, 108, 12, "faceAreas"], [109, 23, 108, 21], [109, 26, 108, 24], [109, 27, 109, 8], [110, 10, 109, 10, "x"], [110, 11, 109, 11], [110, 13, 109, 13], [110, 17, 109, 17], [111, 10, 109, 19, "y"], [111, 11, 109, 20], [111, 13, 109, 22], [111, 17, 109, 26], [112, 10, 109, 28, "w"], [112, 11, 109, 29], [112, 13, 109, 31], [112, 16, 109, 34], [113, 10, 109, 36, "h"], [113, 11, 109, 37], [113, 13, 109, 39], [114, 8, 109, 43], [114, 9, 109, 44], [115, 8, 109, 46], [116, 8, 110, 8], [117, 10, 110, 10, "x"], [117, 11, 110, 11], [117, 13, 110, 13], [117, 16, 110, 16], [118, 10, 110, 18, "y"], [118, 11, 110, 19], [118, 13, 110, 21], [118, 16, 110, 24], [119, 10, 110, 26, "w"], [119, 11, 110, 27], [119, 13, 110, 29], [119, 17, 110, 33], [120, 10, 110, 35, "h"], [120, 11, 110, 36], [120, 13, 110, 38], [121, 8, 110, 42], [121, 9, 110, 43], [122, 8, 110, 46], [123, 8, 111, 8], [124, 10, 111, 10, "x"], [124, 11, 111, 11], [124, 13, 111, 13], [124, 17, 111, 17], [125, 10, 111, 19, "y"], [125, 11, 111, 20], [125, 13, 111, 22], [125, 16, 111, 25], [126, 10, 111, 27, "w"], [126, 11, 111, 28], [126, 13, 111, 30], [126, 17, 111, 34], [127, 10, 111, 36, "h"], [127, 11, 111, 37], [127, 13, 111, 39], [128, 8, 111, 43], [128, 9, 111, 44], [128, 10, 111, 46], [129, 8, 111, 46], [129, 9, 112, 7], [130, 8, 114, 6, "faceAreas"], [130, 17, 114, 15], [130, 18, 114, 16, "for<PERSON>ach"], [130, 25, 114, 23], [130, 26, 114, 24, "area"], [130, 30, 114, 28], [130, 34, 114, 32], [131, 10, 115, 8], [131, 16, 115, 14, "x"], [131, 17, 115, 15], [131, 20, 115, 18, "area"], [131, 24, 115, 22], [131, 25, 115, 23, "x"], [131, 26, 115, 24], [131, 29, 115, 27, "scaledW"], [131, 36, 115, 34], [131, 39, 115, 37, "offsetX"], [131, 46, 115, 44], [132, 10, 116, 8], [132, 16, 116, 14, "y"], [132, 17, 116, 15], [132, 20, 116, 18, "area"], [132, 24, 116, 22], [132, 25, 116, 23, "y"], [132, 26, 116, 24], [132, 29, 116, 27, "scaledH"], [132, 36, 116, 34], [132, 39, 116, 37, "offsetY"], [132, 46, 116, 44], [133, 10, 117, 8], [133, 16, 117, 14, "w"], [133, 17, 117, 15], [133, 20, 117, 18, "area"], [133, 24, 117, 22], [133, 25, 117, 23, "w"], [133, 26, 117, 24], [133, 29, 117, 27, "scaledW"], [133, 36, 117, 34], [134, 10, 118, 8], [134, 16, 118, 14, "h"], [134, 17, 118, 15], [134, 20, 118, 18, "area"], [134, 24, 118, 22], [134, 25, 118, 23, "h"], [134, 26, 118, 24], [134, 29, 118, 27, "scaledH"], [134, 36, 118, 34], [135, 10, 120, 8, "ctx"], [135, 13, 120, 11], [135, 14, 120, 12, "save"], [135, 18, 120, 16], [135, 19, 120, 17], [135, 20, 120, 18], [136, 10, 121, 8, "ctx"], [136, 13, 121, 11], [136, 14, 121, 12, "beginPath"], [136, 23, 121, 21], [136, 24, 121, 22], [136, 25, 121, 23], [137, 10, 122, 8, "ctx"], [137, 13, 122, 11], [137, 14, 122, 12, "ellipse"], [137, 21, 122, 19], [137, 22, 122, 20, "x"], [137, 23, 122, 21], [137, 26, 122, 24, "w"], [137, 27, 122, 25], [137, 30, 122, 28], [137, 31, 122, 29], [137, 33, 122, 31, "y"], [137, 34, 122, 32], [137, 37, 122, 35, "h"], [137, 38, 122, 36], [137, 41, 122, 39], [137, 42, 122, 40], [137, 44, 122, 42, "w"], [137, 45, 122, 43], [137, 48, 122, 46], [137, 49, 122, 47], [137, 51, 122, 49, "h"], [137, 52, 122, 50], [137, 55, 122, 53], [137, 56, 122, 54], [137, 58, 122, 56], [137, 59, 122, 57], [137, 61, 122, 59], [137, 62, 122, 60], [137, 64, 122, 62, "Math"], [137, 68, 122, 66], [137, 69, 122, 67, "PI"], [137, 71, 122, 69], [137, 74, 122, 72], [137, 75, 122, 73], [137, 76, 122, 74], [138, 10, 123, 8, "ctx"], [138, 13, 123, 11], [138, 14, 123, 12, "clip"], [138, 18, 123, 16], [138, 19, 123, 17], [138, 20, 123, 18], [139, 10, 124, 8, "ctx"], [139, 13, 124, 11], [139, 14, 124, 12, "filter"], [139, 20, 124, 18], [139, 23, 124, 21], [139, 35, 124, 33], [140, 10, 125, 8, "ctx"], [140, 13, 125, 11], [140, 14, 125, 12, "drawImage"], [140, 23, 125, 21], [140, 24, 125, 22, "video"], [140, 29, 125, 27], [140, 31, 125, 29], [140, 32, 125, 30], [140, 34, 125, 32], [140, 35, 125, 33], [140, 37, 125, 35, "canvasW"], [140, 44, 125, 42], [140, 46, 125, 44, "canvasH"], [140, 53, 125, 51], [140, 54, 125, 52], [141, 10, 126, 8, "ctx"], [141, 13, 126, 11], [141, 14, 126, 12, "restore"], [141, 21, 126, 19], [141, 22, 126, 20], [141, 23, 126, 21], [142, 8, 127, 6], [142, 9, 127, 7], [142, 10, 127, 8], [143, 6, 128, 4], [143, 7, 128, 5], [144, 6, 130, 4], [144, 12, 130, 10, "loop"], [144, 16, 130, 14], [144, 19, 130, 17], [144, 25, 130, 17, "loop"], [144, 26, 130, 17], [144, 31, 130, 29], [145, 8, 131, 6], [145, 12, 131, 10], [145, 13, 131, 11, "running"], [145, 20, 131, 18], [145, 22, 131, 20], [146, 8, 132, 6, "rafRef"], [146, 14, 132, 12], [146, 15, 132, 13, "current"], [146, 22, 132, 20], [146, 25, 132, 23, "requestAnimationFrame"], [146, 46, 132, 44], [146, 47, 132, 45, "loop"], [146, 51, 132, 49], [146, 52, 132, 50], [147, 8, 134, 6], [147, 12, 134, 10], [147, 13, 134, 11, "modelRef"], [147, 21, 134, 19], [147, 22, 134, 20, "current"], [147, 29, 134, 27], [147, 31, 134, 29], [148, 10, 135, 8], [149, 10, 136, 8, "debugCounterRef"], [149, 25, 136, 23], [149, 26, 136, 24, "current"], [149, 33, 136, 31], [149, 35, 136, 33], [150, 10, 137, 8], [150, 14, 137, 12, "debugCounterRef"], [150, 29, 137, 27], [150, 30, 137, 28, "current"], [150, 37, 137, 35], [150, 40, 137, 38], [150, 42, 137, 40], [150, 47, 137, 45], [150, 48, 137, 46], [150, 50, 137, 48], [151, 12, 137, 50], [152, 12, 138, 10, "console"], [152, 19, 138, 17], [152, 20, 138, 18, "log"], [152, 23, 138, 21], [152, 24, 138, 22], [152, 87, 138, 85], [152, 88, 138, 86], [153, 10, 139, 8], [154, 10, 140, 8], [155, 8, 141, 6], [156, 8, 143, 6], [156, 12, 143, 10], [157, 10, 144, 8], [157, 16, 144, 14, "videoW"], [157, 22, 144, 20], [157, 25, 144, 23, "video"], [157, 30, 144, 28], [157, 31, 144, 29, "videoWidth"], [157, 41, 144, 39], [157, 45, 144, 43, "width"], [157, 50, 144, 48], [158, 10, 145, 8], [158, 16, 145, 14, "videoH"], [158, 22, 145, 20], [158, 25, 145, 23, "video"], [158, 30, 145, 28], [158, 31, 145, 29, "videoHeight"], [158, 42, 145, 40], [158, 46, 145, 44, "height"], [158, 52, 145, 50], [160, 10, 147, 8], [161, 10, 148, 8, "ctx"], [161, 13, 148, 11], [161, 14, 148, 12, "clearRect"], [161, 23, 148, 21], [161, 24, 148, 22], [161, 25, 148, 23], [161, 27, 148, 25], [161, 28, 148, 26], [161, 30, 148, 28, "canvas"], [161, 36, 148, 34], [161, 37, 148, 35, "width"], [161, 42, 148, 40], [161, 44, 148, 42, "canvas"], [161, 50, 148, 48], [161, 51, 148, 49, "height"], [161, 57, 148, 55], [161, 58, 148, 56], [163, 10, 150, 8], [164, 10, 151, 8, "debugCounterRef"], [164, 25, 151, 23], [164, 26, 151, 24, "current"], [164, 33, 151, 31], [164, 35, 151, 33], [165, 10, 152, 8], [165, 14, 152, 12, "debugCounterRef"], [165, 29, 152, 27], [165, 30, 152, 28, "current"], [165, 37, 152, 35], [165, 40, 152, 38], [165, 43, 152, 41], [165, 48, 152, 46], [165, 49, 152, 47], [165, 51, 152, 49], [166, 12, 152, 51], [167, 12, 153, 10, "console"], [167, 19, 153, 17], [167, 20, 153, 18, "log"], [167, 23, 153, 21], [167, 24, 153, 22], [167, 62, 153, 60], [167, 64, 153, 62], [168, 14, 154, 12, "dimensions"], [168, 24, 154, 22], [168, 26, 154, 24], [168, 29, 154, 27, "videoW"], [168, 35, 154, 33], [168, 39, 154, 37, "videoH"], [168, 45, 154, 43], [168, 47, 154, 45], [169, 14, 155, 12, "readyState"], [169, 24, 155, 22], [169, 26, 155, 24, "video"], [169, 31, 155, 29], [169, 32, 155, 30, "readyState"], [169, 42, 155, 40], [170, 14, 156, 12, "paused"], [170, 20, 156, 18], [170, 22, 156, 20, "video"], [170, 27, 156, 25], [170, 28, 156, 26, "paused"], [170, 34, 156, 32], [171, 14, 157, 12, "currentTime"], [171, 25, 157, 23], [171, 27, 157, 25, "video"], [171, 32, 157, 30], [171, 33, 157, 31, "currentTime"], [172, 12, 158, 10], [172, 13, 158, 11], [172, 14, 158, 12], [173, 10, 159, 8], [174, 10, 161, 8], [174, 14, 161, 12, "videoW"], [174, 20, 161, 18], [174, 25, 161, 23], [174, 26, 161, 24], [174, 30, 161, 28, "videoH"], [174, 36, 161, 34], [174, 41, 161, 39], [174, 42, 161, 40], [174, 44, 161, 42], [175, 12, 162, 10], [175, 16, 162, 14, "debugCounterRef"], [175, 31, 162, 29], [175, 32, 162, 30, "current"], [175, 39, 162, 37], [175, 42, 162, 40], [175, 44, 162, 42], [175, 49, 162, 47], [175, 50, 162, 48], [175, 52, 162, 50], [176, 14, 163, 12, "console"], [176, 21, 163, 19], [176, 22, 163, 20, "log"], [176, 25, 163, 23], [176, 26, 163, 24], [176, 80, 163, 78], [176, 82, 163, 80, "videoW"], [176, 88, 163, 86], [176, 90, 163, 88], [176, 93, 163, 91], [176, 95, 163, 93, "videoH"], [176, 101, 163, 99], [176, 102, 163, 100], [177, 12, 164, 10], [178, 12, 165, 10], [179, 10, 166, 8], [181, 10, 168, 8], [182, 10, 169, 8], [182, 16, 169, 14, "tf"], [182, 18, 169, 16], [182, 21, 169, 20, "window"], [182, 27, 169, 26], [182, 28, 169, 35, "tf"], [182, 30, 169, 37], [183, 10, 170, 8], [183, 16, 170, 14, "tensor"], [183, 22, 170, 20], [183, 25, 170, 23, "tf"], [183, 27, 170, 25], [183, 28, 170, 26, "browser"], [183, 35, 170, 33], [183, 36, 170, 34, "fromPixels"], [183, 46, 170, 44], [183, 47, 170, 45, "video"], [183, 52, 170, 50], [183, 53, 170, 51], [184, 10, 171, 8], [184, 16, 171, 14, "predictions"], [184, 27, 171, 25], [184, 30, 171, 28], [184, 36, 171, 34, "modelRef"], [184, 44, 171, 42], [184, 45, 171, 43, "current"], [184, 52, 171, 50], [184, 53, 171, 51, "estimateFaces"], [184, 66, 171, 64], [184, 67, 171, 65, "tensor"], [184, 73, 171, 71], [184, 75, 171, 73], [184, 80, 171, 78], [184, 82, 171, 80], [184, 85, 171, 83], [184, 86, 171, 84], [184, 87, 171, 85], [184, 88, 171, 86], [185, 10, 172, 8, "tensor"], [185, 16, 172, 14], [185, 17, 172, 15, "dispose"], [185, 24, 172, 22], [185, 25, 172, 23], [185, 26, 172, 24], [185, 27, 172, 25], [185, 28, 172, 26], [187, 10, 174, 8], [188, 10, 175, 8], [188, 14, 175, 12, "predictions"], [188, 25, 175, 23], [188, 26, 175, 24, "length"], [188, 32, 175, 30], [188, 35, 175, 33], [188, 36, 175, 34], [188, 38, 175, 36], [189, 12, 176, 10, "console"], [189, 19, 176, 17], [189, 20, 176, 18, "log"], [189, 23, 176, 21], [189, 24, 176, 22], [189, 62, 176, 60, "predictions"], [189, 73, 176, 71], [189, 74, 176, 72, "length"], [189, 80, 176, 78], [189, 101, 176, 99, "debugCounterRef"], [189, 116, 176, 114], [189, 117, 176, 115, "current"], [189, 124, 176, 122], [189, 126, 176, 124], [189, 127, 176, 125], [190, 10, 177, 8], [190, 11, 177, 9], [190, 17, 177, 15], [190, 21, 177, 19, "debugCounterRef"], [190, 36, 177, 34], [190, 37, 177, 35, "current"], [190, 44, 177, 42], [190, 47, 177, 45], [190, 50, 177, 48], [190, 55, 177, 53], [190, 56, 177, 54], [190, 58, 177, 56], [191, 12, 177, 58], [192, 12, 178, 10, "console"], [192, 19, 178, 17], [192, 20, 178, 18, "log"], [192, 23, 178, 21], [192, 24, 178, 22], [192, 75, 178, 73, "debugCounterRef"], [192, 90, 178, 88], [192, 91, 178, 89, "current"], [192, 98, 178, 96], [192, 101, 178, 99], [192, 102, 178, 100], [193, 10, 179, 8], [194, 10, 181, 8], [194, 14, 181, 12, "predictions"], [194, 25, 181, 23], [194, 26, 181, 24, "length"], [194, 32, 181, 30], [194, 37, 181, 35], [194, 38, 181, 36], [194, 40, 181, 38], [195, 12, 182, 10], [196, 12, 183, 10], [196, 16, 183, 14, "debugCounterRef"], [196, 31, 183, 29], [196, 32, 183, 30, "current"], [196, 39, 183, 37], [196, 42, 183, 40], [196, 44, 183, 42], [196, 46, 183, 44], [197, 14, 183, 46], [198, 14, 184, 12, "applyFallbackBlur"], [198, 31, 184, 29], [198, 32, 184, 30, "ctx"], [198, 35, 184, 33], [198, 37, 184, 35, "videoW"], [198, 43, 184, 41], [198, 45, 184, 43, "videoH"], [198, 51, 184, 49], [198, 53, 184, 51, "width"], [198, 58, 184, 56], [198, 60, 184, 58, "height"], [198, 66, 184, 64], [198, 67, 184, 65], [199, 12, 185, 10], [200, 12, 186, 10], [201, 10, 187, 8], [203, 10, 189, 8], [204, 10, 190, 8], [204, 16, 190, 14, "scale"], [204, 21, 190, 19], [204, 24, 190, 22, "Math"], [204, 28, 190, 26], [204, 29, 190, 27, "max"], [204, 32, 190, 30], [204, 33, 190, 31, "width"], [204, 38, 190, 36], [204, 41, 190, 39, "videoW"], [204, 47, 190, 45], [204, 49, 190, 47, "height"], [204, 55, 190, 53], [204, 58, 190, 56, "videoH"], [204, 64, 190, 62], [204, 65, 190, 63], [205, 10, 191, 8], [205, 16, 191, 14, "scaledW"], [205, 23, 191, 21], [205, 26, 191, 24, "videoW"], [205, 32, 191, 30], [205, 35, 191, 33, "scale"], [205, 40, 191, 38], [206, 10, 192, 8], [206, 16, 192, 14, "scaledH"], [206, 23, 192, 21], [206, 26, 192, 24, "videoH"], [206, 32, 192, 30], [206, 35, 192, 33, "scale"], [206, 40, 192, 38], [207, 10, 193, 8], [207, 16, 193, 14, "offsetX"], [207, 23, 193, 21], [207, 26, 193, 24], [207, 27, 193, 25, "width"], [207, 32, 193, 30], [207, 35, 193, 33, "scaledW"], [207, 42, 193, 40], [207, 46, 193, 44], [207, 47, 193, 45], [208, 10, 194, 8], [208, 16, 194, 14, "offsetY"], [208, 23, 194, 21], [208, 26, 194, 24], [208, 27, 194, 25, "height"], [208, 33, 194, 31], [208, 36, 194, 34, "scaledH"], [208, 43, 194, 41], [208, 47, 194, 45], [208, 48, 194, 46], [210, 10, 196, 8], [211, 10, 197, 8, "predictions"], [211, 21, 197, 19], [211, 22, 197, 20, "for<PERSON>ach"], [211, 29, 197, 27], [211, 30, 197, 28], [211, 31, 197, 29, "prediction"], [211, 41, 197, 44], [211, 43, 197, 46, "index"], [211, 48, 197, 59], [211, 53, 197, 64], [212, 12, 198, 10], [212, 18, 198, 16], [212, 19, 198, 17, "x1"], [212, 21, 198, 19], [212, 23, 198, 21, "y1"], [212, 25, 198, 23], [212, 26, 198, 24], [212, 29, 198, 27, "prediction"], [212, 39, 198, 37], [212, 40, 198, 38, "topLeft"], [212, 47, 198, 45], [213, 12, 199, 10], [213, 18, 199, 16], [213, 19, 199, 17, "x2"], [213, 21, 199, 19], [213, 23, 199, 21, "y2"], [213, 25, 199, 23], [213, 26, 199, 24], [213, 29, 199, 27, "prediction"], [213, 39, 199, 37], [213, 40, 199, 38, "bottomRight"], [213, 51, 199, 49], [214, 12, 201, 10], [214, 16, 201, 14, "debugCounterRef"], [214, 31, 201, 29], [214, 32, 201, 30, "current"], [214, 39, 201, 37], [214, 42, 201, 40], [214, 44, 201, 42], [214, 49, 201, 47], [214, 50, 201, 48], [214, 52, 201, 50], [215, 14, 202, 12, "console"], [215, 21, 202, 19], [215, 22, 202, 20, "log"], [215, 25, 202, 23], [215, 26, 202, 24], [215, 57, 202, 55, "index"], [215, 62, 202, 60], [215, 65, 202, 63], [215, 66, 202, 64], [215, 80, 202, 78], [215, 82, 202, 80], [216, 16, 202, 82, "x1"], [216, 18, 202, 84], [217, 16, 202, 86, "y1"], [217, 18, 202, 88], [218, 16, 202, 90, "x2"], [218, 18, 202, 92], [219, 16, 202, 94, "y2"], [220, 14, 202, 97], [220, 15, 202, 98], [220, 16, 202, 99], [221, 12, 203, 10], [223, 12, 205, 10], [224, 12, 206, 10], [224, 16, 206, 14, "x1"], [224, 18, 206, 16], [224, 22, 206, 20, "x2"], [224, 24, 206, 22], [224, 28, 206, 26, "y1"], [224, 30, 206, 28], [224, 34, 206, 32, "y2"], [224, 36, 206, 34], [224, 38, 206, 36], [225, 14, 207, 12, "console"], [225, 21, 207, 19], [225, 22, 207, 20, "warn"], [225, 26, 207, 24], [225, 27, 207, 25], [225, 87, 207, 85, "index"], [225, 92, 207, 90], [225, 95, 207, 93], [225, 96, 207, 94], [225, 98, 207, 96], [225, 99, 207, 97], [226, 14, 208, 12], [227, 12, 209, 10], [229, 12, 211, 10], [230, 12, 212, 10], [230, 18, 212, 16, "rawFaceWidth"], [230, 30, 212, 28], [230, 33, 212, 31, "Math"], [230, 37, 212, 35], [230, 38, 212, 36, "abs"], [230, 41, 212, 39], [230, 42, 212, 40, "x2"], [230, 44, 212, 42], [230, 47, 212, 45, "x1"], [230, 49, 212, 47], [230, 50, 212, 48], [231, 12, 213, 10], [231, 18, 213, 16, "rawFaceHeight"], [231, 31, 213, 29], [231, 34, 213, 32, "Math"], [231, 38, 213, 36], [231, 39, 213, 37, "abs"], [231, 42, 213, 40], [231, 43, 213, 41, "y2"], [231, 45, 213, 43], [231, 48, 213, 46, "y1"], [231, 50, 213, 48], [231, 51, 213, 49], [232, 12, 215, 10], [232, 16, 215, 14, "rawFaceWidth"], [232, 28, 215, 26], [232, 32, 215, 30], [232, 33, 215, 31], [232, 37, 215, 35, "rawFaceHeight"], [232, 50, 215, 48], [232, 54, 215, 52], [232, 55, 215, 53], [232, 57, 215, 55], [233, 14, 216, 12, "console"], [233, 21, 216, 19], [233, 22, 216, 20, "warn"], [233, 26, 216, 24], [233, 27, 216, 25], [233, 86, 216, 84, "index"], [233, 91, 216, 89], [233, 94, 216, 92], [233, 95, 216, 93], [233, 97, 216, 95], [233, 98, 216, 96], [234, 14, 217, 12], [235, 12, 218, 10], [237, 12, 220, 10], [238, 12, 221, 10], [238, 18, 221, 16, "centerX"], [238, 25, 221, 23], [238, 28, 221, 26], [238, 29, 221, 27, "x1"], [238, 31, 221, 29], [238, 34, 221, 32, "x2"], [238, 36, 221, 34], [238, 40, 221, 38], [238, 41, 221, 39], [239, 12, 222, 10], [239, 18, 222, 16, "centerY"], [239, 25, 222, 23], [239, 28, 222, 26], [239, 29, 222, 27, "y1"], [239, 31, 222, 29], [239, 34, 222, 32, "y2"], [239, 36, 222, 34], [239, 40, 222, 38], [239, 41, 222, 39], [240, 12, 223, 10], [240, 18, 223, 16, "faceWidth"], [240, 27, 223, 25], [240, 30, 223, 28, "rawFaceWidth"], [240, 42, 223, 40], [240, 45, 223, 43], [240, 48, 223, 46], [240, 49, 223, 47], [240, 50, 223, 48], [241, 12, 224, 10], [241, 18, 224, 16, "faceHeight"], [241, 28, 224, 26], [241, 31, 224, 29, "rawFaceHeight"], [241, 44, 224, 42], [241, 47, 224, 45], [241, 50, 224, 48], [241, 51, 224, 49], [241, 52, 224, 50], [243, 12, 226, 10], [243, 18, 226, 16, "expandedX1"], [243, 28, 226, 26], [243, 31, 226, 29, "centerX"], [243, 38, 226, 36], [243, 41, 226, 39, "faceWidth"], [243, 50, 226, 48], [243, 53, 226, 51], [243, 54, 226, 52], [244, 12, 227, 10], [244, 18, 227, 16, "expandedY1"], [244, 28, 227, 26], [244, 31, 227, 29, "centerY"], [244, 38, 227, 36], [244, 41, 227, 39, "faceHeight"], [244, 51, 227, 49], [244, 54, 227, 52], [244, 55, 227, 53], [246, 12, 229, 10], [247, 12, 230, 10], [247, 18, 230, 16, "canvasX"], [247, 25, 230, 23], [247, 28, 230, 26, "expandedX1"], [247, 38, 230, 36], [247, 41, 230, 39, "scale"], [247, 46, 230, 44], [247, 49, 230, 47, "offsetX"], [247, 56, 230, 54], [248, 12, 231, 10], [248, 18, 231, 16, "canvasY"], [248, 25, 231, 23], [248, 28, 231, 26, "expandedY1"], [248, 38, 231, 36], [248, 41, 231, 39, "scale"], [248, 46, 231, 44], [248, 49, 231, 47, "offsetY"], [248, 56, 231, 54], [249, 12, 232, 10], [249, 18, 232, 16, "canvasW"], [249, 25, 232, 23], [249, 28, 232, 26, "faceWidth"], [249, 37, 232, 35], [249, 40, 232, 38, "scale"], [249, 45, 232, 43], [250, 12, 233, 10], [250, 18, 233, 16, "canvasH"], [250, 25, 233, 23], [250, 28, 233, 26, "faceHeight"], [250, 38, 233, 36], [250, 41, 233, 39, "scale"], [250, 46, 233, 44], [252, 12, 235, 10], [253, 12, 236, 10], [253, 18, 236, 16, "radiusX"], [253, 25, 236, 23], [253, 28, 236, 26, "Math"], [253, 32, 236, 30], [253, 33, 236, 31, "max"], [253, 36, 236, 34], [253, 37, 236, 35, "canvasW"], [253, 44, 236, 42], [253, 47, 236, 45], [253, 48, 236, 46], [253, 50, 236, 48], [253, 51, 236, 49], [253, 52, 236, 50], [254, 12, 237, 10], [254, 18, 237, 16, "radiusY"], [254, 25, 237, 23], [254, 28, 237, 26, "Math"], [254, 32, 237, 30], [254, 33, 237, 31, "max"], [254, 36, 237, 34], [254, 37, 237, 35, "canvasH"], [254, 44, 237, 42], [254, 47, 237, 45], [254, 48, 237, 46], [254, 50, 237, 48], [254, 51, 237, 49], [254, 52, 237, 50], [256, 12, 239, 10], [257, 12, 240, 10, "ctx"], [257, 15, 240, 13], [257, 16, 240, 14, "save"], [257, 20, 240, 18], [257, 21, 240, 19], [257, 22, 240, 20], [258, 12, 241, 10, "ctx"], [258, 15, 241, 13], [258, 16, 241, 14, "beginPath"], [258, 25, 241, 23], [258, 26, 241, 24], [258, 27, 241, 25], [259, 12, 242, 10, "ctx"], [259, 15, 242, 13], [259, 16, 242, 14, "ellipse"], [259, 23, 242, 21], [259, 24, 242, 22, "canvasX"], [259, 31, 242, 29], [259, 34, 242, 32, "canvasW"], [259, 41, 242, 39], [259, 44, 242, 42], [259, 45, 242, 43], [259, 47, 242, 45, "canvasY"], [259, 54, 242, 52], [259, 57, 242, 55, "canvasH"], [259, 64, 242, 62], [259, 67, 242, 65], [259, 68, 242, 66], [259, 70, 242, 68, "radiusX"], [259, 77, 242, 75], [259, 79, 242, 77, "radiusY"], [259, 86, 242, 84], [259, 88, 242, 86], [259, 89, 242, 87], [259, 91, 242, 89], [259, 92, 242, 90], [259, 94, 242, 92, "Math"], [259, 98, 242, 96], [259, 99, 242, 97, "PI"], [259, 101, 242, 99], [259, 104, 242, 102], [259, 105, 242, 103], [259, 106, 242, 104], [260, 12, 243, 10, "ctx"], [260, 15, 243, 13], [260, 16, 243, 14, "clip"], [260, 20, 243, 18], [260, 21, 243, 19], [260, 22, 243, 20], [261, 12, 244, 10, "ctx"], [261, 15, 244, 13], [261, 16, 244, 14, "filter"], [261, 22, 244, 20], [261, 25, 244, 23], [261, 37, 244, 35], [262, 12, 245, 10, "ctx"], [262, 15, 245, 13], [262, 16, 245, 14, "drawImage"], [262, 25, 245, 23], [262, 26, 245, 24, "video"], [262, 31, 245, 29], [262, 33, 245, 31], [262, 34, 245, 32], [262, 36, 245, 34], [262, 37, 245, 35], [262, 39, 245, 37, "width"], [262, 44, 245, 42], [262, 46, 245, 44, "height"], [262, 52, 245, 50], [262, 53, 245, 51], [263, 12, 246, 10, "ctx"], [263, 15, 246, 13], [263, 16, 246, 14, "restore"], [263, 23, 246, 21], [263, 24, 246, 22], [263, 25, 246, 23], [264, 12, 248, 10], [264, 16, 248, 14, "debugCounterRef"], [264, 31, 248, 29], [264, 32, 248, 30, "current"], [264, 39, 248, 37], [264, 42, 248, 40], [264, 44, 248, 42], [264, 49, 248, 47], [264, 50, 248, 48], [264, 52, 248, 50], [265, 14, 249, 12, "console"], [265, 21, 249, 19], [265, 22, 249, 20, "log"], [265, 25, 249, 23], [265, 26, 249, 24], [265, 65, 249, 63, "index"], [265, 70, 249, 68], [265, 73, 249, 71], [265, 74, 249, 72], [265, 82, 249, 80, "Math"], [265, 86, 249, 84], [265, 87, 249, 85, "round"], [265, 92, 249, 90], [265, 93, 249, 91, "canvasX"], [265, 100, 249, 98], [265, 101, 249, 99], [265, 106, 249, 104, "Math"], [265, 110, 249, 108], [265, 111, 249, 109, "round"], [265, 116, 249, 114], [265, 117, 249, 115, "canvasY"], [265, 124, 249, 122], [265, 125, 249, 123], [265, 142, 249, 140, "Math"], [265, 146, 249, 144], [265, 147, 249, 145, "round"], [265, 152, 249, 150], [265, 153, 249, 151, "radiusX"], [265, 160, 249, 158], [265, 161, 249, 159], [265, 166, 249, 164, "Math"], [265, 170, 249, 168], [265, 171, 249, 169, "round"], [265, 176, 249, 174], [265, 177, 249, 175, "radiusY"], [265, 184, 249, 182], [265, 185, 249, 183], [265, 188, 249, 186], [265, 189, 249, 187], [266, 12, 250, 10], [267, 10, 251, 8], [267, 11, 251, 9], [267, 12, 251, 10], [268, 8, 253, 6], [268, 9, 253, 7], [268, 10, 253, 8], [268, 17, 253, 15, "error"], [268, 22, 253, 20], [268, 24, 253, 22], [269, 10, 254, 8], [270, 10, 255, 8], [270, 14, 255, 12, "debugCounterRef"], [270, 29, 255, 27], [270, 30, 255, 28, "current"], [270, 37, 255, 35], [270, 40, 255, 38], [270, 43, 255, 41], [270, 48, 255, 46], [270, 49, 255, 47], [270, 51, 255, 49], [271, 12, 256, 10, "console"], [271, 19, 256, 17], [271, 20, 256, 18, "error"], [271, 25, 256, 23], [271, 26, 256, 24], [271, 67, 256, 65], [271, 69, 256, 67, "error"], [271, 74, 256, 72], [271, 75, 256, 73], [272, 10, 257, 8], [273, 8, 258, 6], [274, 6, 259, 4], [274, 7, 259, 5], [276, 6, 261, 4], [277, 6, 262, 4, "loadTensorFlowAndBlazeFace"], [277, 32, 262, 30], [277, 33, 262, 31], [277, 34, 262, 32], [277, 35, 262, 33, "then"], [277, 39, 262, 37], [277, 40, 262, 38], [277, 46, 262, 44], [278, 8, 263, 6], [278, 12, 263, 10, "running"], [278, 19, 263, 17], [278, 21, 263, 19], [279, 10, 264, 8, "rafRef"], [279, 16, 264, 14], [279, 17, 264, 15, "current"], [279, 24, 264, 22], [279, 27, 264, 25, "requestAnimationFrame"], [279, 48, 264, 46], [279, 49, 264, 47, "loop"], [279, 53, 264, 51], [279, 54, 264, 52], [280, 8, 265, 6], [281, 6, 266, 4], [281, 7, 266, 5], [281, 8, 266, 6], [282, 6, 268, 4], [282, 13, 268, 11], [282, 19, 268, 17], [283, 8, 269, 6, "running"], [283, 15, 269, 13], [283, 18, 269, 16], [283, 23, 269, 21], [284, 8, 270, 6], [284, 12, 270, 10, "rafRef"], [284, 18, 270, 16], [284, 19, 270, 17, "current"], [284, 26, 270, 24], [284, 28, 270, 26], [285, 10, 271, 8, "cancelAnimationFrame"], [285, 30, 271, 28], [285, 31, 271, 29, "rafRef"], [285, 37, 271, 35], [285, 38, 271, 36, "current"], [285, 45, 271, 43], [285, 46, 271, 44], [286, 8, 272, 6], [287, 6, 273, 4], [287, 7, 273, 5], [288, 4, 274, 2], [288, 5, 274, 3], [288, 7, 274, 5], [288, 8, 274, 6, "containerId"], [288, 19, 274, 17], [288, 21, 274, 19, "width"], [288, 26, 274, 24], [288, 28, 274, 26, "height"], [288, 34, 274, 32], [288, 35, 274, 33], [288, 36, 274, 34], [289, 4, 276, 2], [289, 24, 277, 4], [289, 28, 277, 4, "_jsxDevRuntime"], [289, 42, 277, 4], [289, 43, 277, 4, "jsxDEV"], [289, 49, 277, 4], [289, 51, 277, 4, "_jsxDevRuntime"], [289, 65, 277, 4], [289, 66, 277, 4, "Fragment"], [289, 74, 277, 4], [290, 6, 277, 4, "children"], [290, 14, 277, 4], [290, 30, 278, 6], [290, 34, 278, 6, "_jsxDevRuntime"], [290, 48, 278, 6], [290, 49, 278, 6, "jsxDEV"], [290, 55, 278, 6], [291, 8, 279, 8, "ref"], [291, 11, 279, 11], [291, 13, 279, 13, "canvasRef"], [291, 22, 279, 23], [292, 8, 280, 8, "style"], [292, 13, 280, 13], [292, 15, 280, 15], [293, 10, 281, 10, "position"], [293, 18, 281, 18], [293, 20, 281, 20], [293, 30, 281, 30], [294, 10, 282, 10, "left"], [294, 14, 282, 14], [294, 16, 282, 16], [294, 17, 282, 17], [295, 10, 283, 10, "top"], [295, 13, 283, 13], [295, 15, 283, 15], [295, 16, 283, 16], [296, 10, 284, 10, "width"], [296, 15, 284, 15], [297, 10, 285, 10, "height"], [297, 16, 285, 16], [298, 10, 286, 10, "pointerEvents"], [298, 23, 286, 23], [298, 25, 286, 25], [298, 31, 286, 31], [299, 10, 287, 10, "zIndex"], [299, 16, 287, 16], [299, 18, 287, 18], [300, 8, 288, 8], [301, 6, 288, 10], [302, 8, 288, 10, "fileName"], [302, 16, 288, 10], [302, 18, 288, 10, "_jsxFileName"], [302, 30, 288, 10], [303, 8, 288, 10, "lineNumber"], [303, 18, 288, 10], [304, 8, 288, 10, "columnNumber"], [304, 20, 288, 10], [305, 6, 288, 10], [305, 13, 289, 7], [305, 14, 289, 8], [305, 16, 290, 7, "isLoading"], [305, 25, 290, 16], [305, 42, 291, 8], [305, 46, 291, 8, "_jsxDevRuntime"], [305, 60, 291, 8], [305, 61, 291, 8, "jsxDEV"], [305, 67, 291, 8], [306, 8, 291, 13, "style"], [306, 13, 291, 18], [306, 15, 291, 20], [307, 10, 292, 10, "position"], [307, 18, 292, 18], [307, 20, 292, 20], [307, 30, 292, 30], [308, 10, 293, 10, "top"], [308, 13, 293, 13], [308, 15, 293, 15], [308, 17, 293, 17], [309, 10, 294, 10, "left"], [309, 14, 294, 14], [309, 16, 294, 16], [309, 18, 294, 18], [310, 10, 295, 10, "background"], [310, 20, 295, 20], [310, 22, 295, 22], [310, 39, 295, 39], [311, 10, 296, 10, "color"], [311, 15, 296, 15], [311, 17, 296, 17], [311, 24, 296, 24], [312, 10, 297, 10, "padding"], [312, 17, 297, 17], [312, 19, 297, 19], [312, 29, 297, 29], [313, 10, 298, 10, "borderRadius"], [313, 22, 298, 22], [313, 24, 298, 24], [313, 29, 298, 29], [314, 10, 299, 10, "fontSize"], [314, 18, 299, 18], [314, 20, 299, 20], [314, 26, 299, 26], [315, 10, 300, 10, "zIndex"], [315, 16, 300, 16], [315, 18, 300, 18], [316, 8, 301, 8], [316, 9, 301, 10], [317, 8, 301, 10, "children"], [317, 16, 301, 10], [317, 18, 301, 11], [318, 6, 303, 8], [319, 8, 303, 8, "fileName"], [319, 16, 303, 8], [319, 18, 303, 8, "_jsxFileName"], [319, 30, 303, 8], [320, 8, 303, 8, "lineNumber"], [320, 18, 303, 8], [321, 8, 303, 8, "columnNumber"], [321, 20, 303, 8], [322, 6, 303, 8], [322, 13, 303, 13], [322, 14, 304, 7], [322, 16, 305, 7], [322, 17, 305, 8, "isLoading"], [322, 26, 305, 17], [322, 30, 305, 21], [322, 31, 305, 22, "error"], [322, 36, 305, 27], [322, 53, 306, 8], [322, 57, 306, 8, "_jsxDevRuntime"], [322, 71, 306, 8], [322, 72, 306, 8, "jsxDEV"], [322, 78, 306, 8], [323, 8, 306, 13, "style"], [323, 13, 306, 18], [323, 15, 306, 20], [324, 10, 307, 10, "position"], [324, 18, 307, 18], [324, 20, 307, 20], [324, 30, 307, 30], [325, 10, 308, 10, "top"], [325, 13, 308, 13], [325, 15, 308, 15], [325, 17, 308, 17], [326, 10, 309, 10, "left"], [326, 14, 309, 14], [326, 16, 309, 16], [326, 18, 309, 18], [327, 10, 310, 10, "background"], [327, 20, 310, 20], [327, 22, 310, 22], [327, 47, 310, 47], [328, 10, 311, 10, "color"], [328, 15, 311, 15], [328, 17, 311, 17], [328, 24, 311, 24], [329, 10, 312, 10, "padding"], [329, 17, 312, 17], [329, 19, 312, 19], [329, 29, 312, 29], [330, 10, 313, 10, "borderRadius"], [330, 22, 313, 22], [330, 24, 313, 24], [330, 29, 313, 29], [331, 10, 314, 10, "fontSize"], [331, 18, 314, 18], [331, 20, 314, 20], [331, 26, 314, 26], [332, 10, 315, 10, "zIndex"], [332, 16, 315, 16], [332, 18, 315, 18], [333, 8, 316, 8], [333, 9, 316, 10], [334, 8, 316, 10, "children"], [334, 16, 316, 10], [334, 18, 316, 11], [335, 6, 318, 8], [336, 8, 318, 8, "fileName"], [336, 16, 318, 8], [336, 18, 318, 8, "_jsxFileName"], [336, 30, 318, 8], [337, 8, 318, 8, "lineNumber"], [337, 18, 318, 8], [338, 8, 318, 8, "columnNumber"], [338, 20, 318, 8], [339, 6, 318, 8], [339, 13, 318, 13], [339, 14, 319, 7], [339, 16, 320, 7, "error"], [339, 21, 320, 12], [339, 38, 321, 8], [339, 42, 321, 8, "_jsxDevRuntime"], [339, 56, 321, 8], [339, 57, 321, 8, "jsxDEV"], [339, 63, 321, 8], [340, 8, 321, 13, "style"], [340, 13, 321, 18], [340, 15, 321, 20], [341, 10, 322, 10, "position"], [341, 18, 322, 18], [341, 20, 322, 20], [341, 30, 322, 30], [342, 10, 323, 10, "top"], [342, 13, 323, 13], [342, 15, 323, 15], [342, 17, 323, 17], [343, 10, 324, 10, "left"], [343, 14, 324, 14], [343, 16, 324, 16], [343, 18, 324, 18], [344, 10, 325, 10, "background"], [344, 20, 325, 20], [344, 22, 325, 22], [344, 41, 325, 41], [345, 10, 326, 10, "color"], [345, 15, 326, 15], [345, 17, 326, 17], [345, 24, 326, 24], [346, 10, 327, 10, "padding"], [346, 17, 327, 17], [346, 19, 327, 19], [346, 29, 327, 29], [347, 10, 328, 10, "borderRadius"], [347, 22, 328, 22], [347, 24, 328, 24], [347, 29, 328, 29], [348, 10, 329, 10, "fontSize"], [348, 18, 329, 18], [348, 20, 329, 20], [348, 26, 329, 26], [349, 10, 330, 10, "zIndex"], [349, 16, 330, 16], [349, 18, 330, 18], [350, 8, 331, 8], [350, 9, 331, 10], [351, 8, 331, 10, "children"], [351, 16, 331, 10], [351, 19, 331, 11], [351, 43, 332, 32], [351, 45, 332, 33, "error"], [351, 50, 332, 38], [352, 6, 332, 38], [353, 8, 332, 38, "fileName"], [353, 16, 332, 38], [353, 18, 332, 38, "_jsxFileName"], [353, 30, 332, 38], [354, 8, 332, 38, "lineNumber"], [354, 18, 332, 38], [355, 8, 332, 38, "columnNumber"], [355, 20, 332, 38], [356, 6, 332, 38], [356, 13, 333, 13], [356, 14, 334, 7], [357, 4, 334, 7], [357, 19, 335, 6], [357, 20, 335, 7], [358, 2, 337, 0], [359, 2, 337, 1, "_s"], [359, 4, 337, 1], [359, 5, 10, 24, "TensorFlowFaceCanvas"], [359, 25, 10, 44], [360, 2, 10, 44, "_c"], [360, 4, 10, 44], [360, 7, 10, 24, "TensorFlowFaceCanvas"], [360, 27, 10, 44], [361, 2, 10, 44], [361, 6, 10, 44, "_c"], [361, 8, 10, 44], [362, 2, 10, 44, "$RefreshReg$"], [362, 14, 10, 44], [362, 15, 10, 44, "_c"], [362, 17, 10, 44], [363, 0, 10, 44], [363, 3]], "functionMap": {"names": ["<global>", "TensorFlowFaceCanvas", "useEffect$argument_0", "loadTensorFlowAndBlazeFace", "loadScript", "Promise$argument_0", "script.onload", "script.onerror", "applyFallbackBlur", "faceAreas.forEach$argument_0", "loop", "predictions.forEach$argument_0", "loadTensorFlowAndBlazeFace.then$argument_0", "<anonymous>"], "mappings": "AAA;eCS;YCQ;uCCsC;KD8B;uBEE;yBCC;wBCI,eD;yBEC,wDF;ODE;KFC;8BME;wBCe;ODa;KNC;iBQE;4BCmE;SDsD;KRQ;sCUG;KVI;WWE;KXK;GDC;CD+D"}}, "type": "js/module"}]}