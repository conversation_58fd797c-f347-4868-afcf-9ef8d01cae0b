{"dependencies": [], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.shallowEq = exports.mapKeys = exports.exhaustiveCheck = void 0;\n  const _worklet_13114184306695_init_data = {\n    code: \"function typeddashJs1(obj){return Object.keys(obj);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\renderer\\\\typeddash.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"typeddashJs1\\\",\\\"obj\\\",\\\"Object\\\",\\\"keys\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/renderer/typeddash.js\\\"],\\\"mappings\\\":\\\"AAAuB,SAAAA,YAAOA,CAAAC,GAAA,EAG5B,MAAO,CAAAC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACzB\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const mapKeys = exports.mapKeys = function () {\n    const _e = [new global.Error(), 1, -27];\n    const typeddashJs1 = function (obj) {\n      return Object.keys(obj);\n    };\n    typeddashJs1.__closure = {};\n    typeddashJs1.__workletHash = 13114184306695;\n    typeddashJs1.__initData = _worklet_13114184306695_init_data;\n    typeddashJs1.__stackDetails = _e;\n    return typeddashJs1;\n  }();\n  const _worklet_11166336950651_init_data = {\n    code: \"function typeddashJs2(a){throw new Error(\\\"Unexhaustive handling for \\\"+a);}\",\n    location: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\node_modules\\\\@shopify\\\\react-native-skia\\\\lib\\\\module\\\\renderer\\\\typeddash.js\",\n    sourceMap: \"{\\\"version\\\":3,\\\"names\\\":[\\\"typeddashJs2\\\",\\\"a\\\",\\\"Error\\\"],\\\"sources\\\":[\\\"C:/Users/<USER>/Desktop/ECHO/apps/mobile/node_modules/@shopify/react-native-skia/lib/module/renderer/typeddash.js\\\"],\\\"mappings\\\":\\\"AAK+B,QAAC,CAAAA,YAAIA,CAAAC,CAAA,EAGlC,KAAM,IAAI,CAAAC,KAAK,8BAA8BD,CAAG,CAAC,CACnD\\\",\\\"ignoreList\\\":[]}\",\n    version: \"3.17.5\"\n  };\n  const exhaustiveCheck = exports.exhaustiveCheck = function () {\n    const _e = [new global.Error(), 1, -27];\n    const typeddashJs2 = function (a) {\n      throw new Error(`Unexhaustive handling for ${a}`);\n    };\n    typeddashJs2.__closure = {};\n    typeddashJs2.__workletHash = 11166336950651;\n    typeddashJs2.__initData = _worklet_11166336950651_init_data;\n    typeddashJs2.__stackDetails = _e;\n    return typeddashJs2;\n  }();\n\n  // Shallow eq on props (without children)\n  const shallowEq = (p1, p2) => {\n    const keys1 = mapKeys(p1);\n    const keys2 = mapKeys(p2);\n    if (keys1.length !== keys2.length) {\n      return false;\n    }\n    for (const key of keys1) {\n      if (key === \"children\") {\n        continue;\n      }\n      if (p1[key] !== p2[key]) {\n        return false;\n      }\n    }\n    return true;\n  };\n  exports.shallowEq = shallowEq;\n});", "lineCount": 59, "map": [[12, 2, 1, 7], [12, 8, 1, 13, "mapKeys"], [12, 15, 1, 20], [12, 18, 1, 20, "exports"], [12, 25, 1, 20], [12, 26, 1, 20, "mapKeys"], [12, 33, 1, 20], [12, 36, 1, 23], [13, 4, 1, 23], [13, 10, 1, 23, "_e"], [13, 12, 1, 23], [13, 20, 1, 23, "global"], [13, 26, 1, 23], [13, 27, 1, 23, "Error"], [13, 32, 1, 23], [14, 4, 1, 23], [14, 10, 1, 23, "typeddashJs1"], [14, 22, 1, 23], [14, 34, 1, 23, "typeddashJs1"], [14, 35, 1, 23, "obj"], [14, 38, 1, 26], [14, 40, 1, 30], [15, 6, 4, 2], [15, 13, 4, 9, "Object"], [15, 19, 4, 15], [15, 20, 4, 16, "keys"], [15, 24, 4, 20], [15, 25, 4, 21, "obj"], [15, 28, 4, 24], [15, 29, 4, 25], [16, 4, 5, 0], [16, 5, 5, 1], [17, 4, 5, 1, "typeddashJs1"], [17, 16, 5, 1], [17, 17, 5, 1, "__closure"], [17, 26, 5, 1], [18, 4, 5, 1, "typeddashJs1"], [18, 16, 5, 1], [18, 17, 5, 1, "__workletHash"], [18, 30, 5, 1], [19, 4, 5, 1, "typeddashJs1"], [19, 16, 5, 1], [19, 17, 5, 1, "__initData"], [19, 27, 5, 1], [19, 30, 5, 1, "_worklet_13114184306695_init_data"], [19, 63, 5, 1], [20, 4, 5, 1, "typeddashJs1"], [20, 16, 5, 1], [20, 17, 5, 1, "__stackDetails"], [20, 31, 5, 1], [20, 34, 5, 1, "_e"], [20, 36, 5, 1], [21, 4, 5, 1], [21, 11, 5, 1, "typeddashJs1"], [21, 23, 5, 1], [22, 2, 5, 1], [22, 3, 1, 23], [22, 5, 5, 1], [23, 2, 5, 2], [23, 8, 5, 2, "_worklet_11166336950651_init_data"], [23, 41, 5, 2], [24, 4, 5, 2, "code"], [24, 8, 5, 2], [25, 4, 5, 2, "location"], [25, 12, 5, 2], [26, 4, 5, 2, "sourceMap"], [26, 13, 5, 2], [27, 4, 5, 2, "version"], [27, 11, 5, 2], [28, 2, 5, 2], [29, 2, 6, 7], [29, 8, 6, 13, "exhaustiveCheck"], [29, 23, 6, 28], [29, 26, 6, 28, "exports"], [29, 33, 6, 28], [29, 34, 6, 28, "exhaustiveCheck"], [29, 49, 6, 28], [29, 52, 6, 31], [30, 4, 6, 31], [30, 10, 6, 31, "_e"], [30, 12, 6, 31], [30, 20, 6, 31, "global"], [30, 26, 6, 31], [30, 27, 6, 31, "Error"], [30, 32, 6, 31], [31, 4, 6, 31], [31, 10, 6, 31, "typeddashJs2"], [31, 22, 6, 31], [31, 34, 6, 31, "typeddashJs2"], [31, 35, 6, 31, "a"], [31, 36, 6, 32], [31, 38, 6, 36], [32, 6, 9, 2], [32, 12, 9, 8], [32, 16, 9, 12, "Error"], [32, 21, 9, 17], [32, 22, 9, 18], [32, 51, 9, 47, "a"], [32, 52, 9, 48], [32, 54, 9, 50], [32, 55, 9, 51], [33, 4, 10, 0], [33, 5, 10, 1], [34, 4, 10, 1, "typeddashJs2"], [34, 16, 10, 1], [34, 17, 10, 1, "__closure"], [34, 26, 10, 1], [35, 4, 10, 1, "typeddashJs2"], [35, 16, 10, 1], [35, 17, 10, 1, "__workletHash"], [35, 30, 10, 1], [36, 4, 10, 1, "typeddashJs2"], [36, 16, 10, 1], [36, 17, 10, 1, "__initData"], [36, 27, 10, 1], [36, 30, 10, 1, "_worklet_11166336950651_init_data"], [36, 63, 10, 1], [37, 4, 10, 1, "typeddashJs2"], [37, 16, 10, 1], [37, 17, 10, 1, "__stackDetails"], [37, 31, 10, 1], [37, 34, 10, 1, "_e"], [37, 36, 10, 1], [38, 4, 10, 1], [38, 11, 10, 1, "typeddashJs2"], [38, 23, 10, 1], [39, 2, 10, 1], [39, 3, 6, 31], [39, 5, 10, 1], [41, 2, 12, 0], [42, 2, 13, 7], [42, 8, 13, 13, "shallowEq"], [42, 17, 13, 22], [42, 20, 13, 25, "shallowEq"], [42, 21, 13, 26, "p1"], [42, 23, 13, 28], [42, 25, 13, 30, "p2"], [42, 27, 13, 32], [42, 32, 13, 37], [43, 4, 14, 2], [43, 10, 14, 8, "keys1"], [43, 15, 14, 13], [43, 18, 14, 16, "mapKeys"], [43, 25, 14, 23], [43, 26, 14, 24, "p1"], [43, 28, 14, 26], [43, 29, 14, 27], [44, 4, 15, 2], [44, 10, 15, 8, "keys2"], [44, 15, 15, 13], [44, 18, 15, 16, "mapKeys"], [44, 25, 15, 23], [44, 26, 15, 24, "p2"], [44, 28, 15, 26], [44, 29, 15, 27], [45, 4, 16, 2], [45, 8, 16, 6, "keys1"], [45, 13, 16, 11], [45, 14, 16, 12, "length"], [45, 20, 16, 18], [45, 25, 16, 23, "keys2"], [45, 30, 16, 28], [45, 31, 16, 29, "length"], [45, 37, 16, 35], [45, 39, 16, 37], [46, 6, 17, 4], [46, 13, 17, 11], [46, 18, 17, 16], [47, 4, 18, 2], [48, 4, 19, 2], [48, 9, 19, 7], [48, 15, 19, 13, "key"], [48, 18, 19, 16], [48, 22, 19, 20, "keys1"], [48, 27, 19, 25], [48, 29, 19, 27], [49, 6, 20, 4], [49, 10, 20, 8, "key"], [49, 13, 20, 11], [49, 18, 20, 16], [49, 28, 20, 26], [49, 30, 20, 28], [50, 8, 21, 6], [51, 6, 22, 4], [52, 6, 23, 4], [52, 10, 23, 8, "p1"], [52, 12, 23, 10], [52, 13, 23, 11, "key"], [52, 16, 23, 14], [52, 17, 23, 15], [52, 22, 23, 20, "p2"], [52, 24, 23, 22], [52, 25, 23, 23, "key"], [52, 28, 23, 26], [52, 29, 23, 27], [52, 31, 23, 29], [53, 8, 24, 6], [53, 15, 24, 13], [53, 20, 24, 18], [54, 6, 25, 4], [55, 4, 26, 2], [56, 4, 27, 2], [56, 11, 27, 9], [56, 15, 27, 13], [57, 2, 28, 0], [57, 3, 28, 1], [58, 2, 28, 2, "exports"], [58, 9, 28, 2], [58, 10, 28, 2, "shallowEq"], [58, 19, 28, 2], [58, 22, 28, 2, "shallowEq"], [58, 31, 28, 2], [59, 0, 28, 2], [59, 3]], "functionMap": {"names": ["<global>", "mapKeys", "exhaustiveCheck", "shallowEq"], "mappings": "AAA,uBC;CDI;+BEC;CFI;yBGG;CHe"}}, "type": "js/module"}]}