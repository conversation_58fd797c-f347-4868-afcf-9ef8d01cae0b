{"dependencies": [{"name": "../../dom/types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 57}, "end": {"line": 3, "column": 43, "index": 100}}], "key": "sExY87SNUbbJtOx4ghJnWxzXqE0=", "exportNames": ["*"]}}, {"name": "../Node", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 101}, "end": {"line": 4, "column": 68, "index": 169}}], "key": "629oO5tQuU3DWlSfXJ8iqlt7cNE=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.visit = exports.processPaint = void 0;\n  var _types = require(_dependencyMap[0], \"../../dom/types\");\n  var _Node = require(_dependencyMap[1], \"../Node\");\n  /* eslint-disable @typescript-eslint/no-explicit-any */\n\n  const processPaint = ({\n    opacity,\n    color,\n    strokeWidth,\n    blendMode,\n    style,\n    strokeJoin,\n    strokeCap,\n    strokeMiter,\n    antiAlias,\n    dither,\n    paint: paintRef\n  }) => {\n    const paint = {};\n    if (opacity !== undefined) {\n      paint.opacity = opacity;\n    }\n    if (color !== undefined) {\n      paint.color = color;\n    }\n    if (strokeWidth !== undefined) {\n      paint.strokeWidth = strokeWidth;\n    }\n    if (blendMode !== undefined) {\n      paint.blendMode = blendMode;\n    }\n    if (style !== undefined) {\n      paint.style = style;\n    }\n    if (strokeJoin !== undefined) {\n      paint.strokeJoin = strokeJoin;\n    }\n    if (strokeCap !== undefined) {\n      paint.strokeCap = strokeCap;\n    }\n    if (strokeMiter !== undefined) {\n      paint.strokeMiter = strokeMiter;\n    }\n    if (antiAlias !== undefined) {\n      paint.antiAlias = antiAlias;\n    }\n    if (dither !== undefined) {\n      paint.dither = dither;\n    }\n    if (paintRef !== undefined) {\n      paint.paint = paintRef;\n    }\n    if (opacity !== undefined || color !== undefined || strokeWidth !== undefined || blendMode !== undefined || style !== undefined || strokeJoin !== undefined || strokeCap !== undefined || strokeMiter !== undefined || antiAlias !== undefined || dither !== undefined || paintRef !== undefined) {\n      return paint;\n    }\n    return null;\n  };\n  exports.processPaint = processPaint;\n  const processCTM = ({\n    clip,\n    invertClip,\n    transform,\n    origin,\n    matrix,\n    layer\n  }) => {\n    const ctm = {};\n    if (clip) {\n      ctm.clip = clip;\n    }\n    if (invertClip) {\n      ctm.invertClip = invertClip;\n    }\n    if (transform) {\n      ctm.transform = transform;\n    }\n    if (origin) {\n      ctm.origin = origin;\n    }\n    if (matrix) {\n      ctm.matrix = matrix;\n    }\n    if (layer) {\n      ctm.layer = layer;\n    }\n    if (clip !== undefined || invertClip !== undefined || transform !== undefined || origin !== undefined || matrix !== undefined || layer !== undefined) {\n      return ctm;\n    }\n    return null;\n  };\n  const pushColorFilters = (recorder, colorFilters) => {\n    colorFilters.forEach(colorFilter => {\n      if (colorFilter.children.length > 0) {\n        pushColorFilters(recorder, colorFilter.children);\n      }\n      recorder.pushColorFilter(colorFilter.type, colorFilter.props);\n      const needsComposition = colorFilter.type !== _types.NodeType.LerpColorFilter && colorFilter.children.length > 0;\n      if (needsComposition) {\n        recorder.composeColorFilter();\n      }\n    });\n  };\n  const pushPathEffects = (recorder, pathEffects) => {\n    pathEffects.forEach(pathEffect => {\n      if (pathEffect.children.length > 0) {\n        pushPathEffects(recorder, pathEffect.children);\n      }\n      recorder.pushPathEffect(pathEffect.type, pathEffect.props);\n      const needsComposition = pathEffect.type !== _types.NodeType.SumPathEffect && pathEffect.children.length > 0;\n      if (needsComposition) {\n        recorder.composePathEffect();\n      }\n    });\n  };\n  const pushImageFilters = (recorder, imageFilters) => {\n    imageFilters.forEach(imageFilter => {\n      if (imageFilter.children.length > 0) {\n        pushImageFilters(recorder, imageFilter.children);\n      }\n      if ((0, _Node.isImageFilter)(imageFilter.type)) {\n        recorder.pushImageFilter(imageFilter.type, imageFilter.props);\n      } else if ((0, _Node.isShader)(imageFilter.type)) {\n        recorder.pushShader(imageFilter.type, imageFilter.props);\n      }\n      const needsComposition = imageFilter.type !== _types.NodeType.BlendImageFilter && imageFilter.children.length > 0;\n      if (needsComposition) {\n        recorder.composeImageFilter();\n      }\n    });\n  };\n  const pushShaders = (recorder, shaders) => {\n    shaders.forEach(shader => {\n      if (shader.children.length > 0) {\n        pushShaders(recorder, shader.children);\n      }\n      recorder.pushShader(shader.type, shader.props);\n    });\n  };\n  const pushMaskFilters = (recorder, maskFilters) => {\n    if (maskFilters.length > 0) {\n      recorder.pushBlurMaskFilter(maskFilters[maskFilters.length - 1].props);\n    }\n  };\n  const pushPaints = (recorder, paints) => {\n    paints.forEach(paint => {\n      recorder.savePaint(paint.props);\n      const {\n        colorFilters,\n        maskFilters,\n        shaders,\n        imageFilters,\n        pathEffects\n      } = (0, _Node.sortNodeChildren)(paint);\n      pushColorFilters(recorder, colorFilters);\n      pushImageFilters(recorder, imageFilters);\n      pushMaskFilters(recorder, maskFilters);\n      pushShaders(recorder, shaders);\n      pushPathEffects(recorder, pathEffects);\n      recorder.restorePaintDeclaration();\n    });\n  };\n  const visitNode = (recorder, node) => {\n    if (node.type === _types.NodeType.Group) {\n      recorder.saveGroup();\n    }\n    const {\n      props\n    } = node;\n    const {\n      colorFilters,\n      maskFilters,\n      drawings,\n      shaders,\n      imageFilters,\n      pathEffects,\n      paints\n    } = (0, _Node.sortNodeChildren)(node);\n    const paint = processPaint(props);\n    const shouldPushPaint = paint || colorFilters.length > 0 || maskFilters.length > 0 || imageFilters.length > 0 || pathEffects.length > 0 || shaders.length > 0;\n    if (shouldPushPaint) {\n      recorder.savePaint(paint !== null && paint !== void 0 ? paint : {});\n      pushColorFilters(recorder, colorFilters);\n      pushImageFilters(recorder, imageFilters);\n      pushMaskFilters(recorder, maskFilters);\n      pushShaders(recorder, shaders);\n      pushPathEffects(recorder, pathEffects);\n      // For mixed nodes like BackdropFilters we don't materialize the paint\n      if (node.type === _types.NodeType.BackdropFilter) {\n        recorder.saveBackdropFilter();\n      } else {\n        recorder.materializePaint();\n      }\n    }\n    pushPaints(recorder, paints);\n    if (node.type === _types.NodeType.Layer) {\n      recorder.saveLayer();\n    }\n    const ctm = processCTM(props);\n    const shouldRestore = !!ctm || node.type === _types.NodeType.Layer;\n    if (ctm) {\n      recorder.saveCTM(ctm);\n    }\n    switch (node.type) {\n      case _types.NodeType.Box:\n        const shadows = node.children.filter(n => n.type === _types.NodeType.BoxShadow)\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        .map(({\n          props\n        }) => ({\n          props\n        }));\n        recorder.drawBox(props, shadows);\n        break;\n      case _types.NodeType.Fill:\n        recorder.drawPaint();\n        break;\n      case _types.NodeType.Image:\n        recorder.drawImage(props);\n        break;\n      case _types.NodeType.Circle:\n        recorder.drawCircle(props);\n        break;\n      case _types.NodeType.Points:\n        recorder.drawPoints(props);\n        break;\n      case _types.NodeType.Path:\n        recorder.drawPath(props);\n        break;\n      case _types.NodeType.Rect:\n        recorder.drawRect(props);\n        break;\n      case _types.NodeType.RRect:\n        recorder.drawRRect(props);\n        break;\n      case _types.NodeType.Oval:\n        recorder.drawOval(props);\n        break;\n      case _types.NodeType.Line:\n        recorder.drawLine(props);\n        break;\n      case _types.NodeType.Patch:\n        recorder.drawPatch(props);\n        break;\n      case _types.NodeType.Vertices:\n        recorder.drawVertices(props);\n        break;\n      case _types.NodeType.DiffRect:\n        recorder.drawDiffRect(props);\n        break;\n      case _types.NodeType.Text:\n        recorder.drawText(props);\n        break;\n      case _types.NodeType.TextPath:\n        recorder.drawTextPath(props);\n        break;\n      case _types.NodeType.TextBlob:\n        recorder.drawTextBlob(props);\n        break;\n      case _types.NodeType.Glyphs:\n        recorder.drawGlyphs(props);\n        break;\n      case _types.NodeType.Picture:\n        recorder.drawPicture(props);\n        break;\n      case _types.NodeType.ImageSVG:\n        recorder.drawImageSVG(props);\n        break;\n      case _types.NodeType.Paragraph:\n        recorder.drawParagraph(props);\n        break;\n      case _types.NodeType.Atlas:\n        recorder.drawAtlas(props);\n        break;\n    }\n    drawings.forEach(drawing => {\n      visitNode(recorder, drawing);\n    });\n    if (shouldPushPaint) {\n      recorder.restorePaint();\n    }\n    if (shouldRestore) {\n      recorder.restoreCTM();\n    }\n    if (node.type === _types.NodeType.Group) {\n      recorder.restoreGroup();\n    }\n  };\n  const visit = (recorder, root) => {\n    root.forEach(node => {\n      visitNode(recorder, node);\n    });\n  };\n  exports.visit = visit;\n});", "lineCount": 298, "map": [[6, 2, 3, 0], [6, 6, 3, 0, "_types"], [6, 12, 3, 0], [6, 15, 3, 0, "require"], [6, 22, 3, 0], [6, 23, 3, 0, "_dependencyMap"], [6, 37, 3, 0], [7, 2, 4, 0], [7, 6, 4, 0, "_Node"], [7, 11, 4, 0], [7, 14, 4, 0, "require"], [7, 21, 4, 0], [7, 22, 4, 0, "_dependencyMap"], [7, 36, 4, 0], [8, 2, 1, 0], [10, 2, 5, 7], [10, 8, 5, 13, "processPaint"], [10, 20, 5, 25], [10, 23, 5, 28, "processPaint"], [10, 24, 5, 29], [11, 4, 6, 2, "opacity"], [11, 11, 6, 9], [12, 4, 7, 2, "color"], [12, 9, 7, 7], [13, 4, 8, 2, "strokeWidth"], [13, 15, 8, 13], [14, 4, 9, 2, "blendMode"], [14, 13, 9, 11], [15, 4, 10, 2, "style"], [15, 9, 10, 7], [16, 4, 11, 2, "<PERSON><PERSON><PERSON><PERSON>"], [16, 14, 11, 12], [17, 4, 12, 2, "strokeCap"], [17, 13, 12, 11], [18, 4, 13, 2, "strokeMiter"], [18, 15, 13, 13], [19, 4, 14, 2, "antiAlias"], [19, 13, 14, 11], [20, 4, 15, 2, "dither"], [20, 10, 15, 8], [21, 4, 16, 2, "paint"], [21, 9, 16, 7], [21, 11, 16, 9, "paintRef"], [22, 2, 17, 0], [22, 3, 17, 1], [22, 8, 17, 6], [23, 4, 18, 2], [23, 10, 18, 8, "paint"], [23, 15, 18, 13], [23, 18, 18, 16], [23, 19, 18, 17], [23, 20, 18, 18], [24, 4, 19, 2], [24, 8, 19, 6, "opacity"], [24, 15, 19, 13], [24, 20, 19, 18, "undefined"], [24, 29, 19, 27], [24, 31, 19, 29], [25, 6, 20, 4, "paint"], [25, 11, 20, 9], [25, 12, 20, 10, "opacity"], [25, 19, 20, 17], [25, 22, 20, 20, "opacity"], [25, 29, 20, 27], [26, 4, 21, 2], [27, 4, 22, 2], [27, 8, 22, 6, "color"], [27, 13, 22, 11], [27, 18, 22, 16, "undefined"], [27, 27, 22, 25], [27, 29, 22, 27], [28, 6, 23, 4, "paint"], [28, 11, 23, 9], [28, 12, 23, 10, "color"], [28, 17, 23, 15], [28, 20, 23, 18, "color"], [28, 25, 23, 23], [29, 4, 24, 2], [30, 4, 25, 2], [30, 8, 25, 6, "strokeWidth"], [30, 19, 25, 17], [30, 24, 25, 22, "undefined"], [30, 33, 25, 31], [30, 35, 25, 33], [31, 6, 26, 4, "paint"], [31, 11, 26, 9], [31, 12, 26, 10, "strokeWidth"], [31, 23, 26, 21], [31, 26, 26, 24, "strokeWidth"], [31, 37, 26, 35], [32, 4, 27, 2], [33, 4, 28, 2], [33, 8, 28, 6, "blendMode"], [33, 17, 28, 15], [33, 22, 28, 20, "undefined"], [33, 31, 28, 29], [33, 33, 28, 31], [34, 6, 29, 4, "paint"], [34, 11, 29, 9], [34, 12, 29, 10, "blendMode"], [34, 21, 29, 19], [34, 24, 29, 22, "blendMode"], [34, 33, 29, 31], [35, 4, 30, 2], [36, 4, 31, 2], [36, 8, 31, 6, "style"], [36, 13, 31, 11], [36, 18, 31, 16, "undefined"], [36, 27, 31, 25], [36, 29, 31, 27], [37, 6, 32, 4, "paint"], [37, 11, 32, 9], [37, 12, 32, 10, "style"], [37, 17, 32, 15], [37, 20, 32, 18, "style"], [37, 25, 32, 23], [38, 4, 33, 2], [39, 4, 34, 2], [39, 8, 34, 6, "<PERSON><PERSON><PERSON><PERSON>"], [39, 18, 34, 16], [39, 23, 34, 21, "undefined"], [39, 32, 34, 30], [39, 34, 34, 32], [40, 6, 35, 4, "paint"], [40, 11, 35, 9], [40, 12, 35, 10, "<PERSON><PERSON><PERSON><PERSON>"], [40, 22, 35, 20], [40, 25, 35, 23, "<PERSON><PERSON><PERSON><PERSON>"], [40, 35, 35, 33], [41, 4, 36, 2], [42, 4, 37, 2], [42, 8, 37, 6, "strokeCap"], [42, 17, 37, 15], [42, 22, 37, 20, "undefined"], [42, 31, 37, 29], [42, 33, 37, 31], [43, 6, 38, 4, "paint"], [43, 11, 38, 9], [43, 12, 38, 10, "strokeCap"], [43, 21, 38, 19], [43, 24, 38, 22, "strokeCap"], [43, 33, 38, 31], [44, 4, 39, 2], [45, 4, 40, 2], [45, 8, 40, 6, "strokeMiter"], [45, 19, 40, 17], [45, 24, 40, 22, "undefined"], [45, 33, 40, 31], [45, 35, 40, 33], [46, 6, 41, 4, "paint"], [46, 11, 41, 9], [46, 12, 41, 10, "strokeMiter"], [46, 23, 41, 21], [46, 26, 41, 24, "strokeMiter"], [46, 37, 41, 35], [47, 4, 42, 2], [48, 4, 43, 2], [48, 8, 43, 6, "antiAlias"], [48, 17, 43, 15], [48, 22, 43, 20, "undefined"], [48, 31, 43, 29], [48, 33, 43, 31], [49, 6, 44, 4, "paint"], [49, 11, 44, 9], [49, 12, 44, 10, "antiAlias"], [49, 21, 44, 19], [49, 24, 44, 22, "antiAlias"], [49, 33, 44, 31], [50, 4, 45, 2], [51, 4, 46, 2], [51, 8, 46, 6, "dither"], [51, 14, 46, 12], [51, 19, 46, 17, "undefined"], [51, 28, 46, 26], [51, 30, 46, 28], [52, 6, 47, 4, "paint"], [52, 11, 47, 9], [52, 12, 47, 10, "dither"], [52, 18, 47, 16], [52, 21, 47, 19, "dither"], [52, 27, 47, 25], [53, 4, 48, 2], [54, 4, 49, 2], [54, 8, 49, 6, "paintRef"], [54, 16, 49, 14], [54, 21, 49, 19, "undefined"], [54, 30, 49, 28], [54, 32, 49, 30], [55, 6, 50, 4, "paint"], [55, 11, 50, 9], [55, 12, 50, 10, "paint"], [55, 17, 50, 15], [55, 20, 50, 18, "paintRef"], [55, 28, 50, 26], [56, 4, 51, 2], [57, 4, 52, 2], [57, 8, 52, 6, "opacity"], [57, 15, 52, 13], [57, 20, 52, 18, "undefined"], [57, 29, 52, 27], [57, 33, 52, 31, "color"], [57, 38, 52, 36], [57, 43, 52, 41, "undefined"], [57, 52, 52, 50], [57, 56, 52, 54, "strokeWidth"], [57, 67, 52, 65], [57, 72, 52, 70, "undefined"], [57, 81, 52, 79], [57, 85, 52, 83, "blendMode"], [57, 94, 52, 92], [57, 99, 52, 97, "undefined"], [57, 108, 52, 106], [57, 112, 52, 110, "style"], [57, 117, 52, 115], [57, 122, 52, 120, "undefined"], [57, 131, 52, 129], [57, 135, 52, 133, "<PERSON><PERSON><PERSON><PERSON>"], [57, 145, 52, 143], [57, 150, 52, 148, "undefined"], [57, 159, 52, 157], [57, 163, 52, 161, "strokeCap"], [57, 172, 52, 170], [57, 177, 52, 175, "undefined"], [57, 186, 52, 184], [57, 190, 52, 188, "strokeMiter"], [57, 201, 52, 199], [57, 206, 52, 204, "undefined"], [57, 215, 52, 213], [57, 219, 52, 217, "antiAlias"], [57, 228, 52, 226], [57, 233, 52, 231, "undefined"], [57, 242, 52, 240], [57, 246, 52, 244, "dither"], [57, 252, 52, 250], [57, 257, 52, 255, "undefined"], [57, 266, 52, 264], [57, 270, 52, 268, "paintRef"], [57, 278, 52, 276], [57, 283, 52, 281, "undefined"], [57, 292, 52, 290], [57, 294, 52, 292], [58, 6, 53, 4], [58, 13, 53, 11, "paint"], [58, 18, 53, 16], [59, 4, 54, 2], [60, 4, 55, 2], [60, 11, 55, 9], [60, 15, 55, 13], [61, 2, 56, 0], [61, 3, 56, 1], [62, 2, 56, 2, "exports"], [62, 9, 56, 2], [62, 10, 56, 2, "processPaint"], [62, 22, 56, 2], [62, 25, 56, 2, "processPaint"], [62, 37, 56, 2], [63, 2, 57, 0], [63, 8, 57, 6, "processCTM"], [63, 18, 57, 16], [63, 21, 57, 19, "processCTM"], [63, 22, 57, 20], [64, 4, 58, 2, "clip"], [64, 8, 58, 6], [65, 4, 59, 2, "invertClip"], [65, 14, 59, 12], [66, 4, 60, 2, "transform"], [66, 13, 60, 11], [67, 4, 61, 2, "origin"], [67, 10, 61, 8], [68, 4, 62, 2, "matrix"], [68, 10, 62, 8], [69, 4, 63, 2, "layer"], [70, 2, 64, 0], [70, 3, 64, 1], [70, 8, 64, 6], [71, 4, 65, 2], [71, 10, 65, 8, "ctm"], [71, 13, 65, 11], [71, 16, 65, 14], [71, 17, 65, 15], [71, 18, 65, 16], [72, 4, 66, 2], [72, 8, 66, 6, "clip"], [72, 12, 66, 10], [72, 14, 66, 12], [73, 6, 67, 4, "ctm"], [73, 9, 67, 7], [73, 10, 67, 8, "clip"], [73, 14, 67, 12], [73, 17, 67, 15, "clip"], [73, 21, 67, 19], [74, 4, 68, 2], [75, 4, 69, 2], [75, 8, 69, 6, "invertClip"], [75, 18, 69, 16], [75, 20, 69, 18], [76, 6, 70, 4, "ctm"], [76, 9, 70, 7], [76, 10, 70, 8, "invertClip"], [76, 20, 70, 18], [76, 23, 70, 21, "invertClip"], [76, 33, 70, 31], [77, 4, 71, 2], [78, 4, 72, 2], [78, 8, 72, 6, "transform"], [78, 17, 72, 15], [78, 19, 72, 17], [79, 6, 73, 4, "ctm"], [79, 9, 73, 7], [79, 10, 73, 8, "transform"], [79, 19, 73, 17], [79, 22, 73, 20, "transform"], [79, 31, 73, 29], [80, 4, 74, 2], [81, 4, 75, 2], [81, 8, 75, 6, "origin"], [81, 14, 75, 12], [81, 16, 75, 14], [82, 6, 76, 4, "ctm"], [82, 9, 76, 7], [82, 10, 76, 8, "origin"], [82, 16, 76, 14], [82, 19, 76, 17, "origin"], [82, 25, 76, 23], [83, 4, 77, 2], [84, 4, 78, 2], [84, 8, 78, 6, "matrix"], [84, 14, 78, 12], [84, 16, 78, 14], [85, 6, 79, 4, "ctm"], [85, 9, 79, 7], [85, 10, 79, 8, "matrix"], [85, 16, 79, 14], [85, 19, 79, 17, "matrix"], [85, 25, 79, 23], [86, 4, 80, 2], [87, 4, 81, 2], [87, 8, 81, 6, "layer"], [87, 13, 81, 11], [87, 15, 81, 13], [88, 6, 82, 4, "ctm"], [88, 9, 82, 7], [88, 10, 82, 8, "layer"], [88, 15, 82, 13], [88, 18, 82, 16, "layer"], [88, 23, 82, 21], [89, 4, 83, 2], [90, 4, 84, 2], [90, 8, 84, 6, "clip"], [90, 12, 84, 10], [90, 17, 84, 15, "undefined"], [90, 26, 84, 24], [90, 30, 84, 28, "invertClip"], [90, 40, 84, 38], [90, 45, 84, 43, "undefined"], [90, 54, 84, 52], [90, 58, 84, 56, "transform"], [90, 67, 84, 65], [90, 72, 84, 70, "undefined"], [90, 81, 84, 79], [90, 85, 84, 83, "origin"], [90, 91, 84, 89], [90, 96, 84, 94, "undefined"], [90, 105, 84, 103], [90, 109, 84, 107, "matrix"], [90, 115, 84, 113], [90, 120, 84, 118, "undefined"], [90, 129, 84, 127], [90, 133, 84, 131, "layer"], [90, 138, 84, 136], [90, 143, 84, 141, "undefined"], [90, 152, 84, 150], [90, 154, 84, 152], [91, 6, 85, 4], [91, 13, 85, 11, "ctm"], [91, 16, 85, 14], [92, 4, 86, 2], [93, 4, 87, 2], [93, 11, 87, 9], [93, 15, 87, 13], [94, 2, 88, 0], [94, 3, 88, 1], [95, 2, 89, 0], [95, 8, 89, 6, "pushColorFilters"], [95, 24, 89, 22], [95, 27, 89, 25, "pushColorFilters"], [95, 28, 89, 26, "recorder"], [95, 36, 89, 34], [95, 38, 89, 36, "colorFilters"], [95, 50, 89, 48], [95, 55, 89, 53], [96, 4, 90, 2, "colorFilters"], [96, 16, 90, 14], [96, 17, 90, 15, "for<PERSON>ach"], [96, 24, 90, 22], [96, 25, 90, 23, "colorFilter"], [96, 36, 90, 34], [96, 40, 90, 38], [97, 6, 91, 4], [97, 10, 91, 8, "colorFilter"], [97, 21, 91, 19], [97, 22, 91, 20, "children"], [97, 30, 91, 28], [97, 31, 91, 29, "length"], [97, 37, 91, 35], [97, 40, 91, 38], [97, 41, 91, 39], [97, 43, 91, 41], [98, 8, 92, 6, "pushColorFilters"], [98, 24, 92, 22], [98, 25, 92, 23, "recorder"], [98, 33, 92, 31], [98, 35, 92, 33, "colorFilter"], [98, 46, 92, 44], [98, 47, 92, 45, "children"], [98, 55, 92, 53], [98, 56, 92, 54], [99, 6, 93, 4], [100, 6, 94, 4, "recorder"], [100, 14, 94, 12], [100, 15, 94, 13, "pushColorFilter"], [100, 30, 94, 28], [100, 31, 94, 29, "colorFilter"], [100, 42, 94, 40], [100, 43, 94, 41, "type"], [100, 47, 94, 45], [100, 49, 94, 47, "colorFilter"], [100, 60, 94, 58], [100, 61, 94, 59, "props"], [100, 66, 94, 64], [100, 67, 94, 65], [101, 6, 95, 4], [101, 12, 95, 10, "needsComposition"], [101, 28, 95, 26], [101, 31, 95, 29, "colorFilter"], [101, 42, 95, 40], [101, 43, 95, 41, "type"], [101, 47, 95, 45], [101, 52, 95, 50, "NodeType"], [101, 67, 95, 58], [101, 68, 95, 59, "LerpColorFilter"], [101, 83, 95, 74], [101, 87, 95, 78, "colorFilter"], [101, 98, 95, 89], [101, 99, 95, 90, "children"], [101, 107, 95, 98], [101, 108, 95, 99, "length"], [101, 114, 95, 105], [101, 117, 95, 108], [101, 118, 95, 109], [102, 6, 96, 4], [102, 10, 96, 8, "needsComposition"], [102, 26, 96, 24], [102, 28, 96, 26], [103, 8, 97, 6, "recorder"], [103, 16, 97, 14], [103, 17, 97, 15, "composeColorFilter"], [103, 35, 97, 33], [103, 36, 97, 34], [103, 37, 97, 35], [104, 6, 98, 4], [105, 4, 99, 2], [105, 5, 99, 3], [105, 6, 99, 4], [106, 2, 100, 0], [106, 3, 100, 1], [107, 2, 101, 0], [107, 8, 101, 6, "pushPathEffects"], [107, 23, 101, 21], [107, 26, 101, 24, "pushPathEffects"], [107, 27, 101, 25, "recorder"], [107, 35, 101, 33], [107, 37, 101, 35, "pathEffects"], [107, 48, 101, 46], [107, 53, 101, 51], [108, 4, 102, 2, "pathEffects"], [108, 15, 102, 13], [108, 16, 102, 14, "for<PERSON>ach"], [108, 23, 102, 21], [108, 24, 102, 22, "pathEffect"], [108, 34, 102, 32], [108, 38, 102, 36], [109, 6, 103, 4], [109, 10, 103, 8, "pathEffect"], [109, 20, 103, 18], [109, 21, 103, 19, "children"], [109, 29, 103, 27], [109, 30, 103, 28, "length"], [109, 36, 103, 34], [109, 39, 103, 37], [109, 40, 103, 38], [109, 42, 103, 40], [110, 8, 104, 6, "pushPathEffects"], [110, 23, 104, 21], [110, 24, 104, 22, "recorder"], [110, 32, 104, 30], [110, 34, 104, 32, "pathEffect"], [110, 44, 104, 42], [110, 45, 104, 43, "children"], [110, 53, 104, 51], [110, 54, 104, 52], [111, 6, 105, 4], [112, 6, 106, 4, "recorder"], [112, 14, 106, 12], [112, 15, 106, 13, "pushPathEffect"], [112, 29, 106, 27], [112, 30, 106, 28, "pathEffect"], [112, 40, 106, 38], [112, 41, 106, 39, "type"], [112, 45, 106, 43], [112, 47, 106, 45, "pathEffect"], [112, 57, 106, 55], [112, 58, 106, 56, "props"], [112, 63, 106, 61], [112, 64, 106, 62], [113, 6, 107, 4], [113, 12, 107, 10, "needsComposition"], [113, 28, 107, 26], [113, 31, 107, 29, "pathEffect"], [113, 41, 107, 39], [113, 42, 107, 40, "type"], [113, 46, 107, 44], [113, 51, 107, 49, "NodeType"], [113, 66, 107, 57], [113, 67, 107, 58, "SumPathEffect"], [113, 80, 107, 71], [113, 84, 107, 75, "pathEffect"], [113, 94, 107, 85], [113, 95, 107, 86, "children"], [113, 103, 107, 94], [113, 104, 107, 95, "length"], [113, 110, 107, 101], [113, 113, 107, 104], [113, 114, 107, 105], [114, 6, 108, 4], [114, 10, 108, 8, "needsComposition"], [114, 26, 108, 24], [114, 28, 108, 26], [115, 8, 109, 6, "recorder"], [115, 16, 109, 14], [115, 17, 109, 15, "composePathEffect"], [115, 34, 109, 32], [115, 35, 109, 33], [115, 36, 109, 34], [116, 6, 110, 4], [117, 4, 111, 2], [117, 5, 111, 3], [117, 6, 111, 4], [118, 2, 112, 0], [118, 3, 112, 1], [119, 2, 113, 0], [119, 8, 113, 6, "pushImageFilters"], [119, 24, 113, 22], [119, 27, 113, 25, "pushImageFilters"], [119, 28, 113, 26, "recorder"], [119, 36, 113, 34], [119, 38, 113, 36, "imageFilters"], [119, 50, 113, 48], [119, 55, 113, 53], [120, 4, 114, 2, "imageFilters"], [120, 16, 114, 14], [120, 17, 114, 15, "for<PERSON>ach"], [120, 24, 114, 22], [120, 25, 114, 23, "imageFilter"], [120, 36, 114, 34], [120, 40, 114, 38], [121, 6, 115, 4], [121, 10, 115, 8, "imageFilter"], [121, 21, 115, 19], [121, 22, 115, 20, "children"], [121, 30, 115, 28], [121, 31, 115, 29, "length"], [121, 37, 115, 35], [121, 40, 115, 38], [121, 41, 115, 39], [121, 43, 115, 41], [122, 8, 116, 6, "pushImageFilters"], [122, 24, 116, 22], [122, 25, 116, 23, "recorder"], [122, 33, 116, 31], [122, 35, 116, 33, "imageFilter"], [122, 46, 116, 44], [122, 47, 116, 45, "children"], [122, 55, 116, 53], [122, 56, 116, 54], [123, 6, 117, 4], [124, 6, 118, 4], [124, 10, 118, 8], [124, 14, 118, 8, "isImageFilter"], [124, 33, 118, 21], [124, 35, 118, 22, "imageFilter"], [124, 46, 118, 33], [124, 47, 118, 34, "type"], [124, 51, 118, 38], [124, 52, 118, 39], [124, 54, 118, 41], [125, 8, 119, 6, "recorder"], [125, 16, 119, 14], [125, 17, 119, 15, "pushImageFilter"], [125, 32, 119, 30], [125, 33, 119, 31, "imageFilter"], [125, 44, 119, 42], [125, 45, 119, 43, "type"], [125, 49, 119, 47], [125, 51, 119, 49, "imageFilter"], [125, 62, 119, 60], [125, 63, 119, 61, "props"], [125, 68, 119, 66], [125, 69, 119, 67], [126, 6, 120, 4], [126, 7, 120, 5], [126, 13, 120, 11], [126, 17, 120, 15], [126, 21, 120, 15, "<PERSON><PERSON><PERSON><PERSON>"], [126, 35, 120, 23], [126, 37, 120, 24, "imageFilter"], [126, 48, 120, 35], [126, 49, 120, 36, "type"], [126, 53, 120, 40], [126, 54, 120, 41], [126, 56, 120, 43], [127, 8, 121, 6, "recorder"], [127, 16, 121, 14], [127, 17, 121, 15, "push<PERSON><PERSON>er"], [127, 27, 121, 25], [127, 28, 121, 26, "imageFilter"], [127, 39, 121, 37], [127, 40, 121, 38, "type"], [127, 44, 121, 42], [127, 46, 121, 44, "imageFilter"], [127, 57, 121, 55], [127, 58, 121, 56, "props"], [127, 63, 121, 61], [127, 64, 121, 62], [128, 6, 122, 4], [129, 6, 123, 4], [129, 12, 123, 10, "needsComposition"], [129, 28, 123, 26], [129, 31, 123, 29, "imageFilter"], [129, 42, 123, 40], [129, 43, 123, 41, "type"], [129, 47, 123, 45], [129, 52, 123, 50, "NodeType"], [129, 67, 123, 58], [129, 68, 123, 59, "BlendImageFilter"], [129, 84, 123, 75], [129, 88, 123, 79, "imageFilter"], [129, 99, 123, 90], [129, 100, 123, 91, "children"], [129, 108, 123, 99], [129, 109, 123, 100, "length"], [129, 115, 123, 106], [129, 118, 123, 109], [129, 119, 123, 110], [130, 6, 124, 4], [130, 10, 124, 8, "needsComposition"], [130, 26, 124, 24], [130, 28, 124, 26], [131, 8, 125, 6, "recorder"], [131, 16, 125, 14], [131, 17, 125, 15, "composeImageFilter"], [131, 35, 125, 33], [131, 36, 125, 34], [131, 37, 125, 35], [132, 6, 126, 4], [133, 4, 127, 2], [133, 5, 127, 3], [133, 6, 127, 4], [134, 2, 128, 0], [134, 3, 128, 1], [135, 2, 129, 0], [135, 8, 129, 6, "pushShaders"], [135, 19, 129, 17], [135, 22, 129, 20, "pushShaders"], [135, 23, 129, 21, "recorder"], [135, 31, 129, 29], [135, 33, 129, 31, "shaders"], [135, 40, 129, 38], [135, 45, 129, 43], [136, 4, 130, 2, "shaders"], [136, 11, 130, 9], [136, 12, 130, 10, "for<PERSON>ach"], [136, 19, 130, 17], [136, 20, 130, 18, "shader"], [136, 26, 130, 24], [136, 30, 130, 28], [137, 6, 131, 4], [137, 10, 131, 8, "shader"], [137, 16, 131, 14], [137, 17, 131, 15, "children"], [137, 25, 131, 23], [137, 26, 131, 24, "length"], [137, 32, 131, 30], [137, 35, 131, 33], [137, 36, 131, 34], [137, 38, 131, 36], [138, 8, 132, 6, "pushShaders"], [138, 19, 132, 17], [138, 20, 132, 18, "recorder"], [138, 28, 132, 26], [138, 30, 132, 28, "shader"], [138, 36, 132, 34], [138, 37, 132, 35, "children"], [138, 45, 132, 43], [138, 46, 132, 44], [139, 6, 133, 4], [140, 6, 134, 4, "recorder"], [140, 14, 134, 12], [140, 15, 134, 13, "push<PERSON><PERSON>er"], [140, 25, 134, 23], [140, 26, 134, 24, "shader"], [140, 32, 134, 30], [140, 33, 134, 31, "type"], [140, 37, 134, 35], [140, 39, 134, 37, "shader"], [140, 45, 134, 43], [140, 46, 134, 44, "props"], [140, 51, 134, 49], [140, 52, 134, 50], [141, 4, 135, 2], [141, 5, 135, 3], [141, 6, 135, 4], [142, 2, 136, 0], [142, 3, 136, 1], [143, 2, 137, 0], [143, 8, 137, 6, "pushMaskFilters"], [143, 23, 137, 21], [143, 26, 137, 24, "pushMaskFilters"], [143, 27, 137, 25, "recorder"], [143, 35, 137, 33], [143, 37, 137, 35, "maskFilters"], [143, 48, 137, 46], [143, 53, 137, 51], [144, 4, 138, 2], [144, 8, 138, 6, "maskFilters"], [144, 19, 138, 17], [144, 20, 138, 18, "length"], [144, 26, 138, 24], [144, 29, 138, 27], [144, 30, 138, 28], [144, 32, 138, 30], [145, 6, 139, 4, "recorder"], [145, 14, 139, 12], [145, 15, 139, 13, "pushBlurMaskFilter"], [145, 33, 139, 31], [145, 34, 139, 32, "maskFilters"], [145, 45, 139, 43], [145, 46, 139, 44, "maskFilters"], [145, 57, 139, 55], [145, 58, 139, 56, "length"], [145, 64, 139, 62], [145, 67, 139, 65], [145, 68, 139, 66], [145, 69, 139, 67], [145, 70, 139, 68, "props"], [145, 75, 139, 73], [145, 76, 139, 74], [146, 4, 140, 2], [147, 2, 141, 0], [147, 3, 141, 1], [148, 2, 142, 0], [148, 8, 142, 6, "pushPaints"], [148, 18, 142, 16], [148, 21, 142, 19, "pushPaints"], [148, 22, 142, 20, "recorder"], [148, 30, 142, 28], [148, 32, 142, 30, "paints"], [148, 38, 142, 36], [148, 43, 142, 41], [149, 4, 143, 2, "paints"], [149, 10, 143, 8], [149, 11, 143, 9, "for<PERSON>ach"], [149, 18, 143, 16], [149, 19, 143, 17, "paint"], [149, 24, 143, 22], [149, 28, 143, 26], [150, 6, 144, 4, "recorder"], [150, 14, 144, 12], [150, 15, 144, 13, "save<PERSON><PERSON>t"], [150, 24, 144, 22], [150, 25, 144, 23, "paint"], [150, 30, 144, 28], [150, 31, 144, 29, "props"], [150, 36, 144, 34], [150, 37, 144, 35], [151, 6, 145, 4], [151, 12, 145, 10], [152, 8, 146, 6, "colorFilters"], [152, 20, 146, 18], [153, 8, 147, 6, "maskFilters"], [153, 19, 147, 17], [154, 8, 148, 6, "shaders"], [154, 15, 148, 13], [155, 8, 149, 6, "imageFilters"], [155, 20, 149, 18], [156, 8, 150, 6, "pathEffects"], [157, 6, 151, 4], [157, 7, 151, 5], [157, 10, 151, 8], [157, 14, 151, 8, "sortNodeChildren"], [157, 36, 151, 24], [157, 38, 151, 25, "paint"], [157, 43, 151, 30], [157, 44, 151, 31], [158, 6, 152, 4, "pushColorFilters"], [158, 22, 152, 20], [158, 23, 152, 21, "recorder"], [158, 31, 152, 29], [158, 33, 152, 31, "colorFilters"], [158, 45, 152, 43], [158, 46, 152, 44], [159, 6, 153, 4, "pushImageFilters"], [159, 22, 153, 20], [159, 23, 153, 21, "recorder"], [159, 31, 153, 29], [159, 33, 153, 31, "imageFilters"], [159, 45, 153, 43], [159, 46, 153, 44], [160, 6, 154, 4, "pushMaskFilters"], [160, 21, 154, 19], [160, 22, 154, 20, "recorder"], [160, 30, 154, 28], [160, 32, 154, 30, "maskFilters"], [160, 43, 154, 41], [160, 44, 154, 42], [161, 6, 155, 4, "pushShaders"], [161, 17, 155, 15], [161, 18, 155, 16, "recorder"], [161, 26, 155, 24], [161, 28, 155, 26, "shaders"], [161, 35, 155, 33], [161, 36, 155, 34], [162, 6, 156, 4, "pushPathEffects"], [162, 21, 156, 19], [162, 22, 156, 20, "recorder"], [162, 30, 156, 28], [162, 32, 156, 30, "pathEffects"], [162, 43, 156, 41], [162, 44, 156, 42], [163, 6, 157, 4, "recorder"], [163, 14, 157, 12], [163, 15, 157, 13, "restorePaintDeclaration"], [163, 38, 157, 36], [163, 39, 157, 37], [163, 40, 157, 38], [164, 4, 158, 2], [164, 5, 158, 3], [164, 6, 158, 4], [165, 2, 159, 0], [165, 3, 159, 1], [166, 2, 160, 0], [166, 8, 160, 6, "visitNode"], [166, 17, 160, 15], [166, 20, 160, 18, "visitNode"], [166, 21, 160, 19, "recorder"], [166, 29, 160, 27], [166, 31, 160, 29, "node"], [166, 35, 160, 33], [166, 40, 160, 38], [167, 4, 161, 2], [167, 8, 161, 6, "node"], [167, 12, 161, 10], [167, 13, 161, 11, "type"], [167, 17, 161, 15], [167, 22, 161, 20, "NodeType"], [167, 37, 161, 28], [167, 38, 161, 29, "Group"], [167, 43, 161, 34], [167, 45, 161, 36], [168, 6, 162, 4, "recorder"], [168, 14, 162, 12], [168, 15, 162, 13, "saveGroup"], [168, 24, 162, 22], [168, 25, 162, 23], [168, 26, 162, 24], [169, 4, 163, 2], [170, 4, 164, 2], [170, 10, 164, 8], [171, 6, 165, 4, "props"], [172, 4, 166, 2], [172, 5, 166, 3], [172, 8, 166, 6, "node"], [172, 12, 166, 10], [173, 4, 167, 2], [173, 10, 167, 8], [174, 6, 168, 4, "colorFilters"], [174, 18, 168, 16], [175, 6, 169, 4, "maskFilters"], [175, 17, 169, 15], [176, 6, 170, 4, "drawings"], [176, 14, 170, 12], [177, 6, 171, 4, "shaders"], [177, 13, 171, 11], [178, 6, 172, 4, "imageFilters"], [178, 18, 172, 16], [179, 6, 173, 4, "pathEffects"], [179, 17, 173, 15], [180, 6, 174, 4, "paints"], [181, 4, 175, 2], [181, 5, 175, 3], [181, 8, 175, 6], [181, 12, 175, 6, "sortNodeChildren"], [181, 34, 175, 22], [181, 36, 175, 23, "node"], [181, 40, 175, 27], [181, 41, 175, 28], [182, 4, 176, 2], [182, 10, 176, 8, "paint"], [182, 15, 176, 13], [182, 18, 176, 16, "processPaint"], [182, 30, 176, 28], [182, 31, 176, 29, "props"], [182, 36, 176, 34], [182, 37, 176, 35], [183, 4, 177, 2], [183, 10, 177, 8, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [183, 25, 177, 23], [183, 28, 177, 26, "paint"], [183, 33, 177, 31], [183, 37, 177, 35, "colorFilters"], [183, 49, 177, 47], [183, 50, 177, 48, "length"], [183, 56, 177, 54], [183, 59, 177, 57], [183, 60, 177, 58], [183, 64, 177, 62, "maskFilters"], [183, 75, 177, 73], [183, 76, 177, 74, "length"], [183, 82, 177, 80], [183, 85, 177, 83], [183, 86, 177, 84], [183, 90, 177, 88, "imageFilters"], [183, 102, 177, 100], [183, 103, 177, 101, "length"], [183, 109, 177, 107], [183, 112, 177, 110], [183, 113, 177, 111], [183, 117, 177, 115, "pathEffects"], [183, 128, 177, 126], [183, 129, 177, 127, "length"], [183, 135, 177, 133], [183, 138, 177, 136], [183, 139, 177, 137], [183, 143, 177, 141, "shaders"], [183, 150, 177, 148], [183, 151, 177, 149, "length"], [183, 157, 177, 155], [183, 160, 177, 158], [183, 161, 177, 159], [184, 4, 178, 2], [184, 8, 178, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [184, 23, 178, 21], [184, 25, 178, 23], [185, 6, 179, 4, "recorder"], [185, 14, 179, 12], [185, 15, 179, 13, "save<PERSON><PERSON>t"], [185, 24, 179, 22], [185, 25, 179, 23, "paint"], [185, 30, 179, 28], [185, 35, 179, 33], [185, 39, 179, 37], [185, 43, 179, 41, "paint"], [185, 48, 179, 46], [185, 53, 179, 51], [185, 58, 179, 56], [185, 59, 179, 57], [185, 62, 179, 60, "paint"], [185, 67, 179, 65], [185, 70, 179, 68], [185, 71, 179, 69], [185, 72, 179, 70], [185, 73, 179, 71], [186, 6, 180, 4, "pushColorFilters"], [186, 22, 180, 20], [186, 23, 180, 21, "recorder"], [186, 31, 180, 29], [186, 33, 180, 31, "colorFilters"], [186, 45, 180, 43], [186, 46, 180, 44], [187, 6, 181, 4, "pushImageFilters"], [187, 22, 181, 20], [187, 23, 181, 21, "recorder"], [187, 31, 181, 29], [187, 33, 181, 31, "imageFilters"], [187, 45, 181, 43], [187, 46, 181, 44], [188, 6, 182, 4, "pushMaskFilters"], [188, 21, 182, 19], [188, 22, 182, 20, "recorder"], [188, 30, 182, 28], [188, 32, 182, 30, "maskFilters"], [188, 43, 182, 41], [188, 44, 182, 42], [189, 6, 183, 4, "pushShaders"], [189, 17, 183, 15], [189, 18, 183, 16, "recorder"], [189, 26, 183, 24], [189, 28, 183, 26, "shaders"], [189, 35, 183, 33], [189, 36, 183, 34], [190, 6, 184, 4, "pushPathEffects"], [190, 21, 184, 19], [190, 22, 184, 20, "recorder"], [190, 30, 184, 28], [190, 32, 184, 30, "pathEffects"], [190, 43, 184, 41], [190, 44, 184, 42], [191, 6, 185, 4], [192, 6, 186, 4], [192, 10, 186, 8, "node"], [192, 14, 186, 12], [192, 15, 186, 13, "type"], [192, 19, 186, 17], [192, 24, 186, 22, "NodeType"], [192, 39, 186, 30], [192, 40, 186, 31, "<PERSON>drop<PERSON><PERSON><PERSON>"], [192, 54, 186, 45], [192, 56, 186, 47], [193, 8, 187, 6, "recorder"], [193, 16, 187, 14], [193, 17, 187, 15, "saveBackdropFilter"], [193, 35, 187, 33], [193, 36, 187, 34], [193, 37, 187, 35], [194, 6, 188, 4], [194, 7, 188, 5], [194, 13, 188, 11], [195, 8, 189, 6, "recorder"], [195, 16, 189, 14], [195, 17, 189, 15, "materialize<PERSON><PERSON><PERSON>"], [195, 33, 189, 31], [195, 34, 189, 32], [195, 35, 189, 33], [196, 6, 190, 4], [197, 4, 191, 2], [198, 4, 192, 2, "pushPaints"], [198, 14, 192, 12], [198, 15, 192, 13, "recorder"], [198, 23, 192, 21], [198, 25, 192, 23, "paints"], [198, 31, 192, 29], [198, 32, 192, 30], [199, 4, 193, 2], [199, 8, 193, 6, "node"], [199, 12, 193, 10], [199, 13, 193, 11, "type"], [199, 17, 193, 15], [199, 22, 193, 20, "NodeType"], [199, 37, 193, 28], [199, 38, 193, 29, "Layer"], [199, 43, 193, 34], [199, 45, 193, 36], [200, 6, 194, 4, "recorder"], [200, 14, 194, 12], [200, 15, 194, 13, "save<PERSON><PERSON><PERSON>"], [200, 24, 194, 22], [200, 25, 194, 23], [200, 26, 194, 24], [201, 4, 195, 2], [202, 4, 196, 2], [202, 10, 196, 8, "ctm"], [202, 13, 196, 11], [202, 16, 196, 14, "processCTM"], [202, 26, 196, 24], [202, 27, 196, 25, "props"], [202, 32, 196, 30], [202, 33, 196, 31], [203, 4, 197, 2], [203, 10, 197, 8, "shouldRest<PERSON>"], [203, 23, 197, 21], [203, 26, 197, 24], [203, 27, 197, 25], [203, 28, 197, 26, "ctm"], [203, 31, 197, 29], [203, 35, 197, 33, "node"], [203, 39, 197, 37], [203, 40, 197, 38, "type"], [203, 44, 197, 42], [203, 49, 197, 47, "NodeType"], [203, 64, 197, 55], [203, 65, 197, 56, "Layer"], [203, 70, 197, 61], [204, 4, 198, 2], [204, 8, 198, 6, "ctm"], [204, 11, 198, 9], [204, 13, 198, 11], [205, 6, 199, 4, "recorder"], [205, 14, 199, 12], [205, 15, 199, 13, "saveCTM"], [205, 22, 199, 20], [205, 23, 199, 21, "ctm"], [205, 26, 199, 24], [205, 27, 199, 25], [206, 4, 200, 2], [207, 4, 201, 2], [207, 12, 201, 10, "node"], [207, 16, 201, 14], [207, 17, 201, 15, "type"], [207, 21, 201, 19], [208, 6, 202, 4], [208, 11, 202, 9, "NodeType"], [208, 26, 202, 17], [208, 27, 202, 18, "Box"], [208, 30, 202, 21], [209, 8, 203, 6], [209, 14, 203, 12, "shadows"], [209, 21, 203, 19], [209, 24, 203, 22, "node"], [209, 28, 203, 26], [209, 29, 203, 27, "children"], [209, 37, 203, 35], [209, 38, 203, 36, "filter"], [209, 44, 203, 42], [209, 45, 203, 43, "n"], [209, 46, 203, 44], [209, 50, 203, 48, "n"], [209, 51, 203, 49], [209, 52, 203, 50, "type"], [209, 56, 203, 54], [209, 61, 203, 59, "NodeType"], [209, 76, 203, 67], [209, 77, 203, 68, "BoxShadow"], [209, 86, 203, 77], [210, 8, 204, 6], [211, 8, 204, 6], [211, 9, 205, 7, "map"], [211, 12, 205, 10], [211, 13, 205, 11], [211, 14, 205, 12], [212, 10, 206, 8, "props"], [213, 8, 207, 6], [213, 9, 207, 7], [213, 15, 207, 13], [214, 10, 208, 8, "props"], [215, 8, 209, 6], [215, 9, 209, 7], [215, 10, 209, 8], [215, 11, 209, 9], [216, 8, 210, 6, "recorder"], [216, 16, 210, 14], [216, 17, 210, 15, "drawBox"], [216, 24, 210, 22], [216, 25, 210, 23, "props"], [216, 30, 210, 28], [216, 32, 210, 30, "shadows"], [216, 39, 210, 37], [216, 40, 210, 38], [217, 8, 211, 6], [218, 6, 212, 4], [218, 11, 212, 9, "NodeType"], [218, 26, 212, 17], [218, 27, 212, 18, "Fill"], [218, 31, 212, 22], [219, 8, 213, 6, "recorder"], [219, 16, 213, 14], [219, 17, 213, 15, "<PERSON><PERSON><PERSON><PERSON>"], [219, 26, 213, 24], [219, 27, 213, 25], [219, 28, 213, 26], [220, 8, 214, 6], [221, 6, 215, 4], [221, 11, 215, 9, "NodeType"], [221, 26, 215, 17], [221, 27, 215, 18, "Image"], [221, 32, 215, 23], [222, 8, 216, 6, "recorder"], [222, 16, 216, 14], [222, 17, 216, 15, "drawImage"], [222, 26, 216, 24], [222, 27, 216, 25, "props"], [222, 32, 216, 30], [222, 33, 216, 31], [223, 8, 217, 6], [224, 6, 218, 4], [224, 11, 218, 9, "NodeType"], [224, 26, 218, 17], [224, 27, 218, 18, "Circle"], [224, 33, 218, 24], [225, 8, 219, 6, "recorder"], [225, 16, 219, 14], [225, 17, 219, 15, "drawCircle"], [225, 27, 219, 25], [225, 28, 219, 26, "props"], [225, 33, 219, 31], [225, 34, 219, 32], [226, 8, 220, 6], [227, 6, 221, 4], [227, 11, 221, 9, "NodeType"], [227, 26, 221, 17], [227, 27, 221, 18, "Points"], [227, 33, 221, 24], [228, 8, 222, 6, "recorder"], [228, 16, 222, 14], [228, 17, 222, 15, "drawPoints"], [228, 27, 222, 25], [228, 28, 222, 26, "props"], [228, 33, 222, 31], [228, 34, 222, 32], [229, 8, 223, 6], [230, 6, 224, 4], [230, 11, 224, 9, "NodeType"], [230, 26, 224, 17], [230, 27, 224, 18, "Path"], [230, 31, 224, 22], [231, 8, 225, 6, "recorder"], [231, 16, 225, 14], [231, 17, 225, 15, "drawPath"], [231, 25, 225, 23], [231, 26, 225, 24, "props"], [231, 31, 225, 29], [231, 32, 225, 30], [232, 8, 226, 6], [233, 6, 227, 4], [233, 11, 227, 9, "NodeType"], [233, 26, 227, 17], [233, 27, 227, 18, "Rect"], [233, 31, 227, 22], [234, 8, 228, 6, "recorder"], [234, 16, 228, 14], [234, 17, 228, 15, "drawRect"], [234, 25, 228, 23], [234, 26, 228, 24, "props"], [234, 31, 228, 29], [234, 32, 228, 30], [235, 8, 229, 6], [236, 6, 230, 4], [236, 11, 230, 9, "NodeType"], [236, 26, 230, 17], [236, 27, 230, 18, "RRect"], [236, 32, 230, 23], [237, 8, 231, 6, "recorder"], [237, 16, 231, 14], [237, 17, 231, 15, "drawRRect"], [237, 26, 231, 24], [237, 27, 231, 25, "props"], [237, 32, 231, 30], [237, 33, 231, 31], [238, 8, 232, 6], [239, 6, 233, 4], [239, 11, 233, 9, "NodeType"], [239, 26, 233, 17], [239, 27, 233, 18, "Oval"], [239, 31, 233, 22], [240, 8, 234, 6, "recorder"], [240, 16, 234, 14], [240, 17, 234, 15, "drawOval"], [240, 25, 234, 23], [240, 26, 234, 24, "props"], [240, 31, 234, 29], [240, 32, 234, 30], [241, 8, 235, 6], [242, 6, 236, 4], [242, 11, 236, 9, "NodeType"], [242, 26, 236, 17], [242, 27, 236, 18, "Line"], [242, 31, 236, 22], [243, 8, 237, 6, "recorder"], [243, 16, 237, 14], [243, 17, 237, 15, "drawLine"], [243, 25, 237, 23], [243, 26, 237, 24, "props"], [243, 31, 237, 29], [243, 32, 237, 30], [244, 8, 238, 6], [245, 6, 239, 4], [245, 11, 239, 9, "NodeType"], [245, 26, 239, 17], [245, 27, 239, 18, "Patch"], [245, 32, 239, 23], [246, 8, 240, 6, "recorder"], [246, 16, 240, 14], [246, 17, 240, 15, "drawPatch"], [246, 26, 240, 24], [246, 27, 240, 25, "props"], [246, 32, 240, 30], [246, 33, 240, 31], [247, 8, 241, 6], [248, 6, 242, 4], [248, 11, 242, 9, "NodeType"], [248, 26, 242, 17], [248, 27, 242, 18, "Vertices"], [248, 35, 242, 26], [249, 8, 243, 6, "recorder"], [249, 16, 243, 14], [249, 17, 243, 15, "drawVertices"], [249, 29, 243, 27], [249, 30, 243, 28, "props"], [249, 35, 243, 33], [249, 36, 243, 34], [250, 8, 244, 6], [251, 6, 245, 4], [251, 11, 245, 9, "NodeType"], [251, 26, 245, 17], [251, 27, 245, 18, "DiffRect"], [251, 35, 245, 26], [252, 8, 246, 6, "recorder"], [252, 16, 246, 14], [252, 17, 246, 15, "drawDiffRect"], [252, 29, 246, 27], [252, 30, 246, 28, "props"], [252, 35, 246, 33], [252, 36, 246, 34], [253, 8, 247, 6], [254, 6, 248, 4], [254, 11, 248, 9, "NodeType"], [254, 26, 248, 17], [254, 27, 248, 18, "Text"], [254, 31, 248, 22], [255, 8, 249, 6, "recorder"], [255, 16, 249, 14], [255, 17, 249, 15, "drawText"], [255, 25, 249, 23], [255, 26, 249, 24, "props"], [255, 31, 249, 29], [255, 32, 249, 30], [256, 8, 250, 6], [257, 6, 251, 4], [257, 11, 251, 9, "NodeType"], [257, 26, 251, 17], [257, 27, 251, 18, "TextPath"], [257, 35, 251, 26], [258, 8, 252, 6, "recorder"], [258, 16, 252, 14], [258, 17, 252, 15, "drawTextPath"], [258, 29, 252, 27], [258, 30, 252, 28, "props"], [258, 35, 252, 33], [258, 36, 252, 34], [259, 8, 253, 6], [260, 6, 254, 4], [260, 11, 254, 9, "NodeType"], [260, 26, 254, 17], [260, 27, 254, 18, "TextBlob"], [260, 35, 254, 26], [261, 8, 255, 6, "recorder"], [261, 16, 255, 14], [261, 17, 255, 15, "drawTextBlob"], [261, 29, 255, 27], [261, 30, 255, 28, "props"], [261, 35, 255, 33], [261, 36, 255, 34], [262, 8, 256, 6], [263, 6, 257, 4], [263, 11, 257, 9, "NodeType"], [263, 26, 257, 17], [263, 27, 257, 18, "Glyphs"], [263, 33, 257, 24], [264, 8, 258, 6, "recorder"], [264, 16, 258, 14], [264, 17, 258, 15, "drawGlyphs"], [264, 27, 258, 25], [264, 28, 258, 26, "props"], [264, 33, 258, 31], [264, 34, 258, 32], [265, 8, 259, 6], [266, 6, 260, 4], [266, 11, 260, 9, "NodeType"], [266, 26, 260, 17], [266, 27, 260, 18, "Picture"], [266, 34, 260, 25], [267, 8, 261, 6, "recorder"], [267, 16, 261, 14], [267, 17, 261, 15, "drawPicture"], [267, 28, 261, 26], [267, 29, 261, 27, "props"], [267, 34, 261, 32], [267, 35, 261, 33], [268, 8, 262, 6], [269, 6, 263, 4], [269, 11, 263, 9, "NodeType"], [269, 26, 263, 17], [269, 27, 263, 18, "ImageSVG"], [269, 35, 263, 26], [270, 8, 264, 6, "recorder"], [270, 16, 264, 14], [270, 17, 264, 15, "drawImageSVG"], [270, 29, 264, 27], [270, 30, 264, 28, "props"], [270, 35, 264, 33], [270, 36, 264, 34], [271, 8, 265, 6], [272, 6, 266, 4], [272, 11, 266, 9, "NodeType"], [272, 26, 266, 17], [272, 27, 266, 18, "Paragraph"], [272, 36, 266, 27], [273, 8, 267, 6, "recorder"], [273, 16, 267, 14], [273, 17, 267, 15, "drawParagraph"], [273, 30, 267, 28], [273, 31, 267, 29, "props"], [273, 36, 267, 34], [273, 37, 267, 35], [274, 8, 268, 6], [275, 6, 269, 4], [275, 11, 269, 9, "NodeType"], [275, 26, 269, 17], [275, 27, 269, 18, "Atlas"], [275, 32, 269, 23], [276, 8, 270, 6, "recorder"], [276, 16, 270, 14], [276, 17, 270, 15, "drawAtlas"], [276, 26, 270, 24], [276, 27, 270, 25, "props"], [276, 32, 270, 30], [276, 33, 270, 31], [277, 8, 271, 6], [278, 4, 272, 2], [279, 4, 273, 2, "drawings"], [279, 12, 273, 10], [279, 13, 273, 11, "for<PERSON>ach"], [279, 20, 273, 18], [279, 21, 273, 19, "drawing"], [279, 28, 273, 26], [279, 32, 273, 30], [280, 6, 274, 4, "visitNode"], [280, 15, 274, 13], [280, 16, 274, 14, "recorder"], [280, 24, 274, 22], [280, 26, 274, 24, "drawing"], [280, 33, 274, 31], [280, 34, 274, 32], [281, 4, 275, 2], [281, 5, 275, 3], [281, 6, 275, 4], [282, 4, 276, 2], [282, 8, 276, 6, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [282, 23, 276, 21], [282, 25, 276, 23], [283, 6, 277, 4, "recorder"], [283, 14, 277, 12], [283, 15, 277, 13, "<PERSON><PERSON><PERSON><PERSON>"], [283, 27, 277, 25], [283, 28, 277, 26], [283, 29, 277, 27], [284, 4, 278, 2], [285, 4, 279, 2], [285, 8, 279, 6, "shouldRest<PERSON>"], [285, 21, 279, 19], [285, 23, 279, 21], [286, 6, 280, 4, "recorder"], [286, 14, 280, 12], [286, 15, 280, 13, "restoreCTM"], [286, 25, 280, 23], [286, 26, 280, 24], [286, 27, 280, 25], [287, 4, 281, 2], [288, 4, 282, 2], [288, 8, 282, 6, "node"], [288, 12, 282, 10], [288, 13, 282, 11, "type"], [288, 17, 282, 15], [288, 22, 282, 20, "NodeType"], [288, 37, 282, 28], [288, 38, 282, 29, "Group"], [288, 43, 282, 34], [288, 45, 282, 36], [289, 6, 283, 4, "recorder"], [289, 14, 283, 12], [289, 15, 283, 13, "restoreGroup"], [289, 27, 283, 25], [289, 28, 283, 26], [289, 29, 283, 27], [290, 4, 284, 2], [291, 2, 285, 0], [291, 3, 285, 1], [292, 2, 286, 7], [292, 8, 286, 13, "visit"], [292, 13, 286, 18], [292, 16, 286, 21, "visit"], [292, 17, 286, 22, "recorder"], [292, 25, 286, 30], [292, 27, 286, 32, "root"], [292, 31, 286, 36], [292, 36, 286, 41], [293, 4, 287, 2, "root"], [293, 8, 287, 6], [293, 9, 287, 7, "for<PERSON>ach"], [293, 16, 287, 14], [293, 17, 287, 15, "node"], [293, 21, 287, 19], [293, 25, 287, 23], [294, 6, 288, 4, "visitNode"], [294, 15, 288, 13], [294, 16, 288, 14, "recorder"], [294, 24, 288, 22], [294, 26, 288, 24, "node"], [294, 30, 288, 28], [294, 31, 288, 29], [295, 4, 289, 2], [295, 5, 289, 3], [295, 6, 289, 4], [296, 2, 290, 0], [296, 3, 290, 1], [297, 2, 290, 2, "exports"], [297, 9, 290, 2], [297, 10, 290, 2, "visit"], [297, 15, 290, 2], [297, 18, 290, 2, "visit"], [297, 23, 290, 2], [298, 0, 290, 2], [298, 3]], "functionMap": {"names": ["<global>", "processPaint", "processCTM", "pushColorFilters", "colorFilters.forEach$argument_0", "pushPathEffects", "pathEffects.forEach$argument_0", "pushImageFilters", "imageFilters.forEach$argument_0", "pushShaders", "shaders.forEach$argument_0", "pushMaskFilters", "pushPaints", "paints.forEach$argument_0", "visitNode", "node.children.filter$argument_0", "node.children.filter.map$argument_0", "drawings.forEach$argument_0", "visit", "root.forEach$argument_0"], "mappings": "AAA;4BCI;CDmD;mBEC;CF+B;yBGC;uBCC;GDS;CHC;wBKC;sBCC;GDS;CLC;yBOC;uBCC;GDa;CPC;oBSC;kBCC;GDK;CTC;wBWC;CXI;mBYC;iBCC;GDe;CZC;kBcC;2CC2C,kCD;WEE;QFI;mBGgE;GHE;CdU;qBkBC;eCC;GDE;ClBC"}}, "type": "js/module"}]}