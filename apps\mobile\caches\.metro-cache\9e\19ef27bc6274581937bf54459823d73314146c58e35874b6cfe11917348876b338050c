{"dependencies": [{"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 56, "index": 56}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "./Camera.types", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 57}, "end": {"line": 2, "column": 51, "index": 108}}], "key": "YcvES77PsJEiJoknRXvymJVPk40=", "exportNames": ["*"]}}, {"name": "./web/WebUserMediaManager", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 3, "column": 0, "index": 109}, "end": {"line": 3, "column": 118, "index": 227}}], "key": "akJjvp4N1IdGH6uLgZRZFAIpjyQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _expoModulesCore = require(_dependencyMap[0], \"expo-modules-core\");\n  var _Camera = require(_dependencyMap[1], \"./Camera.types\");\n  var _WebUserMediaManager = require(_dependencyMap[2], \"./web/WebUserMediaManager\");\n  function getUserMedia(constraints) {\n    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {\n      return navigator.mediaDevices.getUserMedia(constraints);\n    }\n    // Some browsers partially implement mediaDevices. We can't just assign an object\n    // with getUserMedia as it would overwrite existing properties.\n    // Here, we will just add the getUserMedia property if it's missing.\n    // First get ahold of the legacy getUserMedia, if present\n    const getUserMedia =\n    // TODO: this method is deprecated, migrate to https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getUserMedia\n    navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia || function () {\n      const error = new Error('Permission unimplemented');\n      error.code = 0;\n      error.name = 'NotAllowedError';\n      throw error;\n    };\n    return new Promise((resolve, reject) => {\n      // TODO(@kitten): The types indicates that this is incorrect.\n      // Please check whether this is correct!\n      // @ts-expect-error: The `successCallback` doesn't match a `resolve` function\n      getUserMedia.call(navigator, constraints, resolve, reject);\n    });\n  }\n  function handleGetUserMediaError({\n    message\n  }) {\n    // name: NotAllowedError\n    // code: 0\n    if (message === 'Permission dismissed') {\n      return {\n        status: _Camera.PermissionStatus.UNDETERMINED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: false\n      };\n    } else {\n      // TODO: Bacon: [OSX] The system could deny access to chrome.\n      // TODO: Bacon: add: { status: 'unimplemented' }\n      return {\n        status: _Camera.PermissionStatus.DENIED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: false\n      };\n    }\n  }\n  async function handleRequestPermissionsAsync() {\n    try {\n      const streams = await getUserMedia({\n        video: true\n      });\n      // We need to close the media stream returned by getUserMedia\n      // to avoid using the camera since we won't use these streams now\n      // https://developer.mozilla.org/fr/docs/Web/API/MediaDevices/getUserMedia\n      streams.getTracks().forEach(track => {\n        track.stop();\n        streams.removeTrack(track);\n      });\n      return {\n        status: _Camera.PermissionStatus.GRANTED,\n        expires: 'never',\n        canAskAgain: true,\n        granted: true\n      };\n    } catch (error) {\n      return handleGetUserMediaError(error.message);\n    }\n  }\n  async function handlePermissionsQueryAsync(query) {\n    if (!navigator?.permissions?.query) {\n      throw new _expoModulesCore.UnavailabilityError('expo-camera', 'navigator.permissions API is not available');\n    }\n    try {\n      const {\n        state\n      } = await navigator.permissions.query({\n        name: query\n      });\n      switch (state) {\n        case 'prompt':\n          return {\n            status: _Camera.PermissionStatus.UNDETERMINED,\n            expires: 'never',\n            canAskAgain: true,\n            granted: false\n          };\n        case 'granted':\n          return {\n            status: _Camera.PermissionStatus.GRANTED,\n            expires: 'never',\n            canAskAgain: true,\n            granted: true\n          };\n        case 'denied':\n          return {\n            status: _Camera.PermissionStatus.DENIED,\n            expires: 'never',\n            canAskAgain: true,\n            granted: false\n          };\n      }\n    } catch (e) {\n      // Firefox doesn't support querying for the camera permission, so return undetermined status\n      if (e instanceof TypeError) {\n        return {\n          status: _Camera.PermissionStatus.UNDETERMINED,\n          expires: 'never',\n          canAskAgain: true,\n          granted: false\n        };\n      }\n      throw e;\n    }\n  }\n  var _default = exports.default = {\n    get Type() {\n      return {\n        back: 'back',\n        front: 'front'\n      };\n    },\n    get FlashMode() {\n      return {\n        on: 'on',\n        off: 'off',\n        auto: 'auto',\n        torch: 'torch'\n      };\n    },\n    get AutoFocus() {\n      return {\n        on: 'on',\n        off: 'off',\n        auto: 'auto',\n        singleShot: 'singleShot'\n      };\n    },\n    get WhiteBalance() {\n      return {\n        auto: 'auto',\n        continuous: 'continuous',\n        manual: 'manual'\n      };\n    },\n    get VideoQuality() {\n      return {};\n    },\n    get VideoStabilization() {\n      return {};\n    },\n    async isAvailableAsync() {\n      return (0, _WebUserMediaManager.canGetUserMedia)();\n    },\n    async takePicture(options, camera) {\n      return await camera.takePicture(options);\n    },\n    async pausePreview(camera) {\n      await camera.pausePreview();\n    },\n    async resumePreview(camera) {\n      return await camera.resumePreview();\n    },\n    async getAvailableCameraTypesAsync() {\n      if (!(0, _WebUserMediaManager.canGetUserMedia)() || !navigator.mediaDevices.enumerateDevices) return [];\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      const types = await Promise.all([(await (0, _WebUserMediaManager.isFrontCameraAvailableAsync)(devices)) && 'front', (await (0, _WebUserMediaManager.isBackCameraAvailableAsync)()) && 'back']);\n      return types.filter(Boolean);\n    },\n    async getAvailablePictureSizes(ratio, camera) {\n      return await camera.getAvailablePictureSizes(ratio);\n    },\n    /*\n    async record(\n      options?: CameraRecordingOptions,\n      camera: ExponentCameraRef\n    ): Promise<{ uri: string }> {\n      // TODO: Support on web\n    },\n    async stopRecording(camera: ExponentCameraRef): Promise<void> {\n      // TODO: Support on web\n    }, */\n    async getPermissionsAsync() {\n      return handlePermissionsQueryAsync('camera');\n    },\n    async requestPermissionsAsync() {\n      return handleRequestPermissionsAsync();\n    },\n    async getCameraPermissionsAsync() {\n      return handlePermissionsQueryAsync('camera');\n    },\n    async requestCameraPermissionsAsync() {\n      return handleRequestPermissionsAsync();\n    },\n    async getMicrophonePermissionsAsync() {\n      return handlePermissionsQueryAsync('microphone');\n    },\n    async requestMicrophonePermissionsAsync() {\n      try {\n        await getUserMedia({\n          audio: true\n        });\n        return {\n          status: _Camera.PermissionStatus.GRANTED,\n          expires: 'never',\n          canAskAgain: true,\n          granted: true\n        };\n      } catch (error) {\n        return handleGetUserMediaError(error.message);\n      }\n    }\n  };\n});", "lineCount": 221, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_expoModulesCore"], [6, 22, 1, 0], [6, 25, 1, 0, "require"], [6, 32, 1, 0], [6, 33, 1, 0, "_dependencyMap"], [6, 47, 1, 0], [7, 2, 2, 0], [7, 6, 2, 0, "_Camera"], [7, 13, 2, 0], [7, 16, 2, 0, "require"], [7, 23, 2, 0], [7, 24, 2, 0, "_dependencyMap"], [7, 38, 2, 0], [8, 2, 3, 0], [8, 6, 3, 0, "_WebUserMediaManager"], [8, 26, 3, 0], [8, 29, 3, 0, "require"], [8, 36, 3, 0], [8, 37, 3, 0, "_dependencyMap"], [8, 51, 3, 0], [9, 2, 4, 0], [9, 11, 4, 9, "getUserMedia"], [9, 23, 4, 21, "getUserMedia"], [9, 24, 4, 22, "constraints"], [9, 35, 4, 33], [9, 37, 4, 35], [10, 4, 5, 4], [10, 8, 5, 8, "navigator"], [10, 17, 5, 17], [10, 18, 5, 18, "mediaDevices"], [10, 30, 5, 30], [10, 34, 5, 34, "navigator"], [10, 43, 5, 43], [10, 44, 5, 44, "mediaDevices"], [10, 56, 5, 56], [10, 57, 5, 57, "getUserMedia"], [10, 69, 5, 69], [10, 71, 5, 71], [11, 6, 6, 8], [11, 13, 6, 15, "navigator"], [11, 22, 6, 24], [11, 23, 6, 25, "mediaDevices"], [11, 35, 6, 37], [11, 36, 6, 38, "getUserMedia"], [11, 48, 6, 50], [11, 49, 6, 51, "constraints"], [11, 60, 6, 62], [11, 61, 6, 63], [12, 4, 7, 4], [13, 4, 8, 4], [14, 4, 9, 4], [15, 4, 10, 4], [16, 4, 11, 4], [17, 4, 12, 4], [17, 10, 12, 10, "getUserMedia"], [17, 22, 12, 22], [18, 4, 13, 4], [19, 4, 14, 4, "navigator"], [19, 13, 14, 13], [19, 14, 14, 14, "getUserMedia"], [19, 26, 14, 26], [19, 30, 15, 8, "navigator"], [19, 39, 15, 17], [19, 40, 15, 18, "webkitGetUserMedia"], [19, 58, 15, 36], [19, 62, 16, 8, "navigator"], [19, 71, 16, 17], [19, 72, 16, 18, "mozGetUserMedia"], [19, 87, 16, 33], [19, 91, 17, 8], [19, 103, 17, 20], [20, 6, 18, 12], [20, 12, 18, 18, "error"], [20, 17, 18, 23], [20, 20, 18, 26], [20, 24, 18, 30, "Error"], [20, 29, 18, 35], [20, 30, 18, 36], [20, 56, 18, 62], [20, 57, 18, 63], [21, 6, 19, 12, "error"], [21, 11, 19, 17], [21, 12, 19, 18, "code"], [21, 16, 19, 22], [21, 19, 19, 25], [21, 20, 19, 26], [22, 6, 20, 12, "error"], [22, 11, 20, 17], [22, 12, 20, 18, "name"], [22, 16, 20, 22], [22, 19, 20, 25], [22, 36, 20, 42], [23, 6, 21, 12], [23, 12, 21, 18, "error"], [23, 17, 21, 23], [24, 4, 22, 8], [24, 5, 22, 9], [25, 4, 23, 4], [25, 11, 23, 11], [25, 15, 23, 15, "Promise"], [25, 22, 23, 22], [25, 23, 23, 23], [25, 24, 23, 24, "resolve"], [25, 31, 23, 31], [25, 33, 23, 33, "reject"], [25, 39, 23, 39], [25, 44, 23, 44], [26, 6, 24, 8], [27, 6, 25, 8], [28, 6, 26, 8], [29, 6, 27, 8, "getUserMedia"], [29, 18, 27, 20], [29, 19, 27, 21, "call"], [29, 23, 27, 25], [29, 24, 27, 26, "navigator"], [29, 33, 27, 35], [29, 35, 27, 37, "constraints"], [29, 46, 27, 48], [29, 48, 27, 50, "resolve"], [29, 55, 27, 57], [29, 57, 27, 59, "reject"], [29, 63, 27, 65], [29, 64, 27, 66], [30, 4, 28, 4], [30, 5, 28, 5], [30, 6, 28, 6], [31, 2, 29, 0], [32, 2, 30, 0], [32, 11, 30, 9, "handleGetUserMediaError"], [32, 34, 30, 32, "handleGetUserMediaError"], [32, 35, 30, 33], [33, 4, 30, 35, "message"], [34, 2, 30, 43], [34, 3, 30, 44], [34, 5, 30, 46], [35, 4, 31, 4], [36, 4, 32, 4], [37, 4, 33, 4], [37, 8, 33, 8, "message"], [37, 15, 33, 15], [37, 20, 33, 20], [37, 42, 33, 42], [37, 44, 33, 44], [38, 6, 34, 8], [38, 13, 34, 15], [39, 8, 35, 12, "status"], [39, 14, 35, 18], [39, 16, 35, 20, "PermissionStatus"], [39, 40, 35, 36], [39, 41, 35, 37, "UNDETERMINED"], [39, 53, 35, 49], [40, 8, 36, 12, "expires"], [40, 15, 36, 19], [40, 17, 36, 21], [40, 24, 36, 28], [41, 8, 37, 12, "canAskAgain"], [41, 19, 37, 23], [41, 21, 37, 25], [41, 25, 37, 29], [42, 8, 38, 12, "granted"], [42, 15, 38, 19], [42, 17, 38, 21], [43, 6, 39, 8], [43, 7, 39, 9], [44, 4, 40, 4], [44, 5, 40, 5], [44, 11, 41, 9], [45, 6, 42, 8], [46, 6, 43, 8], [47, 6, 44, 8], [47, 13, 44, 15], [48, 8, 45, 12, "status"], [48, 14, 45, 18], [48, 16, 45, 20, "PermissionStatus"], [48, 40, 45, 36], [48, 41, 45, 37, "DENIED"], [48, 47, 45, 43], [49, 8, 46, 12, "expires"], [49, 15, 46, 19], [49, 17, 46, 21], [49, 24, 46, 28], [50, 8, 47, 12, "canAskAgain"], [50, 19, 47, 23], [50, 21, 47, 25], [50, 25, 47, 29], [51, 8, 48, 12, "granted"], [51, 15, 48, 19], [51, 17, 48, 21], [52, 6, 49, 8], [52, 7, 49, 9], [53, 4, 50, 4], [54, 2, 51, 0], [55, 2, 52, 0], [55, 17, 52, 15, "handleRequestPermissionsAsync"], [55, 46, 52, 44, "handleRequestPermissionsAsync"], [55, 47, 52, 44], [55, 49, 52, 47], [56, 4, 53, 4], [56, 8, 53, 8], [57, 6, 54, 8], [57, 12, 54, 14, "streams"], [57, 19, 54, 21], [57, 22, 54, 24], [57, 28, 54, 30, "getUserMedia"], [57, 40, 54, 42], [57, 41, 54, 43], [58, 8, 55, 12, "video"], [58, 13, 55, 17], [58, 15, 55, 19], [59, 6, 56, 8], [59, 7, 56, 9], [59, 8, 56, 10], [60, 6, 57, 8], [61, 6, 58, 8], [62, 6, 59, 8], [63, 6, 60, 8, "streams"], [63, 13, 60, 15], [63, 14, 60, 16, "getTracks"], [63, 23, 60, 25], [63, 24, 60, 26], [63, 25, 60, 27], [63, 26, 60, 28, "for<PERSON>ach"], [63, 33, 60, 35], [63, 34, 60, 37, "track"], [63, 39, 60, 42], [63, 43, 60, 47], [64, 8, 61, 12, "track"], [64, 13, 61, 17], [64, 14, 61, 18, "stop"], [64, 18, 61, 22], [64, 19, 61, 23], [64, 20, 61, 24], [65, 8, 62, 12, "streams"], [65, 15, 62, 19], [65, 16, 62, 20, "removeTrack"], [65, 27, 62, 31], [65, 28, 62, 32, "track"], [65, 33, 62, 37], [65, 34, 62, 38], [66, 6, 63, 8], [66, 7, 63, 9], [66, 8, 63, 10], [67, 6, 64, 8], [67, 13, 64, 15], [68, 8, 65, 12, "status"], [68, 14, 65, 18], [68, 16, 65, 20, "PermissionStatus"], [68, 40, 65, 36], [68, 41, 65, 37, "GRANTED"], [68, 48, 65, 44], [69, 8, 66, 12, "expires"], [69, 15, 66, 19], [69, 17, 66, 21], [69, 24, 66, 28], [70, 8, 67, 12, "canAskAgain"], [70, 19, 67, 23], [70, 21, 67, 25], [70, 25, 67, 29], [71, 8, 68, 12, "granted"], [71, 15, 68, 19], [71, 17, 68, 21], [72, 6, 69, 8], [72, 7, 69, 9], [73, 4, 70, 4], [73, 5, 70, 5], [73, 6, 71, 4], [73, 13, 71, 11, "error"], [73, 18, 71, 16], [73, 20, 71, 18], [74, 6, 72, 8], [74, 13, 72, 15, "handleGetUserMediaError"], [74, 36, 72, 38], [74, 37, 72, 39, "error"], [74, 42, 72, 44], [74, 43, 72, 45, "message"], [74, 50, 72, 52], [74, 51, 72, 53], [75, 4, 73, 4], [76, 2, 74, 0], [77, 2, 75, 0], [77, 17, 75, 15, "handlePermissionsQueryAsync"], [77, 44, 75, 42, "handlePermissionsQueryAsync"], [77, 45, 75, 43, "query"], [77, 50, 75, 48], [77, 52, 75, 50], [78, 4, 76, 4], [78, 8, 76, 8], [78, 9, 76, 9, "navigator"], [78, 18, 76, 18], [78, 20, 76, 20, "permissions"], [78, 31, 76, 31], [78, 33, 76, 33, "query"], [78, 38, 76, 38], [78, 40, 76, 40], [79, 6, 77, 8], [79, 12, 77, 14], [79, 16, 77, 18, "UnavailabilityError"], [79, 52, 77, 37], [79, 53, 77, 38], [79, 66, 77, 51], [79, 68, 77, 53], [79, 112, 77, 97], [79, 113, 77, 98], [80, 4, 78, 4], [81, 4, 79, 4], [81, 8, 79, 8], [82, 6, 80, 8], [82, 12, 80, 14], [83, 8, 80, 16, "state"], [84, 6, 80, 22], [84, 7, 80, 23], [84, 10, 80, 26], [84, 16, 80, 32, "navigator"], [84, 25, 80, 41], [84, 26, 80, 42, "permissions"], [84, 37, 80, 53], [84, 38, 80, 54, "query"], [84, 43, 80, 59], [84, 44, 80, 60], [85, 8, 80, 62, "name"], [85, 12, 80, 66], [85, 14, 80, 68, "query"], [86, 6, 80, 74], [86, 7, 80, 75], [86, 8, 80, 76], [87, 6, 81, 8], [87, 14, 81, 16, "state"], [87, 19, 81, 21], [88, 8, 82, 12], [88, 13, 82, 17], [88, 21, 82, 25], [89, 10, 83, 16], [89, 17, 83, 23], [90, 12, 84, 20, "status"], [90, 18, 84, 26], [90, 20, 84, 28, "PermissionStatus"], [90, 44, 84, 44], [90, 45, 84, 45, "UNDETERMINED"], [90, 57, 84, 57], [91, 12, 85, 20, "expires"], [91, 19, 85, 27], [91, 21, 85, 29], [91, 28, 85, 36], [92, 12, 86, 20, "canAskAgain"], [92, 23, 86, 31], [92, 25, 86, 33], [92, 29, 86, 37], [93, 12, 87, 20, "granted"], [93, 19, 87, 27], [93, 21, 87, 29], [94, 10, 88, 16], [94, 11, 88, 17], [95, 8, 89, 12], [95, 13, 89, 17], [95, 22, 89, 26], [96, 10, 90, 16], [96, 17, 90, 23], [97, 12, 91, 20, "status"], [97, 18, 91, 26], [97, 20, 91, 28, "PermissionStatus"], [97, 44, 91, 44], [97, 45, 91, 45, "GRANTED"], [97, 52, 91, 52], [98, 12, 92, 20, "expires"], [98, 19, 92, 27], [98, 21, 92, 29], [98, 28, 92, 36], [99, 12, 93, 20, "canAskAgain"], [99, 23, 93, 31], [99, 25, 93, 33], [99, 29, 93, 37], [100, 12, 94, 20, "granted"], [100, 19, 94, 27], [100, 21, 94, 29], [101, 10, 95, 16], [101, 11, 95, 17], [102, 8, 96, 12], [102, 13, 96, 17], [102, 21, 96, 25], [103, 10, 97, 16], [103, 17, 97, 23], [104, 12, 98, 20, "status"], [104, 18, 98, 26], [104, 20, 98, 28, "PermissionStatus"], [104, 44, 98, 44], [104, 45, 98, 45, "DENIED"], [104, 51, 98, 51], [105, 12, 99, 20, "expires"], [105, 19, 99, 27], [105, 21, 99, 29], [105, 28, 99, 36], [106, 12, 100, 20, "canAskAgain"], [106, 23, 100, 31], [106, 25, 100, 33], [106, 29, 100, 37], [107, 12, 101, 20, "granted"], [107, 19, 101, 27], [107, 21, 101, 29], [108, 10, 102, 16], [108, 11, 102, 17], [109, 6, 103, 8], [110, 4, 104, 4], [110, 5, 104, 5], [110, 6, 105, 4], [110, 13, 105, 11, "e"], [110, 14, 105, 12], [110, 16, 105, 14], [111, 6, 106, 8], [112, 6, 107, 8], [112, 10, 107, 12, "e"], [112, 11, 107, 13], [112, 23, 107, 25, "TypeError"], [112, 32, 107, 34], [112, 34, 107, 36], [113, 8, 108, 12], [113, 15, 108, 19], [114, 10, 109, 16, "status"], [114, 16, 109, 22], [114, 18, 109, 24, "PermissionStatus"], [114, 42, 109, 40], [114, 43, 109, 41, "UNDETERMINED"], [114, 55, 109, 53], [115, 10, 110, 16, "expires"], [115, 17, 110, 23], [115, 19, 110, 25], [115, 26, 110, 32], [116, 10, 111, 16, "canAskAgain"], [116, 21, 111, 27], [116, 23, 111, 29], [116, 27, 111, 33], [117, 10, 112, 16, "granted"], [117, 17, 112, 23], [117, 19, 112, 25], [118, 8, 113, 12], [118, 9, 113, 13], [119, 6, 114, 8], [120, 6, 115, 8], [120, 12, 115, 14, "e"], [120, 13, 115, 15], [121, 4, 116, 4], [122, 2, 117, 0], [123, 2, 117, 1], [123, 6, 117, 1, "_default"], [123, 14, 117, 1], [123, 17, 117, 1, "exports"], [123, 24, 117, 1], [123, 25, 117, 1, "default"], [123, 32, 117, 1], [123, 35, 118, 15], [124, 4, 119, 4], [124, 8, 119, 8, "Type"], [124, 12, 119, 12, "Type"], [124, 13, 119, 12], [124, 15, 119, 15], [125, 6, 120, 8], [125, 13, 120, 15], [126, 8, 121, 12, "back"], [126, 12, 121, 16], [126, 14, 121, 18], [126, 20, 121, 24], [127, 8, 122, 12, "front"], [127, 13, 122, 17], [127, 15, 122, 19], [128, 6, 123, 8], [128, 7, 123, 9], [129, 4, 124, 4], [129, 5, 124, 5], [130, 4, 125, 4], [130, 8, 125, 8, "FlashMode"], [130, 17, 125, 17, "FlashMode"], [130, 18, 125, 17], [130, 20, 125, 20], [131, 6, 126, 8], [131, 13, 126, 15], [132, 8, 127, 12, "on"], [132, 10, 127, 14], [132, 12, 127, 16], [132, 16, 127, 20], [133, 8, 128, 12, "off"], [133, 11, 128, 15], [133, 13, 128, 17], [133, 18, 128, 22], [134, 8, 129, 12, "auto"], [134, 12, 129, 16], [134, 14, 129, 18], [134, 20, 129, 24], [135, 8, 130, 12, "torch"], [135, 13, 130, 17], [135, 15, 130, 19], [136, 6, 131, 8], [136, 7, 131, 9], [137, 4, 132, 4], [137, 5, 132, 5], [138, 4, 133, 4], [138, 8, 133, 8, "AutoFocus"], [138, 17, 133, 17, "AutoFocus"], [138, 18, 133, 17], [138, 20, 133, 20], [139, 6, 134, 8], [139, 13, 134, 15], [140, 8, 135, 12, "on"], [140, 10, 135, 14], [140, 12, 135, 16], [140, 16, 135, 20], [141, 8, 136, 12, "off"], [141, 11, 136, 15], [141, 13, 136, 17], [141, 18, 136, 22], [142, 8, 137, 12, "auto"], [142, 12, 137, 16], [142, 14, 137, 18], [142, 20, 137, 24], [143, 8, 138, 12, "singleShot"], [143, 18, 138, 22], [143, 20, 138, 24], [144, 6, 139, 8], [144, 7, 139, 9], [145, 4, 140, 4], [145, 5, 140, 5], [146, 4, 141, 4], [146, 8, 141, 8, "WhiteBalance"], [146, 20, 141, 20, "WhiteBalance"], [146, 21, 141, 20], [146, 23, 141, 23], [147, 6, 142, 8], [147, 13, 142, 15], [148, 8, 143, 12, "auto"], [148, 12, 143, 16], [148, 14, 143, 18], [148, 20, 143, 24], [149, 8, 144, 12, "continuous"], [149, 18, 144, 22], [149, 20, 144, 24], [149, 32, 144, 36], [150, 8, 145, 12, "manual"], [150, 14, 145, 18], [150, 16, 145, 20], [151, 6, 146, 8], [151, 7, 146, 9], [152, 4, 147, 4], [152, 5, 147, 5], [153, 4, 148, 4], [153, 8, 148, 8, "VideoQuality"], [153, 20, 148, 20, "VideoQuality"], [153, 21, 148, 20], [153, 23, 148, 23], [154, 6, 149, 8], [154, 13, 149, 15], [154, 14, 149, 16], [154, 15, 149, 17], [155, 4, 150, 4], [155, 5, 150, 5], [156, 4, 151, 4], [156, 8, 151, 8, "VideoStabilization"], [156, 26, 151, 26, "VideoStabilization"], [156, 27, 151, 26], [156, 29, 151, 29], [157, 6, 152, 8], [157, 13, 152, 15], [157, 14, 152, 16], [157, 15, 152, 17], [158, 4, 153, 4], [158, 5, 153, 5], [159, 4, 154, 4], [159, 10, 154, 10, "isAvailableAsync"], [159, 26, 154, 26, "isAvailableAsync"], [159, 27, 154, 26], [159, 29, 154, 29], [160, 6, 155, 8], [160, 13, 155, 15], [160, 17, 155, 15, "canGetUserMedia"], [160, 53, 155, 30], [160, 55, 155, 31], [160, 56, 155, 32], [161, 4, 156, 4], [161, 5, 156, 5], [162, 4, 157, 4], [162, 10, 157, 10, "takePicture"], [162, 21, 157, 21, "takePicture"], [162, 22, 157, 22, "options"], [162, 29, 157, 29], [162, 31, 157, 31, "camera"], [162, 37, 157, 37], [162, 39, 157, 39], [163, 6, 158, 8], [163, 13, 158, 15], [163, 19, 158, 21, "camera"], [163, 25, 158, 27], [163, 26, 158, 28, "takePicture"], [163, 37, 158, 39], [163, 38, 158, 40, "options"], [163, 45, 158, 47], [163, 46, 158, 48], [164, 4, 159, 4], [164, 5, 159, 5], [165, 4, 160, 4], [165, 10, 160, 10, "pausePreview"], [165, 22, 160, 22, "pausePreview"], [165, 23, 160, 23, "camera"], [165, 29, 160, 29], [165, 31, 160, 31], [166, 6, 161, 8], [166, 12, 161, 14, "camera"], [166, 18, 161, 20], [166, 19, 161, 21, "pausePreview"], [166, 31, 161, 33], [166, 32, 161, 34], [166, 33, 161, 35], [167, 4, 162, 4], [167, 5, 162, 5], [168, 4, 163, 4], [168, 10, 163, 10, "resumePreview"], [168, 23, 163, 23, "resumePreview"], [168, 24, 163, 24, "camera"], [168, 30, 163, 30], [168, 32, 163, 32], [169, 6, 164, 8], [169, 13, 164, 15], [169, 19, 164, 21, "camera"], [169, 25, 164, 27], [169, 26, 164, 28, "resumePreview"], [169, 39, 164, 41], [169, 40, 164, 42], [169, 41, 164, 43], [170, 4, 165, 4], [170, 5, 165, 5], [171, 4, 166, 4], [171, 10, 166, 10, "getAvailableCameraTypesAsync"], [171, 38, 166, 38, "getAvailableCameraTypesAsync"], [171, 39, 166, 38], [171, 41, 166, 41], [172, 6, 167, 8], [172, 10, 167, 12], [172, 11, 167, 13], [172, 15, 167, 13, "canGetUserMedia"], [172, 51, 167, 28], [172, 53, 167, 29], [172, 54, 167, 30], [172, 58, 167, 34], [172, 59, 167, 35, "navigator"], [172, 68, 167, 44], [172, 69, 167, 45, "mediaDevices"], [172, 81, 167, 57], [172, 82, 167, 58, "enumerateDevices"], [172, 98, 167, 74], [172, 100, 168, 12], [172, 107, 168, 19], [172, 109, 168, 21], [173, 6, 169, 8], [173, 12, 169, 14, "devices"], [173, 19, 169, 21], [173, 22, 169, 24], [173, 28, 169, 30, "navigator"], [173, 37, 169, 39], [173, 38, 169, 40, "mediaDevices"], [173, 50, 169, 52], [173, 51, 169, 53, "enumerateDevices"], [173, 67, 169, 69], [173, 68, 169, 70], [173, 69, 169, 71], [174, 6, 170, 8], [174, 12, 170, 14, "types"], [174, 17, 170, 19], [174, 20, 170, 22], [174, 26, 170, 28, "Promise"], [174, 33, 170, 35], [174, 34, 170, 36, "all"], [174, 37, 170, 39], [174, 38, 170, 40], [174, 39, 171, 12], [174, 40, 171, 13], [174, 46, 171, 19], [174, 50, 171, 19, "isFrontCameraAvailableAsync"], [174, 98, 171, 46], [174, 100, 171, 47, "devices"], [174, 107, 171, 54], [174, 108, 171, 55], [174, 113, 171, 60], [174, 120, 171, 67], [174, 122, 172, 12], [174, 123, 172, 13], [174, 129, 172, 19], [174, 133, 172, 19, "isBackCameraAvailableAsync"], [174, 180, 172, 45], [174, 182, 172, 46], [174, 183, 172, 47], [174, 188, 172, 52], [174, 194, 172, 58], [174, 195, 173, 9], [174, 196, 173, 10], [175, 6, 174, 8], [175, 13, 174, 15, "types"], [175, 18, 174, 20], [175, 19, 174, 21, "filter"], [175, 25, 174, 27], [175, 26, 174, 28, "Boolean"], [175, 33, 174, 35], [175, 34, 174, 36], [176, 4, 175, 4], [176, 5, 175, 5], [177, 4, 176, 4], [177, 10, 176, 10, "getAvailablePictureSizes"], [177, 34, 176, 34, "getAvailablePictureSizes"], [177, 35, 176, 35, "ratio"], [177, 40, 176, 40], [177, 42, 176, 42, "camera"], [177, 48, 176, 48], [177, 50, 176, 50], [178, 6, 177, 8], [178, 13, 177, 15], [178, 19, 177, 21, "camera"], [178, 25, 177, 27], [178, 26, 177, 28, "getAvailablePictureSizes"], [178, 50, 177, 52], [178, 51, 177, 53, "ratio"], [178, 56, 177, 58], [178, 57, 177, 59], [179, 4, 178, 4], [179, 5, 178, 5], [180, 4, 179, 4], [181, 0, 180, 0], [182, 0, 181, 0], [183, 0, 182, 0], [184, 0, 183, 0], [185, 0, 184, 0], [186, 0, 185, 0], [187, 0, 186, 0], [188, 0, 187, 0], [189, 0, 188, 0], [190, 4, 189, 4], [190, 10, 189, 10, "getPermissionsAsync"], [190, 29, 189, 29, "getPermissionsAsync"], [190, 30, 189, 29], [190, 32, 189, 32], [191, 6, 190, 8], [191, 13, 190, 15, "handlePermissionsQueryAsync"], [191, 40, 190, 42], [191, 41, 190, 43], [191, 49, 190, 51], [191, 50, 190, 52], [192, 4, 191, 4], [192, 5, 191, 5], [193, 4, 192, 4], [193, 10, 192, 10, "requestPermissionsAsync"], [193, 33, 192, 33, "requestPermissionsAsync"], [193, 34, 192, 33], [193, 36, 192, 36], [194, 6, 193, 8], [194, 13, 193, 15, "handleRequestPermissionsAsync"], [194, 42, 193, 44], [194, 43, 193, 45], [194, 44, 193, 46], [195, 4, 194, 4], [195, 5, 194, 5], [196, 4, 195, 4], [196, 10, 195, 10, "getCameraPermissionsAsync"], [196, 35, 195, 35, "getCameraPermissionsAsync"], [196, 36, 195, 35], [196, 38, 195, 38], [197, 6, 196, 8], [197, 13, 196, 15, "handlePermissionsQueryAsync"], [197, 40, 196, 42], [197, 41, 196, 43], [197, 49, 196, 51], [197, 50, 196, 52], [198, 4, 197, 4], [198, 5, 197, 5], [199, 4, 198, 4], [199, 10, 198, 10, "requestCameraPermissionsAsync"], [199, 39, 198, 39, "requestCameraPermissionsAsync"], [199, 40, 198, 39], [199, 42, 198, 42], [200, 6, 199, 8], [200, 13, 199, 15, "handleRequestPermissionsAsync"], [200, 42, 199, 44], [200, 43, 199, 45], [200, 44, 199, 46], [201, 4, 200, 4], [201, 5, 200, 5], [202, 4, 201, 4], [202, 10, 201, 10, "getMicrophonePermissionsAsync"], [202, 39, 201, 39, "getMicrophonePermissionsAsync"], [202, 40, 201, 39], [202, 42, 201, 42], [203, 6, 202, 8], [203, 13, 202, 15, "handlePermissionsQueryAsync"], [203, 40, 202, 42], [203, 41, 202, 43], [203, 53, 202, 55], [203, 54, 202, 56], [204, 4, 203, 4], [204, 5, 203, 5], [205, 4, 204, 4], [205, 10, 204, 10, "requestMicrophonePermissionsAsync"], [205, 43, 204, 43, "requestMicrophonePermissionsAsync"], [205, 44, 204, 43], [205, 46, 204, 46], [206, 6, 205, 8], [206, 10, 205, 12], [207, 8, 206, 12], [207, 14, 206, 18, "getUserMedia"], [207, 26, 206, 30], [207, 27, 206, 31], [208, 10, 207, 16, "audio"], [208, 15, 207, 21], [208, 17, 207, 23], [209, 8, 208, 12], [209, 9, 208, 13], [209, 10, 208, 14], [210, 8, 209, 12], [210, 15, 209, 19], [211, 10, 210, 16, "status"], [211, 16, 210, 22], [211, 18, 210, 24, "PermissionStatus"], [211, 42, 210, 40], [211, 43, 210, 41, "GRANTED"], [211, 50, 210, 48], [212, 10, 211, 16, "expires"], [212, 17, 211, 23], [212, 19, 211, 25], [212, 26, 211, 32], [213, 10, 212, 16, "canAskAgain"], [213, 21, 212, 27], [213, 23, 212, 29], [213, 27, 212, 33], [214, 10, 213, 16, "granted"], [214, 17, 213, 23], [214, 19, 213, 25], [215, 8, 214, 12], [215, 9, 214, 13], [216, 6, 215, 8], [216, 7, 215, 9], [216, 8, 216, 8], [216, 15, 216, 15, "error"], [216, 20, 216, 20], [216, 22, 216, 22], [217, 8, 217, 12], [217, 15, 217, 19, "handleGetUserMediaError"], [217, 38, 217, 42], [217, 39, 217, 43, "error"], [217, 44, 217, 48], [217, 45, 217, 49, "message"], [217, 52, 217, 56], [217, 53, 217, 57], [218, 6, 218, 8], [219, 4, 219, 4], [220, 2, 220, 0], [220, 3, 220, 1], [221, 0, 220, 1], [221, 3]], "functionMap": {"names": ["<global>", "getUserMedia", "<anonymous>", "Promise$argument_0", "handleGetUserMediaError", "handleRequestPermissionsAsync", "streams.getTracks.forEach$argument_0", "handlePermissionsQueryAsync", "default.get__Type", "default.get__FlashMode", "default.get__AutoFocus", "default.get__WhiteBalance", "default.get__VideoQuality", "default.get__VideoStabilization", "default.isAvailableAsync", "default.takePicture", "default.pausePreview", "default.resumePreview", "default.getAvailableCameraTypesAsync", "default.getAvailablePictureSizes", "default.getPermissionsAsync", "default.requestPermissionsAsync", "default.getCameraPermissionsAsync", "default.requestCameraPermissionsAsync", "default.getMicrophonePermissionsAsync", "default.requestMicrophonePermissionsAsync"], "mappings": "AAA;ACG;QCa;SDK;uBEC;KFK;CDC;AIC;CJqB;AKC;oCCQ;SDG;CLW;AOC;CP0C;IQE;KRK;ISC;KTO;IUC;KVO;IWC;KXM;IYC;KZE;IaC;KbE;IcC;KdE;IeC;KfE;IgBC;KhBE;IiBC;KjBE;IkBC;KlBS;ImBC;KnBE;IoBW;KpBE;IqBC;KrBE;IsBC;KtBE;IuBC;KvBE;IwBC;KxBE;IyBC;KzBe"}}, "type": "js/module"}]}