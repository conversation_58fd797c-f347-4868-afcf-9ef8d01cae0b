{"dependencies": [{"name": "expo-modules-core", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 47, "index": 47}}], "key": "fU8WLIPqoAGygnPbZ/QJiQQfXEY=", "exportNames": ["*"]}}, {"name": "../utils.web", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 4, "column": 0, "index": 108}, "end": {"line": 4, "column": 42, "index": 150}}], "key": "V6Cd8v/K/LtdxP2wf1WD5HCNhqM=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  var _expoModulesCore = require(_dependencyMap[0], \"expo-modules-core\");\n  var _utils = require(_dependencyMap[1], \"../utils.web\");\n  const clamp = (value, max) => Math.max(0, Math.min(max, value));\n  var _default = (canvas, options) => {\n    // ensure values are defined.\n    let {\n      originX = 0,\n      originY = 0,\n      width = 0,\n      height = 0\n    } = options;\n    // lock within bounds.\n    width = clamp(width, canvas.width);\n    height = clamp(height, canvas.height);\n    originX = clamp(originX, canvas.width);\n    originY = clamp(originY, canvas.height);\n\n    // lock sum of crop.\n    width = Math.min(originX + width, canvas.width) - originX;\n    height = Math.min(originY + height, canvas.height) - originY;\n    if (width === 0 || height === 0) {\n      throw new _expoModulesCore.CodedError('ERR_IMAGE_MANIPULATOR_CROP', 'Crop size must be greater than 0: ' + JSON.stringify(options, null, 2));\n    }\n    const result = document.createElement('canvas');\n    result.width = width;\n    result.height = height;\n    const context = (0, _utils.getContext)(result);\n    context.drawImage(canvas, originX, originY, width, height, 0, 0, width, height);\n    return result;\n  };\n  exports.default = _default;\n});", "lineCount": 37, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_expoModulesCore"], [6, 22, 1, 0], [6, 25, 1, 0, "require"], [6, 32, 1, 0], [6, 33, 1, 0, "_dependencyMap"], [6, 47, 1, 0], [7, 2, 4, 0], [7, 6, 4, 0, "_utils"], [7, 12, 4, 0], [7, 15, 4, 0, "require"], [7, 22, 4, 0], [7, 23, 4, 0, "_dependencyMap"], [7, 37, 4, 0], [8, 2, 6, 0], [8, 8, 6, 6, "clamp"], [8, 13, 6, 11], [8, 16, 6, 14, "clamp"], [8, 17, 6, 15, "value"], [8, 22, 6, 28], [8, 24, 6, 30, "max"], [8, 27, 6, 41], [8, 32, 6, 54, "Math"], [8, 36, 6, 58], [8, 37, 6, 59, "max"], [8, 40, 6, 62], [8, 41, 6, 63], [8, 42, 6, 64], [8, 44, 6, 66, "Math"], [8, 48, 6, 70], [8, 49, 6, 71, "min"], [8, 52, 6, 74], [8, 53, 6, 75, "max"], [8, 56, 6, 78], [8, 58, 6, 80, "value"], [8, 63, 6, 85], [8, 64, 6, 86], [8, 65, 6, 87], [9, 2, 6, 88], [9, 6, 6, 88, "_default"], [9, 14, 6, 88], [9, 17, 8, 15, "_default"], [9, 18, 8, 16, "canvas"], [9, 24, 8, 41], [9, 26, 8, 43, "options"], [9, 33, 8, 70], [9, 38, 8, 75], [10, 4, 9, 2], [11, 4, 10, 2], [11, 8, 10, 6], [12, 6, 10, 8, "originX"], [12, 13, 10, 15], [12, 16, 10, 18], [12, 17, 10, 19], [13, 6, 10, 21, "originY"], [13, 13, 10, 28], [13, 16, 10, 31], [13, 17, 10, 32], [14, 6, 10, 34, "width"], [14, 11, 10, 39], [14, 14, 10, 42], [14, 15, 10, 43], [15, 6, 10, 45, "height"], [15, 12, 10, 51], [15, 15, 10, 54], [16, 4, 10, 56], [16, 5, 10, 57], [16, 8, 10, 60, "options"], [16, 15, 10, 67], [17, 4, 11, 2], [18, 4, 12, 2, "width"], [18, 9, 12, 7], [18, 12, 12, 10, "clamp"], [18, 17, 12, 15], [18, 18, 12, 16, "width"], [18, 23, 12, 21], [18, 25, 12, 23, "canvas"], [18, 31, 12, 29], [18, 32, 12, 30, "width"], [18, 37, 12, 35], [18, 38, 12, 36], [19, 4, 13, 2, "height"], [19, 10, 13, 8], [19, 13, 13, 11, "clamp"], [19, 18, 13, 16], [19, 19, 13, 17, "height"], [19, 25, 13, 23], [19, 27, 13, 25, "canvas"], [19, 33, 13, 31], [19, 34, 13, 32, "height"], [19, 40, 13, 38], [19, 41, 13, 39], [20, 4, 14, 2, "originX"], [20, 11, 14, 9], [20, 14, 14, 12, "clamp"], [20, 19, 14, 17], [20, 20, 14, 18, "originX"], [20, 27, 14, 25], [20, 29, 14, 27, "canvas"], [20, 35, 14, 33], [20, 36, 14, 34, "width"], [20, 41, 14, 39], [20, 42, 14, 40], [21, 4, 15, 2, "originY"], [21, 11, 15, 9], [21, 14, 15, 12, "clamp"], [21, 19, 15, 17], [21, 20, 15, 18, "originY"], [21, 27, 15, 25], [21, 29, 15, 27, "canvas"], [21, 35, 15, 33], [21, 36, 15, 34, "height"], [21, 42, 15, 40], [21, 43, 15, 41], [23, 4, 17, 2], [24, 4, 18, 2, "width"], [24, 9, 18, 7], [24, 12, 18, 10, "Math"], [24, 16, 18, 14], [24, 17, 18, 15, "min"], [24, 20, 18, 18], [24, 21, 18, 19, "originX"], [24, 28, 18, 26], [24, 31, 18, 29, "width"], [24, 36, 18, 34], [24, 38, 18, 36, "canvas"], [24, 44, 18, 42], [24, 45, 18, 43, "width"], [24, 50, 18, 48], [24, 51, 18, 49], [24, 54, 18, 52, "originX"], [24, 61, 18, 59], [25, 4, 19, 2, "height"], [25, 10, 19, 8], [25, 13, 19, 11, "Math"], [25, 17, 19, 15], [25, 18, 19, 16, "min"], [25, 21, 19, 19], [25, 22, 19, 20, "originY"], [25, 29, 19, 27], [25, 32, 19, 30, "height"], [25, 38, 19, 36], [25, 40, 19, 38, "canvas"], [25, 46, 19, 44], [25, 47, 19, 45, "height"], [25, 53, 19, 51], [25, 54, 19, 52], [25, 57, 19, 55, "originY"], [25, 64, 19, 62], [26, 4, 21, 2], [26, 8, 21, 6, "width"], [26, 13, 21, 11], [26, 18, 21, 16], [26, 19, 21, 17], [26, 23, 21, 21, "height"], [26, 29, 21, 27], [26, 34, 21, 32], [26, 35, 21, 33], [26, 37, 21, 35], [27, 6, 22, 4], [27, 12, 22, 10], [27, 16, 22, 14, "CodedError"], [27, 43, 22, 24], [27, 44, 23, 6], [27, 72, 23, 34], [27, 74, 24, 6], [27, 110, 24, 42], [27, 113, 24, 45, "JSON"], [27, 117, 24, 49], [27, 118, 24, 50, "stringify"], [27, 127, 24, 59], [27, 128, 24, 60, "options"], [27, 135, 24, 67], [27, 137, 24, 69], [27, 141, 24, 73], [27, 143, 24, 75], [27, 144, 24, 76], [27, 145, 25, 4], [27, 146, 25, 5], [28, 4, 26, 2], [29, 4, 28, 2], [29, 10, 28, 8, "result"], [29, 16, 28, 14], [29, 19, 28, 17, "document"], [29, 27, 28, 25], [29, 28, 28, 26, "createElement"], [29, 41, 28, 39], [29, 42, 28, 40], [29, 50, 28, 48], [29, 51, 28, 49], [30, 4, 29, 2, "result"], [30, 10, 29, 8], [30, 11, 29, 9, "width"], [30, 16, 29, 14], [30, 19, 29, 17, "width"], [30, 24, 29, 22], [31, 4, 30, 2, "result"], [31, 10, 30, 8], [31, 11, 30, 9, "height"], [31, 17, 30, 15], [31, 20, 30, 18, "height"], [31, 26, 30, 24], [32, 4, 32, 2], [32, 10, 32, 8, "context"], [32, 17, 32, 15], [32, 20, 32, 18], [32, 24, 32, 18, "getContext"], [32, 41, 32, 28], [32, 43, 32, 29, "result"], [32, 49, 32, 35], [32, 50, 32, 36], [33, 4, 33, 2, "context"], [33, 11, 33, 9], [33, 12, 33, 10, "drawImage"], [33, 21, 33, 19], [33, 22, 33, 20, "canvas"], [33, 28, 33, 26], [33, 30, 33, 28, "originX"], [33, 37, 33, 35], [33, 39, 33, 37, "originY"], [33, 46, 33, 44], [33, 48, 33, 46, "width"], [33, 53, 33, 51], [33, 55, 33, 53, "height"], [33, 61, 33, 59], [33, 63, 33, 61], [33, 64, 33, 62], [33, 66, 33, 64], [33, 67, 33, 65], [33, 69, 33, 67, "width"], [33, 74, 33, 72], [33, 76, 33, 74, "height"], [33, 82, 33, 80], [33, 83, 33, 81], [34, 4, 35, 2], [34, 11, 35, 9, "result"], [34, 17, 35, 15], [35, 2, 36, 0], [35, 3, 36, 1], [36, 2, 36, 1, "exports"], [36, 9, 36, 1], [36, 10, 36, 1, "default"], [36, 17, 36, 1], [36, 20, 36, 1, "_default"], [36, 28, 36, 1], [37, 0, 36, 1], [37, 3]], "functionMap": {"names": ["<global>", "clamp", "default"], "mappings": "AAA;cCK,yED;eEE;CF4B"}}, "type": "js/module"}]}