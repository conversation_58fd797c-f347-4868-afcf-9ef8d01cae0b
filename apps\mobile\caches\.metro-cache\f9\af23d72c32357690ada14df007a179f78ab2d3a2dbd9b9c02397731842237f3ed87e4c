{"dependencies": [{"name": "@babel/runtime/helpers/interopRequireDefault", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "kslwqCIsh6ew+I1KeA1rlVRjsAk=", "exportNames": ["*"]}}, {"name": "@react-native/assets-registry/registry", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 70, "index": 70}}], "key": "DfKH1NNXqDIAaDOtB+YKkBB07j8=", "exportNames": ["*"]}}, {"name": "./AssetSourceResolver", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 2, "column": 0, "index": 71}, "end": {"line": 2, "column": 56, "index": 127}}], "key": "1XREZLfiB40YsGJEOFyn6rrYKeQ=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  var _interopRequireDefault = require(_dependencyMap[0], \"@babel/runtime/helpers/interopRequireDefault\");\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.pickScale = exports.default = void 0;\n  exports.setCustomSourceTransformer = setCustomSourceTransformer;\n  var _registry = require(_dependencyMap[1], \"@react-native/assets-registry/registry\");\n  var _AssetSourceResolver = _interopRequireDefault(require(_dependencyMap[2], \"./AssetSourceResolver\"));\n  let _customSourceTransformer;\n  function setCustomSourceTransformer(transformer) {\n    _customSourceTransformer = transformer;\n  }\n  /**\n   * `source` is either a number (opaque type returned by require('./foo.png'))\n   * or an `ImageSource` like { uri: '<http location || file path>' }\n   */\n  function resolveAssetSource(source) {\n    if (typeof source === 'object') {\n      return source;\n    }\n    const asset = (0, _registry.getAssetByID)(source);\n    if (!asset) {\n      return null;\n    }\n    const resolver = new _AssetSourceResolver.default(\n    // Doesn't matter since this is removed on web\n    'https://expo.dev', null, asset);\n    if (_customSourceTransformer) {\n      return _customSourceTransformer(resolver);\n    }\n    return resolver.defaultAsset();\n  }\n  Object.defineProperty(resolveAssetSource, 'setCustomSourceTransformer', {\n    get() {\n      return setCustomSourceTransformer;\n    }\n  });\n  var _default = exports.default = resolveAssetSource;\n  const {\n    pickScale\n  } = _AssetSourceResolver.default;\n  exports.pickScale = pickScale;\n});", "lineCount": 44, "map": [[8, 2, 1, 0], [8, 6, 1, 0, "_registry"], [8, 15, 1, 0], [8, 18, 1, 0, "require"], [8, 25, 1, 0], [8, 26, 1, 0, "_dependencyMap"], [8, 40, 1, 0], [9, 2, 2, 0], [9, 6, 2, 0, "_AssetSourceResolver"], [9, 26, 2, 0], [9, 29, 2, 0, "_interopRequireDefault"], [9, 51, 2, 0], [9, 52, 2, 0, "require"], [9, 59, 2, 0], [9, 60, 2, 0, "_dependencyMap"], [9, 74, 2, 0], [10, 2, 3, 0], [10, 6, 3, 4, "_customSourceTransformer"], [10, 30, 3, 28], [11, 2, 4, 7], [11, 11, 4, 16, "setCustomSourceTransformer"], [11, 37, 4, 42, "setCustomSourceTransformer"], [11, 38, 4, 43, "transformer"], [11, 49, 4, 54], [11, 51, 4, 56], [12, 4, 5, 4, "_customSourceTransformer"], [12, 28, 5, 28], [12, 31, 5, 31, "transformer"], [12, 42, 5, 42], [13, 2, 6, 0], [14, 2, 7, 0], [15, 0, 8, 0], [16, 0, 9, 0], [17, 0, 10, 0], [18, 2, 11, 0], [18, 11, 11, 9, "resolveAssetSource"], [18, 29, 11, 27, "resolveAssetSource"], [18, 30, 11, 28, "source"], [18, 36, 11, 34], [18, 38, 11, 36], [19, 4, 12, 4], [19, 8, 12, 8], [19, 15, 12, 15, "source"], [19, 21, 12, 21], [19, 26, 12, 26], [19, 34, 12, 34], [19, 36, 12, 36], [20, 6, 13, 8], [20, 13, 13, 15, "source"], [20, 19, 13, 21], [21, 4, 14, 4], [22, 4, 15, 4], [22, 10, 15, 10, "asset"], [22, 15, 15, 15], [22, 18, 15, 18], [22, 22, 15, 18, "getAssetByID"], [22, 44, 15, 30], [22, 46, 15, 31, "source"], [22, 52, 15, 37], [22, 53, 15, 38], [23, 4, 16, 4], [23, 8, 16, 8], [23, 9, 16, 9, "asset"], [23, 14, 16, 14], [23, 16, 16, 16], [24, 6, 17, 8], [24, 13, 17, 15], [24, 17, 17, 19], [25, 4, 18, 4], [26, 4, 19, 4], [26, 10, 19, 10, "resolver"], [26, 18, 19, 18], [26, 21, 19, 21], [26, 25, 19, 25, "AssetSourceResolver"], [26, 53, 19, 44], [27, 4, 20, 4], [28, 4, 21, 4], [28, 22, 21, 22], [28, 24, 21, 24], [28, 28, 21, 28], [28, 30, 21, 30, "asset"], [28, 35, 21, 35], [28, 36, 21, 36], [29, 4, 22, 4], [29, 8, 22, 8, "_customSourceTransformer"], [29, 32, 22, 32], [29, 34, 22, 34], [30, 6, 23, 8], [30, 13, 23, 15, "_customSourceTransformer"], [30, 37, 23, 39], [30, 38, 23, 40, "resolver"], [30, 46, 23, 48], [30, 47, 23, 49], [31, 4, 24, 4], [32, 4, 25, 4], [32, 11, 25, 11, "resolver"], [32, 19, 25, 19], [32, 20, 25, 20, "defaultAsset"], [32, 32, 25, 32], [32, 33, 25, 33], [32, 34, 25, 34], [33, 2, 26, 0], [34, 2, 27, 0, "Object"], [34, 8, 27, 6], [34, 9, 27, 7, "defineProperty"], [34, 23, 27, 21], [34, 24, 27, 22, "resolveAssetSource"], [34, 42, 27, 40], [34, 44, 27, 42], [34, 72, 27, 70], [34, 74, 27, 72], [35, 4, 28, 4, "get"], [35, 7, 28, 7, "get"], [35, 8, 28, 7], [35, 10, 28, 10], [36, 6, 29, 8], [36, 13, 29, 15, "setCustomSourceTransformer"], [36, 39, 29, 41], [37, 4, 30, 4], [38, 2, 31, 0], [38, 3, 31, 1], [38, 4, 31, 2], [39, 2, 31, 3], [39, 6, 31, 3, "_default"], [39, 14, 31, 3], [39, 17, 31, 3, "exports"], [39, 24, 31, 3], [39, 25, 31, 3, "default"], [39, 32, 31, 3], [39, 35, 32, 15, "resolveAssetSource"], [39, 53, 32, 33], [40, 2, 33, 7], [40, 8, 33, 13], [41, 4, 33, 15, "pickScale"], [42, 2, 33, 25], [42, 3, 33, 26], [42, 6, 33, 29, "AssetSourceResolver"], [42, 34, 33, 48], [43, 2, 33, 49, "exports"], [43, 9, 33, 49], [43, 10, 33, 49, "pickScale"], [43, 19, 33, 49], [43, 22, 33, 49, "pickScale"], [43, 31, 33, 49], [44, 0, 33, 49], [44, 3]], "functionMap": {"names": ["<global>", "setCustomSourceTransformer", "resolveAssetSource", "Object.defineProperty$argument_2.get"], "mappings": "AAA;OCG;CDE;AEK;CFe;IGE;KHE"}}, "type": "js/module"}]}