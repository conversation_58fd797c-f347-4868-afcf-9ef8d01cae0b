{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BlazeFaceCanvas;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[1], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\web\\\\BlazeFaceCanvas.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * BlazeFace Canvas Component\n   *\n   * Uses TensorFlow.js BlazeFace model for accurate real-time face detection and blurring.\n   * This implementation matches the working solution from test-blazeface-integration.html\n   * with proper coordinate mapping and mirror effect handling.\n   */\n  function BlazeFaceCanvas({\n    containerId,\n    width,\n    height,\n    onReady\n  }) {\n    _s();\n    const canvasRef = (0, _react.useRef)(null);\n    const rafRef = (0, _react.useRef)(null);\n    const modelRef = (0, _react.useRef)(null);\n    const [isLoading, setIsLoading] = (0, _react.useState)(true);\n    const [faceCount, setFaceCount] = (0, _react.useState)(0);\n    (0, _react.useEffect)(() => {\n      console.log('[BlazeFaceCanvas] Starting initialization...', {\n        containerId,\n        width,\n        height\n      });\n      const container = document.getElementById(containerId);\n      if (!container) {\n        console.error('[BlazeFaceCanvas] Container not found:', containerId);\n        return;\n      }\n      const video = container.querySelector('video');\n      if (!video) {\n        console.error('[BlazeFaceCanvas] Video element not found in container');\n        return;\n      }\n      const canvas = canvasRef.current;\n      if (!canvas) {\n        console.error('[BlazeFaceCanvas] Canvas ref not available');\n        return;\n      }\n\n      // Set canvas size to match video dimensions when available\n      const updateCanvasSize = () => {\n        if (video.videoWidth && video.videoHeight) {\n          canvas.width = video.videoWidth;\n          canvas.height = video.videoHeight;\n          canvas.style.width = '100%';\n          canvas.style.height = '100%';\n          console.log('[BlazeFaceCanvas] Canvas resized to match video:', video.videoWidth, 'x', video.videoHeight);\n        } else {\n          // Fallback to provided dimensions\n          canvas.width = width;\n          canvas.height = height;\n          console.log('[BlazeFaceCanvas] Canvas resized to provided dimensions:', width, 'x', height);\n        }\n      };\n      const ctx = canvas.getContext('2d');\n      if (!ctx) {\n        console.error('[BlazeFaceCanvas] Canvas context not available');\n        return;\n      }\n      let isDetecting = true;\n\n      // Helper function to load scripts\n      const loadScript = src => {\n        return new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = src;\n          script.onload = () => resolve();\n          script.onerror = () => reject(new Error(`Failed to load ${src}`));\n          document.head.appendChild(script);\n        });\n      };\n\n      // Load TensorFlow.js and BlazeFace model - matching working test implementation\n      const loadModel = async () => {\n        try {\n          console.log('[BlazeFaceCanvas] Loading TensorFlow.js...');\n\n          // Load TensorFlow.js\n          if (!window.tf) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.20.0/dist/tf.min.js');\n          }\n          console.log('[BlazeFaceCanvas] Loading BlazeFace model...');\n\n          // Load BlazeFace model\n          if (!window.blazeface) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js');\n          }\n\n          // Initialize BlazeFace model\n          console.log('[BlazeFaceCanvas] Initializing BlazeFace model...');\n          modelRef.current = await window.blazeface.load();\n          console.log('[BlazeFaceCanvas] ✅ BlazeFace model loaded successfully');\n          setIsLoading(false);\n\n          // Update canvas size once video is ready\n          updateCanvasSize();\n\n          // Notify parent that BlazeFace is ready\n          if (onReady) {\n            onReady();\n          }\n\n          // Start detection loop\n          detectLoop();\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] ❌ Failed to load model:', error);\n          setIsLoading(false);\n        }\n      };\n      const detectLoop = async () => {\n        if (!isDetecting || !modelRef.current) return;\n        try {\n          // Check if video has valid dimensions\n          if (!video.videoWidth || !video.videoHeight) {\n            rafRef.current = requestAnimationFrame(detectLoop);\n            return;\n          }\n\n          // Create tensor from video\n          const tf = window.tf;\n          const tensor = tf.browser.fromPixels(video);\n\n          // Detect faces with same confidence threshold as working test\n          const predictions = await modelRef.current.estimateFaces(tensor, false, 0.6);\n          tensor.dispose();\n\n          // Clear canvas and draw the original video frame first\n          ctx.clearRect(0, 0, canvas.width, canvas.height);\n          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n          if (predictions.length > 0) {\n            setFaceCount(predictions.length);\n\n            // Process each detected face - REAL-TIME BLURRING like in test\n            predictions.forEach((prediction, index) => {\n              const [x1, y1] = prediction.topLeft;\n              const [x2, y2] = prediction.bottomRight;\n\n              // Fix coordinate order\n              let minX = Math.min(x1, x2);\n              let maxX = Math.max(x1, x2);\n              const minY = Math.min(y1, y2);\n              const maxY = Math.max(y1, y2);\n\n              // Account for horizontal flip (mirror effect)\n              const canvasWidth = canvas.width;\n              const flippedMinX = canvasWidth - maxX;\n              const flippedMaxX = canvasWidth - minX;\n              minX = flippedMinX;\n              maxX = flippedMaxX;\n\n              // Calculate face dimensions\n              const faceWidth = maxX - minX;\n              const faceHeight = maxY - minY;\n              if (faceWidth <= 0 || faceHeight <= 0) {\n                return;\n              }\n\n              // Expand the bounding box for better coverage\n              const centerX = (minX + maxX) / 2;\n              const centerY = (minY + maxY) / 2;\n              const expandedWidth = faceWidth * 1.5;\n              const expandedHeight = faceHeight * 1.8;\n\n              // Ensure positive radii\n              const radiusX = Math.max(expandedWidth / 2, 10);\n              const radiusY = Math.max(expandedHeight / 2, 10);\n\n              // Apply REAL-TIME elliptical blur - this creates the live preview blur\n              ctx.save();\n              ctx.beginPath();\n              ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, Math.PI * 2);\n              ctx.clip();\n              ctx.filter = 'blur(25px)'; // Stronger blur for better privacy\n              ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n              ctx.restore();\n\n              // Add debug rectangle to show detection area (optional)\n              if (__DEV__) {\n                ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';\n                ctx.lineWidth = 2;\n                ctx.strokeRect(minX, minY, faceWidth, faceHeight);\n              }\n            });\n          } else {\n            setFaceCount(0);\n          }\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] Detection error:', error);\n        }\n\n        // Continue detection loop\n        if (isDetecting) {\n          rafRef.current = requestAnimationFrame(detectLoop);\n        }\n      };\n\n      // Wait for video to be ready before starting\n      const waitForVideoAndStart = () => {\n        if (video.readyState >= 2) {\n          // HAVE_CURRENT_DATA\n          loadModel();\n        } else {\n          video.addEventListener('loadeddata', loadModel, {\n            once: true\n          });\n        }\n      };\n\n      // Start the process\n      waitForVideoAndStart();\n\n      // Cleanup function\n      return () => {\n        isDetecting = false;\n        if (rafRef.current) {\n          cancelAnimationFrame(rafRef.current);\n        }\n      };\n    }, [containerId, width, height]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          position: 'absolute',\n          left: 0,\n          top: 0,\n          width: '100%',\n          height: '100%',\n          pointerEvents: 'none',\n          zIndex: 15,\n          // Higher z-index to be above video\n          objectFit: 'cover',\n          backgroundColor: 'transparent'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 7\n      }, this), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(59, 130, 246, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(59, 130, 246, 0.3)'\n        },\n        children: \"Loading BlazeFace model...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this), !isLoading && faceCount > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(16, 185, 129, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(16, 185, 129, 0.3)'\n        },\n        children: [\"\\uD83D\\uDEE1\\uFE0F Protecting \", faceCount, \" face\", faceCount > 1 ? 's' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), !isLoading && faceCount === 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(107, 114, 128, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(107, 114, 128, 0.3)'\n        },\n        children: \"\\uD83D\\uDC40 Looking for faces...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  _s(BlazeFaceCanvas, \"4hwCZvaM14AzCul7FunDVbR3j+4=\");\n  _c = BlazeFaceCanvas;\n  var _c;\n  $RefreshReg$(_c, \"BlazeFaceCanvas\");\n});", "lineCount": 315, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "_interopRequireWildcard"], [6, 38, 1, 0], [6, 39, 1, 0, "require"], [6, 46, 1, 0], [6, 47, 1, 0, "_dependencyMap"], [6, 61, 1, 0], [7, 2, 1, 59], [7, 6, 1, 59, "_jsxDevRuntime"], [7, 20, 1, 59], [7, 23, 1, 59, "require"], [7, 30, 1, 59], [7, 31, 1, 59, "_dependencyMap"], [7, 45, 1, 59], [8, 2, 1, 59], [8, 6, 1, 59, "_jsxFileName"], [8, 18, 1, 59], [9, 4, 1, 59, "_s"], [9, 6, 1, 59], [9, 9, 1, 59, "$RefreshSig$"], [9, 21, 1, 59], [10, 2, 1, 59], [10, 11, 1, 59, "_interopRequireWildcard"], [10, 35, 1, 59, "e"], [10, 36, 1, 59], [10, 38, 1, 59, "t"], [10, 39, 1, 59], [10, 68, 1, 59, "WeakMap"], [10, 75, 1, 59], [10, 81, 1, 59, "r"], [10, 82, 1, 59], [10, 89, 1, 59, "WeakMap"], [10, 96, 1, 59], [10, 100, 1, 59, "n"], [10, 101, 1, 59], [10, 108, 1, 59, "WeakMap"], [10, 115, 1, 59], [10, 127, 1, 59, "_interopRequireWildcard"], [10, 150, 1, 59], [10, 162, 1, 59, "_interopRequireWildcard"], [10, 163, 1, 59, "e"], [10, 164, 1, 59], [10, 166, 1, 59, "t"], [10, 167, 1, 59], [10, 176, 1, 59, "t"], [10, 177, 1, 59], [10, 181, 1, 59, "e"], [10, 182, 1, 59], [10, 186, 1, 59, "e"], [10, 187, 1, 59], [10, 188, 1, 59, "__esModule"], [10, 198, 1, 59], [10, 207, 1, 59, "e"], [10, 208, 1, 59], [10, 214, 1, 59, "o"], [10, 215, 1, 59], [10, 217, 1, 59, "i"], [10, 218, 1, 59], [10, 220, 1, 59, "f"], [10, 221, 1, 59], [10, 226, 1, 59, "__proto__"], [10, 235, 1, 59], [10, 243, 1, 59, "default"], [10, 250, 1, 59], [10, 252, 1, 59, "e"], [10, 253, 1, 59], [10, 270, 1, 59, "e"], [10, 271, 1, 59], [10, 294, 1, 59, "e"], [10, 295, 1, 59], [10, 320, 1, 59, "e"], [10, 321, 1, 59], [10, 330, 1, 59, "f"], [10, 331, 1, 59], [10, 337, 1, 59, "o"], [10, 338, 1, 59], [10, 341, 1, 59, "t"], [10, 342, 1, 59], [10, 345, 1, 59, "n"], [10, 346, 1, 59], [10, 349, 1, 59, "r"], [10, 350, 1, 59], [10, 358, 1, 59, "o"], [10, 359, 1, 59], [10, 360, 1, 59, "has"], [10, 363, 1, 59], [10, 364, 1, 59, "e"], [10, 365, 1, 59], [10, 375, 1, 59, "o"], [10, 376, 1, 59], [10, 377, 1, 59, "get"], [10, 380, 1, 59], [10, 381, 1, 59, "e"], [10, 382, 1, 59], [10, 385, 1, 59, "o"], [10, 386, 1, 59], [10, 387, 1, 59, "set"], [10, 390, 1, 59], [10, 391, 1, 59, "e"], [10, 392, 1, 59], [10, 394, 1, 59, "f"], [10, 395, 1, 59], [10, 411, 1, 59, "t"], [10, 412, 1, 59], [10, 416, 1, 59, "e"], [10, 417, 1, 59], [10, 433, 1, 59, "t"], [10, 434, 1, 59], [10, 441, 1, 59, "hasOwnProperty"], [10, 455, 1, 59], [10, 456, 1, 59, "call"], [10, 460, 1, 59], [10, 461, 1, 59, "e"], [10, 462, 1, 59], [10, 464, 1, 59, "t"], [10, 465, 1, 59], [10, 472, 1, 59, "i"], [10, 473, 1, 59], [10, 477, 1, 59, "o"], [10, 478, 1, 59], [10, 481, 1, 59, "Object"], [10, 487, 1, 59], [10, 488, 1, 59, "defineProperty"], [10, 502, 1, 59], [10, 507, 1, 59, "Object"], [10, 513, 1, 59], [10, 514, 1, 59, "getOwnPropertyDescriptor"], [10, 538, 1, 59], [10, 539, 1, 59, "e"], [10, 540, 1, 59], [10, 542, 1, 59, "t"], [10, 543, 1, 59], [10, 550, 1, 59, "i"], [10, 551, 1, 59], [10, 552, 1, 59, "get"], [10, 555, 1, 59], [10, 559, 1, 59, "i"], [10, 560, 1, 59], [10, 561, 1, 59, "set"], [10, 564, 1, 59], [10, 568, 1, 59, "o"], [10, 569, 1, 59], [10, 570, 1, 59, "f"], [10, 571, 1, 59], [10, 573, 1, 59, "t"], [10, 574, 1, 59], [10, 576, 1, 59, "i"], [10, 577, 1, 59], [10, 581, 1, 59, "f"], [10, 582, 1, 59], [10, 583, 1, 59, "t"], [10, 584, 1, 59], [10, 588, 1, 59, "e"], [10, 589, 1, 59], [10, 590, 1, 59, "t"], [10, 591, 1, 59], [10, 602, 1, 59, "f"], [10, 603, 1, 59], [10, 608, 1, 59, "e"], [10, 609, 1, 59], [10, 611, 1, 59, "t"], [10, 612, 1, 59], [11, 2, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [15, 0, 14, 0], [16, 0, 15, 0], [17, 0, 16, 0], [18, 2, 17, 15], [18, 11, 17, 24, "BlazeFaceCanvas"], [18, 26, 17, 39, "BlazeFaceCanvas"], [18, 27, 17, 40], [19, 4, 17, 42, "containerId"], [19, 15, 17, 53], [20, 4, 17, 55, "width"], [20, 9, 17, 60], [21, 4, 17, 62, "height"], [21, 10, 17, 68], [22, 4, 17, 70, "onReady"], [23, 2, 17, 100], [23, 3, 17, 101], [23, 5, 17, 103], [24, 4, 17, 103, "_s"], [24, 6, 17, 103], [25, 4, 18, 2], [25, 10, 18, 8, "canvasRef"], [25, 19, 18, 17], [25, 22, 18, 20], [25, 26, 18, 20, "useRef"], [25, 39, 18, 26], [25, 41, 18, 53], [25, 45, 18, 57], [25, 46, 18, 58], [26, 4, 19, 2], [26, 10, 19, 8, "rafRef"], [26, 16, 19, 14], [26, 19, 19, 17], [26, 23, 19, 17, "useRef"], [26, 36, 19, 23], [26, 38, 19, 39], [26, 42, 19, 43], [26, 43, 19, 44], [27, 4, 20, 2], [27, 10, 20, 8, "modelRef"], [27, 18, 20, 16], [27, 21, 20, 19], [27, 25, 20, 19, "useRef"], [27, 38, 20, 25], [27, 40, 20, 38], [27, 44, 20, 42], [27, 45, 20, 43], [28, 4, 21, 2], [28, 10, 21, 8], [28, 11, 21, 9, "isLoading"], [28, 20, 21, 18], [28, 22, 21, 20, "setIsLoading"], [28, 34, 21, 32], [28, 35, 21, 33], [28, 38, 21, 36], [28, 42, 21, 36, "useState"], [28, 57, 21, 44], [28, 59, 21, 45], [28, 63, 21, 49], [28, 64, 21, 50], [29, 4, 22, 2], [29, 10, 22, 8], [29, 11, 22, 9, "faceCount"], [29, 20, 22, 18], [29, 22, 22, 20, "setFaceCount"], [29, 34, 22, 32], [29, 35, 22, 33], [29, 38, 22, 36], [29, 42, 22, 36, "useState"], [29, 57, 22, 44], [29, 59, 22, 45], [29, 60, 22, 46], [29, 61, 22, 47], [30, 4, 24, 2], [30, 8, 24, 2, "useEffect"], [30, 24, 24, 11], [30, 26, 24, 12], [30, 32, 24, 18], [31, 6, 25, 4, "console"], [31, 13, 25, 11], [31, 14, 25, 12, "log"], [31, 17, 25, 15], [31, 18, 25, 16], [31, 64, 25, 62], [31, 66, 25, 64], [32, 8, 25, 66, "containerId"], [32, 19, 25, 77], [33, 8, 25, 79, "width"], [33, 13, 25, 84], [34, 8, 25, 86, "height"], [35, 6, 25, 93], [35, 7, 25, 94], [35, 8, 25, 95], [36, 6, 27, 4], [36, 12, 27, 10, "container"], [36, 21, 27, 19], [36, 24, 27, 22, "document"], [36, 32, 27, 30], [36, 33, 27, 31, "getElementById"], [36, 47, 27, 45], [36, 48, 27, 46, "containerId"], [36, 59, 27, 57], [36, 60, 27, 58], [37, 6, 28, 4], [37, 10, 28, 8], [37, 11, 28, 9, "container"], [37, 20, 28, 18], [37, 22, 28, 20], [38, 8, 29, 6, "console"], [38, 15, 29, 13], [38, 16, 29, 14, "error"], [38, 21, 29, 19], [38, 22, 29, 20], [38, 62, 29, 60], [38, 64, 29, 62, "containerId"], [38, 75, 29, 73], [38, 76, 29, 74], [39, 8, 30, 6], [40, 6, 31, 4], [41, 6, 33, 4], [41, 12, 33, 10, "video"], [41, 17, 33, 40], [41, 20, 33, 43, "container"], [41, 29, 33, 52], [41, 30, 33, 53, "querySelector"], [41, 43, 33, 66], [41, 44, 33, 67], [41, 51, 33, 74], [41, 52, 33, 75], [42, 6, 34, 4], [42, 10, 34, 8], [42, 11, 34, 9, "video"], [42, 16, 34, 14], [42, 18, 34, 16], [43, 8, 35, 6, "console"], [43, 15, 35, 13], [43, 16, 35, 14, "error"], [43, 21, 35, 19], [43, 22, 35, 20], [43, 78, 35, 76], [43, 79, 35, 77], [44, 8, 36, 6], [45, 6, 37, 4], [46, 6, 39, 4], [46, 12, 39, 10, "canvas"], [46, 18, 39, 16], [46, 21, 39, 19, "canvasRef"], [46, 30, 39, 28], [46, 31, 39, 29, "current"], [46, 38, 39, 36], [47, 6, 40, 4], [47, 10, 40, 8], [47, 11, 40, 9, "canvas"], [47, 17, 40, 15], [47, 19, 40, 17], [48, 8, 41, 6, "console"], [48, 15, 41, 13], [48, 16, 41, 14, "error"], [48, 21, 41, 19], [48, 22, 41, 20], [48, 66, 41, 64], [48, 67, 41, 65], [49, 8, 42, 6], [50, 6, 43, 4], [52, 6, 45, 4], [53, 6, 46, 4], [53, 12, 46, 10, "updateCanvasSize"], [53, 28, 46, 26], [53, 31, 46, 29, "updateCanvasSize"], [53, 32, 46, 29], [53, 37, 46, 35], [54, 8, 47, 6], [54, 12, 47, 10, "video"], [54, 17, 47, 15], [54, 18, 47, 16, "videoWidth"], [54, 28, 47, 26], [54, 32, 47, 30, "video"], [54, 37, 47, 35], [54, 38, 47, 36, "videoHeight"], [54, 49, 47, 47], [54, 51, 47, 49], [55, 10, 48, 8, "canvas"], [55, 16, 48, 14], [55, 17, 48, 15, "width"], [55, 22, 48, 20], [55, 25, 48, 23, "video"], [55, 30, 48, 28], [55, 31, 48, 29, "videoWidth"], [55, 41, 48, 39], [56, 10, 49, 8, "canvas"], [56, 16, 49, 14], [56, 17, 49, 15, "height"], [56, 23, 49, 21], [56, 26, 49, 24, "video"], [56, 31, 49, 29], [56, 32, 49, 30, "videoHeight"], [56, 43, 49, 41], [57, 10, 50, 8, "canvas"], [57, 16, 50, 14], [57, 17, 50, 15, "style"], [57, 22, 50, 20], [57, 23, 50, 21, "width"], [57, 28, 50, 26], [57, 31, 50, 29], [57, 37, 50, 35], [58, 10, 51, 8, "canvas"], [58, 16, 51, 14], [58, 17, 51, 15, "style"], [58, 22, 51, 20], [58, 23, 51, 21, "height"], [58, 29, 51, 27], [58, 32, 51, 30], [58, 38, 51, 36], [59, 10, 52, 8, "console"], [59, 17, 52, 15], [59, 18, 52, 16, "log"], [59, 21, 52, 19], [59, 22, 52, 20], [59, 72, 52, 70], [59, 74, 52, 72, "video"], [59, 79, 52, 77], [59, 80, 52, 78, "videoWidth"], [59, 90, 52, 88], [59, 92, 52, 90], [59, 95, 52, 93], [59, 97, 52, 95, "video"], [59, 102, 52, 100], [59, 103, 52, 101, "videoHeight"], [59, 114, 52, 112], [59, 115, 52, 113], [60, 8, 53, 6], [60, 9, 53, 7], [60, 15, 53, 13], [61, 10, 54, 8], [62, 10, 55, 8, "canvas"], [62, 16, 55, 14], [62, 17, 55, 15, "width"], [62, 22, 55, 20], [62, 25, 55, 23, "width"], [62, 30, 55, 28], [63, 10, 56, 8, "canvas"], [63, 16, 56, 14], [63, 17, 56, 15, "height"], [63, 23, 56, 21], [63, 26, 56, 24, "height"], [63, 32, 56, 30], [64, 10, 57, 8, "console"], [64, 17, 57, 15], [64, 18, 57, 16, "log"], [64, 21, 57, 19], [64, 22, 57, 20], [64, 80, 57, 78], [64, 82, 57, 80, "width"], [64, 87, 57, 85], [64, 89, 57, 87], [64, 92, 57, 90], [64, 94, 57, 92, "height"], [64, 100, 57, 98], [64, 101, 57, 99], [65, 8, 58, 6], [66, 6, 59, 4], [66, 7, 59, 5], [67, 6, 61, 4], [67, 12, 61, 10, "ctx"], [67, 15, 61, 13], [67, 18, 61, 16, "canvas"], [67, 24, 61, 22], [67, 25, 61, 23, "getContext"], [67, 35, 61, 33], [67, 36, 61, 34], [67, 40, 61, 38], [67, 41, 61, 39], [68, 6, 62, 4], [68, 10, 62, 8], [68, 11, 62, 9, "ctx"], [68, 14, 62, 12], [68, 16, 62, 14], [69, 8, 63, 6, "console"], [69, 15, 63, 13], [69, 16, 63, 14, "error"], [69, 21, 63, 19], [69, 22, 63, 20], [69, 70, 63, 68], [69, 71, 63, 69], [70, 8, 64, 6], [71, 6, 65, 4], [72, 6, 67, 4], [72, 10, 67, 8, "isDetecting"], [72, 21, 67, 19], [72, 24, 67, 22], [72, 28, 67, 26], [74, 6, 69, 4], [75, 6, 70, 4], [75, 12, 70, 10, "loadScript"], [75, 22, 70, 20], [75, 25, 70, 24, "src"], [75, 28, 70, 35], [75, 32, 70, 55], [76, 8, 71, 6], [76, 15, 71, 13], [76, 19, 71, 17, "Promise"], [76, 26, 71, 24], [76, 27, 71, 25], [76, 28, 71, 26, "resolve"], [76, 35, 71, 33], [76, 37, 71, 35, "reject"], [76, 43, 71, 41], [76, 48, 71, 46], [77, 10, 72, 8], [77, 16, 72, 14, "script"], [77, 22, 72, 20], [77, 25, 72, 23, "document"], [77, 33, 72, 31], [77, 34, 72, 32, "createElement"], [77, 47, 72, 45], [77, 48, 72, 46], [77, 56, 72, 54], [77, 57, 72, 55], [78, 10, 73, 8, "script"], [78, 16, 73, 14], [78, 17, 73, 15, "src"], [78, 20, 73, 18], [78, 23, 73, 21, "src"], [78, 26, 73, 24], [79, 10, 74, 8, "script"], [79, 16, 74, 14], [79, 17, 74, 15, "onload"], [79, 23, 74, 21], [79, 26, 74, 24], [79, 32, 74, 30, "resolve"], [79, 39, 74, 37], [79, 40, 74, 38], [79, 41, 74, 39], [80, 10, 75, 8, "script"], [80, 16, 75, 14], [80, 17, 75, 15, "onerror"], [80, 24, 75, 22], [80, 27, 75, 25], [80, 33, 75, 31, "reject"], [80, 39, 75, 37], [80, 40, 75, 38], [80, 44, 75, 42, "Error"], [80, 49, 75, 47], [80, 50, 75, 48], [80, 68, 75, 66, "src"], [80, 71, 75, 69], [80, 73, 75, 71], [80, 74, 75, 72], [80, 75, 75, 73], [81, 10, 76, 8, "document"], [81, 18, 76, 16], [81, 19, 76, 17, "head"], [81, 23, 76, 21], [81, 24, 76, 22, "append<PERSON><PERSON><PERSON>"], [81, 35, 76, 33], [81, 36, 76, 34, "script"], [81, 42, 76, 40], [81, 43, 76, 41], [82, 8, 77, 6], [82, 9, 77, 7], [82, 10, 77, 8], [83, 6, 78, 4], [83, 7, 78, 5], [85, 6, 80, 4], [86, 6, 81, 4], [86, 12, 81, 10, "loadModel"], [86, 21, 81, 19], [86, 24, 81, 22], [86, 30, 81, 22, "loadModel"], [86, 31, 81, 22], [86, 36, 81, 34], [87, 8, 82, 6], [87, 12, 82, 10], [88, 10, 83, 8, "console"], [88, 17, 83, 15], [88, 18, 83, 16, "log"], [88, 21, 83, 19], [88, 22, 83, 20], [88, 66, 83, 64], [88, 67, 83, 65], [90, 10, 85, 8], [91, 10, 86, 8], [91, 14, 86, 12], [91, 15, 86, 14, "window"], [91, 21, 86, 20], [91, 22, 86, 29, "tf"], [91, 24, 86, 31], [91, 26, 86, 33], [92, 12, 87, 10], [92, 18, 87, 16, "loadScript"], [92, 28, 87, 26], [92, 29, 87, 27], [92, 98, 87, 96], [92, 99, 87, 97], [93, 10, 88, 8], [94, 10, 90, 8, "console"], [94, 17, 90, 15], [94, 18, 90, 16, "log"], [94, 21, 90, 19], [94, 22, 90, 20], [94, 68, 90, 66], [94, 69, 90, 67], [96, 10, 92, 8], [97, 10, 93, 8], [97, 14, 93, 12], [97, 15, 93, 14, "window"], [97, 21, 93, 20], [97, 22, 93, 29, "blazeface"], [97, 31, 93, 38], [97, 33, 93, 40], [98, 12, 94, 10], [98, 18, 94, 16, "loadScript"], [98, 28, 94, 26], [98, 29, 94, 27], [98, 112, 94, 110], [98, 113, 94, 111], [99, 10, 95, 8], [101, 10, 97, 8], [102, 10, 98, 8, "console"], [102, 17, 98, 15], [102, 18, 98, 16, "log"], [102, 21, 98, 19], [102, 22, 98, 20], [102, 73, 98, 71], [102, 74, 98, 72], [103, 10, 99, 8, "modelRef"], [103, 18, 99, 16], [103, 19, 99, 17, "current"], [103, 26, 99, 24], [103, 29, 99, 27], [103, 35, 99, 34, "window"], [103, 41, 99, 40], [103, 42, 99, 49, "blazeface"], [103, 51, 99, 58], [103, 52, 99, 59, "load"], [103, 56, 99, 63], [103, 57, 99, 64], [103, 58, 99, 65], [104, 10, 100, 8, "console"], [104, 17, 100, 15], [104, 18, 100, 16, "log"], [104, 21, 100, 19], [104, 22, 100, 20], [104, 79, 100, 77], [104, 80, 100, 78], [105, 10, 102, 8, "setIsLoading"], [105, 22, 102, 20], [105, 23, 102, 21], [105, 28, 102, 26], [105, 29, 102, 27], [107, 10, 104, 8], [108, 10, 105, 8, "updateCanvasSize"], [108, 26, 105, 24], [108, 27, 105, 25], [108, 28, 105, 26], [110, 10, 107, 8], [111, 10, 108, 8], [111, 14, 108, 12, "onReady"], [111, 21, 108, 19], [111, 23, 108, 21], [112, 12, 109, 10, "onReady"], [112, 19, 109, 17], [112, 20, 109, 18], [112, 21, 109, 19], [113, 10, 110, 8], [115, 10, 112, 8], [116, 10, 113, 8, "detectLoop"], [116, 20, 113, 18], [116, 21, 113, 19], [116, 22, 113, 20], [117, 8, 115, 6], [117, 9, 115, 7], [117, 10, 115, 8], [117, 17, 115, 15, "error"], [117, 22, 115, 20], [117, 24, 115, 22], [118, 10, 116, 8, "console"], [118, 17, 116, 15], [118, 18, 116, 16, "error"], [118, 23, 116, 21], [118, 24, 116, 22], [118, 67, 116, 65], [118, 69, 116, 67, "error"], [118, 74, 116, 72], [118, 75, 116, 73], [119, 10, 117, 8, "setIsLoading"], [119, 22, 117, 20], [119, 23, 117, 21], [119, 28, 117, 26], [119, 29, 117, 27], [120, 8, 118, 6], [121, 6, 119, 4], [121, 7, 119, 5], [122, 6, 121, 4], [122, 12, 121, 10, "detectLoop"], [122, 22, 121, 20], [122, 25, 121, 23], [122, 31, 121, 23, "detectLoop"], [122, 32, 121, 23], [122, 37, 121, 35], [123, 8, 122, 6], [123, 12, 122, 10], [123, 13, 122, 11, "isDetecting"], [123, 24, 122, 22], [123, 28, 122, 26], [123, 29, 122, 27, "modelRef"], [123, 37, 122, 35], [123, 38, 122, 36, "current"], [123, 45, 122, 43], [123, 47, 122, 45], [124, 8, 124, 6], [124, 12, 124, 10], [125, 10, 125, 8], [126, 10, 126, 8], [126, 14, 126, 12], [126, 15, 126, 13, "video"], [126, 20, 126, 18], [126, 21, 126, 19, "videoWidth"], [126, 31, 126, 29], [126, 35, 126, 33], [126, 36, 126, 34, "video"], [126, 41, 126, 39], [126, 42, 126, 40, "videoHeight"], [126, 53, 126, 51], [126, 55, 126, 53], [127, 12, 127, 10, "rafRef"], [127, 18, 127, 16], [127, 19, 127, 17, "current"], [127, 26, 127, 24], [127, 29, 127, 27, "requestAnimationFrame"], [127, 50, 127, 48], [127, 51, 127, 49, "detectLoop"], [127, 61, 127, 59], [127, 62, 127, 60], [128, 12, 128, 10], [129, 10, 129, 8], [131, 10, 131, 8], [132, 10, 132, 8], [132, 16, 132, 14, "tf"], [132, 18, 132, 16], [132, 21, 132, 20, "window"], [132, 27, 132, 26], [132, 28, 132, 35, "tf"], [132, 30, 132, 37], [133, 10, 133, 8], [133, 16, 133, 14, "tensor"], [133, 22, 133, 20], [133, 25, 133, 23, "tf"], [133, 27, 133, 25], [133, 28, 133, 26, "browser"], [133, 35, 133, 33], [133, 36, 133, 34, "fromPixels"], [133, 46, 133, 44], [133, 47, 133, 45, "video"], [133, 52, 133, 50], [133, 53, 133, 51], [135, 10, 135, 8], [136, 10, 136, 8], [136, 16, 136, 14, "predictions"], [136, 27, 136, 25], [136, 30, 136, 28], [136, 36, 136, 34, "modelRef"], [136, 44, 136, 42], [136, 45, 136, 43, "current"], [136, 52, 136, 50], [136, 53, 136, 51, "estimateFaces"], [136, 66, 136, 64], [136, 67, 136, 65, "tensor"], [136, 73, 136, 71], [136, 75, 136, 73], [136, 80, 136, 78], [136, 82, 136, 80], [136, 85, 136, 83], [136, 86, 136, 84], [137, 10, 137, 8, "tensor"], [137, 16, 137, 14], [137, 17, 137, 15, "dispose"], [137, 24, 137, 22], [137, 25, 137, 23], [137, 26, 137, 24], [139, 10, 139, 8], [140, 10, 140, 8, "ctx"], [140, 13, 140, 11], [140, 14, 140, 12, "clearRect"], [140, 23, 140, 21], [140, 24, 140, 22], [140, 25, 140, 23], [140, 27, 140, 25], [140, 28, 140, 26], [140, 30, 140, 28, "canvas"], [140, 36, 140, 34], [140, 37, 140, 35, "width"], [140, 42, 140, 40], [140, 44, 140, 42, "canvas"], [140, 50, 140, 48], [140, 51, 140, 49, "height"], [140, 57, 140, 55], [140, 58, 140, 56], [141, 10, 141, 8, "ctx"], [141, 13, 141, 11], [141, 14, 141, 12, "drawImage"], [141, 23, 141, 21], [141, 24, 141, 22, "video"], [141, 29, 141, 27], [141, 31, 141, 29], [141, 32, 141, 30], [141, 34, 141, 32], [141, 35, 141, 33], [141, 37, 141, 35, "canvas"], [141, 43, 141, 41], [141, 44, 141, 42, "width"], [141, 49, 141, 47], [141, 51, 141, 49, "canvas"], [141, 57, 141, 55], [141, 58, 141, 56, "height"], [141, 64, 141, 62], [141, 65, 141, 63], [142, 10, 143, 8], [142, 14, 143, 12, "predictions"], [142, 25, 143, 23], [142, 26, 143, 24, "length"], [142, 32, 143, 30], [142, 35, 143, 33], [142, 36, 143, 34], [142, 38, 143, 36], [143, 12, 144, 10, "setFaceCount"], [143, 24, 144, 22], [143, 25, 144, 23, "predictions"], [143, 36, 144, 34], [143, 37, 144, 35, "length"], [143, 43, 144, 41], [143, 44, 144, 42], [145, 12, 146, 10], [146, 12, 147, 10, "predictions"], [146, 23, 147, 21], [146, 24, 147, 22, "for<PERSON>ach"], [146, 31, 147, 29], [146, 32, 147, 30], [146, 33, 147, 31, "prediction"], [146, 43, 147, 46], [146, 45, 147, 48, "index"], [146, 50, 147, 61], [146, 55, 147, 66], [147, 14, 148, 12], [147, 20, 148, 18], [147, 21, 148, 19, "x1"], [147, 23, 148, 21], [147, 25, 148, 23, "y1"], [147, 27, 148, 25], [147, 28, 148, 26], [147, 31, 148, 29, "prediction"], [147, 41, 148, 39], [147, 42, 148, 40, "topLeft"], [147, 49, 148, 47], [148, 14, 149, 12], [148, 20, 149, 18], [148, 21, 149, 19, "x2"], [148, 23, 149, 21], [148, 25, 149, 23, "y2"], [148, 27, 149, 25], [148, 28, 149, 26], [148, 31, 149, 29, "prediction"], [148, 41, 149, 39], [148, 42, 149, 40, "bottomRight"], [148, 53, 149, 51], [150, 14, 151, 12], [151, 14, 152, 12], [151, 18, 152, 16, "minX"], [151, 22, 152, 20], [151, 25, 152, 23, "Math"], [151, 29, 152, 27], [151, 30, 152, 28, "min"], [151, 33, 152, 31], [151, 34, 152, 32, "x1"], [151, 36, 152, 34], [151, 38, 152, 36, "x2"], [151, 40, 152, 38], [151, 41, 152, 39], [152, 14, 153, 12], [152, 18, 153, 16, "maxX"], [152, 22, 153, 20], [152, 25, 153, 23, "Math"], [152, 29, 153, 27], [152, 30, 153, 28, "max"], [152, 33, 153, 31], [152, 34, 153, 32, "x1"], [152, 36, 153, 34], [152, 38, 153, 36, "x2"], [152, 40, 153, 38], [152, 41, 153, 39], [153, 14, 154, 12], [153, 20, 154, 18, "minY"], [153, 24, 154, 22], [153, 27, 154, 25, "Math"], [153, 31, 154, 29], [153, 32, 154, 30, "min"], [153, 35, 154, 33], [153, 36, 154, 34, "y1"], [153, 38, 154, 36], [153, 40, 154, 38, "y2"], [153, 42, 154, 40], [153, 43, 154, 41], [154, 14, 155, 12], [154, 20, 155, 18, "maxY"], [154, 24, 155, 22], [154, 27, 155, 25, "Math"], [154, 31, 155, 29], [154, 32, 155, 30, "max"], [154, 35, 155, 33], [154, 36, 155, 34, "y1"], [154, 38, 155, 36], [154, 40, 155, 38, "y2"], [154, 42, 155, 40], [154, 43, 155, 41], [156, 14, 157, 12], [157, 14, 158, 12], [157, 20, 158, 18, "canvasWidth"], [157, 31, 158, 29], [157, 34, 158, 32, "canvas"], [157, 40, 158, 38], [157, 41, 158, 39, "width"], [157, 46, 158, 44], [158, 14, 159, 12], [158, 20, 159, 18, "flippedMinX"], [158, 31, 159, 29], [158, 34, 159, 32, "canvasWidth"], [158, 45, 159, 43], [158, 48, 159, 46, "maxX"], [158, 52, 159, 50], [159, 14, 160, 12], [159, 20, 160, 18, "flippedMaxX"], [159, 31, 160, 29], [159, 34, 160, 32, "canvasWidth"], [159, 45, 160, 43], [159, 48, 160, 46, "minX"], [159, 52, 160, 50], [160, 14, 161, 12, "minX"], [160, 18, 161, 16], [160, 21, 161, 19, "flippedMinX"], [160, 32, 161, 30], [161, 14, 162, 12, "maxX"], [161, 18, 162, 16], [161, 21, 162, 19, "flippedMaxX"], [161, 32, 162, 30], [163, 14, 164, 12], [164, 14, 165, 12], [164, 20, 165, 18, "faceWidth"], [164, 29, 165, 27], [164, 32, 165, 30, "maxX"], [164, 36, 165, 34], [164, 39, 165, 37, "minX"], [164, 43, 165, 41], [165, 14, 166, 12], [165, 20, 166, 18, "faceHeight"], [165, 30, 166, 28], [165, 33, 166, 31, "maxY"], [165, 37, 166, 35], [165, 40, 166, 38, "minY"], [165, 44, 166, 42], [166, 14, 168, 12], [166, 18, 168, 16, "faceWidth"], [166, 27, 168, 25], [166, 31, 168, 29], [166, 32, 168, 30], [166, 36, 168, 34, "faceHeight"], [166, 46, 168, 44], [166, 50, 168, 48], [166, 51, 168, 49], [166, 53, 168, 51], [167, 16, 169, 14], [168, 14, 170, 12], [170, 14, 172, 12], [171, 14, 173, 12], [171, 20, 173, 18, "centerX"], [171, 27, 173, 25], [171, 30, 173, 28], [171, 31, 173, 29, "minX"], [171, 35, 173, 33], [171, 38, 173, 36, "maxX"], [171, 42, 173, 40], [171, 46, 173, 44], [171, 47, 173, 45], [172, 14, 174, 12], [172, 20, 174, 18, "centerY"], [172, 27, 174, 25], [172, 30, 174, 28], [172, 31, 174, 29, "minY"], [172, 35, 174, 33], [172, 38, 174, 36, "maxY"], [172, 42, 174, 40], [172, 46, 174, 44], [172, 47, 174, 45], [173, 14, 175, 12], [173, 20, 175, 18, "expandedWidth"], [173, 33, 175, 31], [173, 36, 175, 34, "faceWidth"], [173, 45, 175, 43], [173, 48, 175, 46], [173, 51, 175, 49], [174, 14, 176, 12], [174, 20, 176, 18, "expandedHeight"], [174, 34, 176, 32], [174, 37, 176, 35, "faceHeight"], [174, 47, 176, 45], [174, 50, 176, 48], [174, 53, 176, 51], [176, 14, 178, 12], [177, 14, 179, 12], [177, 20, 179, 18, "radiusX"], [177, 27, 179, 25], [177, 30, 179, 28, "Math"], [177, 34, 179, 32], [177, 35, 179, 33, "max"], [177, 38, 179, 36], [177, 39, 179, 37, "expandedWidth"], [177, 52, 179, 50], [177, 55, 179, 53], [177, 56, 179, 54], [177, 58, 179, 56], [177, 60, 179, 58], [177, 61, 179, 59], [178, 14, 180, 12], [178, 20, 180, 18, "radiusY"], [178, 27, 180, 25], [178, 30, 180, 28, "Math"], [178, 34, 180, 32], [178, 35, 180, 33, "max"], [178, 38, 180, 36], [178, 39, 180, 37, "expandedHeight"], [178, 53, 180, 51], [178, 56, 180, 54], [178, 57, 180, 55], [178, 59, 180, 57], [178, 61, 180, 59], [178, 62, 180, 60], [180, 14, 182, 12], [181, 14, 183, 12, "ctx"], [181, 17, 183, 15], [181, 18, 183, 16, "save"], [181, 22, 183, 20], [181, 23, 183, 21], [181, 24, 183, 22], [182, 14, 184, 12, "ctx"], [182, 17, 184, 15], [182, 18, 184, 16, "beginPath"], [182, 27, 184, 25], [182, 28, 184, 26], [182, 29, 184, 27], [183, 14, 185, 12, "ctx"], [183, 17, 185, 15], [183, 18, 185, 16, "ellipse"], [183, 25, 185, 23], [183, 26, 185, 24, "centerX"], [183, 33, 185, 31], [183, 35, 185, 33, "centerY"], [183, 42, 185, 40], [183, 44, 185, 42, "radiusX"], [183, 51, 185, 49], [183, 53, 185, 51, "radiusY"], [183, 60, 185, 58], [183, 62, 185, 60], [183, 63, 185, 61], [183, 65, 185, 63], [183, 66, 185, 64], [183, 68, 185, 66, "Math"], [183, 72, 185, 70], [183, 73, 185, 71, "PI"], [183, 75, 185, 73], [183, 78, 185, 76], [183, 79, 185, 77], [183, 80, 185, 78], [184, 14, 186, 12, "ctx"], [184, 17, 186, 15], [184, 18, 186, 16, "clip"], [184, 22, 186, 20], [184, 23, 186, 21], [184, 24, 186, 22], [185, 14, 187, 12, "ctx"], [185, 17, 187, 15], [185, 18, 187, 16, "filter"], [185, 24, 187, 22], [185, 27, 187, 25], [185, 39, 187, 37], [185, 40, 187, 38], [185, 41, 187, 39], [186, 14, 188, 12, "ctx"], [186, 17, 188, 15], [186, 18, 188, 16, "drawImage"], [186, 27, 188, 25], [186, 28, 188, 26, "video"], [186, 33, 188, 31], [186, 35, 188, 33], [186, 36, 188, 34], [186, 38, 188, 36], [186, 39, 188, 37], [186, 41, 188, 39, "canvas"], [186, 47, 188, 45], [186, 48, 188, 46, "width"], [186, 53, 188, 51], [186, 55, 188, 53, "canvas"], [186, 61, 188, 59], [186, 62, 188, 60, "height"], [186, 68, 188, 66], [186, 69, 188, 67], [187, 14, 189, 12, "ctx"], [187, 17, 189, 15], [187, 18, 189, 16, "restore"], [187, 25, 189, 23], [187, 26, 189, 24], [187, 27, 189, 25], [189, 14, 191, 12], [190, 14, 192, 12], [190, 18, 192, 16, "__DEV__"], [190, 25, 192, 23], [190, 27, 192, 25], [191, 16, 193, 14, "ctx"], [191, 19, 193, 17], [191, 20, 193, 18, "strokeStyle"], [191, 31, 193, 29], [191, 34, 193, 32], [191, 56, 193, 54], [192, 16, 194, 14, "ctx"], [192, 19, 194, 17], [192, 20, 194, 18, "lineWidth"], [192, 29, 194, 27], [192, 32, 194, 30], [192, 33, 194, 31], [193, 16, 195, 14, "ctx"], [193, 19, 195, 17], [193, 20, 195, 18, "strokeRect"], [193, 30, 195, 28], [193, 31, 195, 29, "minX"], [193, 35, 195, 33], [193, 37, 195, 35, "minY"], [193, 41, 195, 39], [193, 43, 195, 41, "faceWidth"], [193, 52, 195, 50], [193, 54, 195, 52, "faceHeight"], [193, 64, 195, 62], [193, 65, 195, 63], [194, 14, 196, 12], [195, 12, 197, 10], [195, 13, 197, 11], [195, 14, 197, 12], [196, 10, 198, 8], [196, 11, 198, 9], [196, 17, 198, 15], [197, 12, 199, 10, "setFaceCount"], [197, 24, 199, 22], [197, 25, 199, 23], [197, 26, 199, 24], [197, 27, 199, 25], [198, 10, 200, 8], [199, 8, 202, 6], [199, 9, 202, 7], [199, 10, 202, 8], [199, 17, 202, 15, "error"], [199, 22, 202, 20], [199, 24, 202, 22], [200, 10, 203, 8, "console"], [200, 17, 203, 15], [200, 18, 203, 16, "error"], [200, 23, 203, 21], [200, 24, 203, 22], [200, 60, 203, 58], [200, 62, 203, 60, "error"], [200, 67, 203, 65], [200, 68, 203, 66], [201, 8, 204, 6], [203, 8, 206, 6], [204, 8, 207, 6], [204, 12, 207, 10, "isDetecting"], [204, 23, 207, 21], [204, 25, 207, 23], [205, 10, 208, 8, "rafRef"], [205, 16, 208, 14], [205, 17, 208, 15, "current"], [205, 24, 208, 22], [205, 27, 208, 25, "requestAnimationFrame"], [205, 48, 208, 46], [205, 49, 208, 47, "detectLoop"], [205, 59, 208, 57], [205, 60, 208, 58], [206, 8, 209, 6], [207, 6, 210, 4], [207, 7, 210, 5], [209, 6, 212, 4], [210, 6, 213, 4], [210, 12, 213, 10, "waitForVideoAndStart"], [210, 32, 213, 30], [210, 35, 213, 33, "waitForVideoAndStart"], [210, 36, 213, 33], [210, 41, 213, 39], [211, 8, 214, 6], [211, 12, 214, 10, "video"], [211, 17, 214, 15], [211, 18, 214, 16, "readyState"], [211, 28, 214, 26], [211, 32, 214, 30], [211, 33, 214, 31], [211, 35, 214, 33], [212, 10, 214, 35], [213, 10, 215, 8, "loadModel"], [213, 19, 215, 17], [213, 20, 215, 18], [213, 21, 215, 19], [214, 8, 216, 6], [214, 9, 216, 7], [214, 15, 216, 13], [215, 10, 217, 8, "video"], [215, 15, 217, 13], [215, 16, 217, 14, "addEventListener"], [215, 32, 217, 30], [215, 33, 217, 31], [215, 45, 217, 43], [215, 47, 217, 45, "loadModel"], [215, 56, 217, 54], [215, 58, 217, 56], [216, 12, 217, 58, "once"], [216, 16, 217, 62], [216, 18, 217, 64], [217, 10, 217, 69], [217, 11, 217, 70], [217, 12, 217, 71], [218, 8, 218, 6], [219, 6, 219, 4], [219, 7, 219, 5], [221, 6, 221, 4], [222, 6, 222, 4, "waitForVideoAndStart"], [222, 26, 222, 24], [222, 27, 222, 25], [222, 28, 222, 26], [224, 6, 224, 4], [225, 6, 225, 4], [225, 13, 225, 11], [225, 19, 225, 17], [226, 8, 226, 6, "isDetecting"], [226, 19, 226, 17], [226, 22, 226, 20], [226, 27, 226, 25], [227, 8, 227, 6], [227, 12, 227, 10, "rafRef"], [227, 18, 227, 16], [227, 19, 227, 17, "current"], [227, 26, 227, 24], [227, 28, 227, 26], [228, 10, 228, 8, "cancelAnimationFrame"], [228, 30, 228, 28], [228, 31, 228, 29, "rafRef"], [228, 37, 228, 35], [228, 38, 228, 36, "current"], [228, 45, 228, 43], [228, 46, 228, 44], [229, 8, 229, 6], [230, 6, 230, 4], [230, 7, 230, 5], [231, 4, 231, 2], [231, 5, 231, 3], [231, 7, 231, 5], [231, 8, 231, 6, "containerId"], [231, 19, 231, 17], [231, 21, 231, 19, "width"], [231, 26, 231, 24], [231, 28, 231, 26, "height"], [231, 34, 231, 32], [231, 35, 231, 33], [231, 36, 231, 34], [232, 4, 233, 2], [232, 24, 234, 4], [232, 28, 234, 4, "_jsxDevRuntime"], [232, 42, 234, 4], [232, 43, 234, 4, "jsxDEV"], [232, 49, 234, 4], [232, 51, 234, 4, "_jsxDevRuntime"], [232, 65, 234, 4], [232, 66, 234, 4, "Fragment"], [232, 74, 234, 4], [233, 6, 234, 4, "children"], [233, 14, 234, 4], [233, 30, 235, 6], [233, 34, 235, 6, "_jsxDevRuntime"], [233, 48, 235, 6], [233, 49, 235, 6, "jsxDEV"], [233, 55, 235, 6], [234, 8, 236, 8, "ref"], [234, 11, 236, 11], [234, 13, 236, 13, "canvasRef"], [234, 22, 236, 23], [235, 8, 237, 8, "style"], [235, 13, 237, 13], [235, 15, 237, 15], [236, 10, 238, 10, "position"], [236, 18, 238, 18], [236, 20, 238, 20], [236, 30, 238, 30], [237, 10, 239, 10, "left"], [237, 14, 239, 14], [237, 16, 239, 16], [237, 17, 239, 17], [238, 10, 240, 10, "top"], [238, 13, 240, 13], [238, 15, 240, 15], [238, 16, 240, 16], [239, 10, 241, 10, "width"], [239, 15, 241, 15], [239, 17, 241, 17], [239, 23, 241, 23], [240, 10, 242, 10, "height"], [240, 16, 242, 16], [240, 18, 242, 18], [240, 24, 242, 24], [241, 10, 243, 10, "pointerEvents"], [241, 23, 243, 23], [241, 25, 243, 25], [241, 31, 243, 31], [242, 10, 244, 10, "zIndex"], [242, 16, 244, 16], [242, 18, 244, 18], [242, 20, 244, 20], [243, 10, 244, 22], [244, 10, 245, 10, "objectFit"], [244, 19, 245, 19], [244, 21, 245, 21], [244, 28, 245, 28], [245, 10, 246, 10, "backgroundColor"], [245, 25, 246, 25], [245, 27, 246, 27], [246, 8, 247, 8], [247, 6, 247, 10], [248, 8, 247, 10, "fileName"], [248, 16, 247, 10], [248, 18, 247, 10, "_jsxFileName"], [248, 30, 247, 10], [249, 8, 247, 10, "lineNumber"], [249, 18, 247, 10], [250, 8, 247, 10, "columnNumber"], [250, 20, 247, 10], [251, 6, 247, 10], [251, 13, 248, 7], [251, 14, 248, 8], [251, 16, 250, 7, "isLoading"], [251, 25, 250, 16], [251, 42, 251, 8], [251, 46, 251, 8, "_jsxDevRuntime"], [251, 60, 251, 8], [251, 61, 251, 8, "jsxDEV"], [251, 67, 251, 8], [252, 8, 251, 13, "style"], [252, 13, 251, 18], [252, 15, 251, 20], [253, 10, 252, 10, "position"], [253, 18, 252, 18], [253, 20, 252, 20], [253, 30, 252, 30], [254, 10, 253, 10, "top"], [254, 13, 253, 13], [254, 15, 253, 15], [254, 17, 253, 17], [255, 10, 254, 10, "left"], [255, 14, 254, 14], [255, 16, 254, 16], [255, 18, 254, 18], [256, 10, 255, 10, "background"], [256, 20, 255, 20], [256, 22, 255, 22], [256, 47, 255, 47], [257, 10, 256, 10, "color"], [257, 15, 256, 15], [257, 17, 256, 17], [257, 24, 256, 24], [258, 10, 257, 10, "padding"], [258, 17, 257, 17], [258, 19, 257, 19], [258, 29, 257, 29], [259, 10, 258, 10, "borderRadius"], [259, 22, 258, 22], [259, 24, 258, 24], [259, 29, 258, 29], [260, 10, 259, 10, "fontSize"], [260, 18, 259, 18], [260, 20, 259, 20], [260, 26, 259, 26], [261, 10, 260, 10, "fontWeight"], [261, 20, 260, 20], [261, 22, 260, 22], [261, 27, 260, 27], [262, 10, 261, 10, "zIndex"], [262, 16, 261, 16], [262, 18, 261, 18], [262, 20, 261, 20], [263, 10, 262, 10, "border"], [263, 16, 262, 16], [263, 18, 262, 18], [264, 8, 263, 8], [264, 9, 263, 10], [265, 8, 263, 10, "children"], [265, 16, 263, 10], [265, 18, 263, 11], [266, 6, 265, 8], [267, 8, 265, 8, "fileName"], [267, 16, 265, 8], [267, 18, 265, 8, "_jsxFileName"], [267, 30, 265, 8], [268, 8, 265, 8, "lineNumber"], [268, 18, 265, 8], [269, 8, 265, 8, "columnNumber"], [269, 20, 265, 8], [270, 6, 265, 8], [270, 13, 265, 13], [270, 14, 266, 7], [270, 16, 267, 7], [270, 17, 267, 8, "isLoading"], [270, 26, 267, 17], [270, 30, 267, 21, "faceCount"], [270, 39, 267, 30], [270, 42, 267, 33], [270, 43, 267, 34], [270, 60, 268, 8], [270, 64, 268, 8, "_jsxDevRuntime"], [270, 78, 268, 8], [270, 79, 268, 8, "jsxDEV"], [270, 85, 268, 8], [271, 8, 268, 13, "style"], [271, 13, 268, 18], [271, 15, 268, 20], [272, 10, 269, 10, "position"], [272, 18, 269, 18], [272, 20, 269, 20], [272, 30, 269, 30], [273, 10, 270, 10, "top"], [273, 13, 270, 13], [273, 15, 270, 15], [273, 17, 270, 17], [274, 10, 271, 10, "left"], [274, 14, 271, 14], [274, 16, 271, 16], [274, 18, 271, 18], [275, 10, 272, 10, "background"], [275, 20, 272, 20], [275, 22, 272, 22], [275, 47, 272, 47], [276, 10, 273, 10, "color"], [276, 15, 273, 15], [276, 17, 273, 17], [276, 24, 273, 24], [277, 10, 274, 10, "padding"], [277, 17, 274, 17], [277, 19, 274, 19], [277, 29, 274, 29], [278, 10, 275, 10, "borderRadius"], [278, 22, 275, 22], [278, 24, 275, 24], [278, 29, 275, 29], [279, 10, 276, 10, "fontSize"], [279, 18, 276, 18], [279, 20, 276, 20], [279, 26, 276, 26], [280, 10, 277, 10, "fontWeight"], [280, 20, 277, 20], [280, 22, 277, 22], [280, 27, 277, 27], [281, 10, 278, 10, "zIndex"], [281, 16, 278, 16], [281, 18, 278, 18], [281, 20, 278, 20], [282, 10, 279, 10, "border"], [282, 16, 279, 16], [282, 18, 279, 18], [283, 8, 280, 8], [283, 9, 280, 10], [284, 8, 280, 10, "children"], [284, 16, 280, 10], [284, 19, 280, 11], [284, 51, 281, 25], [284, 53, 281, 26, "faceCount"], [284, 62, 281, 35], [284, 64, 281, 36], [284, 71, 281, 41], [284, 73, 281, 42, "faceCount"], [284, 82, 281, 51], [284, 85, 281, 54], [284, 86, 281, 55], [284, 89, 281, 58], [284, 92, 281, 61], [284, 95, 281, 64], [284, 97, 281, 66], [285, 6, 281, 66], [286, 8, 281, 66, "fileName"], [286, 16, 281, 66], [286, 18, 281, 66, "_jsxFileName"], [286, 30, 281, 66], [287, 8, 281, 66, "lineNumber"], [287, 18, 281, 66], [288, 8, 281, 66, "columnNumber"], [288, 20, 281, 66], [289, 6, 281, 66], [289, 13, 282, 13], [289, 14, 283, 7], [289, 16, 284, 7], [289, 17, 284, 8, "isLoading"], [289, 26, 284, 17], [289, 30, 284, 21, "faceCount"], [289, 39, 284, 30], [289, 44, 284, 35], [289, 45, 284, 36], [289, 62, 285, 8], [289, 66, 285, 8, "_jsxDevRuntime"], [289, 80, 285, 8], [289, 81, 285, 8, "jsxDEV"], [289, 87, 285, 8], [290, 8, 285, 13, "style"], [290, 13, 285, 18], [290, 15, 285, 20], [291, 10, 286, 10, "position"], [291, 18, 286, 18], [291, 20, 286, 20], [291, 30, 286, 30], [292, 10, 287, 10, "top"], [292, 13, 287, 13], [292, 15, 287, 15], [292, 17, 287, 17], [293, 10, 288, 10, "left"], [293, 14, 288, 14], [293, 16, 288, 16], [293, 18, 288, 18], [294, 10, 289, 10, "background"], [294, 20, 289, 20], [294, 22, 289, 22], [294, 48, 289, 48], [295, 10, 290, 10, "color"], [295, 15, 290, 15], [295, 17, 290, 17], [295, 24, 290, 24], [296, 10, 291, 10, "padding"], [296, 17, 291, 17], [296, 19, 291, 19], [296, 29, 291, 29], [297, 10, 292, 10, "borderRadius"], [297, 22, 292, 22], [297, 24, 292, 24], [297, 29, 292, 29], [298, 10, 293, 10, "fontSize"], [298, 18, 293, 18], [298, 20, 293, 20], [298, 26, 293, 26], [299, 10, 294, 10, "fontWeight"], [299, 20, 294, 20], [299, 22, 294, 22], [299, 27, 294, 27], [300, 10, 295, 10, "zIndex"], [300, 16, 295, 16], [300, 18, 295, 18], [300, 20, 295, 20], [301, 10, 296, 10, "border"], [301, 16, 296, 16], [301, 18, 296, 18], [302, 8, 297, 8], [302, 9, 297, 10], [303, 8, 297, 10, "children"], [303, 16, 297, 10], [303, 18, 297, 11], [304, 6, 299, 8], [305, 8, 299, 8, "fileName"], [305, 16, 299, 8], [305, 18, 299, 8, "_jsxFileName"], [305, 30, 299, 8], [306, 8, 299, 8, "lineNumber"], [306, 18, 299, 8], [307, 8, 299, 8, "columnNumber"], [307, 20, 299, 8], [308, 6, 299, 8], [308, 13, 299, 13], [308, 14, 300, 7], [309, 4, 300, 7], [309, 19, 301, 6], [309, 20, 301, 7], [310, 2, 303, 0], [311, 2, 303, 1, "_s"], [311, 4, 303, 1], [311, 5, 17, 24, "BlazeFaceCanvas"], [311, 20, 17, 39], [312, 2, 17, 39, "_c"], [312, 4, 17, 39], [312, 7, 17, 24, "BlazeFaceCanvas"], [312, 22, 17, 39], [313, 2, 17, 39], [313, 6, 17, 39, "_c"], [313, 8, 17, 39], [314, 2, 17, 39, "$RefreshReg$"], [314, 14, 17, 39], [314, 15, 17, 39, "_c"], [314, 17, 17, 39], [315, 0, 17, 39], [315, 3]], "functionMap": {"names": ["<global>", "BlazeFaceCanvas", "useEffect$argument_0", "updateCanvasSize", "loadScript", "Promise$argument_0", "script.onload", "script.onerror", "loadModel", "detectLoop", "predictions.forEach$argument_0", "waitForVideoAndStart", "<anonymous>"], "mappings": "AAA;eCgB;YCO;6BCsB;KDa;uBEW;yBCC;wBCG,eD;yBEC,gDF;ODE;KFC;sBMG;KNsC;uBOE;8BC0B;WDkD;KPa;iCSG;KTM;WUM;KVK;GDC;CDwE"}}, "type": "js/module"}]}