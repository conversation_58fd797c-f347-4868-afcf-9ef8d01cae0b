{"dependencies": [{"name": "react", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 59, "index": 59}}], "key": "RtGiGa+/H7VrI7GDQDLhO1UbpU8=", "exportNames": ["*"]}}, {"name": "react/jsx-dev-runtime", "data": {"asyncType": null, "isESMImport": false, "locs": [], "key": "L9D70Z4hi4aGuui1ysja/oQ5ytI=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = BlazeFaceCanvas;\n  var _react = _interopRequireWildcard(require(_dependencyMap[0], \"react\"));\n  var _jsxDevRuntime = require(_dependencyMap[1], \"react/jsx-dev-runtime\");\n  var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ECHO\\\\apps\\\\mobile\\\\src\\\\components\\\\camera\\\\web\\\\BlazeFaceCanvas.tsx\",\n    _s = $RefreshSig$();\n  function _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != typeof e && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) \"default\" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }\n  /**\n   * BlazeFace Canvas Component\n   *\n   * Uses TensorFlow.js BlazeFace model for accurate real-time face detection and blurring.\n   * This implementation matches the working solution from test-blazeface-integration.html\n   * with proper coordinate mapping and mirror effect handling.\n   */\n  function BlazeFaceCanvas({\n    containerId,\n    width,\n    height,\n    onReady\n  }) {\n    _s();\n    const canvasRef = (0, _react.useRef)(null);\n    const rafRef = (0, _react.useRef)(null);\n    const modelRef = (0, _react.useRef)(null);\n    const [isLoading, setIsLoading] = (0, _react.useState)(true);\n    const [faceCount, setFaceCount] = (0, _react.useState)(0);\n    (0, _react.useEffect)(() => {\n      console.log('[BlazeFaceCanvas] Starting initialization...', {\n        containerId,\n        width,\n        height\n      });\n      const container = document.getElementById(containerId);\n      if (!container) {\n        console.error('[BlazeFaceCanvas] Container not found:', containerId);\n        return;\n      }\n      console.log('[BlazeFaceCanvas] Container found:', container);\n      console.log('[BlazeFaceCanvas] Container children:', container.children);\n      const video = container.querySelector('video');\n      if (!video) {\n        console.error('[BlazeFaceCanvas] Video element not found in container');\n        console.log('[BlazeFaceCanvas] Available elements:', container.querySelectorAll('*'));\n        return;\n      }\n      console.log('[BlazeFaceCanvas] Video element found:', video);\n      console.log('[BlazeFaceCanvas] Video dimensions:', video.videoWidth, 'x', video.videoHeight);\n      console.log('[BlazeFaceCanvas] Video ready state:', video.readyState);\n      const canvas = canvasRef.current;\n      if (!canvas) {\n        console.error('[BlazeFaceCanvas] Canvas ref not available');\n        return;\n      }\n\n      // Set canvas size to match video dimensions when available\n      const updateCanvasSize = () => {\n        if (video.videoWidth && video.videoHeight) {\n          canvas.width = video.videoWidth;\n          canvas.height = video.videoHeight;\n          canvas.style.width = '100%';\n          canvas.style.height = '100%';\n          console.log('[BlazeFaceCanvas] Canvas resized to match video:', video.videoWidth, 'x', video.videoHeight);\n        } else {\n          // Fallback to provided dimensions\n          canvas.width = width;\n          canvas.height = height;\n          console.log('[BlazeFaceCanvas] Canvas resized to provided dimensions:', width, 'x', height);\n        }\n      };\n      const ctx = canvas.getContext('2d');\n      if (!ctx) {\n        console.error('[BlazeFaceCanvas] Canvas context not available');\n        return;\n      }\n      let isDetecting = true;\n\n      // Helper function to load scripts\n      const loadScript = src => {\n        return new Promise((resolve, reject) => {\n          const script = document.createElement('script');\n          script.src = src;\n          script.onload = () => resolve();\n          script.onerror = () => reject(new Error(`Failed to load ${src}`));\n          document.head.appendChild(script);\n        });\n      };\n\n      // Load TensorFlow.js and BlazeFace model - matching working test implementation\n      const loadModel = async () => {\n        try {\n          console.log('[BlazeFaceCanvas] Loading TensorFlow.js...');\n\n          // Load TensorFlow.js\n          if (!window.tf) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.20.0/dist/tf.min.js');\n          }\n          console.log('[BlazeFaceCanvas] Loading BlazeFace model...');\n\n          // Load BlazeFace model\n          if (!window.blazeface) {\n            await loadScript('https://cdn.jsdelivr.net/npm/@tensorflow-models/blazeface@0.0.7/dist/blazeface.js');\n          }\n\n          // Initialize BlazeFace model\n          console.log('[BlazeFaceCanvas] Initializing BlazeFace model...');\n          modelRef.current = await window.blazeface.load();\n          console.log('[BlazeFaceCanvas] ✅ BlazeFace model loaded successfully');\n          setIsLoading(false);\n\n          // Update canvas size once video is ready\n          updateCanvasSize();\n\n          // Notify parent that BlazeFace is ready\n          if (onReady) {\n            onReady();\n          }\n\n          // Start detection loop\n          detectLoop();\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] ❌ Failed to load model:', error);\n          setIsLoading(false);\n        }\n      };\n      const detectLoop = async () => {\n        if (!isDetecting || !modelRef.current) return;\n        try {\n          // Check if video has valid dimensions\n          if (!video.videoWidth || !video.videoHeight) {\n            rafRef.current = requestAnimationFrame(detectLoop);\n            return;\n          }\n\n          // Create tensor from video\n          const tf = window.tf;\n          const tensor = tf.browser.fromPixels(video);\n\n          // Detect faces with same confidence threshold as working test\n          const predictions = await modelRef.current.estimateFaces(tensor, false, 0.6);\n          tensor.dispose();\n\n          // Clear canvas - DON'T draw the original video, just clear for overlay\n          ctx.clearRect(0, 0, canvas.width, canvas.height);\n          if (predictions.length > 0) {\n            setFaceCount(predictions.length);\n\n            // Process each detected face - OVERLAY BLURRED PATCHES like in working test\n            predictions.forEach((prediction, index) => {\n              const [x1, y1] = prediction.topLeft;\n              const [x2, y2] = prediction.bottomRight;\n\n              // Fix coordinate order\n              let minX = Math.min(x1, x2);\n              let maxX = Math.max(x1, x2);\n              const minY = Math.min(y1, y2);\n              const maxY = Math.max(y1, y2);\n\n              // Account for horizontal flip (mirror effect) - CRITICAL for front camera\n              const canvasWidth = canvas.width;\n              const flippedMinX = canvasWidth - maxX;\n              const flippedMaxX = canvasWidth - minX;\n              minX = flippedMinX;\n              maxX = flippedMaxX;\n\n              // Calculate face dimensions\n              const faceWidth = maxX - minX;\n              const faceHeight = maxY - minY;\n              if (faceWidth <= 0 || faceHeight <= 0) {\n                return;\n              }\n\n              // Expand the bounding box for better coverage\n              const centerX = (minX + maxX) / 2;\n              const centerY = (minY + maxY) / 2;\n              const expandedWidth = faceWidth * 1.5;\n              const expandedHeight = faceHeight * 1.8;\n\n              // Ensure positive radii\n              const radiusX = Math.max(expandedWidth / 2, 10);\n              const radiusY = Math.max(expandedHeight / 2, 10);\n\n              // Apply OVERLAY elliptical blur patch - like working test\n              ctx.save();\n              ctx.beginPath();\n              ctx.ellipse(centerX, centerY, radiusX, radiusY, 0, 0, Math.PI * 2);\n              ctx.clip();\n              ctx.filter = 'blur(20px)'; // Match working test blur intensity\n              ctx.drawImage(video, 0, 0, canvas.width, canvas.height);\n              ctx.restore();\n\n              // Add debug rectangle to show detection area (optional)\n              if (__DEV__) {\n                ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';\n                ctx.lineWidth = 2;\n                ctx.strokeRect(minX, minY, faceWidth, faceHeight);\n              }\n            });\n          } else {\n            setFaceCount(0);\n          }\n        } catch (error) {\n          console.error('[BlazeFaceCanvas] Detection error:', error);\n        }\n\n        // Continue detection loop\n        if (isDetecting) {\n          rafRef.current = requestAnimationFrame(detectLoop);\n        }\n      };\n\n      // Wait for video to be ready before starting\n      const waitForVideoAndStart = () => {\n        if (video.readyState >= 2) {\n          // HAVE_CURRENT_DATA\n          loadModel();\n        } else {\n          video.addEventListener('loadeddata', loadModel, {\n            once: true\n          });\n        }\n      };\n\n      // Start the process\n      waitForVideoAndStart();\n\n      // Cleanup function\n      return () => {\n        isDetecting = false;\n        if (rafRef.current) {\n          cancelAnimationFrame(rafRef.current);\n        }\n      };\n    }, [containerId, width, height]);\n    return /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(_jsxDevRuntime.Fragment, {\n      children: [/*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          position: 'absolute',\n          left: 0,\n          top: 0,\n          width: '100%',\n          height: '100%',\n          pointerEvents: 'none',\n          zIndex: 5,\n          // Above video but below UI controls\n          objectFit: 'cover',\n          backgroundColor: 'transparent'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 7\n      }, this), isLoading && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(59, 130, 246, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(59, 130, 246, 0.3)'\n        },\n        children: \"Loading BlazeFace model...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this), !isLoading && faceCount > 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(16, 185, 129, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(16, 185, 129, 0.3)'\n        },\n        children: [\"\\uD83D\\uDEE1\\uFE0F Protecting \", faceCount, \" face\", faceCount > 1 ? 's' : '']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), !isLoading && faceCount === 0 && /*#__PURE__*/(0, _jsxDevRuntime.jsxDEV)(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 10,\n          left: 10,\n          background: 'rgba(107, 114, 128, 0.9)',\n          color: 'white',\n          padding: '8px 12px',\n          borderRadius: '8px',\n          fontSize: '12px',\n          fontWeight: '600',\n          zIndex: 20,\n          border: '1px solid rgba(107, 114, 128, 0.3)'\n        },\n        children: \"\\uD83D\\uDC40 Looking for faces...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  _s(BlazeFaceCanvas, \"4hwCZvaM14AzCul7FunDVbR3j+4=\");\n  _c = BlazeFaceCanvas;\n  var _c;\n  $RefreshReg$(_c, \"BlazeFaceCanvas\");\n});", "lineCount": 320, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_react"], [6, 12, 1, 0], [6, 15, 1, 0, "_interopRequireWildcard"], [6, 38, 1, 0], [6, 39, 1, 0, "require"], [6, 46, 1, 0], [6, 47, 1, 0, "_dependencyMap"], [6, 61, 1, 0], [7, 2, 1, 59], [7, 6, 1, 59, "_jsxDevRuntime"], [7, 20, 1, 59], [7, 23, 1, 59, "require"], [7, 30, 1, 59], [7, 31, 1, 59, "_dependencyMap"], [7, 45, 1, 59], [8, 2, 1, 59], [8, 6, 1, 59, "_jsxFileName"], [8, 18, 1, 59], [9, 4, 1, 59, "_s"], [9, 6, 1, 59], [9, 9, 1, 59, "$RefreshSig$"], [9, 21, 1, 59], [10, 2, 1, 59], [10, 11, 1, 59, "_interopRequireWildcard"], [10, 35, 1, 59, "e"], [10, 36, 1, 59], [10, 38, 1, 59, "t"], [10, 39, 1, 59], [10, 68, 1, 59, "WeakMap"], [10, 75, 1, 59], [10, 81, 1, 59, "r"], [10, 82, 1, 59], [10, 89, 1, 59, "WeakMap"], [10, 96, 1, 59], [10, 100, 1, 59, "n"], [10, 101, 1, 59], [10, 108, 1, 59, "WeakMap"], [10, 115, 1, 59], [10, 127, 1, 59, "_interopRequireWildcard"], [10, 150, 1, 59], [10, 162, 1, 59, "_interopRequireWildcard"], [10, 163, 1, 59, "e"], [10, 164, 1, 59], [10, 166, 1, 59, "t"], [10, 167, 1, 59], [10, 176, 1, 59, "t"], [10, 177, 1, 59], [10, 181, 1, 59, "e"], [10, 182, 1, 59], [10, 186, 1, 59, "e"], [10, 187, 1, 59], [10, 188, 1, 59, "__esModule"], [10, 198, 1, 59], [10, 207, 1, 59, "e"], [10, 208, 1, 59], [10, 214, 1, 59, "o"], [10, 215, 1, 59], [10, 217, 1, 59, "i"], [10, 218, 1, 59], [10, 220, 1, 59, "f"], [10, 221, 1, 59], [10, 226, 1, 59, "__proto__"], [10, 235, 1, 59], [10, 243, 1, 59, "default"], [10, 250, 1, 59], [10, 252, 1, 59, "e"], [10, 253, 1, 59], [10, 270, 1, 59, "e"], [10, 271, 1, 59], [10, 294, 1, 59, "e"], [10, 295, 1, 59], [10, 320, 1, 59, "e"], [10, 321, 1, 59], [10, 330, 1, 59, "f"], [10, 331, 1, 59], [10, 337, 1, 59, "o"], [10, 338, 1, 59], [10, 341, 1, 59, "t"], [10, 342, 1, 59], [10, 345, 1, 59, "n"], [10, 346, 1, 59], [10, 349, 1, 59, "r"], [10, 350, 1, 59], [10, 358, 1, 59, "o"], [10, 359, 1, 59], [10, 360, 1, 59, "has"], [10, 363, 1, 59], [10, 364, 1, 59, "e"], [10, 365, 1, 59], [10, 375, 1, 59, "o"], [10, 376, 1, 59], [10, 377, 1, 59, "get"], [10, 380, 1, 59], [10, 381, 1, 59, "e"], [10, 382, 1, 59], [10, 385, 1, 59, "o"], [10, 386, 1, 59], [10, 387, 1, 59, "set"], [10, 390, 1, 59], [10, 391, 1, 59, "e"], [10, 392, 1, 59], [10, 394, 1, 59, "f"], [10, 395, 1, 59], [10, 411, 1, 59, "t"], [10, 412, 1, 59], [10, 416, 1, 59, "e"], [10, 417, 1, 59], [10, 433, 1, 59, "t"], [10, 434, 1, 59], [10, 441, 1, 59, "hasOwnProperty"], [10, 455, 1, 59], [10, 456, 1, 59, "call"], [10, 460, 1, 59], [10, 461, 1, 59, "e"], [10, 462, 1, 59], [10, 464, 1, 59, "t"], [10, 465, 1, 59], [10, 472, 1, 59, "i"], [10, 473, 1, 59], [10, 477, 1, 59, "o"], [10, 478, 1, 59], [10, 481, 1, 59, "Object"], [10, 487, 1, 59], [10, 488, 1, 59, "defineProperty"], [10, 502, 1, 59], [10, 507, 1, 59, "Object"], [10, 513, 1, 59], [10, 514, 1, 59, "getOwnPropertyDescriptor"], [10, 538, 1, 59], [10, 539, 1, 59, "e"], [10, 540, 1, 59], [10, 542, 1, 59, "t"], [10, 543, 1, 59], [10, 550, 1, 59, "i"], [10, 551, 1, 59], [10, 552, 1, 59, "get"], [10, 555, 1, 59], [10, 559, 1, 59, "i"], [10, 560, 1, 59], [10, 561, 1, 59, "set"], [10, 564, 1, 59], [10, 568, 1, 59, "o"], [10, 569, 1, 59], [10, 570, 1, 59, "f"], [10, 571, 1, 59], [10, 573, 1, 59, "t"], [10, 574, 1, 59], [10, 576, 1, 59, "i"], [10, 577, 1, 59], [10, 581, 1, 59, "f"], [10, 582, 1, 59], [10, 583, 1, 59, "t"], [10, 584, 1, 59], [10, 588, 1, 59, "e"], [10, 589, 1, 59], [10, 590, 1, 59, "t"], [10, 591, 1, 59], [10, 602, 1, 59, "f"], [10, 603, 1, 59], [10, 608, 1, 59, "e"], [10, 609, 1, 59], [10, 611, 1, 59, "t"], [10, 612, 1, 59], [11, 2, 10, 0], [12, 0, 11, 0], [13, 0, 12, 0], [14, 0, 13, 0], [15, 0, 14, 0], [16, 0, 15, 0], [17, 0, 16, 0], [18, 2, 17, 15], [18, 11, 17, 24, "BlazeFaceCanvas"], [18, 26, 17, 39, "BlazeFaceCanvas"], [18, 27, 17, 40], [19, 4, 17, 42, "containerId"], [19, 15, 17, 53], [20, 4, 17, 55, "width"], [20, 9, 17, 60], [21, 4, 17, 62, "height"], [21, 10, 17, 68], [22, 4, 17, 70, "onReady"], [23, 2, 17, 100], [23, 3, 17, 101], [23, 5, 17, 103], [24, 4, 17, 103, "_s"], [24, 6, 17, 103], [25, 4, 18, 2], [25, 10, 18, 8, "canvasRef"], [25, 19, 18, 17], [25, 22, 18, 20], [25, 26, 18, 20, "useRef"], [25, 39, 18, 26], [25, 41, 18, 53], [25, 45, 18, 57], [25, 46, 18, 58], [26, 4, 19, 2], [26, 10, 19, 8, "rafRef"], [26, 16, 19, 14], [26, 19, 19, 17], [26, 23, 19, 17, "useRef"], [26, 36, 19, 23], [26, 38, 19, 39], [26, 42, 19, 43], [26, 43, 19, 44], [27, 4, 20, 2], [27, 10, 20, 8, "modelRef"], [27, 18, 20, 16], [27, 21, 20, 19], [27, 25, 20, 19, "useRef"], [27, 38, 20, 25], [27, 40, 20, 38], [27, 44, 20, 42], [27, 45, 20, 43], [28, 4, 21, 2], [28, 10, 21, 8], [28, 11, 21, 9, "isLoading"], [28, 20, 21, 18], [28, 22, 21, 20, "setIsLoading"], [28, 34, 21, 32], [28, 35, 21, 33], [28, 38, 21, 36], [28, 42, 21, 36, "useState"], [28, 57, 21, 44], [28, 59, 21, 45], [28, 63, 21, 49], [28, 64, 21, 50], [29, 4, 22, 2], [29, 10, 22, 8], [29, 11, 22, 9, "faceCount"], [29, 20, 22, 18], [29, 22, 22, 20, "setFaceCount"], [29, 34, 22, 32], [29, 35, 22, 33], [29, 38, 22, 36], [29, 42, 22, 36, "useState"], [29, 57, 22, 44], [29, 59, 22, 45], [29, 60, 22, 46], [29, 61, 22, 47], [30, 4, 24, 2], [30, 8, 24, 2, "useEffect"], [30, 24, 24, 11], [30, 26, 24, 12], [30, 32, 24, 18], [31, 6, 25, 4, "console"], [31, 13, 25, 11], [31, 14, 25, 12, "log"], [31, 17, 25, 15], [31, 18, 25, 16], [31, 64, 25, 62], [31, 66, 25, 64], [32, 8, 25, 66, "containerId"], [32, 19, 25, 77], [33, 8, 25, 79, "width"], [33, 13, 25, 84], [34, 8, 25, 86, "height"], [35, 6, 25, 93], [35, 7, 25, 94], [35, 8, 25, 95], [36, 6, 27, 4], [36, 12, 27, 10, "container"], [36, 21, 27, 19], [36, 24, 27, 22, "document"], [36, 32, 27, 30], [36, 33, 27, 31, "getElementById"], [36, 47, 27, 45], [36, 48, 27, 46, "containerId"], [36, 59, 27, 57], [36, 60, 27, 58], [37, 6, 28, 4], [37, 10, 28, 8], [37, 11, 28, 9, "container"], [37, 20, 28, 18], [37, 22, 28, 20], [38, 8, 29, 6, "console"], [38, 15, 29, 13], [38, 16, 29, 14, "error"], [38, 21, 29, 19], [38, 22, 29, 20], [38, 62, 29, 60], [38, 64, 29, 62, "containerId"], [38, 75, 29, 73], [38, 76, 29, 74], [39, 8, 30, 6], [40, 6, 31, 4], [41, 6, 33, 4, "console"], [41, 13, 33, 11], [41, 14, 33, 12, "log"], [41, 17, 33, 15], [41, 18, 33, 16], [41, 54, 33, 52], [41, 56, 33, 54, "container"], [41, 65, 33, 63], [41, 66, 33, 64], [42, 6, 34, 4, "console"], [42, 13, 34, 11], [42, 14, 34, 12, "log"], [42, 17, 34, 15], [42, 18, 34, 16], [42, 57, 34, 55], [42, 59, 34, 57, "container"], [42, 68, 34, 66], [42, 69, 34, 67, "children"], [42, 77, 34, 75], [42, 78, 34, 76], [43, 6, 36, 4], [43, 12, 36, 10, "video"], [43, 17, 36, 40], [43, 20, 36, 43, "container"], [43, 29, 36, 52], [43, 30, 36, 53, "querySelector"], [43, 43, 36, 66], [43, 44, 36, 67], [43, 51, 36, 74], [43, 52, 36, 75], [44, 6, 37, 4], [44, 10, 37, 8], [44, 11, 37, 9, "video"], [44, 16, 37, 14], [44, 18, 37, 16], [45, 8, 38, 6, "console"], [45, 15, 38, 13], [45, 16, 38, 14, "error"], [45, 21, 38, 19], [45, 22, 38, 20], [45, 78, 38, 76], [45, 79, 38, 77], [46, 8, 39, 6, "console"], [46, 15, 39, 13], [46, 16, 39, 14, "log"], [46, 19, 39, 17], [46, 20, 39, 18], [46, 59, 39, 57], [46, 61, 39, 59, "container"], [46, 70, 39, 68], [46, 71, 39, 69, "querySelectorAll"], [46, 87, 39, 85], [46, 88, 39, 86], [46, 91, 39, 89], [46, 92, 39, 90], [46, 93, 39, 91], [47, 8, 40, 6], [48, 6, 41, 4], [49, 6, 43, 4, "console"], [49, 13, 43, 11], [49, 14, 43, 12, "log"], [49, 17, 43, 15], [49, 18, 43, 16], [49, 58, 43, 56], [49, 60, 43, 58, "video"], [49, 65, 43, 63], [49, 66, 43, 64], [50, 6, 44, 4, "console"], [50, 13, 44, 11], [50, 14, 44, 12, "log"], [50, 17, 44, 15], [50, 18, 44, 16], [50, 55, 44, 53], [50, 57, 44, 55, "video"], [50, 62, 44, 60], [50, 63, 44, 61, "videoWidth"], [50, 73, 44, 71], [50, 75, 44, 73], [50, 78, 44, 76], [50, 80, 44, 78, "video"], [50, 85, 44, 83], [50, 86, 44, 84, "videoHeight"], [50, 97, 44, 95], [50, 98, 44, 96], [51, 6, 45, 4, "console"], [51, 13, 45, 11], [51, 14, 45, 12, "log"], [51, 17, 45, 15], [51, 18, 45, 16], [51, 56, 45, 54], [51, 58, 45, 56, "video"], [51, 63, 45, 61], [51, 64, 45, 62, "readyState"], [51, 74, 45, 72], [51, 75, 45, 73], [52, 6, 47, 4], [52, 12, 47, 10, "canvas"], [52, 18, 47, 16], [52, 21, 47, 19, "canvasRef"], [52, 30, 47, 28], [52, 31, 47, 29, "current"], [52, 38, 47, 36], [53, 6, 48, 4], [53, 10, 48, 8], [53, 11, 48, 9, "canvas"], [53, 17, 48, 15], [53, 19, 48, 17], [54, 8, 49, 6, "console"], [54, 15, 49, 13], [54, 16, 49, 14, "error"], [54, 21, 49, 19], [54, 22, 49, 20], [54, 66, 49, 64], [54, 67, 49, 65], [55, 8, 50, 6], [56, 6, 51, 4], [58, 6, 53, 4], [59, 6, 54, 4], [59, 12, 54, 10, "updateCanvasSize"], [59, 28, 54, 26], [59, 31, 54, 29, "updateCanvasSize"], [59, 32, 54, 29], [59, 37, 54, 35], [60, 8, 55, 6], [60, 12, 55, 10, "video"], [60, 17, 55, 15], [60, 18, 55, 16, "videoWidth"], [60, 28, 55, 26], [60, 32, 55, 30, "video"], [60, 37, 55, 35], [60, 38, 55, 36, "videoHeight"], [60, 49, 55, 47], [60, 51, 55, 49], [61, 10, 56, 8, "canvas"], [61, 16, 56, 14], [61, 17, 56, 15, "width"], [61, 22, 56, 20], [61, 25, 56, 23, "video"], [61, 30, 56, 28], [61, 31, 56, 29, "videoWidth"], [61, 41, 56, 39], [62, 10, 57, 8, "canvas"], [62, 16, 57, 14], [62, 17, 57, 15, "height"], [62, 23, 57, 21], [62, 26, 57, 24, "video"], [62, 31, 57, 29], [62, 32, 57, 30, "videoHeight"], [62, 43, 57, 41], [63, 10, 58, 8, "canvas"], [63, 16, 58, 14], [63, 17, 58, 15, "style"], [63, 22, 58, 20], [63, 23, 58, 21, "width"], [63, 28, 58, 26], [63, 31, 58, 29], [63, 37, 58, 35], [64, 10, 59, 8, "canvas"], [64, 16, 59, 14], [64, 17, 59, 15, "style"], [64, 22, 59, 20], [64, 23, 59, 21, "height"], [64, 29, 59, 27], [64, 32, 59, 30], [64, 38, 59, 36], [65, 10, 60, 8, "console"], [65, 17, 60, 15], [65, 18, 60, 16, "log"], [65, 21, 60, 19], [65, 22, 60, 20], [65, 72, 60, 70], [65, 74, 60, 72, "video"], [65, 79, 60, 77], [65, 80, 60, 78, "videoWidth"], [65, 90, 60, 88], [65, 92, 60, 90], [65, 95, 60, 93], [65, 97, 60, 95, "video"], [65, 102, 60, 100], [65, 103, 60, 101, "videoHeight"], [65, 114, 60, 112], [65, 115, 60, 113], [66, 8, 61, 6], [66, 9, 61, 7], [66, 15, 61, 13], [67, 10, 62, 8], [68, 10, 63, 8, "canvas"], [68, 16, 63, 14], [68, 17, 63, 15, "width"], [68, 22, 63, 20], [68, 25, 63, 23, "width"], [68, 30, 63, 28], [69, 10, 64, 8, "canvas"], [69, 16, 64, 14], [69, 17, 64, 15, "height"], [69, 23, 64, 21], [69, 26, 64, 24, "height"], [69, 32, 64, 30], [70, 10, 65, 8, "console"], [70, 17, 65, 15], [70, 18, 65, 16, "log"], [70, 21, 65, 19], [70, 22, 65, 20], [70, 80, 65, 78], [70, 82, 65, 80, "width"], [70, 87, 65, 85], [70, 89, 65, 87], [70, 92, 65, 90], [70, 94, 65, 92, "height"], [70, 100, 65, 98], [70, 101, 65, 99], [71, 8, 66, 6], [72, 6, 67, 4], [72, 7, 67, 5], [73, 6, 69, 4], [73, 12, 69, 10, "ctx"], [73, 15, 69, 13], [73, 18, 69, 16, "canvas"], [73, 24, 69, 22], [73, 25, 69, 23, "getContext"], [73, 35, 69, 33], [73, 36, 69, 34], [73, 40, 69, 38], [73, 41, 69, 39], [74, 6, 70, 4], [74, 10, 70, 8], [74, 11, 70, 9, "ctx"], [74, 14, 70, 12], [74, 16, 70, 14], [75, 8, 71, 6, "console"], [75, 15, 71, 13], [75, 16, 71, 14, "error"], [75, 21, 71, 19], [75, 22, 71, 20], [75, 70, 71, 68], [75, 71, 71, 69], [76, 8, 72, 6], [77, 6, 73, 4], [78, 6, 75, 4], [78, 10, 75, 8, "isDetecting"], [78, 21, 75, 19], [78, 24, 75, 22], [78, 28, 75, 26], [80, 6, 77, 4], [81, 6, 78, 4], [81, 12, 78, 10, "loadScript"], [81, 22, 78, 20], [81, 25, 78, 24, "src"], [81, 28, 78, 35], [81, 32, 78, 55], [82, 8, 79, 6], [82, 15, 79, 13], [82, 19, 79, 17, "Promise"], [82, 26, 79, 24], [82, 27, 79, 25], [82, 28, 79, 26, "resolve"], [82, 35, 79, 33], [82, 37, 79, 35, "reject"], [82, 43, 79, 41], [82, 48, 79, 46], [83, 10, 80, 8], [83, 16, 80, 14, "script"], [83, 22, 80, 20], [83, 25, 80, 23, "document"], [83, 33, 80, 31], [83, 34, 80, 32, "createElement"], [83, 47, 80, 45], [83, 48, 80, 46], [83, 56, 80, 54], [83, 57, 80, 55], [84, 10, 81, 8, "script"], [84, 16, 81, 14], [84, 17, 81, 15, "src"], [84, 20, 81, 18], [84, 23, 81, 21, "src"], [84, 26, 81, 24], [85, 10, 82, 8, "script"], [85, 16, 82, 14], [85, 17, 82, 15, "onload"], [85, 23, 82, 21], [85, 26, 82, 24], [85, 32, 82, 30, "resolve"], [85, 39, 82, 37], [85, 40, 82, 38], [85, 41, 82, 39], [86, 10, 83, 8, "script"], [86, 16, 83, 14], [86, 17, 83, 15, "onerror"], [86, 24, 83, 22], [86, 27, 83, 25], [86, 33, 83, 31, "reject"], [86, 39, 83, 37], [86, 40, 83, 38], [86, 44, 83, 42, "Error"], [86, 49, 83, 47], [86, 50, 83, 48], [86, 68, 83, 66, "src"], [86, 71, 83, 69], [86, 73, 83, 71], [86, 74, 83, 72], [86, 75, 83, 73], [87, 10, 84, 8, "document"], [87, 18, 84, 16], [87, 19, 84, 17, "head"], [87, 23, 84, 21], [87, 24, 84, 22, "append<PERSON><PERSON><PERSON>"], [87, 35, 84, 33], [87, 36, 84, 34, "script"], [87, 42, 84, 40], [87, 43, 84, 41], [88, 8, 85, 6], [88, 9, 85, 7], [88, 10, 85, 8], [89, 6, 86, 4], [89, 7, 86, 5], [91, 6, 88, 4], [92, 6, 89, 4], [92, 12, 89, 10, "loadModel"], [92, 21, 89, 19], [92, 24, 89, 22], [92, 30, 89, 22, "loadModel"], [92, 31, 89, 22], [92, 36, 89, 34], [93, 8, 90, 6], [93, 12, 90, 10], [94, 10, 91, 8, "console"], [94, 17, 91, 15], [94, 18, 91, 16, "log"], [94, 21, 91, 19], [94, 22, 91, 20], [94, 66, 91, 64], [94, 67, 91, 65], [96, 10, 93, 8], [97, 10, 94, 8], [97, 14, 94, 12], [97, 15, 94, 14, "window"], [97, 21, 94, 20], [97, 22, 94, 29, "tf"], [97, 24, 94, 31], [97, 26, 94, 33], [98, 12, 95, 10], [98, 18, 95, 16, "loadScript"], [98, 28, 95, 26], [98, 29, 95, 27], [98, 98, 95, 96], [98, 99, 95, 97], [99, 10, 96, 8], [100, 10, 98, 8, "console"], [100, 17, 98, 15], [100, 18, 98, 16, "log"], [100, 21, 98, 19], [100, 22, 98, 20], [100, 68, 98, 66], [100, 69, 98, 67], [102, 10, 100, 8], [103, 10, 101, 8], [103, 14, 101, 12], [103, 15, 101, 14, "window"], [103, 21, 101, 20], [103, 22, 101, 29, "blazeface"], [103, 31, 101, 38], [103, 33, 101, 40], [104, 12, 102, 10], [104, 18, 102, 16, "loadScript"], [104, 28, 102, 26], [104, 29, 102, 27], [104, 112, 102, 110], [104, 113, 102, 111], [105, 10, 103, 8], [107, 10, 105, 8], [108, 10, 106, 8, "console"], [108, 17, 106, 15], [108, 18, 106, 16, "log"], [108, 21, 106, 19], [108, 22, 106, 20], [108, 73, 106, 71], [108, 74, 106, 72], [109, 10, 107, 8, "modelRef"], [109, 18, 107, 16], [109, 19, 107, 17, "current"], [109, 26, 107, 24], [109, 29, 107, 27], [109, 35, 107, 34, "window"], [109, 41, 107, 40], [109, 42, 107, 49, "blazeface"], [109, 51, 107, 58], [109, 52, 107, 59, "load"], [109, 56, 107, 63], [109, 57, 107, 64], [109, 58, 107, 65], [110, 10, 108, 8, "console"], [110, 17, 108, 15], [110, 18, 108, 16, "log"], [110, 21, 108, 19], [110, 22, 108, 20], [110, 79, 108, 77], [110, 80, 108, 78], [111, 10, 110, 8, "setIsLoading"], [111, 22, 110, 20], [111, 23, 110, 21], [111, 28, 110, 26], [111, 29, 110, 27], [113, 10, 112, 8], [114, 10, 113, 8, "updateCanvasSize"], [114, 26, 113, 24], [114, 27, 113, 25], [114, 28, 113, 26], [116, 10, 115, 8], [117, 10, 116, 8], [117, 14, 116, 12, "onReady"], [117, 21, 116, 19], [117, 23, 116, 21], [118, 12, 117, 10, "onReady"], [118, 19, 117, 17], [118, 20, 117, 18], [118, 21, 117, 19], [119, 10, 118, 8], [121, 10, 120, 8], [122, 10, 121, 8, "detectLoop"], [122, 20, 121, 18], [122, 21, 121, 19], [122, 22, 121, 20], [123, 8, 123, 6], [123, 9, 123, 7], [123, 10, 123, 8], [123, 17, 123, 15, "error"], [123, 22, 123, 20], [123, 24, 123, 22], [124, 10, 124, 8, "console"], [124, 17, 124, 15], [124, 18, 124, 16, "error"], [124, 23, 124, 21], [124, 24, 124, 22], [124, 67, 124, 65], [124, 69, 124, 67, "error"], [124, 74, 124, 72], [124, 75, 124, 73], [125, 10, 125, 8, "setIsLoading"], [125, 22, 125, 20], [125, 23, 125, 21], [125, 28, 125, 26], [125, 29, 125, 27], [126, 8, 126, 6], [127, 6, 127, 4], [127, 7, 127, 5], [128, 6, 129, 4], [128, 12, 129, 10, "detectLoop"], [128, 22, 129, 20], [128, 25, 129, 23], [128, 31, 129, 23, "detectLoop"], [128, 32, 129, 23], [128, 37, 129, 35], [129, 8, 130, 6], [129, 12, 130, 10], [129, 13, 130, 11, "isDetecting"], [129, 24, 130, 22], [129, 28, 130, 26], [129, 29, 130, 27, "modelRef"], [129, 37, 130, 35], [129, 38, 130, 36, "current"], [129, 45, 130, 43], [129, 47, 130, 45], [130, 8, 132, 6], [130, 12, 132, 10], [131, 10, 133, 8], [132, 10, 134, 8], [132, 14, 134, 12], [132, 15, 134, 13, "video"], [132, 20, 134, 18], [132, 21, 134, 19, "videoWidth"], [132, 31, 134, 29], [132, 35, 134, 33], [132, 36, 134, 34, "video"], [132, 41, 134, 39], [132, 42, 134, 40, "videoHeight"], [132, 53, 134, 51], [132, 55, 134, 53], [133, 12, 135, 10, "rafRef"], [133, 18, 135, 16], [133, 19, 135, 17, "current"], [133, 26, 135, 24], [133, 29, 135, 27, "requestAnimationFrame"], [133, 50, 135, 48], [133, 51, 135, 49, "detectLoop"], [133, 61, 135, 59], [133, 62, 135, 60], [134, 12, 136, 10], [135, 10, 137, 8], [137, 10, 139, 8], [138, 10, 140, 8], [138, 16, 140, 14, "tf"], [138, 18, 140, 16], [138, 21, 140, 20, "window"], [138, 27, 140, 26], [138, 28, 140, 35, "tf"], [138, 30, 140, 37], [139, 10, 141, 8], [139, 16, 141, 14, "tensor"], [139, 22, 141, 20], [139, 25, 141, 23, "tf"], [139, 27, 141, 25], [139, 28, 141, 26, "browser"], [139, 35, 141, 33], [139, 36, 141, 34, "fromPixels"], [139, 46, 141, 44], [139, 47, 141, 45, "video"], [139, 52, 141, 50], [139, 53, 141, 51], [141, 10, 143, 8], [142, 10, 144, 8], [142, 16, 144, 14, "predictions"], [142, 27, 144, 25], [142, 30, 144, 28], [142, 36, 144, 34, "modelRef"], [142, 44, 144, 42], [142, 45, 144, 43, "current"], [142, 52, 144, 50], [142, 53, 144, 51, "estimateFaces"], [142, 66, 144, 64], [142, 67, 144, 65, "tensor"], [142, 73, 144, 71], [142, 75, 144, 73], [142, 80, 144, 78], [142, 82, 144, 80], [142, 85, 144, 83], [142, 86, 144, 84], [143, 10, 145, 8, "tensor"], [143, 16, 145, 14], [143, 17, 145, 15, "dispose"], [143, 24, 145, 22], [143, 25, 145, 23], [143, 26, 145, 24], [145, 10, 147, 8], [146, 10, 148, 8, "ctx"], [146, 13, 148, 11], [146, 14, 148, 12, "clearRect"], [146, 23, 148, 21], [146, 24, 148, 22], [146, 25, 148, 23], [146, 27, 148, 25], [146, 28, 148, 26], [146, 30, 148, 28, "canvas"], [146, 36, 148, 34], [146, 37, 148, 35, "width"], [146, 42, 148, 40], [146, 44, 148, 42, "canvas"], [146, 50, 148, 48], [146, 51, 148, 49, "height"], [146, 57, 148, 55], [146, 58, 148, 56], [147, 10, 150, 8], [147, 14, 150, 12, "predictions"], [147, 25, 150, 23], [147, 26, 150, 24, "length"], [147, 32, 150, 30], [147, 35, 150, 33], [147, 36, 150, 34], [147, 38, 150, 36], [148, 12, 151, 10, "setFaceCount"], [148, 24, 151, 22], [148, 25, 151, 23, "predictions"], [148, 36, 151, 34], [148, 37, 151, 35, "length"], [148, 43, 151, 41], [148, 44, 151, 42], [150, 12, 153, 10], [151, 12, 154, 10, "predictions"], [151, 23, 154, 21], [151, 24, 154, 22, "for<PERSON>ach"], [151, 31, 154, 29], [151, 32, 154, 30], [151, 33, 154, 31, "prediction"], [151, 43, 154, 46], [151, 45, 154, 48, "index"], [151, 50, 154, 61], [151, 55, 154, 66], [152, 14, 155, 12], [152, 20, 155, 18], [152, 21, 155, 19, "x1"], [152, 23, 155, 21], [152, 25, 155, 23, "y1"], [152, 27, 155, 25], [152, 28, 155, 26], [152, 31, 155, 29, "prediction"], [152, 41, 155, 39], [152, 42, 155, 40, "topLeft"], [152, 49, 155, 47], [153, 14, 156, 12], [153, 20, 156, 18], [153, 21, 156, 19, "x2"], [153, 23, 156, 21], [153, 25, 156, 23, "y2"], [153, 27, 156, 25], [153, 28, 156, 26], [153, 31, 156, 29, "prediction"], [153, 41, 156, 39], [153, 42, 156, 40, "bottomRight"], [153, 53, 156, 51], [155, 14, 158, 12], [156, 14, 159, 12], [156, 18, 159, 16, "minX"], [156, 22, 159, 20], [156, 25, 159, 23, "Math"], [156, 29, 159, 27], [156, 30, 159, 28, "min"], [156, 33, 159, 31], [156, 34, 159, 32, "x1"], [156, 36, 159, 34], [156, 38, 159, 36, "x2"], [156, 40, 159, 38], [156, 41, 159, 39], [157, 14, 160, 12], [157, 18, 160, 16, "maxX"], [157, 22, 160, 20], [157, 25, 160, 23, "Math"], [157, 29, 160, 27], [157, 30, 160, 28, "max"], [157, 33, 160, 31], [157, 34, 160, 32, "x1"], [157, 36, 160, 34], [157, 38, 160, 36, "x2"], [157, 40, 160, 38], [157, 41, 160, 39], [158, 14, 161, 12], [158, 20, 161, 18, "minY"], [158, 24, 161, 22], [158, 27, 161, 25, "Math"], [158, 31, 161, 29], [158, 32, 161, 30, "min"], [158, 35, 161, 33], [158, 36, 161, 34, "y1"], [158, 38, 161, 36], [158, 40, 161, 38, "y2"], [158, 42, 161, 40], [158, 43, 161, 41], [159, 14, 162, 12], [159, 20, 162, 18, "maxY"], [159, 24, 162, 22], [159, 27, 162, 25, "Math"], [159, 31, 162, 29], [159, 32, 162, 30, "max"], [159, 35, 162, 33], [159, 36, 162, 34, "y1"], [159, 38, 162, 36], [159, 40, 162, 38, "y2"], [159, 42, 162, 40], [159, 43, 162, 41], [161, 14, 164, 12], [162, 14, 165, 12], [162, 20, 165, 18, "canvasWidth"], [162, 31, 165, 29], [162, 34, 165, 32, "canvas"], [162, 40, 165, 38], [162, 41, 165, 39, "width"], [162, 46, 165, 44], [163, 14, 166, 12], [163, 20, 166, 18, "flippedMinX"], [163, 31, 166, 29], [163, 34, 166, 32, "canvasWidth"], [163, 45, 166, 43], [163, 48, 166, 46, "maxX"], [163, 52, 166, 50], [164, 14, 167, 12], [164, 20, 167, 18, "flippedMaxX"], [164, 31, 167, 29], [164, 34, 167, 32, "canvasWidth"], [164, 45, 167, 43], [164, 48, 167, 46, "minX"], [164, 52, 167, 50], [165, 14, 168, 12, "minX"], [165, 18, 168, 16], [165, 21, 168, 19, "flippedMinX"], [165, 32, 168, 30], [166, 14, 169, 12, "maxX"], [166, 18, 169, 16], [166, 21, 169, 19, "flippedMaxX"], [166, 32, 169, 30], [168, 14, 171, 12], [169, 14, 172, 12], [169, 20, 172, 18, "faceWidth"], [169, 29, 172, 27], [169, 32, 172, 30, "maxX"], [169, 36, 172, 34], [169, 39, 172, 37, "minX"], [169, 43, 172, 41], [170, 14, 173, 12], [170, 20, 173, 18, "faceHeight"], [170, 30, 173, 28], [170, 33, 173, 31, "maxY"], [170, 37, 173, 35], [170, 40, 173, 38, "minY"], [170, 44, 173, 42], [171, 14, 175, 12], [171, 18, 175, 16, "faceWidth"], [171, 27, 175, 25], [171, 31, 175, 29], [171, 32, 175, 30], [171, 36, 175, 34, "faceHeight"], [171, 46, 175, 44], [171, 50, 175, 48], [171, 51, 175, 49], [171, 53, 175, 51], [172, 16, 176, 14], [173, 14, 177, 12], [175, 14, 179, 12], [176, 14, 180, 12], [176, 20, 180, 18, "centerX"], [176, 27, 180, 25], [176, 30, 180, 28], [176, 31, 180, 29, "minX"], [176, 35, 180, 33], [176, 38, 180, 36, "maxX"], [176, 42, 180, 40], [176, 46, 180, 44], [176, 47, 180, 45], [177, 14, 181, 12], [177, 20, 181, 18, "centerY"], [177, 27, 181, 25], [177, 30, 181, 28], [177, 31, 181, 29, "minY"], [177, 35, 181, 33], [177, 38, 181, 36, "maxY"], [177, 42, 181, 40], [177, 46, 181, 44], [177, 47, 181, 45], [178, 14, 182, 12], [178, 20, 182, 18, "expandedWidth"], [178, 33, 182, 31], [178, 36, 182, 34, "faceWidth"], [178, 45, 182, 43], [178, 48, 182, 46], [178, 51, 182, 49], [179, 14, 183, 12], [179, 20, 183, 18, "expandedHeight"], [179, 34, 183, 32], [179, 37, 183, 35, "faceHeight"], [179, 47, 183, 45], [179, 50, 183, 48], [179, 53, 183, 51], [181, 14, 185, 12], [182, 14, 186, 12], [182, 20, 186, 18, "radiusX"], [182, 27, 186, 25], [182, 30, 186, 28, "Math"], [182, 34, 186, 32], [182, 35, 186, 33, "max"], [182, 38, 186, 36], [182, 39, 186, 37, "expandedWidth"], [182, 52, 186, 50], [182, 55, 186, 53], [182, 56, 186, 54], [182, 58, 186, 56], [182, 60, 186, 58], [182, 61, 186, 59], [183, 14, 187, 12], [183, 20, 187, 18, "radiusY"], [183, 27, 187, 25], [183, 30, 187, 28, "Math"], [183, 34, 187, 32], [183, 35, 187, 33, "max"], [183, 38, 187, 36], [183, 39, 187, 37, "expandedHeight"], [183, 53, 187, 51], [183, 56, 187, 54], [183, 57, 187, 55], [183, 59, 187, 57], [183, 61, 187, 59], [183, 62, 187, 60], [185, 14, 189, 12], [186, 14, 190, 12, "ctx"], [186, 17, 190, 15], [186, 18, 190, 16, "save"], [186, 22, 190, 20], [186, 23, 190, 21], [186, 24, 190, 22], [187, 14, 191, 12, "ctx"], [187, 17, 191, 15], [187, 18, 191, 16, "beginPath"], [187, 27, 191, 25], [187, 28, 191, 26], [187, 29, 191, 27], [188, 14, 192, 12, "ctx"], [188, 17, 192, 15], [188, 18, 192, 16, "ellipse"], [188, 25, 192, 23], [188, 26, 192, 24, "centerX"], [188, 33, 192, 31], [188, 35, 192, 33, "centerY"], [188, 42, 192, 40], [188, 44, 192, 42, "radiusX"], [188, 51, 192, 49], [188, 53, 192, 51, "radiusY"], [188, 60, 192, 58], [188, 62, 192, 60], [188, 63, 192, 61], [188, 65, 192, 63], [188, 66, 192, 64], [188, 68, 192, 66, "Math"], [188, 72, 192, 70], [188, 73, 192, 71, "PI"], [188, 75, 192, 73], [188, 78, 192, 76], [188, 79, 192, 77], [188, 80, 192, 78], [189, 14, 193, 12, "ctx"], [189, 17, 193, 15], [189, 18, 193, 16, "clip"], [189, 22, 193, 20], [189, 23, 193, 21], [189, 24, 193, 22], [190, 14, 194, 12, "ctx"], [190, 17, 194, 15], [190, 18, 194, 16, "filter"], [190, 24, 194, 22], [190, 27, 194, 25], [190, 39, 194, 37], [190, 40, 194, 38], [190, 41, 194, 39], [191, 14, 195, 12, "ctx"], [191, 17, 195, 15], [191, 18, 195, 16, "drawImage"], [191, 27, 195, 25], [191, 28, 195, 26, "video"], [191, 33, 195, 31], [191, 35, 195, 33], [191, 36, 195, 34], [191, 38, 195, 36], [191, 39, 195, 37], [191, 41, 195, 39, "canvas"], [191, 47, 195, 45], [191, 48, 195, 46, "width"], [191, 53, 195, 51], [191, 55, 195, 53, "canvas"], [191, 61, 195, 59], [191, 62, 195, 60, "height"], [191, 68, 195, 66], [191, 69, 195, 67], [192, 14, 196, 12, "ctx"], [192, 17, 196, 15], [192, 18, 196, 16, "restore"], [192, 25, 196, 23], [192, 26, 196, 24], [192, 27, 196, 25], [194, 14, 198, 12], [195, 14, 199, 12], [195, 18, 199, 16, "__DEV__"], [195, 25, 199, 23], [195, 27, 199, 25], [196, 16, 200, 14, "ctx"], [196, 19, 200, 17], [196, 20, 200, 18, "strokeStyle"], [196, 31, 200, 29], [196, 34, 200, 32], [196, 56, 200, 54], [197, 16, 201, 14, "ctx"], [197, 19, 201, 17], [197, 20, 201, 18, "lineWidth"], [197, 29, 201, 27], [197, 32, 201, 30], [197, 33, 201, 31], [198, 16, 202, 14, "ctx"], [198, 19, 202, 17], [198, 20, 202, 18, "strokeRect"], [198, 30, 202, 28], [198, 31, 202, 29, "minX"], [198, 35, 202, 33], [198, 37, 202, 35, "minY"], [198, 41, 202, 39], [198, 43, 202, 41, "faceWidth"], [198, 52, 202, 50], [198, 54, 202, 52, "faceHeight"], [198, 64, 202, 62], [198, 65, 202, 63], [199, 14, 203, 12], [200, 12, 204, 10], [200, 13, 204, 11], [200, 14, 204, 12], [201, 10, 205, 8], [201, 11, 205, 9], [201, 17, 205, 15], [202, 12, 206, 10, "setFaceCount"], [202, 24, 206, 22], [202, 25, 206, 23], [202, 26, 206, 24], [202, 27, 206, 25], [203, 10, 207, 8], [204, 8, 209, 6], [204, 9, 209, 7], [204, 10, 209, 8], [204, 17, 209, 15, "error"], [204, 22, 209, 20], [204, 24, 209, 22], [205, 10, 210, 8, "console"], [205, 17, 210, 15], [205, 18, 210, 16, "error"], [205, 23, 210, 21], [205, 24, 210, 22], [205, 60, 210, 58], [205, 62, 210, 60, "error"], [205, 67, 210, 65], [205, 68, 210, 66], [206, 8, 211, 6], [208, 8, 213, 6], [209, 8, 214, 6], [209, 12, 214, 10, "isDetecting"], [209, 23, 214, 21], [209, 25, 214, 23], [210, 10, 215, 8, "rafRef"], [210, 16, 215, 14], [210, 17, 215, 15, "current"], [210, 24, 215, 22], [210, 27, 215, 25, "requestAnimationFrame"], [210, 48, 215, 46], [210, 49, 215, 47, "detectLoop"], [210, 59, 215, 57], [210, 60, 215, 58], [211, 8, 216, 6], [212, 6, 217, 4], [212, 7, 217, 5], [214, 6, 219, 4], [215, 6, 220, 4], [215, 12, 220, 10, "waitForVideoAndStart"], [215, 32, 220, 30], [215, 35, 220, 33, "waitForVideoAndStart"], [215, 36, 220, 33], [215, 41, 220, 39], [216, 8, 221, 6], [216, 12, 221, 10, "video"], [216, 17, 221, 15], [216, 18, 221, 16, "readyState"], [216, 28, 221, 26], [216, 32, 221, 30], [216, 33, 221, 31], [216, 35, 221, 33], [217, 10, 221, 35], [218, 10, 222, 8, "loadModel"], [218, 19, 222, 17], [218, 20, 222, 18], [218, 21, 222, 19], [219, 8, 223, 6], [219, 9, 223, 7], [219, 15, 223, 13], [220, 10, 224, 8, "video"], [220, 15, 224, 13], [220, 16, 224, 14, "addEventListener"], [220, 32, 224, 30], [220, 33, 224, 31], [220, 45, 224, 43], [220, 47, 224, 45, "loadModel"], [220, 56, 224, 54], [220, 58, 224, 56], [221, 12, 224, 58, "once"], [221, 16, 224, 62], [221, 18, 224, 64], [222, 10, 224, 69], [222, 11, 224, 70], [222, 12, 224, 71], [223, 8, 225, 6], [224, 6, 226, 4], [224, 7, 226, 5], [226, 6, 228, 4], [227, 6, 229, 4, "waitForVideoAndStart"], [227, 26, 229, 24], [227, 27, 229, 25], [227, 28, 229, 26], [229, 6, 231, 4], [230, 6, 232, 4], [230, 13, 232, 11], [230, 19, 232, 17], [231, 8, 233, 6, "isDetecting"], [231, 19, 233, 17], [231, 22, 233, 20], [231, 27, 233, 25], [232, 8, 234, 6], [232, 12, 234, 10, "rafRef"], [232, 18, 234, 16], [232, 19, 234, 17, "current"], [232, 26, 234, 24], [232, 28, 234, 26], [233, 10, 235, 8, "cancelAnimationFrame"], [233, 30, 235, 28], [233, 31, 235, 29, "rafRef"], [233, 37, 235, 35], [233, 38, 235, 36, "current"], [233, 45, 235, 43], [233, 46, 235, 44], [234, 8, 236, 6], [235, 6, 237, 4], [235, 7, 237, 5], [236, 4, 238, 2], [236, 5, 238, 3], [236, 7, 238, 5], [236, 8, 238, 6, "containerId"], [236, 19, 238, 17], [236, 21, 238, 19, "width"], [236, 26, 238, 24], [236, 28, 238, 26, "height"], [236, 34, 238, 32], [236, 35, 238, 33], [236, 36, 238, 34], [237, 4, 240, 2], [237, 24, 241, 4], [237, 28, 241, 4, "_jsxDevRuntime"], [237, 42, 241, 4], [237, 43, 241, 4, "jsxDEV"], [237, 49, 241, 4], [237, 51, 241, 4, "_jsxDevRuntime"], [237, 65, 241, 4], [237, 66, 241, 4, "Fragment"], [237, 74, 241, 4], [238, 6, 241, 4, "children"], [238, 14, 241, 4], [238, 30, 242, 6], [238, 34, 242, 6, "_jsxDevRuntime"], [238, 48, 242, 6], [238, 49, 242, 6, "jsxDEV"], [238, 55, 242, 6], [239, 8, 243, 8, "ref"], [239, 11, 243, 11], [239, 13, 243, 13, "canvasRef"], [239, 22, 243, 23], [240, 8, 244, 8, "style"], [240, 13, 244, 13], [240, 15, 244, 15], [241, 10, 245, 10, "position"], [241, 18, 245, 18], [241, 20, 245, 20], [241, 30, 245, 30], [242, 10, 246, 10, "left"], [242, 14, 246, 14], [242, 16, 246, 16], [242, 17, 246, 17], [243, 10, 247, 10, "top"], [243, 13, 247, 13], [243, 15, 247, 15], [243, 16, 247, 16], [244, 10, 248, 10, "width"], [244, 15, 248, 15], [244, 17, 248, 17], [244, 23, 248, 23], [245, 10, 249, 10, "height"], [245, 16, 249, 16], [245, 18, 249, 18], [245, 24, 249, 24], [246, 10, 250, 10, "pointerEvents"], [246, 23, 250, 23], [246, 25, 250, 25], [246, 31, 250, 31], [247, 10, 251, 10, "zIndex"], [247, 16, 251, 16], [247, 18, 251, 18], [247, 19, 251, 19], [248, 10, 251, 21], [249, 10, 252, 10, "objectFit"], [249, 19, 252, 19], [249, 21, 252, 21], [249, 28, 252, 28], [250, 10, 253, 10, "backgroundColor"], [250, 25, 253, 25], [250, 27, 253, 27], [251, 8, 254, 8], [252, 6, 254, 10], [253, 8, 254, 10, "fileName"], [253, 16, 254, 10], [253, 18, 254, 10, "_jsxFileName"], [253, 30, 254, 10], [254, 8, 254, 10, "lineNumber"], [254, 18, 254, 10], [255, 8, 254, 10, "columnNumber"], [255, 20, 254, 10], [256, 6, 254, 10], [256, 13, 255, 7], [256, 14, 255, 8], [256, 16, 257, 7, "isLoading"], [256, 25, 257, 16], [256, 42, 258, 8], [256, 46, 258, 8, "_jsxDevRuntime"], [256, 60, 258, 8], [256, 61, 258, 8, "jsxDEV"], [256, 67, 258, 8], [257, 8, 258, 13, "style"], [257, 13, 258, 18], [257, 15, 258, 20], [258, 10, 259, 10, "position"], [258, 18, 259, 18], [258, 20, 259, 20], [258, 30, 259, 30], [259, 10, 260, 10, "top"], [259, 13, 260, 13], [259, 15, 260, 15], [259, 17, 260, 17], [260, 10, 261, 10, "left"], [260, 14, 261, 14], [260, 16, 261, 16], [260, 18, 261, 18], [261, 10, 262, 10, "background"], [261, 20, 262, 20], [261, 22, 262, 22], [261, 47, 262, 47], [262, 10, 263, 10, "color"], [262, 15, 263, 15], [262, 17, 263, 17], [262, 24, 263, 24], [263, 10, 264, 10, "padding"], [263, 17, 264, 17], [263, 19, 264, 19], [263, 29, 264, 29], [264, 10, 265, 10, "borderRadius"], [264, 22, 265, 22], [264, 24, 265, 24], [264, 29, 265, 29], [265, 10, 266, 10, "fontSize"], [265, 18, 266, 18], [265, 20, 266, 20], [265, 26, 266, 26], [266, 10, 267, 10, "fontWeight"], [266, 20, 267, 20], [266, 22, 267, 22], [266, 27, 267, 27], [267, 10, 268, 10, "zIndex"], [267, 16, 268, 16], [267, 18, 268, 18], [267, 20, 268, 20], [268, 10, 269, 10, "border"], [268, 16, 269, 16], [268, 18, 269, 18], [269, 8, 270, 8], [269, 9, 270, 10], [270, 8, 270, 10, "children"], [270, 16, 270, 10], [270, 18, 270, 11], [271, 6, 272, 8], [272, 8, 272, 8, "fileName"], [272, 16, 272, 8], [272, 18, 272, 8, "_jsxFileName"], [272, 30, 272, 8], [273, 8, 272, 8, "lineNumber"], [273, 18, 272, 8], [274, 8, 272, 8, "columnNumber"], [274, 20, 272, 8], [275, 6, 272, 8], [275, 13, 272, 13], [275, 14, 273, 7], [275, 16, 274, 7], [275, 17, 274, 8, "isLoading"], [275, 26, 274, 17], [275, 30, 274, 21, "faceCount"], [275, 39, 274, 30], [275, 42, 274, 33], [275, 43, 274, 34], [275, 60, 275, 8], [275, 64, 275, 8, "_jsxDevRuntime"], [275, 78, 275, 8], [275, 79, 275, 8, "jsxDEV"], [275, 85, 275, 8], [276, 8, 275, 13, "style"], [276, 13, 275, 18], [276, 15, 275, 20], [277, 10, 276, 10, "position"], [277, 18, 276, 18], [277, 20, 276, 20], [277, 30, 276, 30], [278, 10, 277, 10, "top"], [278, 13, 277, 13], [278, 15, 277, 15], [278, 17, 277, 17], [279, 10, 278, 10, "left"], [279, 14, 278, 14], [279, 16, 278, 16], [279, 18, 278, 18], [280, 10, 279, 10, "background"], [280, 20, 279, 20], [280, 22, 279, 22], [280, 47, 279, 47], [281, 10, 280, 10, "color"], [281, 15, 280, 15], [281, 17, 280, 17], [281, 24, 280, 24], [282, 10, 281, 10, "padding"], [282, 17, 281, 17], [282, 19, 281, 19], [282, 29, 281, 29], [283, 10, 282, 10, "borderRadius"], [283, 22, 282, 22], [283, 24, 282, 24], [283, 29, 282, 29], [284, 10, 283, 10, "fontSize"], [284, 18, 283, 18], [284, 20, 283, 20], [284, 26, 283, 26], [285, 10, 284, 10, "fontWeight"], [285, 20, 284, 20], [285, 22, 284, 22], [285, 27, 284, 27], [286, 10, 285, 10, "zIndex"], [286, 16, 285, 16], [286, 18, 285, 18], [286, 20, 285, 20], [287, 10, 286, 10, "border"], [287, 16, 286, 16], [287, 18, 286, 18], [288, 8, 287, 8], [288, 9, 287, 10], [289, 8, 287, 10, "children"], [289, 16, 287, 10], [289, 19, 287, 11], [289, 51, 288, 25], [289, 53, 288, 26, "faceCount"], [289, 62, 288, 35], [289, 64, 288, 36], [289, 71, 288, 41], [289, 73, 288, 42, "faceCount"], [289, 82, 288, 51], [289, 85, 288, 54], [289, 86, 288, 55], [289, 89, 288, 58], [289, 92, 288, 61], [289, 95, 288, 64], [289, 97, 288, 66], [290, 6, 288, 66], [291, 8, 288, 66, "fileName"], [291, 16, 288, 66], [291, 18, 288, 66, "_jsxFileName"], [291, 30, 288, 66], [292, 8, 288, 66, "lineNumber"], [292, 18, 288, 66], [293, 8, 288, 66, "columnNumber"], [293, 20, 288, 66], [294, 6, 288, 66], [294, 13, 289, 13], [294, 14, 290, 7], [294, 16, 291, 7], [294, 17, 291, 8, "isLoading"], [294, 26, 291, 17], [294, 30, 291, 21, "faceCount"], [294, 39, 291, 30], [294, 44, 291, 35], [294, 45, 291, 36], [294, 62, 292, 8], [294, 66, 292, 8, "_jsxDevRuntime"], [294, 80, 292, 8], [294, 81, 292, 8, "jsxDEV"], [294, 87, 292, 8], [295, 8, 292, 13, "style"], [295, 13, 292, 18], [295, 15, 292, 20], [296, 10, 293, 10, "position"], [296, 18, 293, 18], [296, 20, 293, 20], [296, 30, 293, 30], [297, 10, 294, 10, "top"], [297, 13, 294, 13], [297, 15, 294, 15], [297, 17, 294, 17], [298, 10, 295, 10, "left"], [298, 14, 295, 14], [298, 16, 295, 16], [298, 18, 295, 18], [299, 10, 296, 10, "background"], [299, 20, 296, 20], [299, 22, 296, 22], [299, 48, 296, 48], [300, 10, 297, 10, "color"], [300, 15, 297, 15], [300, 17, 297, 17], [300, 24, 297, 24], [301, 10, 298, 10, "padding"], [301, 17, 298, 17], [301, 19, 298, 19], [301, 29, 298, 29], [302, 10, 299, 10, "borderRadius"], [302, 22, 299, 22], [302, 24, 299, 24], [302, 29, 299, 29], [303, 10, 300, 10, "fontSize"], [303, 18, 300, 18], [303, 20, 300, 20], [303, 26, 300, 26], [304, 10, 301, 10, "fontWeight"], [304, 20, 301, 20], [304, 22, 301, 22], [304, 27, 301, 27], [305, 10, 302, 10, "zIndex"], [305, 16, 302, 16], [305, 18, 302, 18], [305, 20, 302, 20], [306, 10, 303, 10, "border"], [306, 16, 303, 16], [306, 18, 303, 18], [307, 8, 304, 8], [307, 9, 304, 10], [308, 8, 304, 10, "children"], [308, 16, 304, 10], [308, 18, 304, 11], [309, 6, 306, 8], [310, 8, 306, 8, "fileName"], [310, 16, 306, 8], [310, 18, 306, 8, "_jsxFileName"], [310, 30, 306, 8], [311, 8, 306, 8, "lineNumber"], [311, 18, 306, 8], [312, 8, 306, 8, "columnNumber"], [312, 20, 306, 8], [313, 6, 306, 8], [313, 13, 306, 13], [313, 14, 307, 7], [314, 4, 307, 7], [314, 19, 308, 6], [314, 20, 308, 7], [315, 2, 310, 0], [316, 2, 310, 1, "_s"], [316, 4, 310, 1], [316, 5, 17, 24, "BlazeFaceCanvas"], [316, 20, 17, 39], [317, 2, 17, 39, "_c"], [317, 4, 17, 39], [317, 7, 17, 24, "BlazeFaceCanvas"], [317, 22, 17, 39], [318, 2, 17, 39], [318, 6, 17, 39, "_c"], [318, 8, 17, 39], [319, 2, 17, 39, "$RefreshReg$"], [319, 14, 17, 39], [319, 15, 17, 39, "_c"], [319, 17, 17, 39], [320, 0, 17, 39], [320, 3]], "functionMap": {"names": ["<global>", "BlazeFaceCanvas", "useEffect$argument_0", "updateCanvasSize", "loadScript", "Promise$argument_0", "script.onload", "script.onerror", "loadModel", "detectLoop", "predictions.forEach$argument_0", "waitForVideoAndStart", "<anonymous>"], "mappings": "AAA;eCgB;YCO;6BC8B;KDa;uBEW;yBCC;wBCG,eD;yBEC,gDF;ODE;KFC;sBMG;KNsC;uBOE;8BCyB;WDkD;KPa;iCSG;KTM;WUM;KVK;GDC;CDwE"}}, "type": "js/module"}]}