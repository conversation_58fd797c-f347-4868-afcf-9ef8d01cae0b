{"dependencies": [{"name": "./Host", "data": {"asyncType": null, "isESMImport": true, "locs": [{"start": {"line": 1, "column": 0, "index": 0}, "end": {"line": 1, "column": 30, "index": 30}}], "key": "BQhWeBRDaUSxZaA5iU6wGzQsFFs=", "exportNames": ["*"]}}], "output": [{"data": {"code": "__d(function (global, require, _$$_IMPORT_DEFAULT, _$$_IMPORT_ALL, module, exports, _dependencyMap) {\n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.JsiSkNativeBufferFactory = void 0;\n  var _Host = require(_dependencyMap[0], \"./Host\");\n  class JsiSkNativeBufferFactory extends _Host.Host {\n    constructor(CanvasKit) {\n      super(CanvasKit);\n    }\n    MakeFromImage(image) {\n      const info = image.getImageInfo();\n      const uint8ClampedArray = new Uint8ClampedArray(image.readPixels());\n      const imageData = new ImageData(uint8ClampedArray, info.width, info.height);\n      const canvas = new OffscreenCanvas(info.width, info.height);\n      const ctx = canvas.getContext(\"2d\");\n      if (!ctx) {\n        throw new Error(\"Failed to get 2d context from canvas\");\n      }\n      ctx.putImageData(imageData, 0, 0);\n      return canvas;\n    }\n    Release(_nativeBuffer) {\n      // it's a noop on Web\n    }\n  }\n  exports.JsiSkNativeBufferFactory = JsiSkNativeBufferFactory;\n});", "lineCount": 28, "map": [[6, 2, 1, 0], [6, 6, 1, 0, "_Host"], [6, 11, 1, 0], [6, 14, 1, 0, "require"], [6, 21, 1, 0], [6, 22, 1, 0, "_dependencyMap"], [6, 36, 1, 0], [7, 2, 2, 7], [7, 8, 2, 13, "JsiSkNativeBufferFactory"], [7, 32, 2, 37], [7, 41, 2, 46, "Host"], [7, 51, 2, 50], [7, 52, 2, 51], [8, 4, 3, 2, "constructor"], [8, 15, 3, 13, "constructor"], [8, 16, 3, 14, "CanvasKit"], [8, 25, 3, 23], [8, 27, 3, 25], [9, 6, 4, 4], [9, 11, 4, 9], [9, 12, 4, 10, "CanvasKit"], [9, 21, 4, 19], [9, 22, 4, 20], [10, 4, 5, 2], [11, 4, 6, 2, "MakeFromImage"], [11, 17, 6, 15, "MakeFromImage"], [11, 18, 6, 16, "image"], [11, 23, 6, 21], [11, 25, 6, 23], [12, 6, 7, 4], [12, 12, 7, 10, "info"], [12, 16, 7, 14], [12, 19, 7, 17, "image"], [12, 24, 7, 22], [12, 25, 7, 23, "getImageInfo"], [12, 37, 7, 35], [12, 38, 7, 36], [12, 39, 7, 37], [13, 6, 8, 4], [13, 12, 8, 10, "uint8ClampedArray"], [13, 29, 8, 27], [13, 32, 8, 30], [13, 36, 8, 34, "Uint8ClampedArray"], [13, 53, 8, 51], [13, 54, 8, 52, "image"], [13, 59, 8, 57], [13, 60, 8, 58, "readPixels"], [13, 70, 8, 68], [13, 71, 8, 69], [13, 72, 8, 70], [13, 73, 8, 71], [14, 6, 9, 4], [14, 12, 9, 10, "imageData"], [14, 21, 9, 19], [14, 24, 9, 22], [14, 28, 9, 26, "ImageData"], [14, 37, 9, 35], [14, 38, 9, 36, "uint8ClampedArray"], [14, 55, 9, 53], [14, 57, 9, 55, "info"], [14, 61, 9, 59], [14, 62, 9, 60, "width"], [14, 67, 9, 65], [14, 69, 9, 67, "info"], [14, 73, 9, 71], [14, 74, 9, 72, "height"], [14, 80, 9, 78], [14, 81, 9, 79], [15, 6, 10, 4], [15, 12, 10, 10, "canvas"], [15, 18, 10, 16], [15, 21, 10, 19], [15, 25, 10, 23, "OffscreenCanvas"], [15, 40, 10, 38], [15, 41, 10, 39, "info"], [15, 45, 10, 43], [15, 46, 10, 44, "width"], [15, 51, 10, 49], [15, 53, 10, 51, "info"], [15, 57, 10, 55], [15, 58, 10, 56, "height"], [15, 64, 10, 62], [15, 65, 10, 63], [16, 6, 11, 4], [16, 12, 11, 10, "ctx"], [16, 15, 11, 13], [16, 18, 11, 16, "canvas"], [16, 24, 11, 22], [16, 25, 11, 23, "getContext"], [16, 35, 11, 33], [16, 36, 11, 34], [16, 40, 11, 38], [16, 41, 11, 39], [17, 6, 12, 4], [17, 10, 12, 8], [17, 11, 12, 9, "ctx"], [17, 14, 12, 12], [17, 16, 12, 14], [18, 8, 13, 6], [18, 14, 13, 12], [18, 18, 13, 16, "Error"], [18, 23, 13, 21], [18, 24, 13, 22], [18, 62, 13, 60], [18, 63, 13, 61], [19, 6, 14, 4], [20, 6, 15, 4, "ctx"], [20, 9, 15, 7], [20, 10, 15, 8, "putImageData"], [20, 22, 15, 20], [20, 23, 15, 21, "imageData"], [20, 32, 15, 30], [20, 34, 15, 32], [20, 35, 15, 33], [20, 37, 15, 35], [20, 38, 15, 36], [20, 39, 15, 37], [21, 6, 16, 4], [21, 13, 16, 11, "canvas"], [21, 19, 16, 17], [22, 4, 17, 2], [23, 4, 18, 2, "Release"], [23, 11, 18, 9, "Release"], [23, 12, 18, 10, "_native<PERSON><PERSON>er"], [23, 25, 18, 23], [23, 27, 18, 25], [24, 6, 19, 4], [25, 4, 19, 4], [26, 2, 21, 0], [27, 2, 21, 1, "exports"], [27, 9, 21, 1], [27, 10, 21, 1, "JsiSkNativeBufferFactory"], [27, 34, 21, 1], [27, 37, 21, 1, "JsiSkNativeBufferFactory"], [27, 61, 21, 1], [28, 0, 21, 1], [28, 3]], "functionMap": {"names": ["<global>", "JsiSkNativeBufferFactory", "constructor", "MakeFromImage", "Release"], "mappings": "AAA;OCC;ECC;GDE;EEC;GFW;EGC;GHE;CDC"}}, "type": "js/module"}]}